import { <PERSON><PERSON><PERSON><PERSON> } from 'cron'
import { getLogger } from '../common/logger2'
import * as <PERSON><PERSON><PERSON> from 'bunyan'

const logger = getLogger('cronTasks')

interface ICreateTask {
  name: string
  cronTime: string,
  execute: () => Promise<any>,
}

interface IFullTask extends ICreateTask {
  logger: Bunyan
  manager: TaskManager
}

class Task {
  public name: string
  public cronTime: string
  private execute: () => Promise<any>
  private running: boolean = false
  private logger: Bunyan
  private manager: TaskManager

  constructor(option: IFullTask) {
    this.name = option.name
    this.cronTime = option.cronTime
    this.execute = option.execute
    this.logger = option.logger
    this.manager = option.manager
  }

  async run() {
    if (!this.running) {
      try {
        this.running = true
        const startTime = Date.now()
        await this.execute()
        const endTime = Date.now()
        this.manager.recordFinishInfo(this, startTime, endTime)
      } catch (err) {
        this.logger.error(err)
      } finally {
        this.running = false
      }
    } else {
      this.logger.warn('Fire running task, slow down trigger frequency!')
    }
  }

  static create(option: IFullTask) {
    return new Task(option)
  }
}

interface TimeDuration {
  start: number
  end: number
}


const LOG_RUN_INFO_INTERVAL = 15 * 60 * 1000

export class TaskManager {
  private jobMap: Map<string, CronJob> = new Map()
  private name: string
  private flags: object
  private logger
  private finishInfo: Map<string, TimeDuration> = new Map()

  constructor(name: string, flags: object) {
    this.name = name
    this.logger = logger.child({ manager: this.name })
    this.flags = flags
  }

  static create(name: string, flags: object) {
    return new TaskManager(name, flags)
  }

  private addByTask(task: Task) {
    let runOnInit = this.isEnableTask(task.name)
    let job = new CronJob({
      cronTime: task.cronTime,
      runOnInit: runOnInit,
      onTick: async function () {
        try {
          await task.run()
        } catch (err) {
          logger.error(err)
        }
      }
    })
    this.jobMap.set(task.name, job)
  }

  add(option: ICreateTask) {
    let logger = this.logger.child({ task: option.name })
    let params = Object.assign({}, option, { logger: logger, manager: this })
    let task = Task.create(params)
    this.addByTask(task)
  }

  isEnableTask(taskName: string): boolean {
    return !!this.flags[taskName]
  }

  recordFinishInfo(task: Task, startTime: number, endTime: number) {
    this.finishInfo.set(task.name, { start: startTime, end: endTime })
  }

  printFinishInfo() {
    for (let [name, td] of this.finishInfo) {
      let duration = td.end - td.start
      this.logger.info({ task: name, start: td.start, end: td.end, duration: duration }, 'LastFinishTaskTime')
    }
  }

  runAll() {
    for (let [name, job] of this.jobMap) {
      if (this.isEnableTask(name)) {
        this.logger.info('Begin to Run task ' + name)
        job.start()
      } else {
        this.logger.warn('Task:' + name + ' is disabled')
      }
    }

    setInterval(() => {
      this.printFinishInfo()
    }, LOG_RUN_INFO_INTERVAL)
  }
}

export let CronPattern = {
  EVERY_MINUTE: '* * * * *',
  EVERY_TEN_MINUTE: '*/10 * * * *',
  EVERY_TWO_HOUR: '0 */2 * * *',
  EVERY_SECOND: '* * * * * *',
  EVERY_HOUR: '0 */1 * * *',
}