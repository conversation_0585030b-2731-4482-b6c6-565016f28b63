import { DelayQueue, <PERSON><PERSON>ob } from "../common/delayQueue";
import { TopicStatus } from "../models/PyqActivityTopic";
import { apiLogger } from "../pyq-server/logger";
import { updateTopicStatus } from "../pyq-server/services/activityTopic";
import { playerTransfer } from "../pyq-server/services/transfer";
import * as HotMomentsCache from "../service/qnm/pyq/HotMomentsCache";

export const PyqDelayQueue = new DelayQueue("pyq_delay_queue");

export const DELAY_TOPICS = {
  DownHotMomentTopic: "DownHotMomentTopic",
  DownHotMoment: "DownHotMoment",
  TopTopic: "TopTopic",
  PlayerTransfer: "PlayerTransfer",
};

export interface IDownMoment {
  momentId: number;
  serverId?: number;
}

export interface TopTopicPayload {
  topicId: number;
  topTime: number;
}

export async function processReadyTask() {
  const job: IJob = await PyqDelayQueue.popReady();
  if (!job) return;
  if (job.topic === DELAY_TOPICS.DownHotMomentTopic) {
    const params = job.body as IDownMoment;
    return HotMomentsCache.downHotMoments([params.momentId], "" + params.serverId, null);
  } else if (job.topic === DELAY_TOPICS.DownHotMoment) {
    const params = job.body as { momentIds: number[]; serverId?: number };
    return HotMomentsCache.downHotMoments(params.momentIds, "" + params.serverId, null);
  } else if (job.topic === DELAY_TOPICS.TopTopic) {
    const body: Partial<TopTopicPayload> = job.body;
    apiLogger.info({ job: job }, "ProcessTopTopicTask");
    return updateTopicStatus({ id: body.topicId, status: TopicStatus.TOP });
  } else if (job.topic === DELAY_TOPICS.PlayerTransfer) {
    const body = job.body as { oldId: number; newId: number };
    return playerTransfer(body.oldId, body.newId);
  }
}
