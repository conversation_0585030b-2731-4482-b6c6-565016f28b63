import { transferLogger } from "../pyq-server/logger";
import { TransferModel } from "../pyq-server/models/transfer";
import { playerTransfer, TransferStatus } from "../pyq-server/services/transfer";
import { CronPattern, TaskManager } from "./taskManager";

const TaskSwitch = {
    makeupTransfer: true,
};

const manager = TaskManager.create("pyqCrons", TaskSwitch);

manager.add({
    name: 'makeupTransfer',
    cronTime: CronPattern.EVERY_HOUR,
    execute: async () => {
        // 检查一个小时前的数据是否有遗漏
        const now = Date.now();
        const oneHourAgo = now - 60 * 60 * 1000;
        const startTime = now - 3 * 60 * 60 * 1000;
        const transfers = await TransferModel.executeByQuery(TransferModel.scope().where('status', TransferStatus.INIT).whereBetween('createTime', [startTime, oneHourAgo]));
        for (const transfer of transfers) {
            transferLogger.info({ transfer }, "MakeupTransfer");
            await playerTransfer(transfer.oldId, transfer.newId);
        }
    },
});


if (require.main === module) {
    manager.runAll();
} else {
    exports.manager = manager;
}