import { TaskManager, CronPattern } from "./taskManager";
import * as SummerLbsService from "../pyq-server/services/summerlbs";
import * as PyqRank from "../service/qnm/pyq/rank";
import * as GmReviewInfos from "../pyq-server/services/gmReviewInfos";
import { updatePyqMomentsHot } from "./recalMomentHotValue";
import * as HotMomentsCache from "../service/qnm/pyq/HotMomentsCache";
import { PyqDelayQueue, processReadyTask } from "./delayTasks";
import { syncGiftEventToDB } from "../service/qnm/pyq/events/flower";
import { cronTaskSwitch } from "../common/config";
import { unlinkLog } from "../pyq-server/common/yunyingLogger";
import { clazzLogger, transferLogger } from "../pyq-server/logger";
import { TransferModel } from "../pyq-server/models/transfer";
import { TransferStatus, playerTransfer } from "../pyq-server/services/transfer";
import { cronJobCfg, fixMomentIdServerIdCfg, musicClubCfg } from "../pyq-server/common/config";
import { autoFixMomentServerId } from "../pyq-server/services/fixMomentIdServerIdService";
import { embedHealthCheckServer } from "../pyq-server/helper";
import { MusicClubRankingCache } from "../pyq-server/services/music-club/musicClubRankingCache";
import { MusicClubRecordingAuditTaskService } from "../pyq-server/services/music-club/musicClubRecordingAuditTaskService";
import { Context } from "../pyq-server/context";
const logger = clazzLogger("cron-jobs/pyqCrons");

const manager = TaskManager.create("pyqCrons", cronTaskSwitch);

// 暑假活动定时设置食物过期
manager.add({
  name: "SummerLbsJob",
  cronTime: CronPattern.EVERY_MINUTE,
  execute: async function () {
    return SummerLbsService.removeExpireFoodArea();
  },
});

// 倩女手游排行榜定时刷缓存
manager.add({
  name: "RefreshPlayersRank",
  cronTime: "*/15 * * * *",
  execute: async function () {
    return PyqRank.refreshTopPlayerRanks({ intervalTime: 200 });
  },
});

// 定时获取GM心易客服数据
manager.add({
  name: "FetchGmReviews",
  cronTime: "30 2 * * *", // 每天02：30
  execute: async function () {
    return GmReviewInfos.fetAllGmReviews();
  },
});

// 热度值随时间衰减
manager.add({
  name: "RefreshMomentHotValue",
  cronTime: "35 */2 * * *",
  execute: async function () {
    return updatePyqMomentsHot();
  },
});

if (fixMomentIdServerIdCfg.enable) {
  // 修正动态表的serverId字段
  manager.add({
    name: "FixMomentServerId",
    cronTime: "*/10 * * * *",
    execute: async function () {
      autoFixMomentServerId();
    },
  });
}

// 重建热门列表缓存
manager.add({
  name: "RebuildHotMomentCache",
  cronTime: "*/10 * * * *", // At every 10th minute
  execute: async function () {
    return HotMomentsCache.refreshAll();
  },
});

manager.add({
  name: "TriggerDelayTask",
  cronTime: CronPattern.EVERY_SECOND,
  execute: async function () {
    await PyqDelayQueue.onTick();
  },
});

manager.add({
  name: "ProcessReadyTask",
  cronTime: CronPattern.EVERY_SECOND,
  execute: async function () {
    return processReadyTask();
  },
});

manager.add({
  name: "SyncGiftEventToDb",
  cronTime: CronPattern.EVERY_SECOND,
  execute: async function () {
    return syncGiftEventToDB(100);
  },
});

// 定时回收运营日志
manager.add({
  name: "unlinkYunyingLog",
  cronTime: CronPattern.EVERY_HOUR,
  execute: async function () {
    unlinkLog();
  },
});

// 重试待处理的音频审核任务
manager.add({
  name: "RetryPendingAudioAuditTasks",
  cronTime: "*/5 * * * *", // 每5分钟执行一次
  execute: async function () {
    try {
      const ctx = Context.emptyContext();
      ctx.set("requestId", `cron_retry_audit_${Date.now()}`);
      const auditTaskService = MusicClubRecordingAuditTaskService.getInstance();
      const count = await auditTaskService.retryPendingTasks(ctx, musicClubCfg.auditMaxRetries);
      if (count > 0) {
        logger.info({ ctx, count }, "RetryPendingAudioAuditTasksCompleted");
      }
      return count;
    } catch (error) {
      logger.error({ error }, "RetryPendingAudioAuditTasksError");
      return 0;
    }
  },
});

// 检查超时的音频审核任务
manager.add({
  name: "CheckTimeoutAudioAuditTasks",
  cronTime: "*/10 * * * *", // 每10分钟执行一次
  execute: async function () {
    try {
      const ctx = Context.emptyContext();
      ctx.set("requestId", `cron_check_timeout_${Date.now()}`);
      const auditTaskService = MusicClubRecordingAuditTaskService.getInstance();
      const count = await auditTaskService.checkTimeoutTasks(ctx, musicClubCfg.auditTimeoutMinutes);
      if (count > 0) {
        logger.info({ ctx, count }, "CheckTimeoutAudioAuditTasksCompleted");
      }
      return count;
    } catch (error) {
      logger.error({ error }, "CheckTimeoutAudioAuditTasksError");
      return 0;
    }
  },
});

manager.add({
  name: "makeupTransfer",
  cronTime: CronPattern.EVERY_HOUR,
  execute: async () => {
    // 检查一个小时前的数据是否有遗漏
    const now = Date.now();
    const oneHourAgo = now - 60 * 60 * 1000;
    const startTime = now - 3 * 60 * 60 * 1000;
    const transfers = await TransferModel.executeByQuery(
      TransferModel.scope().where("status", TransferStatus.INIT).whereBetween("createTime", [startTime, oneHourAgo])
    );
    for (const transfer of transfers) {
      transferLogger.info({ transfer }, "MakeupTransfer");
      await playerTransfer(transfer.oldId, transfer.newId);
    }
  },
});


if (musicClubCfg.enableHotRankCacheRefresh) {
  manager.add({
    name: "RefreshMusicClubRankCache",
    cronTime: musicClubCfg.refreshRankCacheCron,
    execute: async function () {
      const rankingCache = MusicClubRankingCache.getInstance();
      await rankingCache.refreshCache();
    },
  });

}


async function main() {
  try {
    manager.runAll();
    logger.info("Cron jobs started");
    if (cronJobCfg.embedHealthCheckServer) {
      embedHealthCheckServer("pyqCrons");
    }
  } catch (error) {
    console.error(error);
  }
}

if (require.main === module) {
  main();
} else {
  exports.manager = manager;
}