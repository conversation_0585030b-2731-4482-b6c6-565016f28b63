"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const taskManager_2 = require("./taskManager");
const SummerLbsService = require("../pyq-server/services/summerlbs");
const PyqRank = require("../service/qnm/pyq/rank");
const GmReviewInfos = require("../pyq-server/services/gmReviewInfos");
const recalMomentHotValue_1 = require("./recalMomentHotValue");
const HotMomentsCache = require("../service/qnm/pyq/HotMomentsCache");
const delayTasks_1 = require("./delayTasks");
const flower_1 = require("../service/qnm/pyq/events/flower");
const config_1 = require("../common/config");
const yunyingLogger_1 = require("../pyq-server/common/yunyingLogger");
const logger_1 = require("../pyq-server/logger");
const transfer_1 = require("../pyq-server/models/transfer");
const transfer_2 = require("../pyq-server/services/transfer");
const config_2 = require("../pyq-server/common/config");
const fixMomentIdServerIdService_1 = require("../pyq-server/services/fixMomentIdServerIdService");
const helper_1 = require("../pyq-server/helper");
const musicClubRankingCache_1 = require("../pyq-server/services/music-club/musicClubRankingCache");
const musicClubRecordingAuditTaskService_1 = require("../pyq-server/services/music-club/musicClubRecordingAuditTaskService");
const context_1 = require("../pyq-server/context");
const logger = (0, logger_1.clazzLogger)("cron-jobs/pyqCrons");
const manager = taskManager_2.TaskManager.create("pyqCrons", config_1.cronTaskSwitch);
// 暑假活动定时设置食物过期
manager.add({
    name: "SummerLbsJob",
    cronTime: taskManager_2.CronPattern.EVERY_MINUTE,
    execute: function () {
        return __awaiter(this, void 0, void 0, function* () {
            return SummerLbsService.removeExpireFoodArea();
        });
    },
});
// 倩女手游排行榜定时刷缓存
manager.add({
    name: "RefreshPlayersRank",
    cronTime: "*/15 * * * *",
    execute: function () {
        return __awaiter(this, void 0, void 0, function* () {
            return PyqRank.refreshTopPlayerRanks({ intervalTime: 200 });
        });
    },
});
// 定时获取GM心易客服数据
manager.add({
    name: "FetchGmReviews",
    cronTime: "30 2 * * *",
    execute: function () {
        return __awaiter(this, void 0, void 0, function* () {
            return GmReviewInfos.fetAllGmReviews();
        });
    },
});
// 热度值随时间衰减
manager.add({
    name: "RefreshMomentHotValue",
    cronTime: "35 */2 * * *",
    execute: function () {
        return __awaiter(this, void 0, void 0, function* () {
            return (0, recalMomentHotValue_1.updatePyqMomentsHot)();
        });
    },
});
if (config_2.fixMomentIdServerIdCfg.enable) {
    // 修正动态表的serverId字段
    manager.add({
        name: "FixMomentServerId",
        cronTime: "*/10 * * * *",
        execute: function () {
            return __awaiter(this, void 0, void 0, function* () {
                (0, fixMomentIdServerIdService_1.autoFixMomentServerId)();
            });
        },
    });
}
// 重建热门列表缓存
manager.add({
    name: "RebuildHotMomentCache",
    cronTime: "*/10 * * * *",
    execute: function () {
        return __awaiter(this, void 0, void 0, function* () {
            return HotMomentsCache.refreshAll();
        });
    },
});
manager.add({
    name: "TriggerDelayTask",
    cronTime: taskManager_2.CronPattern.EVERY_SECOND,
    execute: function () {
        return __awaiter(this, void 0, void 0, function* () {
            yield delayTasks_1.PyqDelayQueue.onTick();
        });
    },
});
manager.add({
    name: "ProcessReadyTask",
    cronTime: taskManager_2.CronPattern.EVERY_SECOND,
    execute: function () {
        return __awaiter(this, void 0, void 0, function* () {
            return (0, delayTasks_1.processReadyTask)();
        });
    },
});
manager.add({
    name: "SyncGiftEventToDb",
    cronTime: taskManager_2.CronPattern.EVERY_SECOND,
    execute: function () {
        return __awaiter(this, void 0, void 0, function* () {
            return (0, flower_1.syncGiftEventToDB)(100);
        });
    },
});
// 定时回收运营日志
manager.add({
    name: "unlinkYunyingLog",
    cronTime: taskManager_2.CronPattern.EVERY_HOUR,
    execute: function () {
        return __awaiter(this, void 0, void 0, function* () {
            (0, yunyingLogger_1.unlinkLog)();
        });
    },
});
// 重试待处理的音频审核任务
manager.add({
    name: "RetryPendingAudioAuditTasks",
    cronTime: "*/5 * * * *",
    execute: function () {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const ctx = context_1.Context.emptyContext();
                ctx.set("requestId", `cron_retry_audit_${Date.now()}`);
                const auditTaskService = musicClubRecordingAuditTaskService_1.MusicClubRecordingAuditTaskService.getInstance();
                const count = yield auditTaskService.retryPendingTasks(ctx, config_2.musicClubCfg.auditMaxRetries);
                if (count > 0) {
                    logger.info({ ctx, count }, "RetryPendingAudioAuditTasksCompleted");
                }
                return count;
            }
            catch (error) {
                logger.error({ error }, "RetryPendingAudioAuditTasksError");
                return 0;
            }
        });
    },
});
// 检查超时的音频审核任务
manager.add({
    name: "CheckTimeoutAudioAuditTasks",
    cronTime: "*/10 * * * *",
    execute: function () {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const ctx = context_1.Context.emptyContext();
                ctx.set("requestId", `cron_check_timeout_${Date.now()}`);
                const auditTaskService = musicClubRecordingAuditTaskService_1.MusicClubRecordingAuditTaskService.getInstance();
                const count = yield auditTaskService.checkTimeoutTasks(ctx, config_2.musicClubCfg.auditTimeoutMinutes);
                if (count > 0) {
                    logger.info({ ctx, count }, "CheckTimeoutAudioAuditTasksCompleted");
                }
                return count;
            }
            catch (error) {
                logger.error({ error }, "CheckTimeoutAudioAuditTasksError");
                return 0;
            }
        });
    },
});
manager.add({
    name: "makeupTransfer",
    cronTime: taskManager_2.CronPattern.EVERY_HOUR,
    execute: () => __awaiter(void 0, void 0, void 0, function* () {
        // 检查一个小时前的数据是否有遗漏
        const now = Date.now();
        const oneHourAgo = now - 60 * 60 * 1000;
        const startTime = now - 3 * 60 * 60 * 1000;
        const transfers = yield transfer_1.TransferModel.executeByQuery(transfer_1.TransferModel.scope().where("status", transfer_2.TransferStatus.INIT).whereBetween("createTime", [startTime, oneHourAgo]));
        for (const transfer of transfers) {
            logger_1.transferLogger.info({ transfer }, "MakeupTransfer");
            yield (0, transfer_2.playerTransfer)(transfer.oldId, transfer.newId);
        }
    }),
});
if (config_2.musicClubCfg.enableHotRankCacheRefresh) {
    manager.add({
        name: "RefreshMusicClubRankCache",
        cronTime: config_2.musicClubCfg.refreshRankCacheCron,
        execute: function () {
            return __awaiter(this, void 0, void 0, function* () {
                const rankingCache = musicClubRankingCache_1.MusicClubRankingCache.getInstance();
                yield rankingCache.refreshCache();
            });
        },
    });
}
function main() {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            manager.runAll();
            logger.info("Cron jobs started");
            if (config_2.cronJobCfg.embedHealthCheckServer) {
                (0, helper_1.embedHealthCheckServer)("pyqCrons");
            }
        }
        catch (error) {
            console.error(error);
        }
    });
}
if (require.main === module) {
    main();
}
else {
    exports.manager = manager;
}
//# sourceMappingURL=pyqCrons.js.map