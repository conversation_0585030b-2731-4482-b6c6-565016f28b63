import * as mdHotMomentsService from '../md-server/services/HotMoments'
import * as _ from 'lodash'
import * as ModelManager from '../models/ModelManager'
import { calHot as recalMomentHot } from '../service/qnm/pyq/moment'
import * as bluebird from 'bluebird'
import {PyqMomentRecord} from '../common/type'

const PyqMoments = ModelManager.getModelByTableName<PyqMomentRecord>('pyq_moment')

const SCAN_MOMENT_MAX_SIZE = 100000// 限制扫表的最大数量
const SCAN_MOMENT_PAGE_SIZE = 500// 每次取多少来更新
const UPDATE_MOMENT_PER_DELAY = 50 // 更新时每条更新的间隔时间，减少对DB的冲击

async function updateMdMomentsHot() {
  let result = await mdHotMomentsService.updatePublishDurationAndHot()
  return result
}

async function findScanIds(page): Promise<number[]> {
  const PyqMoments = ModelManager.getModelByTableName('pyq_moment', 'SLAVE')
  const query = PyqMoments.normalScope()
    .select('ID')
    .orderBy('Hot', 'desc')
  const rows = await PyqMoments.queryWithPagination(query, { page: page, pageSize: SCAN_MOMENT_PAGE_SIZE }) as { ID: number }[]
  return _.map(rows, 'ID')
}

async function updateHotByMomentIds(ids: number[], now: number) {
  for (let i = 0; i < ids.length; i++) {
    const id = ids[i]
    const moment = await PyqMoments.findById(id, ['CreateTime', 'HotState', 'Hot'])
    const newHot = recalMomentHot(moment, 'time', null, {
      now: now
    })
    const updateValues = { HotState: JSON.stringify(newHot.state), Hot: newHot.hot }
    const query = PyqMoments.scope().where('ID', id).update(updateValues)
    await PyqMoments.executeByQuery(query)
    await bluebird.delay(UPDATE_MOMENT_PER_DELAY)
  }
}

export async function updatePyqMomentsHot() {
  const now = Date.now() // 同一批计算使用同一个当前时间来计算热度
  const totalPage = Math.ceil(SCAN_MOMENT_MAX_SIZE / SCAN_MOMENT_PAGE_SIZE)
  for (let i = 1; i <= totalPage; i++) {
    const mIds = await findScanIds(i)
    if (_.isEmpty(mIds)) {
      break
    } else {
      await updateHotByMomentIds(mIds, now)
    }
  }
}

export async function updateMomentHot() {
  try {
    let r1 = await updateMdMomentsHot()
    let r2 = await updatePyqMomentsHot()
    return [r1, r2]
  } catch (err) {
    console.error(err)
  }
}
