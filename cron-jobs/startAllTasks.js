const CronJob = require('cron').CronJob

class JobManger {
  constructor () {
    this.jobs = []
  }

  startJobs () {
    const jobs = this.jobs
    for (let i = 0; i < jobs.length; i++) {
      const job = jobs[i]
      job.cronJob.start()
      if (job.cronJob.running) {
        console.log(`Job ${job.name} status is running`)
      } else {
        console.log(`Error! ${job.name} run failed`)
      }
    }
  }

  addJob (name, cronJobOptions) {
    const cronJob = new CronJob(cronJobOptions)
    const job = {name: name, cronJob: cronJob}
    this.jobs.push(job)
    return this
  }
}

const jobManager = new JobManger()

// summerLbs 暑假活动
jobManager.addJob('summerLbsJob', {
  cronTime: '*/1 * * * *', // At every 1 minute
  onTick: function () {
    const summerLbsService = require('../pyq-server/services/summerlbs')
    summerLbsService.removeExpireFoodArea() 
    // 定时设置食物过期
  },
  runOnInit: true
})

// 倩女手游图库
const QnmTukuService = require('../md-server/services/QnmTukuService')
jobManager.addJob('refreshQnmTukuChannelAlbumJob', {
  cronTime: '0 */2 * * *', // At minute 0 past every 2 hour
  onTick: function () {
    QnmTukuService.refreshAllChannelAlbum()
  },
  runOnInit: true
})

jobManager.addJob('QnmTukuCorrectJob', {
  cronTime: '*10 * * * *', // At every 10th minute
  onTick: function () {
    QnmTukuService.correctTopAlbumsInAllChannels()
  },
  runOnInit: true
})

// 定时根据心情的热度值重排生产热门心情列表
const HotMomentsCache = require('../service/qnm/pyq/HotMomentsCache')
const MdHotMoments = require('../md-server/services/HotMoments')
jobManager.addJob('refreshMomentRankJob ', {
  cronTime: '*/10 * * * *', // At every 10th minute
  onTick: function () {
    MdHotMoments.refreshAll()
    return HotMomentsCache.refreshAll()
  },
  runOnInit: true
})

// 定时从数据中心更新倩女端游排行榜
jobManager.addJob('refreshQnRankListJob', {
  cronTime: '0 */4 * * *', // At past every 4th hour.”
  onTick: function () {
    require('./refreshQnRankingList')()
  },
  runOnInit: true
})

// 热度随着时间变化，重新计算mdMoment和pyqMoment的Hot字段的值
jobManager.addJob('recalcuateMomentHotJob', {
  cronTime: '35 */2 * * *', // At minute 35 past every 2 hour.
  onTick: function () {
    require('./recalMomentHotValue')()
  },
  runOnInit: false
})

// 倩女手游排行榜定时刷缓存
jobManager.addJob('refreshQnmPlayersRankJob', {
  cronTime: '*/15 * * * * ', // At every 15 minute
  onTick: function () {
    require('../service/qnm/pyq/rank').refreshTopPlayerRanks({intervalTime: 200})
  },
  runOnInit: true
})

// 定时获取GM心易客服数据
jobManager.addJob('fetchGmReviews', {
  cronTime: '30 2 * * *', // 每天02：30
  onTick: function () {
    require('../pyq-server/services/gmReviewInfos').fetAllGmReviews()
  },
  runOnInit: false
})

// 定时刷新同步推送倩女手游角色信息
jobManager.addJob('syncQnmPushInfoToDB', {
  cronTime: '* * * * * *', // 每秒钟
  onTick: function () {
    require('../service/qnm/client/syncQueueWriter').syncToDB(300)
  },
  runOnInit: true
})

// 定时刷新同步推送倩女端游角色信息
jobManager.addJob('syncQnPushInfoToDB', {
  cronTime: '* * * * * *', // 每秒钟
  onTick: function () {
    require('../service/qn/client/syncQueueWriter').syncToDB(300)
  },
  runOnInit: true
})

jobManager.startJobs()
