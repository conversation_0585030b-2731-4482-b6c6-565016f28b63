"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendAlarmMessage = void 0;
const redisCollection_1 = require("../common/redisCollection");
const PoPoAlarm = require("../common/popoAlarm");
const taskManager_2 = require("./taskManager");
const config_1 = require("../pyq-server/common/config");
let qnQueue = new redisCollection_1.Queue({ key: "qn:sync_info_queue" });
let qnmQueue = new redisCollection_1.Queue({ key: "qnm:sync_info_queue" });
let tasks = [
    { game: "倩女端游", queue: qnQueue, alarmSize: config_1.watchQueueCfg.alarmSize },
    { game: "倩女手游", queue: qnmQueue, alarmSize: config_1.watchQueueCfg.alarmSize },
];
function CheckSyncQueueLength() {
    return __awaiter(this, void 0, void 0, function* () {
        for (let t of tasks) {
            let size = yield t.queue.len();
            if (size >= t.alarmSize) {
                yield sendAlarmMessage(t.game, size, t.alarmSize);
            }
            else {
                console.log("GetQueueLength", { game: t.game, size: size, alarmSize: t.alarmSize });
            }
        }
    });
}
function sendAlarmMessage(game, size, alarmSize) {
    return __awaiter(this, void 0, void 0, function* () {
        let msg = `${game}角色信息同步队列过长! 当前值: ${size}, 警戒值: ${alarmSize}`;
        console.log("PrepareSendAlarm", { game: game, size: size, alarmSize: alarmSize });
        let ret = yield PoPoAlarm.sendPoPo("watchQueue", msg);
        return ret;
    });
}
exports.sendAlarmMessage = sendAlarmMessage;
let manager = taskManager_2.TaskManager.create('l10Crons', { QueueConsumerMonitor: true });
// 检查队列消费情况
manager.add({
    name: 'QueueConsumerMonitor',
    cronTime: taskManager_2.CronPattern.EVERY_SECOND,
    execute: function () {
        return __awaiter(this, void 0, void 0, function* () {
            const ret = yield CheckSyncQueueLength();
            return ret;
        });
    }
});
if (require.main === module) {
    manager.runAll();
}
else {
    exports.manager = manager;
}
//# sourceMappingURL=watchSyncQueueMonitor.js.map