import { Queue } from "../common/redisCollection";
import * as PoPoAlarm from "../common/popoAlarm";
import { TaskManager, CronPattern } from './taskManager'
import { watchQueueCfg } from "../pyq-server/common/config";


let qnQueue = new Queue({ key: "qn:sync_info_queue" });
let qnmQueue = new Queue({ key: "qnm:sync_info_queue" });

let tasks = [
  { game: "倩女端游", queue: qnQueue, alarmSize: watchQueueCfg.alarmSize },
  { game: "倩女手游", queue: qnmQueue, alarmSize: watchQueueCfg.alarmSize },
];

async function CheckSyncQueueLength() {
  for (let t of tasks) {
    let size = await t.queue.len();
    if (size >= t.alarmSize) {
      await sendAlarmMessage(t.game, size, t.alarmSize);
    } else {
      console.log("GetQueueLength", { game: t.game, size: size, alarmSize: t.alarmSize });
    }
  }
}

export async function sendAlarmMessage(game: string, size: number, alarmSize: number) {
  let msg = `${game}角色信息同步队列过长! 当前值: ${size}, 警戒值: ${alarmSize}`;
  console.log("PrepareSendAlarm", { game: game, size: size, alarmSize: alarmSize });
  let ret = await PoPoAlarm.sendPoPo("watchQueue", msg);
  return ret;
}


let manager = TaskManager.create('l10Crons', { QueueConsumerMonitor: true })

// 检查队列消费情况
manager.add({
  name: 'QueueConsumerMonitor',
  cronTime: CronPattern.EVERY_SECOND,
  execute: async function () {
    const ret = await CheckSyncQueueLength()
    return ret
  }
})


if (require.main === module) {
  manager.runAll()
} else {
  exports.manager = manager
}