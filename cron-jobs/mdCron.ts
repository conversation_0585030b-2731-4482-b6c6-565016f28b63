import { TaskManager, CronPattern } from "./taskManager";
import * as QnmTukuService from "../md-server/services/QnmTukuService";
import * as MdHotMomentsCache from "../md-server/services/HotMoments";
import { syncToDB } from "../service/qn/client/syncQueueWriter";
import { updateRankingList } from "./refreshQnRankingList";

const TaskSwitch = {
  RefreshRankingList: true,
  RefreshQnmTukuAlbum: true,
  RefreshQnmTopAlbum: true,
  RefreshMomentHotValue: true,
  RebuildHotMomentCache: true,
  SyncPushInfoToDB: true,
};

const manager = TaskManager.create("mdCrons", TaskSwitch);

// 倩女端游排行榜
manager.add({
  name: "RefreshRankingList",
  cronTime: "0 */6 * * *", // At past every 6th hour.”
  execute: async function () {
    return updateRankingList();
  },
});

// 倩女手游图库刷新
manager.add({
  name: "RefreshQnmTukuAlbum",
  cronTime: CronPattern.EVERY_TWO_HOUR,
  execute: async function () {
    return QnmTukuService.refreshAllChannelAlbum();
  },
});

manager.add({
  name: "RefreshQnmTopAlbum",
  cronTime: CronPattern.EVERY_TEN_MINUTE,
  execute: async function () {
    return QnmTukuService.correctTopAlbumsInAllChannels();
  },
});

// 热度值随时间衰减
manager.add({
  name: "RefreshMomentHotValue",
  cronTime: "35 */2 * * *",
  execute: async function () {
    return MdHotMomentsCache.updatePublishDurationAndHot();
  },
});

// 重建热门列表缓存
manager.add({
  name: "RebuildHotMomentCache",
  cronTime: "*/10 * * * *", // At every 10th minute
  execute: async function () {
    return MdHotMomentsCache.refreshAll();
  },
});

// 同步手游推送数据队列
manager.add({
  name: "SyncPushInfoToDB",
  cronTime: CronPattern.EVERY_SECOND,
  execute: async function () {
    return syncToDB(400);
  },
});

if (require.main === module) {
  manager.runAll();
} else {
  exports.manager = manager;
}
