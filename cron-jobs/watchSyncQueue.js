"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendAlarmMessage = void 0;
const redisCollection_1 = require("../common/redisCollection");
const PoPoAlarm = require("../common/popoAlarm");
let qnQueue = new redisCollection_1.Queue({ key: "qn:sync_info_queue" });
let qnmQueue = new redisCollection_1.Queue({ key: "qnm:sync_info_queue" });
let tasks = [
    { game: "倩女端游", queue: qnQueue, alarmSize: 5000 },
    { game: "倩女手游", queue: qnmQueue, alarmSize: 5000 },
];
function main() {
    return __awaiter(this, void 0, void 0, function* () {
        for (let t of tasks) {
            let size = yield t.queue.len();
            if (size >= t.alarmSize) {
                yield sendAlarmMessage(t.game, size, t.alarmSize);
            }
            else {
                console.log("GetQueueLength", { game: t.game, size: size, alarmSize: t.alarmSize });
            }
        }
    });
}
function sendAlarmMessage(game, size, alarmSize) {
    return __awaiter(this, void 0, void 0, function* () {
        let msg = `${game}角色信息同步队列过长! 当前值: ${size}, 警戒值: ${alarmSize}`;
        let ret = yield PoPoAlarm.sendPoPo("watchQueue", msg);
        return ret;
    });
}
exports.sendAlarmMessage = sendAlarmMessage;
main()
    .then((x) => {
    process.exit(0);
})
    .catch((err) => {
    console.error(err);
    process.exit(-1);
});
//# sourceMappingURL=watchSyncQueue.js.map