import { Queue } from "../common/redisCollection";
import * as PoPoAlarm from "../common/popoAlarm";

let qnQueue = new Queue({ key: "qn:sync_info_queue" });
let qnmQueue = new Queue({ key: "qnm:sync_info_queue" });

let tasks = [
  { game: "倩女端游", queue: qnQueue, alarmSize: 5000 },
  { game: "倩女手游", queue: qnmQueue, alarmSize: 5000 },
];

async function main() {
  for (let t of tasks) {
    let size = await t.queue.len();
    if (size >= t.alarmSize) {
      await sendAlarmMessage(t.game, size, t.alarmSize);
    } else {
      console.log("GetQueueLength", { game: t.game, size: size, alarmSize: t.alarmSize });
    }
  }
}

export async function sendAlarmMessage(game: string, size: number, alarmSize: number) {
  let msg = `${game}角色信息同步队列过长! 当前值: ${size}, 警戒值: ${alarmSize}`;
  let ret = await PoPoAlarm.sendPoPo("watchQueue", msg);
  return ret;
}

main()
  .then((x) => {
    process.exit(0);
  })
  .catch((err) => {
    console.error(err);
    process.exit(-1);
  });
