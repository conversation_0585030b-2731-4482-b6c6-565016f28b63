"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try {
            step(generator.next(value));
        }
        catch (e) {
            reject(e);
        } }
        function rejected(value) { try {
            step(generator["throw"](value));
        }
        catch (e) {
            reject(e);
        } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const taskManager_1 = require("./taskManager");
const syncQueueWriter_1 = require("../service/qnm/client/syncQueueWriter");
let manager = taskManager_1.TaskManager.create("l10_sync_push", { SyncPushInfoToDB: true });
// 同步手游推送数据队列
manager.add({
    name: "SyncPushInfoToDB",
    cronTime: taskManager_1.CronPattern.EVERY_SECOND,
    execute: function () {
        return __awaiter(this, void 0, void 0, function* () {
            return syncQueueWriter_1.syncToDB(400);
        });
    },
});
if (require.main === module) {
    manager.runAll();
}
else {
    exports.manager = manager;
}
//# sourceMappingURL=pyqSyncQueue.js.map