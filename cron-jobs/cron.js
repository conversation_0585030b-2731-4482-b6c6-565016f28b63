"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const logger_1 = require("../pyq-server/logger");
const transfer_1 = require("../pyq-server/models/transfer");
const transfer_2 = require("../pyq-server/services/transfer");
const taskManager_2 = require("./taskManager");
const TaskSwitch = {
    makeupTransfer: true,
};
const manager = taskManager_2.TaskManager.create("pyqCrons", TaskSwitch);
manager.add({
    name: 'makeupTransfer',
    cronTime: taskManager_2.CronPattern.EVERY_HOUR,
    execute: () => __awaiter(void 0, void 0, void 0, function* () {
        // 检查一个小时前的数据是否有遗漏
        const now = Date.now();
        const oneHourAgo = now - 60 * 60 * 1000;
        const startTime = now - 3 * 60 * 60 * 1000;
        const transfers = yield transfer_1.TransferModel.executeByQuery(transfer_1.TransferModel.scope().where('status', transfer_2.TransferStatus.INIT).whereBetween('createTime', [startTime, oneHourAgo]));
        for (const transfer of transfers) {
            logger_1.transferLogger.info({ transfer }, "MakeupTransfer");
            yield (0, transfer_2.playerTransfer)(transfer.oldId, transfer.newId);
        }
    }),
});
if (require.main === module) {
    manager.runAll();
}
else {
    exports.manager = manager;
}
//# sourceMappingURL=cron.js.map