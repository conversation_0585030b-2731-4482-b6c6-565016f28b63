"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const taskManager_2 = require("./taskManager");
const QnmTukuService = require("../md-server/services/QnmTukuService");
const MdHotMomentsCache = require("../md-server/services/HotMoments");
const syncQueueWriter_2 = require("../service/qn/client/syncQueueWriter");
const refreshQnRankingList_1 = require("./refreshQnRankingList");
const TaskSwitch = {
    RefreshRankingList: true,
    RefreshQnmTukuAlbum: true,
    RefreshQnmTopAlbum: true,
    RefreshMomentHotValue: true,
    RebuildHotMomentCache: true,
    SyncPushInfoToDB: true,
};
const manager = taskManager_2.TaskManager.create("mdCrons", TaskSwitch);
// 倩女端游排行榜
manager.add({
    name: "RefreshRankingList",
    cronTime: "0 */6 * * *",
    execute: function () {
        return __awaiter(this, void 0, void 0, function* () {
            return (0, refreshQnRankingList_1.updateRankingList)();
        });
    },
});
// 倩女手游图库刷新
manager.add({
    name: "RefreshQnmTukuAlbum",
    cronTime: taskManager_2.CronPattern.EVERY_TWO_HOUR,
    execute: function () {
        return __awaiter(this, void 0, void 0, function* () {
            return QnmTukuService.refreshAllChannelAlbum();
        });
    },
});
manager.add({
    name: "RefreshQnmTopAlbum",
    cronTime: taskManager_2.CronPattern.EVERY_TEN_MINUTE,
    execute: function () {
        return __awaiter(this, void 0, void 0, function* () {
            return QnmTukuService.correctTopAlbumsInAllChannels();
        });
    },
});
// 热度值随时间衰减
manager.add({
    name: "RefreshMomentHotValue",
    cronTime: "35 */2 * * *",
    execute: function () {
        return __awaiter(this, void 0, void 0, function* () {
            return MdHotMomentsCache.updatePublishDurationAndHot();
        });
    },
});
// 重建热门列表缓存
manager.add({
    name: "RebuildHotMomentCache",
    cronTime: "*/10 * * * *",
    execute: function () {
        return __awaiter(this, void 0, void 0, function* () {
            return MdHotMomentsCache.refreshAll();
        });
    },
});
// 同步手游推送数据队列
manager.add({
    name: "SyncPushInfoToDB",
    cronTime: taskManager_2.CronPattern.EVERY_SECOND,
    execute: function () {
        return __awaiter(this, void 0, void 0, function* () {
            return (0, syncQueueWriter_2.syncToDB)(400);
        });
    },
});
if (require.main === module) {
    manager.runAll();
}
else {
    exports.manager = manager;
}
//# sourceMappingURL=mdCron.js.map