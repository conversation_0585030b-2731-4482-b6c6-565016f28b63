"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.processReadyTask = exports.DELAY_TOPICS = exports.PyqDelayQueue = void 0;
const delayQueue_1 = require("../common/delayQueue");
const PyqActivityTopic_1 = require("../models/PyqActivityTopic");
const logger_1 = require("../pyq-server/logger");
const activityTopic_1 = require("../pyq-server/services/activityTopic");
const transfer_1 = require("../pyq-server/services/transfer");
const HotMomentsCache = require("../service/qnm/pyq/HotMomentsCache");
exports.PyqDelayQueue = new delayQueue_1.DelayQueue("pyq_delay_queue");
exports.DELAY_TOPICS = {
    DownHotMomentTopic: "DownHotMomentTopic",
    DownHotMoment: "DownHotMoment",
    TopTopic: "TopTopic",
    PlayerTransfer: "PlayerTransfer",
};
function processReadyTask() {
    return __awaiter(this, void 0, void 0, function* () {
        const job = yield exports.PyqDelayQueue.popReady();
        if (!job)
            return;
        if (job.topic === exports.DELAY_TOPICS.DownHotMomentTopic) {
            const params = job.body;
            return HotMomentsCache.downHotMoments([params.momentId], "" + params.serverId, null);
        }
        else if (job.topic === exports.DELAY_TOPICS.DownHotMoment) {
            const params = job.body;
            return HotMomentsCache.downHotMoments(params.momentIds, "" + params.serverId, null);
        }
        else if (job.topic === exports.DELAY_TOPICS.TopTopic) {
            const body = job.body;
            logger_1.apiLogger.info({ job: job }, "ProcessTopTopicTask");
            return (0, activityTopic_1.updateTopicStatus)({ id: body.topicId, status: PyqActivityTopic_1.TopicStatus.TOP });
        }
        else if (job.topic === exports.DELAY_TOPICS.PlayerTransfer) {
            const body = job.body;
            return (0, transfer_1.playerTransfer)(body.oldId, body.newId);
        }
    });
}
exports.processReadyTask = processReadyTask;
//# sourceMappingURL=delayTasks.js.map