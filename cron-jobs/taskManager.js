"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CronPattern = exports.TaskManager = void 0;
const cron_1 = require("cron");
const logger2_1 = require("../common/logger2");
const logger = (0, logger2_1.getLogger)('cronTasks');
class Task {
    constructor(option) {
        this.running = false;
        this.name = option.name;
        this.cronTime = option.cronTime;
        this.execute = option.execute;
        this.logger = option.logger;
        this.manager = option.manager;
    }
    run() {
        return __awaiter(this, void 0, void 0, function* () {
            if (!this.running) {
                try {
                    this.running = true;
                    const startTime = Date.now();
                    yield this.execute();
                    const endTime = Date.now();
                    this.manager.recordFinishInfo(this, startTime, endTime);
                }
                catch (err) {
                    this.logger.error(err);
                }
                finally {
                    this.running = false;
                }
            }
            else {
                this.logger.warn('Fire running task, slow down trigger frequency!');
            }
        });
    }
    static create(option) {
        return new Task(option);
    }
}
const LOG_RUN_INFO_INTERVAL = 15 * 60 * 1000;
class TaskManager {
    constructor(name, flags) {
        this.jobMap = new Map();
        this.finishInfo = new Map();
        this.name = name;
        this.logger = logger.child({ manager: this.name });
        this.flags = flags;
    }
    static create(name, flags) {
        return new TaskManager(name, flags);
    }
    addByTask(task) {
        let runOnInit = this.isEnableTask(task.name);
        let job = new cron_1.CronJob({
            cronTime: task.cronTime,
            runOnInit: runOnInit,
            onTick: function () {
                return __awaiter(this, void 0, void 0, function* () {
                    try {
                        yield task.run();
                    }
                    catch (err) {
                        logger.error(err);
                    }
                });
            }
        });
        this.jobMap.set(task.name, job);
    }
    add(option) {
        let logger = this.logger.child({ task: option.name });
        let params = Object.assign({}, option, { logger: logger, manager: this });
        let task = Task.create(params);
        this.addByTask(task);
    }
    isEnableTask(taskName) {
        return !!this.flags[taskName];
    }
    recordFinishInfo(task, startTime, endTime) {
        this.finishInfo.set(task.name, { start: startTime, end: endTime });
    }
    printFinishInfo() {
        for (let [name, td] of this.finishInfo) {
            let duration = td.end - td.start;
            this.logger.info({ task: name, start: td.start, end: td.end, duration: duration }, 'LastFinishTaskTime');
        }
    }
    runAll() {
        for (let [name, job] of this.jobMap) {
            if (this.isEnableTask(name)) {
                this.logger.info('Begin to Run task ' + name);
                job.start();
            }
            else {
                this.logger.warn('Task:' + name + ' is disabled');
            }
        }
        setInterval(() => {
            this.printFinishInfo();
        }, LOG_RUN_INFO_INTERVAL);
    }
}
exports.TaskManager = TaskManager;
exports.CronPattern = {
    EVERY_MINUTE: '* * * * *',
    EVERY_TEN_MINUTE: '*/10 * * * *',
    EVERY_TWO_HOUR: '0 */2 * * *',
    EVERY_SECOND: '* * * * * *',
    EVERY_HOUR: '0 */1 * * *',
};
//# sourceMappingURL=taskManager.js.map