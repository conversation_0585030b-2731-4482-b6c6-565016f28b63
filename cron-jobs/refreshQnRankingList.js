"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateRankingList = void 0;
const _ = require('lodash');
const QnRankingList = require('../models/QnRankingList');
const QnServers = require('../models/QNServers');
const RankTypes = require('../service/qn/data/RankType.js');
const logger2_1 = require("../common/logger2");
let logger = (0, logger2_1.getLogger)('qn_rank');
function getServerIds() {
    return QnServers.findAll()
        .then(servers => {
        return _.map(servers, 'id');
    });
}
function getRankIds() {
    const rankIds = RankTypes.getAllRankIds();
    return _.reject(rankIds, rankId => RankTypes.isFromQnPush(rankId));
}
function getRankByServerIdAndRankId(serverId, rankId) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            let data = yield QnRankingList.getByServerIdAndRankId(serverId, rankId, { isSkipCache: true });
            return data;
        }
        catch (err) {
            logger.error({ serverId: serverId, rankId: rankId, err: err }, 'getRankFailed');
        }
    });
}
function updateRankingList() {
    return __awaiter(this, void 0, void 0, function* () {
        const rankIds = getRankIds();
        const serverIds = yield getServerIds();
        for (let i = 0; i < rankIds.length; i++) {
            const rankId = rankIds[i];
            let newRankList = [];
            for (let j = 0; j < serverIds.length; j++) {
                const serverId = serverIds[j];
                const ranking = yield getRankByServerIdAndRankId(serverId, rankId);
                yield QnRankingList.setUpdateTime(rankId, serverId, Date.now());
                newRankList = _.chain(ranking)
                    .concat(newRankList)
                    .uniqBy(_.property('Id'))
                    .orderBy('Val', 'desc')
                    .take(QnRankingList.RankingListMaxSize)
                    .value();
            }
            yield QnRankingList.setByRankId(rankId, newRankList);
            yield QnRankingList.setUpdateTime(rankId, 'all', Date.now());
        }
    });
}
exports.updateRankingList = updateRankingList;
//# sourceMappingURL=refreshQnRankingList.js.map