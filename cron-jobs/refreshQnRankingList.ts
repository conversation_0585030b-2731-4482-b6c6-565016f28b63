const _ = require('lodash')
const QnRankingList = require('../models/QnRankingList')
const QnServers = require('../models/QNServers')
const RankTypes = require('../service/qn/data/RankType.js')
import { getLogger } from '../common/logger2'
let logger = getLogger('qn_rank')

function getServerIds() {
  return QnServers.findAll()
    .then(servers => {
      return _.map(servers, 'id')
    })
}

function getRankIds() {
  const rankIds = RankTypes.getAllRankIds()
  return _.reject(rankIds, rankId => RankTypes.isFromQnPush(rankId))
}

async function getRankByServerIdAndRankId(serverId: number, rankId: number) {
  try {
    let data = await QnRankingList.getByServerIdAndRankId(serverId, rankId, { isSkipCache: true })
    return data
  } catch (err) {
    logger.error({ serverId: serverId, rankId: rankId, err: err }, 'getRankFailed')
  }
}

export async function updateRankingList() {
  const rankIds = getRankIds()
  const serverIds = await getServerIds()
  for (let i = 0; i < rankIds.length; i++) {
    const rankId = rankIds[i]
    let newRankList = []
    for (let j = 0; j < serverIds.length; j++) {
      const serverId = serverIds[j]
      const ranking = await getRankByServerIdAndRankId(serverId, rankId)
      await QnRankingList.setUpdateTime(rankId, serverId, Date.now())
      newRankList = _.chain(ranking)
        .concat(newRankList)
        .uniqBy(_.property('Id'))
        .orderBy('Val', 'desc')
        .take(QnRankingList.RankingListMaxSize)
        .value()
    }
    await QnRankingList.setByRankId(rankId, newRankList)
    await QnRankingList.setUpdateTime(rankId, 'all', Date.now())
  }
}