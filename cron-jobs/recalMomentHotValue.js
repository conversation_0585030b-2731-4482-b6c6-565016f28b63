"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateMomentHot = exports.updatePyqMomentsHot = void 0;
const mdHotMomentsService = require("../md-server/services/HotMoments");
const _ = require("lodash");
const ModelManager = require("../models/ModelManager");
const moment_1 = require("../service/qnm/pyq/moment");
const bluebird = require("bluebird");
const PyqMoments = ModelManager.getModelByTableName('pyq_moment');
const SCAN_MOMENT_MAX_SIZE = 100000; // 限制扫表的最大数量
const SCAN_MOMENT_PAGE_SIZE = 500; // 每次取多少来更新
const UPDATE_MOMENT_PER_DELAY = 50; // 更新时每条更新的间隔时间，减少对DB的冲击
function updateMdMomentsHot() {
    return __awaiter(this, void 0, void 0, function* () {
        let result = yield mdHotMomentsService.updatePublishDurationAndHot();
        return result;
    });
}
function findScanIds(page) {
    return __awaiter(this, void 0, void 0, function* () {
        const PyqMoments = ModelManager.getModelByTableName('pyq_moment', 'SLAVE');
        const query = PyqMoments.normalScope()
            .select('ID')
            .orderBy('Hot', 'desc');
        const rows = yield PyqMoments.queryWithPagination(query, { page: page, pageSize: SCAN_MOMENT_PAGE_SIZE });
        return _.map(rows, 'ID');
    });
}
function updateHotByMomentIds(ids, now) {
    return __awaiter(this, void 0, void 0, function* () {
        for (let i = 0; i < ids.length; i++) {
            const id = ids[i];
            const moment = yield PyqMoments.findById(id, ['CreateTime', 'HotState', 'Hot']);
            const newHot = (0, moment_1.calHot)(moment, 'time', null, {
                now: now
            });
            const updateValues = { HotState: JSON.stringify(newHot.state), Hot: newHot.hot };
            const query = PyqMoments.scope().where('ID', id).update(updateValues);
            yield PyqMoments.executeByQuery(query);
            yield bluebird.delay(UPDATE_MOMENT_PER_DELAY);
        }
    });
}
function updatePyqMomentsHot() {
    return __awaiter(this, void 0, void 0, function* () {
        const now = Date.now(); // 同一批计算使用同一个当前时间来计算热度
        const totalPage = Math.ceil(SCAN_MOMENT_MAX_SIZE / SCAN_MOMENT_PAGE_SIZE);
        for (let i = 1; i <= totalPage; i++) {
            const mIds = yield findScanIds(i);
            if (_.isEmpty(mIds)) {
                break;
            }
            else {
                yield updateHotByMomentIds(mIds, now);
            }
        }
    });
}
exports.updatePyqMomentsHot = updatePyqMomentsHot;
function updateMomentHot() {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            let r1 = yield updateMdMomentsHot();
            let r2 = yield updatePyqMomentsHot();
            return [r1, r2];
        }
        catch (err) {
            console.error(err);
        }
    });
}
exports.updateMomentHot = updateMomentHot;
//# sourceMappingURL=recalMomentHotValue.js.map