<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音乐社团审核管理后台</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🎵 音乐社团审核管理后台</h1>
            <div class="header-actions">
                <button id="refreshBtn" class="btn btn-primary">刷新数据</button>
            </div>
        </header>

        <!-- 统计概览 -->
        <section class="stats-section">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalCount">-</div>
                    <div class="stat-label">总审核任务</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="pendingCount">-</div>
                    <div class="stat-label">待审核</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="submittedCount">-</div>
                    <div class="stat-label">已送审</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="passedCount">-</div>
                    <div class="stat-label">审核通过</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="rejectedCount">-</div>
                    <div class="stat-label">审核拒绝</div>
                </div>
            </div>
        </section>

        <!-- 搜索筛选 -->
        <section class="search-section">
            <div class="search-form">
                <div class="form-row">
                    <div class="form-group">
                        <label>录音ID:</label>
                        <input type="text" id="recordingIdInput" placeholder="输入录音ID">
                    </div>
                    <div class="form-group">
                        <label>用户ID:</label>
                        <input type="text" id="userIdInput" placeholder="输入用户ID">
                    </div>
                    <div class="form-group">
                        <label>服务器ID:</label>
                        <input type="text" id="serverIdInput" placeholder="输入服务器ID">
                    </div>
                    <div class="form-group">
                        <label>审核状态:</label>
                        <select id="statusSelect">
                            <option value="">全部状态</option>
                            <option value="0">待审核</option>
                            <option value="1">已送审</option>
                            <option value="2">审核通过</option>
                            <option value="3">审核拒绝</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group time-range-group">
                        <label>时间范围:</label>
                        <div class="time-range-inputs">
                            <input type="datetime-local" id="startTimeInput" placeholder="开始时间">
                            <span class="time-separator">至</span>
                            <input type="datetime-local" id="endTimeInput" placeholder="结束时间">
                        </div>
                    </div>
                    <div class="form-actions">
                        <button id="searchBtn" class="btn btn-primary">搜索</button>
                        <button id="resetBtn" class="btn btn-secondary">重置</button>
                        <button id="todayBtn" class="btn btn-info">今天</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- 数据列表 -->
        <section class="table-section">
            <div class="table-header">
                <h3>审核任务列表</h3>
                <div class="table-actions">
                    <span id="resultCount" class="result-count">共 0 条记录</span>
                </div>
            </div>

            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>任务ID</th>
                            <th>录音ID</th>
                            <th>录音名称</th>
                            <th>用户ID</th>
                            <th>服务器ID</th>
                            <th>审核状态</th>
                            <th>重试次数</th>
                            <th>创建时间</th>
                            <th>更新时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="dataTableBody">
                        <tr>
                            <td colspan="10" class="loading">加载中...</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="pagination">
                <button id="prevPageBtn" class="btn btn-secondary" disabled>上一页</button>
                <span id="pageInfo">第 1 页，共 1 页</span>
                <button id="nextPageBtn" class="btn btn-secondary" disabled>下一页</button>
            </div>
        </section>
    </div>

    <!-- 详情弹窗 -->
    <div id="detailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>审核任务详情</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body" id="detailContent">
                <!-- 详情内容将通过JavaScript动态填充 -->
            </div>
            <div class="modal-footer">
                <button id="retryBtn" class="btn btn-warning" style="display: none;">重新提交审核</button>
                <button class="btn btn-secondary modal-close">关闭</button>
            </div>
        </div>
    </div>

    <!-- 确认弹窗 -->
    <div id="confirmModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>确认操作</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <p id="confirmMessage">确定要执行此操作吗？</p>
            </div>
            <div class="modal-footer">
                <button id="confirmBtn" class="btn btn-warning">确认</button>
                <button class="btn btn-secondary modal-close">取消</button>
            </div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div id="messageToast" class="toast"></div>

    <script src="js/api.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
