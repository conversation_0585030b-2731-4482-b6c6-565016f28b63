// 工具函数模块

// 格式化时间
function formatDateTime(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

// 格式化相对时间
function formatRelativeTime(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    const now = new Date();
    const diff = now - date;

    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 7) return `${days}天前`;

    return formatDateTime(dateString);
}

// 获取状态显示文本和样式
function getStatusInfo(status) {
    const statusMap = {
        0: { text: '待审核', class: 'status-pending' },
        1: { text: '已送审', class: 'status-submitted' },
        2: { text: '审核通过', class: 'status-passed' },
        3: { text: '审核拒绝', class: 'status-rejected' }
    };

    return statusMap[status] || { text: '未知', class: '' };
}

// 创建状态标签HTML
function createStatusBadge(status) {
    const info = getStatusInfo(status);
    return `<span class="status-badge ${info.class}">${info.text}</span>`;
}

// 显示消息提示
function showToast(message, type = 'success') {
    const toast = document.getElementById('messageToast');
    toast.textContent = message;
    toast.className = `toast ${type}`;
    toast.classList.add('show');

    setTimeout(() => {
        toast.classList.remove('show');
    }, 3000);
}

// 显示确认对话框
function showConfirm(message, onConfirm) {
    const modal = document.getElementById('confirmModal');
    const messageEl = document.getElementById('confirmMessage');
    const confirmBtn = document.getElementById('confirmBtn');

    messageEl.textContent = message;
    modal.style.display = 'block';

    // 移除之前的事件监听器
    const newConfirmBtn = confirmBtn.cloneNode(true);
    confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

    // 添加新的事件监听器
    newConfirmBtn.addEventListener('click', () => {
        modal.style.display = 'none';
        onConfirm();
    });
}

// 显示详情弹窗
function showDetailModal(taskData) {
    const modal = document.getElementById('detailModal');
    const content = document.getElementById('detailContent');
    const retryBtn = document.getElementById('retryBtn');

    // 构建详情HTML
    const detailHTML = `
        <div class="detail-section">
            <h4>📋 任务基本信息</h4>
            <div class="detail-grid">
                <div class="detail-item">
                    <div class="detail-label">任务ID</div>
                    <div class="detail-value">${taskData.ID || '-'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">录音ID</div>
                    <div class="detail-value">${taskData.RecordingId || '-'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">审核状态</div>
                    <div class="detail-value">${createStatusBadge(taskData.Status)}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">重试次数</div>
                    <div class="detail-value">${taskData.RetryCount || 0}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">CC任务ID</div>
                    <div class="detail-value">${taskData.CcTaskId || '-'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">创建时间</div>
                    <div class="detail-value">${formatDateTime(taskData.CreateTime)}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">更新时间</div>
                    <div class="detail-value">${formatDateTime(taskData.UpdateTime)}</div>
                </div>
            </div>
        </div>

        <div class="detail-section">
            <h4>🎵 录音信息</h4>
            <div class="detail-grid">
                <div class="detail-item">
                    <div class="detail-label">录音名称</div>
                    <div class="detail-value">${taskData.recording?.Name || '-'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">用户ID</div>
                    <div class="detail-value">${taskData.RoleId || '-'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">服务器ID</div>
                    <div class="detail-value">${taskData.recording?.ServerId || '-'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">音乐社团ID</div>
                    <div class="detail-value">${taskData.recording?.MusicClubId || '-'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">时长</div>
                    <div class="detail-value">${taskData.recording?.Duration || '-'}秒</div>
                </div>
            </div>
        </div>

        <div class="detail-section">
            <h4>🔗 音频链接</h4>
            <div class="detail-grid">
                <div class="detail-item">
                    <div class="detail-label">VocalUrl</div>
                    <div class="detail-value">${taskData.VocalUrl || '-'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">DataUrl</div>
                    <div class="detail-value">${taskData.recording?.DataUrl || '-'}</div>
                </div>
            </div>
        </div>

        ${taskData.RejectReason ? `
        <div class="detail-section">
            <h4>❌ 拒绝原因</h4>
            <div class="detail-value" style="color: #e74c3c; font-weight: 500;">
                ${taskData.RejectReason}
            </div>
        </div>
        ` : ''}
    `;

    content.innerHTML = detailHTML;

    // 控制重试按钮显示（管理后台允许无限制重试待审核任务）
    if (taskData.Status === 0) {
        retryBtn.style.display = 'inline-block';
        retryBtn.onclick = () => retryTask(taskData.ID);

        // 如果重试次数较高，显示警告样式
        if ((taskData.RetryCount || 0) >= 5) {
            retryBtn.className = 'btn btn-danger';
            retryBtn.textContent = `强制重试 (已重试${taskData.RetryCount || 0}次)`;
        } else {
            retryBtn.className = 'btn btn-warning';
            retryBtn.textContent = '重新提交审核';
        }
    } else {
        retryBtn.style.display = 'none';
    }

    modal.style.display = 'block';
}

// 重试任务
async function retryTask(taskId) {
    try {
        showToast('正在重新提交审核任务...', 'info');
        const response = await api.retryAuditTask(taskId);

        if (response.success) {
            showToast('重新提交成功！任务已重新进入审核队列', 'success');
        } else {
            showToast(`重新提交失败: ${response.message || '未知错误'}`, 'warning');
        }

        document.getElementById('detailModal').style.display = 'none';
        // 刷新列表
        window.loadAuditList();
    } catch (error) {
        showToast(`重新提交失败: ${error.message}`, 'error');
    }
}

// 截断长文本
function truncateText(text, maxLength = 30) {
    if (!text) return '-';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
}

// 初始化弹窗事件
function initModalEvents() {
    // 关闭弹窗事件
    document.querySelectorAll('.close, .modal-close').forEach(element => {
        element.addEventListener('click', (e) => {
            e.target.closest('.modal').style.display = 'none';
        });
    });

    // 点击弹窗外部关闭
    document.querySelectorAll('.modal').forEach(modal => {
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });
    });
}

// 页面加载完成后初始化弹窗事件
document.addEventListener('DOMContentLoaded', initModalEvents);
