// 主要逻辑文件

// 全局状态
let currentPage = 1;
let pageSize = 20;
let currentFilters = {};

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initEventListeners();
    setDefaultTimeRange();
    loadStatistics();
    loadAuditList();
});

// 初始化事件监听器
function initEventListeners() {
    // 刷新按钮
    document.getElementById('refreshBtn').addEventListener('click', () => {
        loadStatistics();
        loadAuditList();
    });

    // 搜索按钮
    document.getElementById('searchBtn').addEventListener('click', handleSearch);

    // 重置按钮
    document.getElementById('resetBtn').addEventListener('click', handleReset);

    // 今天按钮
    document.getElementById('todayBtn').addEventListener('click', handleTodayFilter);

    // 分页按钮
    document.getElementById('prevPageBtn').addEventListener('click', () => {
        if (currentPage > 1) {
            currentPage--;
            loadAuditList();
        }
    });

    document.getElementById('nextPageBtn').addEventListener('click', () => {
        currentPage++;
        loadAuditList();
    });

    // 回车搜索
    document.querySelectorAll('#recordingIdInput, #userIdInput, #serverIdInput').forEach(input => {
        input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                handleSearch();
            }
        });
    });
}

// 处理搜索
function handleSearch() {
    currentPage = 1;

    currentFilters = {
        recordingId: document.getElementById('recordingIdInput').value.trim(),
        userId: document.getElementById('userIdInput').value.trim(),
        serverId: document.getElementById('serverIdInput').value.trim(),
        status: document.getElementById('statusSelect').value,
        startTime: document.getElementById('startTimeInput').value,
        endTime: document.getElementById('endTimeInput').value
    };

    loadAuditList();
}

// 处理重置
function handleReset() {
    document.getElementById('recordingIdInput').value = '';
    document.getElementById('userIdInput').value = '';
    document.getElementById('serverIdInput').value = '';
    document.getElementById('statusSelect').value = '';
    document.getElementById('startTimeInput').value = '';
    document.getElementById('endTimeInput').value = '';

    currentFilters = {};
    currentPage = 1;
    setDefaultTimeRange();
    loadAuditList();
}

// 设置默认时间范围（今天）
function setDefaultTimeRange() {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const endOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);

    // 格式化为 datetime-local 格式
    const startTime = formatDateTimeLocal(today);
    const endTime = formatDateTimeLocal(endOfToday);

    document.getElementById('startTimeInput').value = startTime;
    document.getElementById('endTimeInput').value = endTime;
}

// 处理今天按钮点击
function handleTodayFilter() {
    setDefaultTimeRange();
    handleSearch();
}

// 格式化日期为 datetime-local 输入格式
function formatDateTimeLocal(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day}T${hours}:${minutes}`;
}

// 加载统计信息
async function loadStatistics() {
    try {
        const response = await api.getStatistics();
        const stats = response.data;

        document.getElementById('totalCount').textContent = stats.total || 0;
        document.getElementById('pendingCount').textContent = stats.pending || 0;
        document.getElementById('submittedCount').textContent = stats.submitted || 0;
        document.getElementById('passedCount').textContent = stats.passed || 0;
        document.getElementById('rejectedCount').textContent = stats.rejected || 0;
    } catch (error) {
        console.error('加载统计信息失败:', error);
        showToast('加载统计信息失败', 'error');
    }
}

// 加载审核任务列表
async function loadAuditList() {
    const tableBody = document.getElementById('dataTableBody');
    const resultCount = document.getElementById('resultCount');
    const pageInfo = document.getElementById('pageInfo');
    const prevBtn = document.getElementById('prevPageBtn');
    const nextBtn = document.getElementById('nextPageBtn');

    // 显示加载状态
    tableBody.innerHTML = '<tr><td colspan="10" class="loading">加载中...</td></tr>';

    try {
        const params = {
            ...currentFilters,
            page: currentPage,
            pageSize: pageSize
        };

        const response = await api.getAuditList(params);
        const { data, total, page, totalPages } = response;

        // 更新结果计数
        resultCount.textContent = `共 ${total} 条记录`;

        // 更新分页信息
        pageInfo.textContent = `第 ${page} 页，共 ${totalPages} 页`;
        prevBtn.disabled = page <= 1;
        nextBtn.disabled = page >= totalPages;

        // 渲染表格数据
        if (data && data.length > 0) {
            renderTableData(data);
        } else {
            tableBody.innerHTML = '<tr><td colspan="10" class="loading">暂无数据</td></tr>';
        }

    } catch (error) {
        console.error('加载审核任务列表失败:', error);
        showToast('加载数据失败', 'error');
        tableBody.innerHTML = '<tr><td colspan="10" class="loading">加载失败</td></tr>';
    }
}

// 渲染表格数据
function renderTableData(data) {
    const tableBody = document.getElementById('dataTableBody');

    const rows = data.map(item => `
        <tr>
            <td>${item.ID}</td>
            <td>${item.RecordingId}</td>
            <td title="${item.recording?.Name || '-'}">${truncateText(item.recording?.Name)}</td>
            <td>${item.recording?.RoleId || '-'}</td>
            <td>${item.recording?.ServerId || '-'}</td>
            <td>${createStatusBadge(item.Status)}</td>
            <td>${item.RetryCount || 0}</td>
            <td title="${formatDateTime(item.CreateTime)}">${formatRelativeTime(item.CreateTime)}</td>
            <td title="${formatDateTime(item.UpdateTime)}">${formatRelativeTime(item.UpdateTime)}</td>
            <td>
                <button class="btn btn-primary" onclick="viewDetail(${item.ID})" style="font-size: 12px; padding: 4px 8px;">
                    查看详情
                </button>
            </td>
        </tr>
    `).join('');

    tableBody.innerHTML = rows;
}

// 查看详情
async function viewDetail(taskId) {
    try {
        const response = await api.getAuditDetail(taskId);
        const taskData = response.data;
        showDetailModal(taskData);
    } catch (error) {
        console.error('获取任务详情失败:', error);
        showToast('获取详情失败', 'error');
    }
}

// 导出全局函数供HTML调用
window.loadAuditList = loadAuditList;
window.viewDetail = viewDetail;
