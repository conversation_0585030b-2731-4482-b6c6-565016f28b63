// API调用模块
class API {
    constructor() {
        this.baseURL = '/qnm/admin/api/music_club/audit';
    }

    // 通用请求方法
    async request(url, options = {}) {
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        try {
            const response = await fetch(url, config);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || `HTTP ${response.status}`);
            }

            return data;
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }

    // 获取审核任务列表
    async getAuditList(params = {}) {
        const queryString = new URLSearchParams();

        // 添加查询参数
        Object.keys(params).forEach(key => {
            if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
                queryString.append(key, params[key]);
            }
        });

        const url = `${this.baseURL}/list${queryString.toString() ? '?' + queryString.toString() : ''}`;
        return this.request(url);
    }

    // 获取审核任务详情
    async getAuditDetail(taskId) {
        const url = `${this.baseURL}/detail/${taskId}`;
        return this.request(url);
    }

    // 获取统计信息
    async getStatistics() {
        const url = `${this.baseURL}/statistics`;
        return this.request(url);
    }

    // 重新提交审核任务
    async retryAuditTask(taskId) {
        const url = `${this.baseURL}/retry/${taskId}`;
        return this.request(url, {
            method: 'POST'
        });
    }
}

// 创建全局API实例
window.api = new API();
