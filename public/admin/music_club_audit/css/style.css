/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    color: #2c3e50;
    font-size: 24px;
}

/* 统计卡片 */
.stats-section {
    margin-bottom: 20px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
}

.stat-number {
    font-size: 32px;
    font-weight: bold;
    color: #3498db;
    margin-bottom: 5px;
}

.stat-label {
    color: #7f8c8d;
    font-size: 14px;
}

/* 搜索表单 */
.search-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    align-items: end;
    flex-wrap: wrap;
}

.form-group {
    display: flex;
    flex-direction: column;
    min-width: 150px;
}

.form-group label {
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.form-group input,
.form-group select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #3498db;
}

.form-actions {
    display: flex;
    gap: 10px;
    align-items: end;
}

/* 时间范围选择器样式 */
.time-range-group {
    min-width: 300px;
}

.time-range-inputs {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: nowrap;
}

.time-range-inputs input[type="datetime-local"] {
    flex: 1;
    min-width: 140px;
}

.time-separator {
    color: #666;
    font-weight: 500;
    white-space: nowrap;
    padding: 0 4px;
}

/* 时间输入框样式优化 */
.time-range-inputs input[type="datetime-local"]::-webkit-calendar-picker-indicator {
    cursor: pointer;
    filter: invert(0.5);
}

.time-range-inputs input[type="datetime-local"]:hover::-webkit-calendar-picker-indicator {
    filter: invert(0.3);
}

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.btn-secondary {
    background-color: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
}

.btn-info {
    background-color: #17a2b8;
    color: white;
}

.btn-info:hover {
    background-color: #138496;
}

.btn-warning {
    background-color: #f39c12;
    color: white;
}

.btn-warning:hover {
    background-color: #e67e22;
}

.btn:disabled {
    background-color: #bdc3c7;
    cursor: not-allowed;
}

/* 表格样式 */
.table-section {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.table-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.result-count {
    color: #7f8c8d;
    font-size: 14px;
}

.table-container {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.data-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #555;
}

.data-table tr:hover {
    background-color: #f8f9fa;
}

.loading {
    text-align: center;
    color: #7f8c8d;
    font-style: italic;
}

/* 状态标签 */
.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-pending {
    background-color: #f39c12;
    color: white;
}

.status-submitted {
    background-color: #3498db;
    color: white;
}

.status-passed {
    background-color: #27ae60;
    color: white;
}

.status-rejected {
    background-color: #e74c3c;
    color: white;
}

/* 分页 */
.pagination {
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    border-top: 1px solid #eee;
}

/* 弹窗样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 80%;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #000;
}

/* 消息提示 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 4px;
    color: white;
    font-weight: 500;
    z-index: 1001;
    transform: translateX(400px);
    transition: transform 0.3s ease;
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    background-color: #27ae60;
}

.toast.error {
    background-color: #e74c3c;
}

.toast.warning {
    background-color: #f39c12;
}

/* 详情样式 */
.detail-section {
    margin-bottom: 20px;
}

.detail-section h4 {
    margin-bottom: 10px;
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 5px;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.detail-item {
    display: flex;
    flex-direction: column;
}

.detail-label {
    font-weight: 600;
    color: #555;
    margin-bottom: 5px;
}

.detail-value {
    color: #333;
    word-break: break-all;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .form-row {
        flex-direction: column;
    }

    .form-group {
        min-width: auto;
    }

    .time-range-group {
        min-width: auto;
    }

    .time-range-inputs {
        flex-wrap: wrap;
        gap: 10px;
    }

    .time-range-inputs input[type="datetime-local"] {
        min-width: 120px;
        flex: 1 1 45%;
    }

    .time-separator {
        order: 2;
        flex: 0 0 auto;
        margin: 0 5px;
    }

    .form-actions {
        flex-wrap: wrap;
        justify-content: center;
    }

    .form-actions .btn {
        margin: 2px;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
}
