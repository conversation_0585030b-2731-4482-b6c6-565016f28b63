<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>梦岛 GM 管理后台</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            display: flex;
            height: 100vh;
            background-color: #f4f7f6;
            color: #333;
        }

        .sidebar {
            width: 220px;
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        }

        .sidebar h2 {
            text-align: center;
            margin-top: 0;
            margin-bottom: 30px;
            font-size: 1.5em;
            color: #1abc9c;
        }

        .sidebar ul {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }

        .sidebar ul li {
            padding: 12px 15px;
            margin-bottom: 8px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s ease, color 0.3s ease;
            font-size: 0.95em;
        }

        .sidebar ul li:hover {
            background-color: #34495e;
            color: #1abc9c;
        }

        .sidebar ul li.active {
            background-color: #1abc9c;
            color: #fff;
            font-weight: bold;
        }

        .main-content {
            flex-grow: 1;
            padding: 30px;
            overflow-y: auto;
        }

        .page {
            display: none; /* Initially hide all pages */
            animation: fadeIn 0.5s ease-in-out;
        }

        .page.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .upload-area {
            border: 3px dashed #1abc9c;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            background-color: #fff;
            transition: background-color 0.3s ease, border-color 0.3s ease;
            margin-top: 20px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
        }

        .upload-area:hover, .upload-area.dragover {
            background-color: #e8f8f5;
            border-color: #16a085;
        }

        .upload-area p {
            margin: 0;
            font-size: 1.1em;
            color: #555;
        }
        .upload-area .upload-icon {
            font-size: 3em;
            color: #1abc9c;
            margin-bottom: 15px;
        }
        #fileName {
            margin-top: 15px;
            font-style: italic;
            color: #3498db;
        }

    </style>
</head>
<body>
    <div class="sidebar">
        <h2>梦岛 GM 工具</h2>
        <ul>
            <li id="nav-import-flower-data" class="active" onclick="showPage('import-flower-data')">导入送花数据</li>
            <!-- Future navigation items can be added here -->
        </ul>
    </div>

    <div class="main-content">
        <div id="page-import-flower-data" class="page active">
            <h1>导入送花数据</h1>
            <p>请上传包含送花数据的 Excel 文件 (.xlsx, .xls)。</p>

            <div id="upload-area" class="upload-area">
                <div class="upload-icon">📁</div>
                <p>点击此处选择文件，或将文件拖拽到这里</p>
                <input type="file" id="file-input" accept=".xlsx, .xls" style="display: none;">
                <p id="fileName"></p>
            </div>
            <div id="upload-status" style="margin-top: 20px;"></div>
            <div id="progress-details" style="margin-top: 15px; max-height: 300px; overflow-y: auto; background-color: #fff; border: 1px solid #eee; padding: 10px; border-radius: 5px;"></div>
        </div>

        <!-- Future pages can be added here -->
        <!--
        <div id="page-another-feature" class="page">
            <h1>Another Feature</h1>
            <p>Content for another feature...</p>
        </div>
        -->
    </div>

    <script>
        function showPage(pageId) {
            // Hide all pages
            document.querySelectorAll('.main-content .page').forEach(page => {
                page.classList.remove('active');
            });
            // Show the selected page
            document.getElementById('page-' + pageId).classList.add('active');

            // Update active state in sidebar
            document.querySelectorAll('.sidebar ul li').forEach(navItem => {
                navItem.classList.remove('active');
            });
            document.getElementById('nav-' + pageId).classList.add('active');
        }

        const uploadArea = document.getElementById('upload-area');
        const fileInput = document.getElementById('file-input');
        const fileNameDisplay = document.getElementById('fileName');
        const uploadStatus = document.getElementById('upload-status');
        const progressDetails = document.getElementById('progress-details'); // New element for details

        // Trigger file input when upload area is clicked
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        // Handle file selection
        fileInput.addEventListener('change', (event) => {
            const files = event.target.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        // Drag and drop events
        uploadArea.addEventListener('dragover', (event) => {
            event.preventDefault(); // Prevent default behavior (opening file)
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (event) => {
            event.preventDefault(); // Prevent default behavior (opening file)
            uploadArea.classList.remove('dragover');

            const files = event.dataTransfer.files;
            if (files.length > 0) {
                // Check if the file is an Excel file
                const file = files[0];
                if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
                    fileInput.files = files; // Assign to file input for consistency
                    handleFile(file);
                } else {
                    fileNameDisplay.textContent = '错误：请上传 Excel 文件 (.xlsx, .xls)。';
                    uploadStatus.textContent = '';
                    uploadStatus.style.color = 'red';
                }
            }
        });

        function handleFile(file) {
            fileNameDisplay.textContent = `已选择文件: ${file.name}`;
            uploadStatus.textContent = '准备连接服务器...';
            uploadStatus.style.color = 'blue';
            progressDetails.innerHTML = ''; // Clear previous details

            uploadAndProcessFileWithSSE(file);
        }

        async function uploadAndProcessFileWithSSE(file) {
            uploadStatus.textContent = `正在上传 ${file.name} 并等待处理...`;
            uploadStatus.style.color = 'orange';

            const formData = new FormData();
            formData.append('excelFile', file);

            try {
                const response = await fetch('/qnm/gm_web/api/import_send_flower', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    // Handle initial HTTP errors before SSE stream starts
                    let errorMsg = `服务器错误: ${response.status}`;
                    try {
                        const errData = await response.json();
                        errorMsg = errData.error || errorMsg;
                    } catch (e) { /* Ignore if response is not JSON */ }
                    throw new Error(errorMsg);
                }

                if (!response.body) {
                    throw new Error('响应体不可读');
                }

                const reader = response.body.pipeThrough(new TextDecoderStream()).getReader();
                let buffer = '';

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) {
                        // Stream finished, but completion might be signaled by a specific SSE event
                        // Check if the last message was ProcessingComplete or if we need to infer it.
                        if (!uploadStatus.textContent.includes('所有行处理完毕') && !uploadStatus.textContent.includes('错误')) {
                            uploadStatus.textContent = '数据流关闭，但未收到完成信号。';
                            uploadStatus.style.color = 'orange';
                        }
                        break;
                    }

                    buffer += value;
                    let eventEndIndex;
                    // Process all complete events in the buffer
                    while ((eventEndIndex = buffer.indexOf('\n\n')) !== -1) {
                        const eventText = buffer.substring(0, eventEndIndex);
                        buffer = buffer.substring(eventEndIndex + 2); // Skip '\n\n'

                        let eventName = 'message'; // Default event type
                        let eventData = {};

                        const lines = eventText.split('\n');
                        for (const line of lines) {
                            if (line.startsWith('event:')) {
                                eventName = line.substring('event:'.length).trim();
                            } else if (line.startsWith('data:')) {
                                const dataString = line.substring('data:'.length).trim();
                                try {
                                    eventData = JSON.parse(dataString);
                                } catch (e) {
                                    console.error('Error parsing SSE data JSON:', e, dataString);
                                    eventData = { raw: dataString, parseError: true };
                                }
                            }
                        }
                        handleSseEvent(eventName, eventData, file.name);
                    }
                }

            } catch (error) {
                console.error('SSE/Upload error:', error);
                uploadStatus.textContent = `连接或上传出错: ${error.message}`;
                uploadStatus.style.color = 'red';
                progressDetails.innerHTML += `<div style="color: red;">错误: ${error.message}</div>`;
            }
        }

        function handleSseEvent(eventName, data, fileName) {
            console.log('SSE Event:', eventName, data);
            switch (eventName) {
                case 'StreamStart':
                    uploadStatus.textContent = data.message || `开始处理 ${fileName}`;
                    uploadStatus.style.color = 'blue';
                    progressDetails.innerHTML = `<p><strong>文件:</strong> ${data.fileName}, <strong>工作表:</strong> ${data.sheetName}, <strong>总行数:</strong> ${data.totalRows}</p>`;
                    break;
                case 'FlowerEvent':
                    let detailClass = data.success ? 'color: green;' : 'color: red;';
                    let statusMessage = data.message || (data.success ? '处理成功' : '处理失败');

                    let rowContentDisplay = '无效事件数据';
                    if (data.rowData) {
                        const eventDetails = data.rowData;
                        const formattedTimestamp = eventDetails.timestamp ? new Date(eventDetails.timestamp).toLocaleString() : 'N/A';
                        const paramsDisplay = typeof eventDetails.parameter === 'object' ? JSON.stringify(eventDetails.parameter) : eventDetails.parameter;

                        rowContentDisplay = `类型: ${eventDetails.type || 'N/A'}, ` +
                                          `服务器: ${eventDetails.server || 'N/A'}, ` +
                                          `角色ID: ${eventDetails.roleid || 'N/A'}, ` +
                                          `目标ID: ${eventDetails.targetid || 'N/A'}, ` +
                                          `时间戳: ${formattedTimestamp}, ` +
                                          `参数: ${paramsDisplay || 'N/A'}`;
                    }

                    progressDetails.innerHTML =
                        `<div style="${detailClass} padding: 3px;">` +
                           `行 ${data.rowIndex + 1} / ${data.totalRowsInSheet || 'N/A'}: ${statusMessage} <br>` +
                           `<small style="color: #777;">数据详情: ${rowContentDisplay}</small>` +
                        `</div>`;

                    uploadStatus.textContent = `正在处理行 ${data.rowIndex + 1}...`;
                    break;
                case 'ProcessingComplete':
                    uploadStatus.textContent = data.message; // Keep detailed message from backend in uploadStatus

                    if (data.failedRows && data.failedRows > 0) {
                        uploadStatus.style.color = 'red';
                        // For progressDetails, show a summary, as the specific failed row info was just displayed by FlowerEvent
                        progressDetails.innerHTML =
                            `<div style="color: red; font-weight: bold; margin-top: 10px;">` +
                                `处理已因错误中断。<br>` +
                                `最终统计: ${data.successfulRows} 成功, ${data.failedRows} 失败 (共处理 ${data.totalRowsProcessed} 行).` +
                            `</div>`;
                    } else { // Success
                        uploadStatus.style.color = 'green';
                        progressDetails.innerHTML =
                            `<div style="color: green; font-weight: bold; margin-top: 10px;">` +
                                `${data.message} <br>` + // data.message from backend is the success message (e.g., "File processed.")
                                `统计: ${data.successfulRows} 成功 (共处理 ${data.totalRowsProcessed} 行).`+
                            `</div>`;
                    }
                    break;
                case 'StreamError':
                    uploadStatus.textContent = data.error || '处理过程中发生严重流错误。';
                    uploadStatus.style.color = 'red';
                    progressDetails.innerHTML = `<div style="color: red; font-weight: bold; margin-top: 10px;">错误: ${data.error} ${data.details || ''}</div>`;
                    break;
                default:
                    console.warn('Received unknown SSE event:', eventName, data);
            }
        }

        // Initialize the first page
        document.addEventListener('DOMContentLoaded', () => {
            showPage('import-flower-data');
        });
    </script>
</body>
</html>
