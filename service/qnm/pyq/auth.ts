import * as _ from "lodash";
import { AUTH_TOKEN_SALT, testCfg } from "../../../common/config";
import { getRedis } from "../../../common/redis";
import * as QnmRoleInfo from "../../../models/QNMRoleInfos";
import {
  EventNames,
  LOGIN_RECORD_KEEP_SIZE,
  RoleInfoAccountCol,
  RoleInfoAccountIdCol,
} from "../../../pyq-server/constants";
import { errorCodes } from "../../../pyq-server/errorCodes";
import { clazzLogger } from "../../../pyq-server/logger";
import { ProfileModel } from "../../../pyq-server/models";
import util = require("../../../common/util");
import WeekRank = require("../cache/WeekRank");
import ProfileCache = require("../cache/Profile");
import PyqProfile = require("../../../models/PyqProfile");
import dateUtil = require("../../../common/dateUtil");
import type { AuthLoginParams, AuthLogoutParams, EventPayload } from "../../../pyq-server/types/type";
import { LoginSession } from "../../../common/auth";
import { EventBus } from "../../../pyq-server/eventBus2";
import { DailyLoginService } from "../../../pyq-server/services/dailyLoginService";
import { SPEECH_LIMIT_STRATEGY } from "../../../pyq-server/services/speechLimitPlugin";
const logger = clazzLogger("service/qnm/pyq/auth");

/**
 * Get new login record
 * @param {Array} curLoginRecord array of recordRow
 * @param {Object} recordRow
 * @param {Number} recordRow.level
 * @param {String} recordRow.date
 * @return {Array} newLoginRecord array of recordRow
 */
export function getNewLoginRecord(curLoginRecord, recordRow) {
  return _.chain(curLoginRecord).push(recordRow).uniqBy(_.property("date")).takeRight(LOGIN_RECORD_KEEP_SIZE).value();
}

/**
 * Update player pyq_profile LoginRecord field
 * @param {Object} profile
 * @param {Number} profile.RoleId
 * @param {String} profile.LoginRecord
 * @param {Object} params
 * @param {Number} params.level
 */
function updatePlayerLoginRecord(profile, params) {
  const curLoginRecord = util.getJsonInfo(profile.LoginRecord, []);
  const newLoginRecord = getNewLoginRecord(curLoginRecord, {
    date: dateUtil.format(new Date(), "yyyyMMdd"),
    level: params.level || 0,
  });
  return PyqProfile.updateByCondition({ RoleId: profile.RoleId }, { LoginRecord: JSON.stringify(newLoginRecord) });
}

function updateQnmRoleInfo(params: AuthLoginParams) {
  const now = Date.now();
  const urs = params.urs || "";
  const insertProps = {
    RoleId: params.roleid,
    UserName: urs,
    [RoleInfoAccountCol]: params.account,
    ServerId: params.server,
    CreateTime: now,
    UpdateTime: now,
    Level: params.level,
    Language: params.language,
    Country: params.country,
  };
  const updateProps = {
    UserName: urs,
    [RoleInfoAccountCol]: params.account,
    ServerId: params.server,
    UpdateTime: now,
    Level: params.level,
    Language: params.language,
    Country: params.country,
  };
  if (params.vip) {
    insertProps["VIP"] = params.vip;
    updateProps["VIP"] = params.vip;
  }
  if (params.accountId) {
    insertProps[RoleInfoAccountIdCol] = params.accountId;
    updateProps[RoleInfoAccountIdCol] = params.accountId;
  }
  return QnmRoleInfo.createOrUpdate(insertProps, updateProps);
}

export function logOut(params: AuthLogoutParams) {
  checkAuthActionToken(params);
  return LoginSession.del(params.roleid);
}

export function checkRankToken(req, params): boolean {
  const type = params.type;
  const serverid = params.serverid;
  const count = params.count;
  const threshold = params.threshold;
  const signature = [type, serverid, count, threshold, AUTH_TOKEN_SALT].join("");
  const token = util.hexMd5(signature);
  if (params.token === token) {
    return true;
  } else {
    if (testCfg.skip_auth) {
      logger.warn({ req, params, expect: token, actual: params.token, signature }, "GetRankTokenSkip");
      return true;
    } else {
      logger.warn({ req, params, expect: token, actual: params.token, signature }, "GetRankTokenInvalid");
      return false;
    }
  }
}

export async function getLoginParams(roleId: number, field: string): Promise<string> {
  const key = "sess:role:" + roleId;
  const result = await getRedis().hgetAsync(key, field);
  return result;
}

export async function isOverSeaUser(roleId: number) {
  const isOverSea = await getLoginParams(roleId, "isOverSea");
  return isOverSea === "1";
}

export async function login(params: AuthLoginParams) {
  checkAuthActionToken(params);
  const urs = params.urs;
  const roleId = params.roleid;
  const serverId = params.server;
  const account = params.account;
  const isOverSea = params.isOverSea || "0";
  const language = params.language || "cn";
  const country = params.country || 0;
  const speechLimitStrategy = params.speechLimitStrategy || SPEECH_LIMIT_STRATEGY.BLOCK_ALL.toString();

  // 保存每日登录时间, 无需阻塞登录流程
  saveDailyLoginTime(roleId, Date.now());

  const skeyInfo = await LoginSession.set(params.roleid, {
    time: params.time,
    account: account,
    isOverSea: isOverSea,
    server: serverId,
    urs: urs,
    language: language,
    country: country,
    speechLimitStrategy: speechLimitStrategy,
  });

  // 查询 renqi  flowerrenqi 及周 renqi  flowerrenqi
  const [profileRows, weekRanks] = await Promise.all([
    ProfileCache.query(roleId, {
      cols: ["RoleId", "RenQi", "Flower", "FlowerRenQi", "SendFlowerRenQi", "Gift", "Photo", "LoginRecord"],
    }),
    WeekRank.getAllScore(serverId, roleId),
  ]);

  const isNewRoleId = _.isEmpty(profileRows);
  const item = profileRows[0] || {};
  item.HasPhoto = item.Photo ? 1 : 0;
  util.extend(item, weekRanks);

  const loginInfo = util.extend(skeyInfo, util.toLowerCaseKey(item));
  if (isNewRoleId) {
    const roleIdInt = parseInt("" + roleId, 10);
    try {
      await ProfileModel.insert({ RoleId: roleIdInt, UpdateTime: Date.now() });
    } catch (err) {
      logger.warn({ err, roleIdInt, profileCache: loginInfo }, "LoginStartInsertProfileDuplicate");
    }
  } else {
    await updatePlayerLoginRecord(item, params);
  }
  // 根据服务端数据添加添加urs及serverId信息
  await updateQnmRoleInfo(params);

  const event: AuthEvent.LoginOK = { ts: Date.now(), skey: skeyInfo.skey, loginParams: params, roleId };
  EventBus.emit(EventNames.AUTH_LOGIN$LOGIN_OK, event);

  return loginInfo;
}

async function saveDailyLoginTime(roleId: number, loginTime: number) {
  try {
    await DailyLoginService.saveLoginTime(roleId, loginTime);
  } catch (err) {
    logger.error({ err, roleId, loginTime }, "DailyLoginServiceSaveLoginTimeFailed");
  }
}

function checkAuthActionToken(params: AuthLoginParams) {
  const isAuthValid = isAuthActionTokenValid(params);
  if (!isAuthValid) {
    throw errorCodes.CheckTokenFailed;
  }
}

function isAuthActionTokenValid(params: AuthLoginParams): boolean {
  const time = params.time,
    urs = params.urs,
    account = params.account,
    roleId = params.roleid,
    token = util.hexMd5(time + account + urs + roleId + AUTH_TOKEN_SALT);

  if (params.token != token) {
    logger.warn({ actual: params.token, expect: token, params }, "AuthLoginWithInvalidToken");
    if (testCfg.skip_token_check) {
      return true;
    } else {
      return false;
    }
  }
  return true;
}

export namespace AuthEvent {
  export interface LoginOK extends EventPayload {
    loginParams: AuthLoginParams;
    skey: string;
  }
}

export interface IAuthLoginEventHandler {
  onLoginOK?(payload: AuthEvent.LoginOK);
}
