"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.login = exports.isOverSeaUser = exports.getLoginParams = exports.checkRankToken = exports.logOut = exports.getNewLoginRecord = void 0;
const _ = require("lodash");
const config_1 = require("../../../common/config");
const redis_1 = require("../../../common/redis");
const QnmRoleInfo = require("../../../models/QNMRoleInfos");
const constants_1 = require("../../../pyq-server/constants");
const errorCodes_1 = require("../../../pyq-server/errorCodes");
const logger_1 = require("../../../pyq-server/logger");
const models_1 = require("../../../pyq-server/models");
const util = require("../../../common/util");
const WeekRank = require("../cache/WeekRank");
const ProfileCache = require("../cache/Profile");
const PyqProfile = require("../../../models/PyqProfile");
const dateUtil = require("../../../common/dateUtil");
const auth_1 = require("../../../common/auth");
const eventBus2_1 = require("../../../pyq-server/eventBus2");
const dailyLoginService_1 = require("../../../pyq-server/services/dailyLoginService");
const speechLimitPlugin_1 = require("../../../pyq-server/services/speechLimitPlugin");
const logger = (0, logger_1.clazzLogger)("service/qnm/pyq/auth");
/**
 * Get new login record
 * @param {Array} curLoginRecord array of recordRow
 * @param {Object} recordRow
 * @param {Number} recordRow.level
 * @param {String} recordRow.date
 * @return {Array} newLoginRecord array of recordRow
 */
function getNewLoginRecord(curLoginRecord, recordRow) {
    return _.chain(curLoginRecord).push(recordRow).uniqBy(_.property("date")).takeRight(constants_1.LOGIN_RECORD_KEEP_SIZE).value();
}
exports.getNewLoginRecord = getNewLoginRecord;
/**
 * Update player pyq_profile LoginRecord field
 * @param {Object} profile
 * @param {Number} profile.RoleId
 * @param {String} profile.LoginRecord
 * @param {Object} params
 * @param {Number} params.level
 */
function updatePlayerLoginRecord(profile, params) {
    const curLoginRecord = util.getJsonInfo(profile.LoginRecord, []);
    const newLoginRecord = getNewLoginRecord(curLoginRecord, {
        date: dateUtil.format(new Date(), "yyyyMMdd"),
        level: params.level || 0,
    });
    return PyqProfile.updateByCondition({ RoleId: profile.RoleId }, { LoginRecord: JSON.stringify(newLoginRecord) });
}
function updateQnmRoleInfo(params) {
    const now = Date.now();
    const urs = params.urs || "";
    const insertProps = {
        RoleId: params.roleid,
        UserName: urs,
        [constants_1.RoleInfoAccountCol]: params.account,
        ServerId: params.server,
        CreateTime: now,
        UpdateTime: now,
        Level: params.level,
        Language: params.language,
        Country: params.country,
    };
    const updateProps = {
        UserName: urs,
        [constants_1.RoleInfoAccountCol]: params.account,
        ServerId: params.server,
        UpdateTime: now,
        Level: params.level,
        Language: params.language,
        Country: params.country,
    };
    if (params.vip) {
        insertProps["VIP"] = params.vip;
        updateProps["VIP"] = params.vip;
    }
    if (params.accountId) {
        insertProps[constants_1.RoleInfoAccountIdCol] = params.accountId;
        updateProps[constants_1.RoleInfoAccountIdCol] = params.accountId;
    }
    return QnmRoleInfo.createOrUpdate(insertProps, updateProps);
}
function logOut(params) {
    checkAuthActionToken(params);
    return auth_1.LoginSession.del(params.roleid);
}
exports.logOut = logOut;
function checkRankToken(req, params) {
    const type = params.type;
    const serverid = params.serverid;
    const count = params.count;
    const threshold = params.threshold;
    const signature = [type, serverid, count, threshold, config_1.AUTH_TOKEN_SALT].join("");
    const token = util.hexMd5(signature);
    if (params.token === token) {
        return true;
    }
    else {
        if (config_1.testCfg.skip_auth) {
            logger.warn({ req, params, expect: token, actual: params.token, signature }, "GetRankTokenSkip");
            return true;
        }
        else {
            logger.warn({ req, params, expect: token, actual: params.token, signature }, "GetRankTokenInvalid");
            return false;
        }
    }
}
exports.checkRankToken = checkRankToken;
function getLoginParams(roleId, field) {
    return __awaiter(this, void 0, void 0, function* () {
        const key = "sess:role:" + roleId;
        const result = yield (0, redis_1.getRedis)().hgetAsync(key, field);
        return result;
    });
}
exports.getLoginParams = getLoginParams;
function isOverSeaUser(roleId) {
    return __awaiter(this, void 0, void 0, function* () {
        const isOverSea = yield getLoginParams(roleId, "isOverSea");
        return isOverSea === "1";
    });
}
exports.isOverSeaUser = isOverSeaUser;
function login(params) {
    return __awaiter(this, void 0, void 0, function* () {
        checkAuthActionToken(params);
        const urs = params.urs;
        const roleId = params.roleid;
        const serverId = params.server;
        const account = params.account;
        const isOverSea = params.isOverSea || "0";
        const language = params.language || "cn";
        const country = params.country || 0;
        const speechLimitStrategy = params.speechLimitStrategy || speechLimitPlugin_1.SPEECH_LIMIT_STRATEGY.BLOCK_ALL.toString();
        // 保存每日登录时间, 无需阻塞登录流程
        saveDailyLoginTime(roleId, Date.now());
        const skeyInfo = yield auth_1.LoginSession.set(params.roleid, {
            time: params.time,
            account: account,
            isOverSea: isOverSea,
            server: serverId,
            urs: urs,
            language: language,
            country: country,
            speechLimitStrategy: speechLimitStrategy,
        });
        // 查询 renqi  flowerrenqi 及周 renqi  flowerrenqi
        const [profileRows, weekRanks] = yield Promise.all([
            ProfileCache.query(roleId, {
                cols: ["RoleId", "RenQi", "Flower", "FlowerRenQi", "SendFlowerRenQi", "Gift", "Photo", "LoginRecord"],
            }),
            WeekRank.getAllScore(serverId, roleId),
        ]);
        const isNewRoleId = _.isEmpty(profileRows);
        const item = profileRows[0] || {};
        item.HasPhoto = item.Photo ? 1 : 0;
        util.extend(item, weekRanks);
        const loginInfo = util.extend(skeyInfo, util.toLowerCaseKey(item));
        if (isNewRoleId) {
            const roleIdInt = parseInt("" + roleId, 10);
            try {
                yield models_1.ProfileModel.insert({ RoleId: roleIdInt, UpdateTime: Date.now() });
            }
            catch (err) {
                logger.warn({ err, roleIdInt, profileCache: loginInfo }, "LoginStartInsertProfileDuplicate");
            }
        }
        else {
            yield updatePlayerLoginRecord(item, params);
        }
        // 根据服务端数据添加添加urs及serverId信息
        yield updateQnmRoleInfo(params);
        const event = { ts: Date.now(), skey: skeyInfo.skey, loginParams: params, roleId };
        eventBus2_1.EventBus.emit(constants_1.EventNames.AUTH_LOGIN$LOGIN_OK, event);
        return loginInfo;
    });
}
exports.login = login;
function saveDailyLoginTime(roleId, loginTime) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            yield dailyLoginService_1.DailyLoginService.saveLoginTime(roleId, loginTime);
        }
        catch (err) {
            logger.error({ err, roleId, loginTime }, "DailyLoginServiceSaveLoginTimeFailed");
        }
    });
}
function checkAuthActionToken(params) {
    const isAuthValid = isAuthActionTokenValid(params);
    if (!isAuthValid) {
        throw errorCodes_1.errorCodes.CheckTokenFailed;
    }
}
function isAuthActionTokenValid(params) {
    const time = params.time, urs = params.urs, account = params.account, roleId = params.roleid, token = util.hexMd5(time + account + urs + roleId + config_1.AUTH_TOKEN_SALT);
    if (params.token != token) {
        logger.warn({ actual: params.token, expect: token, params }, "AuthLoginWithInvalidToken");
        if (config_1.testCfg.skip_token_check) {
            return true;
        }
        else {
            return false;
        }
    }
    return true;
}
//# sourceMappingURL=auth.js.map