var restify = require('restify'),
  Q = require('q'),
  util = require('../common/util'),
  config = require('../common/config'),
  logger = require('../common/logger'),
  erros = require('../common/errors');
auth = require('../common/auth');

let { speechLimitPlugin } = require('./services/speechLimitPlugin');
let { isIn64SpecialPeriod } = require('../common/helper');

var CookieParser = require('restify-cookies');
let { jsonp } = require('../common/betterJsonp')


var app = restify.createServer({
  name: 'md-server',
  version: '1.0.0',

  formatters: {
    'application/javascript; q=0.4': require('./formatters/jsonp'), //使用自己的formatter
  }
});

var Initialzers = require('./initializers');
Initialzers.initAll();


app.use(CookieParser.parse);
app.use(restify.acceptParser(app.acceptable));
app.use(restify.queryParser());
app.use(restify.bodyParser({ uploadDir: __dirname + '/../public', keepExtensions: true,
  limit: config.MAX_UPLOAD_SIZE, maxBodySize: 10 * config.MAX_UPLOAD_SIZE }));
app.use(jsonp());
app.use(restify.gzipResponse());


if(process.env.NODE_ENV === "development") {
  app.get(/\/public\/apidoc\/?.*/, restify.serveStatic({
    directory: __dirname
  }));
}



app.use(function (req, res, next) {     // 请求日志
  logger.req(app.name, req);
  next();
});

app.use(function (req, res, next) {
  res.charSet('utf-8');

  var SKIP_AUTH_LIST = {
    '/crossdomain.xml': 1,
    '/md/transact/login': 1,
    '/md/login': 1,
    '/md/app/login': 1,
    '/md/users/login': 1,
    '/md/users/login/ngp': 1,
    '/md/nos/callback': 1,
    '/md/:game/delbigevent': 1,
    '/md/home': 0,
    '/md/qnm/gethotmoments': 0,
    '/md/qnm/getmoment': 0,
    '/md/qnm/getownmoments': 0,
    "/md/photo_channels/:channel_id/photos": 1,
    "/md/photo_albums": 1,

    "/md/public/photo_channels/:channel_id/albums": 1,
    "/md/public/photo_channels/:channel_id/albums/editor_pick": 1,
    "/md/public/photo_albums/:album_id/photos": 1,
    "/md/qnm/tuku/hot_moment_photos": 1,
    "/md/public/qnm/moments/hot" : 1,

    '/md/:game/rank': 1,
    '/md/:game/role/info/:roleid': 1,
    '/md/:game/role/skill/:roleid': 1,
    '/md/:game/role/animal/:roleid': 1,
    '/md/:game/role/home/<USER>': 1,
    '/md/:game/role/child/:roleid': 1,
    '/md/:game/role/event/:roleid': 1,

    "/md/logs/collect": 1,
    "/md/:game/update_signature": 1,
    "/md/migrate": 1,
    "/md/migrate/push": 1,

    "/md/tongren/audit": 1,
    "/md/tongren/work/:workid/score_ratio": 1,
    "/md/wx/login": 1,
    "/md/wx/bindUrs": 1,

    "/md/user/get_basic_info": 1,
    "/md/users/role_infos/bind": 1,

    '/md/qnm/tuku/set_audit_results' : 1,
    '/md/qnm/tuku/pyq_moment_photo/list' : 1,
    '/md/qnm/tuku/pyq_moment_photo/pick' : 1,

    '/md/pocket/auth/login': 1,
    '/md/pocket/auth/check_status': 1,

    // 倩女端游挑选图片相关api
    '/md/photos/qn/listPicks': 1,
    '/md/photos/qn/pick': 1,

    // json rpc 暴露的接口地址
    '/md/jsonrpc': 1
  };

  //可选授权列表
  var OPTIONAL_AUTH_LIST = {
    "/md/reality_shows/events": 1,
    "/md/reality_shows": 1,
    "/md/show_subjects": 1,
    "/md/show_subjects/slide_show": 1,
    "/md/show_subjects/:subject_id": 1,
    "/md/show_subjects/:subject_id/show_topics": 1,
    "/md/show_topics/:topic_id": 1,
    "/md/users/:user_id/show_topics": 1,
    '/md/:game/getrank/:serverId/:rankId': 1,
    '/md/:game/getrank/:rankId': 1,

    '/md/tongren/home': 1,
    '/md/tongren/list': 1,
    '/md/tongren/work/:id': 1,
    '/md/tongren/comment/get': 1,
    '/md/tongren/search': 1
  };

  var SKIP_USER_LIST = {
    '/md/transact/getcode': 1,
    '/md/transact/getrecord': 1,
    '/md/logout': 1,
    '/md/getcode': 1,
    '/md/checkname': 1,
    '/md/getuserinfo': 1,
    '/md/setuserinfo': 1,
    '/md/nos/gettoken': 1,
    '/md/base64upload': 1,
    '/md/fileupload': 1,

    '/md/:game/listchar': 1,
    '/md/:game/rank': 1,
    '/md/:game/getrank/:serverId/:rankId': 1,
    '/md/:game/getrank/:rankId': 1,
    '/md/:game/role/info/:roleid': 1,
    '/md/:game/role/skill/:roleid': 1,
    '/md/:game/role/animal/:roleid': 1,
    '/md/:game/role/home/<USER>': 1,
    '/md/:game/role/child/:roleid': 1,
    '/md/users/get_recent_urs_login_info': 1,
    '/md/users/queryUserSecInfoStatus': 1,
    '/md/users/verifyintegritymibao2': 1,
    '/md/auth/validate_md_login': 1
  };

  var path = req.route.path + '';
  var isOptionalAuth = OPTIONAL_AUTH_LIST[path];
  var skipAuth = SKIP_AUTH_LIST[path];
  if (skipAuth || config.testCfg.skip_auth) {
    next();
    return;
  }

  var params = req.params;
  delete  params.userid;     // 清除用户输入参数
  auth.check(req)
  .fail(function (failMsg) {    // Note: 使用Q串联写法，.fail必须放在.then前，否则在.then中发生异常后也将进入这里的错误处理；并且.fail中必须抛出异常，否则.then仍将执行
    if (skipAuth !== 0 && !isOptionalAuth) {
      throw({ code: 'NO_LOGIN', msg: '请先登录：' + failMsg });
    }
  })
  .then(function (info) {
    if (info) {
      var userId = info.userid;
      if (!userId && !SKIP_USER_LIST[path] && !isOptionalAuth) {
        throw({ code: 'NO_USER', msg: '请先注册帐号' });
      }

      params.urs = info.urs;
      userId && (params.userid = userId);
    }
    next();
  })
  .catch(function (ex) {
    res.send(util.response(ex));
  });
});


var MiddleWareManager = require('./middlewares');
MiddleWareManager.mountAll(app);

app.on('uncaughtException', function (req, res, route, err) {
  logger.server(app.name, err);
  try { // 部分请求未等待异步操作完成，可能多次产生多个 uncaughtException
    res.send(500, util.response(err));
  } catch (ex) {
    logger.error(ex);
  }
});

if (isIn64SpecialPeriod(new Date())) {
  app.use(speechLimitPlugin)
}

app.get('/md/nos/gettoken', function(req, res, next) {
  var nos = require('../common/nos');
  var token = nos.getToken('md', req.params);
  res.send(util.response(token));
});
app.post('/md/nos/callback', function(req, res, next) {
  var nos = require('../common/nos');
  nos.callback('md', req, function () {
    res.send(util.response.apply(null, arguments));
  });
});

app.get('/md/login', function(req, res, next) {
  var user = require('../service/app/user');
  user.login(req, res, function() {
    res.send(util.response.apply(null, arguments));
  });
});
app.get('/md/app/login', function(req, res, next) {
  var _ = require('lodash');
  var isJumpToMaintainPage = false;
  var user = require('../service/app/user'),
    target = req.params.target,
    url = target || config.MD_HOME_URL;

  var uaLastTag = _.last(_.split(req.headers['user-agent'], ' '));
  if((uaLastTag === "qian_nv_help_iOS" || uaLastTag === "qian_nv_help_android")) {
    delete require.cache[require('path').resolve('../common/config.all.js')]; // 清除require缓存，保证每次都重新读取appLogin配置.
    delete require.cache[require('path').resolve('../common/config.js')];
    config = require('../common/config');
    if(!config.AppLogin[uaLastTag]) {
      isJumpToMaintainPage = true;
    }
  }
  if(isJumpToMaintainPage) {
    res.writeHead(302, {Location: 'https://new.hi.163.com/special/maintain.html'});
    res.end();
  }
  else {
    user.login(req, res, function(info) {
      if (info.code && !target) {  // 指定target时，登录失败也跳转到target
        res.writeHead(302, {Location: 'https://xqn.163.com/app/signin/tip.html'});
        res.end();
      } else {
        res.writeHead(302, {Location: url});
        res.end();
      }
    });
  }
});

app.get('/md/logout', function(req, res, next) {
  var user = require('../service/app/user');
  user.logout(req, res, function() {
    res.send(util.response.apply(null, arguments));
  });
});

app.get('/md/checkcode', function(req, res, next) {
  var user = require('../service/app/user');
  user.checkCode(req, function() {
    res.send(util.response.apply(null, arguments));
  });
});

// http://localhost:3003/md/getuserinfo
app.get('/md/getuserinfo', function(req, res, next) {
  var setting = require('../service/app/setting');
  setting.getInfo(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

app.get('/md/user/get_basic_info', function(req, res, next) {
  let setting = require('../service/app/setting');
  setting.getUserBasicInfo(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

app.get('/md/setuserinfo', function(req, res, next) {
  var setting = require('../service/app/setting');
  setting.setInfo(req, res, function() {
    res.send(util.response.apply(null, arguments));
  });
});
app.get('/md/checkname', function(req, res, next) {
  var setting = require('../service/app/setting');
  setting.checkName(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

app.get('/md/home', function(req, res, next) {
  var home = require('../service/app/home');
  home.getHome(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});
app.get('/md/home/<USER>', function(req, res, next) {
  var home = require('../service/app/home');
  home.getMainRole(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

let { checkPermission } = require('./services/listCharPermission')
app.get('/md/:game/listchar', checkPermission, function (req, res, next) {
  var listChar = require('../service/' + req.params.game + '/role/listchar');
  listChar.get(req.params, function () {
    res.send(util.response.apply(null, arguments));
  });
});

app.get('/md/:game/role/info/:roleid', function(req, res, next) {
  var roleInfo = require('../service/' + req.params.game + '/role/info');
  roleInfo.getInfo(req.params, function () {
    res.send(util.response.apply(null, arguments));
  });
});
app.get('/md/:game/role/event/:roleid', function(req, res, next) {
  var roleInfo = require('../service/' + req.params.game + '/role/info');
  roleInfo.getEvent(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});
app.get('/md/:game/role/skill/:roleid', function(req, res, next) {
  var roleInfo = require('../service/' + req.params.game + '/role/info');
  roleInfo.getSkill(req.params, function () {
    res.send(util.response.apply(null, arguments));
  });
});
app.get('/md/:game/role/animal/:roleid', function(req, res, next) {
  var roleInfo = require('../service/' + req.params.game + '/role/info');
  roleInfo.getAnimal(req.params, function () {
    res.send(util.response.apply(null, arguments));
  });
});
app.get('/md/:game/role/home/<USER>', function(req, res, next) {
  var roleInfo = require('../service/' + req.params.game + '/role/info');
  roleInfo.getHome(req.params, function () {
    res.send(util.response.apply(null, arguments));
  });
});
app.get('/md/:game/role/child/:roleid', function(req, res, next) {
  var roleInfo = require('../service/' + req.params.game + '/role/info');
  roleInfo.getChild(req.params, function () {
    res.send(util.response.apply(null, arguments));
  });
});
app.get('/md/:game/rank', function(req, res, next) {
  var rankIndex = require('../service/' + req.params.game + '/rank/index');
  rankIndex.get(function () {
    res.send(util.response.apply(null, arguments));
    return next();
  });
});
app.get('/md/:game/getrank/:serverId/:rankId', function(req, res, next) {
  var rankList = require('../service/qn/rank/list');
  rankList.get(req.params, function () {
    res.send(util.response.apply(null, arguments));
    return next();
  });
});
app.get('/md/:game/getrank/:rankId', function(req, res, next) {
  var rankList = require('../service/qn/rank/list');
  rankList.get(req.params, function () {
    res.send(util.response.apply(null, arguments));
    return next();
  });
});

const setting = require('../service/app/setting')
app.get('/md/:game/getbindrole', setting.getBindRoleHandler)

app.get('/md/:game/bindrole', function(req, res, next) {
  var setting = require('../service/app/setting');
  setting.bindRole(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

app.get('/md/:game/unbindrole', function(req, res, next) {
  var setting = require('../service/app/setting');
  setting.unbindRole(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

app.get('/md/:game/setmainrole', function(req, res, next) {
  var setting = require('../service/app/setting');
  setting.setMainRole(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

app.get('/md/:game/delbigevent', function(req, res, next) {
  var roleEvent = require('../service/' + req.params.game + '/role/event');
  roleEvent.delBig(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

// http://localhost:3003/md/qnm/recommendFriList
app.get('/md/qnm/recommendFriList', function(req, res, next) {
  var listfriend = require('../service/qnm/md/recommendFriList');
  listfriend.get(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

// http://localhost:3003/md/qnm/addfri
app.get('/md/qnm/addfri', function(req, res, next) {
  var contactlist = require('../service/qnm/md/contactlist');
  contactlist.add(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

// http://localhost:3003/md/qnm/delfri
app.get('/md/qnm/delfri', function(req, res, next) {
  var contactlist = require('../service/qnm/md/contactlist');
  contactlist.del(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

// http://localhost:3003/md/qnm/getcontacts/:account
app.get('/md/qnm/getcontacts', function(req,res,next){
  var contactlist = require('../service/qnm/md/contactlist');
  contactlist.get(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

// http://localhost:3003/md/qnm/addmoment
app.get('/md/qnm/addmoment', function(req,res,next){
  var moment = require('../service/qnm/md/moment');
  moment.add(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

// http://localhost:3003/md/qnm/delmoment/:mid
app.get('/md/qnm/delmoment', function(req,res,next){
  var moment = require('../service/qnm/md/moment');
  moment.del(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

// http://localhost:3003/md/qnm/getmoments
app.get('/md/qnm/getmoments', function(req,res,next){
  var moment = require('../service/qnm/md/moment');
  moment.getmoments(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

// http://localhost:3003/md/qnm/getownmoments
app.get('/md/qnm/getownmoments', function(req,res,next){
  var moment = require('../service/qnm/md/moment');
  moment.getownmoments(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

// http://localhost:3003/md/qnm/setmomenttop
// app.get('/md/qnm/setmomenttop', function(req,res,next){
//     var moment = require('../service/qnm/md/moment');
//     moment.settop(req.params, function() {
//         res.send(util.response.apply(null, arguments));
//     });
// });

// http://localhost:3003/md/qnm/gethotmoments
app.get('/md/qnm/gethotmoments', function(req,res,next){
  // var moment = require('../service/qnm/md/moment');
  // moment.gethotmoments(req.params, function() {
  //   res.send(util.response.apply(null, arguments));
  // });

  if(req.params.page) {
    req.params.page = Number.parseInt(req.params.page, 10) + 1; //原来的page从0开始数
  }
  require('./controllers/MomentsController').listHot(req, res, next);
});

// http://localhost:3003/md/qnm/getmoment
app.get('/md/qnm/getmoment', function(req,res,next){
  var moment = require('../service/qnm/md/moment');
  moment.getmoment(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

// http://localhost:3003/md/qnm/getcomosfmom
app.get('/md/qnm/getcomosfmom', function(req,res,next){
  var moment = require('../service/qnm/md/moment');
  moment.getComsOfMoment(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

// http://localhost:3003/md/qnm/setzan
app.get('/md/qnm/setzan', function(req,res,next){
  var moment = require('../service/qnm/md/moment');
  moment.zan(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

// http://localhost:3003/md/qnm/addcomm
app.get('/md/qnm/addcomm', function(req,res,next){
  var moment = require('../service/qnm/md/moment');
  moment.addcomm(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

// http://localhost:3003/md/qnm/delcomm
app.get('/md/qnm/delcomm', function(req,res,next){
  var moment = require('../service/qnm/md/moment');
  moment.delcomm(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

// http://localhost:3003/md/qnm/addmsg
app.get('/md/qnm/addmsg', function(req,res,next){
  var message = require('../service/qnm/md/message');
  message.add(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

// http://localhost:3003/md/qnm/delmsg
app.get('/md/qnm/delmsg', function(req,res,next){
  var message = require('../service/qnm/md/message');
  message.del(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

// http://localhost:3003/md/qnm/ansmsg
app.get('/md/qnm/ansmsg', function(req,res,next){
  var message = require('../service/qnm/md/message');
  message.ans(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

// http://localhost:3003/md/qnm/delans
app.get('/md/qnm/delans', function(req,res,next){
  var message = require('../service/qnm/md/message');
  message.delans(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

// http://localhost:3003/md/qnm/getmsgs
app.get('/md/qnm/getmsgs', function(req,res,next){
  var message = require('../service/qnm/md/message');
  message.get(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

// http://localhost:3003/md/qnm/getmsgsnum
app.get('/md/qnm/getmsgsnum', function(req,res,next){
  var message = require('../service/qnm/md/message');
  message.getmsgsnum(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

// http://localhost:3003/md/qnm/getmsg
app.get('/md/qnm/getmsg', function(req,res,next){
  var message = require('../service/qnm/md/message');
  message.getone(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

// http://localhost:3003/md/qnm/getansofmsg
app.get('/md/qnm/getansofmsg', function(req,res,next){
  var message = require('../service/qnm/md/message');
  message.getAnsOfMsg(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

// http://localhost:3003/md/qnm/getinforms
app.get('/md/qnm/getinforms', function(req,res,next){
  var inform = require('../service/qnm/md/inform');
  inform.get(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

// http://localhost:3003/md/qnm/getnewnum
app.get('/md/qnm/getnewnum', function(req,res,next){
  var inform = require('../service/qnm/md/inform');
  inform.getnewnum(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

// http://localhost:3003/md/qnm/getfriinforms
app.get('/md/qnm/getfriinforms', function(req,res,next){
  var inform = require('../service/qnm/md/inform');
  inform.getfriinforms(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

// http://localhost:3003/md/qnm/getmsginforms
app.get('/md/qnm/getmsginforms', function(req,res,next){
  var inform = require('../service/qnm/md/inform');
  inform.getmsginforms(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

// http://localhost:3003/md/qnm/fuzzysearch
app.get('/md/qnm/fuzzysearch', function(req,res,next){
  var search = require('../service/qnm/md/search');
  search.fuzzysearch(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

// http://localhost:3003/md/qnm/kwsearch
app.get('/md/qnm/kwsearch', function(req,res,next){
  var search = require('../service/qnm/md/search');
  search.kwsearch(req.params, function() {
    res.send(util.response.apply(null, arguments));
  });
});

// http://localhost:3003/md/transact/login
app.get('/md/transact/login', function(req, res, next) {
  var User = require('../service/app/user');
  User.login(req, res, function() {
    res.send(util.response.apply(null, arguments));
  });
});

// http://localhost:3003/qnm/transact/getcode
app.get('/md/transact/getcode', function(req,res,next) {
  var User = require('../service/app/user');
  User.getCode(req, function(code, data) {
    if(req.params.encode_base64 == "true") {
      res.send({code:0, data: data.dataURL});
    } else {
      res.header('Content-Type', 'image/png');
      res.end(data.buffer, 'binary');
    }
  });
});

// http://localhost:3003/qnm/transact/getrecord
app.get('/md/transact/getrecord', function(req,res,next) {
  var transact = require('../service/qnm/role/transact');
  transact.gettransact(req, function() {
    res.send(util.response.apply(null, arguments));
  });
});

const Report = require('../service/app/report')
app.get('/md/report', Report.addFromPcClient)

app.post('/md/base64upload', function(req, res, next) {
  var params = req.params;
  var Uploader = require('../service/app/uploader');
  Uploader.uploadBase64(req, function(url, err) {
    url = params.url + (err ? ('?errMsg=' + err.message) : ('?url=' + url));
    res.writeHead(302, { Location: url });
    res.end();
  });
});

app.get('/crossdomain.xml', function (req, res, next) {
  var xml = '<?xml version="1.0" ?>\n'+
    '<cross-domain-policy>\n'+
    '<site-control permitted-cross-domain-policies="master-only"/>\n'+
    '<allow-access-from domain="*.163.com" to-ports="*" secure="false"/>\n'+
    '<allow-http-request-headers-from domain="*.163.com" headers="*" secure="false"/>\n'+
    '</cross-domain-policy>';
  res.header('Content-Type', 'application/xml');
  res.end(xml);
});
app.post('/md/fileupload', function(req, res, next) {
  var params = req.params;
  var Uploader = require('../service/app/uploader');
  Uploader.uploadFile(req, function(url, err) {
    var callback = params.callback;
    callback || res.header('Content-Type', 'application/javascript');
    if (err) {
      res.send(util.response(err));
    } else {
      var result = { url: url };
      callback ? res.send(callback + '(' + JSON.stringify(result) + ')') : res.send(result);
    }
  });
});

app.use(function(req, res, next) {
  if('userid' in  req.params) {
    req.params.userid = parseInt(req.params.userid);
  }
  next();
});

var GamesController = require('./controllers/GamesController');
app.get("/md/games/:game_name", GamesController.get);

var MomentsController = require('./controllers/MomentsController');
app.get("/md/moments/hot", MomentsController.listHot.bind(MomentsController));

var LogsController = require('./controllers/LogsController');
app.get("/md/logs/collect", LogsController.collect);


let routers = require('./routers/index');
routers.mount(app);

var portArg = process.argv.splice(2)[0] || '';
var port = portArg.indexOf('-p') >=0 ? parseInt(portArg.replace('-p', ''), 10) : 3003;

const EventBus = require('./eventBus')
EventBus.registerAllListeners()

app.listen(port, function () {
  logger.server(app.name, 'Start listening at %s', app.url);
});

module.exports = app
