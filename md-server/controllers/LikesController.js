var ParamsValidator = require('../../common/paramsValidator');
var Likes = require('../../models/Likes');
var Photos = require('../../models/Photos');
var Notifications = require('../../models/Notifications');
var errorHandler = require('../errorHandler');
var _ = require('lodash');
var util = require('../../common/util');

var LikesController = {};
module.exports = LikesController;

LikesController.checkPhotoValid = function(photoId) {
  return Photos.findByIdForce(photoId);
};


/**
 * @api {get} /md/likes/photos/:photo_id/new Like a photo
 * @apiName  LikePhoto
 * @apiGroup Likes
 * @apiParam {String} photo_id photo id
 */
LikesController.likePhoto = function(req, res, next) {
  var userId = req.params.userid;
  var photoId = req.params.photo_id;
  var curPhoto;
  return ParamsValidator
    .from(req.params)
    .param('photo_id')
    .validate()
    .then(function() {
      return LikesController.checkPhotoValid(photoId);
    })
    .then(function(photo) {
      curPhoto = photo;
      Notifications.addLikePhotoEvent(userId, photo);
      return Likes.likePhoto(userId, photoId);
    }).then(function(likesCount) {
      curPhoto.LikesCount = likesCount
      res.succSend({photo:curPhoto});
    }).catch(function(err) {
      errorHandler(err, req, res, next);
    });
};

/**
 * @api {get} /md/likes/photos/:photo_id/delete Cacel like a photo
 * @apiName  CancelLikePhoto
 * @apiGroup Likes
 * @apiParam {String} photo_id photo id
 */
LikesController.cancelLikePhoto = function(req, res, next) {
  var userId = req.params.userid;
  var photoId = req.params.photo_id;
  var curPhoto;
  return ParamsValidator
    .from(req.params)
    .param('photo_id')
    .validate()
    .then(function() {
      return LikesController.checkPhotoValid(photoId);
    })
    .then(function(photo) {
      curPhoto = photo;
      Notifications.softDeleteLikePhotoEvent(userId, photo);
      return Likes.cancelLikePhoto(userId, photoId);
    }).then(function(likesCount) {
      curPhoto.LikesCount = likesCount;
      res.succSend({photo:curPhoto});
    }).catch(function(err) {
      errorHandler(err, req, res, next);
    });
};


/**
 * @api {get} /md/like 点赞某个资源
 * @apiName  LikeResource
 * @apiParam {String="reality_shows","show_topics"} resource_type 资源类型
 * @apiParam {Number} resource_id 资源id
 * @apiGroup Likes
 */
LikesController.like = function (req, res, next) {
  return LikesController.likeAction(req, res, next, "like");
};

/**
 * @api {get} /md/unlike 取消点赞某个资源
 * @apiName  unLikeResource
 * @apiParam {String="reality_shows","show_topics"} resource_type 资源类型
 * @apiParam {Number} resource_id 资源id
 * @apiGroup Likes
 */
LikesController.unLike = function (req, res, next) {
  return LikesController.likeAction(req, res, next, "unLike");
};


function checkResourceExist(resourceModel, resourceId) {
  return resourceModel.findOne({ID: resourceId, Status: resourceModel.Statuses.NORMAL})
    .then(function (resource) {
      if(resource) {
        return resource;
      } else {
        return Promise.reject({errorType: "EntityNotFound", msg: "找不到该资源"});
      }
    });
}

LikesController.likeAction = function (req, res, next, likeAction) {
  var resourceType, ResourceModel, curUserId, resourceId;
  return req.paramsValidator
    .param('resource_type', {type: String, values: ["reality_shows", "show_topics"]})
    .param('resource_id', {type: Number})
    .validate()
    .then(function () {
      curUserId = req.params.userid;
      resourceType = _.camelCase(req.params.resource_type);
      resourceId = req.params.resource_id;
      ResourceModel = require(`../../models/${util.capitalizeFirstLetter(resourceType)}`);
      return checkResourceExist(ResourceModel, resourceId);
    })
    .then(function () {
      return ResourceModel[likeAction](curUserId, resourceId);
    })
    .then(function (record) {
      res.succSend(record);
    })
    .catch(function (err) {
      errorHandler(err, req, res, next);
    })
};

module.exports = LikesController;
