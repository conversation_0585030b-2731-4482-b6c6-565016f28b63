﻿var ParamsValidator = require('../../common/paramsValidator');
var PhotoAlbums = require('../../models/PhotoAlbums');
var Photos = require('../../models/Photos');
var errorHandler = require('../errorHandler');
var PhotoChannelController = {};
var _ = require('lodash');
var Promise = require('bluebird');
var { getRedis } = require('../../common/redis');

module.exports = PhotoChannelController;


/**
 * @api {get} /md/photo_channels/:channel_id/photos Get channel photos  [!弃用!]
 * @apiName listChannelPhotos
 * @apiGroup PhotoChannels
 * @apiParam {Number=0,1,2,3} channel_id Channel id  | 0:真人秀 | 1:牛图 | 2:壁纸 | 3:精彩同人 |
 * @apiParam {Number} [cur_page=1] current page
 * @apiParam {Number} [page_size=20] size for perpage, default 20, greater than 20 will be treated as 20
 * @apiParam {String=new,hot} sort_by new 通过创建时间排序;  hot通过热度排序 热度由两个指标决定， 指标A: 点赞数
 * 指标B： 新鲜度 刚上传为10， 7天内依次递减直至0    热度=点赞数 + 新鲜度
 *
 */
PhotoChannelController.listPhotos = function(req, res, next) {
  var channelId = req.params.channel_id;
  var sortBy = req.params.sort_by;
  var self = this;
  var MAX_PAGE_SIZE = 20;
  return ParamsValidator
    .from(req.params)
    .param('channel_id', {type: Number, values: PhotoAlbums.ChannelIds})
    .param('page_size', {type: Number, required:false})
    .param('cur_page', {type: Number, required: false})
    .param('sort_by', {type: String, values:["new", "hot"]})
    .validate()
    .then(function() {
      var curPage = req.params.cur_page || 1;
      var pageSize = Math.min((req.params.page_size || MAX_PAGE_SIZE), MAX_PAGE_SIZE);
      if(sortBy === "hot") {
        return Photos.listHotPhotosByChannelId(channelId, {
          curPage: curPage,
          pageSize: pageSize
        });
      }
      if(sortBy === "new") {
        return Photos.listNewPhotosByChannelId(channelId, {
          curPage: curPage,
          pageSize: pageSize
        });
      }
    }).then(function(result) {
      res.succSend(result);
    }).catch(function(err) {
      errorHandler(err, req, res, next);
    });
};


/**
 * @api {get} /md/public/photo_channels/:channel_id/albums Get channel albums
 * @apiName listChannelAlbums
 * @apiGroup PhotoChannels
 * @apiParam {Number=0,1,2,3} channel_id Channel id  | 0:真人秀 | 1:牛图 | 2:壁纸 | 3:精彩同人 |
 * @apiParam {Number} [cur_page=1] current page
 * @apiParam {Number} [page_size=20] size for perpage, default 20, greater than 20 will be treated as 20
 * @apiParam {String=new,hot} sort_by new 通过相册最新上传图片时间排序，hot通过热度排序 热度由两个指标决定， 指标A: 相册内相片点赞数之和
 * 指标B： 新鲜度 取相册内最新图片的时间， 刚上传图片的相册为10， 7天内依次递减直至0    热度=点赞数 + 新鲜度
 *
 */

PhotoChannelController.listPhotoAlbums = function(req, res, next) {
  var MAX_PAGE_SIZE = 20;
  var QnmTukuService = require('../services/QnmTukuService');
  return ParamsValidator
    .from(req.params)
    .param('channel_id', {type: Number, values: PhotoAlbums.ChannelIds})
    .param('page_size', {type: Number, required:false})
    .param('cur_page', {type: Number, required: false})
    .param('sort_by', {type: String, values:["new", "hot"], default:"hot"})
    .validate()
    .then(function() {
      var curPage = req.params.cur_page || 1;
      var pageSize = Math.min((req.params.page_size || MAX_PAGE_SIZE), MAX_PAGE_SIZE);
      return QnmTukuService.listChannelPhotoAlbums(req.params.channel_id, {
        page: curPage,
        pageSize: pageSize
      }, req.params.sort_by)
    }).then(function(result) {
      res.succSend(result);
    }).catch(function(err) {
      errorHandler(err, req, res, next);
    });
};


/**
 * @api {get} /md/public/photo_channels/:channel_id/albums/editor_pick Get channel editor pick albums
 * @apiName listEditorPickAlbums
 * @apiGroup PhotoChannels
 * @apiParam {Number} [cur_page=1] current page
 * @apiParam {Number} [page_size=8] page size
 */

PhotoChannelController.listEditorPickAlbums= function(req, res, next) {
  var channelId = req.params.channel_id;
  var self = this;
  return ParamsValidator
    .from(req.params)
    .param('channel_id', {type: Number, values: PhotoAlbums.ChannelIds})
    .param('page_size', {type: Number, required:false, default: 8})
    .param('cur_page', {type: Number, required: false, default: 1})
    .validate()
    .then(function() {
      var pageSize = req.params.page_size;
      var curPage = req.params.curPage;
      return PhotoAlbums.listByEditorPick(channelId, {
        curPage: curPage,
        pageSize: pageSize
      });
    }).then(function(result) {
      res.succSend(result);
    }).catch(function(err) {
      errorHandler(err, req, res, next);
    });
};


PhotoChannelController.getChannelAlbumsByLast7Days = function(channelId, option) {
  var self = this;
  var albumIdToPhotos;
  var curPage = option.curPage;
  var pageSize = option.pageSize;
  var totalPage;
  var albums;
  var sortFunc = option.sortFunc;

  return Photos.getByLast7Days().then(function(photos) {
    albumIdToPhotos = _.groupBy(photos, 'PhotoAlbumID');
    var albumIds = _.uniq(_.map(photos, 'PhotoAlbumID'));
    var query =  PhotoAlbums.scope()
      .where('ChannelId', channelId)
      .whereIn('ID', albumIds)
      .whereIn('AuditStatus', [PhotoAlbums.AUDIT_STATUSES.PASSED, PhotoAlbums.AUDIT_STATUSES.EDITOR_PICK])
      .where('Type', PhotoAlbums.Types.QNM_NORMAL)
      .where('Status', PhotoAlbums.Statuses.NORMAL);
      return PhotoAlbums.executeByQuery(query)
      .then(function(curAlbums) {
        return Promise.map(curAlbums, function(album) {
          return Photos.getTotalLikesCount(albumIdToPhotos[album.ID]).then(function(likesCount) {
            album.LikesCount = likesCount;
            return album;
          });
        });
      }).then(function(curAlbums) {
        albums = curAlbums;
        var albumsCount = albums.length;
        totalPage = Math.ceil(albumsCount / pageSize);
        curPage = Math.min(curPage, totalPage);
        albums = albums.map(function(album) {
          return PhotoAlbums.fillInfoForAlbumWithPhotos(album, albumIdToPhotos[album.ID]);
        });
        return sortFunc(albums);
      }).then(function(sortedAlbumsKey) {
        start = (curPage - 1) * pageSize;
        stop = start + pageSize - 1;
        return getRedis().zrevrangeAsync(sortedAlbumsKey, start, stop);
      }).then(function(albumIds) {
        return albumIds.map(function(id) {
          id = parseInt(id);
          return _.find(albums, {ID: id});
        });
      }).then(function(albums) {
        return {
          curPage: curPage,
          albums: albums,
          totalPage: totalPage
        };
      });
  });
};
