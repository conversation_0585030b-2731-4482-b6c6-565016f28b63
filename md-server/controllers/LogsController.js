/**
 * Created by zhen<PERSON> on 16-10-11.
 */

var LogsController = module.exports;
var errorHandler = require('../../md-server/errorHandler');
var NotFoundLogs = require('../../models/NotFoundLogs');

LogsController.collect = function (req, res, next) {
  var type, location;
  return req.paramsValidator
    .param('type', {type:String, values: ["404"]})
    .param('location')
    .validate()
    .then(function () {
      type = req.params.type;
      location = req.params.location;
    }).then(function () {
      return NotFoundLogs.create({
        url:location,
        CreateTime: Date.now(),
      })
    })
    .then(function () {
      res.succSend({msg: "Ok"});
    })
    .catch(function (err) {
      errorHandler(err, req, res, next);
    })
};
