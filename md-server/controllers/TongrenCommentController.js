let Promise = require('bluebird');
let _ = require('lodash');
let errorHandler = require('../errorHandler');

let TongrenCommentModel = require('../../models/MdTongrenComment');
let TongrenUserModel = require('../../models/MdTongrenUser');
let TongrenWorksModel = require('../../models/MdTongrenWorks');
let NotificationsModel = require('../../models/Notifications');
let WORK_TYPE_MAP = TongrenWorksModel.WORK_TYPE_MAP;

let commentMethods = {};
let TongrenCommentController = {};

module.exports = TongrenCommentController;

/**
 * @api {get} /md/tongren/comment/get  获取评论列表
 * @apiName  GetComment
 * @apiParam {Number} work_id  作品ID
 * @apiGroup Tongren
 */
TongrenCommentController.get = (req, res, next) => {
    let params = req.params;
    let workId = params.work_id;
    let curUserId = params.userid;

    return req.paramsValidator
        .param('work_id', {required: true, type: Number})
        .validate()
        .then(() => {
            return TongrenCommentModel.getFullCommentList(workId, curUserId, 1, TongrenCommentModel.COMMENT_CONFIG.pageSize);
        })
        .then(commentList => {
            res.succSend({list: commentList});
        })
        .catch(function(err) {
            errorHandler(err, req, res, next);
        });
};

/**
 * @api {get} /md/tongren/comment/add  添加评论
 * @apiName  AddComment
 * @apiParam {Number} work_id  作品ID
 * @apiParam {Number} [reply_id]  回复的评论ID
 * @apiParam {String} content  评论内容
 * @apiGroup Tongren
 */
TongrenCommentController.add = (req, res, next) => {
    let params = req.params;
    let workId = params.work_id;
    let replyId = params.reply_id;
    let replyContent = params.content;
    let curUserId = params.userid;
    let createTime = (new Date()).getTime();
    let workInfo, replyUserId, workTypeName;
    let commentInfo = {};

    return req.paramsValidator
        .param('work_id', {required: true, type: Number})
        .param('reply_id', {required: false, type: Number})
        .param('content', {required: true, type: String})
        .validate()
        .then(() => {
            // 获取作品详情，判断作品是否存在
            return commentMethods.checkWorkIsExit(workId)
                .then(workData => {
                    workInfo = workData;
                    workTypeName = _.find(WORK_TYPE_MAP, {ID: workData.Type}).NAME;
                });
        })
        .then(() => {
            // 判断回复的评论是否是当前用户所发
            if(replyId) {
                return TongrenCommentModel.getCommentInfoById(replyId)
                    .then(replyCommentInfo => {
                        if(_.isEmpty(replyCommentInfo)) {
                            return Promise.reject({msg: '回复的评论不存在'});
                        } else {
                            let replyWorkId = replyCommentInfo.WorkId;
                            replyUserId = replyCommentInfo.UserId;
                            if(replyUserId == curUserId) {
                                return Promise.reject({msg: '自己不能回复自己'});
                            } else if(replyWorkId != workId) {
                                return Promise.reject({msg: '回复的评论不属于本作品'});
                            } else {
                                return TongrenUserModel.getUserById(replyUserId, curUserId);
                            }
                        }
                    })
                    .then(replyUserInfo => {
                        commentInfo.ReplyUserInfo = replyUserInfo;
                    });
            }
        })
        .then(() => {
            replyId = replyId || null;
            let commentProps = {
                workId: workId,
                userId: curUserId,
                authorId: workInfo.AuthorId,
                replyId: replyId,
                replyContent: replyContent,
                createTime: createTime
            };
            return Promise.props({
                commentInfo: TongrenCommentModel.addAndUpdateCache(commentProps), // 添加评论
                userInfo: TongrenUserModel.getUserById(curUserId, curUserId), // 用户信息
                setCommentCount: commentMethods.setCommentCountByWorkId(workId, 'add'), // 设置评论数
                clearListCache: TongrenWorksModel.clearListCache(workInfo.Type, workInfo.ID) // 删除最热作品列表缓存
            });
        })
        .then(result => {
            commentInfo = _.extend(result.commentInfo, commentInfo);
            commentInfo.UserInfo = result.userInfo;
            commentInfo.Duration = (new Date()).getTime() - commentInfo.CreateTime || '1';
            commentInfo.AllowDelete = 'true';
        })
        .then(() => {
            // 推送评论的提醒消息
            let authorId = workInfo.AuthorId;
            let messageObj = {
                Type: _.toString(workInfo.Type),
                TypeName: workTypeName,
                Title: workInfo.Title,
                Content: replyContent,
                IsReply: 'false'
            };
            let message = JSON.stringify(messageObj);

            if(curUserId != authorId) {
                return Promise.join(
                    TongrenWorksModel.updateHotScore(workId, {comment: 1}), // 更新热度
                    commentMethods.addCommentNotification(curUserId, authorId, commentInfo.ID, message, createTime) // 推送评论的提醒消息
                );
            }
        })
        .then(() => {
            if(replyId) {
                // 推送回复评论的提示消息
                let replyMessageObj = {
                    Type: _.toString(workInfo.Type),
                    TypeName: workTypeName,
                    Title: workInfo.Title,
                    Content: replyContent,
                    IsReply: 'true'
                };
                let replyMessage = JSON.stringify(replyMessageObj);
                return commentMethods.addCommentNotification(curUserId, replyUserId, commentInfo.ID, replyMessage, createTime);
            }
        })
        .then(() => {
            res.succSend(commentInfo);
        })
        .catch(function(err) {
            errorHandler(err, req, res, next);
        });
};

/**
 * @api {get} /md/tongren/comment/delete  删除评论
 * @apiName  DeleteComment
 * @apiParam {Number} comment_id  评论ID
 * @apiGroup Tongren
 */
TongrenCommentController.delete = (req, res, next) => {
    let params = req.params;
    let commentId = params.comment_id;
    let curUserId = params.userid;
    let workId, authorId, workType;

    return req.paramsValidator
        .param('comment_id', {required: true, type: Number})
        .validate()
        .then(() => {
            return TongrenCommentModel.getCommentInfoById(commentId)
                .then(commentInfo => {
                    if(_.isEmpty(commentInfo)) {
                        return Promise.reject({msg: '评论不存在'});
                    } else {
                        workId = commentInfo.WorkId;
                        authorId = commentInfo.AuthorId;
                    }
                });
        })
        .then(() => {
            // 判断作品是否存在
            return TongrenWorksModel.getWorkDetail(workId, false)
                .then(workInfo => {
                    if(!_.isEmpty(workInfo)) {
                        workType = workInfo.Type;
                    } else {
                        return Promise.reject({msg: '作品不存在'});
                    }
                });
        })
        .then(() => {
            // 判断是否有权限删除评论
            return TongrenCommentModel.checkDeletePermissionById(curUserId, commentId);
        })
        .then(() => {
            return Promise.join(
                TongrenCommentModel.deleteAndUpdateCache(commentId, workId), // 删除评论
                NotificationsModel.removeDeletedTongrenComment(commentId), // 删除对应的评论推送消息
                commentMethods.setCommentCountByWorkId(workId, 'delete') // 设置作品表的评论数
            );
        })
        .then(() => {
            if(curUserId != authorId) {
                return Promise.join(
                    TongrenWorksModel.updateHotScore(workId, {comment: -1}), // 更新热度
                    TongrenWorksModel.clearListCache(workType, workId) // 删除最热作品列表缓存
                );
            }
        })
        .then(() => {
            res.succSend({msg: '已删除'});
        })
        .catch(function(err) {
            errorHandler(err, req, res, next);
        });
};

// 设置作品表的评论数
commentMethods.setCommentCountByWorkId = (workId, action) => {
    // 获取作品表的评论数
    return TongrenWorksModel.getCommentCountByWorkId(workId)
        .then(commentCount => {
            // 设置作品的评论数
            let count = commentCount;
            switch (action) {
                // 添加评论
                case 'add':
                    count += 1;
                    break;
                // 删除评论
                case 'delete':
                    count -= 1;
                    break;
                default:
                    break;
            }
            return TongrenWorksModel.setCommentCountByWorkId(workId, count)
                .return(count);
        });
};

// 验证作品是否存在
commentMethods.checkWorkIsExit = (workId) => {
    return TongrenWorksModel.getWorkDetail(workId, false)
        .then(workData => {
            if(_.isEmpty(workData)) {
                return Promise.reject({msg: '作品不存在'});
            } else {
                return workData;
            }
        });
};

// 推送评论的提醒消息
commentMethods.addCommentNotification = (userId, replyUserId, commentId, message, createTime) => {
    let notificationType = NotificationsModel.EVENT_TYPES["COMMENT_TONGREN_WORK"];
    return NotificationsModel.create({
        UserId: userId,
        TargetId: replyUserId,
        Type: notificationType,
        RelateId: commentId,
        Message: message,
        CreateTime: createTime
    });
};