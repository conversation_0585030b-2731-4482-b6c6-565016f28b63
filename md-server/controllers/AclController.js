/**
 * Created by <PERSON><PERSON><PERSON> on 16-11-7.
 */

var Users = require('../../models/Users');
var ACL = module.exports;

var ACTIONS = {
  ALL: "all",
  ADD_MOMENT: "add_moment",
  ADD_SHOW_TOPIC: "add_show_topic",
  ADD_COMMENT: "add_comment",
  ADD_PHOTO: "add_photo",
  ADD_MSG: "add_msg",
  LIKE: "like"
};

var USER_STATUSES = {
  NORMAL: 0,
  BANNED: -1, //用户封禁
  BAN_CREATE: -2, //用户禁言 所有插库操作禁止
};

var BanStateManger = {};

BanStateManger.getBanState = function (userId) {
  return Users.findById(userId, ['Status'])
    .then(function (user) {
      if(user.Status === USER_STATUSES.BANNED || user.Status === USER_STATUSES.BAN_CREATE) {
        return {all:true}; // 封禁和禁言都映射岛禁止所有状态
      } else {
        return {};
      }
    })
};

BanStateManger.isBanAll = function (banState) {
  return !!banState[ACTIONS.ALL];
};

BanStateManger.isBanThisAction = function (banState, action) {
  return !!banState[action];
};


BanStateManger.isBan = function (banState, action) {
  return BanStateManger.isBanAll(banState) || BanStateManger.isBanThisAction(banState, action);
};

ACL.Actions = ACTIONS;

ACL.can = function (userId, checkAction) {
  return BanStateManger.getBanState(userId).then(function (state) {
    return BanStateManger.isBan(state, checkAction);
  }).then(function (isBan) {
    if(isBan) {
      return Promise.reject({errorType: "PermissionDenied", msg:"权限拒绝!", banAction: checkAction});
    } else {
      return true;
    }
  });
};
