/**
 * Created by <PERSON><PERSON><PERSON> on 2017/4/1.
 */

const errorHandler = require('../errorHandler');
const util = require('../../common/util');
const _ =  require('lodash');
const Promise =  require('bluebird');
const Constants =  require('../../common/data').Constants;
const TukuPyqPhoto = require('../../models/TukuPyqPhoto');
const PyqMoment = require('../../models/PyqMoments');

const TukuController = module.exports = {
  /**
   * @api {post} /md/qnm/tuku/set_audit_results 设置手游热门心情图片的审核状态
   * @apiName  setAuditResult
   * @apiGroup QnmTuku
   * @apiParam {JsonObject} audit_results  json结构 [{photo_id: String, audit_status: Int}...]
   */
  setAuditResults: function (req, res, next) {
    return new Promise((resolve, reject) => {
      let result = req.params.audit_results;
      result = util.getJsonInfo(result);
      result = _.isArray(result) ? result: [result];
      const isSchemeValid = _.every(result, row => {
        return row.hasOwnProperty('photo_id') && row.hasOwnProperty('audit_status')
      });
      if(!isSchemeValid) {
        reject({errorType:"ParamsInvalid", msg: "参数格式非法"})
      } else {
        resolve(result);
      }
    }).then(auditResultList => {
      //这里合并下请求在更新数据库
      const auditStatusToRecord = _.groupBy(auditResultList, 'audit_status');
      const now = Date.now();
      return Promise.map([Constants.STATUS_AUDIT_REJECT, Constants.STATUS_AUDIT_PASS, Constants.STATUS_AUDIT_PICK], auditStatus => {
        const photoIds = _.map(auditStatusToRecord[auditStatus], 'photo_id');
        if(!_.isEmpty(photoIds)) {
          return TukuPyqPhoto.updateByCondition({ID:photoIds}, {
            AuditStatus: auditStatus,
            AuditTime: now
          });
        } else {
          return [];
        }
      })
    }).then(() => {
      res.succSend({msg: "OK!"});
    }).catch(err => {
      errorHandler(err, req, res, next);
    })
  },

  /**
   * @api {get} /md/qnm/tuku/pyq_moment_photo/list 列出图库手游热门心情的图片
   * @apiName  listPyqMomentPhoto
   * @apiGroup QnmTuku
   * @apiParam {Number} [last_audit_time] lastAuditTime  获取这个审核事件节点之后的图片
   * @apiParam {Number} [cur_page=1] page 页码
   * @apiParam {Number} [page_size=8] page size 页码大小
   */
  listPyqMomentPhoto: function (req, res, next) {
    return req.paramsValidator
    .param('last_audit_time', {type: Number, required: false})
    .param('cur_page', {type: Number, default:1, min:1, max:200, required: false})
    .param('page_size', {type: Number, required:false, default:8, max:16})
    .validate()
    .then(() => {
      const params = req.params;
      const pageSize = params.page_size;
      if(params.hasOwnProperty('last_audit_time')) {
        return TukuController.listPyqMomentByPhotoByLastAuditTime(params.last_audit_time, pageSize);
      } else {
        return TukuController.listPyqMomentPhotoByPage({page: params.cur_page, pageSize:pageSize});
      }
    })
    .then(info => {
      res.succSend(info);
    }).catch(err => {
      errorHandler(err, req, res, next);
    })
  },

  listPyqMomentPhotoByPage: function (pagination) {
    const  getPhotos = () => {
      const query = TukuPyqPhoto.normalScope()
      .select('ID', 'PyqMomentId', 'Url', 'CreateTime', 'AuditTime')
      .whereIn('AuditStatus', [Constants.STATUS_AUDIT_PASS, Constants.STATUS_AUDIT_PICK])
      .where('Status', Constants.STATUS_NORMAL)
      .orderBy('AuditTime', 'desc');
      return TukuPyqPhoto.queryWithPagination(query,pagination)
      .then(TukuController.formatPyqMomentPhotos);
    };

    const getTotalPage = () => {
      const query =  TukuPyqPhoto.scope().count('ID as count')
      .from(function () {
        return this.select('ID').from(TukuPyqPhoto.tableName)
        .where('Status', Constants.STATUS_NORMAL)
        .whereIn('AuditStatus', [Constants.STATUS_AUDIT_PASS, Constants.STATUS_AUDIT_PICK])
        .limit(500)
        .as("r")
      });
      return TukuPyqPhoto.executeByQuery(query).then(row => {
        return Math.ceil(row[0].count / pagination.pageSize);
      });
    };

    return Promise.all([
      getPhotos(),
      getTotalPage(),
    ]).spread((photos, totalPage) => {
      return {
        photos: photos,
        meta: {
          page: pagination.page,
          totalPage: totalPage
        }
      }
    })
  },

  listPyqMomentByPhotoByLastAuditTime: function (lastAuditTime, limit) {
    let query = TukuPyqPhoto.normalScope()
    .select('ID', 'PyqMomentId', 'Url', 'CreateTime', 'AuditTime')
    .whereIn('AuditStatus', [Constants.STATUS_AUDIT_PASS, Constants.STATUS_AUDIT_PICK])
    .where('Status', Constants.STATUS_NORMAL)
    .orderBy('AuditTime', 'desc')
    .limit(limit);
    if(lastAuditTime) {
      query = query.where('AuditTime', '>', lastAuditTime);
    }
    return TukuPyqPhoto.executeByQuery(query)
    .then(TukuController.formatPyqMomentPhotos)
    .then(photos => {
      const info = {photos: photos, meta: {hasMore:false, lastAuditTime: null}};
      if(!_.isEmpty(photos) && photos.length === limit) {
        info.meta.hasMore = true;
        info.meta.lastAuditTime = _.last(photos).AuditTime;
      }
      return info;
    })
  },

  /**
   * @api {get} /md/qnm/tuku/pyq_moment_photo/pick 列出上首页的手游热门心情的图片
   * @apiName  listPickPyqMomentPhoto
   * @apiGroup QnmTuku
   * @apiParam {Number} [cur_page=1] page 页码
   * @apiParam {Number} [page_size=8] page size 页码大小
   */
  listPickPyqMomentPhoto: function (req, res, next) {
    return req.paramsValidator
    .param('page_size', {type: Number, required:false, default:8, max:16})
    .param('cur_page', {type: Number, required: false, default:1, min:1})
    .validate()
    .then(() => {
      const params = req.params;
      const query = TukuPyqPhoto.normalScope()
      .select('ID', 'PyqMomentId', 'Url', 'CreateTime')
      .where('AuditStatus', Constants.STATUS_AUDIT_PICK)
      .orderBy('AuditTime', 'desc');
      return TukuPyqPhoto.queryWithPagination(query, {page: params.cur_page, pageSize: params.page_size});
    })
    .then(TukuController.formatPyqMomentPhotos)
    .then(data => {
      res.succSend(data);
    }).catch(err => {
      errorHandler(err, req, res, next);
    })
  },

  formatPyqMomentPhotos: function (photos) {
    const pyqMomentIds = _.uniq(_.map(photos, 'PyqMomentId'));
    return PyqMoment.findByIds(pyqMomentIds, ['ID', 'HotState'])
    .then(moments => {
      const pyqMomentIdToRecord = util.keyToRecordHash(moments, 'ID');
      _.forEach(photos, photo => {
        const relatedMoment = pyqMomentIdToRecord[photo.PyqMomentId];
        const hotState = util.getJsonInfo(relatedMoment.HotState);
        photo.LikesCount = hotState.like || 0;
        photo.Url = util.toHttps(photo.Url);
        delete photo.PyqMomentId;
      });
      return photos;
    })
  }
};
