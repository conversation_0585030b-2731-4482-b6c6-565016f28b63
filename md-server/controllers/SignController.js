/**
 * Created by <PERSON><PERSON><PERSON> on 2017/1/4.
 */

let SignController = {};
let errorHandler = require('../errorHandler');
let SignService = require('../../service/qn/role/sign');
let nosClient = require('../../common/nosSdkClient').nosClient;
let NosConfig = require('../../common/config').NOS_CFG;
let Promise = require('bluebird');

function putToNos(key, filePath) {
  const QN_BUCKET = NosConfig.bucketName['qn'];
  return nosClient.put_file_async({
    bucket: QN_BUCKET,
    key: key,
    filepath: filePath,
  }).then(function (info) {
    if(info.statusCode == 200) {
      return 'https://hi-163-qn.nosdn.127.net/' +  key;
    } else {
      return Promise.reject({errorType: "NosUpdateFailed", error: info});
    }
  });
}


SignController.updateSignature = function (req, res, next) {
  let roleId;
  return req.paramsValidator
    .param('roleid', {type: String})
    .param('game', {type: String, values:["qn"]})
    .validate()
    .then(function () {
      roleId = req.params.roleid;
      return SignService.getSignaturePaths(roleId);
    })
    .then(function (path) {
      return Promise.props({
        attrPath: putToNos(`qn/sign/${roleId}_attr.png`, path.attrPath),
        antiPath: putToNos(`qn/sign/${roleId}_anti.png`, path.antiPath)
      })
    })
    .then(function (urls) {
      res.succSend(urls);
    }).catch(err => {
      errorHandler(err, req, res, next);
    })
};


module.exports = SignController;
