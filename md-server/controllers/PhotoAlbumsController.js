var PhotoAlbums = require('../../models/PhotoAlbums');
var Photos = require('../../models/Photos');
var Users = require('../../models/Users'); var logger = require('../../common/logger');
var Errors = require('../../common/errors');
var errorHandler = require('../errorHandler');
var _ = require('lodash');
var ParamsValidator = require('../../common/paramsValidator');
var Promise = require('bluebird');
var Auth = require('../../common/auth');
var Constants = require('../../common/data').Constants;

var PhotoAlbumsController = {};

PhotoAlbumsController.MAX_PAGE_SIZE = 25;

PhotoAlbumsController.checkDeleteValidation = function(userId, albumId) {
  return PhotoAlbumsController.checkUserOwnAlbum(userId, albumId).then(function(album) {
    if(!PhotoAlbums.isNormalAlbum(album)) {
      return Promise.reject({msg: "Only normal album can be deleted!"});
    }
  });
};


PhotoAlbumsController.checkUserOwnAlbum = function(userId, albumId) {
  return PhotoAlbums.checkUserOwnAlbum(userId, albumId);
};

PhotoAlbumsController.checkUpdateValidation = function(userId, albumId, updateProps) {
  var album;
  return PhotoAlbumsController.checkUserOwnAlbum(userId, albumId).then(function(album) {
    album = album;
    if(updateProps.Name) {
      var updateName = updateProps.Name;
      return PhotoAlbums.isRenameConflict(album, updateName).then(function(isNameConflict) {
        if(isNameConflict) {
          return Promise.reject({errorType:"AlbumNameExist", msg: "album new name is conflict with other album names"});
        }
      });
    }
  }).then(function() {
    return album;
  });
};

/**
 * @api {get} /md/photo_albums Get list of user photo albums
 * @apiName GetUserAlbumsList
 * @apiGroup PhotoAlbums
 * @apiParam {String} [album_user_id]  album user id, default is current user
 */
PhotoAlbumsController.list = function(req, res, next) {
  var albumsUserId = req.params.album_user_id;
  var curUserId;
  var isBrowseSelfAlbums;

  return Auth.check(req)
    .then(function(info) {
      curUserId = info.userid;
    }).catch(function(err) {
      logger.error("get photo without login", err);
      //ignore auth error cause this api can be called without login
      if(process.env.NODE_ENV === "development") {
        curUserId = req.params.userid;
      }
    }).then(function() {
      albumsUserId = albumsUserId || curUserId;
      isBrowseSelfAlbums = (curUserId == albumsUserId);
      return Users.findByIdForce(albumsUserId);
    }).then(function(user) {
      return PhotoAlbums.getUserAlbums(albumsUserId, curUserId);
    }).then(function(albums) {
      var avatarAlbum = albums.avatarAlbum;
      var momentAlbum = albums.momentAlbum;
      var showTopicAlbum = albums.showTopicAlbum;
      var qnScreenshotAlbum = albums.qnScreenshotAlbum;
      var normalAlbums = albums.normalAlbums;

      var normalAlbumsPhotoCount = PhotoAlbums.totalPhotoCount(normalAlbums);

      res.succSend({
        avatarAlbum: avatarAlbum,
        momentAlbum: momentAlbum,
        showTopicAlbum: showTopicAlbum,
        qnScreenshotAlbum: qnScreenshotAlbum,
        normalAlbums: {
          total: normalAlbumsPhotoCount,
          albums: normalAlbums,
        },
        meta: {
          isSelf: isBrowseSelfAlbums
        }
      });

    }).catch(function(err) {
      errorHandler(err, req, res, next);
    });
};


PhotoAlbumsController.create = function(req, res, next) {
  var userId = req.params.userid;
  var name = req.params.name;
  var channelId = req.params.channelId;
  return ParamsValidator.from(req.params)
    .param('channelId', {type: Number, values: PhotoAlbums.ChannelIds})
    .param('name', {type: String, maxlen: 7, sensitiveCheck: true})
    .param('game_name', {type: String, default:'qnm', values: ['qn', 'qnm']})
    .validate()
    .then(function() {
      return PhotoAlbums.findOne({UserId: userId, Name: name});
    }).then(function(album) {
      if(album) {
        return Promise.reject({errorType:"DuplicateAlbumName", msg: "Album name exists!"});
      }
    }).then(function() {
      var gameName = req.params.game_name;
      var albumType = PhotoAlbums.Types.QNM_NORMAL;
      if(gameName == "qn") {
        albumType = PhotoAlbums.Types.QN_NORMAL;
      }
      return PhotoAlbums.create({
        UserId: userId,
        Name: name,
        ChannelID: channelId,
        Type: albumType
      });
    }).then(function(photoAlbum) {
      photoAlbum.Count = 0;
      res.succSend({photoAlbum: photoAlbum});
    }).catch(function(err) {
      errorHandler(err, req, res, next);
    });
};



PhotoAlbumsController.delete = function(req, res, next) {
  var albumId = req.params.album_id;
  var userId = req.params.userid;
  return PhotoAlbumsController.checkDeleteValidation(userId, albumId).then(function() {
    return PhotoAlbums.softDeleteById(albumId);
  }).then(function(result) {
    res.succSend({msg: "OK"});
  }).catch(function(err) {
    errorHandler(err, req, res, next);
  });
};


PhotoAlbumsController.update = function(req, res, next) {
  var userId = req.params.userid;
  var albumId = req.params.album_id;
  var name = req.params.name;
  var channelId = req.params.channelId;
  var updateProps = {};
  if(name) {
    updateProps.Name = name;
  }
  if(channelId) {
    updateProps.ChannelId = channelId;
  }

  return ParamsValidator.from(req.params)
    .param('album_id')
    .param('name', {type: String, required: false, maxlen:7})
    .param('channelId', {type: Number, required:false, values: PhotoAlbums.ChannelIds})
    .validate()
    .then(function() {
      return PhotoAlbumsController.checkUpdateValidation(userId, albumId, updateProps);
    }).then(function() {
      return PhotoAlbums.updateById(albumId, updateProps);
    }).then(function(album) {
      res.succSend({album: album});
    }).catch(function(err) {
      errorHandler(err, req, res, next);
    });
};

PhotoAlbumsController.listPhotos = function(req, res, next) {
  var currentUserId = parseInt(req.params.userid);
  var albumId = req.params.album_id;
  var lastId = req.params.lastId || null;
  var perPage = req.params.perPage || PhotoAlbumsController.MAX_PAGE_SIZE;
  perPage = Math.min(perPage, PhotoAlbumsController.MAX_PAGE_SIZE);
  var photoAlbum;
  var isBrowseSelfAlbum = false;

  return PhotoAlbums.findByIdForce(albumId).then(function(album) {
    return PhotoAlbums.checkViewPermission(album, currentUserId);
  }).then(function(album) {
    if(album.UserId === currentUserId) {
      isBrowseSelfAlbum = true;
    }
    return PhotoAlbums.getAlbumWithCount(album, isBrowseSelfAlbum);
  }).then(function(album) {
    photoAlbum = album;
    if(lastId) {
      return Photos.findByIdForce(lastId).then(function(lastPhoto) {
        return PhotoAlbums.listPhotos(album.ID, {
          perPage: perPage,
          lastCreateTime: lastPhoto.CreateTime,
          isBrowseSelfAlbum: isBrowseSelfAlbum
        });
      });
    } else {
      return PhotoAlbums.listPhotos(album.ID, {
        perPage: perPage,
        isBrowseSelfAlbum: isBrowseSelfAlbum
      });
    }
  }).then(function(photos) {
    _.forEach(photos, photo => {
      if(photo.AuditStatus === Constants.STATUS_AUDIT_PICK) {
        photo.AuditStatus = Constants.STATUS_AUDIT_PASS;
      }
    });
    photos = Photos.formatAuditStatus(photos);
    return Promise.props({
      photos: photos,
      albums: PhotoAlbums.getUserNormalAlbums(currentUserId)
    });
  }).then(function(albumsAndPhotos) {
    var photos = albumsAndPhotos.photos;
    var albums = _.compact(albumsAndPhotos.albums);
    albums = _.filter(albums, function(album) {
      return album.ID !== photoAlbum.ID;
    });

    if(photoAlbum.UserId === currentUserId) {
      photos = Photos.addAuditingWaterMarkToPhotos(photos);
    } else {
      photos = Photos.filterAuditPassedPhotos(photos);
    }
    photoAlbum = PhotoAlbums.fillInfoForAlbumWithPhotos(photoAlbum, photos);
    res.succSend({
      photos:photos,
      album:photoAlbum,
      targetMovedAlbums: albums,
      meta: {
        isSelf: isBrowseSelfAlbum
      }
    });
  }).catch(function(err) {
    errorHandler(err, req, res, next);
  });
};


/**
 * @api {get} /md/public/photo_albums/:album_id/photos Get album pubic photos
 * @apiName listAlbumPublicPhotos
 * @apiGroup PhotoAlbums
 * @apiParam {Number} album_id 相册的ID
 */
PhotoAlbumsController.listPublicPhotos = function(req, res, next) {
  var albumId = req.params.album_id;
  var curAlbum;
  const Contants = require('../../common/data').Constants;
  return ParamsValidator.from(req.params)
    .param('album_id', {type: Number})
    .validate()
    .then(function() {
      return PhotoAlbums.findOne({
        ID: albumId,
        Status: PhotoAlbums.Statuses.NORMAL,
        AuditStatus: [PhotoAlbums.AUDIT_STATUSES.PASSED, PhotoAlbums.AUDIT_STATUSES.EDITOR_PICK]
      }, ['ID', 'CoverUrl', 'Name', 'ChannelId', 'CreateTime', 'UpdateTime', 'LastAddPhotoTime', 'UserId']);
    }).then(function(album) {
      if(!album) {
        return Promise.reject({errorType:"AlbumIdInvalid", msg: "相册ID非法"});
      }
      curAlbum = album;
      return album;
    }).then(function(album) {
      var getAlbumPhotos = Photos.executeByQuery(
        Photos.scope()
        .where({PhotoAlbumID: curAlbum.ID})
        .where({Status: Photos.Statuses.NORMAL})
        .where('AuditStatus', Contants.STATUS_AUDIT_PICK)
        .orderBy('CreateTime', 'desc')
      ).then(function(photos) {
        const lastPhoto = _.last(photos);
        if(lastPhoto) {
          curAlbum.LastAddPhotoTime = album.UpdateTime = lastPhoto.CreateTime;
          curAlbum.CoverUrl = album.Cover || lastPhoto.Url;
        }
        return Promise.map(photos, Photos.getPhotoWithLikesCount);
      });
      var getPhotoOwner = Users.findOne({
        Id: curAlbum.UserId,
        AvaAuthStatus: Users.AvaAuthStatuses.PASSED
      }).then(function(user) {
        if(user && user.Avatar) {
          return _.pick(user, 'Avatar');
        } else {
          return {Avatar: Users.DEFAULT_AVATAR};
        }
      });
      return Promise.props({
        album: curAlbum,
        albumPhotos: getAlbumPhotos,
        photoOwner: getPhotoOwner
      });
    }).then(function(result) {
      return res.succSend(result);
    }).catch(function(err) {
      errorHandler(err, req, res, next);
    });
};



module.exports = PhotoAlbumsController;
