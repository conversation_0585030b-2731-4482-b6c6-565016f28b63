var ShowTopicsController = module.exports = {};
var errorHandler = require('../errorHandler');
var ShowTopics = require('../../models/ShowTopics');
var ShowSubjects = require('../../models/ShowSubjects');
var Tags = require('../../models/Tags');
var Users = require('../../models/Users');
var Contacts = require('../../models/Contacts');
var _ = require('lodash');
var util = require('../../common/util');
var Promise = require('bluebird');
var MDEventBus = require('../eventBus');
var AuditPhotoService = require('../../common/audit');
var PhotoAlbums = require('../../models/PhotoAlbums');
var logger = require('../../common/logger');
var RealityShows = require('../../models/RealityShows');

/**
 * @api {get} /md/show_topics/new Create showTopic
 * @apiName CreateShowTopic
 * @apiParam {String} subject_id   炫耀贴主题Id
 * @apiParam {String} title        炫耀贴标题
 * @apiParam {String} desc         炫耀贴简介
 * @apiParam {String} content      炫耀贴内容
 * @apiParam {String} [tags]       tags用逗号风格
 * @apiParam {Array}  [imglist]    图片Url数组
 * @apiGroup ShowTopics
 */
ShowTopicsController.create = function(req, res, next) {
  var params = req.params;
  var showSubject;
  return req.paramsValidator
    .param('subject_id', {type: Number})
    .param('title', {type: String, maxlen: 30})
    .param('desc', {type: String, maxlen: 60})
    .param('content', {type: String})
    .param('imglist', {type: Array, minSize:1, maxSize: 9})
    .param('tags', {type: String, required: false})
    .validate()
    .then(function() {
      var props = {
        UserId: params.userid,
        SubjectId: params.subject_id,
        Title: params.title,
        Desc: params.desc,
        Content: params.content,
      };
      if(params.imglist) {
        var isStartWithNosPrefix = _.every(params.imglist, x => x.match(/nosdn\.127\.net/) && !x.includes(","));
        if(!isStartWithNosPrefix) {
          return Promise.reject({errorType: "ParamsInvalid", msg: "图片路径必须符合nos前缀"});
        }
        props.ImgList = _.join(params.imglist, ',');
        var Constants = require('../../common/data').Constants;
        props.ImgAudit = _.repeat(`${Constants.STATUS_AUDIT_INIT},`, params.imglist.length);
      }

      return ShowSubjects.checkRecordById(props.SubjectId, "该炫耀主题不存在!")
        .then(function (r) {
          showSubject = r;
          return ShowTopics.create(props)
            .then(function (r) {
              AuditPhotoService.sendPic('md', params.imglist, {
                roleId: props.UserId,
                picId: 'md_show_topic:' + r.ID,
              });
              PhotoAlbums.addPhotoUrlsToShowTopicAlbum(r.UserId, params.imglist).catch(function (err) {
                logger.error(err);
              });
              return r;
            })
        });
    })
    .then(function(showTopic) {
      var tags = [showTopic.Title];
      tags = _.union(tags, Tags.fromTagsStr(req.params.tags));
      return ShowTopics.addTags(showTopic.ID, tags).then(function() {
        showTopic.Tags = tags;
        return showTopic;
      });
    })
    .then(ShowTopics.Serializers.default)
    .then(function(showTopic) {
      res.succSend({
        showTopic: showTopic
      });
    }).catch(function(err) {
      errorHandler(err, req, res, next);
    });
};

function checkShowTopicExist(topicId) {
  return ShowTopics.findOne({ID: topicId, Status: ShowTopics.Statuses.NORMAL})
    .then(function(showTopic) {
      if(_.isEmpty(showTopic)) {
        return Promise.reject({errorType: "EntityNotFound", msg: "找不到对应ID的炫耀贴"});
      } else {
        return showTopic;
      }
    });
}


function checkDeletePermission(userId, topicId) {
  return checkShowTopicExist(topicId)
    .then(function(showTopic) {
      if(showTopic.UserId === userId) {
        return showTopic;
      } else {
        return Promise.reject({errorType: "PermissionDenied", msg: "没有删除权限"});
      }
    });
}

function checkUpdatePermission(userId, topicId) {
  return checkShowTopicExist(topicId)
    .then(function(showTopic) {
      if(showTopic.UserId === userId) {
        return showTopic;
      } else {
        return Promise.reject({errorType: "PermissionDenied", msg: "没有更新权限"});
      }
    });
}

/**
 * @api {get} /md/show_topics/:topic_id/delete Delete user showTopic
 * @apiParam {Number} topic_id
 * @apiName DeleteShowTopic
 * @apiGroup ShowTopics
 */
ShowTopicsController.delete = function(req, res, next) {
  let Notifications = require('../../models/Notifications');
  let topicId;
  return req.paramsValidator
    .param('topic_id', {type: Number})
    .validate()
    .then(function() {
      topicId = req.params.topic_id;
      var userId = req.params.userid;
      return checkDeletePermission(userId, topicId);
    }).then(function(showTopic) {
      return ShowTopics.softDeleteById(showTopic.ID);
    }).then(function() {
      Notifications.removeOnDeleteShowTopic(topicId);
      res.succSend({msg: "OK"});
    }).catch(function(err) {
      errorHandler(err, req, res, next);
    });
};

/**
 * @api {get} /md/show_topics/:topic_id/update Update user showTopic
 * @apiParam {Number} topic_id
 * @apiParam {String} [title]
 * @apiParam {String} [tags]
 * @apiName UpdateShowTopic
 * @apiGroup ShowTopics
 */
ShowTopicsController.update = function(req, res, next) {
  var topicId, userId, title, tags;
  return req.paramsValidator
    .param('topic_id', {type: Number})
    .param('title', {required: false, minlen:3})
    .param('tags', {required: false})
    .requiredAny(['title', 'tags'])
    .validate()
    .then(function() {
      topicId = req.params.topic_id;
      userId = req.params.userid;
      title = req.params.title;
      tags = req.params.tags;
      return checkUpdatePermission(userId, topicId);
    }).then(function(showTopic) {
      var updateProps = {};
      if(title) {
        updateProps.Title = title;
      }
      if(tags) {
        var tagsArr = _.split(tags, ',');
        updateProps.Tags = tagsArr;
      }
      return ShowTopics.updateById(showTopic.ID, updateProps);
    })
    .then(ShowTopics.Serializers.default)
    .then(function(showTopic) {
      res.succSend({showTopic: showTopic});
    }).catch(function(err) {
      errorHandler(err, req, res, next);
    });
};



/**
 * @api {get} /md/users/:user_id/show_topics List user showtopics
 * @apiName ListUserShowTopics
 * @apiParam {user_id} user_id
 * @apiParam {Number} [page=1] 页码 最小为1
 * @apiParam {Number} [page_size=10] 分页大小 最大为10
 * @apiParam {Number="new","hot"} [sort_by=new] 最新或者最热排序
 * @apiGroup ShowTopics
 */
ShowTopicsController.listByUser = function(req, res, next) {
  var MAX_PAGE_SIZE = 10;
  var page;
  var pageSize;
  return req.paramsValidator
    .param('user_id', {type: Number})
    .param('page', {type:Number, default:1, min:1})
    .param('page_size', {type:Number, default: MAX_PAGE_SIZE, min:1})
    .param('sort_by', {default: "new", values: ["hot", "new"]})
    .validate()
    .then(function() {
      var curUserId = req.params.userid;
      var visitUserId = req.params.user_id;
      page = req.params.page;
      pageSize = Math.min(req.params.page_size, MAX_PAGE_SIZE);
      var pagination = { page: page, pageSize: pageSize};
      var sortBy = req.params.sort_by;
      var isViewBySelf = (curUserId == visitUserId);

      var getUserShowTopics =  function () {
        return ShowTopics.listByUser(visitUserId, pagination, sortBy, isViewBySelf)
          .then(ShowTopics.Serializers.default);
      };

      return Users.checkUserValid(visitUserId)
        .then(function () {
          return Promise.props({
            showTopics: getUserShowTopics(),
            user: Users.getUser(curUserId).then(function (user) {
              require('../../common/data').md.setAvatarView(user, curUserId);
              return Contacts.getUserWithFollowType(curUserId, user, 'FollowType');
            })
          })
        });
    })
    .then(function(record) {
      res.succSend(record);
    }).catch(function(err) {
      errorHandler(err, req, res, next);
    });
};


/**
 * @api {get} /md/show_topics/search 通过关键字搜索炫耀贴
 * @apiName  showTopicsSearch
 * @apiParam {Number} [page=1] 页码 最小为1
 * @apiParam {Number} [page_size=10] 分页大小 最大为10
 * @apiParam {String} keyword 关键词
 * @apiGroup ShowTopics
 */
ShowTopicsController.search = function(req, res, next) {
  var MAX_PAGE_SIZE = 10;
  var page;
  var pageSize;
  var keyword;
  var curUserId;
  var subjectId;
  var showSubject;
  return req.paramsValidator
    .param('keyword', {type: String})
    .param('subject_id', {required: false})
    .param('page', {type:Number, default:1, min:1})
    .param('page_size', {type:Number, default: MAX_PAGE_SIZE, min:1})
    .validate()
    .then(function() {
      keyword = req.params.keyword;
      subjectId = req.params.subject_id;
      page = req.params.page;
      curUserId = req.params.userid;
      pageSize = Math.min(req.params.page_size, MAX_PAGE_SIZE);

      var pagination = { page: page, pageSize: pageSize};
      if(subjectId) {
        return ShowSubjects.checkRecordById(subjectId, "该炫耀主题不存在!")
        .then(r => ShowSubjects.getSubjectWithHot(r))
        .then(r => {
          showSubject = r;
          return ShowTopics.findByTagLikeInSubject(subjectId, keyword, pagination);
        });
      } else {
        return ShowTopics.findByTagLike(keyword, pagination);
      }
    })
    .then(function(showTopics) {
        return ShowTopics.getShowTopicsWithTags(showTopics);
    })
    .then(ShowTopics.Serializers.default)
    .then(function (showTopics) {
      var userIds = _.map(showTopics, 'UserId');
      return Users.getUsersByIds(userIds, ['ID', 'NickName', 'Avatar', 'AvaAuthStatus'])
        .map(function (user) {
          require('../../common/data').md.setAvatarView(user, curUserId);
          return user;
        })
        .then(function (users) {
          return Contacts.getUsersWithFollowType(curUserId, users, 'FollowType');
        })
        .then(function (users) {
          util.embeddedOn(showTopics,  users, 'UserId', 'ID', 'User');
          return showTopics;
        })
    })
    .then(function(showTopics) {
      var result = {showTopics: showTopics};
      if(showSubject) {
        result.showSubject = showSubject;
      }
      return result;
    })
    .then(function (result) {
      res.succSend(result);
    })
    .catch(function(err) {
      errorHandler(err, req, res, next);
    });
};

/**
 * @api {get} /md/show_topics/:topic_id Get showTopic
 * @apiParam {Number} topic_id
 * @apiName GetShowTopic
 * @apiGroup ShowTopics
 */
ShowTopicsController.get = function (req, res, next) {
  var curUserId;
  return req.paramsValidator
    .param('topic_id', {type: Number})
    .validate()
    .then(function() {
      var topicId = req.params.topic_id;
      curUserId = req.params.userid;
      return ShowTopics.checkRecordById(topicId, "找不到该炫耀贴");
    })
    .then(function (showTopic) {
      return ShowTopics.getWithTags(showTopic);
    })
    .then(function (showTopic) {
      var userId = showTopic.UserId;
      return Users.getBasicUserInfo(userId)
        .then(function (user) {
          return Contacts.getUserWithFollowType(curUserId, user, 'FollowType');
        }).then(function (user) {
          require('../../common/data').md.setAvatarView(user, curUserId);
          _.assign(showTopic, {User: user, Duration: Date.now() - showTopic.CreateTime});
          return showTopic;
        })
    })
    .then(function (showTopic) {
      return ShowTopics.getComments(showTopic.ID)
        .then(function (comments) {
          return _.assign(showTopic, {Comments: comments})
        })
    })
    .then(function (showTopic) {
      var userIds = util.csvStrToArray(showTopic.ZanList);
      showTopic.Comments.forEach(function (c) {
        userIds.push(c.UserId);
        userIds.push(c.ReplyId);
      });
      return Users.getUsersByIds(userIds, ['ID', 'NickName', 'Avatar', 'AvaAuthStatus'])
        .map(function (user) {
          require('../../common/data').md.setAvatarView(user, curUserId);
          return user;
        })
        .then(function (users) {
          showTopic.ZanList = util.csvStrToArray(showTopic.ZanList);
          showTopic.ImgList = require('../../common/data').md.setImgListView(showTopic, curUserId);
          util.embeddedOn(showTopic,  users, 'ZanList', 'ID', 'ZanUsers');
          util.embeddedOn(showTopic.Comments,  users, 'UserId', 'ID', 'UserInfo');
          util.embeddedOn(showTopic.Comments,  users, 'ReplyId', 'ID', 'ReplyUserInfo');
          return showTopic;
        })
    })
    .then(showTopic => {
        // 获取真人秀状态
        return RealityShows.fillUsersWithRealityShow([{ID: showTopic.UserId}])
            .then(realityShowData => {
                if(_.isArray(realityShowData) && !_.isEmpty(realityShowData)) {
                    showTopic.RealityShow = realityShowData[0].RealityShow;
                }
                return showTopic;
            });
    })
    .then(function (showTopic) {
      return ShowSubjects.findById(showTopic.SubjectId, ['ID', 'Title', 'Content', 'ImgUrl', 'CreateTime'])
        .then(function (showSubject) {
          return ShowSubjects.getSubjectWithHot(showSubject);
        })
        .then(function (showSubject) {
          return {
            showSubject: showSubject,
            showTopic: showTopic
          }
        });
    })
    .then(function(result) {
      res.succSend(result);
    }).catch(function(err) {
      errorHandler(err, req, res, next);
    });
};
