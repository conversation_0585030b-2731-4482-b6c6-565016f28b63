/**
 * Created by zhen<PERSON> on 2017/3/20.
 */

const _ = require('lodash')
const util = require('../../common/util')
const errorHandler = require('../errorHandler')
const cacheService = require('../../common/cacheService')
const toHttps = util.toHttps

const PublicController = {
  getPyqMomentsHot: function (req, res, next) {
    const HotMomentsCache = require('../../service/qnm/pyq/HotMomentsCache')
    const PyqMoments = require('../../models/PyqMoments')
    const QnmRoleInfo = require('../../models/QNMRoleInfos')
    const PyqData = require('../../common/data').pyq

    const formatMoments = moments => {
      return moments.map(m => {
        PyqData.setImgListView(m)
        _.forEach(m.ImgList, imgList => {
          imgList.pic = toHttps(imgList.pic)
          imgList.thumb = toHttps(imgList.thumb)
        })
        m.Avatar = toHttps(m.Avatar)
        m.Text = PyqMoments.richTextToHtml(m.Text)
        m = _.pick(m, ['ID', 'RoleId', 'RoleName', 'ServerName', 'Text', 'ImgList', 'LikeCount', 'CommentCount', 'CreateTime', 'Avatar'])
        return m
      })
    }

    const getAvatar = function getAvatar (roleInfo) {
      if (roleInfo.RoleId === 1005) {
        return 'https://hi-163-common.nosdn.127.net/upload/201802/09/0fb6c6300d8811e884463bfcdcaa8889'
      } else {
        return QnmRoleInfo.getJobAvatar(roleInfo) || 'http://res.hi.netease.com/pc/fab/20160803171644/img/avatarDefault_e466644.jpg'
      }
    }

    const fillColumns = function (moments, colsNotInCache, roleInfos) {
      const idToCols = util.keyToRecordHash(colsNotInCache, 'ID')
      const roleIdToInfos = util.keyToRecordHash(roleInfos, 'RoleId')
      return moments.map(m => {
        const record = idToCols[m.ID]
        const roleInfo = roleIdToInfos[m.RoleId] || {}
        const hotState = util.getJsonInfo(record.HotState)
        m.LikeCount = hotState.like || util.csvStrToArray(record.ZanList).length
        m.CommentCount = parseInt(hotState.comment, 10) + parseInt(hotState.reply, 10) || 0
        m.ImgAudit = record.ImgAudit
        m.Avatar = getAvatar(roleInfo)
        m.RoleName = roleInfo.RoleName
        m.ServerName = _.get(roleInfo, 'Server.name') || ''
        return m
      })
    }

    const getTop20Moments = function () {
      return HotMomentsCache.getMoments('all').then(moments => {
        const top20Moments = _.take(moments, 20)
        const ids = _.map(top20Moments, 'ID')
        const roleIds = _.map(top20Moments, 'RoleId')
        return [
          top20Moments,
          PyqMoments.findByIds(ids, ['ID', 'ZanList', 'HotState', 'ImgAudit']),
          QnmRoleInfo.findByRoleIds(roleIds)
        ]
      })
    }

    const getData = () => {
      return getTop20Moments()
        .spread((moments, colsNotInCache, roleInfos) => {
          return fillColumns(moments, colsNotInCache, roleInfos)
        }).then(formatMoments)
    }

    return cacheService.cacheInRedis(getData, {
      cacheKey: 'md:public:qnm:moments:hot',
      expire: 60 * 1000// 1 min
    })().then(moments => {
      res.succSend(moments)
    }).catch(err => {
      errorHandler(err, req, res, next)
    })
  }
}

module.exports = PublicController
