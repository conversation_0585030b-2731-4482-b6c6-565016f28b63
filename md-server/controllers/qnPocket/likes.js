/* eslint-disable prefer-promise-reject-errors */
/**
 * Created by zhen<PERSON> on 2017/6/30.
 */

const errorHandler = require('./errorHandler')
const EventBus = require('../../eventBus')
const Events = EventBus.Events
const util = require('../../../common/util')
const co = util.co
const _ = require('lodash')

function createLike (params) {
  const MdMoments = require('../../../models/Moments')
  const curUserId = parseInt(params.userid, 10)
  const momentId = params.moment_id

  return co(function * () {
    const m = yield MdMoments.findOne({ID: momentId, Status: 0}, ['UserId', 'ZanList'])
    if (!m) {
      return Promise.reject({msg: '心情不存在!'})
    }
    const likeUserIds = util.csvStrToIntArray(m.ZanList)
    if (_.includes(likeUserIds, curUserId)) {
      return Promise.reject({msg: '您已经赞过该心情!'})
    } else {
      likeUserIds.push(curUserId)
      const newZanListCsv = likeUserIds.join(',')
      const updateInfo = yield MdMoments.updateByCondition({ID: momentId}, {ZanList: newZanListCsv})
      EventBus.emit(Events.LIKE_MOMENT, {
        userId: curUserId,
        momentId: momentId,
        momentUserId: m.UserId
      })
      return {affectedRows: updateInfo.affectedRows}
    }
  })
}

function destroyLike (params) {
  const MdMoments = require('../../../models/Moments')
  const curUserId = parseInt(params.userid, 10)
  const momentId = params.moment_id

  return co(function * () {
    const m = yield MdMoments.findOne({ID: momentId, Status: 0}, ['UserId', 'ZanList'])
    if (!m) {
      return Promise.reject({msg: '心情不存在!'})
    }
    const likeUserIds = util.csvStrToIntArray(m.ZanList)
    if (!_.includes(likeUserIds, curUserId)) {
      return Promise.reject({msg: '您未点赞过该心情!'})
    } else {
      _.pull(likeUserIds, curUserId)
      const newZanListCsv = likeUserIds.join(',')
      const updateInfo = yield MdMoments.updateByCondition({ID: momentId}, {ZanList: newZanListCsv})
      EventBus.emit(Events.CANCEL_LIKE_MOMENT, {
        userId: curUserId,
        momentId: momentId,
        momentUserId: m.UserId
      })
      return {affectedRows: updateInfo.affectedRows}
    }
  })
}

exports.create = function (req, res, next) {
  return req.paramsValidator
  .param('moment_id', {type: Number})
  .validate()
  .then(() => {
    const params = req.params
    return createLike(params)
  }).then(data => {
    res.succSend(data)
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}

exports.destroy = function (req, res, next) {
  return req.paramsValidator
  .param('moment_id', {type: Number})
  .validate()
  .then(() => {
    const params = req.params
    return destroyLike(params)
  }).then(data => {
    res.succSend(data)
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}
