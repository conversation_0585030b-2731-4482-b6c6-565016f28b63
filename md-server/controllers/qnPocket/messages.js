/* eslint-disable prefer-promise-reject-errors */
/**
 * Created by zhen<PERSON> on 2017/7/6.
 */

const config = require('../../../common/config')
const util = require('../../../common/util')
const co = util.co
const commentTextLimit = config.TETXLIMIT
const errorHandler = require('./errorHandler')
const Promise = require('bluebird')
const _ = require('lodash')
const EventBus = require('../../eventBus')
const Events = EventBus.Events
const Answers = require('../../../models/Answers')

function getIncludeResourceWithMessages (messages) {
  const UsersService = require('../../services/qnPocket/users')
  return co(function * () {
    const messageIds = _.map(messages, 'ID')
    let answers = yield Promise.map(messageIds, mId => {
      return Answers.getRecentAnswers(mId, {
        cols: ['ID', 'UserId', 'TargetId as MessageId', 'ReplyId as ReplyUserId', 'Text', 'CreateTime'],
        limit: 10
      })
    }, {concurrency: 5})
    answers = _.flatten(answers)
    const userIds = _.uniq(_.compact(_.concat(
      _.map(messages, 'UserId'),
      _.map(answers, 'UserId'),
      _.map(answers, 'ReplyUserId')
    )))
    const users = yield UsersService.getShowUsers(userIds)
    const commentCounts = yield Answers.getCommentsCountBatch(messageIds)

    const messageIdToAnswers = _.groupBy(answers, 'MessageId')
    _.forEach(messages, m => {
      m.comments = _.map(messageIdToAnswers[m.ID], a => _.omit(a, 'MessageId'))
      const commentsCount = _.get(_.find(commentCounts, {TargetId: m.ID}), 'Count') || 0
      m.commentsCount = commentsCount
    })

    return {
      messages: messages,
      users: users
    }
  })
}

function listMessages (params) {
  const Messages = require('../../../models/Messages')
  const curUserid = params.userid
  const targetUserId = params.user_id || curUserid

  return co(function * () {
    const messages = yield Messages.getUserMessages(targetUserId, {
      cols: ['ID', 'UserId', 'Text', 'CreateTime'],
      sinceId: params.since_id,
      maxId: params.max_id,
      limit: params.count
    })
    return getIncludeResourceWithMessages(messages)
  })
}

function showMessage (params) {
  const Messages = require('../../../models/Messages')
  const messageId = params.message_id

  return Messages.findNormalById(messageId, ['ID', 'UserId', 'Text', 'CreateTime'])
  .then(message => {
    if (!message) {
      return Promise.reject({msg: '留言不存在!'})
    }
    return getIncludeResourceWithMessages([message])
  })
}

function createMessage (params) {
  const Messages = require('../../../models/Messages')
  const curUserId = params.userid
  const targetUserId = params.user_id
  const text = params.text
  if (curUserId === targetUserId) {
    return Promise.reject({errorType: 'ReplySelf', msg: '不能给自己留言'})
  }
  return Messages.insert({
    UserId: curUserId,
    TargetId: targetUserId,
    Text: text,
    CreateTime: Date.now()
  }).then(insertInfo => {
    const messageId = insertInfo.insertId
    EventBus.emit(Events.CREATE_MESSAGE, {
      userId: curUserId,
      messageBoardUserId: targetUserId,
      messageId: messageId
    })
    return {messageId: messageId}
  })
}

function destroyMessage (params) {
  const Messages = require('../../../models/Messages')
  const curUserid = params.userid
  const messageId = params.message_id

  return Messages.findNormalById(messageId, ['ID', 'UserId', 'TargetId'])
  .then(message => {
    if (!message) {
      return Promise.reject({msg: '留言不存在!'})
    } else {
      if (curUserid !== message.UserId && curUserid !== message.TargetId) {
        return Promise.reject({msg: '权限不足!'})
      }
    }
  }).then(() => {
    return Messages.softDeleteByCondition({ID: messageId})
  }).then(() => {
    EventBus.emit(Events.DELETE_MESSAGE, {messageId: messageId})
    return {msg: 'ok'}
  })
}

exports.list = function (req, res, next) {
  return req.paramsValidator
  .param('user_id', {required: false})
  .param('since_id', {required: false})
  .param('max_id', {required: false})
  .param('count', {type: Number, required: false, default: 10, max: 20})
  .validate()
  .then(() => {
    return listMessages(req.params)
  }).then(data => {
    res.succSend(data)
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}

exports.create = function (req, res, next) {
  return req.paramsValidator
  .param('user_id', {type: Number})
  .param('text', {type: String, maxlen: commentTextLimit})
  .validate()
  .then(() => {
    return createMessage(req.params)
  }).then(data => {
    res.succSend(data)
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}

exports.destroy = function (req, res, next) {
  return req.paramsValidator
  .param('message_id')
  .validate()
  .then(() => {
    return destroyMessage(req.params)
  }).then(data => {
    res.succSend(data)
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}

exports.show = function (req, res, next) {
  return req.paramsValidator
  .param('message_id')
  .validate()
  .then(() => {
    return showMessage(req.params)
  }).then(data => {
    res.succSend(data)
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}
