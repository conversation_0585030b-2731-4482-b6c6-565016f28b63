/**
 * Created by <PERSON><PERSON><PERSON> on 2017/6/27.
 */

const TestCfg = require('../../../common/config').testCfg
const logger = require('../../../common/logger')
const _ = require('lodash')

module.exports = function errorHandler(err, req, res) {
  if (TestCfg.debug) {
    console.log(err)
  }
  logger.error(JSON.stringify(err, Object.getOwnPropertyNames(err)))
  let errorCode = -1
  if (err.code && _.isNumber(err.code)) {
    errorCode = err.code
  }

  if (_.isPlainObject(err)) {
    let msg = err.msg
    // 参数验证器抛出的错误
    if (err.errorType && err.param) {
      msg = '参数错误'
    }
    res.send({ code: errorCode, msg: msg })
  } else {
    res.send({ code: errorCode, msg: '请求失败' })
  }
}