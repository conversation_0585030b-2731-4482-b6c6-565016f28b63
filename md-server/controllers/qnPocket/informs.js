const errorHandler = require('./errorHandler')
const co = require('../../../common/util').co
const MdInforms = require('../../../models/Notifications')
const userService = require('../../services/qnPocket/users')
const _ = require('lodash')
const util = require('../../../common/util')
const Promise = require('bluebird')
const InformStatus = MdInforms.Statuses

const EventTypes = MdInforms.EVENT_TYPES
const eventTypesHash = {}
eventTypesHash.leave_message = ['LEAVE_MESSAGE', 'REPLY_LEAVE_MESSAGE'].map(x => EventTypes[x])
eventTypesHash.moment = ['LIKE_MOMENT', 'COMMENT_MOMENT', 'REPLY_MOMENT'].map(x => EventTypes[x])

function getEventTypes (type) {
  return eventTypesHash[type]
}

function findRefer (inform, refers) {
  const refer = _.find(refers[inform.RelatedTable], {ID: inform.RelateId}) || {}
  if (inform.ActionType === 'COMMENT_SHOW_TOPIC' || inform.ActionType === 'REPLY_SHOW_TOPIC') {
    refer.ShowTopic = _.find(refers['md_show_topic'], {ID: refer.TargetId})
  }
  if (inform.ActionType === 'COMMENT_MOMENT' || inform.ActionType === 'REPLY_MOMENT') {
    refer.Moment = _.find(refers['md_moment'], {ID: refer.TargetId})
  }
  if (inform.ActionType === 'COMMENT_PHOTO' || inform.ActionType === 'REPLY_PHOTO') {
    refer.Photo = _.find(refers['md_photo'], {ID: refer.TargetId})
  }
  return refer
}

function formatInforms (informs, refers, users) {
  _.forEach(refers.md_moment, m => {
    const imgAudit = util.csvStrToIntArray(m.ImgAudit)
    const urls = util.csvStrToArray(m.ImgList)
    m.ImgList = _.map(urls, (url, index) => {
      return {url: url, auditStatus: imgAudit[index] || 0}
    })
    delete m.ImgAudit
  })
  _.forEach(informs, inform => {
    const relatedInfo = MdInforms.getRelatedInfoByType(inform.Type)
    inform.RelatedTable = relatedInfo.RelatedTable
    inform.ActionType = relatedInfo.Action
    inform.refer = findRefer(inform, refers)
    delete inform.RelatedTable
  })
  return {
    informs: informs,
    users: users
  }
}

function markReadInformsByIds (ids) {
  return MdInforms.updateByCondition({id: ids, Status: InformStatus.UNREAD}, {Status: InformStatus.READ})
}

function listInforms (params) {
  const curUserId = params.userid
  return co(function * () {
    let query = MdInforms.normalScope()
    .select(['ID', 'UserId', 'Type', 'TargetId', 'RelateId', 'CreateTime'])
    .where('TargetId', curUserId)
    .whereIn('Type', getEventTypes(params.group))
    .orderBy('ID', 'desc')
    .limit(params.count)
    if (params.since_id) {
      query = query.where('ID', '>', params.since_id)
    }
    if (params.max_id) {
      query = query.where('ID', '<', params.max_id)
    }
    const informs = yield MdInforms.executeByQuery(query)
    const refers = yield MdInforms.getRefers(informs)
    const userIds = _.map(informs, x => x['UserId'])
    const users = yield userService.getShowUsers(userIds)
    const informIds = _.map(informs, x => x['ID'])
    markReadInformsByIds(informIds)
    return formatInforms(informs, refers, users)
  })
}

function countInforms (params) {
  return Promise.map(['moment', 'leave_message'], group => {
    const curUserId = params.userid
    const query = MdInforms.normalScope()
    .count('ID as count')
    .where('TargetId', curUserId)
    .whereIn('Type', getEventTypes(group))
    return MdInforms.executeByQuery(query).then(r => {
      return r[0].count
    })
  }).spread((mCount, lmCount) => {
    return {
      moment: mCount,
      leave_message: lmCount
    }
  })
}

function markReadInforms (params) {
  const curUserId = params.userid
  const group = params.group
  const UnReadStatus = InformStatus.UNREAD
  const ReadStatus = InformStatus.READ
  return MdInforms.updateByCondition({TargetId: curUserId, Type: getEventTypes(group), Status: UnReadStatus},
    {Status: ReadStatus})
}

exports.list = function (req, res, next) {
  let params
  return req.paramsValidator
  .param('since_id', {required: false})
  .param('max_id', {required: false})
  .param('group', {values: ['moment', 'leave_message']})
  .param('count', {type: Number, required: false, default: 10, max: 20})
  .validate()
  .then(() => {
    params = req.params
    return listInforms(params)
  }).then(data => {
    res.succSend(data)
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}

exports.count = function (req, res, next) {
  return countInforms(req.params)
  .then(data => {
    res.succSend(data)
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}

exports.markRead = function (req, res, next) {
  let params
  return req.paramsValidator
  .param('group', {values: ['moment', 'leave_message']})
  .validate()
  .then(() => {
    params = req.params
    return markReadInforms(params)
  }).then(() => {
    res.succSend(null)
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}
