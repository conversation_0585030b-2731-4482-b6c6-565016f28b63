/* eslint-disable prefer-promise-reject-errors */
/**
 * Created by zhen<PERSON> on 2017/6/30.
 */

const config = require('../../../common/config')
const util = require('../../../common/util')
const co = util.co
const commentTextLimit = config.TETXLIMIT
const errorHandler = require('./errorHandler')
const _ = require('lodash')
const EventBus = require('../../eventBus')
const Events = EventBus.Events

function createComment (params) {
  const MdMoments = require('../../../models/Moments')
  const MdComments = require('../../../models/Comments')

  const momentId = params.moment_id
  const curUserId = params.userid
  return co(function * () {
    const moment = yield MdMoments.findOne({ID: momentId, Status: 0}, ['ID', 'UserId'])
    if (!moment) {
      return Promise.reject({msg: '评论的心情不存在'})
    }

    const insertInfo = yield MdComments.insert({
      UserId: curUserId,
      TargetId: momentId,
      Text: params.text,
      CreateTime: Date.now()
    })
    const commentId = insertInfo.insertId
    EventBus.emit(Events.CREATE_MOMENT_COMMENT, {
      userId: curUserId,
      momentId: momentId,
      momentUserId: moment.UserId,
      commentId: commentId
    })
    return {id: commentId}
  })
}

function replyComment (params) {
  const MdMoments = require('../../../models/Moments')
  const MdComments = require('../../../models/Comments')
  const commentId = params.comment_id
  const curUserId = params.userid
  let momentId

  return co(function * () {
    const replyComment = yield MdComments.findOne({ID: commentId, Status: 0}, ['UserId', 'TargetId'])
    const moment = yield MdMoments.findOne({ID: replyComment.TargetId, Status: 0}, ['ID', 'UserId'])
    momentId = moment.ID
    if (!replyComment) {
      return Promise.reject({msg: '回复的评论不存在!'})
    }
    if (curUserId === replyComment.UserId) {
      return Promise.reject({errorType: 'ReplySelf', msg: '不能回复自己!'})
    }

    const insertInfo = yield MdComments.insert({
      UserId: curUserId,
      ReplyId: replyComment.UserId,
      TargetId: replyComment.TargetId,
      Text: params.text,
      CreateTime: Date.now()
    })

    const insertCommentId = insertInfo.insertId
    EventBus.emit(Events.REPLY_MOMENT_COMMENT, {
      userId: curUserId,
      momentId: momentId,
      replyUserId: replyComment.UserId,
      momentUserId: moment.UserId,
      commentId: insertCommentId
    })
    return {id: insertCommentId}
  })
}

function destroyComment (params) {
  const MdComments = require('../../../models/Comments')
  const MdMoments = require('../../../models/Moments')
  const curUserId = params.userid
  const commentId = params.comment_id
  let momentId
  let replyId

  return MdComments.findOne({ID: commentId, Status: 0}, ['UserId', 'TargetId', 'ReplyId'])
  .then(c => {
    if (!c) {
      return Promise.reject({errorType: 'EntityNotFound', msg: '找不到该评论'})
    }
    replyId = c.ReplyId
    momentId = c.TargetId
    if (c.UserId !== curUserId) {
      return MdMoments.findById(c.TargetId, ['ID', 'UserId']).then(m => {
        if (m.UserId !== curUserId) {
          return Promise.reject({errorType: 'PermissionDenied', msg: '权限不足'})
        }
      })
    }
  }).then(() => {
    return MdComments.softDeleteByCondition({ID: commentId})
  }).then(updateInfo => {
    EventBus.emit(Events.DELETE_MOMENT_COMMENT, {commentId: commentId, momentId: momentId, replyId: replyId})
    return updateInfo
  })
}

exports.create = function (req, res, next) {
  return req.paramsValidator
  .param('moment_id')
  .param('text', {type: String, maxlen: commentTextLimit})
  .validate()
  .then(() => {
    return createComment(req.params)
  }).then(data => {
    res.succSend(data)
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}

exports.reply = function (req, res, next) {
  return req.paramsValidator
  .param('comment_id')
  .param('text', {type: String, maxlen: commentTextLimit})
  .validate()
  .then(() => {
    return replyComment(req.params)
  }).then(data => {
    res.succSend(data)
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}

exports.destroy = function (req, res, next) {
  return req.paramsValidator
  .param('comment_id')
  .validate()
  .then(() => {
    return destroyComment(req.params)
  }).then(() => {
    res.succSend(null)
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}
