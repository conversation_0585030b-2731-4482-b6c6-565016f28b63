const errorHandler = require('./errorHandler')
const MdReports = require('../../../models/MdReports')
const Service = require('../../services/reports')

function createReport (req, res, next) {
  return req.paramsValidator
  .param('resource_type', {values: ['moment', 'message', 'photo']})
  .param('resource_id', {type: Number})
  .param('reason_type', {type: Number, values: MdReports.ReasonTypes})
  .param('desc', {required: false})
  .validate()
  .then(() => {
    let params = req.params
    params.source = MdReports.Sources.QnPocket
    return Service.createReport(params)
  }).then(data => {
    res.succSend(data)
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}

module.exports = {
  create: createReport
}
