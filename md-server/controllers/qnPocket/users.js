/**
 * Created by <PERSON>hen<PERSON> on 2017/7/7.
 */
const errorHandler = require('./errorHandler')
const MdUsers = require('../../../models/Users')
const Constants = require('../../../common/data').Constants

exports.show = function (req, res, next) {
  return req.paramsValidator
  .param('user_id', {type: Number, required: false})
  .validate()
  .then(() => {
    const curUserId = req.params.userid
    const viewUserId = req.params.user_id || curUserId
    const qnUserService = require('../../services/qnPocket/users')
    return qnUserService.getUserDetail(viewUserId)
  }).then((data) => {
    res.succSend(data)
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}

exports.updateAvatar = function (req, res, next) {
  return req.paramsValidator
  .param('avatar', {textType: 'url'})
  .validate()
  .then(() => {
    const curUserId = req.params.userid
    const updateProps = {Avatar: req.params.avatar, AvaAuthStatus: Constants.STATUS_AUDIT_INIT}
    return MdUsers.updateByCondition({ID: curUserId}, updateProps)
  }).then(() => {
    res.succSend({msg: 'ok'})
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}

exports.updateLocation = function (req, res, next) {
  return req.paramsValidator
  .param('province')
  .param('city')
  .validate()
  .then(() => {
    const curUserId = req.params.userid
    return MdUsers.updateByCondition({ID: curUserId}, {Province: req.params.province, City: req.params.city})
  }).then(() => {
    res.succSend({msg: 'ok'})
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}
