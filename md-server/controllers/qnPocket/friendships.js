/* eslint-disable prefer-promise-reject-errors */
/**
 * Created by <PERSON><PERSON><PERSON> on 2017/7/4.
 */
const errorHandler = require('./errorHandler')
const Promise = require('bluebird')
const UsesService = require('./../../services/qnPocket/users')
const EventBus = require('../../eventBus')
const Events = EventBus.Events

function getFriends (userId) {
  const MDContacts = require('../../../models/Contacts')
  return MDContacts.getUserFriendIds(userId)
  .then(uIds => {
    return UsesService.getShowUsers(uIds)
  })
}

function getFollowers (userId) {
  const MDContacts = require('../../../models/Contacts')
  return MDContacts.getUserFollowerIds(userId)
  .then(uIds => {
    return UsesService.getShowUsers(uIds)
  })
}

function checkFollowUserExist (userId) {
  const Users = require('../../../models/Users')
  return Users.findNormalById(userId).then(user => {
    if (user) {
      return user
    } else {
      return Promise.reject({errorType: 'FollowUserNotExist', msg: '关注的用户不存在'})
    }
  })
}

function followUser (curUserId, targetUserId) {
  const MDContacts = require('../../../models/Contacts')
  return checkFollowUserExist(targetUserId)
  .then(() => {
    return MDContacts.findOne({UserId: curUserId, TargetId: targetUserId, Status: 0})
  }).then(relation => {
    if (relation) {
      return Promise.reject({errorType: 'AlreadyFollowed', msg: '已关注！'})
    } else {
      return MDContacts.createOrUpdate({UserId: curUserId, TargetId: targetUserId}, {Status: 0})
    }
  }).then(() => {
    EventBus.emit(Events.FOLLOW_USER, {userId: curUserId, followUserId: targetUserId})
    return {msg: 'ok'}
  })
}

function cancelFollowUser (curUserId, targetUserId) {
  const MDContacts = require('../../../models/Contacts')
  return MDContacts.findOne({UserId: curUserId, TargetId: targetUserId, Status: 0})
  .then(relation => {
    if (!relation) {
      return Promise.reject({msg: '未关注!'})
    } else {
      return MDContacts.softDeleteByCondition({UserId: curUserId, TargetId: targetUserId})
    }
  }).then(() => {
    EventBus.emit(Events.CANCEL_FOLLOW_USER, {userId: curUserId, followUserId: targetUserId})
    return {msg: 'ok'}
  })
}

exports.friends = function friends (req, res, next) {
  return getFriends(req.params.userid)
  .then(data => {
    res.succSend(data)
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}

exports.followers = function followers (req, res, next) {
  return getFollowers(req.params.userid)
  .then(data => {
    res.succSend(data)
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}

exports.create = function friends (req, res, next) {
  return req.paramsValidator
  .param('user_id', {type: Number})
  .validate()
  .then(() => {
    const curUserId = req.params.userid
    const targetUserId = req.params.user_id
    return followUser(curUserId, targetUserId)
  }).then(data => {
    res.succSend(data)
  })
  .catch(err => {
    errorHandler(err, req, res, next)
  })
}

exports.destroy = function friends (req, res, next) {
  return req.paramsValidator
  .param('user_id', {type: Number})
  .validate()
  .then(() => {
    const curUserId = req.params.userid
    const targetUserId = req.params.user_id
    return cancelFollowUser(curUserId, targetUserId)
  }).then(data => {
    res.succSend(data)
  })
  .catch(err => {
    errorHandler(err, req, res, next)
  })
}
