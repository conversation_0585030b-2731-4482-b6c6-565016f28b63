/* eslint-disable prefer-promise-reject-errors */
/**
 * Created by zhenhua on 2017/6/26.
 */

const config = require('../../../common/config')
const util = require('../../../common/util')
const nos = require('../../../common/nos')
const momentTextLimit = config.TETXLIMIT
const errorHandler = require('./errorHandler')
const _ = require('lodash')
const Promise = require('bluebird')
const co = require('../../../common/util').co
const MdMoments = require('../../../models/Moments')
const MdComments = require('../../../models/Comments')
const MdTopic = require('../../../models/MdTopic')
const MdUsers = require('../../../models/Users')
const BindRoles = require('../../../models/BindRoles')
const Contacts = require('../../../models/Contacts')
const QnRoleFriends = require('../../../models/QNRoleFriends')
const QnRoleInfos = require('../../../models/QNRoleInfos')
const Games = require('../../../models/Games')
const UsersService = require('../../services/qnPocket/users')
const HotMomentsService = require('../../services/HotMoments')
const EventBus = require('../../eventBus')
const Events = EventBus.Events
const AuditService = require('../../../common/audit')

const commentSize = 10
const likeSize = 10

function createMoment (params) {
  const curUserId = params.userid
  const imgListArr = params.imglist
  const isAllImgValidNosUrl = _.every(imgListArr, url => !!nos.validateUrl(url))
  if (!isAllImgValidNosUrl) {
    return Promise.reject({errorType: 'ParamsInvalid', msg: 'imgList contains invalid nos url'})
  }
  const imgListCsv = _.join(imgListArr, ',')
  const text = params.text || ''
  return MdMoments.insert({
    UserId: params.userid,
    Text: text,
    ImgList: imgListCsv,
    GameId: Games.GameIds.QN,
    isTop: 0,
    CreateTime: Date.now()
  }).then(insertInfo => {
    const momentId = insertInfo.insertId
    if (text) {
      handleMomentTopic(text, momentId, params.userid)
    }
    if (!_.isEmpty(imgListArr)) {
      AuditService.sendPic('md', imgListArr, {        // 提交图片审核
        roleId: curUserId,
        picId: 'moment_md:' + momentId
      })
    }
    return momentId
  })
}

function handleMomentTopic (text, momentId, userId) {
  const topic = MdMoments.extractTopic(text)
  if (topic) {
    return MdTopic.addMomentToTopic(topic, momentId, userId)
    .then(topicId => {
      EventBus.emit(Events.ADD_MOMENT_TO_TOPIC, {
        topicId: topicId,
        topic: topic,
        momentId: momentId,
        text: text,
        userId: userId
      })
    })
  }
}

function getMomentsStatistic (moments) {
  const momentIds = _.map(moments, 'ID')
  return co(function * () {
    const commentCounts = yield MdComments.getCommentsCountBatch(momentIds)

    return _.map(moments, m => {
      const commentsCount = _.get(_.find(commentCounts, {TargetId: m.ID}), 'Count') || 0
      const likesCount = util.csvStrToIntArray(m.ZanList).length
      return {momentId: m.ID, commentsCount: commentsCount, likesCount: likesCount}
    })
  })
}

function getMomentsLikes (moments) {
  return _.map(moments, m => {
    const likeUserIds = _.takeRight(util.csvStrToIntArray(m.ZanList), likeSize)
    return {
      momentId: m.ID,
      likeUserIds: likeUserIds
    }
  })
}

function getIncludeResourceWithMoments (moments, options) {
  options = _.defaults(options, {userIds: []})
  const momentIds = _.map(moments, 'ID')
  return co(function * () {
    const comments = yield MdComments.getLastedCommentsBatch(momentIds, {
      cols: ['ID', 'UserId', 'ReplyId as ReplyUserId', 'TargetId', 'Text', 'CreateTime'],
      limit: commentSize
    })
    const statistic = yield getMomentsStatistic(moments)
    const momentLikes = getMomentsLikes(moments)
    const userIds = _.uniq(_.compact(_.concat(
      options.userIds,
      _.map(moments, 'UserId'),
      _.map(comments, 'UserId'),
      _.map(comments, 'ReplyUserId'),
      _.flatMap(momentLikes, 'likeUserIds')
    )))

    const users = yield UsersService.getShowUsers(userIds, {includeLocation: true})

    return {
      moments: moments,
      comments: comments,
      statistic: statistic,
      momentLikes: momentLikes,
      users: users
    }
  })
}

function formatMomentsStream (resources) {
  const mIdToComments = _.groupBy(resources.comments, 'TargetId')
  const mIdToStatistic = util.keyToRecordHash(resources.statistic, 'momentId')
  const mIdToLikes = util.keyToRecordHash(resources.momentLikes, 'momentId')

  const result = _.map(resources.moments, m => {
    const mId = m.ID
    const item = _.omit(m, ['ZanList', 'ImgAudit'])
    const urls = util.csvStrToArray(m.ImgList)
    const imgAudit = util.csvStrToIntArray(m.ImgAudit)
    item.ImgList = _.map(urls, (url, index) => {
      return {url: url, auditStatus: imgAudit[index] || 0}
    })
    const commments = _.map(mIdToComments[mId], c => _.omit(c, 'TargetId')) || []
    item.comments = commments
    item.commentsCount = _.get(mIdToStatistic[mId], 'commentsCount') || 0
    item.likesCount = _.get(mIdToStatistic[mId], 'likesCount') || 0
    item.likeUesrIds = _.get(mIdToLikes[mId], 'likeUserIds')

    return item
  })

  return {
    moments: result,
    users: resources.users
  }
}

function showMoment (params) {
  const momentId = params.moment_id

  return co(function * () {
    const moment = yield MdMoments.findOne({ID: momentId, Status: 0},
      ['ID', 'UserId', 'Text', 'ImgList', 'ImgAudit', 'CreateTime', 'ZanList'])
    if (!moment) {
      return Promise.reject({msg: '心情不存在'})
    }

    const resources = yield getIncludeResourceWithMoments([moment])
    return formatMomentsStream(resources)
  })
}

function getMomentsBaseQuery () {
  return MdMoments.normalScope()
  .where('GameId', Games.GameIds.QN)
  .select(['ID', 'UserId', 'Text', 'ImgList', 'ImgAudit', 'CreateTime', 'ZanList'])
  .orderBy('ID', 'desc')
}

function userTimeLineMoments (params) {
  const recentSize = 8
  const curUserId = params.userid
  const uId = params.user_id || curUserId
  return co(function * () {
    let query = getMomentsBaseQuery()
    .where('UserId', uId)
    .limit(params.count)
    if (params.since_id) {
      query = query.where('ID', '>', params.since_id)
    }
    if (params.max_id) {
      query = query.where('ID', '<', params.max_id)
    }
    const moments = yield MdMoments.executeByQuery(query)
    let userIds = yield MdUsers.getRecentVisitorIds(uId)
    const recentVisitorIds = _.compact(_.slice(userIds, 0, recentSize))
    const info = yield getIncludeResourceWithMoments(moments, {userIds: recentVisitorIds})
    const result = formatMomentsStream(info)
    MdUsers.visit(curUserId, uId)
    result.recentVisitorIds = recentVisitorIds
    result.curUserInfo = yield UsersService.getUserDetail(uId)
    return result
  })
}

function getFriendUserIds (userId) {
  return co(function * () {
    const roleId = yield BindRoles.getUserQnMainRoleId(userId)
    let qnFriendsUserIds = []
    if (roleId) {
      const friendRoleIds = yield QnRoleFriends.getFriendsRoleInfoIds([roleId])
      qnFriendsUserIds = yield BindRoles.getUserIdByQnMainRoleIds(friendRoleIds)
    }
    const mdFriendUserIds = yield Contacts.getUserFriendIds(userId)
    const friendUserIds = _.union(qnFriendsUserIds, mdFriendUserIds)
    return friendUserIds
  })
}

function homeTimeLineMoments (params) {
  const curUserId = params.userid
  return co(function * () {
    const friendIds = yield getFriendUserIds(curUserId)
    const meAndFriendUserIds = _.concat(curUserId, friendIds)
    let query = getMomentsBaseQuery()
    .whereIn('UserId', meAndFriendUserIds)
    .limit(params.count)
    if (params.since_id) {
      query = query.where('ID', '>', params.since_id)
    }
    if (params.max_id) {
      query = query.where('ID', '<', params.max_id)
    }
    const moments = yield MdMoments.executeByQuery(query)
    const momentsWithRelated = yield getIncludeResourceWithMoments(moments)
    const result = formatMomentsStream(momentsWithRelated)
    const curUserInfo = yield UsersService.getUserDetail(curUserId)
    result.curUserInfo = curUserInfo
    return result
  })
}

function getQnHotMoments (params) {
  return HotMomentsService.getHotMoments('qn')
  .then(moments => {
    const start = params.since_rank - 1
    _.forEach(moments, (m, index) => {
      m.rank = index + 1
    })
    moments = _.slice(moments, start, start + params.count)
    return moments
  })
  .then(getIncludeResourceWithMoments)
  .then(formatMomentsStream)
}

function destroyMoment (params) {
  const curUserId = params.userid
  const momentId = params.moment_id
  return MdMoments.findOne({ID: momentId, Status: 0}, ['ID', 'UserId'])
  .then(record => {
    if (!record) {
      return Promise.reject({errorType: 'EntityNotFound', msg: '找不到该心情'})
    }
    if (record.UserId !== curUserId) {
      return Promise.reject({errorType: 'PermissionDenied', msg: '权限不足'})
    }
  }).then(() => {
    return MdMoments.softDeleteByCondition({ID: momentId})
  }).then(result => {
    EventBus.emit(Events.DELETE_MOMENT, {momentId: momentId})
    return result
  })
}

exports.create = function create (req, res, next) {
  let params
  return req.paramsValidator
  .param('text', {type: String, maxlen: momentTextLimit, required: false})
  .param('imglist', {type: Array, allowCsvArray: true, required: false, maxSize: 9})
  .requiredAny(['text', 'imglist'])
  .validate()
  .then(() => {
    params = req.params
    return createMoment(params)
  }).then(id => {
    res.succSend({id: id})
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}

exports.show = function show (req, res, next) {
  let params
  return req.paramsValidator
  .param('moment_id', {type: Number})
  .validate()
  .then(() => {
    params = req.params
    return showMoment(params)
  }).then(data => {
    res.succSend(data)
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}

exports.destroy = function destroy (req, res, next) {
  let params
  return req.paramsValidator
  .param('moment_id', {type: Number})
  .validate()
  .then(() => {
    params = req.params
    return destroyMoment(params)
  }).then(() => {
    res.succSend(null)
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}

exports.userTimeLine = function userTimeLine (req, res, next) {
  let params
  return req.paramsValidator
  .param('user_id', {required: false})
  .param('since_id', {required: false})
  .param('max_id', {required: false})
  .param('count', {type: Number, required: false, default: 10, max: 20})
  .validate()
  .then(() => {
    params = req.params
    return userTimeLineMoments(params)
  }).then(data => {
    res.succSend(data)
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}

exports.homeTimeLine = function homeTimeLine (req, res, next) {
  let params
  return req.paramsValidator
  .param('since_id', {required: false})
  .param('max_id', {required: false})
  .param('count', {type: Number, required: false, default: 10, max: 20})
  .validate()
  .then(() => {
    params = req.params
    return homeTimeLineMoments(params)
  }).then(data => {
    res.succSend(data)
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}

exports.hot = function hot (req, res, next) {
  return req.paramsValidator
  .param('since_rank', {type: Number, required: false, default: 1, min: 1, max: 200})
  .param('count', {type: Number, required: false, default: 10, max: 20})
  .validate()
  .then(() => {
    return getQnHotMoments(req.params)
  }).then(data => {
    res.succSend(data)
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}

exports.dreamAngelMoments = function (params) {
  return co(function * () {
    const cols = ['ID', 'UserId', 'Text', 'ImgList', 'ImgAudit', 'CreateTime', 'ZanList']
    const topic = yield MdTopic.findOne({Subject: '2017幽梦外传'}, ['ID'])
    if (!topic) {
      return []
    }
    let query = MdMoments.normalScope()
    .from('md_moment as m')
    .select(cols.map(c => 'm.' + c))
    .innerJoin('md_topic_moment as t', 't.MomentId', 'm.ID')
    .where('t.TopicId', '=', topic.ID)
    .orderBy('ID', 'desc')
    .limit(params.count)
    if (params.since_id) {
      query = query.where('m.ID', '>', params.since_id)
    }
    if (params.max_id) {
      query = query.where('m.ID', '<', params.max_id)
    }
    const moments = yield MdMoments.executeByQuery(query)
    for (let i = 0; i < moments.length; i++) {
      const m = moments[i]
      const match = m.Text.match(/##2017幽梦外传####幽梦天使(\d+)##/)
      if (match) {
        m.angelRoleId = match[1]
        const result = yield QnRoleInfos.findByRoleId(m.angelRoleId)
        const roleInfo = {
          roleId: result.RoleId,
          roleName: result.RoleName,
          serverId: result.ServerId,
          serverName: _.get(result, 'Server.name'),
          serverGroup: _.get(result, 'Server.group'),
          gender: result.Gender,
          clazz: result.JobId
        }
        m.angelRoleInfo = roleInfo
      }
    }
    const resources = yield getIncludeResourceWithMoments(moments)
    return formatMomentsStream(resources)
  })
}
