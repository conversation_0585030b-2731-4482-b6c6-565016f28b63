/* eslint-disable prefer-promise-reject-errors */
/**
 * Created by zhen<PERSON> on 2017/7/7.
 */

const config = require('../../../common/config')
const util = require('../../../common/util')
const co = util.co
const commentTextLimit = config.TETXLIMIT
const errorHandler = require('./errorHandler')
const Promise = require('bluebird')
const EventBus = require('../../eventBus')
const Events = EventBus.Events

function createAnswer (params) {
  const Messages = require('../../../models/Messages')
  const Answers = require('../../../models/Answers')
  const curUserId = params.userid
  const messageId = params.message_id
  const text = params.text
  let message

  return Messages.findNormalById(messageId, ['UserId', 'TargetId'])
  .then(m => {
    message = m
    if (!message) {
      return Promise.reject({msg: '留言不存在!'})
    } else {
      return Answers.insert({
        UserId: curUserId,
        TargetId: messageId,
        Text: text,
        CreateTime: Date.now()
      })
    }
  }).then((insertInfo) => {
    const answerId = insertInfo.insertId
    EventBus.emit(Events.CREATE_MESSAGE_ANSWER, {
      userId: curUserId,
      messageBoardUserId: message.TargetId,
      messageUserId: message.UserId,
      answerId: answerId
    })
    return {answerId: answerId}
  })
}

function replyAnswer (params) {
  const Answers = require('../../../models/Answers')
  const Messages = require('../../../models/Messages')
  const curUserId = params.userid
  const answerId = params.answer_id

  return co(function * () {
    const replyAnswer = yield Answers.findOne({ID: answerId, Status: 0}, ['UserId', 'TargetId'])
    if (!replyAnswer) {
      return Promise.reject({msg: '回复不存在!'})
    }
    if (curUserId === replyAnswer.UserId) {
      return Promise.reject({errorType: 'ReplySelf', msg: '不能回复自己!'})
    }
    const message = yield Messages.findOne({ID: replyAnswer.TargetId})

    const insertInfo = yield Answers.insert({
      UserId: curUserId,
      ReplyId: replyAnswer.UserId,
      TargetId: replyAnswer.TargetId,
      Text: params.text,
      CreateTime: Date.now()
    })
    const insertAnswerId = insertInfo.insertId
    EventBus.emit(Events.REPLY_MESSAGE_ANSWER, {
      userId: curUserId,
      replyUserId: replyAnswer.UserId,
      messageUserId: message.UserId,
      messageBoardUserId: message.TargetId,
      answerId: insertAnswerId
    })
    return {answerId: insertInfo.insertId}
  })
}

function destroyAnswer (params) {
  const Messages = require('../../../models/Messages')
  const Answers = require('../../../models/Answers')
  const curUserId = params.userid
  const answerId = params.answer_id
  return Answers.findNormalById(answerId, ['TargetId', 'UserId']).then(answer => {
    if (!answer) {
      return Promise.reject({msg: '回复不存在!'})
    } else {
      return Promise.all([
        answer,
        Messages.findById(answer.TargetId, ['TargetId'])
      ])
    }
  }).spread((answer, message) => {
    if (curUserId !== answer.UserId && curUserId !== message.TargetId) {
      return Promise.reject({msg: '权限不足'})
    } else {
      return Answers.softDeleteByCondition({Id: answerId})
    }
  }).then(() => {
    EventBus.emit(Events.DELETE_MESSAGE_ANSWER, {
      userId: curUserId,
      answerId: answerId
    })
    return {msg: 'ok'}
  })
}
exports.create = function (req, res, next) {
  return req.paramsValidator
  .param('message_id')
  .param('text', {type: String, maxlen: commentTextLimit})
  .validate()
  .then(() => {
    return createAnswer(req.params)
  }).then(data => {
    res.succSend(data)
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}

exports.reply = function (req, res, next) {
  return req.paramsValidator
  .param('answer_id', {type: Number})
  .param('text', {type: String, maxlen: commentTextLimit})
  .validate()
  .then(() => {
    return replyAnswer(req.params)
  }).then(data => {
    res.succSend(data)
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}

exports.destroy = function (req, res, next) {
  return req.paramsValidator
  .param('answer_id', {type: Number})
  .validate()
  .then(() => {
    return destroyAnswer(req.params)
  }).then(data => {
    res.succSend(data)
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}
