/**
 * Created by zhenhua on 2017/7/6.
 */
const util = require('../../../common/util')
const ipUtil = require('../../../common/ipUtil')
const config = require('../../../common/config')

function getIpLocationByUserId (userId) {
  const userIdToLocation = {
    '1753578': { country: '中国', province: '北京市', city: '丰台区' },
    '1753579': { country: '中国', province: '浙江省', city: '杭州市' }
  }
  const defaultIpLocation = { country: '中国', province: '浙江省', city: '杭州市' }
  return userIdToLocation[userId] || defaultIpLocation
}

exports.getLocationFromIp = function (req, res, next) {
  // 内网开关用户用于模拟用户ip获得地理位置
  if (config.testCfg.mock_qn_pocket_ip_location) {
    const location = getIpLocationByUserId(req.params.userid)
    res.succSend(location)
  } else {
    const ip = util.getIp(req)
    const location = ipUtil.getLocationFromIp(ip)
    res.succSend(location)
  }
}

exports.getNosToken = function (req, res, next) {
  const nosService = require('../../../common/nos')
  const token = nosService.getToken('qn', req.params)
  res.send(util.response(token))
}
