/* eslint-disable prefer-promise-reject-errors */
/**
 * Created by zhenhua on 2017/6/26.
 */

const Promise = require('bluebird')
const errorHandler = require('./errorHandler')
const MdUsers = require('../../../models/Users')
const BindRoles = require('../../../models/BindRoles')
const Games = require('../../../models/Games')
const QnRoleInfos = require('../../../models/QNRoleInfos')
const QnUsersService = require('../../services/qnPocket/users')
const AuthService = require('../../../common/auth')
const co = require('../../../common/util').co
const { testCfg } = require('../../../common/config')

const NO_LOGIN_CODE = -2

function validateUrsCookie (cookie) {
  // return { result: 1,
  //   ssn: 'xwangzhenhua',
  //   Uid: '',
  //   mobile: 'O13120991108',
  //   autoLogin: '0',
  //   createTime: 1467716166,
  //   cookieCreateTime: 1498732119,
  //   alias: '',
  //   misc: '0|123.58.191.68|10#0|0|000020|0|dream|' };
  const cookieUtil = require('../../../common/cookie')
  return cookieUtil.getUrsInfo(cookie)
}

function getUrsFromSsn (ssn) {
  return ssn.indexOf('@') >= 0 ? ssn : (ssn + '@163.com')
}

function getUrsFromCookie (cookies) {
  return new Promise((resolve, reject) => {
      return validateUrsCookie(cookies).then(info => {
          if (info.result < 0) {
              reject({code: NO_LOGIN_CODE, msg: 'URS登录校验失败!'})
          } else {
              const mailLogin = !!cookies['NTES_SESS']     // 判断邮箱登录还是手机登录
              const urs = mailLogin ? getUrsFromSsn(info.ssn) : info.alias
              resolve(urs)
          }
      }).catch(err => {
          console.log(err)
          reject({code: NO_LOGIN_CODE, msg: 'URS未登录'})
      })
  })
}

function checkAccountStatus (urs) {
  const result = {
    isEnableIsland: false,
    isBindMainRole: false
  }

  return co(function * () {
    const mdUsers = yield MdUsers.findOne({UserName: urs}, ['ID'])
    if (mdUsers) {
      result.isEnableIsland = true
    }
    if (mdUsers && mdUsers.ID) {
      const bindRole = yield BindRoles.getUserQnMainRoleId(mdUsers.ID)
      if (bindRole) {
        result.isBindMainRole = true
      }
    }
    return result
  })
}

const Services = {}
Services.getUrsFromCookie = getUrsFromCookie
Services.checkAccountStatus = checkAccountStatus

exports.Services = Services

function getUserFromUrs (urs, roleId) {
  const now = Date.now()
  return co(function * () {
    const user = yield MdUsers.findOne({UserName: urs}, ['ID', 'UserName'])
    if (user) {
      MdUsers.updateByCondition({ID: user.ID}, {LastLogin: now})
      return user
    } else {
      return yield MdUsers.create({UserName: urs, NickName: `qn${roleId}`, CreateTime: now, LastLogin: now })
    }
  })
}

function bindAsMainRoleForce (userId, roleId) {
  const mainRoleType = BindRoles.BindTypes.MAIN_ROLE_BIND
  const commonRoleType = BindRoles.BindTypes.NORMAL_BIND
  const qnGameId = Games.GameIds.QN
  const pk = {RoleId: roleId, GameId: qnGameId}
  return Promise.all([
    BindRoles.findOne(pk, ['RoleId', 'Type']),
    BindRoles.getUserQnMainRoleId(userId)
  ]).spread((checkBind, curMainRoleId) => {
    const tryToChangeCurMainRoleBind = () => {
      if (curMainRoleId) {
        return BindRoles.updateByCondition({RoleId: curMainRoleId, GameId: qnGameId}, {Type: commonRoleType})
      } else {
        return Promise.resolve(true)
      }
    }
    if (checkBind) {
      if (checkBind.UserId === userId && checkBind.Type === mainRoleType) {
        return true
      } else {
        return tryToChangeCurMainRoleBind().then(() => {
          return BindRoles.updateByCondition(pk, {UserId: userId, Type: mainRoleType})
        })
      }
    } else {
      return tryToChangeCurMainRoleBind().then(() => {
        return BindRoles.bindMainRole(userId, qnGameId, roleId)
      })
    }
  })
}

function checkUrsAndRoleId(urs, roleId) {
  return QnRoleInfos.findOne({ UserName: urs, RoleId: roleId })
    .then(record => {
      if (!record) {
        return Promise.reject({ errorType: 'UrsRoleIdNotMatch', msg: '该urs下找不到对应roleId的角色' })
      }
    })
}

function getLoginUrs(req) {
  if (testCfg.test_env) {
    return req.params.urs
  } else {
    return Services.getUrsFromCookie(req.cookies)
  }
}

exports.login = function (req, res, next) {
  let userInfo
  let skey
  let roleId
  let urs
  return req.paramsValidator
  .param('roleid', {type: Number})
  .validate()
  .then(() => {
    roleId = req.params.roleid
   return getLoginUrs(req)
  })
  .then(function (value) {
    urs = value
    return checkUrsAndRoleId(urs, roleId)
  })
  .then(() => {
    return getUserFromUrs(urs, roleId)
  })
  .then(user => {
    userInfo = user
    return bindAsMainRoleForce(user.ID, roleId)
  })
  .then(() => {
    const sessionData = {urs: userInfo.UserName, time: 'LONG_TERM', userid: userInfo.ID, roleid: roleId}
    return AuthService.start('pocket_user', {pocket_userid: userInfo.ID}, sessionData)
  })
  .then(data => {
    skey = data.skey
    return QnUsersService.getShowUsers([userInfo.ID])
  }).then(users => {
    const user = users[0]
    user.skey = skey
    res.succSend(user)
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}

exports.logout = function (req, res, next) {
  const curUserId = req.params.userid
  return AuthService.stop('pocket_user', {pocket_userid: curUserId})
  .then(() => {
    res.send({code: 0})
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}

exports.checkStatus = function (req, res, next) {
  return req.paramsValidator
  .param('urs')
  .validate()
  .then(() => {
    return Services.checkAccountStatus(req.params.urs)
  }).then((data) => {
    res.succSend(data)
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}

exports.NO_LOGIN_CODE = NO_LOGIN_CODE
