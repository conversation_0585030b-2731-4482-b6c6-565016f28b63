/**
 * Created by <PERSON><PERSON><PERSON> on 2017/7/7.
 */

const errorHandler = require('./errorHandler')
const momentComments = require('./momentComments')
const messageAnswers = require('./messageAnswers')
const util = require('../../../common/util')
const _ = require('lodash')
const co = util.co

/**
 * 这这个文件把心情的comments和留言的answers统一起来,  给外部api消费方提供统一的接口
 */

const ResourceTypes = {
  'moment': {commentTable: 'md_comment'},
  'message': {commentTable: 'md_answer'}
}

function listComments (params) {
  const UsersService = require('../../services/qnPocket/users')
  const ModelManager = require('../../../models/ModelManager')
  const resourceType = params.resource_type
  const tableName = ResourceTypes[resourceType].commentTable
  return co(function * () {
    const queryModel = ModelManager.getModelByTableName(tableName)
    let query = queryModel.scope()
    .select(['ID', 'UserId', 'ReplyId as ReplyUserId', 'Text', 'CreateTime'])
    .where('TargetId', params.resource_id)
    .orderBy('ID', 'desc')
    .limit(params.count)
    if (params.since_id) {
      query = query.where('ID', '>', params.since_id)
    }
    if (params.max_id) {
      query = query.where('ID', '<', params.max_id)
    }
    const comments = yield queryModel.executeByQuery(query)
    const userIds = _.flatMap(comments, c => [c.UserId, c.ReplyUserId])
    const users = yield UsersService.getShowUsers(userIds)

    return {
      comments: comments,
      users: users
    }
  })
}

exports.list = function (req, res, next) {
  return req.paramsValidator
  .param('resource_type', {values: ['moment', 'message']})
  .param('resource_id', {type: Number})
  .param('since_id', {type: Number, required: false})
  .param('max_id', {type: Number, required: false})
  .param('count', {type: Number, required: false, default: 10, max: 20})
  .validate()
  .then(() => {
    return listComments(req.params)
  })
  .then(data => {
    res.succSend(data)
  })
  .catch(err => {
    errorHandler(err, req, res, next)
  })
}

exports.create = function (req, res, next) {
  return req.paramsValidator
  .param('resource_type', {values: ['moment', 'message']})
  .param('resource_id')
  .validate()
  .then(() => {
    let params = req.params
    if (params['resource_type'] === 'moment') {
      params.moment_id = params.resource_id
      momentComments.create(req, res, next)
    } else {
      params.message_id = params.resource_id
      messageAnswers.create(req, res, next)
    }
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}

exports.reply = function (req, res, next) {
  return req.paramsValidator
  .param('resource_type', {values: ['moment', 'message']})
  .param('comment_id')
  .validate()
  .then(() => {
    let params = req.params
    if (params['resource_type'] === 'moment') {
      momentComments.reply(req, res, next)
    } else {
      params.answer_id = params.comment_id
      messageAnswers.reply(req, res, next)
    }
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}

exports.destroy = function (req, res, next) {
  return req.paramsValidator
  .param('resource_type', {values: ['moment', 'message']})
  .param('comment_id')
  .validate()
  .then(() => {
    let params = req.params
    if (params['resource_type'] === 'moment') {
      momentComments.destroy(req, res, next)
    } else {
      params.answer_id = params.comment_id
      messageAnswers.destroy(req, res, next)
    }
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}
