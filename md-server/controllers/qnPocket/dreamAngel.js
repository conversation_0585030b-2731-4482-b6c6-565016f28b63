const errorHandler = require('./errorHandler')
let dreamAngelService = require('../../services/qnPocket/dreamAngel')
const config = require('../../../common/config')
if (config.testCfg.dream_angel_fixture_data) {
  dreamAngelService = require('../../services/qnPocket/dreamAngelFixtureService')
}

function vote (req, res, next) {
  let params
  return req.paramsValidator
  .param('angel_id', {type: 'Number'})
  .validate()
  .then(() => {
    params = req.params
    return dreamAngelService.vote({angelId: params.angel_id, urs: params.urs})
  }).then(data => {
    res.send({code: 0, data: data.resCode, msg: data.msg})
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}

function listPlayers (req, res, next) {
  return req.paramsValidator
  .param('page', {required: false, default: 1})
  .param('server_id', {required: false, type: Number})
  .param('search', {required: false, type: String})
  .validate()
  .then(() => {
    let params = req.params
    return dreamAngelService.listPlayers({
      page: params.page,
      serverId: params.server_id,
      search: params.search
    })
  })
  .then(data => {
    res.succSend(data)
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}

function getPlayerRank (req, res, next) {
  return dreamAngelService.getRank()
  .then(data => {
    res.succSend(data)
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}

function listStories (req, res, next) {
  return req.paramsValidator
  .param('since_id', {required: false})
  .param('max_id', {required: false})
  .param('count', {type: Number, required: false, default: 10, max: 20})
  .validate()
  .then(function () {
    return dreamAngelService.listStories(req.params)
  })
  .then(data => {
    res.succSend(data)
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}

module.exports = {
  vote: vote,
  listPlayers: listPlayers,
  getPlayerRank: getPlayerRank,
  listStories: listStories,
  Service: dreamAngelService
}
