let Promise = require('bluebird');
let _ = require('lodash');
let errorHandler = require('../errorHandler');

let TongrenWorksModel = require('../../models/MdTongrenWorks');
let TongrenUserModel = require('../../models/MdTongrenUser');
let TongrenLikeModel = require('../../models/MdTongrenLike');
let TongrenCommentModel = require('../../models/MdTongrenComment');
let TongrenScoreRecordModel = require('../../models/MdTongrenScoreRecord');

let ContactsModel = require('../../models/Contacts');
let MomentsModel = require('../../models/Moments');
let RealityShowModel = require('../../models/RealityShows');
let GamesModel = require('../../models/Games');
let NotificationsModel = require('../../models/Notifications');

const NotificationTypes = NotificationsModel.EVENT_TYPES;
const WORK_TYPE = TongrenWorksModel.WORK_TYPE; // 作品类型
const WORK_STATUS = TongrenWorksModel.WORK_STATUS; // 作品状态
const WORK_TYPE_MAP = TongrenWorksModel.WORK_TYPE_MAP;
const WORK_TOP_STATUS = TongrenWorksModel.WORK_TOP_STATUS; // 作品置顶状态
const WORK_CONFIG = TongrenWorksModel.WORK_CONFIG; // 作品展示设置
const WorkTypeWithCoverImg = [WORK_TYPE.video, WORK_TYPE.audio, WORK_TYPE.novel]; // 应上传封面的作品类型
const WorkTypeWithImgList = [WORK_TYPE.cos, WORK_TYPE.illustration, WORK_TYPE.comic]; // 含有图片列表的作品类型
const { isIn64SpecialPeriod } = require('../../common/helper')

let worksMethods = {};
let TongrenWorkController = {};

module.exports = TongrenWorkController;

/**
 * @api {get} /md/tongren/home  获取个人主页
 * @apiName  GetUserInfo
 * @apiParam {String} [targetid] 用户ID
 * @apiParam {Number} [page=1] 页码
 * @apiParam {Number={1-20}} [page_size=20] 分页大小
 * @apiParam {Number={1,2,3,4,5,6}} [work_type] 作品分类：1 COS  2 视频  3 音频  4 小说  5 插画  6 漫画
 * @apiGroup Tongren
 */
TongrenWorkController.home = (req, res, next) => {
    let params = req.params;
    let targetId = params.targetid;
    let curUserId = params.userid;

    let showId; // 个人主页的用户ID
    if (targetId) {
        showId = targetId;
    } else if (curUserId) {
        showId = curUserId;
    }

    let isCurUser = curUserId == showId ? true : false;

    return req.paramsValidator
        .param('targetid', { required: false, type: Number })
        .param('page', { type: Number, default: 1, min: 1 })
        .param('page_size', { type: Number, default: WORK_CONFIG.workMaxPageSize, min: 1, max: WORK_CONFIG.workMaxPageSize })
        .param('work_type', { required: false, type: Number, default: WORK_TYPE.cos, values: _.values(WORK_TYPE, 'id') })
        .validate()
        .then(() => {
            if (!showId) {
                return Promise.reject({ msg: '请登录后查看' });
            }
        })
        .then(() => {
            let gameName = 'qn';
            let workListFilterOpt = {
                page: params.page,
                pageSize: params.page_size,
                workType: params.work_type,
                authorId: showId
            };
            return Promise.props({
                mdUserInfo: TongrenUserModel.getUserFullInfoById(showId, curUserId), // 获取梦岛用户信息
                followType: ContactsModel.getFollowType(curUserId, showId), // 获取用户对作者的关注状态
                realityShow: RealityShowModel.fillUsersWithRealityShow([{ ID: showId }]), // 获取真人秀状态
                mainRoleInfo: GamesModel.getMainRole(gameName, showId), // 获取主角色信息
                workList: worksMethods.getListByUserId(workListFilterOpt, curUserId, isCurUser) // 获取作品列表
            });
        })
        .then(result => {
            let homeData = result.mdUserInfo;
            homeData.FollowType = result.followType;
            homeData.RoleInfo = result.mainRoleInfo;
            homeData.WorkList = result.workList;
            homeData = !_.isEmpty(result.realityShow) ? _.extend(homeData, result.realityShow[0]) : homeData;
            return homeData;
        })
        .then(homeData => {
            // 获取同人用户信息
            if (isCurUser) {
                return TongrenUserModel.getTonrenUserById(showId)
                    .then(tongrenUserInfo => {
                        homeData = !_.isEmpty(tongrenUserInfo) ? _.extend(homeData, tongrenUserInfo[0]) : homeData;
                        return homeData;
                    });
            } else {
                return homeData;
            }
        })
        .then(homeData => {
            res.succSend(homeData);
        })
        .catch(function (err) {
            errorHandler(err, req, res, next);
        });
};

/**
 * @api {get} /md/tongren/list  获取作品列表
 * @apiName  GetWorkList
 * @apiParam {Number} [page=1] 页码
 * @apiParam {Number={1-20}} [page_size=20] 分页大小
 * @apiParam {String="new","hot"} [sort_by=new] 分类：new 最新列表  hot 热门列表
 * @apiParam {Number={1,2,3,4,5,6}} [work_type] 作品分类：1 COS  2 视频  3 音频  4 小说  5 插画  6 漫画
 * @apiParam {Number} [author_id] 作者ID
 * @apiGroup Tongren
 */
TongrenWorkController.list = (req, res, next) => {
    let params = req.params;
    let authorId = params.author_id;
    let curUserId = params.userid;
    let isCurUser = curUserId == authorId ? true : false;

    return req.paramsValidator
        .param('page', { type: Number, default: 1, min: 1 })
        .param('page_size', { type: Number, default: WORK_CONFIG.workMaxPageSize, min: 1, max: WORK_CONFIG.workMaxPageSize })
        .param('sort_by', { type: String, default: 'new', values: ["hot", "new"] })
        .param('author_id', { type: Number, required: false })
        .param('work_type', { type: Number, required: false, default: WORK_TYPE.cos, values: _.values(WORK_TYPE, 'id') })
        .validate()
        .then(() => {
            let filterOpt = {
                page: params.page,
                pageSize: params.page_size,
                sortType: params.sort_by,
                workType: params.work_type,
                authorId: authorId
            };

            if (authorId) {
                // 查看作者个人作品列表
                return worksMethods.getListByUserId(filterOpt, curUserId, isCurUser);
            } else {
                // 查看全部作品列表
                return worksMethods.getListOfAll(filterOpt, curUserId);
            }
        })
        .then(worksList => {
            res.succSend({ list: worksList });
        })
        .catch(function (err) {
            errorHandler(err, req, res, next);
        });
};

/**
 * @api {get} /md/tongren/edit  投稿
 * @apiName  EditWork
 * @apiPermission 登录用户
 * @apiParam {String} title 标题（最多15字）
 * @apiParam {String} [description] 描述（最多60字；音乐作品最多1000字）
 * @apiParam {String} [cover_image_url] 封面图
 * @apiParam {Array={1-9}} attachment_list 附件链接（数组长度：1~9）
 * @apiParam {String} [tag_list] 标签列表
 * @apiParam {Number={1,2,3,4,5,6}} [work_type] 作品分类：1 COS  2 视频  3 音频  4 小说  5 插画  6 漫画
 * @apiGroup Tongren
 */
TongrenWorkController.edit = (req, res, next) => {
    let params = req.params;
    let curUserId = params.userid;
    let createTime = (new Date()).getTime();

    if (isIn64SpecialPeriod(new Date())) {
        const ALERT_MSG = "因技术升级，即日起至6月6日期间该功能暂不可用，对您造成不便深感歉意。"
        return res.send({ code: -100, msg: ALERT_MSG })
    }

    return req.paramsValidator
        .param('title', { required: true, maxlen: 15 })
        .param('type', { required: true, type: Number, values: _.values(WORK_TYPE, 'id') })
        .param('cover_image_url', { required: false, type: String, textType: 'url' })
        .param('attachment_list', { required: true, type: Array, minSize: 1, maxSize: 9, textType: 'url' })
        .param('tag_list', { type: String, required: false })
        .validate()
        .then(() => {
            // 校验作品说明的字数限制
            if (params.type == WORK_TYPE.audio) {
                return req.paramsValidator.param('description', { required: false, maxlen: 1000 }).validate();
            } else {
                return req.paramsValidator.param('description', { required: false, maxlen: 60 }).validate();
            }
        })
        .then(() => {
            // 验证封面数据
            if (_.includes(WorkTypeWithCoverImg, params.type) && !params.cover_image_url) {
                return Promise.reject({ errorType: "ParamRequired", param: 'cover_image_url' });
            } else if (!_.includes(WorkTypeWithCoverImg, params.type) && params.cover_image_url) {
                params.cover_image_url = '';
            }
        })
        .then(() => {
            // 添加投稿
            let workInfo = {
                title: params.title,
                description: params.description,
                coverImageUrl: params.cover_image_url,
                attachmentList: _.join(params.attachment_list, ','),
                userId: curUserId,
                type: params.type,
                tagList: params.tag_list,
                createTime: createTime,
                status: WORK_STATUS.Auditing
            };
            return TongrenWorksModel.createWork(workInfo);
        })
        .then(() => {
            // 将用户写入同人用户表
            return TongrenUserModel.updateOrCreateScore({
                UserId: curUserId,
                CreateTime: createTime
            });
        })
        .then(() => {
            res.succSend({ msg: '投稿成功，请耐心等待审核' });
        })
        .catch(function (err) {
            errorHandler(err, req, res, next);
        });
};

/**
 * @api {get} /md/tongren/work/:id  获取作品详情信息
 * @apiName  ShowWorkDetail
 * @apiPermission 查看他人：审核通过的作品；查看自己：审核中 + 审核通过的作品
 * @apiGroup Tongren
 */
TongrenWorkController.detail = (req, res, next) => {
    let params = req.params;
    let workId = params.id;
    let curUserId = params.userid;
    let authorId;

    return req.paramsValidator
        .param('id', { type: Number, required: true })
        .validate()
        .then(() => {
            // 获取作者ID
            return TongrenWorksModel.getAuthorIdByWorkId(workId);
        })
        .then(authorId => {
            // 获取作品详情
            let isCurUser = authorId == curUserId ? true : false;
            return TongrenWorksModel.getWorkDetail(workId, isCurUser)
                .then(workInfo => {
                    // 判断作品是否存在
                    if (_.isEmpty(workInfo)) {
                        return Promise.reject({ msg: '作品不存在' });
                    } else {
                        return workInfo;
                    }
                });
        })
        .then(workInfo => {
            let commentPage = 1;
            authorId = workInfo.AuthorId;
            return Promise.props({
                workInfo: workInfo,
                authorInfo: TongrenUserModel.getUserFullInfoById(authorId, curUserId), // 获取作者信息
                realityShow: RealityShowModel.fillUsersWithRealityShow([{ ID: authorId }]), // 获取真人秀状态
                likeList: worksMethods.getLikeListByWorkId(workId, curUserId), // 获取点赞列表
                commentList: TongrenCommentModel.getFullCommentList(workId, curUserId, commentPage, TongrenCommentModel.COMMENT_CONFIG.pageSize) // 获取评论列表
            });
        })
        .then(result => {
            let workDetailData = result.workInfo;
            workDetailData.AuthorInfo = result.authorInfo;
            workDetailData.LikeList = result.likeList;
            workDetailData.CommentList = result.commentList;
            workDetailData.RealityShow = !_.isEmpty(result.realityShow) ? result.realityShow[0].RealityShow : {};
            return workDetailData;
        })
        .then(workDetailData => {
            // 获取点赞状态、关注状态、封面
            return worksMethods.formatWorksList([workDetailData], curUserId)
                .then(formatData => {
                    return formatData[0];
                });
        })
        .then(workDetailData => {
            res.succSend(workDetailData);
        })
        .catch(function (err) {
            errorHandler(err, req, res, next);
        });
};

/**
 * @api {get} /md/tongren/search  搜索作品
 * @apiName SearchWorks
 * @apiParam {String} keyword  关键词
 * @apiParam {Number} [page=1] 页码 最小为1
 * @apiParam {Number={1-20}} [page_size=20] 分页大小
 * @apiParam {Number={1,2,3,4,5,6}} [work_type] 作品分类：1 COS  2 视频  3 音频  4 小说  5 插画  6 漫画
 * @apiParam {Number={1,2,3}} [keyword_type=1] 关键词类型：1 全部  2 作品  3 作者
 * @apiGroup Tongren
 */
TongrenWorkController.search = (req, res, next) => {
    let params = req.params;
    let keyword = params.keyword;
    let curUserId = params.userid;

    return req.paramsValidator
        .param('keyword', { type: String, required: true })
        .param('page', { type: Number, required: false, default: 1, min: 1 })
        .param('page_size', { type: Number, required: false, default: WORK_CONFIG.workMaxPageSize, min: 1, max: WORK_CONFIG.workMaxPageSize })
        .param('work_type', { type: Number, required: false, values: _.values(WORK_TYPE, 'id') })
        .param('keyword_type', { type: Number, required: false, default: 1, values: [1, 2, 3] })
        .validate()
        .then(() => {
            // 搜索作者，获取作品ID集合
            if (_.includes([1, 3], params.keyword_type)) {
                return TongrenUserModel.getUserIdBySearchName(keyword)
                    .then(userIdSet => {
                        return TongrenWorksModel.getWorkIdsByUserIds(userIdSet);
                    });
            } else {
                return [];
            }
        })
        .then(workIdSetBySearchUser => {
            // 搜索作品标题和说明，获取作品ID集合
            if (_.includes([1, 2], params.keyword_type)) {
                return TongrenWorksModel.getWorkIdsBySearch(keyword)
                    .then(workIdSetBySearchWork => {
                        return _.uniq(_.concat(workIdSetBySearchUser, workIdSetBySearchWork));
                    });
            } else {
                return workIdSetBySearchUser;
            }
        })
        .then(workIdSet => {
            // 根据作品ID集合，获取作品列表
            let filterOpt = {
                page: params.page,
                pageSize: params.page_size,
                work_type: params.work_type
            };
            return TongrenWorksModel.getWorksListByIds(workIdSet, filterOpt);
        })
        .then(worksList => {
            // 获取点赞状态、关注状态、封面
            return worksMethods.formatWorksList(worksList, curUserId);
        })
        .then(worksList => {
            res.succSend({ list: worksList });
        })
        .catch(function (err) {
            errorHandler(err, req, res, next);
        });
};

/**
 * @api {get} /md/tongren/audit  作品审核
 * @apiName Audit
 * apiPermission 仅允许IP白名单中的客户端访问
 * @apiParam {Number} work_id  作品ID
 * @apiParam {Number={1,-2}} status 作品状态： 1 审核成功  -2 审核不通过
 * @apiParam {String} admin_name 管理员名字
 * @apiParam {Number=[0-100]} [score]  分数
 * @apiParam {String={0-250}} [comment] 评语（最多250字）
 * @apiParam {String} [cover_image_url] 封面图片地址
 * @apiParam {Array={1~9}} [attachment_list] 作品链接数组（数组大小：1~9）
 * @apiParam {Number} [top] 置顶：0 正常  1 置顶
 * @apiGroup Tongren
 */
TongrenWorkController.audit = (req, res, next) => {
    let params = req.params;
    let workId = params.work_id;
    let isAudited = false;
    let requiredWorkStatus = [WORK_STATUS.NORMAL, WORK_STATUS.REJECT];
    let curTime = (new Date()).getTime();
    let curScore = params.status != WORK_STATUS.REJECT ? params.score : 0;
    let authorId, oldScore, deltaScore, workInfo;

    return req.paramsValidator
        .param('work_id', { type: Number, required: true })
        .param('status', { type: Number, required: true, values: requiredWorkStatus })
        .param('score', { type: Number, required: false, default: 0, min: 0, max: 100 })
        .param('comment', { type: String, required: false, maxlen: 250 })
        .param('admin_name', { type: String, required: true })
        .param('cover_image_url', { type: String, required: false, textType: 'url' })
        .param('attachment_list', { type: Array, required: false, minSize: 1, maxSize: 9, textType: 'url' })
        .param('top', { type: Number, required: false, default: 0, values: [WORK_TOP_STATUS.NORMAL, WORK_TOP_STATUS.TOP] })
        .validate()
        .then(() => {
            // 参数检验
            if (params.status == WORK_STATUS.NORMAL && !params.score) {
                return Promise.reject({ errorType: "ParamRequired", param: 'score' });
            }
        })
        .then(() => {
            // 获取作品信息
            return TongrenWorksModel.findById(params.work_id, '*')
                .then(workData => {
                    if (!_.isEmpty(workData)) {
                        authorId = workData.AuthorId;
                        oldScore = workData.Score || 0;
                        workInfo = workData;
                        if (_.includes(requiredWorkStatus, workInfo.Status)) {
                            isAudited = true;
                        }
                    } else {
                        return Promise.reject({ msg: '作品不存在' });
                    }
                });
        })
        .then(() => {
            // 更新作品审核后的状态信息
            let workInfo = {
                ID: workId,
                CoverImageUrl: params.cover_image_url,
                AttachmentList: _.join(params.attachment_list, ','),
                Score: curScore,
                ScoreComment: params.comment,
                AuditAdminName: params.admin_name,
                IsTop: params.top,
                Status: params.status
            };
            return TongrenWorksModel.updateWorkInfoWhenAudit(workInfo);
        })
        .then(() => {
            // 获取用户当前作品的积分比例，计算实际得分
            let rangeOfMonth = worksMethods.getTimeRangeOfMonth(workInfo.CreateTime);
            return worksMethods.getScoreRatio(authorId, rangeOfMonth.lastMonth, workInfo.CreateTime)
                .then(scoreRatioInfo => {
                    let scoreRatio = scoreRatioInfo.ScoreRatio;
                    let realOldScore = oldScore < 60 ? oldScore * scoreRatio : oldScore;
                    curScore = curScore < 60 ? curScore * scoreRatio : curScore;
                    deltaScore = (curScore - realOldScore) || 0;
                });
        })
        .then(() => {
            return Promise.props({
                tongrenUserInfo: TongrenUserModel.getTonrenUserById(authorId), // 获取同人用户的积分信息
                mdUserInfo: TongrenUserModel.getUserById(authorId, authorId) // 获取用户信息
            });
        })
        .then(userInfo => {
            let tongrenUserInfo = userInfo.tongrenUserInfo[0] || {};
            tongrenUserInfo.CurrentScore = tongrenUserInfo.CurrentScore || 0;
            tongrenUserInfo.HistoryScore = tongrenUserInfo.HistoryScore || 0;

            // 更新用户积分
            if (deltaScore && authorId) {
                let scoreData = { // 用户积分数据
                    UserId: authorId,
                    CurrentScore: tongrenUserInfo.CurrentScore + deltaScore,
                    HistoryScore: tongrenUserInfo.HistoryScore + deltaScore,
                    CreateTime: curTime
                };
                let hotData = { // 热度数据
                    score: params.score - oldScore,
                    top: params.top
                };
                let scoreRecordData = { // 积分记录信息数据
                    UserId: authorId,
                    WorkId: workId,
                    DeltaScore: deltaScore,
                    CreateTime: curTime
                };

                return Promise.props({
                    scoreData: scoreData,
                    mdUserInfo: userInfo.mdUserInfo,
                    updateOrCreateScore: TongrenUserModel.updateOrCreateScore(scoreData), // 更新用户积分
                    scoreRecord: TongrenScoreRecordModel.create(scoreRecordData), // 添加积分记录信息
                    updateHotScore: TongrenWorksModel.updateHotScore(workId, hotData) // 更新热度
                });
            } else {
                return {
                    scoreData: tongrenUserInfo,
                    mdUserInfo: userInfo.mdUserInfo
                };
            }
        })
        .then(info => {
            let mdUserInfo = info.mdUserInfo;
            let scoreData = info.scoreData;
            if (params.status != workInfo.Status || deltaScore != 0) {
                return Promise.join(
                    // 推送作品审核的提醒消息
                    worksMethods.addAuditNotification(workInfo, mdUserInfo.NickName, params.status, curScore, scoreData.CurrentScore, params.comment),
                    // 更新作品列表缓存
                    TongrenWorksModel.clearListCache(workInfo.Type, workInfo.ID)
                );
            }
        })
        .then(() => {
            if (params.status == WORK_STATUS.NORMAL && authorId && !isAudited) {
                return Promise.join(
                    TongrenUserModel.setUserToACG(authorId), // 给用户加上二次元标记
                    worksMethods.addMoment(workInfo) // 发布新鲜事
                );
            }
        })

        .then(() => {
            let msg = isAudited ? '已更新' : '已发布';
            res.send({ code: 0, msg: msg });
        })
        .catch(function (err) {
            errorHandler(err, req, res, next);
        });
};

/**
 * @api {get} /md/tongren/work/:id/delete  删除作品
 * @apiName deleteWork
 * @apiGroup Tongren
 */
TongrenWorkController.delete = (req, res, next) => {
    let params = req.params;
    let workId = params.id;
    let curUserId = params.userid;

    return req.paramsValidator
        .param('id', { type: Number, required: true })
        .validate()
        .then(() => {
            // 验证作品是否存在和当前用户的删除权限
            return TongrenWorksModel.getWorkDetail(workId, true)
                .then(workInfo => {
                    if (_.isEmpty(workInfo)) {
                        return Promise.reject({ msg: '作品不存在' });
                    } else {
                        // 只允许删除审核不通过的作品
                        if (workInfo.Status != WORK_STATUS.REJECT) {
                            return Promise.reject({ msg: '该状态下的作品不允许删除' });
                        } else if (workInfo.Status == WORK_STATUS.REJECT && workInfo.AuthorId != curUserId) {
                            return Promise.reject({ msg: '没有删除权限' });
                        }
                    }
                });
        })
        .then(() => {
            return Promise.join(
                TongrenWorksModel.deleteWork(workId), // 删除作品
                NotificationsModel.removeDeletedTongrenWork(workId) // 删除作品对应的消息提示
            );
        })
        .then(() => {
            res.succSend({ msg: '已删除' });
        })
        .catch(function (err) {
            errorHandler(err, req, res, next);
        });
};

/**
 * @api {get} /md/tongren/score_ratio  获取用户本月的投稿次数
 * @apiName getScoreRatioOfUser
 * @apiGroup Tongren
 */
TongrenWorkController.getScoreRatio = (req, res, next) => {
    let params = req.params;
    let curUserId = params.userid;

    return worksMethods.getContributeTimes(curUserId)
        .then(scoreData => {
            // 获取用户基本信息
            return TongrenUserModel.getUserById(curUserId, curUserId)
                .then(userData => {
                    scoreData.UserInfo = userData;
                    return scoreData;
                });
        })
        .then(scoreData => {
            res.succSend(scoreData);
        })
        .catch(function (err) {
            errorHandler(err, req, res, next);
        });

};

/**
 * @api {get} /md/tongren/work/:workid/score_ratio  获取作品的积分比例
 * @apiName getScoreRatioOfWork
 * @apiGroup Tongren
 */
TongrenWorkController.getScoreRatioOfWork = (req, res, next) => {
    let params = req.params;
    let workId = params.workid;

    return TongrenWorksModel.getWorkDetail(workId, true)
        .then(workData => {
            if (_.isEmpty(workData)) {
                return Promise.reject({ msg: '作品不存在' });
            } else {
                return workData;
            }
        })
        .then(workData => {
            let rangeOfMonth = worksMethods.getTimeRangeOfMonth(workData.CreateTime);
            return worksMethods.getScoreRatio(workData.AuthorId, rangeOfMonth.lastMonth, workData.CreateTime);
        })
        .then(scoreRatioInfo => {
            let ScoreRatio = {
                ID: workId,
                ContributeTimes: scoreRatioInfo.ContributeTimes,
                ScoreRatio: scoreRatioInfo.ScoreRatio
            };
            res.succSend(ScoreRatio);
        })
        .catch(function (err) {
            errorHandler(err, req, res, next);
        });
};

// 获取作者的个人作品列表
worksMethods.getListByUserId = (filterOpt, curUserId, isCurUser) => {
    return TongrenWorksModel.getWorksListByUserId(filterOpt, isCurUser)
        .then(worksList => {
            // 格式化作品列表
            return worksMethods.formatWorksList(worksList, curUserId);
        });
};

// 获取全部作品列表
worksMethods.getListOfAll = (filterOpt, curUserId) => {
    return TongrenWorksModel.getListOfAllFromCache(filterOpt)
        .then(worksList => {
            // 获取作者信息
            let userIdSet = _.uniq(_.map(worksList, 'AuthorId')); // 作者ID集合
            return TongrenUserModel.getUsersByIds(userIdSet, curUserId)
                .then(authorInfo => {
                    _.forEach(worksList, work => {
                        let authorID = work.AuthorId;
                        let authorData = _.find(authorInfo, { 'ID': authorID }) || {};

                        work.AuthorId = authorID;
                        work.AuthorAvatar = authorData.Avatar;
                        work.AuthorNickName = authorData.NickName;
                    });
                    return worksList;
                })
        })
        .then(worksList => {
            // 格式化作品列表
            return worksMethods.formatWorksList(worksList, curUserId);
        });
};

// 对作品列表进行格式化：是否允许点赞、关注状态、封面
worksMethods.formatWorksList = (worksList, curUserId) => {
    let curTime = (new Date()).getTime();
    return Promise.resolve()
        .then(() => {
            if (curUserId) {
                let workIdSet = _.uniq(_.map(worksList, 'ID')); // 作品ID集合
                let userIdSet = _.uniq(_.map(worksList, 'AuthorId')); // 作者ID集合
                return Promise.props({
                    usersLikeRecords: TongrenLikeModel.getUserLikeRecordByWorkIds(curUserId, workIdSet), // 用户点赞记录
                    usersFollowTypeRecords: ContactsModel.getFollowTypeOfUserIds(curUserId, userIdSet) // 关注状态记录
                })
                    .then(records => {
                        _.forEach(worksList, workItem => {
                            // 判断当前用户是否允许点赞
                            let likeRecord = _.find(records.usersLikeRecords, { 'UserId': curUserId, 'WorkId': workItem.ID });
                            workItem.AllowLike = _.isEmpty(likeRecord);

                            // 判断当前用户与作者的关注状态
                            let followRecord = _.find(records.usersFollowTypeRecords, { TargetId: workItem.AuthorId });
                            if (!_.isEmpty(followRecord)) {
                                workItem.FollowType = followRecord.FollowType;
                            } else {
                                workItem.FollowType = ContactsModel.FollowType.NO_FOLLOW;
                            }
                        });
                    });
            } else {
                _.forEach(worksList, (workItem) => {
                    workItem.FollowType = ContactsModel.FollowType.NO_FOLLOW;
                    workItem.AllowLike = true;
                });
            }
        })
        .then(() => {
            _.forEach(worksList, workItem => {
                // 设置时间间隔
                workItem.Duration = curTime - workItem.CreateTime;

                // 设置作品封面
                if (!_.includes(WorkTypeWithCoverImg, workItem.Type)) {
                    workItem.CoverImageUrl = _.split(workItem.AttachmentList, ',')[0];
                }
            });
            return worksList;
        });
};

// 获取作品的点赞用户信息列表
worksMethods.getLikeListByWorkId = (workId, curUseId) => {
    // 获取作品ID与用户ID的映射表
    return TongrenLikeModel.getLikeRecordByWorkIdFromCache(workId)
        .then(userIds => {
            // 获取users信息列表
            return TongrenUserModel.getUsersByIds(userIds, curUseId)
                .then(likeList => {
                    return likeList;
                });
        });
};

// 获取本月的始末时间范围
worksMethods.getTimeRangeOfMonth = (curTime, offsetDate) => {
    offsetDate = offsetDate || 21;
    curTime = curTime ? new Date(curTime) : new Date();
    let yearOfLastMonth, monthOfLastMonth;
    let curYear = curTime.getFullYear();
    let curMonth = curTime.getMonth() + 1;
    let curDate = curTime.getDate();
    if (curDate >= offsetDate) {
        monthOfLastMonth = curMonth;
        yearOfLastMonth = curYear;
        if (curMonth == 12) {
            // 12月offsetDate ~ 12月31日
            curYear += 1;
            curMonth = 1;
        } else {
            curMonth += 1;
        }
    } else {
        if (curMonth == 1) {
            // 1月1日 ~ 1月offsetDate
            monthOfLastMonth = 12;
            yearOfLastMonth = curYear - 1;
        } else {
            monthOfLastMonth = curMonth - 1;
            yearOfLastMonth = curYear;
        }
    }

    return {
        lastMonth: new Date(`${yearOfLastMonth}-${monthOfLastMonth}-${offsetDate}`).getTime(),
        thisMonth: new Date(`${curYear}-${curMonth}-${offsetDate - 1} 23:59:59`).getTime()
    };
};

// 获取用户在一定时间范围内的积分比例
worksMethods.getScoreRatio = (userId, startTime, endTime) => {
    // 获取低于60分的投稿次数
    let maxScore = 60;
    return worksMethods.getContributeTimes(userId, maxScore, startTime, endTime)
        .then(contributeInfo => {
            // 计算当前投稿积分比例
            if (contributeInfo.ContributeTimes >= 5) {
                contributeInfo.ScoreRatio = 0;
            } else if (contributeInfo.ContributeTimes >= 3) {
                contributeInfo.ScoreRatio = 0.5;
            } else {
                contributeInfo.ScoreRatio = 1;
            }
            return contributeInfo;
        });
};

// 获取本月投稿次数
worksMethods.getContributeTimes = (userId, maxScore, startTime, endTime) => {
    maxScore = maxScore || 101;
    let monthRange = worksMethods.getTimeRangeOfMonth();
    let contributeInfo = {};
    startTime = startTime || monthRange.lastMonth;
    endTime = endTime || monthRange.thisMonth;

    contributeInfo.UserId = userId;
    contributeInfo.LastMonth = startTime;
    contributeInfo.ThisMonth = endTime;

    return Promise.props({
        contributeTimes: TongrenWorksModel.getContributeTimesMonthlyByUserId(userId, maxScore, startTime, endTime), // 获取本月投稿次数
        userData: TongrenUserModel.getTonrenUserById(userId) // 获取同人用户信息
    })
        .then(result => {
            if (!_.isEmpty(result.contributeTimes)) {
                contributeInfo = _.extend(result.contributeTimes[0]);
            }
            if (!_.isEmpty(result.userData)) {
                contributeInfo = _.extend(contributeInfo, result.userData[0]);
            }
            return contributeInfo;
        });
};

// 发布新鲜事
worksMethods.addMoment = (workInfo) => {
    let imgList = '';
    let description = workInfo.Description || '';
    description = description.slice(1, 100) + '...';
    if (_.includes(WorkTypeWithImgList, workInfo.Type)) {
        imgList = workInfo.AttachmentList || '';
    } else {
        imgList = workInfo.CoverImageUrl || '';
    }
    let imgAuditStatus = _.fill(_.split(imgList, ','), 1);
    return MomentsModel.create({
        UserId: workInfo.AuthorId,
        Text: description,
        ImgList: imgList,
        GameId: GamesModel.GameIds.TONGREN,
        GameMomentId: workInfo.ID,
        ImgAudit: _.join(imgAuditStatus, ',')
    });
};

// 推送作品审核的提醒消息
worksMethods.addAuditNotification = (workInfo, nickName, status, deltaScore, totalScore, comment) => {
    let eventType = NotificationTypes["AUDIT_TONGREN_WORK"];
    let targetUserId = workInfo.AuthorId;
    let resourceId = workInfo.ID;

    nickName = nickName || '用户';
    workInfo.Title = workInfo.Title || '';
    deltaScore = deltaScore || 0;
    totalScore = totalScore || 0;

    let messageObj = {
        AuthorName: nickName,
        Title: workInfo.Title,
        Status: _.toString(status),
        DeltaScore: _.toString(deltaScore),
        TotalScore: _.toString(totalScore),
        Comment: comment
    };
    let message = JSON.stringify(messageObj);
    return NotificationsModel.create({
        UserId: null,
        TargetId: targetUserId,
        Type: eventType,
        RelateId: resourceId,
        Message: message
    });
};