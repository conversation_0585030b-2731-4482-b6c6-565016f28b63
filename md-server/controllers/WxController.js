/**
 * Created by <PERSON><PERSON><PERSON> on 2017/3/2.
 */

const WxController = {};
const rq = require('../../common/request');
const AuthService = require('../../common/auth');
const Users = require('../../models/Users');
const errorHandler = require('../errorHandler');
const WxaBind = require('../../models/WxaBind');
const _ = require('lodash');
const UrsService = require('../../common/urs');
const util = require('../../common/util');
const config = require('../../common/config');
const Promise = require('bluebird');
const path = require('path');
const { getRedis } = require('../../common/redis');


WxController.login = function (req, res, next) {
  return req.paramsValidator
    .param('code', {type: String})
    .validate()
    .then(() => {
      const code = req.params.code;
      return WxService.getSessionByCode(code)
    }).then(wxSession => {
      return Promise.all([
        WxaBind.getUserInfoByOpenId(wxSession.openid),
        wxSession
      ])
    }).spread((userInfo, wxSession)=> {
      let getSkeyInfo;
      if(userInfo) {
        getSkeyInfo= AuthService.start("wxa_user", {wxa_userid: userInfo.ID}, _.assign(wxSession, {userid: userInfo.ID}));
      } else {
        getSkeyInfo = AuthService.start("wxa_guest", {wxa_guestid: wxSession.openid}, wxSession);
      }
      return Promise.all([
        getSkeyInfo,
        userInfo
      ])
    }).spread((skeyInfo, userInfo) => {
      res.succSend(({
        skey: skeyInfo.skey,
        userInfo: userInfo
      }))
    }).catch(err => {
      errorHandler(err, req, res, next);
    })
};

const WxService = {
  getSessionByCode: function (code) {
    const APPID = 'wx30e42d8b3310d206';
    const SECRET = '2195e920f431cf7db66b8f0b4a6d209a';
    return rq.get(`https://api.weixin.qq.com/sns/jscode2session?appid=${APPID}&secret=${SECRET}&js_code=${code}&grant_type=authorization_code`)
      .then(data => {
        if(data.errcode) {
          return Promise.reject(data)
        } else {
          return data;
        }
      });
  }
};

WxController.uploadFile = function (req, res, next) {
  const fs = require('fs');
  const nosClient = require('../../common/nosSdkClient').nosClient;
  let uploadMap;

  return Promise.method(() => {
    const fileInfo = _.get(req, 'files.file');
    const size = fs.statSync(fileInfo.path).size;
    if(fileInfo) {
      return {
        bucket: config.NOS_CFG.bucketName['md'],
        key:  path.basename(fileInfo.path),
        body: fs.createReadStream(fileInfo.path),
        length: size
      }
    } else {
      return Promise.reject({msg:"没有发现上传的文件"});
    }
  })().then(fileMap => {
    uploadMap = fileMap;
    return nosClient.put_object_stream_async(fileMap);
  }).then(uploadInfo => {
    const url = "http://nos.netease.com/" + uploadMap.bucket + '/' + uploadInfo.headers['x-nos-object-name'];
    require('../../common/logger').add("wxUploadFile", url);
    res.succSend({url: url})
  }).catch(err => {
    errorHandler(err, req, res, next);
  })
};

WxController.bindUrs = function (req, res, next) {
  let urs, sessionData;
  return AuthService.check(req, null, {idMatchCheck: false})
    .then(data => {
      sessionData = data;
      urs = req.params.urs;
      return req.paramsValidator
        .param('urs', {type: String})
        .param('password', {type: String})
        .param('skey', {type: String})
        .validate()
    }).then(() => {
      return UrsService.login({
        username: req.params.urs,
        password: req.params.password,
        userip:  util.getIp(req)
      })
    }).then(() => {
      return Users.findOne({UserName: urs}, ['ID', 'NickName'])
    })
    .then(userInfo => {
      if (userInfo) {
        const openId = sessionData.openid;
        const sessionKey = sessionData.session_key;
        return WxaBind.createOrUpdate({
            UserId: userInfo.ID,
            UserName: urs,
            OpenId: openId,
            CreateTime: Date.now(),
            SessionKey: sessionKey,
            RefreshTime: Date.now()
          },
          {RefreshTime: Date.now()}).then(() => {
          return AuthService.start("wxa_user", {wxa_userid: userInfo.ID}, {
            openid: openId,
            session_key: sessionKey,
            userid: userInfo.ID
          });
        }).then(skeyInfo => {
          res.succSend(({
            skey: skeyInfo.skey,
            userInfo: userInfo
          }))
        });
      } else {
        const sessionKey = "sess:" + req._SESS_INFO_.id;
        return getRedis().hsetAsync(sessionKey, "urs", urs)
          .then(() => {
            res.send({code: 'NO_USER', msg: '请先注册帐号'})
          });
      }
    }).catch(err => {
      errorHandler(err, req, res, next);
    })
};

module.exports = WxController;
