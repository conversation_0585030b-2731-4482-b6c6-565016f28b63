var RoleInfosController = module.exports = {};
var errorHandler = require('../errorHandler');
var QNRoleInfos = require('../../models/QNRoleInfos');
var QNMRoleInfos = require('../../models/QNMRoleInfos');
var Games = require('../../models/Games');
var Users = require('../../models/Users');
var BindRoles = require('../../models/BindRoles');
var _ = require('lodash');
var Promise = require('bluebird');
const config = require('../../common/config')


/**
 * @api {get} /md/games/:game_name/role_infos list user all role infos
 * @apiName listRoleInfos
 * @apiGroup RoleInfos
 * @apiParam {String=qn} game_name game name
 */
RoleInfosController.list = function(req, res, next) {
  var userName = req.params.urs;
  var gameName = req.params.game_name;

  return req.paramsValidator
    .param('game_name', {type: String, values: ['qn']})
    .param('user_id', {type: String, required: false})
    .validate()
    .then(function() {
      var curUserId = req.params.userid;
      var visitUserId = req.params.user_id || curUserId;
      if(visitUserId) {
        return Users.findByIdForce(visitUserId).then(function(user) {
          return Games.getRoleInfos(gameName, user.UserName, visitUserId);
        });
      } else {
        return Games.getRoleInfos(gameName, userName);
      }
    }).then(function(roleInfos) {
      res.succSend({
        roleInfos: roleInfos
      });
    }).catch(function(err) {
      errorHandler(err, req, res, next);
    });
};

function fixBindType(roleInfos) {
  return roleInfos.map(function (roleInfo) {
    if(roleInfo.BindType === 0) {
      roleInfo.BindType = BindRoles.BindTypes.NORMAL_BIND; // BindType与BindInfo表Type定义不一致，保持定义兼容;
    }
    return roleInfo;
  });
}

function getQnRoleInfos(userId) {
  return QNRoleInfos.executeByQuery(
    QNRoleInfos.scope()
      .select('r.RoleId', 'r.RoleName', 'b.Type as BindType')
      .from(QNRoleInfos.tableName + " as r")
      .leftJoin('md_bindrole as b', 'r.RoleId', 'b.RoleId')
      .where('b.GameId', Games.GameIds.QN)
      .where('b.UserId', userId)
  ).then(fixBindType);
}

function getQnmRoleInfos(userId) {
  return QNMRoleInfos.executeByQuery(
    QNMRoleInfos.scope()
      .select('r.RoleId', 'r.RoleName', 'b.Type as BindType')
      .from(QNMRoleInfos.tableName + " as r")
      .leftJoin('md_bindrole as b', 'r.RoleId', 'b.RoleId')
      .where('b.GameId', Games.GameIds.QNM)
      .where('b.UserId', userId)
  ).then(fixBindType)
}

RoleInfosController.listBindRole = function(req, res, next) {
  var userId;
  var curUserId;
  var gameName;

  return req.paramsValidator
    .param('user_id', {type: String, required: false})
    .param('game_name', {type: String, required:false, values: ['qn', 'qnm']})
    .validate()
    .then(function() {
      gameName = req.params.game_name;
      userId = req.params.user_id;
      curUserId = req.params.userid;
      if(!userId) {
        userId = curUserId;
      }
      if(gameName === 'qn') {
        return Promise.props({
          qnRoleInfos: getQnRoleInfos(userId),
        }).then(function (props) {
          props.roleInfos = props.qnRoleInfos;
          return props;
        })
      } else if(gameName === 'qnm') {
        return Promise.props({
          qnmRoleInfos: getQnmRoleInfos(userId)
        })

      } else {
        return Promise.props({
          qnRoleInfos: getQnRoleInfos(userId),
          qnmRoleInfos: getQnmRoleInfos(userId)
        })
      }
    })
    .then(function(result) {
      res.succSend(result);
    }).catch(function(err) {
      errorHandler(err, req, res, next);
    });
};



/**
 * @api {get} /md/games/:game_name/main_role_info list user main role info
 * @apiName listMainRoleInfo
 * @apiGroup RoleInfos
 * @apiParam {String=qn} game_name game name
 */
RoleInfosController.mainRoleInfo= function(req, res, next) {
  var userId = req.params.userid;
  var gameName = req.params.game_name;
  var gameId = Games.getGameIdByGameName(gameName);
  return req.paramsValidator
    .param('game_name', {type: String, values: ['qn']})
    .validate()
    .then(function() {
      return BindRoles.getUserMainRoleInGame(userId, gameId);
    }).then(function(roleInfo) {
      res.succSend({
        mainRoleInfo: roleInfo
      });
    }).catch(function(err) {
      errorHandler(err, req, res, next);
    });
};



/**
 * @api {get} /md/games/:game_name/role_infos/:role_id Get RoleInfo by RoleId
 * @apiName  GetRoleInfo
 * @apiGroup RoleInfos
 * @apiParam {String=qn} game_name game name
 * @apiParam {Number} role_id role id
 */
RoleInfosController.get = function(req, res, next) {
  var gameName, roleId, roleInfo;
  return req.paramsValidator
    .param('game_name', {type: String, values: ['qn', 'qnm']})
    .validate()
    .then(function() {
      gameName = req.params.game_name;
      roleId = req.params.role_id;
      if(gameName === Games.GameNames.QN) {
        return Promise.resolve(QNRoleInfos.getRoleInfo(roleId)).then(function(roleInfo) {
          var isTestServerRoleInfo = !config.testCfg.test_env && roleInfo.ServerId < 15
          if(_.isEmpty(roleInfo) || isTestServerRoleInfo) {
            return Promise.reject({errorType: "EntityNotFound", msg: "找不到该Id对应的角色信息"});
          } else {
            return roleInfo;
          }
        }).then(function(roleInfo) {
          return QNRoleInfos.fillRoleInfo(roleInfo);
        })
      } else {
        return QNMRoleInfos.getByRoleId(roleId);
      }
    })
    .then(function(roleInfoResult) {
      roleInfo = roleInfoResult;
      return Games.getBindInfoByRoleId(gameName, roleId);
    }).then(function(bindInfo) {
      if(_.has(bindInfo, 'UserId')) {
        return Users.getBasicUserInfo(bindInfo.UserId);
      } else {
        return null;
      }
    }).then(function(userInfo) {
      res.succSend({
        RoleInfo: roleInfo,
        UserInfo: userInfo
      });
    })
    .catch(function(err) {
      errorHandler(err, req, res, next);
    });
};
