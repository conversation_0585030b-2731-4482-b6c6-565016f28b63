module.exports = ContactsController = {};
var errorHandler = require('../errorHandler');
var Contacts = require('../../models/Contacts');
var Games = require('../../models/Games');
var Users = require('../../models/Users');
var _ = require('lodash');

/**
 * @api {get} /md/:game_name/contacts Get User contacts
 * @apiName  GetUserContacts
 * @apiParam {String=qn, qnm} game_name game name
 * @apiGroup Contacts
 */
ContactsController.list = function(req, res, next) {
  var curUserId;
  var gameName = req.params.game_name;
  return req.paramsValidator
    .param('game_name', {values: Games.GameNames})
    .validate()
    .then(function() {
      curUserId = req.params.userid;
      return Contacts.getUserContact(curUserId);
    })
    .then(function(users) {
      return Contacts.getUsersWithFollowInfo(users);
    })
    .then(function(users) {
      if(gameName === Games.GameNames.QN) {
        return Games.getUsersWithMainRole(gameName, users);
      } else {
        return users;
      }
    })
    .then(function(users) {
      return Users.setUsersAvatar(users, curUserId);
    })
    .then(function(users) {
      return res.succSend({users: users});
    })
    .catch(function(err) {
      errorHandler(err, req, res, next);
    });
};

ContactsController.cancelFollow = function(req, res, next) {
  var curUserId;
  var follow_user_id;
  return req.paramsValidator
    .param('follow_user_id')
    .validate()
    .then(function() {
      curUserId = req.params.userid;
      followUserId = req.params.follow_user_id;
      return Contacts.cancelFollow(curUserId, followUserId);
    })
    .then(function() {
      return res.succSend({msg: "取消关注成功!"});
    })
    .catch(function(err) {
      errorHandler(err, req, res, next);
    });
};
