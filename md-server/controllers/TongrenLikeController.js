let Promise = require('bluebird');
let _ = require('lodash');
let errorHandler = require('../errorHandler');

let TongrenLikeModel = require('../../models/MdTongrenLike');
let TongrenUserModel = require('../../models/MdTongrenUser');
let TongrenWorksModel = require('../../models/MdTongrenWorks');
let NotificationsModel = require('../../models/Notifications');

let WORK_TYPE_MAP = TongrenWorksModel.WORK_TYPE_MAP;

let likeMethods = {};
let TongrenLikeController = {};

module.exports = TongrenLikeController;

/**
 * @api {get} /md/tongren/like  点赞
 * @apiName  AddLikeForWork
 * @apiParam {Number} work_id  作品ID
 * @apiGroup Tongren
 */
TongrenLikeController.like = (req, res, next) => {
    let params = req.params;
    let workId = params.work_id;
    let curUserId = params.userid;
    let createTime = (new Date()).getTime();
    let authorId;
    let workInfo = {};

    return req.paramsValidator
        .param('work_id', {type: Number, required: true})
        .validate()
        .then(() => {
            // 判断作品是否存在
            return likeMethods.checkWorkIsExit(workId)
                .then(workData => {
                    workInfo = workData;
                    authorId = workData.AuthorId;
                    if(!authorId) {
                        return Promise.reject({msg: '该作品的作者不存在'});
                    }
                });
        })
        .then(() => {
            // 判断是否允许点赞
            return likeMethods.checkLikePermission(curUserId, workId);
        })
        .then(() => {
            if (curUserId != authorId) {
                return Promise.join(
                    TongrenWorksModel.updateHotScore(workId, {like: 1}), // 更新热度
                    likeMethods.addLikeNotification(workInfo, curUserId, createTime) // 推送点赞的提醒消息
                );
            }
        })
        .then(() => {
            return Promise.props({
                userInfo: TongrenUserModel.getUserById(curUserId, curUserId), // 获取用户信息
                likeAndUpdateCache: TongrenLikeModel.likeAndUpdateCache(workId, authorId, curUserId, createTime), // 点赞
                setLikeCountByWorkId: likeMethods.setLikeCountByWorkId(workId, 'like'), // 设置点赞数
                clearListCache: TongrenWorksModel.clearListCache(workInfo.Type, workInfo.ID) // 删除最热作品列表缓存
            });
        })
        .then(result => {
            res.succSend({msg: "点赞成功", userInfo: result.userInfo});
        })
        .catch(function(err) {
            errorHandler(err, req, res, next);
        });
};

/**
 * @api {get} /md/tongren/unlike  取消点赞
 * @apiName  UnLikeForWork
 * @apiParam {Number} work_id  作品ID
 * @apiGroup Tongren
 */
TongrenLikeController.unLike = (req, res, next) => {
    let params = req.params;
    let workId = params.work_id;
    let curUserId = params.userid;
    let authorId, workType;

    return req.paramsValidator
        .param('work_id', {type: Number, required: true})
        .validate()
        .then(() => {
            // 判断作品是否存在
            return likeMethods.checkWorkIsExit(params.work_id)
                .then(data => {
                    authorId = data.AuthorId;
                    workType = data.Type;
                });
        })
        .then(() => {
            // 判断是否允许取消点赞
            return likeMethods.checkUnlikePermission(curUserId, workId);
        })
        .then(() => {
            if (curUserId != authorId) {
                return Promise.join(
                    TongrenWorksModel.updateHotScore(workId, {like: -1}) // 更新热度
                );
            }
        })
        .then(() => {
            return Promise.props({
                unLikeAndUpdateCache: TongrenLikeModel.unLikeAndUpdateCache(workId, curUserId), // 取消点赞
                removeNotification: NotificationsModel.removeDeletedTongrenLike(workId, curUserId), // 删除推送的点赞消息
                setLikeCount: likeMethods.setLikeCountByWorkId(workId, 'unlike'), // 设置点赞数
                clearListCache: TongrenWorksModel.clearListCache(workType, workId), // 删除最热作品列表缓存
                userInfo: TongrenUserModel.getUserById(curUserId, curUserId) // 获取用户信息
            });
        })
        .then(result => {
            res.succSend({msg: "已取消点赞", userInfo: result.userInfo});
        })
        .catch(function(err) {
            errorHandler(err, req, res, next);
        });
};

// 判断用户是否有点赞的权限
likeMethods.checkLikePermission = (userId, workId) => {
    return TongrenLikeModel.getLikeCountByWorkId(userId, workId)
        .then(likeCount => {
            let permission = (likeCount <= 0);
            if(!permission) {
                return Promise.reject({msg: '您已点过赞'});
            } else {
                return permission;
            }
        });
};

// 判断用户是否有取消点赞的权限
likeMethods.checkUnlikePermission = (userId, workId) => {
    return TongrenLikeModel.getLikeCountByWorkId(userId, workId)
        .then(likeCount => {
            let permission = (likeCount > 0);
            if(!permission) {
                return Promise.reject({msg: '您还未点赞'});
            } else {
                return permission;
            }
        });
};

// 验证作品是否存在
likeMethods.checkWorkIsExit = (workId) => {
    return TongrenWorksModel.getWorkDetail(workId, false)
        .then(workData => {
            if(_.isEmpty(workData)) {
                return Promise.reject({msg: '作品不存在'});
            } else {
                return workData;
            }
        });
};

// 设置作品的点赞数
likeMethods.setLikeCountByWorkId = (workId, action) => {
    return TongrenWorksModel.getLikeCountByWorkId(workId)
        .then(likeCount => {
            // 设置作品的点赞数
            let count = likeCount;
            switch (action) {
                // 点赞
                case 'like':
                    count += 1;
                    break;
                // 取消点赞
                case 'unlike':
                    count -= 1;
                    break;
                default:
                    break;
            }
            return TongrenWorksModel.setLikeCountByWorkId(workId, count)
                .return(count);
        });
};

// 推送点赞的提醒消息
likeMethods.addLikeNotification = (workInfo, curUserId, createTime) => {
    let notificationType = NotificationsModel.EVENT_TYPES["LIKE_TONGREN_WORK"];
    let workTypeName = _.find(WORK_TYPE_MAP, {ID: workInfo.Type}).NAME;
    let messageObj = {
        Type: _.toString(workInfo.Type),
        TypeName: workTypeName,
        Title: workInfo.Title
    };

    let message = JSON.stringify(messageObj);
    return NotificationsModel.create({
            UserId: curUserId,
            TargetId: workInfo.AuthorId,
            Type: notificationType,
            RelateId: workInfo.ID,
            Message: message,
            CreateTime: createTime
        })
};