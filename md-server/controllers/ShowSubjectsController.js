var ShowSubjectsController = module.exports = {};
var errorHandler = require('../errorHandler');
var ShowSubjects = require('../../models/ShowSubjects');
var ShowTopics = require('../../models/ShowTopics');
var Contacts = require('../../models/Contacts');
var Users = require('../../models/Users');
var _ = require('lodash');
var util = require('../../common/util');
var Promise = require('bluebird');


/**
 * @api {get} /md/show_subjects/:subject_id/show_topics List subject showTopics
 * @apiName  ListSubjectShowTopics
 * @apiParam {subject_id} subject_id
 * @apiParam {Number} [page=1] 页码 最小为1
 * @apiParam {Number} [page_size=10] 分页大小 最大为10
 * @apiGroup ShowSubjects
 */
ShowSubjectsController.listShowTopics = function(req, res, next) {
  var subjectId = req.params.subject_id;
  var MAX_PAGE_SIZE = 10;
  var page;
  var pageSize;
  var curUserId;
  var sortBy;

  return req.paramsValidator
    .param('subject_id', {type: Number})
    .param('page', {type:Number, default:1, min:1})
    .param('page_size', {type:Number, default: MAX_PAGE_SIZE, min:1})
    .param('sort_by', {type:String, default: "new"})
    .validate()
    .then(function() {
      curUserId = req.params.userid;
      sortBy = req.params.sort_by;
      var subjectId = req.params.subject_id;
      page = req.params.page;
      pageSize = Math.min(req.params.page_size, MAX_PAGE_SIZE);
      var pagination = { page: page, pageSize: pageSize};
      if(sortBy === "hot") {
        return ShowSubjects.listShowTopics(subjectId, pagination, 'Hot');
      } else {
        return ShowSubjects.listShowTopics(subjectId, pagination, 'CreateTime');
      }
    })
    .then(function (showTopics) {
      var userIds = _.map(showTopics, 'UserId');
      return Users.getUsersByIds(userIds)
        .then(function (users) {
          return Contacts.getUsersWithFollowType(curUserId, users, 'FollowType');
        })
        .map(function (user) {
          require('../../common/data').md.setAvatarView(user, curUserId);
          user = _.pick(user, ['UserName', 'NickName', 'ID', 'Avatar', 'UserId', 'FollowType']);
          return user;
        })
        .then(function (users) {
          util.embeddedOn(showTopics, users, 'UserId', 'ID', 'User');
          return showTopics;
        });
    })
    .map(function (showTopic) {
      showTopic.ImgList = _.compact(_.split(showTopic.ImgList, ',')) || [];
      showTopic.Cover = _.first(showTopic.ImgList) || null;
      return showTopic;
    })
    .then(ShowTopics.Serializers.default)
    .then(function (showTopics) {
      return Promise.props({
        showTopics: showTopics,
        showSubject: ShowSubjects.findByIdForce(subjectId).then(ShowSubjects.getSubjectWithHot)
      });
    })
    .then(function(result) {
      res.succSend(result);
    }).catch(function(err) {
      errorHandler(err, req, res, next);
    });
};


function fillInfos(showTopics) {
  var embeddedTopicSize = 6;
  return Promise.resolve(showTopics)
    .map(function (showSubject) {
      return _.pick(showSubject, 'ID', 'Title', 'Content', 'ImgUrl', 'CreateTime');
    })
    .map(function (showSubject) {
      return ShowTopics.getLatestBySubjectId(showSubject.ID, embeddedTopicSize)
        .map(function (showTopic) {
          showTopic.Image = _.first(_.split(showTopic.ImgList, ',')) || null;
          showTopic = _.pick(showTopic, ['ID', 'SubjectId', 'UserId', 'Image', 'Desc'])
          return showTopic;
        })
        .then(function (showTopics) {
          showSubject.showTopics = showTopics;
          return showSubject;
        });
    })
    .map(function (showSubject) {
      return ShowSubjects.getSubjectWithHot(showSubject);
    })
    .then(function (showSubjects) {
      return showSubjects;
    });
}

/**
 * @api {get} /md/show_subjects List showSubjects
 * @apiName  ListShowSubjects
 * @apiParam {Number} [page=1] 页码 最小为1
 * @apiParam {Number} [page_size=10] 分页大小 最大为10
 * @apiParam {String="new","hot"} [sort_by=new] 分页大小 最大为10
 * @apiGroup ShowSubjects
 */
ShowSubjectsController.list = function (req, res, next) {
  var MAX_PAGE_SIZE = 10;
  var page, pageSize, sortBy;
  return req.paramsValidator
    .param('sort_by', {default: "new", values: ["hot", "new"]})
    .param('page', {type:Number, default:1, min:1})
    .param('page_size', {type:Number, default: MAX_PAGE_SIZE, min:1, max: MAX_PAGE_SIZE})
    .validate()
    .then(function () {
      page = req.params.page;
      pageSize = req.params.page_size;
      sortBy = req.params.sort_by;
      var pagination = { page: page, pageSize: pageSize};
      if(sortBy === "new") {
        return ShowSubjects.listNew(pagination)
      } else {
        return ShowSubjects.listHot(pagination)
      }
    })
    .then(fillInfos)
    .then(function (showSubjects) {
      res.succSend({showSubjects: showSubjects});
    }).catch(function (err) {
      errorHandler(err, req, res, next);
    })
};


/**
 * @api {get} /md/show_subjects/search 通过主题标题搜索炫耀主题
 * @apiName  showSubjectsSearch
 * @apiParam {Number} [page=1] 页码 最小为1
 * @apiParam {Number} [page_size=10] 分页大小 最大为10
 * @apiParam {String} keyword 关键词
 * @apiGroup ShowSubjects
 */
ShowSubjectsController.search = function (req, res, next) {
  var MAX_PAGE_SIZE = 10;
  var keyword, page, pageSize, curUserId;
  return req.paramsValidator
    .param('keyword', {type:String})
    .param('page', {type:Number, default:1, min:1})
    .param('page_size', {type:Number, default: MAX_PAGE_SIZE, min:1})
    .validate()
    .then(function () {
      curUserId = req.params.userid;
      keyword = req.params.keyword;
      page = req.params.page;
      pageSize = req.params.page_size;
      return ShowSubjects.searchByTitleLike(keyword, {page: page, pageSize: pageSize})
    })
    .then(fillInfos)
    .then(function(records) {
      res.succSend({showSubjects: records});
    }).catch(function(err) {
      errorHandler(err, req, res, next);
    });
};

/**
 * @api {get} /md/show_subjects/:subject_id 获取某个主题
 * @apiName  getShowSubject
 * @apiParam {subject_id} subject_id
 * @apiGroup ShowSubjects
 */
ShowSubjectsController.get = function (req, res, next) {
  return req.paramsValidator
    .param('subject_id', {type:String})
    .validate()
    .then(function () {
      var subjectId = req.params.subject_id;
      return ShowSubjects.findByIdForce(subjectId);
    })
    .then(s => ShowSubjects.getSubjectWithHot(s))
    .then(function(record) {
      res.succSend({showSubject: record});
    }).catch(function(err) {
      errorHandler(err, req, res, next);
    });
};


ShowSubjectsController.listSlideShow = function (req, res, next) {
  var query = ShowSubjects.normalScope().whereNotNull('SlideUrl');
  return ShowSubjects.executeByQuery(query)
    .then(function (rows) {
      return ShowSubjects.promiseMap(rows, s => ShowSubjects.getSubjectWithHot(s));
    })
    .then(function (rows) {
      res.succSend({showSubjects: rows});
    }).catch(function (err) {
      errorHandler(err, req, res, next);
    });
};
