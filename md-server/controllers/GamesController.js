module.exports = GamesController = {};
var Games = require('../../models/Games');
var errorHandler = require('../errorHandler');
var _ = require('lodash');
var Promise = require('bluebird');
const util = require('../../common/util');


/**
 * @api {get} /md/games/:game_name Get Game info
 * @apiName  GetGame
 * @apiGroup Games
 * @apiParam {String=qn} game_name game name
 * @apiParam {Array=roleJobs,servers} includes includes some resources like roleJobs and servers
 */
GamesController.get = function(req, res, next) {
  var gameName = req.params.game_name;
  var includeResources = req.params.includes;
  return req.paramsValidator
    .param('game_name', {values: ["qn", "qnm"]})
    .param('includes',{type: Array, required: false, allowJsonArray: true, subSet: ["roleJobs", "servers"]})
    .validate()
    .then(function() {
      return Games.findByGameName(gameName);
    })
    .then(function(game) {
      var result = {game: game};
      if(_.isString(includeResources)) {
        includeResources = JSON.parse(includeResources);
      }
      if(_.includes(includeResources, "servers")) {
        result.serverList = Games.getGroupedServers(gameName);
      }
      if(_.includes(includeResources, "roleJobs")) {
        result.roleJobs = Games.getRoleJobs(gameName);
      }
      return Promise.props(result);
    })
    .then(function(result) {
      var servers = result.servers;
      res.succSend(result);
    }).catch(function(err) {
      errorHandler(err, req, res, next);
    });
};
