var RealityShow = require('../../models/RealityShows');
var Users = require('../../models/Users');
var Contacts = require('../../models/Contacts');
var Moments = require('../../models/Moments');
var Photos = require('../../models/Photos');
var errorHandler = require('../errorHandler');
var _ = require('lodash');
var util = require('../../common/util');

var RealityShowController = {
  /**
   * @api {get} /md/reality_show/apply 申请真人秀验证
   * @apiName  applyRealityShow
   * @apiGroup RealityShow
   */
  apply: function(req, res, next) {
    var curUserId = req.params.userid;
    return Users.findByIdForce(curUserId)
      .then(function(user) {
        return RealityShow.applyAudit(user);
      })
      .then(function(realityShow) {
        res.succSend({realityShow: realityShow});
      })
      .catch(function(err) {
        errorHandler(err, req, res, next);
      });
  },

  /**
   * @api {get} /md/reality_shows 列出真人秀
   * @apiName  ListRealityShow
   * @apiParam {Number} [page=1] 页码 最小为1
   * @apiParam {Number} [page_size=10] 分页大小 最大为10
   * @apiParam {Number="new","hot"} [sort_by=new] 最新或者最热排序
   * @apiGroup RealityShow
   */
  list: function (req, res, next) {
    var MAX_PAGE_SIZE = 10;
    var page;
    var pageSize;
    var sortBy;
    var curUserId;
    var userIds;
    return req.paramsValidator
      .param('page', {type:Number, default:1, min:1})
      .param('page_size', {type:Number, default: MAX_PAGE_SIZE, min:1})
      .param('sort_by', {default: "new", values: ["hot", "new"]})
      .validate()
      .then(function() {
        page = req.params.page;
        pageSize = Math.min(req.params.page_size, MAX_PAGE_SIZE);
        sortBy = req.params.sort_by;
        curUserId = req.params.userid;
        var pagination = { page: page, pageSize: pageSize};
        if(sortBy === "new") {
          return RealityShow.listByNew(pagination);
        } else {
          return RealityShow.listByHot(pagination);
        }
      })
      .then(function (realityShows) {
        return RealityShowController.fillInfos(realityShows, curUserId);
      })
      .then(function(realityShows) {
        res.succSend({realityShows: realityShows});
      }).catch(function(err) {
        errorHandler(err, req, res, next);
      });

  },

  fillInfos: function (realityShows, curUserId) {
    var userIds = _.map(realityShows, 'UserId');
    return Users.getUsersByIds(userIds, ['ID', 'NickName', 'Province', 'City'])
      .then(function (users) {
        return Contacts.getUsersWithFollowType(curUserId, users, 'FollowType');
      })
      .then(function (users) {
        return Contacts.getUsersWithFollowInfo(users);
      })
      .then(function (users) {
        util.embeddedOn(realityShows, users, 'UserId', 'ID', 'UserInfo');
        return realityShows;
      })
      .map(function (realityShow) {
        return Moments.getUserLatestMoment(realityShow.UserId, ['ID', 'UserId', 'Text', 'ImgList'])
          .then(function (moment) {
            realityShow.Moment = moment;
            return realityShow;
          })
      })
      .map(function (realityShow) {
        return Photos.getUserRecentPhotos(realityShow.UserId, 6)
          .then(function (photos) {
            realityShow.RecentPhotos = photos;
            return realityShow;
          });
      })
      .map(function (r) {
        r.ZanList = util.csvStrToIntArray(r.ZanList);
        r.LikesCount = r.ZanList.length;
        r.IsUserLiked = _.includes(r.ZanList, curUserId);
        return r;
      })
  },

  /**
   * @api {get} /md/reality_shows/search 通过用户昵称搜索真人秀
   * @apiName  realityShowsSearch
   * @apiParam {Number} [page=1] 页码 最小为1
   * @apiParam {Number} [page_size=10] 分页大小 最大为10
   * @apiParam {String} keyword 关键词
   * @apiGroup RealityShow
   */
  search: function (req, res, next) {
    var MAX_PAGE_SIZE = 10;
    var keyword, page, pageSize, curUserId;
    return req.paramsValidator
      .param('keyword', {type:String})
      .param('page', {type:Number, default:1, min:1})
      .param('page_size', {type:Number, default: MAX_PAGE_SIZE, min:1})
      .validate()
      .then(function () {
        curUserId = req.params.userid;
        keyword = req.params.keyword;
        page = req.params.page;
        pageSize = req.params.page_size;
        return RealityShow.searchByNickNameLike(keyword, {page: page, pageSize: pageSize})
      })
      .then(function (realityShows) {
        return RealityShowController.fillInfos(realityShows, curUserId);
      })
      .then(function(realityShows) {
        res.succSend({realityShows: realityShows});
      }).catch(function(err) {
        errorHandler(err, req, res, next);
      });
  },


  /**
   * @api {get} /md/reality_shows/events 列出通过真人秀的事件
   * @apiName  realityShowsEvent
   * @apiGroup RealityShow
   */
  events: function (req, res, next) {
    var EVENT_SIZE = 4;
    return RealityShow.listEvents(EVENT_SIZE)
      .map(function (realityShow) {
        return _.assign(realityShow, {Duration: Date.now() - realityShow.AuditTime})
      })
      .then(function(realityShows) {
        res.succSend({realityShows: realityShows});
      }).catch(function(err) {
        errorHandler(err, req, res, next);
      });
  }

};

module.exports = RealityShowController;
