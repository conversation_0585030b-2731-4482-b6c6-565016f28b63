module.exports = UsersController = {};
var errorHandler = require('../errorHandler');
var Promise = require('bluebird');
var Users = require('../../models/Users');
var Contacts = require('../../models/Contacts');
var Photos = require('../../models/Photos');
var Games = require('../../models/Games');
var LeaveMessages = require('../../models/LeaveMessages');
var RealityShow = require('../../models/RealityShows');
var _ = require('lodash');
const request = require('../../common/request');
const util = require('../../common/util');
const ipUtil = require('../../common/ipUtil');
const UrsService = require('../../common/urs');
const config = require('../../common/config');
let logUtil = require('../../common/logger2')
let loginLog = logUtil.getLogger('md_login')

/**
 * @api {get} /md/games/:game_name/users/:user_id/home User home page info
 * @apiName UserHomePage
 * @apiParam {String=qn} game_name game name
 * @apiParam {Number} [recent_photos_size=8] Recent photos size. max size is 25
 * @apiGroup Users
 */
UsersController.home = function(req, res, next) {
  var curUserId = req.params.userid;
  var visitUserId = req.params.user_id;
  var gameName = req.params.game_name;

  return req.paramsValidator
    .param('user_id', {type: Number})
    .param('game_name', {type: String, values: ['qn']})
    .param('recent_visitor_size', {type: Number, default: 8})
    .param('recent_photos_size', {type: Number, default: 8})
    .validate()
    .then(function() {
      return Users.findByIdForce(visitUserId, ['ID', 'NickName', 'Birthday', 'Province',
        'City', 'Gender', 'GenderStatus', 'Avatar', 'AvaAuthStatus', 'VisitList', 'Signature'
      ]);
    }).then(function(user) {
      if(user && _.isDate(user.Birthday)) {
        user.Birthday = require('../../common/util').formatDate(user.Birthday.getTime(), "yyyy-MM-dd");
      }
      var recentPhotoSize = req.params.recent_photos_size;
      var recentVisitorSize = req.params.recent_visitor_size;
      var userPromise = Games.getUserWithMainRole(gameName, user, "RoleInfo")
        .then(RealityShow.fillUserWithRealityShow)
        .then(function(user) {
          return Users.setUserAvatar(user, curUserId);
        }).then(function(user) {
          return Contacts.getUserWithFollowType(curUserId, user, 'FollowType');
        }).then(function (user) {
          return _.omit(user, 'VisitList');
      });

      var recentVisitorPromise = Users.getRecentVisitorsByUser(user, recentVisitorSize)
        .then(function(users) {
          return Users.setUsersAvatar(users, curUserId);
        });

      return Promise.props({
        user: userPromise,
        recentVisitors: recentVisitorPromise,
        userFollowInfo: Contacts.getUserFollowInfo(user.ID),
        leaveMessagesCount: LeaveMessages.getCountByUserId(user.ID),
        userRecentPhotos: Photos.getUserRecentPhotos(user.ID, recentPhotoSize)
      });
    }).then(function(result) {
      Users.visit(curUserId, visitUserId);
      return res.succSend(result);
    }).catch(function(err) {
      errorHandler(err, req, res, next);
    });
};

/**
 * @api {get} /md/games/:game_name/users/recommend  Get User recommend users by game bind roles
 * @apiName  UserRecommendFriends
 * @apiParam {Array} role_ids recommend by bind role_ids
 * @apiParam {String=qn} game_name game name
 * @apiGroup Users
 */
UsersController.getRecommendUsersByGame = function(req, res, next) {
  var currentUserId = parseInt(req.params.userid);
  var gameName = req.params.game_name;
  var searchRoleIds;
  var isSubSet = function(testArray, targetArray) {
    return testArray.length === _.intersection(testArray, targetArray).length;
  };
  return req.paramsValidator
    .param('game_name', {type: String, values: ['qn', 'qnm']})
    .param('role_ids', {type: Array,  each: {type:Number}})
    .validate()
    .then(function() {
      return Games.getUserBindRoles(gameName, currentUserId);
    })
    .then(function(bindRoles) {
      var roleIds = _.map(bindRoles, 'RoleId');
      searchRoleIds = req.params.role_ids;
      if(!isSubSet(searchRoleIds, roleIds)) {
        return Promise.reject({errorType:"roleIdsInvalid", msg:"角色ID非法!"});
      }
      return Games.getFriendRoleInfosByRoleIds(gameName, searchRoleIds);
    })
    .then(function(roleInfos) {
      return UsersController.getUsersWithRoleInfos(gameName, roleInfos);
    })
    .then(function(users) {
      Users.setUsersAvatar(users, currentUserId);
      return _.reject(users, {ID: currentUserId});
    })
    .then(function(users) {
      return Contacts.getUsersWithFollowType(currentUserId, users);
    })
    .then(function(users) {
      return res.succSend({
        users: users
      });
    })
    .catch(function(err) {
      errorHandler(err, req, res, next);
    });
};


/**
 * @api {get} /md/games/:game_name/users/kwsearch Search User By User NickName or RoleName
 * @apiName  UserKwSearch
 * @apiParam {String=qn} game_name game name
 * @apiParam {String} type 关键字搜索的类型 0 梦岛昵称搜索 1 角色名称搜索
 * @apiParam {String} keyword 搜索关键字
 * @apiParam {Number} [page=1] 页码 最小为1
 * @apiParam {Number} [page_size=10] 分页大小 最大为10
 * @apiGroup Users
 */

var SearchTypes = {
  BY_USER_NAME: 0,
  BY_ROLE_NAME: 1
};

UsersController.kwSearch = function(req, res, next) {
  var gameName = req.params.game_name;
  var keyword = req.params.keyword;
  var searchType;
  var page;
  var pageSize;
  var MAX_PAGE_SIZE = 10;
  var curUserId = req.params.userid;
  return req.paramsValidator
    .param('game_name', {values: ['qn', 'qnm']})
    .param('type', {type:Number, values: SearchTypes})
    .param('keyword', {type:String})
    .param('page', {type:Number, default:1, min:1})
    .param('page_size', {type:Number, default: MAX_PAGE_SIZE, min:1})
    .validate()
    .then(function() {
      searchType = req.params.type;
      page = req.params.page;
      pageSize = Math.min(req.params.page_size, MAX_PAGE_SIZE);
      var paganation = { page: page, pageSize: pageSize};
      if(searchType === SearchTypes.BY_USER_NAME) {
        return Users.findUserNickNameLike(keyword, paganation)
          .then(function(users) {
            return Games.getUsersWithMainRole(gameName, users, 'RoleInfo');
          }).then(function(users) {
            return Contacts.getUsersWithFollowInfo(users);
          });
      } else if(searchType === SearchTypes.BY_ROLE_NAME) {
        var bindInfos;
        var roleInfos;
        return Games.searchBindRoleInfoByRoleName(gameName, keyword, paganation)
          .then(function(roleInfos) {
            return UsersController.getUsersWithRoleInfos(gameName, roleInfos);
          });
      }
    })
    .then(function(users) {
      return Contacts.getUsersWithFollowType(curUserId, users);
    })
    .then(function(users) {
      return Users.setUsersAvatar(users, curUserId);
    })
    .then(function(users) {
      return res.succSend({
        users: users
      });
    })
    .catch(function(err) {
      errorHandler(err, req, res, next);
    });
};


/**
 * @api {get} /md/games/:game_name/users/fuzzy_search fuzzy search user by role related infos
 * @apiName  UserFuzzySearch
 * @apiParam {String=qn} game_name game name
 * @apiParam {String} server_id 角色ServerId
 * @apiParam {Array}  [job_ids] jobIds
 * @apiParam {Array}  [level_range_ids] 角色等级范围ID
 * @apiParam {Number} [page=1] 页码 最小为1
 * @apiParam {Number} [page_size=10] 分页大小 最大为10
 * @apiGroup Users
 */
UsersController.fuzzySearch = function(req, res, next) {
  var jobIds;
  var levelRangeIds;
  var serverId;
  var gameName;
  var page;
  var pageSize;
  var MAX_PAGE_SIZE = 10;
  var curUserId = req.params.userid;
  return req.paramsValidator
    .param('server_id')
    .param('game_name', {type: String, values: ['qn', 'qnm']})
    .param('job_ids', {type:Array, allowJsonArray: true, required:false})
    .param('level_range_ids', {type:Array, allowJsonArray: true, required:false})
    .param('page', {type:Number, default:1, min:1})
    .param('page_size', {type:Number, default: MAX_PAGE_SIZE, min:1})
    .validate()
    .then(function() {
      serverId= req.params.server_id;
      jobIds = req.params.job_ids;
      levelRangeIds = req.params.level_range_ids;
      gameName = req.params.game_name;
      page = req.params.page;
      pageSize = Math.min(req.params.page_size, MAX_PAGE_SIZE);
      var paganation = { page: page, pageSize: pageSize};
      return Games.fuzzySearchRoleInfos(gameName, {
            serverId: serverId,
            jobIds: jobIds,
            levelRangeIds: levelRangeIds,
            paganation: paganation
        });
    })
    .then(function(roleInfos) {
      return UsersController.getUsersWithRoleInfos(gameName, roleInfos);
    })
    .then(function (users) {
      return Users.setUsersAvatar(users, curUserId);
    })
    .then(function(users) {
      return Contacts.getUsersWithFollowType(curUserId, users);
    })
    .then(function(users) {
      return res.succSend({
        users: users
      });
    })
    .catch(function(err) {
      errorHandler(err, req, res, next);
    });
};

UsersController.getUsersWithRoleInfos = function(gameName, roleInfos) {
  var roleIds = _.map(roleInfos, 'RoleId');
  var bindInfos;
  return Games.getBindInfosByRoleIds(gameName, roleIds)
    .then(function(curBindInfos) {
      bindInfos = curBindInfos;
      var userIds = _.map(bindInfos, 'UserId');
      return Users.getUsersByIds(userIds, ['ID', 'Avatar', 'Province', 'City', 'NickName', 'AvaAuthStatus', 'Gender'])
        .then(Users.Serializers.normalUsers)
        .then(_.partial(Users.getUsersWithRoleInfos, _.partial.placeholder, roleInfos, bindInfos));
    })
    .then(function(users) {
      return Contacts.getUsersWithFollowInfo(users);
    });
};



function formatUrsApiInfo(info) {
  const lines = _.split(info, "\n");
  const extraInfo = lines[2];
  const recentLoginInfo = util.getJsonInfo(extraInfo);
  _.forEach(recentLoginInfo, info => {
    if(info.ipAddress) {
      info.ipLocation = ipUtil.getLocationFromIp(info.ipAddress);
    }
  });
  return {code:lines[0], msg:lines[1], extraInfo:recentLoginInfo};
}

UsersController.userLogin = function (req, res, next) {
  return UrsService.ulogin(req, res).catch(err => {
    const userIp = util.getIp(req).replace('::ffff:', '');
    loginLog.error('UserLoginFailed', { params: req.params, err: err, ua: req.headers['user-agent'], ip: userIp })
    errorHandler(err, req, res, next)
  });
};

UsersController.ngpLogin = function (req, res, next) {
  const userIp = util.getIp(req).replace('::ffff:', '');

  let user = require('../../service/app/user');
  return user.login(req, res).spread( (info, option) => {
    if (info.code) {
      return Promise.reject(info.msg);
    }

    let urs = option.urs;
    return Promise.all([
      info,
      UrsService.getRiskInfo(userIp, urs),
      UrsService.getMiBao(userIp, urs)
    ])
  }).spread( (mdInfo, riskInfo, miBao) => {
    return res.succSend({ MdInfo: mdInfo, UrsInfo: riskInfo, MbInfo: miBao });
  }).catch(err => {
    errorHandler(err, req, res, next);
  });
};

UsersController.getRecentLoginInfo = function (req, res, next) {
  const urs =  req.params.urs;
  return request.get("http://***********:10004/services/getRecentLoginInfo", {username:urs})
    .then(formatUrsApiInfo)
    .then(info => {
      res.succSend(info.extraInfo);
    }).catch(err => {
      errorHandler(err, req, res, next);
    })
};

UsersController.queryUserSecInfoStatus = function (req, res, next) {
  const urs =  req.params.urs;
  return UrsService.queryUserSecInfoStatus({username: urs, product: "NGP"})
  .then(info => {
    res.succSend(info.extraInfo);
  }).catch(err => {
    errorHandler(err, req, res, next);
  })
};

UsersController.verifyintegritymibao2 = function (req, res, next) {
  const urs =  req.params.urs;
  return UrsService.verifyintegritymibao2({username: urs, product: "NGP"})
  .then(info => {
    res.succSend(info.extraInfo);
  }).catch(err => {
    errorHandler(err, req, res, next);
  })
};
