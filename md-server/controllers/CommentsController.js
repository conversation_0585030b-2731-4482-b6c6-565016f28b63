/**
 * Created by <PERSON><PERSON><PERSON> on 16-9-27.
 */


var errorHandler = require('../errorHandler');
var NewComments = require('../../models/NewComments');
var util = require('../../common/util');
var _ = require('lodash');
let Promise = require('bluebird');


var CommentsController = {
  checkResourceEntity: function (resourceModel, id) {
    return resourceModel.findOne({ID: id, Status: resourceModel.Statuses.NORMAL})
      .then(function (r) {
        if(r) {
          return r;
        } else {
          return Promise.reject({errorType: "EntityNotFound", msg: "评论的实体未找到"});
        }
      });
  },

  /**
   * @api {get} /md/comments/new 评论某个资源
   * @apiName  CommentResource
   * @apiParam {String=show_topics, photos} resource_type 资源类型
   * @apiParam {Number} resource_id 资源id
   * @apiParam {Number} reply_id 回复Id
   * @apiGroup Comments
   */
  create: function (req, res, next) {
    var params;
    var ResourceModel, curUserId;

    return req.paramsValidator
      .param('resource_type', {type: String, values: ["show_topics", "photos"]})
      .param('resource_id', {type: Number})
      .param('text', {type: String, maxlen: 150})
      .param('reply_id', {type: Number, required: false})
      .validate()
      .then(function () {
        params = req.params;
        curUserId = req.params.userid;
        var resourceType = _.camelCase(req.params.resource_type);
        ResourceModel = require(`../../models/${util.capitalizeFirstLetter(resourceType)}`);
        return CommentsController.checkResourceEntity(ResourceModel, params.resource_id);
      })
      .then(function () {
        if(params.reply_id) {
          return ResourceModel.replyComment(curUserId, params.resource_id, params.reply_id, params.text);
        } else {
          return ResourceModel.comment(curUserId, params.resource_id, params.text);
        }
      })
      .then(function (record) {
        record.Duration = Date.now() - record.CreateTime;
        res.succSend(record)
      }).catch(function (err) {
        errorHandler(err, req, res, next);
      })

  },

  checkDeletePermission: function (curUserId, commentId) {
    let ShowTopics = require('../../models/ShowTopics');
    return NewComments.findByIdForce(commentId)
      .then(function (record) {
        let commentTarget;
        if(record.TargetType === "md_show_topic") {
          commentTarget = ShowTopics.findById(record.TargetId, ["ID", "UserId"])
        }
        return Promise.props({
          comment: record,
          commentTarget: commentTarget
        })
      }).then(function (info) {
        let canDelete;
        if(info.commentTarget && info.commentTarget.UserId == curUserId) {
          canDelete = true;
        } else if (info.comment.UserId == curUserId) {
          canDelete = true;
        } else {
          canDelete = false;
        }
        if(!canDelete) {
          return Promise.reject({errorType: "PermissionDenied", msg: "没有删除权限"});
        }
      });
  },

  /**
   * @api {get} /md/comments/:comment_id/delete 删除某个评论
   * @apiName  deleteComment
   * @apiGroup Comments
   */
  delete: function (req, res, next) {
    var curUserId, commentId;
    let ShowTopics = require('../../models/ShowTopics');
    let Notifications = require('../../models/Notifications');
    return req.paramsValidator
      .param('comment_id', {type: Number})
      .validate()
      .then(function () {
        curUserId = req.params.userid;
        commentId = req.params.comment_id;
      })
      .then(function () {
        return CommentsController.checkDeletePermission(curUserId, commentId)
      })
      .then(function () {
        return NewComments.softDeleteById(commentId)
      })
      .then(function (r) {
        if(r.TargetType === ShowTopics.tableName) {
          ShowTopics.updateHotWhenDeleteComment(r.TargetId);
          Notifications.softDeleteByCondition({UserId: r.UserId, Type: [
            Notifications.EVENT_TYPES.COMMENT_SHOW_TOPIC,
            Notifications.EVENT_TYPES.REPLY_SHOW_TOPIC
          ], RelateId: r.ID})
        } else if(r.TargetType === "md_photo") {
          Notifications.softDeleteByCondition({UserId: r.UserId, Type: [
            Notifications.EVENT_TYPES.COMMENT_PHOTO,
            Notifications.EVENT_TYPES.REPLY_PHOTO
          ], RelateId: r.ID})
        }
        res.succSend({msg: "删除成功"})
      }).catch(function (err) {
        errorHandler(err, req, res, next);
      })

  }
};

module.exports = CommentsController;
