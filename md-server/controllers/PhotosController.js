var PhotosController = {};
var PhotoAlbums = require('../../models/PhotoAlbums');
var Photos = require('../../models/Photos');
var Likes = require('../../models/Likes');
var Users = require('../../models/Users');
var NosFileUsage = require('../../models/NosFileUsage');
var NComments = require('../../models/NewComments');
var errorHandler = require('../errorHandler');
var errors = require('../../common/errors');
var Auth = require('../../common/auth');
var ParamsValidator = require('../../common/paramsValidator');
var Promise = require('bluebird');
var logger = require('../../common/logger');
var _ = require('lodash');
var util = require('../../common/util');
var Constants = require('../../common/data').Constants;


/**
 * @api {get} /md/photos/new Create photo
 * @apiName  CreatePhotos
 * @apiGroup Photos
 * @apiParam {String} [url] photo url
 * @apiParam {Array} [urls] photo urls array
 */
PhotosController.create = function(req, res, next) {
  var url = req.params.url;
  var urls = req.params.urls;
  var albumId = req.params.album_id;
  var userId = req.params.userid;
  return ParamsValidator
    .from(req.params)
    .param('album_id', {required: false})
    .param('url', {type: String, required: false, startsWith: "http://hi-163-common.nosdn.127.net"})
    .param('urls', {type: Array, required: false, allowJsonArray: true, maxSize:25})
    .requiredAny(['url', 'urls'])
    .validate()
    .then(function() {
      if(albumId) {
        return PhotoAlbums.checkUserOwnAlbum(userId, albumId);
      } else {
        return PhotoAlbums.createUserDefaultAlbumIfNotExist(userId);
      }
    }).then(function(album) {
      albumId = album.ID;
      urls = urls || [];
      if(url) {
        urls.push(url);
      }
      var propList = urls.map(function(url) {
        return { Url: url, PhotoAlbumId: album.ID};
      });
      return Photos.createBatch(propList);
    }).then(function(photos) {
      return Photos.auditPhotos(photos);
    }).then(function(photos) {
      PhotoAlbums.updateById(albumId, {AuditStatus: Constants.STATUS_AUDIT_INIT});
      Photos.addFreshnessToRedisBatch(photos);
      photos = Photos.addAuditingWaterMarkToPhotos(photos);
      if(photos.length === 1) {
        res.succSend({photo: photos[0]});
      } else {
        res.succSend({photos: photos});
      }
    }).catch(function(err) {
      errorHandler(err, req, res, next);
    });
};

PhotosController.checkUserOwnPhoto = function(userId, photoId) {
  return Photos.findByIdForce(photoId).then(function(photo) {
    return PhotoAlbums.findByIdForce(photo.PhotoAlbumID);
  }).then(function(album) {
    if(!PhotoAlbums.isUserOwnAlbum(album, userId)) {
      return Promise.reject({errorType:"PhotoOwnedByOther", msg:"该图片不是您自己的照片!"})
    }
  });
};

PhotosController.update = function(req, res, next) {
  var userId = req.params.userid;
  var photoId = req.params.photo_id;
  var name = req.params.name;

  return ParamsValidator
    .from(req.params)
    .param('name', {type: String, required: false, maxlen:7})
    .validate()
    .then(function() {
      return PhotosController.checkUserOwnPhoto(userId, photoId);
    })
    .then(function() {
      return Photos.updateById(photoId, {Name: name});
    }).then(function(photo) {
      res.succSend({photo: photo});
    }).catch(function(err) {
      errorHandler(err, req, res, next);
    });
};

/**
 * @api {get} /md/photos/:photo_id/delete Delete photo
 * @apiName  DeletePhoto
 * @apiGroup Photos
 * @apiParam {Number} photo_id photo id
 */
PhotosController.delete = function(req, res, next) {
  var photoId = req.params.photo_id;
  return ParamsValidator.from(req.params).param('photo_id').validate().then(function() {
    req.params.ids = [photoId];
    return PhotosController.batchDelete(req, res, next);
  });
};

/**
 * @api {get} /md/photos/delete Delete photos
 * @apiName  DeletePhotos
 * @apiGroup Photos
 * @apiParam {Array} ids photos ids
 */
PhotosController.batchDelete = function(req, res, next) {
  var userId = req.params.userid;
  var ids = req.params.ids;
  if(_.isString(ids)) {
    ids = util.getJsonInfo(ids, []);
  }
  return ParamsValidator.from(req.params).param('ids').validate().then(function() {
    return Photos.checkIdsForUserDelete(userId, ids);
  }).then(function() {
    return Photos.softDeleteByIds(ids);
  }).then(function(photos) {
    var albumIds = _.uniq(_.map(photos, 'PhotoAlbumID'));
    PhotoAlbums.tryToTransAuditing(albumIds);
    res.succSend({msg:"OK"});
  }).catch(function(err) {
    errorHandler(err, req, res, next);
  });
};


PhotosController.move = function(req, res, next) {
  var photoId = req.params.id;
  var targetAlbumId = req.params.to_album_id;
  return Photos.move(photoId, targetAlbumId).then(function(photo) {
    res.succSend({photo: photo});
  }).catch(function(err) {
    errorHandler(err, req, res, next);
  });
};


PhotosController.checkBatchMoveValidation = function(userId, ids, targetAlbumId) {
  return PhotoAlbums.findByIdForce(targetAlbumId).then(function(album) {
    if(!PhotoAlbums.isUserOwnAlbum(album, userId)) {
      return Promise.reject({msg: "Target album owned by others"});
    } else if(PhotoAlbums.isMomentAlbum(album)) {
      return Promise.reject({msg: "Moment album can't be moved in"});
    } else if(PhotoAlbums.isAvatarAlbum(album)) {
      return Promise.reject({msg: "Avatar album can't be moved in"});
    }
  }).then(function() {
    return Photos.getAlbumsByPhotoIds(ids);
  }).then(function(albums) {
    if(!PhotoAlbums.isUserOwnAlbums(albums, userId)) {
      return Promise.reject({msg: "Photos Owned by others"});
    }
  });
};

/**
 * @api {get} /md/photos/move Move photos
 * @apiName  MovePhotos
 * @apiGroup Photos
 * @apiParam {Array} ids photos ids
 * @apiParam {Number} to_album_id album id for moved in
 */
PhotosController.batchMove = function(req, res, next) {
  var userId = req.params.userid;
  var ids = req.params.ids;
  if(_.isString(ids)) {
    ids = util.getJsonInfo(ids, []);
  }
  var targetAlbumId = req.params.to_album_id;
  return PhotosController.checkBatchMoveValidation(userId, ids, targetAlbumId).then(function() {
    Photos.findByIds(ids).then(function(photos) {
      PhotoAlbums.tryToTransAuditingWhenMove(photos, targetAlbumId);
    });
    return Photos.moveBatch(ids, targetAlbumId);
  }).then(function(photos) {
    res.succSend({photos: photos});
  }).catch(function(err) {
    errorHandler(err, req, res, next);
  });
};

PhotosController.asCover = function(req, res, next) {
  var userId = req.params.userid;
  var photoId = req.params.photo_id;
  var photo;

  return PhotosController.checkUserOwnPhoto(userId, photoId)
    .then(function() {
      return Photos.findByIdForce(photoId)
    }).then(function(record) {
      photo = record;
      if(photo.AuditStatus === Photos.AUDIT_STATUSES.AUDITING) {
        return Promise.reject({errorType:"PhotoInvalidAsCover", msg: "审核中的图片不能设为封面"})
      }
      return PhotoAlbums.findByIdForce(photo.PhotoAlbumID);
    }).then(function(album) {
      return PhotoAlbums.updateById(album.ID, {CoverUrl: photo.Url});
    }).then(album => {
      res.succSend({album: album});
    }).catch(function(err) {
      errorHandler(err, req, res, next);
    });
};

PhotosController.checkViewPhotoValidForUser = function (photoId, userId) {
  var curPhoto;
  var curAlbum;
  return Photos.findByIdForce(photoId).then(function(photo) {
    curPhoto = photo;
    return PhotoAlbums.findByIdForce(curPhoto.PhotoAlbumID);
  })
  .then(function (album) {
    curAlbum = album;
    return PhotoAlbums.hasViewPermission(album, userId);
  })
  .then(function (hasPermission) {
    if(!hasPermission) {
      return Promise.reject({errorType: "NoPermissionToViewPhoto", msg:"权限不足，无法查看该照片!"})
    } else {
      return curAlbum;
    }
  })
  .then(function(album) {
    if(PhotoAlbums.isUserOwnAlbum(album, userId)) {
      return {
        photo: curPhoto,
        album: curAlbum,
        isUserOwnPhoto: true
      }
    } else {
      if(![Constants.STATUS_AUDIT_PASS, Constants.STATUS_AUDIT_PICK].includes(curPhoto.AuditStatus)) {
        return Promise.reject({errorType: "PhotoNotFound", msg: "图片未存在！"})
      } else {
        return {
          photo: curPhoto,
          album: curAlbum,
          isUserOwnPhoto: false
        }
      }
    }
  })
};

/**
 * @api {get} /md/photos/:photo_id Get photo
 * @apiName  GetPhoto
 * @apiGroup Photos
 * @apiParam {Number} photo_id photo id
 * @apiParam {String="album_photos, adjacent_ids"} [include] adjacent_ids 返回相邻的图片ID album_photos 返回所在相册所有图片
 */
PhotosController.get = function(req, res, next) {
  var photoId = req.params.photo_id;
  var include = req.params.include;
  var curUserId = req.params.userid;
  var isUserOwnPhoto = false;
  var photoAlbum;
  var curPhoto;

  return ParamsValidator
    .from(req.params)
    .param('photo_id')
    .param('include', {required: false, type: String, values: ["album_photos", "adjacent_ids"]})
    .validate()
    .then(function() {
      return PhotosController.checkViewPhotoValidForUser(photoId, curUserId);
    }).then(function(result) {
      var photo = result.photo;
      curPhoto = result.photo;
      isUserOwnPhoto = result.isUserOwnPhoto;
      photoAlbum = result.album;
      return Photos.getPhotoWithLikesCount(photo);
    })
      .then(function(photo) {
        var photoOwner = Users.getUser(photoAlbum.UserId).then(function(user) {
          return Users.setUserAvatar(user, curUserId);
        });
        var promiseProps = {
          album: PhotoAlbums.findById(photo.PhotoAlbumID),
          photoOwner: photoOwner,
          photo: photo
        };
        if(curUserId) {
          promiseProps.isUserOwnPhoto = isUserOwnPhoto;
          promiseProps.isUserLikePhoto = Likes.isUserLikePhoto(curUserId, photo);
        }
        if(include === "album_photos") {
          var query = Photos.scope()
            .where({PhotoAlbumID: photoAlbum.ID})
            .where({Status: Photos.Statuses.NORMAL})
            .orderBy('CreateTime', 'desc');

          if(isUserOwnPhoto) {
            query = query.whereNot({AuditStatus: Photos.AUDIT_STATUSES.REJECT});
          } else {
            query = query.whereIn('AuditStatus', [Constants.STATUS_AUDIT_PASS, Constants.STATUS_AUDIT_PICK]);
          }

          var getAlbumPhotos = Photos.executeByQuery(query).then(function(photos) {
            return Promise.map(photos, function(photo) {
              return Likes.getPhotoLikesCount(photo.ID).then(function(likesCount) {
                photo.LikesCount = likesCount;
                return photo;
              });
            });
          });
          promiseProps.albumPhotos = getAlbumPhotos;
        } else if(include === "adjacent_ids") {
          var query = Photos.scope()
            .where({PhotoAlbumID: photoAlbum.ID})
            .where({Status: Photos.Statuses.NORMAL});

          if(isUserOwnPhoto) {
            query = query.whereNot({AuditStatus: Photos.AUDIT_STATUSES.REJECT});
          } else {
            query = query.whereIn('AuditStatus', [Constants.STATUS_AUDIT_PASS, Constants.STATUS_AUDIT_PICK]);
          }

          var getAdjacentIds = Photos.executeByQuery(query).then(function(photos) {
            return Photos.getAdjacentIdsInPhotos(photos, curPhoto);
          });


          promiseProps.adjacentIds = getAdjacentIds;
        }

        function getComments() {
          let comments;
          return Photos.getComments(curPhoto.ID)
          .then(data => {
            comments = data;
            const userIds = NComments.getRelateUserIds(comments);
            return Users.findByIds(userIds, ['ID', 'NickName', 'Avatar', 'AvaAuthStatus'])
          }).then(users => {
            _.forEach(users, user => {
              require('../../common/data').md.setAvatarView(user, curUserId);
            });
            util.embeddedOn(comments, users, 'UserId', 'ID', 'UserInfo');
            util.embeddedOn(comments,  users, 'ReplyId', 'ID', 'ReplyUserInfo');
            return comments;
          })
        }


        promiseProps.comments = getComments();
        return Promise.props(promiseProps);
      })
      .then(function(result) {
        result.photo = Photos.formatAuditStatus(Photos.addAuditingWaterMarkToPhotoIfNeed(result.photo));
        result.albumPhotos = Photos.formatAuditStatus(Photos.addAuditingWaterMarkToPhotos(result.albumPhotos));
        res.succSend(result);
      }).catch(function(err) {
        errorHandler(err, req, res, next);
      });
};

/**
 *
 * @api {get} /md/photos/qn/listPicks 列出为倩女端游挑选的图片
 * @apiName  listQnPickPhotos
 * @apiGroup QnPickPhoto
 */
PhotosController.listPicksForQn = function (req, res, next) {
  return req.paramsValidator
    .param('type', {values: ['HandPaint'], required: false})
    .validate()
    .then(() => {
      return require('../services/qnPickPhoto').getPickPhotos(req.params.type)
    })
    .then(data => {
      res.succSend(data)
    }).catch(err => {
      errorHandler(err, req, res, next)
    })
}

/**
 * @api {post} /md/photos/qn/pick 选择倩女端游图片上官网
 * @apiName  QnPhotoPick
 * @apiGroup QnPickPhoto
 * @apiParam {String="TongRen","ShowTopic", "Photo", "RealityShow"} pickType pick的类型
 * @apiParam {String} url 选择的图片url地址
 * @apiParam {Number} relatedId 关联的id， 同人贴是同人贴id， 炫耀帖为炫耀帖id， 图片类型为图片id， 真人秀为用户UserId
 */
PhotosController.pickPhoto = function (req, res, next) {
  const QnPickPhotoService = require('../services/qnPickPhoto')
  const pickTypes = _.values(QnPickPhotoService.PickTypes)
  return req.paramsValidator
  .param('pickType', {values: pickTypes})
  .param('url', {textType: 'url'})
  .param('relatedId', {type: Number})
  .validate()
  .then(() => {
    const params = req.params
    return QnPickPhotoService.pickPhoto(params)
  }).then(data => {
    res.succSend(data)
  }).catch(err => {
    errorHandler(err, req, res, next)
  })
}

module.exports = PhotosController;
