/**
 * Created by zhen<PERSON> on 16-9-8.
 */

var Moments = require('../../models/Moments');
var Comments = require('../../models/Comments');
var Contacts = require('../../models/Contacts');
var Users = require('../../models/Users');
var RealityShow = require('../../models/RealityShows');
var errorHandler = require('../errorHandler');
var _ = require('lodash');
var Promise = require('bluebird');
var { getRedis } = require('../../common/redis');
const util = require('../../common/util');


var MomentsController = {
  setDurationForMoments: function (moments) {
    var now = Date.now();
    moments.forEach(function (moment) {
      moment.duration = now - moment.CreateTime;
    });
    return moments;
  },

  setCommentsCountForMoments: function (moments) {
    const HotMoments = require('../services/HotMoments');
    return Promise.mapSeries(moments, function (moment) {
      return HotMoments.getHotState(moment.ID).then(state => {
        moment.comsNum = state.comment + state.reply;
        return moment;
      })
    });
  },

  findMomentsRelatedUsers: function (moments) {
    var userIds = _.reduce(moments, function (userIds, moment) {
      var zanUserIds = _.compact(_.split(moment.ZanList, ','));
      return _.concat(userIds, moment.UserId, zanUserIds);
    }, []).map(_.toNumber);
    return Users.findByIds(userIds, ['ID', 'Avatar', 'AvaAuthStatus', 'NickName', 'Gender'])
      .then(Users.Serializers.normalUsers)
      .then(RealityShow.fillUsersWithRealityShow)
  },

  setUsersAvatarView: function (users, curUserId) {
    return _.map(users, function (user) {
      require('../../common/data').md.setAvatarView(user, curUserId);
      return user;
    });
  },

  setUsersFollowTypeWithVisitorId: function (users, visitorId) {
    if(visitorId) {
      return Contacts.getUsersWithFollowType(visitorId, users, 'FollowType');
    } else {
      return Promise.resolve(users);
    }
  },

  setMomentsImgListView: function (moments, curUserId) {
    return moments.map(function (moment) {
      require('../../common/data').md.setImgListView(moment, curUserId);
      return moment;
    })
  },

  setUserInfoAndZanUserInfo: function (moments, users) {
    var userIdToUser = _.reduce(users, function (userIdToUser, user) {
      userIdToUser[user.ID] = user;
      return userIdToUser;
    }, {});

    _.forEach(moments, function (moment) {
      moment.userInfo = userIdToUser[moment.UserId];
      var zanUserIds = _.compact(_.split(moment.ZanList, ','));
      moment.zanlist = zanUserIds.map(function (userId) {
        return {
          zanUserid: userId,
          userInfo: userIdToUser[userId]
        }
      })
    });
    return moments;
  },


  getHotMoments: function (pagination, category) {
    const HotMomentService = require('../services/HotMoments');
    return HotMomentService.getHotMoments(category).then(moments => {
      const chunks = _.chunk(moments || [], pagination.pageSize);
      const page = pagination.page;
      if(page > chunks.length) {
        return [];
      } else {
        return chunks[page - 1];
      }
    }).then(moments => {
      const ids = _.map(moments, 'ID');
      return Moments.findByIds(ids, ['ID', 'ZanList']).then(zanInfo => {
        const idToZanList = util.keyToRecordHash(zanInfo, 'ID');
        moments.forEach(m => {
          m.ZanList = idToZanList[m.ID].ZanList;
        });
        return moments;
      });
    });
  },


  /**
   * @api {get} /md/moments/hot 列出热门新鲜事
   * @apiName  listHotMoment
   * @apiGroup Moment
   */
  listHot: function (req, res, next) {
    var self = this;
    var params = req.params;
    var curUserId = params.userid;
    var category;
    var isLogin = !!curUserId;

    return req.paramsValidator
      .param('page', {type: Number, required: false, min:1, default:1})
      .param('page_size', {type: Number, required: false, max:12, default:12})
      .param('category', {type: String, required: false, values: ["qn", "qnm"]})
      .validate()
      .then(function () {
        const category = req.params.category;
        return self.getHotMoments({page: params.page, pageSize: params.page_size}, category);
      })
      .then(moments => {
        _.forEach(moments, m=> {
          m.Text = Moments.getFormattedTextInMoment(m)
        });
        return moments;
      })
      .then(self.setDurationForMoments)
      .then(self.setCommentsCountForMoments)
      .then(function (moments) {
        return self.setMomentsImgListView(moments, curUserId);
      })
      .then(function (moments) {
        return self.findMomentsRelatedUsers(moments)
          .then(function (users) {
            return self.setUsersAvatarView(users, curUserId);
          })
          .then(function (users) {
            return self.setUsersFollowTypeWithVisitorId(users, curUserId);
          })
          .then(function (users) {
            return self.setUserInfoAndZanUserInfo(moments, users);
          })
      })
      .then(function (moments) {
        if(isLogin) {
          _.forEach(moments, function (moment) {
            moment.Type = _.get(moment, 'userInfo.FollowType') || Contacts.FollowType.NO_FOLLOW; // keep compatible with old style api
          })
        }
        return moments;
      })
      .then(function (moments) {
        res.succSend({
          list: moments,
          login: isLogin,
        });
      }).catch(function (err) {
        errorHandler(err, req, res, next);
      })
  }
};


module.exports = MomentsController;
