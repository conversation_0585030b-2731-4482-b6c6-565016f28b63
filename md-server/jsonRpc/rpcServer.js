const jayson = require('jayson/promise')
const ModelManager = require('../../models/ModelManager.js')
const QnRoleInfo = ModelManager.getModelByTableName('qn_roleinfo')
const QnmRoleInfo = ModelManager.getModelByTableName('qnm_roleinfo')
const MdMoment = ModelManager.getModelByTableName('md_moment')
const PyqMoment = ModelManager.getModelByTableName('pyq_moment')
const bluebird = require('bluebird')
const _ = require('lodash')

module.exports = jayson.server({
  listQnRolesByUrs: function (args) {
    const [urs] = args
    return QnRoleInfo.findByUserName(urs)
  },

  listQnmRolesByUrs: function (args) {
    const [urs] = args
    return QnmRoleInfo.find({UserName: urs})
      .then(QnmRoleInfo.getRoleInfosWithServerAndJob)
  },

  'qn:findByRoleIds': function (args) {
    const [roleids] = args
    return QnRoleInfo.findByRoleIds(roleids)
  },

  'qnm:findByRoleIds': function (args) {
    const [roleids] = args
    return QnmRoleInfo.findByRoleIds(roleids)
  },

  'urs:getUrsByRoleId': function (args) {
    const [product, roleid] = args
    return bluebird.resolve()
      .then(() => {
        if (product === 'qn') {
          return QnRoleInfo.findOne({RoleId: roleid}, ['UserName'])
        } else {
          return QnmRoleInfo.findOne({RoleId: roleid}, ['UserName'])
        }
      }).then(info => {
        return _.get(info, 'UserName') || ''
      })
  },

  'qn:addmoment': function (args) {
    const [roleid, text] = args
    const BindRole = require('../../models/BindRoles')
    return BindRole.getUserIdByQnRoleId(roleid)
      .then(userId => {
        if (userId) {
          return MdMoment.insert({UserId: userId, Text: text, CreateTime: Date.now()})
        } else {
          return {code: -1, msg: '当前角色未绑定梦岛'}
        }
      })
  },

  'qnm:addmoment': function (args) {
    const [roleid, text] = args
    return PyqMoment.insert({RoleId: roleid, Text: text, CreateTime: Date.now()})
  }

})
