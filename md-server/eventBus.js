const EventEmitter = require('events')
const MAX_LISTENERS = 20

// define constants like this,  just make IDE intelligence happy
const Events = {}
Events.LIKE_MOMENT = 'LIKE_MOMENT'
Events.CANCEL_LIKE_MOMENT = 'CA<PERSON><PERSON>_LIKE_MOMENT'

Events.CREATE_MOMENT_COMMENT = 'CREATE_MOMENT_COMMENT'
Events.REPLY_MOMENT_COMMENT = 'REPLY_MOMENT_COMMENT'
Events.DELETE_MOMENT_COMMENT = 'DELETE_MOMENT_COMMENT'

Events.CREATE_MESSAGE = 'CREATE_MESSAGE'
Events.DELETE_MESSAGE = 'DELETE_MESSAGE'

Events.CREATE_MESSAGE_ANSWER = 'CREATE_MESSAGE_ANSWER'
Events.REPLY_MESSAGE_ANSWER = 'REPLY_MESSAGE_ANSWER'
Events.DELETE_MESSAGE_ANSWER = 'DELETE_MESSAGE_ANSWER'

Events.FOLLOW_USER = 'FOLLOW_USER'
Events.CANCEL_FOLLOW_USER = 'CANCEL_FOLLOW_USER'
Events.ADD_MOMENT_TO_TOPIC = 'ADD_MOMENT_TO_TOPIC'

Events.DELETE_PHOTO = 'DELETE_PHOTO'

class EventBusClass extends EventEmitter {
  constructor(option) {
    super()
    if (option.maxListeners) {
      this.setMaxListeners(option.maxListeners)
    }
    this.Events = Events
  }
}

const EventBus = new EventBusClass({
  maxListeners: MAX_LISTENERS
})

// 在整个app启动的时候会被调用， 确保模块间的消息通行正常
EventBus.registerAllListeners = function () {
  const notifications = require('./services/notifications')
  EventBus.on(Events.DELETE_MOMENT, notifications.deleteMomentEventHandler)
  EventBus.on(Events.LIKE_MOMENT, notifications.likeMomentEventHandler)
  EventBus.on(Events.CANCEL_LIKE_MOMENT, notifications.cancelLikeMomentEventHandler)

  EventBus.on(Events.CREATE_MOMENT_COMMENT, notifications.createMomentCommentEventHandler)
  EventBus.on(Events.REPLY_MOMENT_COMMENT, notifications.replyMomentCommentEventHandler)
  EventBus.on(Events.DELETE_MOMENT_COMMENT, notifications.deleteMomentCommentEventHandler)

  EventBus.on(Events.CREATE_MESSAGE, notifications.createMessageEventHandler)
  EventBus.on(Events.DELETE_MESSAGE, notifications.deleteMessageEventHandler)

  EventBus.on(Events.CREATE_MESSAGE_ANSWER, notifications.createMessageAnswerEventHandler)
  EventBus.on(Events.REPLY_MESSAGE_ANSWER, notifications.replyMessageAnswerEventHandler)
  EventBus.on(Events.DELETE_MESSAGE_ANSWER, notifications.deleteMessageAnswerEventHandler)

  EventBus.on(Events.FOLLOW_USER, notifications.followUserEventHandler)
  EventBus.on(Events.CANCEL_FOLLOW_USER, notifications.cancelFollowUserEventHandler)

  EventBus.on(Events.DELETE_PHOTO, notifications.deletePhotoEventHandler)

  const HotMomentsCache = require('./services/HotMoments')
  EventBus.on(Events.CREATE_MOMENT_COMMENT, HotMomentsCache.createMomentCommentEventHandler)
  EventBus.on(Events.REPLY_MOMENT_COMMENT, HotMomentsCache.replyMomentCommentEventHandler)
  EventBus.on(Events.DELETE_MOMENT_COMMENT, HotMomentsCache.deleteMomentCommentEventHandler)

  EventBus.on(Events.LIKE_MOMENT, HotMomentsCache.likeMomentEventHandler)
  EventBus.on(Events.CANCEL_LIKE_MOMENT, HotMomentsCache.cancelLikeMomentEventHandler)
  EventBus.on(Events.DELETE_MOMENT, notifications.deleteMomentEventHandler)

  const dreamAngelService = require('./services/qnPocket/dreamAngel')
  const dreamMingshiService = require('./services/qnPocket/dreamMingshi')
  EventBus.on(Events.ADD_MOMENT_TO_TOPIC, dreamAngelService.addMomentToTopicEventHandler)
  EventBus.on(Events.ADD_MOMENT_TO_TOPIC, dreamMingshiService.addMomentToTopicEventHandler)
}

module.exports = EventBus
