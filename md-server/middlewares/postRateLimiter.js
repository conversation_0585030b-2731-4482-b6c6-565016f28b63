const DateUtil = require('../../common/dateUtil')
const { getRedis } = require('../../common/redis')
const Promise = require('bluebird')

const checkApiList = [
  // 心情
  '/md/qnm/addmoment',
  '/md/qnm/addcomm',

  // 留言
  '/md/qnm/addmsg',
  '/md/qnm/ansmsg',

  // 评论
  '/md/comments/new',

  '/md/show_topics/new'
]

const rateLimitOption = {
  minute: 4,
  hour: 100
}

function isNeedCheckRoute (path) {
  return checkApiList.includes(path)
}

function checkRateValid (userid) {
  const now = new Date()

  function checkMinuteRate () {
    const startOfMinute = DateUtil.startOfMinute(now)
    const minuteRateKey = `md:post_rate_limit:${userid}:minute:${startOfMinute.getTime()}`
    return getRedis().getAsync(minuteRateKey)
    .then(count => {
      count = parseInt(count, 10)
      if (count && count > rateLimitOption.minute) {
        return Promise.reject({msg: '发言过于频繁'})
      } else {
        return getRedis().incrAsync(minuteRateKey)
        .then(() => {
          return getRedis().expireAsync(minuteRateKey, 60)
        })
      }
    })
  }

  function checkHourRate () {
    const startOfHour = DateUtil.startOfHour(now)
    const hourRateKey = `md:post_rate_limit:${userid}:hour:${startOfHour.getTime()}`
    return getRedis().getAsync(hourRateKey)
    .then(count => {
      count = parseInt(count, 10)
      if (count && count > rateLimitOption.hour) {
        return Promise.reject({msg: '发言过于频繁'})
      } else {
        return getRedis().incrAsync(hourRateKey)
        .then(() => {
          return getRedis().expireAsync(hourRateKey, 3600)
        })
      }
    })
  }

  return Promise.all([checkMinuteRate(), checkHourRate()])
}

// 发言频率控制
function postRateLimiter (req, res, next) {
  const userid = req.params.userid
  const routePath = req.route.path
  if (userid && isNeedCheckRoute(routePath)) {
    return checkRateValid(userid)
    .then(() => {
      next()
    }).catch(err => {
      res.send({code: -1, msg: err.msg})
    })
  } else {
    next()
  }
}

module.exports = postRateLimiter
