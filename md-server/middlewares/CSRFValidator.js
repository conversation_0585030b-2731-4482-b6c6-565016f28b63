/**
 * Created by zhen<PERSON> on 2017/6/13.
 */

/**
 * Place refer https://www.owasp.org/index.php/Cross-Site_Request_Forgery_%28CSRF%29_Prevention_Cheat_Sheet
 */
const config = require('../../common/config');
const crypto = require('crypto');

const cookieName = 'X-CSRF-TOKEN-MD';

function generateToken() {
  return crypto.randomBytes(50).toString('hex');
}

function getCSRFTokenFromParams(req) {
  return req.params['_csrf_token'];
}

function getCSRFTokenFromCookie(req) {
  return req.cookies[cookieName];
}

function isBlank(s) {
  return s === undefined || s === null || s.trim() === '';
}


function setTokenToCookie(res, token) {
  res.setCookie(cookieName, token, { path: '/', domain: '.163.com'});
}


const CSRFAPICheckList = [
  //心情
  '/md/qnm/addmoment',
  '/md/qnm/delmoment',
  '/md/qnm/setzan',
  '/md/qnm/addcomm',
  '/md/qnm/delcomm',

  //留言
  '/md/qnm/addmsg',
  '/md/qnm/ansmsg',
  '/md/qnm/delmsg',
  '/md/qnm/delans',

  //举报
  '/md/report',

  //同人
  '/md/tongren/edit',
  '/md/tongren/like',
  '/md/tongren/unlike',
  '/md/tongren/comment/add',
  '/md/tongren/comment/delete',
  '/md/tongren/work/:id/delete',

  //评论
  '/md/comments/new',
  '/md/comments/:comment_id/delete',

  //关注相关
  '/md/qnm/addfri',
  '/md/qnm/delfri',
  '/md/contacts/cancel_follow',

  //点赞
  '/md/like',
  '/md/unlike',
  '/md/likes/photos/:photo_id/new',
  '/md/likes/photos/:photo_id/delete',

  //相册
  '/md/photo_albums/new',
  '/md/photo_albums/:album_id/delete',
  '/md/photo_albums/:album_id/update',


  //图片
  '/md/photos/new',
  '/md/photos/delete',
  '/md/photos/move',
  '/md/photos/:photo_id/update',
  '/md/photos/:photo_id/delete',
  '/md/photos/:photo_id/asCover',
  '/md/photos/:photo_id/move',

  //真人秀
  '/md/reality_show/apply',

  //炫耀帖
  '/md/show_topics/new',
  '/md/show_topics/:topic_id/delete',
  '/md/show_topics/:topic_id/update',


  //个人设置
  '/md/setuserinfo',

  //绑定角色
  '/md/:game/bindrole',
  '/md/:game/unbindrole',
];

function isNeedCheckCsrf(routePath) {
  return config.testCfg.csrf_token_validate && CSRFAPICheckList.includes(routePath);
}


module.exports = function CSRFfValidator(req, res, next) {
  const newToken = generateToken();
  const routePath = req.route.path;
  function checkSuccessHandler() {
    setTokenToCookie(res, newToken);
    next();
  }
  if(isNeedCheckCsrf(routePath)) {
    const tokenFromParams = getCSRFTokenFromParams(req);
    if(isBlank(tokenFromParams)) {
      res.send({code:-1, msg: "csrf token is empty!"})
    } else if (tokenFromParams !== getCSRFTokenFromCookie(req)){
      //和cookie中不一致
      res.send({code:-1, msg: "csrf token is invalid!"})
    } else {
      checkSuccessHandler();
    }
  } else {
    checkSuccessHandler();
  }
};
