/**
 * Created by z<PERSON><PERSON> on 16-11-10.
 */

var Users = require('../../models/Users');

var BanUserCheckRoutePath = new Set([
  '/md/qnm/addmoment',
  '/md/qnm/addcomm',
  '/md/qnm/addmsg',
  '/md/qnm/ansmsg',
  '/md/photo_albums/new',
  "/md/photos/new",
  "/md/likes/photos/:photo_id/new",
  "/md/show_topics/new",
  "/md/comments/new",
  "/md/like",
  "/md/unlike",
  "/md/qnm/setzan",
  "/md/reality_show/apply",
  "/md/setuserinfo",
  "/md/:game/bindrole",
  "/md/qnm/addfri",
  "/md/contacts/cancel_follow",
  "/md/report"
]);

function isPathNeedCheck(path) {
  return BanUserCheckRoutePath.has(path);
}

module.exports = function (req, res, next) {
  var routePath = req.route.path;
  var userId = req.params.userid;
  if(userId && isPathNeedCheck(routePath)) {
    return Users.findById(userId, ['Status'])
      .then(function (user) {
        if(user) {
          if(user.Status == Users.Statuses.BANNED) {
            res.send({code:-1, errorType:"UserIsMuted", msg:"您的发言存在不和谐内容被系统禁言，请注意言论"});
          } else {
            next();
          }
        } else {
          next();
        }
      })
  } else {
    next();
  }
};
