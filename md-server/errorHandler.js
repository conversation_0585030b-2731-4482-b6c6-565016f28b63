const errors = require('../common/errors')
const env = process.env.NODE_ENV
const logger = require('../common/logger')
const TestCfg = require('../common/config').testCfg

const errorHandler = function (err, req, res) {
  if (TestCfg.debug) {
    console.log(err)
  }
  logger.error(JSON.stringify(err, Object.getOwnPropertyNames(err)))
  const errorRes = {
    code: err.code || -1,
    msg: ''
  }
  if (err.msg) {
    errorRes.msg = err.msg || '请求失败!'
    delete err.msg
  }
  if (errors.isCustomError(err)) {
    if (env !== 'DEBUG') {
      // print err.stack when run in DEBUG mode
      delete err.stack
    }
    errorRes.error.errorType = err.constructor.name
  }
  return res.send(errorRes)
}

module.exports = errorHandler
