"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.modifyResponse = void 0;
const http = require('http');
const _ = require("lodash");
const util2_1 = require("../../common/util2");
const Response = http.ServerResponse;
// 返回数据拦截器
function modifyResponse() {
    const _send = Response.prototype.send;
    Response.prototype.send = function () {
        let body = arguments[0];
        if (_.isObject(body)) {
            body = (0, util2_1.transformObjectForHttps)(body);
            arguments[0] = body;
        }
        const result = _send.apply(this, arguments);
        return result;
    };
}
exports.modifyResponse = modifyResponse;
//# sourceMappingURL=modifyResponse.js.map