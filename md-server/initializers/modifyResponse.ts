const http = require('http')
import * as _ from 'lodash'
import { transformObjectForHttps } from '../../common/util2';

const Response = http.ServerResponse

// 返回数据拦截器
export function modifyResponse() {
    const _send = Response.prototype.send

    Response.prototype.send = function () {
        let body = arguments[0]
        if (_.isObject(body)) {
            body = transformObjectForHttps(body)
            arguments[0] = body
        }
        const result = _send.apply(this, arguments)
        return result
    }
}