const http = require('http')
const Response = http.ServerResponse
const util = require('../../common/util')
const InfluxLog = require('../../common/influxLog').InfluxLog
const _ = require('lodash')

exports.init = function apiInfluxLogger () {
  const _send = Response.prototype.send

  Response.prototype.send = function () {
    const result = _send.apply(this, arguments)

    const req = this.req
    const responseTime = Date.now() - req._time
    const ip = util.getIp(this.req)
    const query = req._url.query
    const origin = req.url.startsWith('/md/pocket') ? 'pocket' : 'pc'
    const userId = _.get(req, 'params.userid')
    const path = _.get(req, 'route.path') || req.url

    const fields = {
      ip: ip,
      url: req.url,
      userAgent: req.header('user-agent'),
      responseTime: responseTime,
      method: req.method,
      statusCode: this.statusCode
    }
    if (userId) {
      fields.userId = parseInt(userId, 10)
    }
    if (query) {
      fields.query = query
    }
    if (req.body) {
      fields.bdoy = req.body
    }

    const l = InfluxLog.create({
      measurement: 'md_server_api',
      tags: {
        path: path,
        origin: origin
      },
      fields: fields
    })
    l.send()
    return result
  }
}
