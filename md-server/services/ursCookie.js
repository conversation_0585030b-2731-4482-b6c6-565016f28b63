"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const AppConfig = require("../../common/config");
const httpLib = require("../../common/request");
const _ = require('lodash');
const UrsCookieHosts = AppConfig.URS_COOKIE_PARSE.HOSTS;
const Config = {
    Hosts: UrsCookieHosts,
    productid: 'f77072fb1b1a4664856363589c4127af',
    cookieNames: ['NTES_SESS', 'NTES_YD_SESS', 'NTES_OSESS', 'NTES_PASSPORT', 'NTES_YD_PASSPORT']
};
function formatSsn(ssn) {
    if (ssn.includes('@')) {
        return ssn;
    }
    else {
        return ssn + '@163.com';
    }
}
function callValidateApi(uri, cookieName, cookieValue) {
    return httpLib.request({
        method: 'POST',
        uri: uri,
        qs: {
            productid: Config.productid,
            cookieName: cookieName,
            cookie: cookieValue,
            recreate: 0
        }
    }).then(body => {
        if (body.retCode) {
            if (body.retCode === 200) {
                const data = body.data;
                data.ssn = formatSsn(data.ssn);
                return { validate: true, data: data };
            }
            else {
                return { validate: false, msg: body.msg };
            }
        }
        return Promise.reject(new Error('Call urs Api ' + uri + ' failed'));
    });
}
function getValidCookie(cookies) {
    const cookeName = _.find(Config.cookieNames, name => {
        return !!cookies[name];
    });
    return { cookieName: cookeName, cookieValue: cookies[cookeName] };
}
function validateCookies(cookies) {
    const checkCookie = getValidCookie(cookies);
    return callValidateApi(Config.Hosts[0], checkCookie.cookieName, checkCookie.cookieValue)
        .catch(() => {
        return callValidateApi(Config.Hosts[1], checkCookie.cookieName, checkCookie.cookieValue);
    });
}
exports.validateCookies = validateCookies;
// 测试机无法使用 ursCookie校验接口，从登录cookie取出来
function validateFromLocal(cookies) {
    const pInfo = cookies['P_INFO'];
    const netsSessInfo = cookies['NTES_SESS'];
    if (pInfo && netsSessInfo) {
        const ssn = pInfo.split('|')[0];
        return { validate: true, data: { ssn: ssn } };
    }
    else {
        return { validate: false, msg: '请先登录网易通行证' };
    }
}
exports.validateFromLocal = validateFromLocal;
function validateUrsCookie(cookies) {
    if (AppConfig.testCfg.test_env) {
        return validateFromLocal(cookies);
    }
    else {
        return validateCookies(cookies);
    }
}
exports.validateUrsCookie = validateUrsCookie;
//# sourceMappingURL=ursCookie.js.map