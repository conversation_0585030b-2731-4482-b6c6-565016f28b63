/**
 * Created by z<PERSON><PERSON> on 2017/4/1.
 */

const { getRedis } = require('../../common/redis');
const _ = require('lodash');
const MdPhotoAlbums = require('../../models/PhotoAlbums');
const MDPhotos = require('../../models/Photos');
const Constants = require('../../common/data').Constants;
const co = require('../../common/util').co;
const Promise = require('bluebird');


const QnmTukuService = module.exports = {
  listChannelPhotoAlbums: function (channelId, pagination, sortBy) {
    const key = `md:qnm:tuku:channels:${channelId}:${sortBy}`;
    return getRedis().zrevrangeAsync(key, 0, 500)
    .then(albumIds => {
      return MdPhotoAlbums.filterAlbumIdsByShowInPublic(albumIds);
    })
    .then(albumIds => {
      const albumIdsChunk = _.chunk(albumIds, pagination.pageSize);
      albumIds = albumIdsChunk[pagination.page - 1];
      const getAlbums = function(albumIds) {
        return MdPhotoAlbums.findByIds(albumIds, ['ID', 'Name', 'Desc', 'ChannelId', 'CoverUrl', 'UserId'])
        .then(albums => {
          return Promise.mapSeries(albums, album => QnmTukuService.getAlbumMetaInfo(album.ID)
          .then(albumMetaInfo => {
            album.LikesCount = albumMetaInfo.likesCount;
            album.CoverUrl = albumMetaInfo.lastAddPhotoUrl || album.CoverUrl;
            album.LastAddPhotoTime = albumMetaInfo.lastAddPhotoTime;
            return album;
          }));
        })
      };
      return Promise.props({
        albums: getAlbums(albumIds),
        curPage: pagination.page,
        totalPage: albumIdsChunk.length
      })
    });
  },


  getAlbumMetaInfo: function (albumId) {
    let photosCount = 0;
    let lastAddPhotoTime = 0;
    let lastAddPhotoUrl = "";
    const query = MDPhotos.scope()
    .select('ID', 'CreateTime', 'Url')
    .where('PhotoAlbumID', albumId)
    .where('AuditStatus', Constants.STATUS_AUDIT_PICK)
    .where('Status', Constants.STATUS_NORMAL)
    .orderBy('CreateTime', 'desc');

    return MDPhotos.executeByQuery(query).then(photos => {
      photosCount = photos.length || 0;
      if(photosCount > 0) {
        const lastPhoto = _.last(photos);
        lastAddPhotoTime = lastPhoto.CreateTime;
        lastAddPhotoUrl = lastPhoto.Url;
      }
      return MDPhotos.getTotalLikesCount(photos)
    }).then(likesCount => {
      return {
        likesCount: likesCount,
        photosCount: photosCount,
        lastAddPhotoTime: lastAddPhotoTime,
        lastAddPhotoUrl: lastAddPhotoUrl,
      }
    });

  },

  calculateHotFromHotState: function (hotState) {
    // const now = Date.now();
    hotState = _.defaults(hotState, {likesCount:0, photosCount:0, lastAddPhotoTime: 0});
    // const passedHours = Math.max(1, (now - hotState.lastAddPhotoTime) / (3600 * 1000));
    // const photoCountFactor = hotState.photosCount > 5 ? 1 : 0.9;    // 小于5张用0.9惩罚下
    // const hot = Math.max(1, photoCountFactor * hotState.likesCount) / passedHours;
    //使用简单的总点赞数来计算热度
    return hotState.likesCount;
  },


  refreshChannelAlbumHot: function (channelId, albumId) {
    const albumHotKey = `md:qnm:tuku:channels:${channelId}:hot`;
    const albumNewKey = `md:qnm:tuku:channels:${channelId}:new`;
    return QnmTukuService.getAlbumMetaInfo(albumId).then(hotState => {
      const hot = QnmTukuService.calculateHotFromHotState(hotState);
      return Promise.all(([
        getRedis().zaddAsync(albumHotKey, hot, albumId),
        getRedis().zaddAsync(albumNewKey, hotState.lastAddPhotoTime, albumId),
      ]))
    });
  },


  refreshChannelAlbumCache: function (channelId) {
    const SCAN_MAX_LIMIT = 10000;
    const SCAN_SIZE_PER_TIME = 500;
    const SCAN_DELAY_TIME = 20;

    const scan = function (lastId, limit) {
      let query = MdPhotoAlbums.normalScope()
      .select('ID')
      .whereIn('AuditStatus', [Constants.STATUS_AUDIT_PASS, Constants.STATUS_AUDIT_PICK])
      .where('Type', MdPhotoAlbums.Types.QNM_NORMAL)
      .where('ChannelId', channelId)
      .orderBy('ID', 'desc')
      .limit(limit);
      if(lastId) {
        query = query.where('ID', '<', lastId);
      }
      return MdPhotoAlbums.executeByQuery(query)
      .then(albums => _.map(albums, 'ID'));
    };


    return co(function *() {
      const times = Math.floor(SCAN_MAX_LIMIT / SCAN_SIZE_PER_TIME);
      let scanLastId = null;
      for(let i = 0; i < times; i++) {
        const albumIds = yield scan(scanLastId, SCAN_SIZE_PER_TIME);
        if(_.isEmpty(albumIds)) {
          break;
        } else {
          for(let j = 0; j < albumIds.length; j++) {
            yield QnmTukuService.refreshChannelAlbumHot(channelId, albumIds[j])
          }
          scanLastId = _.last(albumIds);
        }
        yield Promise.delay(SCAN_DELAY_TIME);
      }
    });
  },

  refreshAllChannelAlbum: function () {
    const channelIds = MdPhotoAlbums.ChannelIds;
    return Promise.mapSeries(channelIds, channelId => {
      return QnmTukuService.refreshChannelAlbumCache(channelId)
    });
  },

  correctTopAlbumsInAllChannels: function () {
    const channelIds = MdPhotoAlbums.ChannelIds;
    const topSize = 200;
    return co(function *() {
      for(let i = 0; i < channelIds.length; i++) {
        const channelId = channelIds[i];
        const albumHotKey = `md:qnm:tuku:channels:${channelId}:hot`;
        const albumNewKey = `md:qnm:tuku:channels:${channelId}:new`;

        const albumHotIds = yield getRedis().zrevrangeAsync(albumHotKey, 0, topSize);
        const albumNewIds = yield getRedis().zrevrangeAsync(albumNewKey, 0, topSize);

        const albumIds = _.union(albumHotIds, albumNewIds);
        const albums = yield MdPhotoAlbums.findByIds(albumIds, ['ID', 'ChannelId']);

        const notValidChannelAlbums = _.filter(albums, album => {
          return album.ChannelId !== channelId;
        });

        const ValidChannelAlbums = _.filter(albums, album => {
          return album.ChannelId !== channelId;
        });

        const notValidIds = _.map(notValidChannelAlbums, 'ID');
        const validIds = _.map(ValidChannelAlbums, 'ID');

        if(!_.isEmpty(notValidIds)) {
          yield getRedis().zremAsync.apply(getRedis(), _.concat(albumHotKey, notValidIds));
          yield getRedis().zremAsync.apply(getRedis(), _.concat(albumNewKey, notValidIds));
        }

        for(let j = 0; j < validIds.length; j++) {
          yield QnmTukuService.refreshChannelAlbumHot(channelId, validIds[j]);
        }
      }
    })
  },

};
