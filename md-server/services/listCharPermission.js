"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkPermission = exports.ListCharPermission = void 0;
const redis_1 = require("../../common/redis");
class ListCharPermission {
    constructor(urs) {
        this.urs = urs;
        this.key = 'md:api:list_char:permission:' + this.urs;
    }
    isAllow() {
        return __awaiter(this, void 0, void 0, function* () {
            let result = yield (0, redis_1.getRedis)().existsAsync(this.key);
            return result === redis_1.IExistResult.Exist;
        });
    }
    authorize() {
        return __awaiter(this, void 0, void 0, function* () {
            let result = yield (0, redis_1.getRedis)().setAsync(this.key, 1, redis_1.ExpireType.EX, ListCharPermission.authDuration);
            return result;
        });
    }
    static from(urs) {
        return new this(urs);
    }
}
exports.ListCharPermission = ListCharPermission;
ListCharPermission.authDuration = 5 * 60; // 5分钟
function checkPermission(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        let urs = req.params.urs;
        try {
            let isAllow = yield ListCharPermission.from(urs).isAllow();
            if (isAllow) {
                return next();
            }
            else {
                res.send({ code: -1, msg: 'No Permission' });
            }
        }
        catch (err) {
            res.send({ code: -1, msg: err.message });
        }
    });
}
exports.checkPermission = checkPermission;
//# sourceMappingURL=listCharPermission.js.map