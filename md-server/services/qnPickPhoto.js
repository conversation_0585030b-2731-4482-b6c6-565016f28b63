// Pick enum
const PickTypes = {
  TongRen: 'TongRen',
  ShowTopic: 'ShowTopic',
  Photo: 'Photo',
  RealityShow: 'RealityShow',
  HandPaint: 'HandPaint'
}

const { getRedis } = require('../../common/redis')
const util = require('../../common/util')
const config = require('../../common/config')
const _ = require('lodash')
const Photos = require('../../models/Photos')
const MdTongrenWorks = require('../../models/MdTongrenWorks')
const MdShowTopic = require('../../models/ShowTopics')
const RealityShow = require('../../models/RealityShows')
const Constants = require('../../common/data').Constants

const photoPickCacheKey = 'md:qn:official_website_pick_photos'
const pickSaveMaxSize = 16
const maxPhotoCount = 8
const Promise = require('bluebird')

function getCacheKeyForPickType(pickType) {
  if(pickType === PickTypes.HandPaint) {
    return 'md:qn:official_website_pick_photos_handpaint'
  } else {
    return photoPickCacheKey
  }
}

function getJumpUrl (pickType, jumpId) {
  const host = config.SiteIslandHost || 'https://new.hi.163.com/'
  if (pickType === PickTypes.TongRen || pickType === PickTypes.HandPaint) {
    return host + `#/fan?fanid=${jumpId}`
  } else if (pickType === PickTypes.ShowTopic) {
    return host + `#/topic?topicid=${jumpId}`
  } else if (pickType === PickTypes.RealityShow) {
    return host + `#/mynews?userid=${jumpId}`
  } else {
    return host + `#/mynews?userid=${jumpId}`
  }
}

/**
 * Pick photo for website, and save to redis
 * @param {Object} options
 * @param {String} options.pickType
 * @param {String} options.url
 * @param {Number} options.relatedId
 */
function pickPhoto (options) {
  let key = getCacheKeyForPickType(options.pickType)
  return getAllPicks(options.pickType).then(picks => {
    const newList = _.take(_.uniqBy(_.concat(options, picks), item => item.url), pickSaveMaxSize)
    return getRedis().setAsync(key, JSON.stringify(newList))
  })
}

function getAllPicks (group) {
  let key = getCacheKeyForPickType(group)
  return getRedis().getAsync(key)
    .then(data => {
      return util.getJsonInfo(data, [])
    })
}

function filterNormal (picks) {
  return Promise.filter(picks, pick => {
    const pickType = pick.pickType
    if (pickType === PickTypes.TongRen || pickType === PickTypes.HandPaint) {
      return MdTongrenWorks.findOne({ID: pick.relatedId, Status: [Constants.STATUS_NORMAL, Constants.STATUS_AUDIT_PASS]}, ['ID'])
    } else if (pickType === PickTypes.ShowTopic) {
      return MdShowTopic.findNormalById(pick.relatedId, ['ID'])
    } else if (pickType === PickTypes.Photo) {
      return Photos.findOne({ID: pick.relatedId, Status: [Constants.STATUS_NORMAL, Constants.STATUS_AUDIT_PASS, Constants.STATUS_AUDIT_PICK]}, ['ID'])
    } else {
      return RealityShow.findOne({UserId: pick.relatedId, Avatar: pick.url}, ['ID'])
    }
  }, {concurrency: 1})
}

function getJumpId (pick) {
  if (pick.pickType === PickTypes.Photo) {
    return Photos.getPhotoOwnerId(pick.relatedId)
  } else {
    return Promise.resolve(pick.relatedId)
  }
}

function getPickPhotos (group) {
  let key = getCacheKeyForPickType(group)
  return getAllPicks(group).then(filterNormal).then(picks => {
    getRedis().setAsync(key, JSON.stringify(picks))
    return Promise.mapSeries(_.take(picks, maxPhotoCount), pick => {
      return getJumpId(pick).then(jumpId => {
        return {
          url: util.toHttps(pick.url),
          jumpUrl: getJumpUrl(pick.pickType, jumpId)
        }
      })
    })
  })
}

module.exports = {
  getPickPhotos: getPickPhotos,
  pickPhoto: pickPhoto,
  PickTypes: PickTypes
}
