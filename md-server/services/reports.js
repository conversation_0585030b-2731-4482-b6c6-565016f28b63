const MdReports = require('../../models/MdReports')
const ModelManager = require('../../models/ModelManager')

function createReport (params) {
  const resourceType = params.resource_type
  const tableName = `md_${resourceType}`
  const id = params.resource_id
  const reportModel = ModelManager.getModelByTableName(tableName)

  return reportModel.checkEntityExsits(id)
  .then(() => {
    return MdReports.insert({
      UserId: params.userid,
      Type: resourceType,
      TargetId: params.resource_id,
      Text: params.desc,
      CreateTime: Date.now(),
      ReasonType: params.reason_type,
      Source: params.source
    })
  }).then(info => {
    return {id: info.insertId}
  })
}

module.exports = {
  createReport: createReport
}
