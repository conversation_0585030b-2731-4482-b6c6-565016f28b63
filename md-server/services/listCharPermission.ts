import { getRedis, IExistResult, ExpireType } from '../../common/redis'

export class ListCharPermission {
  static authDuration = 5 * 60 // 5分钟
  private urs: string
  private key: string

  constructor(urs: string) {
    this.urs = urs
    this.key = 'md:api:list_char:permission:' + this.urs
  }

  async isAllow(): Promise<boolean> {
    let result = await getRedis().existsAsync(this.key)
    return result === IExistResult.Exist
  }

  async authorize() {
    let result = await getRedis().setAsync(this.key, 1, ExpireType.EX, ListCharPermission.authDuration)
    return result
  }

  static from(urs: string) {
    return new this(urs)
  }
}

export async function checkPermission(req, res, next) {
  let urs = req.params.urs
  try {
    let isAllow = await ListCharPermission.from(urs).isAllow()
    if (isAllow) {
      return next()
    } else {
      res.send({ code: -1, msg: 'No Permission' })
    }
  } catch (err) {
    res.send({ code: -1, msg: err.message })
  }
}