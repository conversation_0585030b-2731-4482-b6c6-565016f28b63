/**
 * Created by <PERSON><PERSON><PERSON> on 2017/7/11.
 */
const Notifications = require('../../models/Notifications')
const ModelManager = require('../../models/ModelManager')
const EventTypes = Notifications.EVENT_TYPES
const _ = require('lodash')
const Promise = require('bluebird')

/**
 * @param {Object} props
 * @param {Number} props.UserId
 * @param {Number} props.TargetId
 * @param {Number} props.RelateId
 * @param {Number} props.Type
 * @param {Number} [props.CreateTime]
 */
function insertNotification (props) {
  const userId = parseInt(props.UserId, 10)
  const TargetId = parseInt(props.TargetId, 10)
  if (userId !== TargetId) {
    return Notifications.insert({
      UserId: props.UserId,
      TargetId: props.TargetId,
      RelateId: props.RelateId,
      Type: props.Type,
      CreateTime: props.CreateTime || Date.now()
    })
  } else {
    return Promise.resolve(null)
  }
}

/**
 * @param {Object} payload
 * @param {Number} payload.userId
 * @param {Number} payload.momentUserId
 * @param {Number} payload.momentId
 */
exports.likeMomentEventHandler = function (payload) {
  return insertNotification({
    UserId: payload.userId,
    TargetId: payload.momentUserId,
    RelateId: payload.momentId,
    Type: EventTypes['LIKE_MOMENT'],
    CreateTime: Date.now()
  })
}

/**
 * @param {Object} payload
 * @param {Number} payload.userId
 * @param {Number} payload.momentUserId
 * @param {Number} payload.momentId
 */
exports.cancelLikeMomentEventHandler = function (payload) {
  return Notifications.softDeleteByCondition({
    UserId: payload.userId,
    TargetId: payload.momentUserId,
    RelateId: payload.momentId,
    Type: EventTypes['LIKE_MOMENT']
  })
}

/**
 * @param {Object} payload
 * @param {Number} payload.userId
 * @param {Number} payload.momentUserId
 * @param {Number} payload.commentId
 */
exports.createMomentCommentEventHandler = function (payload) {
  if (payload.userId !== payload.momentUserId) {
    return insertNotification({
      UserId: payload.userId,
      TargetId: payload.momentUserId,
      RelateId: payload.commentId,
      Type: EventTypes['COMMENT_MOMENT'],
      CreateTime: Date.now()
    })
  }
}

/**
 * @param {Object} payload
 * @param {Number} payload.userId
 * @param {Number} payload.replyUserId
 * @param {Number} payload.momentUserId
 * @param {Number} payload.commentId
 */
exports.replyMomentCommentEventHandler = function (payload) {
  console.log('inform', payload)
  const notifyUserIds = _.uniq([payload.replyUserId, payload.momentUserId])
  return Promise.map(notifyUserIds, notifyUserId => {
    return insertNotification({
      UserId: payload.userId,
      TargetId: notifyUserId,
      RelateId: payload.commentId,
      Type: EventTypes['REPLY_MOMENT'],
      CreateTime: Date.now()
    })
  })
}

/**
 * @param {Object} payload
 * @param {Number} payload.commentId
 */
exports.deleteMomentCommentEventHandler = function (payload) {
  return Notifications.softDeleteByCondition({
    Type: [EventTypes['COMMENT_MOMENT'], EventTypes['REPLY_MOMENT']],
    RelateId: payload.commentId
  })
}

/**
 * @param {Object} payload
 * @param {Number} payload.userId
 * @param {Number} payload.messageId
 * @param {Number} payload.messageBoardUserId
 */
exports.createMessageEventHandler = function (payload) {
  return insertNotification({
    UserId: payload.userId,
    TargetId: payload.messageBoardUserId,
    RelateId: payload.messageId,
    Type: EventTypes['LEAVE_MESSAGE'],
    CreateTime: Date.now()
  })
}

/**
 * @param {Object} payload
 * @param {Number} payload.messageId
 */
exports.deleteMessageEventHandler = function (payload) {
  const messageId = payload.messageId
  const Answers = ModelManager.getModelByTableName('md_answer', 'SLAVE')
  const query = Answers.normalScope().where('TargetId', messageId).select('ID')
  return Answers.executeByQuery(query).then(answers => {
    const cIds = _.map(answers, c => c.ID)
    if (!_.isEmpty(cIds)) {
      return Notifications.softDeleteByCondition({
        RelateId: cIds,
        Type: [EventTypes['REPLY_LEAVE_MESSAGE']]
      })
    }
  }).then(() => {
    return Notifications.softDeleteByCondition({Type: [EventTypes['LEAVE_MESSAGE']], RelateId: messageId})
  })
}

/**
 * @param {Object} payload
 * @param {Number} payload.userId
 * @param {Number} payload.messageUserId
 * @param {Number} payload.messageBoardUserId
 * @param {Number} payload.answerId
 */
exports.createMessageAnswerEventHandler = function (payload) {
  const notifyUserIds = _.uniq([payload.messageUserId, payload.messageBoardUserId])
  return Promise.map(notifyUserIds, notifyUserId => {
    return insertNotification({
      UserId: payload.userId,
      TargetId: notifyUserId,
      RelateId: payload.answerId,
      Type: EventTypes['REPLY_LEAVE_MESSAGE'],
      CreateTime: Date.now()
    })
  })
}

/**
 * @param {Object} payload
 * @param {Number} payload.userId
 * @param {Number} payload.replyUserId
 * @param {Number} payload.messageUserId
 * @param {Number} payload.messageBoardUserId
 * @param {Number} payload.answerId
 */
exports.replyMessageAnswerEventHandler = function (payload) {
  const notifyUserIds = _.uniq([payload.messageUserId, payload.replyUserId, payload.messageBoardUserId])
  return Promise.map(notifyUserIds, notifyUserId => {
    return insertNotification({
      UserId: payload.userId,
      TargetId: notifyUserId,
      RelateId: payload.answerId,
      Type: EventTypes['REPLY_LEAVE_MESSAGE']
    })
  })
}

/**
 * @param {Object} payload
 * @param {Number} payload.answerId
 */
exports.deleteMessageAnswerEventHandler = function (payload) {
  return Notifications.softDeleteByCondition({
    Type: EventTypes['REPLY_LEAVE_MESSAGE'],
    RelateId: payload.answerId
  })
}

/**
 * @param {Object} payload
 * @param {Number} payload.userId
 * @param {Number} payload.followUserId
 */
exports.followUserEventHandler = function (payload) {
  return insertNotification({
    UserId: payload.userId,
    TargetId: payload.followUserId,
    Type: EventTypes['FOLLOW'],
    CreateTime: Date.now()
  })
}

/**
 * @param {Object} payload
 * @param {Number} payload.userId
 * @param {Number} payload.followUserId
 */
exports.cancelFollowUserEventHandler = function (payload) {
  return Notifications.softDeleteByCondition({
    Type: EventTypes['FOLLOW'],
    UserId: payload.userId,
    TargetId: payload.followUserId
  })
}

/**
 * @param {Object} payload
 * @param {Number} payload.momentId
 */
exports.deleteMomentEventHandler = function (payload) {
  const momentId = payload.momentId
  const Comments = ModelManager.getModelByTableName('md_comment', 'SLAVE')

  const query = Comments.normalScope().where('TargetId', momentId).select('ID')
  return Comments.executeByQuery(query).then(comments => {
    const cIds = _.map(comments, c => c.ID)
    if (!_.isEmpty(cIds)) {
      return Notifications.softDeleteByCondition({
        RelateId: cIds,
        Type: [EventTypes['COMMENT_MOMENT'], EventTypes['REPLY_MOMENT']]
      })
    }
  }).then(() => {
    return Notifications.softDeleteByCondition({Type: [EventTypes['LIKE_MOMENT']], RelateId: momentId})
  })
}

/**
 * @param {Object} payload
 * @param {Number} payload.photoId
 */
exports.deletePhotoEventHandler = function (payload) {
  const query = Notifications.scope()
  .where('RelateId', payload.photoId)
  .where('Type', Notifications.EVENT_TYPES.LIKE_PHOTO)
  .update({Status: Notifications.Statuses.DELETED})
  return Notifications.executeByQuery(query)
}
