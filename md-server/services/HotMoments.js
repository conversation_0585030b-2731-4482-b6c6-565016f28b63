/**
 * Created by <PERSON>hen<PERSON> on 2017/3/6.
 */

const { getRedis } = require('../../common/redis')
const MdMoments = require('../../models/Moments')
const MdComments = require('../../models/Comments')
const ONE_DAY = 24 * 60 * 3600
const _ = require('lodash')
const EventEmitter = require('events')
const co = require('../../common/util').co
const Promise = require('bluebird')

const Events = new EventEmitter()

const HOT_SIZE = 200

class HotMoments {
  static get Events () {
    return Events
  }

  static getCacheKey (category) {
    category = category || 'all'
    return `md:moments:hot:${category}`
  }

  static getHotMoments (category) {
    return getRedis().getAsync(HotMoments.getCacheKey(category))
    .then(str => {
      return JSON.parse(str)
    })
  }

  static getHotMomentsFromDB (category) {
    let query = MdMoments.normalScope()
    .select('ID', 'UserId', 'Text', 'ImgList', 'CreateTime', 'ImgAudit', 'ZanList')
    .orderBy('isTop', 'desc')
    .orderBy('Hot', 'desc')
    .orderBy('CreateTime', 'desc')
    .limit(HOT_SIZE)
    if (category === 'qn') {
      query = query.whereIn('GameId', ['8001', '1'])
    } else if (category === 'qnm') {
      query = query.where('GameId', '8100')
    }
    return MdMoments.executeByQuery(query).then(moments => {
      return MdMoments.filterValidMoments(null, moments)
    })
  }

  static setInCache (category, data) {
    return getRedis().setAsync(HotMoments.getCacheKey(category), JSON.stringify(data))
  }

  static refresh (category) {
    return HotMoments.getHotMomentsFromDB(category)
    .then(data => HotMoments.setInCache(category, data))
  }

  static refreshAll () {
    return Promise.mapSeries(['all', 'qn', 'qnm'], category => {
      return HotMoments.refresh(category)
    })
  }

  static getHotFromHotState (state) {
    const min = Math.min
    const max = Math.max
    const floor = Math.floor
    const likeCount = state.like || 0 // 点赞数
    const commentCount = state.comment || 0 // 评论数
    const replyCount = state.reply || 0 // 回复数
    const publishDuration = state.publish_duration || 0 // 发布时长
    const topLevel = state.top || 0 // 置顶参数
    const durationFactor = min(1, 43200 / max(43200, publishDuration - 43200)) * max(0, min(1, 172800 - publishDuration))
    const activeHot = max(0, 1 + floor(10 * (likeCount + min(1.5 * likeCount, commentCount * 2) + min(0.5 * likeCount, replyCount * 2) + topLevel * 1000000)))
    const fixFactor = topLevel > 0 ? 1 : durationFactor
    return activeHot * fixFactor
  }

  static countCsvLength (str) {
    return _.compact(_.split(str, ',')).length
  }

  static getHotStateFromDB (momentId) {
    const hotState = {like: 0, comment: 0, reply: 0, publish_duration: 0, top: 0, create_time: 0}
    return MdMoments.findById(momentId, ['ZanList', 'IsTop', 'CreateTime']).then(moment => {
      if (moment) {
        hotState.create_time = moment.CreateTime
        hotState.like = HotMoments.countCsvLength(moment.ZanList)
        hotState.top = moment.IsTop
        hotState.publish_duration = (Date.now() - hotState.create_time) / 1000
      }
    }).then(() => {
      return MdComments.getCommentAndReplyCount(momentId)
      .then(info => {
        hotState.comment = info.commentCount
        hotState.reply = info.replyCount
      })
    }).then(() => {
      return hotState
    })
  }

  static _getHotState (momentId) {
    const hotStateCacheKey = `md:moments:${momentId}:hotState`
    return getRedis().getAsync(hotStateCacheKey).then(data => {
      if (data) {
        return {
          data: JSON.parse(data),
          isFromCache: true
        }
      } else {
        return HotMoments.getHotStateFromDB(momentId).then(data => {
          HotMoments.setHotStateInCache(momentId, data)
          return {
            data: data,
            isFromCache: false
          }
        })
      }
    })
  }

  static getHotState (momentId) {
    return HotMoments._getHotState(momentId).then(info => info.data)
  }

  static setHotStateInCache (momentId, data) {
    const hotStateCacheKey = `md:moments:${momentId}:hotState`
    return getRedis().setAsync(hotStateCacheKey, JSON.stringify(data), 'EX', ONE_DAY)
  }

  static getNewHotState (momentId, changeState) {
    return HotMoments._getHotState(momentId).then(hotStateInfo => {
      const isFromCache = hotStateInfo.isFromCache
      const hotState = hotStateInfo.data
      if (!isFromCache) {
        return hotState
      } else {
        Object.keys(changeState).forEach(key => {
          if (_.isFunction(changeState[key])) {
            hotState[key] = changeState[key](hotState)
          } else {
            hotState[key] = hotState[key] + changeState[key]
          }
        })
        HotMoments.setHotStateInCache(momentId, hotState)
        return hotState
      }
    })
  }

  static calculateHot (momentId, changeState) {
    return HotMoments.getNewHotState(momentId, changeState)
    .then(state => {
      return HotMoments.getHotFromHotState(state)
    })
  }

  static updateMomentHot (momentId, changeState) {
    return HotMoments.calculateHot(momentId, changeState).then(hot => {
      return MdMoments.updateByCondition({Id: momentId}, {Hot: hot})
    })
  }

  /**
   * 暴露给cronJob使用
   */
  static updatePublishDurationAndHot () {
    return co(function * () {
      const SCAN_TOP_HOT_MOMENTS_SIZE = 10000
      const UPDATE_DELAY = 100
      const query = MdMoments.normalScope()
      .select('ID')
      .orderBy('isTop', 'desc')
      .orderBy('Hot', 'desc')
      .orderBy('CreateTime', 'desc')
      .limit(SCAN_TOP_HOT_MOMENTS_SIZE)
      const rows = yield MdMoments.executeByQuery(query)
      const updateMomentIds = _.map(rows, 'ID')
      const now = Date.now()
      for (let i = 0; i < updateMomentIds.length; i++) {
        HotMoments.Events.emit('updateHot', {momentId: updateMomentIds[i], action: 'updatePublishDuration', now: now})
        yield Promise.delay(UPDATE_DELAY)
      }
    })
  }
}

const actionToChangeState = {
  'like': {like: 1},
  'unLike': {like: -1},
  'comment': {comment: 1},
  'deleteComment': {comment: -1},
  'reply': {reply: 1},
  'deleteReply': {reply: -1}
}

function updateHotHandler (payload) {
  const action = payload.action
  const momentId = payload.momentId
  if (action === 'updatePublishDuration') {
    const now = payload.now
    actionToChangeState['updatePublishDuration'] = {
      'publish_duration': function (state) {
        return (now - state.create_time) / 1000
      }
    }
  }
  HotMoments.updateMomentHot(momentId, actionToChangeState[action])
}

function deleteMomentHandler (payload) {
  const momentId = payload.momentId

  return MdMoments.findById(momentId, ['ID', 'GameId']).then(moment => {
    const checkCategories = ['all']
    if (moment.GameId === 8001 || moment.GameId === 1) {
      checkCategories.push('qn')
    } else if (moment.GameId === 8100) {
      checkCategories.push('qnm')
    }
    return Promise.mapSeries(checkCategories, category => {
      return HotMoments.getHotMoments(category).then(moments => {
        if (_.some(moments, m => m.ID === moment.ID)) {
          return HotMoments.refresh(category)
        }
      })
    })
  })
}

HotMoments.Events.on('updateHot', updateHotHandler)
HotMoments.Events.on('deleteMoment', deleteMomentHandler)

HotMoments.createMomentCommentEventHandler = function (payload) {
  return updateHotHandler({action: 'comment', momentId: payload.momentId})
}

HotMoments.replyMomentCommentEventHandler = function (payload) {
  return updateHotHandler({action: 'reply', momentId: payload.momentId})
}

HotMoments.likeMomentEventHandler = function (payload) {
  return updateHotHandler({action: 'like', momentId: payload.momentId})
}

HotMoments.cancelLikeMomentEventHandler = function (payload) {
  return updateHotHandler({action: 'unLike', momentId: payload.momentId})
}

HotMoments.deleteMomentCommentEventHandler = function (payload) {
  if (payload.replyId) {
    return updateHotHandler({action: 'deleteReply', momentId: payload.momentId})
  } else {
    return updateHotHandler({action: 'deleteComment', momentId: payload.momentId})
  }
}

HotMoments.deleteMomentEventHandler = deleteMomentHandler

module.exports = HotMoments
