/**
 * Created by zhen<PERSON> on 2017/7/6.
 */
const _ = require('lodash')
const QnRoleInfos = require('../../../models/QNRoleInfos')
const getGuild = require('../../../service/qn/role/info').getGuild

/**
 * get users for qnPocketApi
 * @param {Array} userIds
 * @param {Object} [options]
 * @param {Boolean} [options.includeLocation]
 * @param {Array} [options.roleInfoCols]
 */
function getShowUsers (userIds, options) {
  const defaultRoleInfoCols = ['RoleId', 'RoleName', 'JobId as Clazz', 'Gender', 'ServerId']
  options = _.defaults(options, {includeLocation: false, roleInfoCols: defaultRoleInfoCols})
  const util = require('./../../../common/util')
  const co = util.co
  const BindRoles = require('../../../models/BindRoles')
  const Users = require('../../../models/Users')
  const ServerList = require('../../../service/qn/server/list')

  return co(function * () {
    let cols = ['Id', 'NickName', 'Avatar', 'AvaAuthStatus']
    if (options.includeLocation) {
      cols = _.concat(cols, ['Province', 'City'])
    }
    const users = yield Users.find({Id: userIds}, {cols: cols})
    const bindRoles = yield BindRoles.getUserQnMainRoleBatch(userIds)
    const roleIds = _.map(bindRoles, 'RoleId')
    let roleInfos = []
    if (!_.isEmpty(roleIds)) {
      roleInfos = yield QnRoleInfos.find({RoleId: roleIds}, {cols: options.roleInfoCols})
    }
    const serverIds = _.map(roleInfos, 'ServerId')
    const servers = yield ServerList.findByIds(serverIds)

    const uIdToUsers = util.keyToRecordHash(users, 'Id')
    const uIdToBindRole = util.keyToRecordHash(bindRoles, 'UserId')
    const rIdToRoleInfos = util.keyToRecordHash(roleInfos, 'RoleId')
    const sIdToServers = util.keyToRecordHash(servers, 'id')
    const showUsers = []
    for (let i = 0; i < users.length; i++) {
      const u = users[i]
      const user = _.pick(u, 'NickName', 'Province', 'City')
      const userId = u.Id
      user.id = userId
      const roleId = _.get(uIdToBindRole[userId], 'RoleId')
      user.avatar = {
        url: uIdToUsers[userId].Avatar || '',
        auditStatus: uIdToUsers[userId].AvaAuthStatus
      }
      user.mainRole = null

      if (roleId && rIdToRoleInfos[roleId]) {
        const mainRole = rIdToRoleInfos[roleId] || {}
        const server = sIdToServers[mainRole.ServerId] || {}
        mainRole.serverGroup = server.group
        mainRole.serverName = server.name
        user.mainRole = mainRole
        if (mainRole.guildId) {
          const guildInfo = yield getGuild(mainRole.ServerId, mainRole.guildId)
          mainRole.guildId = parseInt(mainRole.guildId, 10)
          mainRole.guildName = _.get(guildInfo, 'Name') || ''
        }
      }
      showUsers.push(user)
    }
    return showUsers
  })
}

function getUserDetail (userId) {
  const roleInfoCols = ['RoleId', 'RoleName', 'JobId as Clazz', 'Gender', 'ServerId', 'Level', 'GangId as guildId']
  return getShowUsers([userId], {roleInfoCols: roleInfoCols})
  .then(records => {
    return _.first(records)
  })
}

module.exports = {
  getShowUsers: getShowUsers,
  getUserDetail: getUserDetail
}
