/* eslint-disable prefer-promise-reject-errors */
const request = require('../../../common/request')
const _ = require('lodash')
const MdMoments = require('../../../models/Moments')
const util = require('../../../common/util')
const { getRedis } = require('../../../common/redis')
const Promise = require('bluebird')
const config = require('../../../common/config')


function getDreamAngelApiHost() {
  if (config.testCfg.test_env) {
    return 'http://file.qn.163.com/public/xqn/youmeng_2018/koudai'
    return ''
  } else {
    return 'https://ssl.hi.163.com/file_qn/public/xqn/youmeng_2018/koudai'
  }
}

const dreamAngelApiHost = getDreamAngelApiHost()


const dreamAngelService = {}
dreamAngelService.request = (option) => {
  option.url = dreamAngelApiHost + option.path
  return request.request(option)
}

function rq(option) {
  return dreamAngelService.request(option)
    .then(res => {
      if (res && res.code >= 0) {
        return { data: res.data, msg: res.msg || '', resCode: res.code } // 梦岛使用code=0表示返回正确
      } else {
        if (res && res.code && res.msg) {
          return Promise.reject({ msg: res.msg, resCode: res.code })
        } else {
          return Promise.reject({ errorType: 'dreamAngelRequestError', msg: '请求幽梦天使后台错误', res: res })
        }
      }
    })
}

dreamAngelService.vote = function (params) {
  return rq({ path: `/vote/${params.angelId}/${params.urs}` })
}

const playerItemFormat = function (item) {
  return _.assign({}, {
    roleId: item.playerid,
    roleName: item.playername,
    serverId: item.serverid,
    imgList: item.imgs,
    desc: item.decl,
    totalVote: item.total | item.vote
  }, _.omit(item, ['playerid', 'playername', 'serverid', 'imgs', 'decl']))
}

/**
 * 列出幽梦天使时请求的参数
 * @param {Object} params
 * @param {Number} params.serverId
 * @param {String} params.search
 */
dreamAngelService.listPlayers = function (params) {
  const qs = {}
  if (params.serverId) {
    qs.serverid = params.serverId
  }
  if (params.search) {
    qs.search = params.search
  }
  return rq({ path: `/listfusai/${params.page}`, qs: qs })
    .then(resData => {
      const list = _.map(resData.data, playerItemFormat)
      return {
        list: list,
        totalPage: resData.msg
      }
    })
}

dreamAngelService.getRank = function () {
  return rq({ path: '/rank' })
    .then(resData => {
      const isFormalMatch = resData.msg !== 'chusai'
      const list = _.map(resData.data, playerItemFormat)
      return {
        isFormalMatch: isFormalMatch,
        list: list
      }
    })
}

dreamAngelService.listStories = function (params) {
  return require('../../controllers/qnPocket/moments').dreamAngelMoments(params)
}

dreamAngelService.attendMatch = function (data) {
  return rq({ path: '/apply', method: 'POST', form: data })
}

function getPocketSessionInfo(userId) {
  return getRedis().hgetallAsync('sess:pocket_user:' + userId).then(session => {
    return session || { urs: '' }
  })
}

dreamAngelService.addMomentToTopicEventHandler = function (payload) {
  const topic = payload.topic
  const userId = payload.userId
  const momentId = payload.momentId
  const text = payload.text
  if (topic === '2018幽梦天使') {
    return Promise.all([
      MdMoments.findOne({ ID: momentId }, ['UserId', 'ImgList']),
      getPocketSessionInfo(userId)
    ]).spread((moment, sessionInfo) => {
      const imgs = util.csvStrToArray(moment.ImgList)
      if (!_.isEmpty(imgs)) {
        const sendData = {
          account: sessionInfo.urs,
          playerid: sessionInfo.roleid,
          imgs: JSON.stringify(imgs),
          decl: text
        }
        return QNRoleInfos.isMale(sendData.playerid)
          .then(isMale => {
            if (isMale) {
              return dreamAngelService.attendMatch(sendData)
            }
          })
      }
    })
  }
}

module.exports = dreamAngelService
