const request = require('../../../common/request')
const _ = require('lodash')
const MdMoments = require('../../../models/Moments')
const util = require('../../../common/util')
const { getRedis } = require('../../../common/redis')
import * as bluebird from 'bluebird';
const config = require('../../../common/config')
import * as QnRoleInfo from '../../../models/QNRoleInfos'

import { getLogger } from '../../../common/logger2'
let logger = getLogger('mingshi')

interface MingshiConfig {
  haixuan: TimeRange
  fusai: TimeRange
  fusai_sqs: TimeRange
}

interface TimeRange {
  startDate: string
  endDate: string
}

const MingShiCfg: MingshiConfig = config.dreamMingshi


function getDreamAngelApiHost() {
  if (config.testCfg.test_env) {
    return 'http://file.qn.163.com/public/xqn/youmengmingshi_2019/koudai'
  } else {
    return 'https://ssl.hi.163.com/file_qn/public/xqn/youmengmingshi_2019/koudai'
  }
}

const dreamAngelApiHost = getDreamAngelApiHost()

export let requestApi = (option) => {
  option.url = dreamAngelApiHost + option.path
  return request.request(option)
}

function rq(option) {
  logger.info('RequestMingShiApi', option)
  return requestApi(option)
    .then(res => {
      if (res && res.code >= 0) {
        return { data: res.data, msg: res.msg || '', resCode: res.code } // 梦岛使用code=0表示返回正确
      } else {
        if (res && res.code && res.msg) {
          return bluebird.reject({ msg: res.msg, resCode: res.code })
        } else {
          return bluebird.reject({ errorType: 'dreamAngelRequestError', msg: '请求幽梦天使后台错误', res: res })
        }
      }
    }).catch(err => {
      logger.error('RequestMingShiApiError', err)
      return bluebird.reject(err)
    })
}

function isCanVoteNow() {
  return isDateInRange(MingShiCfg.haixuan) || isDateInRange(MingShiCfg.fusai) || isDateInRange(MingShiCfg.fusai_sqs)
}

export let vote = function (params) {
  if (isCanVoteNow()) {
    return rq({ path: `/vote/${params.angelId}/${params.urs}` })
  } else {
    return bluebird.reject({ message: '投票已截止' })
  }
}

const playerItemFormat = function (item) {
  return _.assign({}, {
    roleId: item.playerid,
    roleName: item.playername,
    serverId: item.serverid,
    imgList: item.imgs,
    desc: item.decl,
    totalVote: item.total | item.vote
  }, _.omit(item, ['playerid', 'playername', 'serverid', 'imgs', 'decl']))
}

function isAfterDate(dateStr: string) {
  return Date.now() > new Date(dateStr).getTime()
}

function isBeforeDate(dateStr: string) {
  return Date.now() < new Date(dateStr).getTime()
}

function isDateInRange(tr: TimeRange) {
  return isAfterDate(tr.startDate) && isBeforeDate(tr.endDate)
}


function getListPath(page: number) {
  if (isAfterDate(MingShiCfg.fusai.startDate)) {
    return '/listfusai/' + page
  } else {
    return '/list/' + page
  }
}

/**
 * 列出幽梦天使时请求的参数
 * @param {Object} params
 * @param {Number} params.serverId
 * @param {String} params.search
 */
export let listPlayers = function (params) {
  const qs = { serverid: '', search: '', type: '' }
  if (params.serverId) {
    qs.serverid = params.serverId
  }
  if (params.search) {
    qs.search = params.search
  }

  if (isAfterDate(MingShiCfg.fusai_sqs.startDate)) {
    qs.type = 'sqs'
  }

  let path = getListPath(params.page)
  return rq({ path: path, qs: qs })
    .then(resData => {
      const list = _.map(resData.data, playerItemFormat)
      return {
        list: list,
        totalPage: resData.msg
      }
    })
}

export let getRank = function () {
  return rq({ path: '/rank' })
    .then(resData => {
      const isFormalMatch = resData.msg !== 'chusai'
      const list = _.map(resData.data, playerItemFormat)
      return {
        isFormalMatch: isFormalMatch,
        list: list
      }
    })
}

export let listStories = function (params) {
  return require('../../controllers/qnPocket/moments').dreamAngelMoments(params)
}

export let attendMatch = function (data) {
  return rq({ path: '/apply', method: 'POST', form: data })
}

function getPocketSessionInfo(userId) {
  return getRedis().hgetallAsync('sess:pocket_user:' + userId).then(session => {
    return session || { urs: '' }
  })
}

export let addMomentToTopicEventHandler = function (payload) {
  const topic = payload.topic
  const userId = payload.userId
  const momentId = payload.momentId
  const text = payload.text

  if (topic === '2019幽梦名士') {
    let date = new Date(config.dreamMingshi.endDate)
    if (Date.now() > date.getTime()) {
      logger.info('活动关闭')
      return
    }

    return bluebird.all([
      MdMoments.findOne({ ID: momentId }, ['UserId', 'ImgList']),
      getPocketSessionInfo(userId)
    ]).spread((moment, sessionInfo) => {
      const imgs = util.csvStrToArray(moment.ImgList)
      if (!_.isEmpty(imgs)) {
        let playerId = sessionInfo.roleid
        const sendData = {
          account: sessionInfo.urs,
          playerid: playerId,
          imgs: JSON.stringify(imgs),
          decl: text
        }
        return QnRoleInfo.isMale(playerId).then(isMale => {
          if (isMale) {
            return attendMatch(sendData)
          }
        })
      }
    })
  }
}