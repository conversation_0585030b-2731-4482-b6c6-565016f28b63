const dreamAngelService = require('./dreamAngel')
const _ = require('lodash')

const dreamAngelFixtureService = {}
dreamAngelFixtureService.listPlayers = () => {
  return Promise.resolve([
    {
      id: 1,
      roleId: 10050344,
      serverId: 344,
      roleName: '．无限幸福～',
      boyVote: 11,
      girlVote: 12
    },
    {
      id: 2,
      roleId: 10050341,
      serverId: 341,
      roleName: '一叶之舞',
      boyVote: 9,
      girlVote: 8
    }
  ])
}

dreamAngelFixtureService.getRank = () => {
  return Promise.resolve({
    isFormalMatch: false,
    list: [
      {
        roleId: 10030020,
        roleName: '渐隐花絮',
        serverId: 341,
        flower: 0,
        vote: 21,
        jobId: 7
      },
      {
        id: 2,
        roleId: 10050341,
        roleName: '一叶之舞',
        serverId: 341,
        flower: 0,
        vote: 30
      }
    ]
  })
}

const Service = _.defaults(dreamAngelFixtureService, dreamAngelService)

module.exports = Service
