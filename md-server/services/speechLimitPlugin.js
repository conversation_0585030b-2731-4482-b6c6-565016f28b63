"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.speechLimitPlugin = void 0;
const _ = require("lodash");
const SPEECH_APIS = [
    '/md/setuserinfo',
    '/md/qnm/addmoment',
    '/md/qnm/addcomm',
    '/md/qnm/addmsg',
    '/md/qnm/ansmsg',
    '/md/comments/new',
    '/md/pocket/moments/create',
    '/md/pocket/comments/create',
    '/md/pocket/messages/create',
    '/md/show_topics/new',
    '/md/reality_show/apply',
    '/md/photos/new',
    '/md/tongren/comment/add',
];
function isUrlHit(url, checkUrls) {
    return _.some(checkUrls, r => {
        return url.startsWith(r);
    });
}
const ALERT_MSG = "因技术升级，即日起至6月6日期间该功能暂不可用，对您造成不便深感歉意。";
function speechLimitPlugin(req, res, next) {
    let url = req.route.path;
    if (isUrlHit(url, SPEECH_APIS)) {
        res.send({ code: -100, msg: ALERT_MSG });
    }
    else {
        next();
    }
}
exports.speechLimitPlugin = speechLimitPlugin;
//# sourceMappingURL=speechLimitPlugin.js.map