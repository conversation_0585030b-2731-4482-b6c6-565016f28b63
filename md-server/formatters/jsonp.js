/**
 * Created by <PERSON><PERSON><PERSON> on 2017/3/2.
 */

'use strict';

///--- Exports

/**
 * JSONP formatter. like JSON, but with a callback invocation.
 * @public
 * @function formatJSONP
 * @param    {Object} req  the request object
 * @param    {Object} res  the response object
 * @param    {Object} body response body
 * @param    {Function} cb cb
 * @returns  {String}
 */
function formatJSONP(req, res, body, cb) {
  if (!body) {
    res.setHeader('Content-Length', 0);
    return (null);
  }

  if (body instanceof Error) {
    if ((body.restCode || body.httpCode) && body.body) {
      body = body.body;
    } else {
      body = {
        message: body.message
      };
    }
  }

  if (Buffer.isBuffer(body)) {
    body = body.toString('base64');
  }

  var _cb = req.query.callback || req.query.jsonp;
  var data;

  body = JSON.stringify(body)
    .replace(/\u2028/g, '\\u2028')
    .replace(/\u2029/g, '\\u2029');


  if (_cb) {
    data = 'typeof ' + _cb + ' === \'function\' && ' +
      _cb + '(' + body + ');';
  } else {
    data = body;
  }

  res.setHeader('Content-Length', Buffer.byteLength(data));
  return cb(null, data);
}

module.exports = formatJSONP;
