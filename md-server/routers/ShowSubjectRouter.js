/**
 * Created by <PERSON><PERSON><PERSON> on 2017/1/4.
 */

let Router = require('./index').Router;
let ShowSubjectRouter = new Router({path:'/show_subjects'});
let ShowSubjectsController = require('../controllers/ShowSubjectsController');

ShowSubjectRouter.get("", ShowSubjectsController.list);
ShowSubjectRouter.get("/search", ShowSubjectsController.search);
ShowSubjectRouter.get("/slide_show", ShowSubjectsController.listSlideShow);
ShowSubjectRouter.get("/:subject_id", ShowSubjectsController.get);
ShowSubjectRouter.get("/:subject_id/show_topics", ShowSubjectsController.listShowTopics);

module.exports = ShowSubjectRouter;
