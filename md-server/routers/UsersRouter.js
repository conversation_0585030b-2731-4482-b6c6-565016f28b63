/**
 * Created by <PERSON><PERSON><PERSON> on 2017/1/4.
 */

let Router = require('./index').Router;
let UsersRouter = new Router();
let UsersController = require('../controllers/UsersController');

UsersRouter.get('/games/:game_name/users/recommend', UsersController.getRecommendUsersByGame);
UsersRouter.get('/games/:game_name/users/kwsearch', UsersController.kwSearch);
UsersRouter.get('/games/:game_name/users/fuzzy_search', UsersController.fuzzySearch);
UsersRouter.get("/games/:game_name/users/:user_id/home", UsersController.home);
// UsersRouter.get("/users/login", UsersController.userLogin);
UsersRouter.post("/users/login", UsersController.userLogin);    // 使用用户名密码登录
UsersRouter.get("/users/login/ngp", UsersController.ngpLogin);  // 使用URS组件登录
UsersRouter.get("/users/get_recent_urs_login_info", UsersController.getRecentLoginInfo);
UsersRouter.get("/users/queryUserSecInfoStatus", UsersController.queryUserSecInfoStatus);
UsersRouter.get("/users/verifyintegritymibao2", UsersController.verifyintegritymibao2);

module.exports = UsersRouter;
