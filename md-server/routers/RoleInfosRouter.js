/**
 * Created by <PERSON><PERSON><PERSON> on 2017/1/4.
 */

let Router = require('./index').Router;
let RoleInfosRouter = new Router();
let RoleInfosController = require('../controllers/RoleInfosController');
let config = require('../../common/config');

RoleInfosRouter.get("/games/:game_name/role_infos", RoleInfosController.list);
RoleInfosRouter.get("/games/:game_name/users/:user_id/role_infos/bind", RoleInfosController.listBindRole);
RoleInfosRouter.get("/users/:user_id/role_infos/bind", RoleInfosController.listBindRole);
RoleInfosRouter.get("/games/:game_name/role_infos/:role_id", RoleInfosController.get);
RoleInfosRouter.get("/games/:game_name/main_role_info", RoleInfosController.mainRoleInfo);
RoleInfosRouter.get("/users/role_infos/bind", {allow_ips: config.qnMicroFilmServerList}, RoleInfosController.listBindRole);

module.exports = RoleInfosRouter;
