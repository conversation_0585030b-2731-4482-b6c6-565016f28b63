/**
 * Created by <PERSON><PERSON><PERSON> on 2017/1/4.
 */

const Router = require('./index').Router
const PhotosRouter = new Router({path: '/photos'})
const PhotosController = require('../controllers/PhotosController')
const auditAllowIps = require('../../common/config').tongrenWorkAuditList // 作品审核后
PhotosRouter.get('/new', PhotosController.create)
PhotosRouter.get('/delete', PhotosController.batchDelete)
PhotosRouter.get('/move', PhotosController.batchMove)
PhotosRouter.get('/qn/listPicks', PhotosController.listPicksForQn)
PhotosRouter.post('/qn/pick', {allow_ips: auditAllowIps}, PhotosController.pickPhoto)

PhotosRouter.get('/:photo_id', PhotosController.get)
PhotosRouter.get('/:photo_id/update', PhotosController.update)
PhotosRouter.get('/:photo_id/delete', PhotosController.delete)
PhotosRouter.get('/:photo_id/asCover', PhotosController.asCover)
PhotosRouter.get('/:photo_id/move', PhotosController.move)

module.exports = PhotosRouter
