/**
 * Created by <PERSON><PERSON><PERSON> on 2017/6/26.
 */

const Router = require('./index').Router
const QnPocketRouter = new Router({ path: '/pocket' })

QnPocketRouter.use('/', function (req, res, next) {
  const util = require('../../common/util')
  res.succSend = function (data) {
    res.send({ code: 0, data: util.keysToCamelCase(data) })
  }
  next()
})

// auth
const authController = require('../controllers/qnPocket/auth')
QnPocketRouter.get('/auth/check_status', authController.checkStatus)
QnPocketRouter.get('/auth/login', authController.login)
QnPocketRouter.post('/auth/login', authController.login)
QnPocketRouter.post('/auth/logout', authController.logout)

// moments
const momentsController = require('../controllers/qnPocket/moments')
QnPocketRouter.post('/moments/create', momentsController.create)
QnPocketRouter.get('/moments/show', momentsController.show)
QnPocketRouter.get('/moments/user_timeline', momentsController.userTimeLine)
QnPocketRouter.get('/moments/home_timeline', momentsController.homeTimeLine)
QnPocketRouter.get('/moments/hot', momentsController.hot)
QnPocketRouter.post('/moments/destroy', momentsController.destroy)

// likes
const likesController = require('../controllers/qnPocket/likes')
QnPocketRouter.post('/likes/create', likesController.create)
QnPocketRouter.post('/likes/destroy', likesController.destroy)

// comments
const commentsController = require('../controllers/qnPocket/comments')
QnPocketRouter.get('/comments/list', commentsController.list)
QnPocketRouter.post('/comments/create', commentsController.create)
QnPocketRouter.post('/comments/reply', commentsController.reply)
QnPocketRouter.post('/comments/destroy', commentsController.destroy)

// friendships
const friendshipsController = require('../controllers/qnPocket/friendships')
QnPocketRouter.get('/friendships/friends', friendshipsController.friends)
QnPocketRouter.get('/friendships/followers', friendshipsController.followers)
QnPocketRouter.post('/friendships/create', friendshipsController.create)
QnPocketRouter.post('/friendships/destroy', friendshipsController.destroy)

// messages
const messagesController = require('../controllers/qnPocket/messages')
QnPocketRouter.get('/messages/show', messagesController.show)
QnPocketRouter.get('/messages/list', messagesController.list)
QnPocketRouter.post('/messages/create', messagesController.create)
QnPocketRouter.post('/messages/destroy', messagesController.destroy)

// users
const usersController = require('../controllers/qnPocket/users')
QnPocketRouter.get('/users/show', usersController.show)
QnPocketRouter.post('/users/update_avatar', usersController.updateAvatar)
QnPocketRouter.post('/users/update_location', usersController.updateLocation)

// informs 通知
const informsController = require('../controllers/qnPocket/informs')
QnPocketRouter.get('/informs/list', informsController.list)
QnPocketRouter.get('/informs/count', informsController.count)
QnPocketRouter.post('/informs/mark_read', informsController.markRead)

// common
const commonController = require('../controllers/qnPocket/common')
QnPocketRouter.get('/common/get_location_by_ip', commonController.getLocationFromIp)
QnPocketRouter.get('/common/get_nos_token', commonController.getNosToken)

// report 举报
const reportsController = require('../controllers/qnPocket/reports')
QnPocketRouter.post('/reports/create', reportsController.create)

const dreamAngelController = require('../controllers/qnPocket/dreamAngel')
QnPocketRouter.post('/dream_angel/vote', dreamAngelController.vote)
QnPocketRouter.get('/dream_angel/players/list', dreamAngelController.listPlayers)
QnPocketRouter.get('/dream_angel/players/rank', dreamAngelController.getPlayerRank)
QnPocketRouter.get('/dream_angel/stories/list', dreamAngelController.listStories)

const dreamMingshiController = require('../controllers/qnPocket/dreamMingshi')
QnPocketRouter.post('/dream_mingshi/vote', dreamMingshiController.vote)
QnPocketRouter.get('/dream_mingshi/players/list', dreamMingshiController.listPlayers)
QnPocketRouter.get('/dream_mingshi/players/rank', dreamMingshiController.getPlayerRank)
QnPocketRouter.get('/dream_mingshi/stories/list', dreamMingshiController.listStories)

module.exports = QnPocketRouter
