/**
 * Created by <PERSON><PERSON><PERSON> on 2017/1/4.
 */

const _ = require('lodash')
const routers = module.exports
const util = require('../../common/util')
const config = require('../../common/config')

const globalApiPrefix = '/md'

function Router (option) {
  option = _.defaults(option, {path: ''})
  this.option = option
  this.routes = []
  this.uses = []
}

Router.prototype = {}

const httpMethods = ['get', 'del', 'post', 'put']

_.forEach(httpMethods, function (m) {
  Router.prototype[m] = function (url, handler) {
    const args = _.slice(arguments)
    this.routes.push({
      method: m,
      url: url,
      handler: _.tail(args)
    })
  }
})

Router.prototype.use = function (url, handler) {
  this.uses.push({
    url: url || '',
    handler: handler
  })
}

function ipLimitMiddleware (allowIps) {
  return function (req, res, next) {
    if (!config.testCfg.skip_ip_whitelist_check && allowIps.join(';').indexOf(util.getIp(req)) < 0) {
      res.send(util.response({code: -2, msg: '请求的Ip非法'}))
    } else {
      next()
    }
  }
}

function allowOriginMiddleware (allowOrigin) {
  function isOriginAllowed (origin, allowedOrigin) {
    if (_.isArray(allowedOrigin)) {
      for (let i = 0; i < allowedOrigin.length; i++) {
        if (isOriginAllowed(origin, allowedOrigin[i])) {
          return true
        }
      }
      return false
    } else if (_.isString(allowedOrigin)) {
      return origin === allowedOrigin
    } else if (allowedOrigin instanceof RegExp) {
      return allowedOrigin.test(origin)
    } else {
      return !!allowedOrigin
    }
  }

  return function (req, res, next) {
    const requestOrigin = req.headers.origin
    if (isOriginAllowed(requestOrigin, allowOrigin)) {
      res.header('Access-Control-Allow-Origin', requestOrigin)
      res.header('Access-Control-Allow-Credentials', 'true')
      next()
    } else {
      res.send(util.response({ code: -2, msg: '非法请求' }))
    }
  }
}

function formatHandlers (handlers) {
  return handlers.map(handler => {
    if (_.isFunction(handler)) {
      return handler
    } else {
      if (handler.allow_ips) {
        return ipLimitMiddleware(handler.allow_ips)
      }
      if (handler.allow_origin) {
        return allowOriginMiddleware(handler.allow_origin)
      }
    }
  })
}

Router.prototype.mount = function (server) {
  const self = this
  // handle middleware only prefix with route path
  _.forEach(this.uses, useInfo => {
    server.use(function (req, res, next) {
      const path = req.route.path
      if (path.startsWith(globalApiPrefix + self.option.path + useInfo.url)) {
        useInfo.handler(req, res, next)
      } else {
        next()
      }
    })
  })
  // mount http method route
  _.forEach(this.routes, function (r) {
    const mountUrl = globalApiPrefix + self.option.path + r.url
    const handlers = formatHandlers(r.handler)
    server[r.method].apply(server, _.concat(mountUrl, handlers))
  })
}

routers.Router = Router

routers.mount = function (server) {
  const path = require('path')
  require('fs').readdirSync(path.join(__dirname, '/')).forEach(function (file) {
    if (file !== 'index.js') {
      require('./' + file).mount(server)
    }
  })
}
