/**
 * Created by <PERSON><PERSON><PERSON> on 2017/1/4.
 */

let Router = require('./index').Router;
let RealityShowRouter = new Router({path:''});
let RealityShowController = require('../controllers/RealityShowController');

/* 这里有个typo， 照理应该用复数的*/
RealityShowRouter.get("/reality_show/apply", RealityShowController.apply);

RealityShowRouter.get("/reality_shows/search", RealityShowController.search);
RealityShowRouter.get("/reality_shows", RealityShowController.list);
RealityShowRouter.get("/reality_shows/events", RealityShowController.events);

module.exports = RealityShowRouter;
