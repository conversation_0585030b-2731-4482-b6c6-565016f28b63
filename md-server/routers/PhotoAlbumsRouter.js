/**
 * Created by <PERSON><PERSON><PERSON> on 2017/1/4.
 */

let Router = require('./index').Router;
let PhotoAlbumsRouter = new Router({path:''});
let PhotoAlbumsController = require('../controllers/PhotoAlbumsController');

PhotoAlbumsRouter.get("/photo_albums", PhotoAlbumsController.list);
PhotoAlbumsRouter.get("/photo_albums/:album_id/photos", PhotoAlbumsController.listPhotos);
PhotoAlbumsRouter.get("/photo_albums/new", PhotoAlbumsController.create);
PhotoAlbumsRouter.get("/photo_albums/:album_id/delete", PhotoAlbumsController.delete);
PhotoAlbumsRouter.get("/photo_albums/:album_id/update", PhotoAlbumsController.update);
PhotoAlbumsRouter.get("/public/photo_albums/:album_id/photos", PhotoAlbumsController.listPublicPhotos); //no Auth 供图库使用

module.exports = PhotoAlbumsRouter;
