/**
 * Created by zhenhua on 2017/4/1.
 */

const Router = require('./index').Router;
const TukuRouter = new Router();
const TukuController  = require('../controllers/TukuController');

TukuRouter.post("/qnm/tuku/set_audit_results", TukuController.setAuditResults);
TukuRouter.get("/qnm/tuku/pyq_moment_photo/pick", TukuController.listPickPyqMomentPhoto);
TukuRouter.get("/qnm/tuku/pyq_moment_photo/list", TukuController.listPyqMomentPhoto);

module.exports = TukuRouter;
