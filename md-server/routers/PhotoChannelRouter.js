/**
 * Created by <PERSON>hen<PERSON> on 2017/1/4.
 */
let Router = require('./index').Router;
let PhotoChannelRouter = new Router();
let PhotoChannelController = require('../controllers/PhotoChannelController.js');

PhotoChannelRouter.get("/photo_channels/:channel_id/photos", PhotoChannelController.listPhotos);
PhotoChannelRouter.get("/public/photo_channels/:channel_id/albums", PhotoChannelController.listPhotoAlbums);
PhotoChannelRouter.get("/public/photo_channels/:channel_id/albums/editor_pick", PhotoChannelController.listEditorPickAlbums);

module.exports = PhotoChannelRouter;
