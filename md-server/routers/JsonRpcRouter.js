const Router = require('./index').Router
const JsonRpcRouter = new Router()
const RpcServer = require('../jsonRpc/rpcServer.js')
const util = require('../../common/util')
const _ = require('lodash')

function restifyMiddleware (rpcServer) {
  return function (req, res, next) {
    rpcServer.call(req.body, (err, retValue) => {
      if (err) {
        res.send(err)
        next(err)
      } else {
        res.send(retValue)
      }
    })
  }
}

function jsonRpcAuth (req, res, next) {
  const secret = 'aBu9PS4zDnEKg3vX'
  const method = req.body.method
  const params = req.body.params.slice(0, req.body.params.length - 1)
  const hashStr = method + params.join('') + secret
  const calToken = util.hexMd5(hashStr)
  const token = _.last(req.body.params)
  if (calToken === token) {
    return next()
  } else {
    res.send({jsonrpc: req.body.jsonrpc, error: {code: -320001, message: 'auth failed'}})
  }
}

JsonRpcRouter.post('/jsonrpc', jsonRpcAuth, restifyMiddleware(RpcServer))

module.exports = JsonRpcRouter
