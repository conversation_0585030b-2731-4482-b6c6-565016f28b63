let Router = require('./index').Router;
let TongrenRouter = new Router({ path: '/tongren' });
const CONFIG = require('../../common/config');
const WORK_AUDIT_ALLOW_IPS = CONFIG.tongrenWorkAuditList; // 作品审核后台白名单

let TongrenWorkController = require('../controllers/TongrenWorkController');
let TongrenCommentController = require('../controllers/TongrenCommentController');
let TongrenLikeController = require('../controllers/TongrenLikeController');

const EDIT_API_ALLOW_ORIGIN = [ // 投稿API允许跨域的HOST
  'http://test.163.com',
  'http://test.nie.163.com',
  'https://new.hi.163.com'
];

TongrenRouter.get('/home', TongrenWorkController.home);
TongrenRouter.get('/score_ratio', TongrenWorkController.getScoreRatio);
TongrenRouter.get('/list', TongrenWorkController.list);
TongrenRouter.get('/work/:id', TongrenWorkController.detail);
TongrenRouter.get('/work/:id/delete', TongrenWorkController.delete);
TongrenRouter.get('/work/:workid/score_ratio', TongrenWorkController.getScoreRatioOfWork);
TongrenRouter.get('/search', TongrenWorkController.search);
TongrenRouter.get('/like', TongrenLikeController.like);
TongrenRouter.get('/unlike', TongrenLikeController.unLike);
TongrenRouter.get('/comment/get', TongrenCommentController.get);
TongrenRouter.get('/comment/add', TongrenCommentController.add);
TongrenRouter.get('/comment/delete', TongrenCommentController.delete);
TongrenRouter.post('/edit', { allow_origin: EDIT_API_ALLOW_ORIGIN }, TongrenWorkController.edit);
TongrenRouter.get('/audit', { allow_ips: WORK_AUDIT_ALLOW_IPS }, TongrenWorkController.audit);

module.exports = TongrenRouter;