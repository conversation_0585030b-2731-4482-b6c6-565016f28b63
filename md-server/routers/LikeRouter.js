/**
 * Created by <PERSON>hen<PERSON> on 2017/1/4.
 */

let Router = require('./index').Router;
let LikeRouter = new Router();
let LikesController = require('../controllers/LikesController');

LikeRouter.get("/like", LikesController.like);
LikeRouter.get("/unlike", LikesController.unLike);
LikeRouter.get("/likes/photos/:photo_id/new", LikesController.likePhoto);
LikeRouter.get("/likes/photos/:photo_id/delete", LikesController.cancelLikePhoto);

module.exports = LikeRouter;
