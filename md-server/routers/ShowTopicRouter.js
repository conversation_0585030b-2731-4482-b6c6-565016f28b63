/**
 * Created by <PERSON><PERSON><PERSON> on 2017/1/4.
 */
let Router = require('./index').Router;
let ShowTopicsRouter = new Router();
let ShowTopicsController = require('../controllers/ShowTopicsController');

ShowTopicsRouter.get("/show_topics/new", ShowTopicsController.create);
ShowTopicsRouter.get("/show_topics/search", ShowTopicsController.search);
ShowTopicsRouter.get("/show_subjects/:subject_id/show_topics/search", ShowTopicsController.search);
ShowTopicsRouter.get("/show_topics/:topic_id", ShowTopicsController.get);
ShowTopicsRouter.get("/show_topics/:topic_id/delete", ShowTopicsController.delete);
ShowTopicsRouter.get("/show_topics/:topic_id/update", ShowTopicsController.update);
ShowTopicsRouter.get("/users/:user_id/show_topics", ShowTopicsController.listByUser);

module.exports = ShowTopicsRouter;
