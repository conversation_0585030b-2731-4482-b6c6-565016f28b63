{
    "files.exclude": {
        "**/.DS_Store": true,
        "**/.git": true,
        "**/.hg": true,
        "**/.svn": true,
        "**/*.js": {
            "when": "$(basename).ts"
        },
        "**/*.js.map": true,
        "**/CVS": true,
        "doc/md": true,
        "doc/qn": true,
        "audit-server": true,
        "bw-server": true,
        "ccsdk-server": true,
        "lib": true,
        "md-server": true,
        "migrations": true,
        "node_modules": true,
        "qn-server": true,
        "opTools": true,
        "qngame-server": true,
        "opTools-server": true,
        "data": true,
        "bin": true,
        "assert": true,
        "tool": true,
        "service/qn": true,
        "service/qnm/md": true,
        "scripts": true,
        "pyq-server/scripts/as2cn": true,
        "cron-jobs": false,
        "qnm-server": false,
        "test": true
    },
    "cSpell.words": [
        "careerid",
        "chusai",
        "clazz",
        "Commentable",
        "dashen",
        "Fenghua",
        "filepick",
        "flowerrenqi",
        "fusai",
        "getprofile",
        "imgs",
        "jsbridge",
        "juesai",
        "lastid",
        "lianghao",
        "Lzma",
        "middlewares",
        "ndzd",
        "Ndzj",
        "Popo",
        "Proxied",
        "PRPASSED",
        "Qian",
        "quan",
        "qyfh",
        "qyfhguild",
        "renqi",
        "resouce",
        "roleid",
        "roleinfo",
        "rolename",
        "roleprop",
        "sendflower",
        "sendflowerrenqi",
        "serverid",
        "sess",
        "Skey",
        "slient",
        "targetid",
        "Tuku",
        "workid",
        "workname",
        "xianfanstatus",
        "Yunying",
        "yunyinglog",
        "zhenhua"
    ],
    "typescript.tsdk": "node_modules/typescript/lib",
    "mochaExplorer.logpanel": true,
    "mochaExplorer.env": {
        "NODE_ENV": "test"
    },
    "mochaExplorer.exit": true,
    "mochaExplorer.optsFile": "",
    "mochaExplorer.files": [
        "spec/**/*.test.ts",
    ],
    "mochaExplorer.require": ["espower-typescript/guess"],
    "mochaExplorer.debuggerConfig": "",
}
