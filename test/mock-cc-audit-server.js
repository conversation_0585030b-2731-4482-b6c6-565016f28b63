const http = require('http');
const url = require('url');

// 存储审核请求，用于测试验证
const auditRequests = [];

// 解析JSON请求体的工具函数
function parseJsonBody(req) {
  return new Promise((resolve, reject) => {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        resolve(body ? JSON.parse(body) : {});
      } catch (err) {
        reject(err);
      }
    });
  });
}

// 发送JSON响应的工具函数
function sendJsonResponse(res, statusCode, data) {
  res.writeHead(statusCode, {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
  });
  res.end(JSON.stringify(data, null, 2));
}

// 创建HTTP服务器
const server = http.createServer(async (req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const pathname = parsedUrl.pathname;
  const query = parsedUrl.query;

  console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);

  // 处理OPTIONS请求（CORS预检）
  if (req.method === 'OPTIONS') {
    res.writeHead(204, {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400'
    });
    res.end();
    return;
  }

  // 处理Mock CC审核接口
  if (req.method === 'POST' && pathname === '/inner/v1/audiorectranslatorext/custom_audio_job/create') {
    try {
      const body = await parseJsonBody(req);

      console.log('\n=== 收到CC审核请求 ===');
      console.log('请求数据:', JSON.stringify(body, null, 2));

      // 保存请求用于验证
      auditRequests.push({
        timestamp: Date.now(),
        data: body
      });

      // 从查询参数获取测试场景，或者从audio_url中提取
      let scenario = query.scenario || 'success';

      // 如果audio_url包含scenario参数，优先使用
      if (body.audio_url && body.audio_url.includes('scenario=')) {
        const urlMatch = body.audio_url.match(/[?&]scenario=([^&]+)/);
        if (urlMatch) {
          scenario = urlMatch[1];
          console.log('从audio_url提取场景:', scenario);
        }
      }

      console.log('最终测试场景:', scenario);

      // 模拟不同的响应场景
      switch (scenario) {
        case 'success':
          const taskId = `mock_task_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
          const successResponse = {
            code: 0,
            message: 'success',
            data: {
              taskId: taskId
            }
          };
          console.log('返回成功响应:', successResponse);
          sendJsonResponse(res, 200, successResponse);
          break;

        case 'error':
          const errorResponse = {
            code: -1,
            message: '审核服务暂时不可用'
          };
          console.log('返回错误响应:', errorResponse);
          sendJsonResponse(res, 200, errorResponse);
          break;

        case 'timeout':
          console.log('模拟超时 - 不响应请求');
          // 不响应，模拟超时
          return;

        case 'slow':
          console.log('模拟慢响应 - 5秒后返回');
          setTimeout(() => {
            const slowResponse = {
              code: 0,
              message: 'success',
              data: {
                taskId: `slow_task_${Date.now()}`
              }
            };
            sendJsonResponse(res, 200, slowResponse);
          }, 5000);
          return;

        default:
          sendJsonResponse(res, 400, {
            code: -1,
            message: `未知测试场景: ${scenario}`
          });
      }
    } catch (err) {
      console.error('处理请求出错:', err);
      sendJsonResponse(res, 500, {
        code: -1,
        message: '服务器内部错误',
        error: err.message
      });
    }
    return;
  }

  // 获取所有审核请求记录（用于测试验证）
  if (req.method === 'GET' && pathname === '/test/audit-requests') {
    sendJsonResponse(res, 200, {
      total: auditRequests.length,
      requests: auditRequests
    });
    return;
  }

  // 清空审核请求记录
  if (req.method === 'DELETE' && pathname === '/test/audit-requests') {
    auditRequests.length = 0;
    sendJsonResponse(res, 200, { message: '审核请求记录已清空' });
    return;
  }

  // 模拟审核回调（用于测试）
  if (req.method === 'POST' && pathname === '/test/trigger-callback') {
    try {
      const body = await parseJsonBody(req);
      const { vocalUrl, auditStatus = 1, rejectReason } = body;

      if (!vocalUrl) {
        sendJsonResponse(res, 400, { error: 'vocalUrl is required' });
        return;
      }

      console.log(`\n=== 触发审核回调 ===`);
      console.log(`vocalUrl: ${vocalUrl}`);
      console.log(`auditStatus: ${auditStatus}`);
      console.log(`rejectReason: ${rejectReason || 'N/A'}`);

      // 模拟向主服务发送回调
      const callbackData = {
        vocalUrl,
        auditStatus,
        rejectReason
      };

      const postData = JSON.stringify(callbackData);

      const options = {
        hostname: 'localhost',
        port: 9992, // 主服务运行在9992端口
        path: '/qnm/music_club/recording/audit_callback',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(postData)
        }
      };

      const callbackReq = http.request(options, (callbackRes) => {
        let data = '';
        callbackRes.on('data', (chunk) => {
          data += chunk;
        });
        callbackRes.on('end', () => {
          console.log('回调响应:', data);
          sendJsonResponse(res, 200, {
            message: '回调已发送',
            response: data
          });
        });
      });

      callbackReq.on('error', (err) => {
        console.error('回调发送失败:', err);
        sendJsonResponse(res, 500, {
          error: '回调发送失败',
          details: err.message
        });
      });

      callbackReq.write(postData);
      callbackReq.end();
    } catch (err) {
      sendJsonResponse(res, 500, {
        error: '处理回调请求失败',
        details: err.message
      });
    }
    return;
  }

  // 健康检查
  if (req.method === 'GET' && pathname === '/health') {
    sendJsonResponse(res, 200, {
      status: 'ok',
      timestamp: new Date().toISOString(),
      totalRequests: auditRequests.length
    });
    return;
  }

  // 404 处理
  sendJsonResponse(res, 404, {
    error: 'Not Found',
    path: pathname,
    method: req.method
  });
});

const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
  console.log(`\n🚀 Mock CC审核服务已启动`);
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`📋 可用的测试场景:`);
  console.log(`   - success: 成功响应 (默认)`);
  console.log(`   - error: 错误响应`);
  console.log(`   - timeout: 超时不响应`);
  console.log(`   - slow: 慢响应(5秒)`);
  console.log(`\n📖 使用方法:`);
  console.log(`   POST /inner/v1/audiorectranslatorext/custom_audio_job/create?scenario=success`);
  console.log(`   GET  /test/audit-requests - 查看所有审核请求`);
  console.log(`   POST /test/trigger-callback - 触发审核回调`);
  console.log(`   GET  /health - 健康检查`);
  console.log(`\n等待审核请求...\n`);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n👋 Mock CC审核服务正在关闭...');
  server.close(() => {
    console.log('服务已关闭');
    process.exit(0);
  });
});
