/**
 * Created by <PERSON><PERSON><PERSON> on 16-11-11.
 */

let fs = require('fs');

var content = fs.readFileSync('./行政区划代码.txt', 'utf-8');

var lines = content.split('\n');

lines = lines.filter(x => !x.startsWith('#')); //skip comment

lines = lines.map(x => x.split(/\b/)
  .map(x => x.trim()))
  .map(x => {
    return {id: x[0], name:x[1]}
  });

fs.writeFileSync("areas.json", JSON.stringify(lines, null, 2));
