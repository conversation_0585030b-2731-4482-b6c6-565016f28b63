/**
 * Created by zhenhua on 16-11-11.
 */

var area = module.exports;
var _ = require('lodash');
var areaData = require('./areas.json');
var util = require('../common/util');

area.getAllProvincesAreas = _.memoize(function () {
  return areaData.filter(function (area) {
    return area.id.endsWith("0000");
  });
});

area.getAllProvinces = _.memoize(function () {
  return area.getAllProvincesAreas().map(x => x.name);
});

area.getAreaIdByProvince = function (province) {
  var results = area.getAllProvincesAreas();
  var nameToRecord =  util.keyToRecordHash(results, 'name');
  return nameToRecord[province].id || null;
};
