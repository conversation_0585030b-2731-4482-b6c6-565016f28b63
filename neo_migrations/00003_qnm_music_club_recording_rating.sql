-- +goose Up
-- +goose StatementBegin
CREATE TABLE `qnm_music_club_recording_rating` (
    `ID` BIGINT(20) PRIMARY KEY AUTO_INCREMENT NOT NULL COMMENT '评分ID',
    `RoleId` BIGINT(20) NOT NULL COMMENT '评分的角色ID',
    `MusicClubId` BIGINT(20) NOT NULL COMMENT '乐团ID',
    `RecordingId` BIGINT(20) NOT NULL COMMENT '唱片ID',
    `Rating` INT(10) NOT NULL COMMENT '评分 1-10分',
    `CreateTime` BIGINT(20) NOT NULL COMMENT '创建时间戳',
    `UpdateTime` BIGINT(20) NOT NULL COMMENT '更新时间戳',
    KEY `RoleId` (`RoleId`),
    KEY `MusicClubId` (`MusicClubId`),
    KEY `RecordingId` (`RecordingId`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '唱片评分记录';
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS `qnm_music_club_recording_rating`;
-- +goose StatementEnd
