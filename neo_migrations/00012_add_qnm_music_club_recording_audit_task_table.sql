-- +goose Up
-- +goose StatementBegin
CREATE TABLE `qnm_music_club_recording_audit_task` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `RecordingId` bigint(20) NOT NULL COMMENT '唱片ID',
  `VocalUrl` varchar(255) NOT NULL COMMENT '人声URL',
  `RoleId` bigint(20) NOT NULL COMMENT '角色ID',
  `ServerId` bigint(20) NOT NULL COMMENT '服务器ID',
  `Status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态: 0-待审核, 1-已送审, 2-审核通过, 3-审核拒绝, 4-审核超时',
  `RetryCount` int(11) NOT NULL DEFAULT '0' COMMENT '重试次数',
  `TaskId` varchar(64) DEFAULT NULL COMMENT '审核任务ID',
  `RejectReason` varchar(255) DEFAULT NULL COMMENT '拒绝原因',
  `CreateTime` bigint(20) NOT NULL COMMENT '创建时间',
  `UpdateTime` bigint(20) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`),
  KEY `idx_recording_id` (`RecordingId`),
  KEY `idx_status` (`Status`),
  KEY `idx_create_time` (`CreateTime`),
  KEY `idx_vocal_url` (`VocalUrl`(191)),
  KEY `idx_role_server` (`RoleId`, `ServerId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='音乐俱乐部录音审核任务';
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS `qnm_music_club_recording_audit_task`;
-- +goose StatementEnd
