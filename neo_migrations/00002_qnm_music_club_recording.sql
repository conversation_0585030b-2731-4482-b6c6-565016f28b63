-- +goose Up
-- +goose StatementBegin
CREATE TABLE `qnm_music_club_recording` (
    `ID` BIGINT(20) NOT NULL PRIMARY KEY AUTO_INCREMENT COMMENT '唱片ID',
    `Name` VARCHAR(255) NOT NULL COMMENT '唱片名称',
    `TrackId` BIGINT(20) NOT NULL COMMENT '唱片录制游戏唯一ID',
    `DataUrl` VARCHAR(255) NOT NULL COMMENT '唱片数据URL',
    `ChorusStart` INT(10) NOT NULL COMMENT '副歌开始时间戳',
    `VocalOffset` INT(10) NOT NULL COMMENT '人声偏移时间戳, 正数表示人声提前，负数表示人声延后',
    `VocalVolume` INT(10) NOT NULL COMMENT '人声音量, 0-100',
    `InstrumentVolume` INT(10) NOT NULL COMMENT '乐器声音量, 0-100',
    `ServerId` BIGINT(20) NOT NULL COMMENT '服务器ID',
    `MusicClubId` BIGINT(20) NOT NULL COMMENT '乐团ID',
    `RequestPlayCount` BIGINT(20) NOT NULL DEFAULT 0 COMMENT '点播次数',
    `LastRequestPlayTime` BIGINT(20) NOT NULL DEFAULT 0 COMMENT '上次点播时间戳',
    `RatingCount` BIGINT(20) NOT NULL DEFAULT 0 COMMENT '评分次数',
    `RatingSum` BIGINT(20) NOT NULL DEFAULT 0 COMMENT '评分总和',
    `AvgRating` DECIMAL(10, 2) NOT NULL DEFAULT 0 COMMENT '平均评分',
    `ReleaseTime` BIGINT(20) NOT NULL COMMENT '上架时间戳',
    `UpdateTime` BIGINT(20) NOT NULL DEFAULT 0 COMMENT '更新时间戳',
    `Status` TINYINT(10) NOT NULL DEFAULT 0 COMMENT '状态, 0-正常, -1-已删除',
    KEY `idx_server_id` (`ServerId`),
    KEY `idx_music_club_id` (`MusicClubId`),
    KEY `idx_status_last_request_play_time_release_time` (`Status`, `LastRequestPlayTime`, `ReleaseTime`),
    KEY `idx_request_play_count_id` (`RequestPlayCount`, `ID`),
    KEY `idx_avg_rating_id` (`AvgRating`, `ID`),
    UNIQUE KEY `uniq_idx_track_id` (`TrackId`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '唱片信息';
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS `qnm_music_club_recording`;
-- +goose StatementEnd
