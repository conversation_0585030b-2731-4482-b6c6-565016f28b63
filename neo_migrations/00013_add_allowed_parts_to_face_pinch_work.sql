-- +goose Up
-- +goose StatementBegin
-- Add allowedParts field to pyq_face_pinch_work table
ALTER TABLE `pyq_face_pinch_work` 
ADD COLUMN `allowedParts` tinyint(4) NOT NULL DEFAULT 7 COMMENT '允许使用的部位：1=塑形，2=妆容，4=全脸，可组合使用位运算' AFTER `bodyShape`;
-- +goose StatementEnd

-- +goose StatementBegin
-- Add index for performance optimization on allowedParts field
ALTER TABLE `pyq_face_pinch_work` 
ADD INDEX `idx_allowed_parts` (`allowedParts`);
-- +goose StatementEnd

-- +goose StatementBegin
-- Update historical data: Set default allowedParts for existing works
-- Historical data before feature launch time will be set to 6 (妆容+全脸, excluding standalone 塑形)
-- This ensures backward compatibility and prevents standalone shape application for old data
UPDATE `pyq_face_pinch_work` 
SET `allowedParts` = 6 
WHERE `createTime` < UNIX_TIMESTAMP('2024-01-01 00:00:00') * 1000;
-- +goose StatementEnd

-- +goose StatementBegin
-- Set default allowedParts=7 (all parts) for newer works
UPDATE `pyq_face_pinch_work` 
SET `allowedParts` = 7 
WHERE `createTime` >= UNIX_TIMESTAMP('2024-01-01 00:00:00') * 1000;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
-- Remove the index first
ALTER TABLE `pyq_face_pinch_work` 
DROP INDEX `idx_allowed_parts`;

-- Remove the allowedParts column
ALTER TABLE `pyq_face_pinch_work` 
DROP COLUMN `allowedParts`;
-- +goose StatementEnd
