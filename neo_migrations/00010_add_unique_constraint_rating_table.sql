-- +goose Up
-- +goose StatementBegin
-- 首先删除可能存在的重复数据，保留最早的记录
DELETE r1 FROM qnm_music_club_recording_rating r1
INNER JOIN qnm_music_club_recording_rating r2
WHERE r1.RoleId = r2.RoleId 
  AND r1.RecordingId = r2.RecordingId 
  AND r1.ID > r2.ID;
-- +goose StatementEnd

-- +goose StatementBegin
-- 添加唯一约束
ALTER TABLE qnm_music_club_recording_rating 
ADD UNIQUE KEY uk_role_recording (RoleId, RecordingId);
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
-- 删除唯一约束
ALTER TABLE qnm_music_club_recording_rating 
DROP KEY uk_role_recording;
-- +goose StatementEnd
