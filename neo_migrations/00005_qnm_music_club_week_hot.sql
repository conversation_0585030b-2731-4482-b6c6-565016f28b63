-- +goose Up
-- +goose StatementBegin
CREATE TABLE `qnm_music_club_week_hot` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '乐团热度ID',
  `WeekDs` varchar(8) NOT NULL COMMENT '用周一作为周期的开始时间标记, yyyyMMdd',
  `MusicClubId` bigint(20) NOT NULL COMMENT '乐团ID',
  `Hot` bigint(10) NOT NULL DEFAULT '0' COMMENT '乐团周热度',
  `UpdateTime` bigint(20) NOT NULL COMMENT '更新时间戳',
  `Level` int(10) NOT NULL DEFAULT '1',
  `LastUpdateHotTime` bigint(20) NOT NULL DEFAULT '0',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `idx_uniq_club_id_week_ds` (`MusicClubId`,`WeekDs`) USING BTREE,
  KEY `idx_week_hot_ranking` (`WeekDs`,`Hot`,`Level`,`LastUpdateHotTime`)
) ENGINE=InnoDB AUTO_INCREMENT=65 DEFAULT CHARSET=utf8mb4 COMMENT='乐团周热度';
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS `qnm_music_club_week_hot`;
-- +goose StatementEnd
