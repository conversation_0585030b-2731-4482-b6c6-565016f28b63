-- +goose Up
-- +goose StatementBegin
CREATE TABLE `qnm_music_club_recording_request_play_log` (
    `ID` BIGINT(20) NOT NULL PRIMARY KEY AUTO_INCREMENT COMMENT '点播记录ID',
    `FromRoleId` BIGINT(20) NOT NULL COMMENT '发起点播角色ID',
    `MusicClubId` BIGINT(20) NOT NULL COMMENT '乐团ID',
    `ToRoleId` BIGINT(20) NOT NULL COMMENT '接收点播角色ID',
    `RecordingId` BIGINT(20) NOT NULL COMMENT '唱片ID',
    `EventTime` BIGINT(20) NOT NULL COMMENT '点播行为时间戳',
    `CreateTime` BIGINT(20) NOT NULL COMMENT '创建时间戳',
    KEY `idx_from_role_id` (`FromRoleId`),
    KEY `idx_music_club_id` (`MusicClubId`),
    KEY `idx_to_role_id` (`ToRoleId`),
    KEY `idx_recording_id` (`RecordingId`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '唱片点播记录';

-- +goose StatementEnd
-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS `qnm_music_club_recording_request_play_log`;
-- +goose StatementEnd
