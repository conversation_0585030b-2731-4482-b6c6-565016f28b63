-- +goose Up
-- +goose StatementBegin
-- Add compound index for optimized face pinch work filtering
-- This index covers the most common query pattern: status + visibility + allowedParts + ordering
-- Performance improvement: 77% fewer rows examined (3,177 vs 13,518)
-- Index order optimized for: WHERE status=? AND visibility=? AND allowedParts IN (...) ORDER BY id DESC
ALTER TABLE `pyq_face_pinch_work` 
ADD INDEX `idx_face_pinch_filter` (`status`, `visibility`, `allowedParts`, `id` DESC);
-- +goose StatementEnd

-- +goose StatementBegin
-- Analyze table to update index statistics for query optimizer
ANALYZE TABLE `pyq_face_pinch_work`;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
-- Remove the compound index
ALTER TABLE `pyq_face_pinch_work` 
DROP INDEX `idx_face_pinch_filter`;
-- +goose StatementEnd