-- +goose Up
-- +goose StatementBegin
CREATE TABLE `qnm_music_club` (
  `ID` bigint(20) NOT NULL COMMENT '乐团ID',
  `Name` varchar(255) NOT NULL COMMENT '乐团名称',
  `ServerId` bigint(20) NOT NULL COMMENT '服务器ID',
  `Level` int(10) NOT NULL DEFAULT '0' COMMENT '乐团等级',
  `Hot` bigint(10) NOT NULL DEFAULT '0' COMMENT '乐团热度',
  `ManagerRoleId` bigint(20) NOT NULL COMMENT '乐团经理角色ID',
  `LeadSingleRecordingId` bigint(20) NOT NULL DEFAULT '0' COMMENT '主打唱片ID',
  `CreateTime` bigint(20) NOT NULL COMMENT '创建时间戳',
  `UpdateTime` bigint(20) NOT NULL COMMENT '更新时间戳',
  `Status` tinyint(10) NOT NULL DEFAULT '0' COMMENT '状态, 0-正常, -1-已解散',
  `DeleteTime` bigint(20) NOT NULL DEFAULT '0' COMMENT '解散时间戳',
  `LastUpdateHotTime` bigint(20) NOT NULL DEFAULT '0' COMMENT '上次更新热度时间戳',
  PRIMARY KEY (`ID`),
  KEY `idx_server_id` (`ServerId`),
  KEY `idx_manager_role_id` (`ManagerRoleId`) USING BTREE,
  KEY `idx_music_club_ranking` (`Status`,`Hot`,`Level`,`LastUpdateHotTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='乐团信息';
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS `qnm_music_club`;
-- +goose StatementEnd
