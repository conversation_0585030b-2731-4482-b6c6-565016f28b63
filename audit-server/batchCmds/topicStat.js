"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTopicStat = void 0;
const ModelManager = require("../../models/ModelManager");
const bluebird = require("bluebird");
const helper_1 = require("../common/helper");
const PyqTopic = ModelManager.getModelByTableName('pyq_topic', 'SLAVE');
const PyqComment = ModelManager.getModelByTableName('pyq_comment', 'SLAVE');
const PyqTopicMoment = ModelManager.getModelByTableName('pyq_topic_moment', 'SLAVE');
const PyqMomentLike = ModelManager.getModelByTableName('pyq_moment_like', 'SLAVE');
function checkTopic(topic) {
    return __awaiter(this, void 0, void 0, function* () {
        let record = yield PyqTopic.findOne({ Subject: topic }, ['ID']);
        if (record) {
            return record.ID;
        }
        else {
            return bluebird.reject({ message: '话题不存在!' });
        }
    });
}
function getMomentCount(topicId, timeRange) {
    return __awaiter(this, void 0, void 0, function* () {
        let query = PyqTopicMoment.scope().where('TopicId', topicId);
        query = (0, helper_1.queryByTimeRange)(query, 'CreateTime', timeRange);
        let count = yield PyqTopicMoment.countByQuery(query);
        return count;
    });
}
function getLikesCount(topicId, timeRange) {
    return __awaiter(this, void 0, void 0, function* () {
        let query = PyqMomentLike.normalScope().from('pyq_moment_like as l')
            .innerJoin('pyq_topic_moment as t', 'l.MomentId', 't.MomentId')
            .where('t.TopicId', topicId);
        query = (0, helper_1.queryByTimeRange)(query, 'l.CreateTime', timeRange);
        let count = yield PyqTopicMoment.countByQuery(query);
        return count;
    });
}
function getForwardCount(topicId, timeRange) {
    return __awaiter(this, void 0, void 0, function* () {
        let query = PyqTopicMoment.scope().from('pyq_topic_moment as t')
            .innerJoin('pyq_moment as m', 'm.ID', 't.MomentId')
            .innerJoin('pyq_moment_forward as f', 'f.MomentId', 'm.Id')
            .whereNotNull('f.OriginId')
            .where('t.TopicId', topicId)
            .where('m.Status', 0);
        query = (0, helper_1.queryByTimeRange)(query, 'm.CreateTime', timeRange);
        let count = yield PyqTopicMoment.countByQuery(query);
        return count;
    });
}
function getCommentCount(topicId, timeRange) {
    return __awaiter(this, void 0, void 0, function* () {
        let query = PyqComment.scope().from('pyq_comment as c')
            .innerJoin('pyq_topic_moment as t', 'c.TargetId', 't.MomentId')
            .where('t.TopicId', topicId)
            .where('c.Status', 0);
        query = (0, helper_1.queryByTimeRange)(query, 'c.CreateTime', timeRange);
        let count = yield PyqTopicMoment.countByQuery(query);
        return count;
    });
}
function getTopicStat(params) {
    return __awaiter(this, void 0, void 0, function* () {
        let topicId = yield checkTopic(params.topic);
        let momentCount = yield getMomentCount(topicId, params);
        let likesCount = yield getLikesCount(topicId, params);
        let forwardCount = yield getForwardCount(topicId, params);
        let commentCount = yield getCommentCount(topicId, params);
        return {
            momentCount: momentCount,
            likesCount: likesCount,
            forwardCount: forwardCount,
            commentCount: commentCount
        };
    });
}
exports.getTopicStat = getTopicStat;
//# sourceMappingURL=topicStat.js.map