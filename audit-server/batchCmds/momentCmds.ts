import * as HotMomentsCache from "../../service/qnm/pyq/HotMomentsCache";
import * as _ from "lodash";
import * as bluebird from "bluebird";
import { csvStrToIntArray } from "../../common/util";
import { BatchCmdParams } from "../common/types";
import { CMD_ARRAY_DELIMITER, MOMENT_STATUS } from "../data/constants";
import * as PyqMoments from "../../models/PyqMoments";
import { getRootMergedServerId } from "../../service/qnm/server/list";
import { PyqMomentRecord } from "../../common/type";
import { checkParams } from "../helper";

export async function downHotMoments(params: { momentIds: string; serverId: string }) {
  const momentIds = csvStrToIntArray(params.momentIds);
  const serverId = params.serverId;
  if (!momentIds) {
    throw `参数错误: momentIds为空`;
  }
  return HotMomentsCache.downHotMoments(momentIds, serverId, null);
}

export async function delMoments(params: BatchCmdParams) {
  const logger = params.logger;
  const schema = {
    playerId: { type: Number },
    momentIds: { type: String },
    serverId: { type: Number },
  };
  await checkParams(params, schema);
  const momentIds = csvStrToIntArray(params.momentIds, CMD_ARRAY_DELIMITER);
  const roleId = params.playerId;
  const moments: PyqMomentRecord[] = await PyqMoments.find(
    { ID: momentIds, RoleId: roleId, Status: MOMENT_STATUS.NORMAL },
    { cols: ["ID"] }
  );
  if (moments.length > 0) {
    const delMomentIds = moments.map((m) => m.ID);
    const ret = await PyqMoments.updateByCondition({ Id: delMomentIds }, { Status: MOMENT_STATUS.CMD_DEL });
    logger.info({ roleid: params.playerId, ids: delMomentIds, ret: ret }, "DelMomentsFinish");
    const serverId = await getRootMergedServerId(params.serverId);

    const r1 = await HotMomentsCache.downHotMoments(delMomentIds, "all", null);
    logger.info({ ret: r1, ids: delMomentIds, server: "all" }, "DownAllServerHot");
    const r2 = await HotMomentsCache.downHotMoments(delMomentIds, "" + serverId, null);
    logger.info({ ret: r2, ids: delMomentIds, server: serverId }, "DownCurServerHot");
    return { delMoments: { affectedRows: ret.affectedRows }, downAllHot: r1, downCurHot: r2 };
  } else {
    return bluebird.reject({ message: "角色id下未找到相关id动态" });
  }
}
