/* eslint-disable no-throw-literal */
const db = require('../../common/db')
const config = require('../../common/config')
const dataUtil = require('../../common/data')
const tableCfg = config.TABLE_CFG
const _ = require('lodash')
const MdEventBus = require('../../md-server/eventBus')
const Events = MdEventBus.Events

exports.handle = function delMdMoment (params) {
  const filter = { UserId: params.playerId }
  const momentIds = params.momentIds
  if (!momentIds) {
    throw '参数错误8'
  }
  if (momentIds !== 'all') {
    filter.ID = momentIds.split(';')
  }
  return db.update({
    table: tableCfg.moment,
    values: { Status: dataUtil.Constants.STATUS_REJECT },
    filter: filter
  }).then(result => {
    const mIds = filter.ID
    if (!_.isEmpty(mIds)) {
      _.forEach(mIds, mId => {
        MdEventBus.emit(Events.DELETE_MOMENT, {momentId: mId})
      })
    }
    return result
  })
}
