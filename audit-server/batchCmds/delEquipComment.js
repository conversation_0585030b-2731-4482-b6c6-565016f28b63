"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.handle = void 0;
// 删除兵器谱评论指令
const bluebird = require("bluebird");
const QnmEquipComment_1 = require("../../models/QnmEquipComment");
function handle(params) {
    return __awaiter(this, void 0, void 0, function* () {
        let targetId = params.targetId;
        let equipGk = params.equipGk;
        if (!targetId || !equipGk) {
            return bluebird.reject({ errorType: '参数缺失' });
        }
        else {
            return (0, QnmEquipComment_1.deleteEquipComment)(equipGk, targetId);
        }
    });
}
exports.handle = handle;
//# sourceMappingURL=delEquipComment.js.map