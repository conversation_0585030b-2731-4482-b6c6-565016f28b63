"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getFollowStat = void 0;
const ModelManager = require("../../models/ModelManager");
const helper_1 = require("../common/helper");
const data_1 = require("../../common/data");
const PyqFollow = ModelManager.getModelByTableName('pyq_follow', 'SLAVE');
function getFansCount(params) {
    return __awaiter(this, void 0, void 0, function* () {
        let query = PyqFollow.scope().where('TargetId', params.playerId).where('Status', data_1.Constants.STATUS_NORMAL);
        query = (0, helper_1.queryByTimeRange)(query, 'UpdateTime', params);
        let count = yield PyqFollow.countByQuery(query);
        return count;
    });
}
function getFollowCount(params) {
    return __awaiter(this, void 0, void 0, function* () {
        let query = PyqFollow.scope().where('RoleId', params.playerId).where('Status', data_1.Constants.STATUS_NORMAL);
        query = (0, helper_1.queryByTimeRange)(query, 'UpdateTime', params);
        let count = yield PyqFollow.countByQuery(query);
        return count;
    });
}
function getCancelFollowCount(params) {
    return __awaiter(this, void 0, void 0, function* () {
        let query = PyqFollow.scope().where('RoleId', params.playerId).where('Status', data_1.Constants.STATUS_DELETE);
        query = (0, helper_1.queryByTimeRange)(query, 'UpdateTime', params);
        let count = yield PyqFollow.countByQuery(query);
        return count;
    });
}
function getFollowStat(params) {
    return __awaiter(this, void 0, void 0, function* () {
        let fansCount = yield getFansCount(params);
        let followCount = yield getFollowCount(params);
        let cancelFollowCount = yield getCancelFollowCount(params);
        return {
            fansCount: fansCount,
            followCount: followCount,
            cancelFollowCount: cancelFollowCount
        };
    });
}
exports.getFollowStat = getFollowStat;
//# sourceMappingURL=followStat.js.map