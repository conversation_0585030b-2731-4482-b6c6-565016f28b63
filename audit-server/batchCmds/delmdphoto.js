/* eslint-disable no-throw-literal */
const db = require('../../common/db')
const dataUtil = require('../../common/data')
const Q = require('q')
const MdEventBus = require('../../md-server/eventBus')
const Events = MdEventBus.Events

exports.handle = function delMdPhoto (params) {
  const roleId = params.playerId
  const photoIds = params.photoIds
  if (!photoIds) {
    throw '参数错误12'
  }
  return Q.fcall(function () {
    if (photoIds !== 'all') {
      const pIds = photoIds.split(';')
      return db.update({
        table: 'md_photo',
        values: { AuditStatus: dataUtil.Constants.STATUS_AUDIT_REJECT },
        filter: {ID: pIds}
      }).then(result => {
        pIds.forEach(pId => {
          MdEventBus.emit(Events.DELETE_PHOTO, {photoId: pId})
        })
        return result
      })
    } else {
      return db.query({
        table: 'md_photo_album',
        cols: ['ID'],
        filter: { UserId: roleId }
      }).then(function (results) {
        let albumIdList = []
        for (let i = 0, l = results.length; i < l; i++) {
          albumIdList.push(results[i].ID)
        }
        if (!albumIdList.length) {
          throw '未找到' + roleId + '的相册记录'
        }
        return db.update({
          table: 'md_photo',
          values: { AuditStatus: dataUtil.Constants.STATUS_AUDIT_REJECT },
          filter: { PhotoAlbumID: albumIdList }
        })
      })
    }
  })
}
