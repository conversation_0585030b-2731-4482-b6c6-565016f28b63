/**
 * Created by zhen<PERSON> on 2017/6/14.
 */

const util = require('../../common/util');
const PyqPhoto = require('../../pyq-server/models/photo');
const Constants = require('../../common/data').Constants;
const { getRedis } = require('../../common/redis');
const _ = require('lodash');
const Promise = require('bluebird');

const deleteChatPhotoMaxSize = 500;

/**
 * 删除图片 delchatimg server ID 图片ID(为ALL时，删除该玩家所有图片）
 */
exports.handle = function delchatimg(params) {
  const photoId = params.photoId;
  const playerId = params.playerId;
  if(!(util.isNumeric(photoId) || photoId === "all")) {
    return Promise.reject({message: 'photoId参数非法!'})
  }
  if(photoId === "all") {
    return deletePlayerChatPhoto(playerId)
  } else {
    return deleteChatPhotoById(photoId, playerId)
  }
};

function deletePlayerChatPhoto(roleId) {
  const query = PyqPhoto.scope()
  .where('RoleId', roleId)
  .where('Type', PyqPhoto.Types.pyqchat)
  .where('Status', Constants.STATUS_NORMAL)
  .orderBy('ID', 'desc')
  .select('ID')
  .limit(deleteChatPhotoMaxSize);
  return PyqPhoto.executeByQuery(query)
  .then(rows => {
    if(_.isEmpty(rows)) {
      return Promise.reject("该用户下没有找到需要删除的聊天图片!");
    } else {
      const ids = _.map(rows, 'ID');
      clearChatPhotoCacheBatch(ids);
      return PyqPhoto.softDeleteByCondition({Id:ids});
    }
  })
  .then(formatOkPacket);
}

function deleteChatPhotoById(photoId, roleId) {
  return PyqPhoto.softDeleteByCondition({
    RoleId: roleId,
    ID: photoId,
    Type: PyqPhoto.Types.pyqchat,
    Status:Constants.STATUS_NORMAL
  })
  .then(okPacket => {
    clearChatPhotoCache(photoId);
    return formatOkPacket(okPacket);
  });
}

// 清楚单张聊天图片的缓存
function clearChatPhotoCache(photoId) {
  return getRedis().delAsync(PyqPhoto.getChatPhotoCacheKey(photoId));
}

function clearChatPhotoCacheBatch(photoIds) {
  return Promise.map(photoIds, id => clearChatPhotoCache(id), {concurrency: 5});
}

function formatOkPacket(okPacket) {
  return {affectedRows: okPacket.affectedRows};
}
