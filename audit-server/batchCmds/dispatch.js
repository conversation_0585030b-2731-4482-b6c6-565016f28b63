"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.dispatch = exports.installCmds = void 0;
const handleMap = new Map();
const delchatimg_1 = require("./delchatimg");
const delEquipComment_1 = require("./delEquipComment");
const delmdcomment_1 = require("./delmdcomment");
const delmdmessage_1 = require("./delmdmessage");
const delmdphoto_1 = require("./delmdphoto");
const getcomment_1 = require("./getcomment");
const getmoment_1 = require("./getmoment");
const topicStat_1 = require("./topicStat");
const momentStat_1 = require("./momentStat");
const followStat_1 = require("./followStat");
const momentCmds_1 = require("./momentCmds");
const logger_1 = require("../logger");
function installCmds() {
    handleMap.set('delchatimg', delchatimg_1.handle);
    handleMap.set('delEquipComment', delEquipComment_1.handle);
    handleMap.set('delmdcomment', delmdcomment_1.handle);
    handleMap.set('delmdmessage', delmdmessage_1.handle);
    handleMap.set('delmdphoto', delmdphoto_1.handle);
    handleMap.set('getcomment', getcomment_1.handle);
    handleMap.set('getmoment', getmoment_1.handle);
    handleMap.set('getTopicStat', topicStat_1.getTopicStat);
    handleMap.set('getMomentStat', momentStat_1.getMomentStat);
    handleMap.set('getMomentStatNew', momentStat_1.getMomentStatNew);
    handleMap.set('getForwardListByMomentId', momentStat_1.getForwardListByMomentId);
    handleMap.set('getCommentListByMmomentId', momentStat_1.getCommentListByMmomentId);
    handleMap.set('getLikeListByMomentId', momentStat_1.getLikeListByMomentId);
    handleMap.set('getFollowStat', followStat_1.getFollowStat);
    handleMap.set('addmomenthot', momentStat_1.updateHotState);
    handleMap.set('delmomenthot', momentStat_1.delHotState);
    handleMap.set('recovermoment', momentStat_1.recoverMoment);
    handleMap.set('downhotmoment', momentCmds_1.downHotMoments);
    handleMap.set('delmoment', momentCmds_1.delMoments);
}
exports.installCmds = installCmds;
installCmds();
function getHandlerByName(name) {
    return handleMap.get(name);
}
function dispatch(cmd, params) {
    let handler = getHandlerByName(cmd);
    if (handler) {
        params.logger = logger_1.cmdLogger.child({ cmd: cmd });
        return handler(params);
    }
    else {
        return 'cmd指令不存在';
    }
}
exports.dispatch = dispatch;
//# sourceMappingURL=dispatch.js.map