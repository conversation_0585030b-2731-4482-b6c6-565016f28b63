const ModelManager = require('../../models/ModelManager')

exports.handle = function getmoment (params) {
  const PyqMoments = ModelManager.getModelByTableName('pyq_moment')
  const pageSize = params.pageSize || 100
  const page = params.page || 0
  const roleId = params.playerId
  const offset = page * pageSize
  const query = PyqMoments.scope()
    .select('*')
    .where('RoleId', roleId)
    .where('Status', 0)
    .orderBy('ID', 'desc')
    .offset(offset)
    .limit(pageSize)
  return PyqMoments.executeByQuery(query)
}
