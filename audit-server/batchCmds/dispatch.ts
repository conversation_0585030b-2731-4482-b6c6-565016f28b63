
type BatchCmdHandler = (params: any & BatchCmdParams) => Promise<any>

const handleMap: Map<string, BatchCmdHandler> = new Map()

import { handle as delchatimg } from './delchatimg'
import { handle as delEquipComment } from './delEquipComment'
import { handle as delmdcomment } from './delmdcomment'
import { handle as delmdmessage } from './delmdmessage'
import { handle as delmdphoto } from './delmdphoto'
import { handle as getcomment } from './getcomment'
import { handle as getmoment } from './getmoment'
import { getTopicStat } from './topicStat'
import { getMomentStat, getMomentStatNew, getForwardListByMomentId, getCommentListByMmomentId, getLikeListByMomentId, updateHotState, delHotState, recoverMoment } from './momentStat';
import { getFollowStat } from './followStat';
import { downHotMoments, delMoments } from './momentCmds';
import { cmdLogger } from '../logger';
import { BatchCmdParams } from '../common/types';

export function installCmds() {
    handleMap.set('delchatimg', delchatimg)
    handleMap.set('delEquipComment', delEquipComment)
    handleMap.set('delmdcomment', delmdcomment)
    handleMap.set('delmdmessage', delmdmessage)
    handleMap.set('delmdphoto', delmdphoto)
    handleMap.set('getcomment', getcomment)
    handleMap.set('getmoment', getmoment)
    handleMap.set('getTopicStat', getTopicStat)
    handleMap.set('getMomentStat', getMomentStat)
    handleMap.set('getMomentStatNew', getMomentStatNew)
    handleMap.set('getForwardListByMomentId', getForwardListByMomentId)
    handleMap.set('getCommentListByMmomentId', getCommentListByMmomentId)
    handleMap.set('getLikeListByMomentId', getLikeListByMomentId)
    handleMap.set('getFollowStat', getFollowStat)
    handleMap.set('addmomenthot', updateHotState)
    handleMap.set('delmomenthot', delHotState)
    handleMap.set('recovermoment', recoverMoment)
    handleMap.set('downhotmoment', downHotMoments)
    handleMap.set('delmoment', delMoments)
}

installCmds()

function getHandlerByName(name: string): BatchCmdHandler {
    return handleMap.get(name)
}


export function dispatch(cmd: string, params: BatchCmdParams) {
    let handler = getHandlerByName(cmd)
    if (handler) {
        params.logger = cmdLogger.child({ cmd: cmd })
        return handler(params)
    } else {
        return 'cmd指令不存在'
    }
}