/* eslint-disable no-throw-literal */
const db = require('../../common/db')
const config = require('../../common/config')
const dataUtil = require('../../common/data')
const tableCfg = config.TABLE_CFG

exports.handle = function delMdComment (params) {
  const targetId = params.targetId
  const commentIds = params.commentIds
  const roleId = params.playerId
  if (!targetId || !commentIds) {
    throw '参数错误9'
  }
  return db.query({
    table: tableCfg.moment,
    cols: ['ID'],
    filter: { UserId: targetId },
    pageSize: 1000
  }).then(function (results) {
    let momentIdList = []
    for (let i = 0, l = results.length; i < l; i++) {
      momentIdList.push(results[i].ID)
    }
    if (!momentIdList.length) {
      throw '未找到' + targetId + '的新鲜事记录'
    }

    let filter = { UserId: roleId, TargetId: momentIdList }
    if (commentIds !== 'all') {
      filter['ID'] = commentIds.split(';')
    }
    db.update({
      table: tableCfg.comment,
      values: { Status: dataUtil.Constants.STATUS_REJECT },
      filter: filter
    })
  })
}
