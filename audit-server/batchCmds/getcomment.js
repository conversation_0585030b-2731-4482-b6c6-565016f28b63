const ModelManager = require('../../models/ModelManager')
const util = require('../../common/util')
const PyqComments = ModelManager.getModelByTableName('pyq_comment', 'SLAVE')
const PyqMoments = ModelManager.getModelByTableName('pyq_moment', 'SLAVE')
const _ = require('lodash')

exports.handle = function getComments (params) {
  const PyqComments = ModelManager.getModelByTableName('pyq_comment', 'SLAVE')
  const roleId = params.playerId
  const pageSize = params.pageSize || 100
  const offset = (params.page || 0) * pageSize
  const query = PyqComments.normalScope()
  .select(['ID', 'RoleId', 'TargetId', 'ReplyId', 'Text', 'CreateTime'])
  .orderBy('ID', 'desc')
  .where('RoleId', roleId)
  .offset(offset)
  .limit(pageSize)
  return PyqComments.executeByQuery(query)
  .then((comments) => {
    const mIds = _.map(comments, 'TargetId')
    return Promise.all([
      comments,
      PyqMoments.find({ID: mIds}, {cols: ['ID', 'RoleId', 'Text', 'ImgList', 'CreateTime']})
    ])
  }).spread((comments, moments) => {
    const mIdToRecord = util.keyToRecordHash(moments, 'ID')
    _.forEach(comments, c => {
      c.Moment = mIdToRecord[c.TargetId]
    })
    return comments
  })
}
