import * as ModelManager from '../../models/ModelManager'
import { TimeStampRange } from "../common/types";
import { queryByTimeRange } from '../common/helper';
import { Constants } from '../../common/data'


const PyqFollow = ModelManager.getModelByTableName('pyq_follow', 'SLAVE')

interface IFollowStatParams extends TimeStampRange {
    playerId: number
}

async function getFansCount(params: IFollowStatParams): Promise<number> {
    let query = PyqFollow.scope().where('TargetId', params.playerId).where('Status', Constants.STATUS_NORMAL)
    query = queryByTimeRange(query, 'UpdateTime', params)
    let count = await PyqFollow.countByQuery(query)
    return count
}

async function getFollowCount(params: IFollowStatParams): Promise<number> {
    let query = PyqFollow.scope().where('RoleId', params.playerId).where('Status', Constants.STATUS_NORMAL)
    query = queryByTimeRange(query, 'UpdateTime', params)
    let count = await PyqFollow.countByQuery(query)
    return count
}

async function getCancelFollowCount(params: IFollowStatParams): Promise<number> {
    let query = PyqFollow.scope().where('RoleId', params.playerId).where('Status', Constants.STATUS_DELETE)
    query = queryByTimeRange(query, 'UpdateTime', params)
    let count = await PyqFollow.countByQuery(query)
    return count
}

export async function getFollowStat(params: IFollowStatParams) {
    let fansCount = await getFansCount(params)
    let followCount = await getFollowCount(params)
    let cancelFollowCount = await getCancelFollowCount(params)
    return {
        fansCount: fansCount,
        followCount: followCount,
        cancelFollowCount: cancelFollowCount
    }
}