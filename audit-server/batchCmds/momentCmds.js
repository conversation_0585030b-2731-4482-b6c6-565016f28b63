"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.delMoments = exports.downHotMoments = void 0;
const HotMomentsCache = require("../../service/qnm/pyq/HotMomentsCache");
const bluebird = require("bluebird");
const util_1 = require("../../common/util");
const constants_1 = require("../data/constants");
const PyqMoments = require("../../models/PyqMoments");
const list_1 = require("../../service/qnm/server/list");
const helper_1 = require("../helper");
function downHotMoments(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const momentIds = (0, util_1.csvStrToIntArray)(params.momentIds);
        const serverId = params.serverId;
        if (!momentIds) {
            throw `参数错误: momentIds为空`;
        }
        return HotMomentsCache.downHotMoments(momentIds, serverId, null);
    });
}
exports.downHotMoments = downHotMoments;
function delMoments(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const logger = params.logger;
        const schema = {
            playerId: { type: Number },
            momentIds: { type: String },
            serverId: { type: Number },
        };
        yield (0, helper_1.checkParams)(params, schema);
        const momentIds = (0, util_1.csvStrToIntArray)(params.momentIds, constants_1.CMD_ARRAY_DELIMITER);
        const roleId = params.playerId;
        const moments = yield PyqMoments.find({ ID: momentIds, RoleId: roleId, Status: constants_1.MOMENT_STATUS.NORMAL }, { cols: ["ID"] });
        if (moments.length > 0) {
            const delMomentIds = moments.map((m) => m.ID);
            const ret = yield PyqMoments.updateByCondition({ Id: delMomentIds }, { Status: constants_1.MOMENT_STATUS.CMD_DEL });
            logger.info({ roleid: params.playerId, ids: delMomentIds, ret: ret }, "DelMomentsFinish");
            const serverId = yield (0, list_1.getRootMergedServerId)(params.serverId);
            const r1 = yield HotMomentsCache.downHotMoments(delMomentIds, "all", null);
            logger.info({ ret: r1, ids: delMomentIds, server: "all" }, "DownAllServerHot");
            const r2 = yield HotMomentsCache.downHotMoments(delMomentIds, "" + serverId, null);
            logger.info({ ret: r2, ids: delMomentIds, server: serverId }, "DownCurServerHot");
            return { delMoments: { affectedRows: ret.affectedRows }, downAllHot: r1, downCurHot: r2 };
        }
        else {
            return bluebird.reject({ message: "角色id下未找到相关id动态" });
        }
    });
}
exports.delMoments = delMoments;
//# sourceMappingURL=momentCmds.js.map