import { TimeStampRange, BatchCmdParams } from "../common/types";
import * as ModelManager from "../../models/ModelManager";
import * as _ from "lodash";
import { queryByTimeRange } from "../common/helper";
import { Statues } from "../../common/constants";
import { getJsonInfo, keysToCamelCase } from "../../common/util";
import { isMomentIdsInHotMoments } from "../../service/qnm/pyq/HotMomentsCache";
import { PyqMomentRecord, PyqCommentRecord, PyqMomentLikeRecord } from "../../common/type";

const PyqMomentLike = ModelManager.getModelByTableName<PyqMomentLikeRecord>("pyq_moment_like", "SLAVE");
const PyqComment = ModelManager.getModelByTableName<PyqCommentRecord>("pyq_comment", "SLAVE");
const PyqMoment = ModelManager.getModelByTableName<PyqMomentRecord>("pyq_moment", "SLAVE");

async function getLikeCount(params: IMomentStatParams) {
  let query = PyqMomentLike.normalScope().where("MomentId", params.momentId);
  query = queryByTimeRange(query, "CreateTime", params);
  const count = await PyqMomentLike.countByQuery(query);
  return count;
}

async function getLikeRoleIds(params: IMomentStatParams) {
  let query = PyqMomentLike.normalScope().distinct("RoleId").where("MomentId", params.momentId);
  query = queryByTimeRange(query, "CreateTime", params);
  const rows = (await PyqMomentLike.executeByQuery(query)) as { RoleId: number }[];
  const result = rows.map((x) => x.RoleId);
  return result;
}

async function getCommentCount(params: IMomentStatParams) {
  let query = PyqComment.normalScope().where("TargetId", params.momentId);
  query = queryByTimeRange(query, "CreateTime", params);
  const count = await PyqComment.countByQuery(query);
  return count;
}

async function getCommentRoleIds(params: IMomentStatParams) {
  let query = PyqComment.normalScope().distinct("RoleId").where("TargetId", params.momentId);
  query = queryByTimeRange(query, "CreateTime", params);
  const rows = (await PyqMomentLike.executeByQuery(query)) as { RoleId: number }[];
  const result = rows.map((x) => x.RoleId);
  return result;
}

async function getForwardCount(params: IMomentStatParams) {
  let query = PyqMoment.scope()
    .from("pyq_moment as m")
    .innerJoin("pyq_moment_forward as f", "m.ID", "f.MomentId")
    .where("f.OriginId", params.momentId);
  query = queryByTimeRange(query, "m.CreateTime", params);
  const count = await PyqMoment.countByQuery(query);
  return count;
}

async function getNormalForwardCount(params: IMomentStatParams) {
  let query = PyqMoment.normalScope()
    .from("pyq_moment as m")
    .innerJoin("pyq_moment_forward as f", "m.ID", "f.MomentId")
    .where("f.OriginId", params.momentId);
  query = queryByTimeRange(query, "m.CreateTime", params);
  const count = await PyqMoment.countByQuery(query);
  return count;
}

async function getForwardRoleIds(params: IMomentStatParams) {
  let query = PyqMoment.scope()
    .from("pyq_moment as m")
    .distinct("m.RoleId")
    .innerJoin("pyq_moment_forward as f", "m.ID", "f.MomentId")
    .where("f.OriginId", params.momentId);
  query = queryByTimeRange(query, "m.CreateTime", params);
  const rows = (await PyqMomentLike.executeByQuery(query)) as { RoleId: number }[];
  const result = rows.map((x) => x.RoleId);
  return result;
}

interface IMomentStatParams extends TimeStampRange {
  momentId: string;
  serverId?: number;
}

export async function getMomentStat(params: IMomentStatParams) {
  const like = await getLikeCount(params);
  const comment = await getCommentCount(params);
  const forward = await getForwardCount(params);

  const likeRoleIds = await getLikeRoleIds(params);
  const commentRoleIds = await getCommentRoleIds(params);
  const forwardRoleIds = await getForwardRoleIds(params);

  return {
    like: like,
    comment: comment,
    forward: forward,
    likeRoleIds: likeRoleIds,
    commentRoleIds: commentRoleIds,
    forwardRoleIds: forwardRoleIds,
  };
}

async function getOnemoment(params: IMomentStatParams, cols?: string[]) {
  cols = cols || [
    "ID",
    "RoleId",
    "Text",
    "ImgList",
    "ImgAudit",
    "VideoList",
    "VideoAudit",
    "VideoCoverList",
    "VideoCoverAudit",
    "Hot",
    "CreateTime",
  ];
  const momentId = params.momentId;
  if (!momentId) {
    throw "参数错误";
  }
  let moment = await PyqMoment.executeByQuery(PyqMoment.normalScope().where({ ID: params.momentId }).select(cols));
  if (!moment || moment.length == 0) {
    throw "该动态不存在";
  }
  moment = moment[0];
  return moment;
}

export async function getMomentStatNew(params: IMomentStatParams) {
  const momentId = parseInt(params.momentId, 10);
  const moment = await getOnemoment(params);
  const serverId = params.serverId;
  const like = await getLikeCount(params);
  const comment = await getCommentCount(params);
  const forward = await getNormalForwardCount(params);
  const isLocalHot = await isMomentIdsInHotMoments(serverId, [momentId]);
  const isAllHot = await isMomentIdsInHotMoments("all", [momentId]);

  const res = {
    createTime: moment.CreateTime,
    hot: moment.Hot,
    isAllHot,
    isLocalHot,
    like,
    comment,
    forward,
  };
  return res;
}

export async function getForwardListByMomentId(params: IMomentStatParams) {
  const moment = await getOnemoment(params);
  let query = PyqMoment.normalScope()
    .from("pyq_moment as m")
    .innerJoin("pyq_moment_forward as f", "m.ID", "f.MomentId")
    .where("f.OriginId", params.momentId)
    .select("m.*");
  query = queryByTimeRange(query, "m.CreateTime", params);
  const rows = await PyqMoment.executeByQuery(query);
  const forwards = _.map(rows, (row) => _.pick(row, ["ID", "RoleId", "CreateTime", "Text", "Hot"]));
  const res = {
    moment,
    forwards: forwards,
  };
  return keysToCamelCase(res);
}

export async function getCommentListByMmomentId(params: IMomentStatParams) {
  const moment = await getOnemoment(params);
  let query = PyqComment.normalScope().where("TargetId", params.momentId);
  query = queryByTimeRange(query, "CreateTime", params);
  const rows = await PyqMomentLike.executeByQuery(query);
  const comments = _.map(rows, (row) => _.pick(row, ["ID", "RoleId", "CreateTime", "Text"]));
  const res = {
    moment,
    comments: comments,
  };
  return keysToCamelCase(res);
}

export async function getLikeListByMomentId(params: IMomentStatParams) {
  const moment = await getOnemoment(params);
  let query = PyqMomentLike.normalScope().where("MomentId", params.momentId);
  query = queryByTimeRange(query, "CreateTime", params);
  const rows = (await PyqMomentLike.executeByQuery(query)) as { RoleId: number }[];
  const likes = _.map(rows, (row) => {
    return _.pick(row, ["ID", "RoleId", "CreateTime"]);
  });
  const res = {
    moment,
    likes: likes,
  };
  return keysToCamelCase(res);
}

interface IUpdateStateParams {
  serverId: number;
  playerId: number;
  momentId: number;
  like?: string;
  forward?: string;
}

function toNumber(num: string): number {
  const result = _.toNumber(num);
  if (_.isNaN(num)) throw "点赞数或转发数非法";
  else return result;
}

export async function updateHotState(params: IUpdateStateParams) {
  const PyqMomentMaster = ModelManager.getModelByTableName<PyqMomentRecord>("pyq_moment", "MASTER");
  const like: number = toNumber(params.like);
  const forward: number = toNumber(params.forward);

  const momentId = params.momentId;
  const moment = await PyqMoment.findById(momentId, ["RoleId", "HotState"]);
  if (moment.RoleId !== _.toNumber(params.playerId)) {
    throw "该心情不属于该用户";
  }

  const hotState = getJsonInfo(moment.HotState) as { like: number; forward: number };

  if (hotState.like) {
    hotState.like = like + _.toNumber(hotState.like);
  } else {
    hotState.like = like;
  }

  if (hotState.forward) {
    hotState.forward = forward + _.toNumber(hotState.forward);
  } else {
    hotState.forward = forward;
  }

  await PyqMomentMaster.updateByCondition({ ID: momentId }, { HotState: JSON.stringify(hotState) });

  return { id: momentId };
}

export async function delHotState(params: IUpdateStateParams) {
  const PyqMomentMaster = ModelManager.getModelByTableName("pyq_moment", "MASTER");
  const momentId = params.momentId;
  const moment = await PyqMoment.findById(momentId);
  if (moment.RoleId !== _.toNumber(params.playerId)) throw "该状态不属于该用户";

  const hotState = JSON.parse(moment.HotState);
  const [like, forward] = await Promise.all([
    getLikeCount({ momentId: _.toString(momentId), from: 0, to: Date.now() }),
    getForwardCount({ momentId: _.toString(momentId), from: 0, to: Date.now() }),
  ]);
  hotState.like = like;
  hotState.forward = forward;

  await PyqMomentMaster.updateByCondition({ ID: momentId }, { HotState: JSON.stringify(hotState) });

  return { id: momentId };
}

interface IRecoverMoment {
  playerId: number;
  momentIds: string;
}

export async function recoverMoment(params: IRecoverMoment) {
  const ids = params.momentIds ? params.momentIds.split(";") : [];
  const PyqMomentMaster = ModelManager.getModelByTableName("pyq_moment", "MASTER");
  const momentList = await PyqMoment.find({ ID: ids }, { cols: ["ID", "RoleId"] });

  if (_.isEmpty(momentList)) throw "状态不存在";

  momentList.forEach((item) => {
    if (item.RoleId != params.playerId) throw `状态:${item.ID} 不属于该用户`;
  });

  await PyqMomentMaster.updateByCondition({ ID: ids }, { Status: Statues.Normal });

  return null;
}
