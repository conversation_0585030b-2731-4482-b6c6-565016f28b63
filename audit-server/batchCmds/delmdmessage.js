
/* eslint-disable no-throw-literal */
const db = require('../../common/db')
const config = require('../../common/config')
const dataUtil = require('../../common/data')
const tableCfg = config.TABLE_CFG
const _ = require('lodash')
const MdEventBus = require('../../md-server/eventBus')
const Events = MdEventBus.Events

exports.handle = function delMdMessage (params) {
  const roleId = params.playerId
  const filter = { UserId: roleId }
  const messageIds = params.messageIds
  if (!messageIds) {
    throw '参数错误10'
  }
  if (messageIds !== 'all') {
    filter.ID = messageIds.split(';')
  }
  return db.update({
    table: tableCfg.message,
    values: { Status: dataUtil.Constants.STATUS_REJECT },
    filter: filter
  }).then(result => {
    const mIds = filter.ID
    if (!_.isEmpty(mIds)) {
      _.forEach(mIds, mId => {
        MdEventBus.emit(Events.DELETE_MESSAGE, {messageId: mId})
      })
    }
    return result
  })
}
