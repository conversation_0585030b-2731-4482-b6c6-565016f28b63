"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const ModelManager = require("../../models/ModelManager");
const PyqTopic = ModelManager.getModelByTableName('pyq_topic', 'SLAVE');
const PyqTopicMoment = ModelManager.getModelByTableName('pyq_topic_moment', 'SLAVE');
function getTopicId(topic) {
    return __awaiter(this, void 0, void 0, function* () {
        let record = yield PyqTopic.findOne({ Subject: topic }, ['ID']);
        if (record) {
            return record.ID;
        }
        else {
            return null;
        }
    });
}
function getMomentsCount(topicId) {
    return __awaiter(this, void 0, void 0, function* () {
        let count = yield PyqTopicMoment.count({ TopicId: topicId });
        return count;
    });
}
function getTopicStat(params) {
    return __awaiter(this, void 0, void 0, function* () {
        let topicId = yield getTopicId(params.topic);
        if (topicId) {
            let momentCount = yield getMomentsCount(topicId);
            return { momentCount: momentCount };
        }
        else {
            return { momentCount: 0 };
        }
    });
}
exports.getTopicStat = getTopicStat;
//# sourceMappingURL=pyqTopic.js.map