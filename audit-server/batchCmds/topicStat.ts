import * as ModelManager from '../../models/ModelManager'
import { TimeStampRange } from '../common/types';
import * as bluebird from 'bluebird'
import { queryByTimeRange } from '../common/helper';
import {PyqTopicRecord, PyqCommentRecord, PyqTopicMomentRecord, PyqMomentLikeRecord} from '../../common/type';

const PyqTopic = ModelManager.getModelByTableName<PyqTopicRecord>('pyq_topic', 'SLAVE')
const PyqComment = ModelManager.getModelByTableName<PyqCommentRecord>('pyq_comment', 'SLAVE')
const PyqTopicMoment = ModelManager.getModelByTableName<PyqTopicMomentRecord>('pyq_topic_moment', 'SLAVE')
const PyqMomentLike = ModelManager.getModelByTableName<PyqMomentLikeRecord>('pyq_moment_like', 'SLAVE')

interface ITopicStatParams extends TimeStampRange {
    topic: string
}

async function checkTopic(topic: string): Promise<number> {
    let record = await PyqTopic.findOne({ Subject: topic }, ['ID']) as { ID: number }
    if (record) {
        return record.ID
    } else {
        return bluebird.reject({ message: '话题不存在!' })
    }
}

async function getMomentCount(topicId: number, timeRange: TimeStampRange) {
    let query = PyqTopicMoment.scope().where('TopicId', topicId)
    query = queryByTimeRange(query, 'CreateTime', timeRange)
    let count = await PyqTopicMoment.countByQuery(query)
    return count
}

async function getLikesCount(topicId: number, timeRange: TimeStampRange) {
    let query = PyqMomentLike.normalScope().from('pyq_moment_like as l')
        .innerJoin('pyq_topic_moment as t', 'l.MomentId', 't.MomentId')
        .where('t.TopicId', topicId)
    query = queryByTimeRange(query, 'l.CreateTime', timeRange)
    let count = await PyqTopicMoment.countByQuery(query)
    return count
}

async function getForwardCount(topicId: number, timeRange: TimeStampRange) {
    let query = PyqTopicMoment.scope().from('pyq_topic_moment as t')
        .innerJoin('pyq_moment as m', 'm.ID', 't.MomentId')
        .innerJoin('pyq_moment_forward as f', 'f.MomentId', 'm.Id')
        .whereNotNull('f.OriginId')
        .where('t.TopicId', topicId)
        .where('m.Status', 0)
    query = queryByTimeRange(query, 'm.CreateTime', timeRange)
    let count = await PyqTopicMoment.countByQuery(query)
    return count
}

async function getCommentCount(topicId: number, timeRange: TimeStampRange) {
    let query = PyqComment.scope().from('pyq_comment as c')
        .innerJoin('pyq_topic_moment as t', 'c.TargetId', 't.MomentId')
        .where('t.TopicId', topicId)
        .where('c.Status', 0)
    query = queryByTimeRange(query, 'c.CreateTime', timeRange)
    let count = await PyqTopicMoment.countByQuery(query)
    return count
}



export async function getTopicStat(params: ITopicStatParams) {
    let topicId = await checkTopic(params.topic)
    let momentCount = await getMomentCount(topicId, params)
    let likesCount = await getLikesCount(topicId, params)
    let forwardCount = await getForwardCount(topicId, params)
    let commentCount = await getCommentCount(topicId, params)

    return {
        momentCount: momentCount,
        likesCount: likesCount,
        forwardCount: forwardCount,
        commentCount: commentCount
    }
}
