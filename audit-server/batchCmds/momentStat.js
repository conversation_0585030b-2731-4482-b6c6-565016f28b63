"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.recoverMoment = exports.delHotState = exports.updateHotState = exports.getLikeListByMomentId = exports.getCommentListByMmomentId = exports.getForwardListByMomentId = exports.getMomentStatNew = exports.getMomentStat = void 0;
const ModelManager = require("../../models/ModelManager");
const _ = require("lodash");
const helper_1 = require("../common/helper");
const constants_1 = require("../../common/constants");
const util_1 = require("../../common/util");
const HotMomentsCache_1 = require("../../service/qnm/pyq/HotMomentsCache");
const PyqMomentLike = ModelManager.getModelByTableName("pyq_moment_like", "SLAVE");
const PyqComment = ModelManager.getModelByTableName("pyq_comment", "SLAVE");
const PyqMoment = ModelManager.getModelByTableName("pyq_moment", "SLAVE");
function getLikeCount(params) {
    return __awaiter(this, void 0, void 0, function* () {
        let query = PyqMomentLike.normalScope().where("MomentId", params.momentId);
        query = (0, helper_1.queryByTimeRange)(query, "CreateTime", params);
        const count = yield PyqMomentLike.countByQuery(query);
        return count;
    });
}
function getLikeRoleIds(params) {
    return __awaiter(this, void 0, void 0, function* () {
        let query = PyqMomentLike.normalScope().distinct("RoleId").where("MomentId", params.momentId);
        query = (0, helper_1.queryByTimeRange)(query, "CreateTime", params);
        const rows = (yield PyqMomentLike.executeByQuery(query));
        const result = rows.map((x) => x.RoleId);
        return result;
    });
}
function getCommentCount(params) {
    return __awaiter(this, void 0, void 0, function* () {
        let query = PyqComment.normalScope().where("TargetId", params.momentId);
        query = (0, helper_1.queryByTimeRange)(query, "CreateTime", params);
        const count = yield PyqComment.countByQuery(query);
        return count;
    });
}
function getCommentRoleIds(params) {
    return __awaiter(this, void 0, void 0, function* () {
        let query = PyqComment.normalScope().distinct("RoleId").where("TargetId", params.momentId);
        query = (0, helper_1.queryByTimeRange)(query, "CreateTime", params);
        const rows = (yield PyqMomentLike.executeByQuery(query));
        const result = rows.map((x) => x.RoleId);
        return result;
    });
}
function getForwardCount(params) {
    return __awaiter(this, void 0, void 0, function* () {
        let query = PyqMoment.scope()
            .from("pyq_moment as m")
            .innerJoin("pyq_moment_forward as f", "m.ID", "f.MomentId")
            .where("f.OriginId", params.momentId);
        query = (0, helper_1.queryByTimeRange)(query, "m.CreateTime", params);
        const count = yield PyqMoment.countByQuery(query);
        return count;
    });
}
function getNormalForwardCount(params) {
    return __awaiter(this, void 0, void 0, function* () {
        let query = PyqMoment.normalScope()
            .from("pyq_moment as m")
            .innerJoin("pyq_moment_forward as f", "m.ID", "f.MomentId")
            .where("f.OriginId", params.momentId);
        query = (0, helper_1.queryByTimeRange)(query, "m.CreateTime", params);
        const count = yield PyqMoment.countByQuery(query);
        return count;
    });
}
function getForwardRoleIds(params) {
    return __awaiter(this, void 0, void 0, function* () {
        let query = PyqMoment.scope()
            .from("pyq_moment as m")
            .distinct("m.RoleId")
            .innerJoin("pyq_moment_forward as f", "m.ID", "f.MomentId")
            .where("f.OriginId", params.momentId);
        query = (0, helper_1.queryByTimeRange)(query, "m.CreateTime", params);
        const rows = (yield PyqMomentLike.executeByQuery(query));
        const result = rows.map((x) => x.RoleId);
        return result;
    });
}
function getMomentStat(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const like = yield getLikeCount(params);
        const comment = yield getCommentCount(params);
        const forward = yield getForwardCount(params);
        const likeRoleIds = yield getLikeRoleIds(params);
        const commentRoleIds = yield getCommentRoleIds(params);
        const forwardRoleIds = yield getForwardRoleIds(params);
        return {
            like: like,
            comment: comment,
            forward: forward,
            likeRoleIds: likeRoleIds,
            commentRoleIds: commentRoleIds,
            forwardRoleIds: forwardRoleIds,
        };
    });
}
exports.getMomentStat = getMomentStat;
function getOnemoment(params, cols) {
    return __awaiter(this, void 0, void 0, function* () {
        cols = cols || [
            "ID",
            "RoleId",
            "Text",
            "ImgList",
            "ImgAudit",
            "VideoList",
            "VideoAudit",
            "VideoCoverList",
            "VideoCoverAudit",
            "Hot",
            "CreateTime",
        ];
        const momentId = params.momentId;
        if (!momentId) {
            throw "参数错误";
        }
        let moment = yield PyqMoment.executeByQuery(PyqMoment.normalScope().where({ ID: params.momentId }).select(cols));
        if (!moment || moment.length == 0) {
            throw "该动态不存在";
        }
        moment = moment[0];
        return moment;
    });
}
function getMomentStatNew(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const momentId = parseInt(params.momentId, 10);
        const moment = yield getOnemoment(params);
        const serverId = params.serverId;
        const like = yield getLikeCount(params);
        const comment = yield getCommentCount(params);
        const forward = yield getNormalForwardCount(params);
        const isLocalHot = yield (0, HotMomentsCache_1.isMomentIdsInHotMoments)(serverId, [momentId]);
        const isAllHot = yield (0, HotMomentsCache_1.isMomentIdsInHotMoments)("all", [momentId]);
        const res = {
            createTime: moment.CreateTime,
            hot: moment.Hot,
            isAllHot,
            isLocalHot,
            like,
            comment,
            forward,
        };
        return res;
    });
}
exports.getMomentStatNew = getMomentStatNew;
function getForwardListByMomentId(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const moment = yield getOnemoment(params);
        let query = PyqMoment.normalScope()
            .from("pyq_moment as m")
            .innerJoin("pyq_moment_forward as f", "m.ID", "f.MomentId")
            .where("f.OriginId", params.momentId)
            .select("m.*");
        query = (0, helper_1.queryByTimeRange)(query, "m.CreateTime", params);
        const rows = yield PyqMoment.executeByQuery(query);
        const forwards = _.map(rows, (row) => _.pick(row, ["ID", "RoleId", "CreateTime", "Text", "Hot"]));
        const res = {
            moment,
            forwards: forwards,
        };
        return (0, util_1.keysToCamelCase)(res);
    });
}
exports.getForwardListByMomentId = getForwardListByMomentId;
function getCommentListByMmomentId(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const moment = yield getOnemoment(params);
        let query = PyqComment.normalScope().where("TargetId", params.momentId);
        query = (0, helper_1.queryByTimeRange)(query, "CreateTime", params);
        const rows = yield PyqMomentLike.executeByQuery(query);
        const comments = _.map(rows, (row) => _.pick(row, ["ID", "RoleId", "CreateTime", "Text"]));
        const res = {
            moment,
            comments: comments,
        };
        return (0, util_1.keysToCamelCase)(res);
    });
}
exports.getCommentListByMmomentId = getCommentListByMmomentId;
function getLikeListByMomentId(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const moment = yield getOnemoment(params);
        let query = PyqMomentLike.normalScope().where("MomentId", params.momentId);
        query = (0, helper_1.queryByTimeRange)(query, "CreateTime", params);
        const rows = (yield PyqMomentLike.executeByQuery(query));
        const likes = _.map(rows, (row) => {
            return _.pick(row, ["ID", "RoleId", "CreateTime"]);
        });
        const res = {
            moment,
            likes: likes,
        };
        return (0, util_1.keysToCamelCase)(res);
    });
}
exports.getLikeListByMomentId = getLikeListByMomentId;
function toNumber(num) {
    const result = _.toNumber(num);
    if (_.isNaN(num))
        throw "点赞数或转发数非法";
    else
        return result;
}
function updateHotState(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const PyqMomentMaster = ModelManager.getModelByTableName("pyq_moment", "MASTER");
        const like = toNumber(params.like);
        const forward = toNumber(params.forward);
        const momentId = params.momentId;
        const moment = yield PyqMoment.findById(momentId, ["RoleId", "HotState"]);
        if (moment.RoleId !== _.toNumber(params.playerId)) {
            throw "该心情不属于该用户";
        }
        const hotState = (0, util_1.getJsonInfo)(moment.HotState);
        if (hotState.like) {
            hotState.like = like + _.toNumber(hotState.like);
        }
        else {
            hotState.like = like;
        }
        if (hotState.forward) {
            hotState.forward = forward + _.toNumber(hotState.forward);
        }
        else {
            hotState.forward = forward;
        }
        yield PyqMomentMaster.updateByCondition({ ID: momentId }, { HotState: JSON.stringify(hotState) });
        return { id: momentId };
    });
}
exports.updateHotState = updateHotState;
function delHotState(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const PyqMomentMaster = ModelManager.getModelByTableName("pyq_moment", "MASTER");
        const momentId = params.momentId;
        const moment = yield PyqMoment.findById(momentId);
        if (moment.RoleId !== _.toNumber(params.playerId))
            throw "该状态不属于该用户";
        const hotState = JSON.parse(moment.HotState);
        const [like, forward] = yield Promise.all([
            getLikeCount({ momentId: _.toString(momentId), from: 0, to: Date.now() }),
            getForwardCount({ momentId: _.toString(momentId), from: 0, to: Date.now() }),
        ]);
        hotState.like = like;
        hotState.forward = forward;
        yield PyqMomentMaster.updateByCondition({ ID: momentId }, { HotState: JSON.stringify(hotState) });
        return { id: momentId };
    });
}
exports.delHotState = delHotState;
function recoverMoment(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const ids = params.momentIds ? params.momentIds.split(";") : [];
        const PyqMomentMaster = ModelManager.getModelByTableName("pyq_moment", "MASTER");
        const momentList = yield PyqMoment.find({ ID: ids }, { cols: ["ID", "RoleId"] });
        if (_.isEmpty(momentList))
            throw "状态不存在";
        momentList.forEach((item) => {
            if (item.RoleId != params.playerId)
                throw `状态:${item.ID} 不属于该用户`;
        });
        yield PyqMomentMaster.updateByCondition({ ID: ids }, { Status: constants_1.Statues.Normal });
        return null;
    });
}
exports.recoverMoment = recoverMoment;
//# sourceMappingURL=momentStat.js.map