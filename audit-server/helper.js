"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ipLimitChecker = exports.errorHandler = exports.checkParams = void 0;
const ParamsValidator = require("../common/paramsValidator");
const logger_1 = require("./logger");
const util = require("../common/util");
const config_1 = require("../common/config");
function checkParams(params, schema) {
    return __awaiter(this, void 0, void 0, function* () {
        let validator = ParamsValidator.from(params);
        for (let k of Object.keys(schema)) {
            validator.param(k, schema[k]);
        }
        try {
            yield validator.validate();
        }
        catch (err) {
            let message = JSON.stringify(err);
            throw { errorType: 'InvalidParams', message: message };
        }
    });
}
exports.checkParams = checkParams;
function errorHandler(err, req, res, next) {
    let message = err.message || 'ServerError';
    let errorType = err.errorType || '';
    if (err instanceof Error) {
        logger_1.apiLogger.error(err);
    }
    res.send({ code: -1, errorType: errorType, msg: message });
}
exports.errorHandler = errorHandler;
function isIpInWhiteList(ip, whiteList) {
    let ipWhiteSet = new Set(whiteList);
    return ipWhiteSet.has(ip);
}
function ipLimitChecker(option) {
    let allowIps = option.allowIps;
    allowIps = [].concat(allowIps, ['127.0.0.1', '::ffff:127.0.0.1']);
    return function (req, res, next) {
        if (config_1.testCfg.skip_ip_whitelist_check) {
            return next();
        }
        const checkIp = util.getIp(req);
        if (isIpInWhiteList(checkIp, allowIps)) {
            next();
        }
        else {
            res.send({ code: -2, msg: 'ip not allowed', accessIp: checkIp });
        }
    };
}
exports.ipLimitChecker = ipLimitChecker;
//# sourceMappingURL=helper.js.map