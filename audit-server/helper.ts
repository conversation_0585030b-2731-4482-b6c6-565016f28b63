import * as ParamsValidator from '../common/paramsValidator'
import { apiLogger } from './logger';
import * as util from '../common/util'
import { testCfg } from '../common/config'


export async function checkParams(params, schema) {
    let validator = ParamsValidator.from(params)
    for (let k of Object.keys(schema)) {
        validator.param(k, schema[k])
    }
    try {
        await validator.validate()
    } catch (err) {
        let message = JSON.stringify(err)
        throw { errorType: 'InvalidParams', message: message }
    }
}

export function errorHandler(err, req, res, next) {
    let message = err.message || 'ServerError'
    let errorType = err.errorType || ''
    if (err instanceof Error) {
        apiLogger.error(err);
    }
    res.send({ code: -1, errorType: errorType, msg: message })
}


function isIpInWhiteList(ip: string, whiteList: string[]) {
    let ipWhiteSet = new Set(whiteList)
    return ipWhiteSet.has(ip)
}

export function ipLimitChecker(option: { allowIps: string[] }) {
    let allowIps = option.allowIps
    allowIps = [].concat(allowIps, ['127.0.0.1', '::ffff:127.0.0.1'])
    return function (req, res, next) {
        if (testCfg.skip_ip_whitelist_check) {
            return next()
        }
        const checkIp = util.getIp(req)
        if (isIpInWhiteList(checkIp, allowIps)) {
            next()
        } else {
            res.send({ code: -2, msg: 'ip not allowed', accessIp: checkIp })
        }
    }
}