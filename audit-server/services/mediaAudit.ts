import * as _ from "lodash";
import { MediaRecordType, PicTypeToHandleMap } from "./auditHandler";
import { imageAuditLogger } from "../../common/logger2";
import { AuditImageResult, EProduct, IReturnPicParams, PicItem, statusHash } from "../../common/auditType";
import { AuditStatues } from "../../common/constants";
import { clazzLogger, returnPicLogger } from "../logger";
const logger = clazzLogger("audit-server/services/mediaAudit");

function dispatchAuditHandler({
  picType,
  id,
  url,
  auditStatus,
}: {
  picType: MediaRecordType;
  id: string;
  url: string;
  auditStatus: AuditStatues;
}) {
  const auditHandler = PicTypeToHandleMap[picType];
  if (auditHandler) {
    return auditHandler(id, url, auditStatus);
  } else {
    logger.error("MediaRecordTypeNotFound", { type: picType, id, url, auditStatus });
  }
}

async function handlePicAuditForItem(product: EProduct, item: PicItem) {
  const url = item.url,
    status = item.status, // 0 审核中 1 审核通过 -1审核不通过
    picId = item.pic_id,
    picInfo = picId ? picId.split(":") : [],
    picType = picInfo[0],
    idVal = picInfo[1];

  if (product === "L10" || product === "L10CHAT") {
    // 倩女手游空间
    if (!idVal) return;
    return dispatchAuditHandler({ picType: picType as MediaRecordType, id: idVal, url, auditStatus: status });
  } else {
    logger.error({ product, picType, idVal, url, auditStatus: status }, "UnknownProduct");
  }
}

export async function handlePicAudit(params: IReturnPicParams) {
  returnPicLogger.info({ product: params.product, picList: params.pic_list }, "ReturnPic");
  const retList = [];
  for (const picItem of params.pic_list) {
    const picItemRet = await handlePicAuditForItem(params.product, picItem);
    retList.push({ picItem, ret: picItemRet });
  }
  return retList;
}

export async function processAuditDupInfo(result: AuditImageResult) {
  if (_.isArray(result.dup_info) && result.dup_info.length > 0) {
    imageAuditLogger.info(result, "FindDupInfo");
    const pic_list = result.dup_info.map((item) => {
      return {
        url: item.url,
        status: statusHash[item.status],
        pic_id: item.pic_id,
        role_id: item.role_id,
        media: item.media,
        note: item.note,
      };
    });
    const ret = await handlePicAudit({ product: "L10", pic_list: pic_list });
    return ret;
  } else {
    logger.debug({ result }, "NoDupInfoFound");
  }
}
