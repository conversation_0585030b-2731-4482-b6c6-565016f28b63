"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.processAuditDupInfo = exports.handlePicAudit = void 0;
const _ = require("lodash");
const auditHandler_1 = require("./auditHandler");
const logger2_1 = require("../../common/logger2");
const auditType_1 = require("../../common/auditType");
const logger_1 = require("../logger");
const logger = (0, logger_1.clazzLogger)("audit-server/services/mediaAudit");
function dispatchAuditHandler({ picType, id, url, auditStatus, }) {
    const auditHandler = auditHandler_1.PicTypeToHandleMap[picType];
    if (auditHandler) {
        return auditHandler(id, url, auditStatus);
    }
    else {
        logger.error("MediaRecordTypeNotFound", { type: picType, id, url, auditStatus });
    }
}
function handlePicAuditForItem(product, item) {
    return __awaiter(this, void 0, void 0, function* () {
        const url = item.url, status = item.status, // 0 审核中 1 审核通过 -1审核不通过
        picId = item.pic_id, picInfo = picId ? picId.split(":") : [], picType = picInfo[0], idVal = picInfo[1];
        if (product === "L10" || product === "L10CHAT") {
            // 倩女手游空间
            if (!idVal)
                return;
            return dispatchAuditHandler({ picType: picType, id: idVal, url, auditStatus: status });
        }
        else {
            logger.error({ product, picType, idVal, url, auditStatus: status }, "UnknownProduct");
        }
    });
}
function handlePicAudit(params) {
    return __awaiter(this, void 0, void 0, function* () {
        logger_1.returnPicLogger.info({ product: params.product, picList: params.pic_list }, "ReturnPic");
        const retList = [];
        for (const picItem of params.pic_list) {
            const picItemRet = yield handlePicAuditForItem(params.product, picItem);
            retList.push({ picItem, ret: picItemRet });
        }
        return retList;
    });
}
exports.handlePicAudit = handlePicAudit;
function processAuditDupInfo(result) {
    return __awaiter(this, void 0, void 0, function* () {
        if (_.isArray(result.dup_info) && result.dup_info.length > 0) {
            logger2_1.imageAuditLogger.info(result, "FindDupInfo");
            const pic_list = result.dup_info.map((item) => {
                return {
                    url: item.url,
                    status: auditType_1.statusHash[item.status],
                    pic_id: item.pic_id,
                    role_id: item.role_id,
                    media: item.media,
                    note: item.note,
                };
            });
            const ret = yield handlePicAudit({ product: "L10", pic_list: pic_list });
            return ret;
        }
        else {
            logger.debug({ result }, "NoDupInfoFound");
        }
    });
}
exports.processAuditDupInfo = processAuditDupInfo;
//# sourceMappingURL=mediaAudit.js.map