"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MdShowTopicModel = exports.MdPhotoAlbumModel = exports.MdPhotoModel = exports.MdMomentModel = exports.MdUserModel = exports.PyqPhotoModel = exports.PyqProfileModel = exports.PyqMomentModel = exports.PyqCommentModel = void 0;
const PyqComment = require("../../models/PyqComment");
const PyqMoment = require("../../models/PyqMoments");
const PyqProfile = require("../../models/PyqProfile");
const PyqPhoto = require("../../pyq-server/models/photo");
const MdMoment = require("../../models/Moments");
const MdUser = require("../../models/Users");
const MdPhoto = require("../../models/Photos");
const MdPhotoAlbum = require("../../models/PhotoAlbums");
const MdShowTopic = require("../../models/ShowTopics");
exports.PyqCommentModel = PyqComment;
exports.PyqMomentModel = PyqMoment;
exports.PyqProfileModel = PyqProfile;
exports.PyqPhotoModel = PyqPhoto;
exports.MdUserModel = MdUser;
exports.MdMomentModel = MdMoment;
exports.MdPhotoModel = MdPhoto;
exports.MdPhotoAlbumModel = MdPhotoAlbum;
exports.MdShowTopicModel = MdShowTopic;
//# sourceMappingURL=modelProxy.js.map