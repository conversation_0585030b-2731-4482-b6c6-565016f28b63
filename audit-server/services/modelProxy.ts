import * as PyqComment from '../../models/PyqComment'
import * as PyqMoment from '../../models/PyqMoments'
import * as PyqProfile from '../../models/PyqProfile'
import * as PyqPhoto from '../../pyq-server/models/photo'

import * as MdMoment from '../../models/Moments'
import * as MdUser from '../../models/Users'
import * as MdPhoto from '../../models/Photos'
import * as MdPhotoAlbum from '../../models/PhotoAlbums'
import * as MdShowTopic from '../../models/ShowTopics'

import BaseModelClass = require('../../models/BaseModelClass');
import { PyqMomentRecord, PyqCommentRecord, PyqProfileRecord, PyqPhotoRecord, MdMomentRecord, MdShowTopicRecord, MdUserRecord, MdPhotoRecord, MdPhotoAlbumRecord } from '../../common/type';

export let PyqCommentModel = PyqComment as BaseModelClass<PyqCommentRecord>
export let PyqMomentModel = PyqMoment as BaseModelClass<PyqMomentRecord>
export let PyqProfileModel = PyqProfile as BaseModelClass<PyqProfileRecord>
export let PyqPhotoModel = PyqPhoto as BaseModelClass<PyqPhotoRecord> & {
    getChatPhotoCacheKey: (key: string) => string
}

export let MdUserModel = MdUser as BaseModelClass<MdUserRecord>
export let MdMomentModel = MdMoment as BaseModelClass<MdMomentRecord>
export let MdPhotoModel = MdPhoto as BaseModelClass<MdPhotoRecord>
export let MdPhotoAlbumModel = MdPhotoAlbum as BaseModelClass<MdPhotoAlbumRecord>
export let MdShowTopicModel = MdShowTopic as BaseModelClass<MdShowTopicRecord>