"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PicTypeToHandleMap = exports.handleAuditForHomeDecoratePhoto = exports.handleAuditForFireWorkPhoto = exports.handleAuditForPyqProfileAvatar = exports.handleAuditForPyqPhoto = exports.handleAuditForPyqChatPhoto = exports.handleAuditForMomentCommon = exports.handleAuditForPyqMomentVideo = exports.handleAuditForPyqMomentPhoto = exports.handleAuditForMdShowTopic = exports.handleAuditForMdMomentVideo = exports.handleAuditForMdMomentImage = exports.handleAuditForMdAvatar = exports.handleAuditForMdPhoto = void 0;
const _ = require("lodash");
const modelProxy_1 = require("./modelProxy");
const util_1 = require("../../common/util");
const redis_1 = require("../../common/redis");
const fireworkPhoto_1 = require("../../pyq-server/models/fireworkPhoto");
const constants_1 = require("../../common/constants");
const logger_1 = require("../logger");
const cacheUtil_1 = require("../../common/cacheUtil");
const photo_1 = require("../../pyq-server/components/homeDecoratePhoto/models/photo");
const nanoid_1 = require("nanoid");
const logger = (0, logger_1.clazzLogger)("services/auditHandler");
function handleAuditForMdPhoto(photoId, url, status) {
    return __awaiter(this, void 0, void 0, function* () {
        const photo = yield modelProxy_1.MdPhotoModel.updateById(_.toNumber(photoId), { AuditStatus: status });
        if (photo && photo.PhotoAlbumID) {
            modelProxy_1.MdPhotoAlbumModel.updateById(photo.PhotoAlbumID, { UpdateTime: Date.now() });
        }
    });
}
exports.handleAuditForMdPhoto = handleAuditForMdPhoto;
function handleAuditForMdAvatar(id, url, status) {
    return __awaiter(this, void 0, void 0, function* () {
        yield modelProxy_1.MdUserModel.updateById(_.toNumber(id), { AvaAuthStatus: status });
        const photo = yield modelProxy_1.MdPhotoModel.findOne({ Url: url });
        if (photo) {
            yield handleAuditForMdPhoto("" + photo.ID, url, status);
        }
    });
}
exports.handleAuditForMdAvatar = handleAuditForMdAvatar;
function handleAuditForMdMomentImage(id, url, status) {
    return __awaiter(this, void 0, void 0, function* () {
        return handleAuditForMomentCommon(modelProxy_1.MdMomentModel, id, url, status, "ImgList", "ImgAudit");
    });
}
exports.handleAuditForMdMomentImage = handleAuditForMdMomentImage;
function handleAuditForMdMomentVideo(id, url, status) {
    return __awaiter(this, void 0, void 0, function* () {
        return handleAuditForMomentCommon(modelProxy_1.MdMomentModel, id, url, status, "VideoList", "VideoAudit");
    });
}
exports.handleAuditForMdMomentVideo = handleAuditForMdMomentVideo;
function handleAuditForMdShowTopic(id, url, status) {
    return __awaiter(this, void 0, void 0, function* () {
        return handleAuditForMomentCommon(modelProxy_1.MdShowTopicModel, id, url, status, "ImgList", "ImgAudit");
    });
}
exports.handleAuditForMdShowTopic = handleAuditForMdShowTopic;
function handleAuditForPyqMomentPhoto(id, url, status) {
    return __awaiter(this, void 0, void 0, function* () {
        return handleAuditForMomentCommon(modelProxy_1.PyqMomentModel, id, url, status, "ImgList", "ImgAudit");
    });
}
exports.handleAuditForPyqMomentPhoto = handleAuditForPyqMomentPhoto;
function handleAuditForPyqMomentVideo(id, url, status) {
    return __awaiter(this, void 0, void 0, function* () {
        return handleAuditForMomentCommon(modelProxy_1.PyqMomentModel, id, url, status, "VideoList", "VideoAudit");
    });
}
exports.handleAuditForPyqMomentVideo = handleAuditForPyqMomentVideo;
function handleAuditForMomentCommon(momentModel, id, url, status, urlListCol, urlAuditCol) {
    return __awaiter(this, void 0, void 0, function* () {
        const lockKey = `modify_audit_for_${momentModel.tableName}:${id}`;
        try {
            const ownerId = (0, nanoid_1.nanoid)();
            const hasLock = yield cacheUtil_1.RedisLock.optimistic(lockKey, ownerId, 1000, 5, 150);
            if (!hasLock) {
                logger.error({ tableName: momentModel.tableName, id, url, status, urlListCol, urlAuditCol }, "AcquireModifyMomentAuditLockFailed");
                return false;
            }
            const record = yield momentModel.findOne({ Id: id }, [urlListCol, urlAuditCol]);
            if (record) {
                const urlList = (0, util_1.csvStrToArray)(record[urlListCol]);
                const auditList = _.fill(new Array(urlList.length), constants_1.AuditStatues.Auditing);
                if (record[urlAuditCol]) {
                    const curAuditList = record[urlAuditCol].split(",");
                    _.forEach(curAuditList, (val, index) => {
                        const auditStatus = parseInt(val, 10);
                        auditList[index] = auditStatus;
                    });
                }
                const auditIndex = _.indexOf(urlList, url);
                if (auditIndex != -1) {
                    auditList[auditIndex] = status;
                }
                const urlAuditValue = auditList.join(",");
                const ret = yield momentModel.updateByCondition({ Id: id }, { [urlAuditCol]: urlAuditValue });
                logger.debug({ id, urlAuditCol, urlAuditValue, url, status, record, urlList, auditList, auditIndex }, "updateMomentAuditOk");
                yield cacheUtil_1.RedisLock.unLock(lockKey, ownerId);
                return ret;
            }
            else {
                logger.error({ tableName: momentModel.tableName, id, url, status, urlListCol, urlAuditCol }, "MomentAuditRecordNotFound");
                yield cacheUtil_1.RedisLock.unLock(lockKey, ownerId);
                return false;
            }
        }
        catch (err) {
            logger.error({ tableName: momentModel.tableName, id, url, status, urlListCol, urlAuditCol, err }, "MomentAuditRecordFailed");
            throw err;
        }
    });
}
exports.handleAuditForMomentCommon = handleAuditForMomentCommon;
function handleAuditForPyqChatPhoto(id, url, status) {
    return __awaiter(this, void 0, void 0, function* () {
        yield modelProxy_1.PyqPhotoModel.updateById(_.toNumber(id), { AuditStatus: status });
        if (constants_1.AuditStatues.Reject === status) {
            const chatPhotoKey = modelProxy_1.PyqPhotoModel.getChatPhotoCacheKey(id);
            yield (0, redis_1.getRedis)().delAsync(chatPhotoKey);
        }
    });
}
exports.handleAuditForPyqChatPhoto = handleAuditForPyqChatPhoto;
function handleAuditForPyqPhoto(id, url, status) {
    return __awaiter(this, void 0, void 0, function* () {
        return modelProxy_1.PyqPhotoModel.updateById(_.toNumber(id), { AuditStatus: status });
    });
}
exports.handleAuditForPyqPhoto = handleAuditForPyqPhoto;
function handleAuditForPyqProfileAvatar(id, url, status) {
    return __awaiter(this, void 0, void 0, function* () {
        return modelProxy_1.PyqProfileModel.updateByCondition({ RoleId: id, Photo: url }, { PhotoAudit: status });
    });
}
exports.handleAuditForPyqProfileAvatar = handleAuditForPyqProfileAvatar;
function handleAuditForFireWorkPhoto(id, url, status) {
    return __awaiter(this, void 0, void 0, function* () {
        return fireworkPhoto_1.FireworkPhotoModel.updateByCondition({ ID: _.toNumber(id), Url: url }, { AuditStatus: status });
    });
}
exports.handleAuditForFireWorkPhoto = handleAuditForFireWorkPhoto;
function handleAuditForHomeDecoratePhoto(id, url, status) {
    return __awaiter(this, void 0, void 0, function* () {
        return photo_1.HomeDecoratePhotoModel.updateByCondition({ id: _.toNumber(id), url }, { auditStatus: status });
    });
}
exports.handleAuditForHomeDecoratePhoto = handleAuditForHomeDecoratePhoto;
exports.PicTypeToHandleMap = {
    profile_pyq: handleAuditForPyqProfileAvatar,
    profile: handleAuditForPyqProfileAvatar,
    pyq_photo: handleAuditForPyqPhoto,
    pyq_moment_video: handleAuditForPyqMomentVideo,
    moment_pyq: handleAuditForPyqMomentPhoto,
    md_album_photo: handleAuditForMdPhoto,
    avatar_md: handleAuditForMdAvatar,
    avatar: handleAuditForMdAvatar,
    moment: handleAuditForMdMomentImage,
    moment_md: handleAuditForMdMomentImage,
    moment_video_md: handleAuditForMdMomentVideo,
    md_show_topic: handleAuditForMdShowTopic,
    pyq_chat_photo: handleAuditForPyqChatPhoto,
    firework_photo: handleAuditForFireWorkPhoto,
    home_decorate_photo: handleAuditForHomeDecoratePhoto
};
//# sourceMappingURL=auditHandler.js.map