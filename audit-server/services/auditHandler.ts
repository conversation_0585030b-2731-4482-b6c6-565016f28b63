import * as _ from "lodash"
import {
  MdMomentModel,
  MdPhotoAlbumModel,
  MdPhotoModel,
  MdShowTopicModel,
  MdUserModel,
  PyqMomentModel,
  PyqPhotoModel,
  PyqProfileModel,
} from "./modelProxy"
import { csvStrToArray } from "../../common/util"
import { getRedis } from "../../common/redis"
import BaseModelClass = require("../../models/BaseModelClass")
import { FireworkPhotoModel } from "../../pyq-server/models/fireworkPhoto"
import { AuditStatues } from "../../common/constants"
import { clazzLogger } from "../logger"
import { RedisLock } from "../../common/cacheUtil"
import { HomeDecoratePhotoModel } from "../../pyq-server/components/homeDecoratePhoto/models/photo"
import { nanoid } from "nanoid"
const logger = clazzLogger("services/auditHandler")

export async function handleAuditForMdPhoto(photoId: string, url: string, status: AuditStatues) {
  const photo = await MdPhotoModel.updateById(_.toNumber(photoId), { AuditStatus: status })
  if (photo && photo.PhotoAlbumID) {
    MdPhotoAlbumModel.updateById(photo.PhotoAlbumID, { UpdateTime: Date.now() })
  }
}

export async function handleAuditForMdAvatar(id: string, url: string, status: AuditStatues) {
  await MdUserModel.updateById(_.toNumber(id), { AvaAuthStatus: status })
  const photo = await MdPhotoModel.findOne({ Url: url })
  if (photo) {
    await handleAuditForMdPhoto("" + photo.ID, url, status)
  }
}

export async function handleAuditForMdMomentImage(id: string, url: string, status: AuditStatues) {
  return handleAuditForMomentCommon(MdMomentModel, id, url, status, "ImgList", "ImgAudit")
}

export async function handleAuditForMdMomentVideo(id: string, url: string, status: AuditStatues) {
  return handleAuditForMomentCommon(MdMomentModel, id, url, status, "VideoList", "VideoAudit")
}

export async function handleAuditForMdShowTopic(id: string, url: string, status: AuditStatues) {
  return handleAuditForMomentCommon(MdShowTopicModel, id, url, status, "ImgList", "ImgAudit")
}

export async function handleAuditForPyqMomentPhoto(id: string, url: string, status: AuditStatues) {
  return handleAuditForMomentCommon(PyqMomentModel, id, url, status, "ImgList", "ImgAudit")
}

export async function handleAuditForPyqMomentVideo(id: string, url: string, status: AuditStatues) {
  return handleAuditForMomentCommon(PyqMomentModel, id, url, status, "VideoList", "VideoAudit")
}

export async function handleAuditForMomentCommon(
  momentModel: BaseModelClass<any>,
  id: string,
  url: string,
  status: AuditStatues,
  urlListCol: string,
  urlAuditCol: string
) {
  const lockKey = `modify_audit_for_${momentModel.tableName}:${id}`
  try {
    const ownerId = nanoid()
    const hasLock = await RedisLock.optimistic(lockKey, ownerId, 1000, 5, 150)
    if (!hasLock) {
      logger.error(
        { tableName: momentModel.tableName, id, url, status, urlListCol, urlAuditCol },
        "AcquireModifyMomentAuditLockFailed"
      )
      return false
    }
    const record = await momentModel.findOne({ Id: id }, [urlListCol, urlAuditCol])
    if (record) {
      const urlList = csvStrToArray(record[urlListCol])
      const auditList = _.fill(new Array(urlList.length), AuditStatues.Auditing)
      if (record[urlAuditCol]) {
        const curAuditList = record[urlAuditCol].split(",")
        _.forEach(curAuditList, (val, index) => {
          const auditStatus = parseInt(val, 10)
          auditList[index] = auditStatus
        })
      }
      const auditIndex = _.indexOf(urlList, url)
      if (auditIndex != -1) {
        auditList[auditIndex] = status
      }
      const urlAuditValue = auditList.join(",")

      const ret = await momentModel.updateByCondition({ Id: id }, { [urlAuditCol]: urlAuditValue })
      logger.debug(
        { id, urlAuditCol, urlAuditValue, url, status, record, urlList, auditList, auditIndex },
        "updateMomentAuditOk"
      )
      await RedisLock.unLock(lockKey, ownerId)
      return ret
    } else {
      logger.error(
        { tableName: momentModel.tableName, id, url, status, urlListCol, urlAuditCol },
        "MomentAuditRecordNotFound"
      )
      await RedisLock.unLock(lockKey, ownerId)
      return false
    }
  } catch (err) {
    logger.error(
      { tableName: momentModel.tableName, id, url, status, urlListCol, urlAuditCol, err },
      "MomentAuditRecordFailed"
    )
    throw err
  }
}

export async function handleAuditForPyqChatPhoto(id: string, url: string, status: AuditStatues) {
  await PyqPhotoModel.updateById(_.toNumber(id), { AuditStatus: status })
  if (AuditStatues.Reject === status) {
    const chatPhotoKey = PyqPhotoModel.getChatPhotoCacheKey(id)
    await getRedis().delAsync(chatPhotoKey)
  }
}

export async function handleAuditForPyqPhoto(id: string, url: string, status: AuditStatues) {
  return PyqPhotoModel.updateById(_.toNumber(id), { AuditStatus: status })
}

export async function handleAuditForPyqProfileAvatar(id: string, url: string, status: AuditStatues) {
  return PyqProfileModel.updateByCondition({ RoleId: id, Photo: url }, { PhotoAudit: status })
}

export async function handleAuditForFireWorkPhoto(id: string, url: string, status: AuditStatues) {
  return FireworkPhotoModel.updateByCondition({ ID: _.toNumber(id), Url: url }, { AuditStatus: status })
}


export async function handleAuditForHomeDecoratePhoto(id: string, url: string, status: AuditStatues) {
  return HomeDecoratePhotoModel.updateByCondition({ id: _.toNumber(id), url }, { auditStatus: status })
}

export const PicTypeToHandleMap = {
  profile_pyq: handleAuditForPyqProfileAvatar,
  profile: handleAuditForPyqProfileAvatar,
  pyq_photo: handleAuditForPyqPhoto,
  pyq_moment_video: handleAuditForPyqMomentVideo,
  moment_pyq: handleAuditForPyqMomentPhoto,
  md_album_photo: handleAuditForMdPhoto,
  avatar_md: handleAuditForMdAvatar,
  avatar: handleAuditForMdAvatar,
  moment: handleAuditForMdMomentImage,
  moment_md: handleAuditForMdMomentImage,
  moment_video_md: handleAuditForMdMomentVideo,
  md_show_topic: handleAuditForMdShowTopic,
  pyq_chat_photo: handleAuditForPyqChatPhoto,
  firework_photo: handleAuditForFireWorkPhoto,
  home_decorate_photo: handleAuditForHomeDecoratePhoto
}

export type MediaRecordType = keyof typeof PicTypeToHandleMap