"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.batchCmd = void 0;
const audit = require("../../common/audit");
const bluebird = require("bluebird");
const _ = require("lodash");
const logger_1 = require("../logger");
function batchCmd(req, res, next) {
    bluebird.method(function () {
        logger_1.cmdLogger.info({ params: req.params, cmd: req.params.cmd }, 'ReceiveCmd');
        return audit.batchCmd(req.params);
    })().then(function (result) {
        res.send({ status: 'OK', result: result });
    }).catch(function (err) {
        let record = _.omit(req.params, ['logger']);
        logger_1.cmdLogger.error({ err: err, record: record });
        res.send({ status: 'fail', result: err.message || err });
    });
}
exports.batchCmd = batchCmd;
//# sourceMappingURL=batchCmd.js.map