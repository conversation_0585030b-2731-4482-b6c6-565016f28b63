"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.cmdHandlers = void 0;
const adminTypes_1 = require("./adminTypes");
const ParamsValidator = require("../../common/paramsValidator");
const QnmRoleInfo = require("../../models/QNMRoleInfos");
const PyqProfile = require("../../models/PyqProfile");
const PyqMoment = require("../../models/PyqMoments");
const PyqComment = require("../../models/PyqComment");
const PyqMomentForward = require("../../models/PyqMomentForward");
const MomentService = require("../../service/qnm/pyq/moment");
const CommentService = require("../../pyq-server/services/comment");
const HotMomentService = require("../../service/qnm/pyq/HotMomentsCache");
const _ = require("lodash");
const nos = require("../../common/nos");
const delayTasks_1 = require("../../cron-jobs/delayTasks");
const util2_1 = require("../../common/util2");
const bluebird = require("bluebird");
const officialRoles_1 = require("../data/officialRoles");
const MomentCols = ["ID", "RoleId", "Text", "ImgList", "CreateTime", "HotState"];
var TopToTypes;
(function (TopToTypes) {
    TopToTypes[TopToTypes["LOCAL_SERVER"] = 1] = "LOCAL_SERVER";
    TopToTypes[TopToTypes["ALL_SERVER"] = 2] = "ALL_SERVER";
})(TopToTypes || (TopToTypes = {}));
function checkParams(params, schema) {
    return __awaiter(this, void 0, void 0, function* () {
        const validator = ParamsValidator.from(params);
        for (const k of Object.keys(schema)) {
            validator.param(k, schema[k]);
        }
        try {
            yield validator.validate();
        }
        catch (err) {
            const message = JSON.stringify(err);
            throw { message: message };
        }
    });
}
function updatePlayerInfo(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const schema = {
            roleId: { type: Number },
            avatar: { type: String, required: false },
            roleName: { type: String, required: false },
            location: { type: String, required: false },
            signature: { type: String, required: false },
        };
        yield checkParams(params, schema);
        // 处理pyq_profile
        const now = Date.now();
        const profileProps = {};
        if (params.avatar) {
            profileProps["Photo"] = params.avatar;
            profileProps["PhotoAudit"] = 1;
        }
        if (params.location) {
            profileProps["Location"] = params.location;
        }
        if (params.signature) {
            profileProps["Signature"] = params.signature;
        }
        if (!_.isEmpty(profileProps)) {
            profileProps["UpdateTime"] = now;
            yield PyqProfile.updateByCondition({ RoleId: params.roleId }, profileProps);
        }
        if (params.roleName) {
            const roleProps = { RoleName: params.roleName, UpdateTime: now };
            yield QnmRoleInfo.updateByCondition({ RoleId: params.roleId }, roleProps);
        }
        const result = yield getProfile(params.roleId);
        return { status: adminTypes_1.CmdStatus.OK, result: result };
    });
}
function officialRoleIdChecker(roleId) {
    return __awaiter(this, void 0, void 0, function* () {
        if ((0, officialRoles_1.checkIsOfficialId)(roleId)) {
            return true;
        }
        else {
            return bluebird.reject({ message: "roleid is not official id" });
        }
    });
}
function getProfile(roleId) {
    return __awaiter(this, void 0, void 0, function* () {
        const profile = yield PyqProfile.findOne({ RoleId: roleId }, ["RoleId", "Photo as Avatar", "Location", "Signature"]);
        const record = yield QnmRoleInfo.findOne({ RoleId: roleId }, ["RoleName"]);
        if (profile && record) {
            const result = Object.assign({}, profile, record);
            return result;
        }
        else {
            return bluebird.reject({ errorType: "DataNotFound", message: "角色信息未找到" });
        }
    });
}
function getPlayerInfo(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const schema = {
            roleId: { type: Number },
        };
        yield checkParams(params, schema);
        yield officialRoleIdChecker(params.roleId);
        const profile = yield getProfile(params.roleId);
        return { status: adminTypes_1.CmdStatus.OK, result: profile };
    });
}
function listMoments(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const schema = {
            roleId: { type: Number },
            lastId: { type: Number, required: false },
            limit: { type: Number, required: false, min: 1, default: 10, max: 20 },
        };
        yield checkParams(params, schema);
        yield officialRoleIdChecker(params.roleId);
        let query = PyqMoment.normalScope()
            .select(MomentCols)
            .whereIn("RoleId", params.roleId)
            .orderBy("ID", "desc")
            .limit(params.limit);
        if (params.lastId) {
            query = query.where("ID", "<", params.lastId);
        }
        const rows = (yield PyqMoment.executeByQuery(query));
        const list = yield formatMomentList(rows);
        return { status: adminTypes_1.CmdStatus.OK, result: { list: list } };
    });
}
function delMoment(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const schema = {
            roleId: { type: Number },
            momentId: { type: Number },
        };
        yield checkParams(params, schema);
        yield officialRoleIdChecker(params.roleId);
        const info = yield PyqMoment.updateByCondition({ ID: params.momentId, RoleId: params.roleId }, { Status: -1 });
        return { status: adminTypes_1.CmdStatus.OK, result: { message: info.message } };
    });
}
function addMoment(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const MAX_IMAGE_SIZE = 6;
        const schema = {
            roleId: { type: Number },
            imgs: { type: Array, required: false, default: [] },
            videos: { type: Array, required: false, default: [] },
            videoCovers: { type: Array, required: false, default: [] },
            text: { type: String, required: false, default: "" },
            topAt: { type: Number, required: false },
            topTo: { type: Number, required: false, values: [TopToTypes.ALL_SERVER, TopToTypes.LOCAL_SERVER] },
        };
        yield checkParams(params, schema);
        // insert moment
        const roleId = params.roleId;
        const now = Date.now();
        params.imgs = _.take(params.imgs, MAX_IMAGE_SIZE);
        const imgList = params.imgs.join(",");
        const imgAudit = (0, util2_1.fillCsvStr)("1", params.imgs.length);
        let videos = params.videos || [];
        videos = videos.map((r) => r.replace("https://hi-163-qnm.nosdn.127.net/", "http://nos.netease.com/hi-163-qnm/"));
        const videoList = videos.join(",");
        const videoAudit = (0, util2_1.fillCsvStr)("1", params.videos.length);
        const newVideoCovers = [];
        const covers = params.videoCovers || [];
        for (let i = 0; i < videos.length; i++) {
            const videoFrameCover = videos[i] + "?vframe";
            const cover = covers[i] || videoFrameCover;
            newVideoCovers.push(cover);
        }
        const videoCoverList = newVideoCovers.join(",");
        const videoCoverAudit = (0, util2_1.fillCsvStr)("1", newVideoCovers.length);
        const props = {
            RoleId: params.roleId,
            ImgList: imgList,
            ImgAudit: imgAudit,
            VideoList: videoList,
            VideoAudit: videoAudit,
            VideoCoverList: videoCoverList,
            VideoCoverAudit: videoCoverAudit,
            Text: params.text,
            CreateTime: now,
        };
        const insertInfo = yield PyqMoment.insert(props);
        const insertId = insertInfo.insertId;
        if (params.topTo === TopToTypes.ALL_SERVER) {
            yield HotMomentService.upMomentForRoleId(roleId, insertId, "all", null);
            yield delayTasks_1.PyqDelayQueue.addJob({
                id: "" + insertId,
                topic: delayTasks_1.DELAY_TOPICS.DownHotMomentTopic,
                body: { momentId: insertId },
                runAt: params.topAt,
            });
        }
        else {
            const record = yield QnmRoleInfo.findOne({ RoleId: params.roleId }, ["ServerId"]);
            if (record && record.ServerId) {
                yield HotMomentService.upMomentForRoleId(roleId, insertId, record.ServerId, null);
                const body = { momentId: insertId, serverId: record.serverId };
                yield delayTasks_1.PyqDelayQueue.addJob({
                    id: "" + insertId,
                    topic: delayTasks_1.DELAY_TOPICS.DownHotMomentTopic,
                    body: body,
                    runAt: params.topAt,
                });
            }
        }
        return { status: adminTypes_1.CmdStatus.OK, result: { id: insertId } };
    });
}
function likeMoment(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const schema = {
            roleId: { type: Number },
            momentId: { type: Number },
        };
        yield checkParams(params, schema);
        yield officialRoleIdChecker(params.roleId);
        yield MomentService.likeMoment({ params: { roleid: params.roleId, id: params.momentId } });
        return { status: adminTypes_1.CmdStatus.OK, result: null };
    });
}
function cancelLikeMoment(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const schema = {
            roleId: { type: Number },
            momentId: { type: Number },
        };
        yield checkParams(params, schema);
        yield officialRoleIdChecker(params.roleId);
        yield MomentService.likeMoment({ params: { roleid: params.roleId, id: params.momentId, action: "undo" } });
        return { status: adminTypes_1.CmdStatus.OK, result: null };
    });
}
function forwardMoment(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const schema = {
            roleId: { type: Number },
            momentId: { type: Number },
            text: { type: String },
        };
        yield checkParams(params, schema);
        yield officialRoleIdChecker(params.roleId);
        const info = yield PyqMomentForward.forwardMoment(params.roleId, params.momentId, params.text);
        return { status: adminTypes_1.CmdStatus.OK, result: info };
    });
}
function getRoleNameMap(roleIds) {
    return __awaiter(this, void 0, void 0, function* () {
        const roleInfos = (yield QnmRoleInfo.find({ RoleId: roleIds }, { cols: ["RoleId", "RoleName"] }));
        const roleNameMap = new Map();
        for (const r of roleInfos) {
            roleNameMap.set(r.RoleId, r.RoleName);
        }
        return roleNameMap;
    });
}
function formatMomentList(list) {
    return __awaiter(this, void 0, void 0, function* () {
        const roleIds = _.uniq(list.map((x) => x.RoleId));
        const roleNameMap = yield getRoleNameMap(roleIds);
        const result = [];
        for (const e of list) {
            const roleName = roleNameMap.get(e.RoleId) || "";
            const imgList = (e.ImgList || "").split(",").filter((s) => !!s);
            const hotState = PyqMoment.formatHotState(e.HotState);
            const likeCount = hotState.like;
            const commentCount = hotState.reply + hotState.comment;
            const item = {
                ID: e.ID,
                RoleId: e.RoleId,
                RoleName: roleName,
                ImgList: imgList,
                Text: e.Text,
                CreateTime: e.CreateTime,
                LikeCount: likeCount,
                CommentCount: commentCount,
            };
            result.push(item);
        }
        return result;
    });
}
function listHotMoments(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const schema = {
            page: { type: Number, required: false, default: 1 },
            pageSize: { type: Number, required: false, default: 10, max: 20, min: 1 },
        };
        yield checkParams(params, schema);
        let moments = (yield HotMomentService.getMoments("all", null));
        const start = (params.page - 1) * params.pageSize;
        const end = start + params.pageSize;
        moments = moments.slice(start, end);
        const mIds = _.uniq(moments.map((e) => e.ID));
        const rows = (yield PyqMoment.findByIds(mIds, MomentCols));
        const list = yield formatMomentList(rows);
        return { status: adminTypes_1.CmdStatus.OK, result: { list: list } };
    });
}
function addComment(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const schema = {
            roleId: { type: Number },
            momentId: { type: Number },
            text: { type: String },
        };
        yield checkParams(params, schema);
        yield officialRoleIdChecker(params.roleId);
        const result = yield CommentService.addComment({ id: params.momentId, roleid: params.roleId, text: params.text });
        return { status: adminTypes_1.CmdStatus.OK, result: result };
    });
}
function delComment(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const schema = {
            roleId: { type: Number },
            commentId: { type: Number },
        };
        yield checkParams(params, schema);
        const comment = yield PyqComment.findOne({ ID: params.commentId, RoleId: params.roleId, Status: 0 }, ["ID"]);
        if (!comment) {
            return { status: adminTypes_1.CmdStatus.Fail, result: { message: "评论不存在" } };
        }
        else {
            yield MomentService.delComment({ id: params.commentId, roleid: params.roleId });
            return { status: adminTypes_1.CmdStatus.OK, result: null };
        }
    });
}
function replyComment(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const schema = {
            roleId: { type: Number },
            commentId: { type: Number },
            text: { type: String },
        };
        yield checkParams(params, schema);
        yield officialRoleIdChecker(params.roleId);
        const comment = (yield PyqComment.findOne({ ID: params.commentId }, ["TargetId as MomentId", "RoleId"]));
        if (!comment) {
            return { status: adminTypes_1.CmdStatus.Fail, result: "找不到对应的评论Id" };
        }
        else {
            const result = yield CommentService.addComment({
                id: comment.MomentId,
                roleid: params.roleId,
                replyid: comment.RoleId,
                text: params.text,
            });
            return { status: adminTypes_1.CmdStatus.OK, result: result };
        }
    });
}
function listComments(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const schema = {
            momentId: { type: Number },
            lastId: { type: Number, required: false },
            limit: { type: Number, required: false, min: 1, default: 10, max: 20 },
        };
        yield checkParams(params, schema);
        const cols = ["ID", "RoleId", "Text", "CreateTime"];
        let query = PyqComment.normalScope().select(cols).where("TargetId", params.momentId).limit(params.limit);
        if (params.lastId) {
            query = query.where("ID", "<", params.lastId);
        }
        const rows = (yield PyqComment.executeByQuery(query));
        const roleIds = _.uniq(rows.map((x) => x.RoleId));
        const roleNameMap = yield getRoleNameMap(roleIds);
        const list = [];
        for (const r of rows) {
            const roleName = roleNameMap.get(r.RoleId);
            const ele = { ID: r.ID, RoleId: r.RoleId, RoleName: roleName, Text: r.Text, CreateTime: r.CreateTime };
            list.push(ele);
        }
        return { status: adminTypes_1.CmdStatus.OK, result: { list: list } };
    });
}
function getNosToken(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const result = nos.getToken("qnm", params);
        return { status: adminTypes_1.CmdStatus.OK, result: result };
    });
}
exports.cmdHandlers = {
    updatePlayerInfo: updatePlayerInfo,
    getPlayerInfo: getPlayerInfo,
    listMoments: listMoments,
    delMoment: delMoment,
    addMoment: addMoment,
    addComment: addComment,
    delComment: delComment,
    replyComment: replyComment,
    likeMoment: likeMoment,
    cancelLikeMoment: cancelLikeMoment,
    forwardMoment: forwardMoment,
    listHotMoments: listHotMoments,
    listComments: listComments,
    getNosToken: getNosToken,
};
//# sourceMappingURL=adminCmd.js.map