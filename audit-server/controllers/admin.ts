import { ICmdParams, CmdStatus, Cmd, CmdHandler, ICmdResult } from './adminTypes'
import { cmdHandlers as adminCmds } from './adminCmd'

function formatErrorObj(err) {
  let message = err.message || err.msg
  return { message: message }
}

export async function dispatchCmd(req, res, next) {
  let params = req.params as ICmdParams
  let handler = CmdDispatch.get(req.params.cmd)
  try {
    let result: ICmdResult = await handler(params)
    res.send(result)
  } catch (err) {
    let errObj = formatErrorObj(err)
    let result: ICmdResult = { status: CmdStatus.Fail, result: { message: errObj.message } }
    res.send(result)
  }
}

class CmdDispatch {
  private static map: Map<Cmd, CmdHandler> = new Map()

  static add(cmd: Cmd, handler: CmdHandler) {
    this.map.set(cmd, handler)
  }

  static get(cmd: Cmd) {
    if (this.map.has(cmd)) {
      return this.map.get(cmd)
    } else {
      return this.defaultHandler
    }
  }

  static async defaultHandler(params: ICmdParams) {
    return {
      status: CmdStatus.Fail,
      result: { message: '未受支持的指令' }
    }
  }
}

function installAllCmds() {
  for (let k of Object.keys(adminCmds)) {
    CmdDispatch.add(k, adminCmds[k])
  }
}

installAllCmds()