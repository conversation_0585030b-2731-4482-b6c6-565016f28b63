import * as audit from '../../common/audit'
import * as bluebird from 'bluebird'
import * as _ from 'lodash'
import { cmdLogger } from '../logger';

export function batchCmd(req, res, next) {
    bluebird.method(function () {
        cmdLogger.info({ params: req.params, cmd: req.params.cmd }, 'ReceiveCmd')
        return audit.batchCmd(req.params)
    })().then(function (result) {
        res.send({ status: 'OK', result: result })
    }).catch(function (err) {
        let record = _.omit(req.params, ['logger'])
        cmdLogger.error({ err: err, record: record })
        res.send({ status: 'fail', result: err.message || err })
    })
}