"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.dispatchCmd = void 0;
const adminTypes_1 = require("./adminTypes");
const adminCmd_1 = require("./adminCmd");
function formatErrorObj(err) {
    let message = err.message || err.msg;
    return { message: message };
}
function dispatchCmd(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        let params = req.params;
        let handler = CmdDispatch.get(req.params.cmd);
        try {
            let result = yield handler(params);
            res.send(result);
        }
        catch (err) {
            let errObj = formatErrorObj(err);
            let result = { status: adminTypes_1.CmdStatus.Fail, result: { message: errObj.message } };
            res.send(result);
        }
    });
}
exports.dispatchCmd = dispatchCmd;
class CmdDispatch {
    static add(cmd, handler) {
        this.map.set(cmd, handler);
    }
    static get(cmd) {
        if (this.map.has(cmd)) {
            return this.map.get(cmd);
        }
        else {
            return this.defaultHandler;
        }
    }
    static defaultHandler(params) {
        return __awaiter(this, void 0, void 0, function* () {
            return {
                status: adminTypes_1.CmdStatus.Fail,
                result: { message: '未受支持的指令' }
            };
        });
    }
}
CmdDispatch.map = new Map();
function installAllCmds() {
    for (let k of Object.keys(adminCmd_1.cmdHandlers)) {
        CmdDispatch.add(k, adminCmd_1.cmdHandlers[k]);
    }
}
installAllCmds();
//# sourceMappingURL=admin.js.map