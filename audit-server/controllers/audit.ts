import { checkPara<PERSON>, errorHandler } from "../helper";
import { handlePicAudit } from "../services/mediaAudit";
import { IReturnPicParams } from "../../common/auditType";

export async function returnPic(req, res, next) {
  const schema = {
    product: { type: String },
    pic_list: { type: Array },
  };
  try {
    await checkParams(req.params, schema);
    const params = req.params as IReturnPicParams;
    const ret = await handlePicAudit(params);
    res.send({ code: 0, data: ret });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}
