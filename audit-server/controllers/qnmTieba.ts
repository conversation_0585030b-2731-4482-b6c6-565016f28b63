import { error<PERSON><PERSON><PERSON> } from "../helper";
import { checkParams } from "../../pyq-server/helper";
import { GenericCache } from "../../common/cacheUtil";
import * as QnmRoleInfos from '../../models/QNMRoleInfos'
import { QnmRoleInfoRecord } from "../../common/type";
import { getServerIdFromRoleId } from "../../common/util2";
import { getRootMergedServerId } from '../../service/qnm/server/list'

const TieBaNewServerIds = [2189, 2190, 2194, 2195]

async function isInNewServerId(roleId: number): Promise<boolean> {
    let serverId = getServerIdFromRoleId(roleId)
    serverId = await getRootMergedServerId(serverId)
    let newServerSet = new Set(TieBaNewServerIds)
    return newServerSet.has(serverId)
}

enum TieBaRetCode {
    SUCCESS = 0,
    PLAYER_NOT_FOUND = 1,
    NOT_VALID_SERVER = 2
}

export async function getPlayerInfo(req, res, next) {
    try {
        let schema = {
            roleid: { type: Number }
        }
        await checkParams(req.params, schema)
        let params = req.params as { roleid: number }
        let isIn = await isInNewServerId(params.roleid)
        if (!isIn) {
            return res.send({ code: TieBaRetCode.NOT_VALID_SERVER, msg: '查询账号非指定服务器!' })
        }
        let plc = new PlayerInfoCache()
        let record = await plc.get(params.roleid)
        if (record) {
            let level = record.Level || 0
            res.send({ code: TieBaRetCode.SUCCESS, data: { roleid: params.roleid, level: level } })
        } else {
            res.send({ code: TieBaRetCode.PLAYER_NOT_FOUND, msg: '查询角色id未找到' })
        }
    } catch (err) {
        errorHandler(err, req, res, next)
    }
}

class PlayerInfoCache extends GenericCache<number, QnmRoleInfoRecord> {
    getExpireTime() {
        return 1 * 60
    }


    getKey(roleId: number): string {
        return `audit:qnm_tieba:player_info:${roleId}`
    }

    async fetchDataSource(roleId: number): Promise<QnmRoleInfoRecord> {
        let record = await QnmRoleInfos.findOne({ RoleId: roleId }, ['*'])
        return record || null
    }
}