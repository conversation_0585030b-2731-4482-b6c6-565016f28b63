"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.returnPic = void 0;
const helper_1 = require("../helper");
const mediaAudit_1 = require("../services/mediaAudit");
function returnPic(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        const schema = {
            product: { type: String },
            pic_list: { type: Array },
        };
        try {
            yield (0, helper_1.checkParams)(req.params, schema);
            const params = req.params;
            const ret = yield (0, mediaAudit_1.handlePicAudit)(params);
            res.send({ code: 0, data: ret });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.returnPic = returnPic;
//# sourceMappingURL=audit.js.map