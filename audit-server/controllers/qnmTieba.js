"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getPlayerInfo = void 0;
const helper_1 = require("../helper");
const helper_2 = require("../../pyq-server/helper");
const cacheUtil_1 = require("../../common/cacheUtil");
const QnmRoleInfos = require("../../models/QNMRoleInfos");
const util2_1 = require("../../common/util2");
const list_1 = require("../../service/qnm/server/list");
const TieBaNewServerIds = [2189, 2190, 2194, 2195];
function isInNewServerId(roleId) {
    return __awaiter(this, void 0, void 0, function* () {
        let serverId = (0, util2_1.getServerIdFromRoleId)(roleId);
        serverId = yield (0, list_1.getRootMergedServerId)(serverId);
        let newServerSet = new Set(TieBaNewServerIds);
        return newServerSet.has(serverId);
    });
}
var TieBaRetCode;
(function (TieBaRetCode) {
    TieBaRetCode[TieBaRetCode["SUCCESS"] = 0] = "SUCCESS";
    TieBaRetCode[TieBaRetCode["PLAYER_NOT_FOUND"] = 1] = "PLAYER_NOT_FOUND";
    TieBaRetCode[TieBaRetCode["NOT_VALID_SERVER"] = 2] = "NOT_VALID_SERVER";
})(TieBaRetCode || (TieBaRetCode = {}));
function getPlayerInfo(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            let schema = {
                roleid: { type: Number }
            };
            yield (0, helper_2.checkParams)(req.params, schema);
            let params = req.params;
            let isIn = yield isInNewServerId(params.roleid);
            if (!isIn) {
                return res.send({ code: TieBaRetCode.NOT_VALID_SERVER, msg: '查询账号非指定服务器!' });
            }
            let plc = new PlayerInfoCache();
            let record = yield plc.get(params.roleid);
            if (record) {
                let level = record.Level || 0;
                res.send({ code: TieBaRetCode.SUCCESS, data: { roleid: params.roleid, level: level } });
            }
            else {
                res.send({ code: TieBaRetCode.PLAYER_NOT_FOUND, msg: '查询角色id未找到' });
            }
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.getPlayerInfo = getPlayerInfo;
class PlayerInfoCache extends cacheUtil_1.GenericCache {
    getExpireTime() {
        return 1 * 60;
    }
    getKey(roleId) {
        return `audit:qnm_tieba:player_info:${roleId}`;
    }
    fetchDataSource(roleId) {
        return __awaiter(this, void 0, void 0, function* () {
            let record = yield QnmRoleInfos.findOne({ RoleId: roleId }, ['*']);
            return record || null;
        });
    }
}
//# sourceMappingURL=qnmTieba.js.map