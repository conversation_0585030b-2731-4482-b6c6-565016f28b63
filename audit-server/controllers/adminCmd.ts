import { ICmdResult, CmdCollection, ICmdParams, CmdStatus } from "./adminTypes";
import * as ParamsValidator from "../../common/paramsValidator";
import * as QnmRoleInfo from "../../models/QNMRoleInfos";
import * as PyqProfile from "../../models/PyqProfile";
import * as PyqMoment from "../../models/PyqMoments";
import * as PyqComment from "../../models/PyqComment";
import * as PyqMomentForward from "../../models/PyqMomentForward";
import * as MomentService from "../../service/qnm/pyq/moment";
import * as CommentService from "../../pyq-server/services/comment";
import HotMomentService = require("../../service/qnm/pyq/HotMomentsCache");
import * as _ from "lodash";
import * as nos from "../../common/nos";
import { PyqDelayQueue, DELAY_TOPICS } from "../../cron-jobs/delayTasks";
import { PyqMomentRecord } from "../../models/modelInterface";
import { fillCsvStr } from "../../common/util2";
import * as bluebird from "bluebird";
import { checkIsOfficialId } from "../data/officialRoles";

const MomentCols = ["ID", "RoleId", "Text", "ImgList", "CreateTime", "HotState"];

interface IGetProfile extends ICmdParams {
  roleId: number;
}

interface IUpdatePlayer extends ICmdParams {
  roleId: number;
  avatar: string;
  roleName: string;
  location: string;
  signature: string;
}

interface IListMoments extends ICmdParams {
  roleId: number;
  lastId: number;
  limit: number;
}

interface IDelMoment extends ICmdParams {
  roleId: number;
  momentId: number;
}

interface IAddComment extends ICmdParams {
  roleId: number;
  momentId: number;
  text: string;
}

interface IDelComment extends ICmdParams {
  roleId: number;
  commentId: number;
}

interface IReplyComment extends ICmdParams {
  roleId: number;
  commentId: number;
  text: string;
}

enum TopToTypes {
  LOCAL_SERVER = 1,
  ALL_SERVER = 2,
}

interface IAddMoment extends ICmdParams {
  roleId: number;
  imgs?: string[];
  videos?: string[];
  videoCovers?: string[];
  text: string;
  topTo?: TopToTypes;
  topAt?: number;
}

interface ILikeMoment extends ICmdParams {
  roleId: number;
  momentId: number;
}

interface IForwardMoment extends ICmdParams {
  roleId: number;
  momentId: number;
  text: string;
}

async function checkParams(params, schema) {
  const validator = ParamsValidator.from(params);
  for (const k of Object.keys(schema)) {
    validator.param(k, schema[k]);
  }
  try {
    await validator.validate();
  } catch (err) {
    const message = JSON.stringify(err);
    throw { message: message };
  }
}

async function updatePlayerInfo(params: IUpdatePlayer): Promise<ICmdResult> {
  const schema = {
    roleId: { type: Number },
    avatar: { type: String, required: false },
    roleName: { type: String, required: false },
    location: { type: String, required: false },
    signature: { type: String, required: false },
  };
  await checkParams(params, schema);

  // 处理pyq_profile
  const now = Date.now();
  const profileProps = {};
  if (params.avatar) {
    profileProps["Photo"] = params.avatar;
    profileProps["PhotoAudit"] = 1;
  }
  if (params.location) {
    profileProps["Location"] = params.location;
  }
  if (params.signature) {
    profileProps["Signature"] = params.signature;
  }
  if (!_.isEmpty(profileProps)) {
    profileProps["UpdateTime"] = now;
    await PyqProfile.updateByCondition({ RoleId: params.roleId }, profileProps);
  }

  if (params.roleName) {
    const roleProps = { RoleName: params.roleName, UpdateTime: now };
    await QnmRoleInfo.updateByCondition({ RoleId: params.roleId }, roleProps);
  }
  const result = await getProfile(params.roleId);

  return { status: CmdStatus.OK, result: result };
}

async function officialRoleIdChecker(roleId: number) {
  if (checkIsOfficialId(roleId)) {
    return true;
  } else {
    return bluebird.reject({ message: "roleid is not official id" });
  }
}

interface IProfile {
  RoleId: number;
  RoleName: string;
  Avatar: string;
  Location: string;
  Signature: string;
}

async function getProfile(roleId: number): Promise<PyqProfile> {
  const profile = await PyqProfile.findOne({ RoleId: roleId }, ["RoleId", "Photo as Avatar", "Location", "Signature"]);
  const record = await QnmRoleInfo.findOne({ RoleId: roleId }, ["RoleName"]);
  if (profile && record) {
    const result: IProfile = Object.assign({}, profile, record);
    return result;
  } else {
    return bluebird.reject({ errorType: "DataNotFound", message: "角色信息未找到" });
  }
}

async function getPlayerInfo(params: IGetProfile) {
  const schema = {
    roleId: { type: Number },
  };
  await checkParams(params, schema);
  await officialRoleIdChecker(params.roleId);
  const profile = await getProfile(params.roleId);
  return { status: CmdStatus.OK, result: profile };
}

async function listMoments(params: IListMoments) {
  const schema = {
    roleId: { type: Number },
    lastId: { type: Number, required: false },
    limit: { type: Number, required: false, min: 1, default: 10, max: 20 },
  };
  await checkParams(params, schema);
  await officialRoleIdChecker(params.roleId);

  let query = PyqMoment.normalScope()
    .select(MomentCols)
    .whereIn("RoleId", params.roleId)
    .orderBy("ID", "desc")
    .limit(params.limit);
  if (params.lastId) {
    query = query.where("ID", "<", params.lastId);
  }
  const rows = (await PyqMoment.executeByQuery(query)) as RawMoment[];

  const list: MomentItem[] = await formatMomentList(rows);
  return { status: CmdStatus.OK, result: { list: list } };
}

async function delMoment(params: IDelMoment) {
  const schema = {
    roleId: { type: Number },
    momentId: { type: Number },
  };
  await checkParams(params, schema);
  await officialRoleIdChecker(params.roleId);
  const info = await PyqMoment.updateByCondition({ ID: params.momentId, RoleId: params.roleId }, { Status: -1 });
  return { status: CmdStatus.OK, result: { message: info.message } };
}

async function addMoment(params: IAddMoment) {
  const MAX_IMAGE_SIZE = 6;
  const schema = {
    roleId: { type: Number },
    imgs: { type: Array, required: false, default: [] },
    videos: { type: Array, required: false, default: [] },
    videoCovers: { type: Array, required: false, default: [] },
    text: { type: String, required: false, default: "" },
    topAt: { type: Number, required: false },
    topTo: { type: Number, required: false, values: [TopToTypes.ALL_SERVER, TopToTypes.LOCAL_SERVER] },
  };
  await checkParams(params, schema);

  // insert moment
  const roleId = params.roleId;
  const now = Date.now();
  params.imgs = _.take(params.imgs, MAX_IMAGE_SIZE);

  const imgList = params.imgs.join(",");
  const imgAudit = fillCsvStr("1", params.imgs.length);

  let videos = params.videos || [];
  videos = videos.map((r) => r.replace("https://hi-163-qnm.nosdn.127.net/", "http://nos.netease.com/hi-163-qnm/"));
  const videoList = videos.join(",");
  const videoAudit = fillCsvStr("1", params.videos.length);

  const newVideoCovers = [];
  const covers = params.videoCovers || [];

  for (let i = 0; i < videos.length; i++) {
    const videoFrameCover = videos[i] + "?vframe";
    const cover = covers[i] || videoFrameCover;
    newVideoCovers.push(cover);
  }

  const videoCoverList = newVideoCovers.join(",");
  const videoCoverAudit = fillCsvStr("1", newVideoCovers.length);

  const props: Partial<PyqMomentRecord> = {
    RoleId: params.roleId,
    ImgList: imgList,
    ImgAudit: imgAudit,
    VideoList: videoList,
    VideoAudit: videoAudit,
    VideoCoverList: videoCoverList,
    VideoCoverAudit: videoCoverAudit,
    Text: params.text,
    CreateTime: now,
  };

  const insertInfo = await PyqMoment.insert(props);
  const insertId: number = insertInfo.insertId;

  if (params.topTo === TopToTypes.ALL_SERVER) {
    await HotMomentService.upMomentForRoleId(roleId, insertId, "all", null);

    await PyqDelayQueue.addJob({
      id: "" + insertId,
      topic: DELAY_TOPICS.DownHotMomentTopic,
      body: { momentId: insertId },
      runAt: params.topAt,
    });
  } else {
    const record = await QnmRoleInfo.findOne({ RoleId: params.roleId }, ["ServerId"]);
    if (record && record.ServerId) {
      await HotMomentService.upMomentForRoleId(roleId, insertId, record.ServerId, null);

      const body = { momentId: insertId, serverId: record.serverId };
      await PyqDelayQueue.addJob({
        id: "" + insertId,
        topic: DELAY_TOPICS.DownHotMomentTopic,
        body: body,
        runAt: params.topAt,
      });
    }
  }

  return { status: CmdStatus.OK, result: { id: insertId } };
}

async function likeMoment(params: ILikeMoment) {
  const schema = {
    roleId: { type: Number },
    momentId: { type: Number },
  };
  await checkParams(params, schema);
  await officialRoleIdChecker(params.roleId);
  await MomentService.likeMoment({ params: { roleid: params.roleId, id: params.momentId } });
  return { status: CmdStatus.OK, result: null };
}

async function cancelLikeMoment(params: ILikeMoment) {
  const schema = {
    roleId: { type: Number },
    momentId: { type: Number },
  };
  await checkParams(params, schema);
  await officialRoleIdChecker(params.roleId);
  await MomentService.likeMoment({ params: { roleid: params.roleId, id: params.momentId, action: "undo" } });
  return { status: CmdStatus.OK, result: null };
}

async function forwardMoment(params: IForwardMoment) {
  const schema = {
    roleId: { type: Number },
    momentId: { type: Number },
    text: { type: String },
  };
  await checkParams(params, schema);
  await officialRoleIdChecker(params.roleId);
  const info = await PyqMomentForward.forwardMoment(params.roleId, params.momentId, params.text);
  return { status: CmdStatus.OK, result: info };
}

interface IPagination {
  page: number;
  pageSize: number;
}

interface MomentItem {
  ID: number;
  RoleId: number;
  RoleName: string;
  ImgList: string[];
  Text: string;
  CreateTime: number;
  LikeCount: number;
  CommentCount: number;
}

interface RawMoment {
  ID: number;
  RoleId: number;
  ImgList: string;
  Text: string;
  HotState: string;
  CreateTime: number;
}

async function getRoleNameMap(roleIds: number[]) {
  const roleInfos = (await QnmRoleInfo.find({ RoleId: roleIds }, { cols: ["RoleId", "RoleName"] })) as {
    RoleId: number;
    RoleName: string;
  }[];
  const roleNameMap: Map<number, string> = new Map();
  for (const r of roleInfos) {
    roleNameMap.set(r.RoleId, r.RoleName);
  }
  return roleNameMap;
}

async function formatMomentList(list: RawMoment[]): Promise<MomentItem[]> {
  const roleIds = _.uniq(list.map((x) => x.RoleId));
  const roleNameMap: Map<number, string> = await getRoleNameMap(roleIds);
  const result: MomentItem[] = [];

  for (const e of list) {
    const roleName = roleNameMap.get(e.RoleId) || "";
    const imgList = (e.ImgList || "").split(",").filter((s) => !!s);
    const hotState = PyqMoment.formatHotState(e.HotState);
    const likeCount = hotState.like;
    const commentCount = hotState.reply + hotState.comment;
    const item: MomentItem = {
      ID: e.ID,
      RoleId: e.RoleId,
      RoleName: roleName,
      ImgList: imgList,
      Text: e.Text,
      CreateTime: e.CreateTime,
      LikeCount: likeCount,
      CommentCount: commentCount,
    };
    result.push(item);
  }
  return result;
}

async function listHotMoments(params: IPagination) {
  const schema = {
    page: { type: Number, required: false, default: 1 },
    pageSize: { type: Number, required: false, default: 10, max: 20, min: 1 },
  };
  await checkParams(params, schema);
  let moments = (await HotMomentService.getMoments("all", null)) as { ID: number }[];
  const start = (params.page - 1) * params.pageSize;
  const end = start + params.pageSize;
  moments = moments.slice(start, end);
  const mIds = _.uniq(moments.map((e) => e.ID));
  const rows = (await PyqMoment.findByIds(mIds, MomentCols)) as RawMoment[];
  const list: MomentItem[] = await formatMomentList(rows);
  return { status: CmdStatus.OK, result: { list: list } };
}

async function addComment(params: IAddComment) {
  const schema = {
    roleId: { type: Number },
    momentId: { type: Number },
    text: { type: String },
  };
  await checkParams(params, schema);
  await officialRoleIdChecker(params.roleId);
  const result = await CommentService.addComment({ id: params.momentId, roleid: params.roleId, text: params.text });
  return { status: CmdStatus.OK, result: result };
}

async function delComment(params: IDelComment) {
  const schema = {
    roleId: { type: Number },
    commentId: { type: Number },
  };
  await checkParams(params, schema);
  const comment = await PyqComment.findOne({ ID: params.commentId, RoleId: params.roleId, Status: 0 }, ["ID"]);
  if (!comment) {
    return { status: CmdStatus.Fail, result: { message: "评论不存在" } };
  } else {
    await MomentService.delComment({ id: params.commentId, roleid: params.roleId });
    return { status: CmdStatus.OK, result: null };
  }
}

async function replyComment(params: IReplyComment) {
  const schema = {
    roleId: { type: Number },
    commentId: { type: Number },
    text: { type: String },
  };
  await checkParams(params, schema);
  await officialRoleIdChecker(params.roleId);
  const comment = (await PyqComment.findOne({ ID: params.commentId }, ["TargetId as MomentId", "RoleId"])) as {
    MomentId: number;
    RoleId: number;
  };
  if (!comment) {
    return { status: CmdStatus.Fail, result: "找不到对应的评论Id" };
  } else {
    const result = await CommentService.addComment({
      id: comment.MomentId,
      roleid: params.roleId,
      replyid: comment.RoleId,
      text: params.text,
    });
    return { status: CmdStatus.OK, result: result };
  }
}

interface IListComments {
  momentId: number;
  lastId: number;
  limit: number;
}

interface CommentItem {
  ID: number;
  RoleId: number;
  RoleName: string;
  Text: string;
  CreateTime: number;
}

async function listComments(params: IListComments) {
  const schema = {
    momentId: { type: Number },
    lastId: { type: Number, required: false },
    limit: { type: Number, required: false, min: 1, default: 10, max: 20 },
  };
  await checkParams(params, schema);
  const cols = ["ID", "RoleId", "Text", "CreateTime"];
  let query = PyqComment.normalScope().select(cols).where("TargetId", params.momentId).limit(params.limit);
  if (params.lastId) {
    query = query.where("ID", "<", params.lastId);
  }
  const rows = (await PyqComment.executeByQuery(query)) as {
    ID: number;
    RoleId: number;
    Text: string;
    CreateTime: number;
  }[];
  const roleIds = _.uniq(rows.map((x) => x.RoleId));
  const roleNameMap = await getRoleNameMap(roleIds);
  const list: CommentItem[] = [];
  for (const r of rows) {
    const roleName = roleNameMap.get(r.RoleId);
    const ele: CommentItem = { ID: r.ID, RoleId: r.RoleId, RoleName: roleName, Text: r.Text, CreateTime: r.CreateTime };
    list.push(ele);
  }
  return { status: CmdStatus.OK, result: { list: list } };
}

async function getNosToken(params) {
  const result = nos.getToken("qnm", params);
  return { status: CmdStatus.OK, result: result };
}

export const cmdHandlers: CmdCollection = {
  updatePlayerInfo: updatePlayerInfo,
  getPlayerInfo: getPlayerInfo,
  listMoments: listMoments,
  delMoment: delMoment,
  addMoment: addMoment,
  addComment: addComment,
  delComment: delComment,
  replyComment: replyComment,
  likeMoment: likeMoment,
  cancelLikeMoment: cancelLikeMoment,
  forwardMoment: forwardMoment,
  listHotMoments: listHotMoments,
  listComments: listComments,
  getNosToken: getNosToken,
};
