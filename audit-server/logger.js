"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.clazzLogger = exports.sendPicLogger = exports.returnPicLogger = exports.cmdLogger = exports.apiLogger = void 0;
const logger2_1 = require("../common/logger2");
exports.apiLogger = (0, logger2_1.getLogger)('audit_server');
exports.cmdLogger = (0, logger2_1.getLogger)('audit_cmd');
exports.returnPicLogger = (0, logger2_1.getLogger)('returnPic');
exports.sendPicLogger = (0, logger2_1.getLogger)('sendPic');
function clazzLogger(name) {
    return exports.apiLogger.child({ clazz: name });
}
exports.clazzLogger = clazzLogger;
//# sourceMappingURL=logger.js.map