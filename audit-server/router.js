"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.router = exports.auditIpLimiter = void 0;
const admin = require("./controllers/admin");
const batchCmd_1 = require("./controllers/batchCmd");
const QnmTiebaController = require("./controllers/qnmTieba");
const helper_1 = require("./helper");
const constants_1 = require("./constants");
const AuditController = require("./controllers/audit");
exports.auditIpLimiter = (0, helper_1.ipLimitChecker)({ allowIps: constants_1.auditIpWhiteList });
let tiebaIpLimiter = (0, helper_1.ipLimitChecker)({ allowIps: constants_1.qnmTiebaWhiteList });
function router(app) {
    // l10 梦岛热点后台
    app.post(constants_1.apiPrefix + '/l10_admin/:cmd', exports.auditIpLimiter, admin.dispatchCmd);
    app.get(constants_1.apiPrefix + '/l10_admin/:cmd', exports.auditIpLimiter, admin.dispatchCmd);
    app.post(constants_1.apiPrefix + '/batch_cmd', exports.auditIpLimiter, batchCmd_1.batchCmd);
    app.get(constants_1.apiPrefix + '/qnm_tieba/get_player_info', tiebaIpLimiter, QnmTiebaController.getPlayerInfo);
    // 图片审核结果
    app.post(constants_1.apiPrefix + '/return_pic', exports.auditIpLimiter, AuditController.returnPic);
}
exports.router = router;
//# sourceMappingURL=router.js.map