import { RoleInfos } from '../data/officialRoles'
import { OfficialAccount } from '../../models/PyqOfficialAccount'
import * as PyqProfile from '../../models/PyqProfile'
import * as QnmRoleInfos from '../../models/QNMRoleInfos'

async function main() {
    for (let r of RoleInfos) {
        await OfficialAccount.createOrUpdate({ RoleId: r.id, PublicStatus: 1, ForceFollow: 0, AutoBeHot: 1, CreateTime: Date.now() }, { RoleId: r.id })
        await PyqProfile.createOrUpdate({ RoleId: r.id, Signature: r.name, }, { RoleId: r.id })
        await QnmRoleInfos.createOrUpdate({ RoleId: r.id, RoleName: r.name, CreateTime: Date.now(), UpdateTime: Date.now() }, { RoleId: r.id, RoleName: r.name, UpdateTime: Date.now() })
    }
}

main().then(_ => {
    console.log('Done!')
    process.exit(0)
}).catch(err => {
    console.error(err)
})