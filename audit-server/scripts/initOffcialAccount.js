"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const officialRoles_1 = require("../data/officialRoles");
const PyqOfficialAccount_1 = require("../../models/PyqOfficialAccount");
const PyqProfile = require("../../models/PyqProfile");
const QnmRoleInfos = require("../../models/QNMRoleInfos");
function main() {
    return __awaiter(this, void 0, void 0, function* () {
        for (let r of officialRoles_1.RoleInfos) {
            yield PyqOfficialAccount_1.OfficialAccount.createOrUpdate({ RoleId: r.id, PublicStatus: 1, ForceFollow: 0, AutoBeHot: 1, CreateTime: Date.now() }, { RoleId: r.id });
            yield PyqProfile.createOrUpdate({ RoleId: r.id, Signature: r.name, }, { RoleId: r.id });
            yield QnmRoleInfos.createOrUpdate({ RoleId: r.id, RoleName: r.name, CreateTime: Date.now(), UpdateTime: Date.now() }, { RoleId: r.id, RoleName: r.name, UpdateTime: Date.now() });
        }
    });
}
main().then(_ => {
    console.log('Done!');
    process.exit(0);
}).catch(err => {
    console.error(err);
});
//# sourceMappingURL=initOffcialAccount.js.map