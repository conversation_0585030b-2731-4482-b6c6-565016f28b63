import * as restify from 'restify'
import * as admin from './controllers/admin'
import { batchCmd } from './controllers/batchCmd'
import * as QnmTiebaController from './controllers/qnmTieba'
import { ipLimitChecker } from './helper';
import { apiPrefix, qnmTiebaWhiteList, auditIpWhiteList } from './constants'
import * as AuditController from './controllers/audit'

export let auditIpLimiter = ipLimitChecker({ allowIps: auditIpWhiteList })

let tiebaIpLimiter = ipLimitChecker({ allowIps: qnmTiebaWhiteList })

export function router(app: restify.Server) {
  // l10 梦岛热点后台
  app.post(apiPrefix + '/l10_admin/:cmd', auditIpLimiter, admin.dispatchCmd)
  app.get(apiPrefix + '/l10_admin/:cmd', auditIpLimiter, admin.dispatchCmd)

  app.post(apiPrefix + '/batch_cmd', auditIpLimiter, batchCmd)

  app.get(apiPrefix + '/qnm_tieba/get_player_info', tiebaIpLimiter, QnmTiebaController.getPlayerInfo)

  // 图片审核结果
  app.post(apiPrefix + '/return_pic', auditIpLimiter, AuditController.returnPic)
}