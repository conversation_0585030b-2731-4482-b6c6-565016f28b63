// 上全服热门
POST {{auditApi}}/audit/batch_cmd
Content-Type: application/json

{
  "cmd": "uphotmoment",
  "playerId": 24056800001,
  "momentIds": 78740230
}
###

// 下全服热门
POST {{auditApi}}/audit/batch_cmd
Content-Type: application/json

{
  "cmd": "downhotmoment",
  "playerId": 24056800001,
  "momentIds": 78740230
}
###

// 获取心情
POST {{auditApi}}/audit/batch_cmd
Content-Type: application/json

{
  "cmd": "getmoment",
  "playerId": 7100001,
  "momentIds": 78740230
}
###

// 发表心情自动热门置顶
POST {{auditApi}}/audit/l10_admin/addMoment
Content-Type: application/json

{
  "roleId": 1005,
  "topAt": 1538032765471,
  "text": "测试设置热门3",
  "topTo": 2
}
###

// l10 官v发送视频 #topTo 1是本服置顶， 2是全服置顶
POST {{auditApi}}/audit/l10_admin/addMoment
Content-Type: application/json

{
  "roleId": 1005,
  "topAt": 1538032765471,
  "videos": ["http://hi-163-qnm.nosdn.127.net/photo/201610/05/1f95c1708ac411e6b044154363cb9e5a.mp4"],
  "videoCovers": ["http://hi-163-qnm.nosdn.127.net/photo/201610/05/1f95c1708ac411e6b044154363cb9e5a.png"],
  "text": "测试添加视频",
  "topTo": 2
}
###

// 发表心情自动热门置顶
POST {{auditApi}}/audit/l10_admin/addMoment?text=111&topTo=1&topAt=1551940695000&roleId=1005

###

// 列出热门心情列表
GET {{auditApi}}/audit/l10_admin/listHotMoments
###

// 话题状态
POST {{auditApi}}/audit/batch_cmd
Content-Type: application/json

{
  "cmd": "getTopicStat",
  "playerId": 7100001,
  "topic": "topic",
  "from": 0,
  "to": 1541731345652
}
###

// 心情状态
POST {{auditApi}}/audit/batch_cmd
Content-Type: application/json

{
  "cmd": "getMomentStat",
  "playerId": 7100001,
  "momentId": 832701,
  "from": 0,
  "to": 1541731345652
}
###

// 心情状态(新)
POST {{auditApi}}/audit/batch_cmd
Content-Type: application/json

{
  "cmd": "getMomentStatNew",
  "playerId": 7100001,
  "momentId": 24373783,
  "from": 0,
  "to": 1541731345652,
  "serverId":"17"
}
###

// 朋友圈转发明细
POST {{auditApi}}/audit/batch_cmd
Content-Type: application/json

{
  "cmd": "getForwardListByMomentId",
  "playerId": 7100001,
  "momentId": 24373783,
  "from": 0,
  "to": 1541731345652,
  "serverId":"17"
}
###

// 朋友圈评论明细
POST {{auditApi}}/audit/batch_cmd
Content-Type: application/json

{
  "cmd": "getCommentListByMmomentId",
  "playerId": 7100001,
  "momentId": 24373783,
  "from": 0,
  "to": 1541731345652,
  "serverId":"17"
}
###

// 朋友圈点赞明细
POST {{auditApi}}/audit/batch_cmd
Content-Type: application/json

{
  "cmd": "getLikeListByMomentId",
  "playerId": 7100001,
  "momentId": 24373783,
  "from": 0,
  "to": 1541731345652,
  "serverId":"17"
}
###

// 关注状态
POST {{auditApi}}/audit/batch_cmd
Content-Type: application/json

{
  "cmd": "getFollowStat",
  "playerId": 7100001,
  "from": 0,
  "to": 1541731345652
}
###

// getmoments
POST {{auditApi}}/audit/batch_cmd
Content-Type: application/json

{
  "cmd": "getmoment",
  "playerId": 7100001
}
###

// 增加心情热度
POST {{auditApi}}/audit/batch_cmd
Content-Type: application/json

{
  "cmd": "addmomenthot",
  "playerId": "5862301007",
  "serverId": "1007",
  "momentId": "91200356",
  "forward": "20",
  "like": "200"
}
###

// 清除心情热度
POST {{auditApi}}/audit/batch_cmd
Content-Type: application/json

{
  "cmd": "delmomenthot",
  "playerId": 115200011,
  "serverId": 1,
  "momentId": 1001
}
###

// 恢复被删除心情
POST {{auditApi}}/audit/batch_cmd
Content-Type: application/json

{
  "cmd": "recovermoment",
  "playerId": 100300001,
  "momentIds": "2;3"
}
###

// 禁止修改location
POST {{auditApi}}/audit/batch_cmd
Content-Type: application/json

{
  "cmd": "disablelocation",
  "playerIds": "1002",
  "duration": 500
}
###

// 允许修改location
POST {{auditApi}}/audit/batch_cmd
Content-Type: application/json

{
  "cmd": "enablelocation",
  "playerIds": "1002"
}
###

// 删除心情指令 
POST {{auditApi}}/audit/batch_cmd
Content-Type: application/json

{
  "cmd": "delmoment",
  "playerId": 100100001,
  "momentIds": "15938804113",
  "serverId": 0
}
###