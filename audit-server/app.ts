// 图片审校接口服务器
let config = require('../common/config')
let util = require('../common/util');


import { router, auditIpLimiter } from './router'
import * as restify from 'restify'
import { apiLogger } from './logger';

var app = restify.createServer({
    name: 'audit-server',
    version: '1.0.0'
});
app.use(restify.acceptParser(app.acceptable));
app.use(restify.queryParser());
app.use(restify.bodyParser());
app.use(function (req, res, next) {
    res.charSet('utf-8');
    if (config.testCfg.req_log) {
        apiLogger.info({ req: req, params: req.params }, 'RequestLog');
    }
    next();
});
app.on('uncaughtException', function (req, res, route, err) {
    apiLogger.error({ err: err })
    try { // 部分请求未等待异步操作完成，可能多次产生多个 uncaughtException
        res.send(500, { status: 'fail', result: err.message || err });
    } catch (ex) {
        //        logger.error(ex);
    }
});

const MdEventBus = require('../md-server/eventBus')
MdEventBus.registerAllListeners() // 注册所有listener

// 接收交易记录
app.post('/audit/transact', auditIpLimiter, function (req, res, next) {
    var audit = require('../common/audit');
    audit.addtransact(req.params);
    res.send(util.response('done'));
});


//提供给运营的接口，提供photo_id返回对应的url
app.get('/audit/get_pyq_photo', auditIpLimiter, function (req, res, next) {
    const PyqPhoto = require('../pyq-server/models/photo');
    const params = req.params;
    if (!params.photo_id) {
        res.send({ code: -1, msg: "参数photo_id不存在" })
    } else {
        return PyqPhoto.findOne({ ID: params.photo_id, Status: 0 }, ['Url'])
            .then(record => {
                if (!record) {
                    res.send({ code: -1, msg: "图片未找到" })
                } else {
                    res.send({ code: 0, data: record.Url });
                }
            }).catch(err => {
                res.send({ code: -1, msg: err.message || err });
            })
    }
});

var portArg = process.argv.splice(2)[0] || '';
var port = portArg.indexOf('-p') >= 0 ? parseInt(portArg.replace('-p', ''), 10) : 3590;

app.listen(port, function () {
    apiLogger.info(app.name, 'Start listening at %s:%d', app.url, port);
});

router(app)