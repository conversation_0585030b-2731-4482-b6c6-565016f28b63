DROP TABLE IF EXISTS `md_show_topic`;

CREATE TABLE `md_show_topic` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `SubjectId` bigint(20) NOT NULL COMMENT "炫耀贴主题",
  `UserId` bigint(20) NOT NULL,
  `Status` tinyint(4) DEFAULT 0 COMMENT '状态：0 正常 -1 已删除',
  `CreateTime` bigint(20) NOT NULL,
  `UpdateTime` bigint(20) NOT NULL,
  `ImgList` text COMMENT '图片列表',
  `ImgAuditList` text COMMENT '图片审核列表',
  `Title`  varchar(20),
  `Content` text,

  PRIMARY KEY (`ID`),
  INDEX `UserId` (`UserId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS `md_show_subject`;

CREATE TABLE `md_show_subject` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `Status` tinyint(4) DEFAULT 0 COMMENT '状态：0 正常 -1 已删除',
  `CreateTime` bigint(20) NOT NULL,
  `UserId` bigint(20) NOT NULL,
  `Title`  varchar(20) NOT NULL,
  `Content` text,
  `ImgUrl` varchar(255),

  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
