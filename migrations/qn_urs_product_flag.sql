CREATE TABLE `urs_product_flag` (
  `ID` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `UserName` varchar(127) DEFAULT NULL COMMENT 'Urs用户名',
  `Product` varchar(255) NOT NULL DEFAULT '' COMMENT '标识产品名',
  `Flag` tinyint(4) NOT NULL COMMENT 'Flag的值',
  `UpdateTime` bigint(20) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `UserName` (`UserName`,`Product`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
