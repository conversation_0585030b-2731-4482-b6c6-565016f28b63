/* DROP TABLE IF EXISTS `md_photo_album`; */

CREATE TABLE `md_photo_album` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `Name` varchar(255) DEFAULT NULL,
  `Desc` varchar(255) DEFAULT NULL,
  `CoverUrl` varchar(255) DEFAULT NULL,
  `ChannelId` smallint(6) DEFAULT NULL COMMENT '0真人秀 1牛图 2壁纸 3精彩同人',
  `Status` tinyint(4) DEFAULT 0 COMMENT '状态：0 正常 -1 已删除',
  `Visibility` tinyint DEFAULT 0 COMMENT '相册可见性: 0 所有人可见 1 仅好友可见',
  `Type` tinyint NOT NULL DEFAULT 0 COMMENT '普通相册: 0, 默认相册: 1',
  `CreateTime` bigint(20) NOT NULL,
  `UpdateTime` bigint(20) NOT NULL,
  `LastAddPhotoTime` bigint(20) DEFAULT 0 COMMENT '最新添加照片的时间， 用于相册排序',
  `UserId` bigint(20) NOT NULL,
  PRIMARY KEY (`ID`),
  INDEX `UserId` (`UserId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

/* DROP TABLE IF EXISTS `md_photo`; */

CREATE TABLE `md_photo` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `Name` varchar(255) DEFAULT NULL,
  `Desc` varchar(255) DEFAULT NULL,
  `Url` varchar(255) NOT NULL,
  `Status` tinyint(4) DEFAULT '0' COMMENT '状态：0 正常 -1 已删除',
  `AuditStatus` tinyint(4) DEFAULT 0 COMMENT '状态：0 审核中 1审核通过 -1 审核拒绝',
  `PhotoAlbumID` bigint(20) NOT NULL,
  `CreateTime` bigint(20) NOT NULL,
  `UpdateTime` bigint(20) NOT NULL,
  PRIMARY KEY (`ID`),
  INDEX `PhotoAlbumID` (`PhotoAlbumID`),
  UNIQUE KEY Url (Url)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
