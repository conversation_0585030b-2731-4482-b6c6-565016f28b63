DROP TABLE IF EXISTS `md_reality_show`;

CREATE TABLE `md_reality_show` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `UserId` bigint(20) NOT NULL COMMENT '申请真人秀的UserId',
  `Avatar` varchar(255) NOT NULL COMMENT '审核真人秀的Avatar Url',
  `ApplyTime` bigint(20) NOT NULL COMMENT '申请时间',
  `AuditTime` bigint(20) DEFAULT NULL COMMENT '审核操作时间',
  `Auditor` varchar(50) DEFAULT NULL COMMENT '审核者的登陆邮箱',
  `AuditStatus` tinyint(4) NOT NULL DEFAULT '0' COMMENT '-1=>审核拒绝 0=>审核中 1=>审核通过 2=>首页推荐',
  `RejectType` tinyint(4) DEFAULT NULL COMMENT '拒绝通过的理由类型 0=>其他',
  `RejectDetail` varchar(200) DEFAULT NULL COMMENT '拒绝通过的理由描述',
  `Status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '数据状态 0=>正常 -1=>已删除',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `UserId_Avatar` (`UserId`, `Avatar`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
