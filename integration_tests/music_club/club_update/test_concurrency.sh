#!/bin/bash

# Concurrency test for music club update API
# This script sends multiple concurrent requests to test for race conditions

API_URL="http://localhost:9992/qnm/music_club/update"
CONTENT_TYPE="Content-Type: application/json"
TEST_DATA='{"managerRoleId":68333400011,"level":2,"musicClubId":300011,"name":"谁是歌手"}'

echo "Starting concurrency test for music club update API..."
echo "URL: $API_URL"
echo "Test data: $TEST_DATA"
echo "========================================="

# Function to send a single request
send_request() {
    local id=$1
    local result=$(curl -s -X POST "$API_URL" -H "$CONTENT_TYPE" -d "$TEST_DATA" 2>&1)
    echo "Request $id: $result"
}

# Test 1: Send 5 concurrent requests
echo "Test 1: Sending 5 concurrent requests..."
for i in {1..5}; do
    send_request $i &
done
wait

echo ""
echo "========================================="

# Test 2: Send 10 concurrent requests with slight variations
echo "Test 2: Sending 10 concurrent requests with level variations..."
for i in {1..10}; do
    level=$((i % 5 + 1))  # levels 1-5
    test_data='{"managerRoleId":68333400011,"level":'$level',"musicClubId":300011,"name":"测试乐团'$i'"}'
    (
        result=$(curl -s -X POST "$API_URL" -H "$CONTENT_TYPE" -d "$test_data" 2>&1)
        echo "Request $i (level $level): $result"
    ) &
done
wait

echo ""
echo "========================================="

# Test 3: Rapid fire test - 20 requests in quick succession
echo "Test 3: Rapid fire test - 20 requests in quick succession..."
for i in {1..20}; do
    (
        result=$(curl -s -X POST "$API_URL" -H "$CONTENT_TYPE" -d "$TEST_DATA" 2>&1)
        echo "Rapid $i: $result"
    ) &
    # Small delay to prevent overwhelming the server
    sleep 0.01
done
wait

echo ""
echo "========================================="

# Test 4: Stress test with different music club IDs
echo "Test 4: Stress test with different music club IDs..."
for i in {1..15}; do
    music_club_id=$((300011 + i % 3))  # Use IDs 300011, 300012, 300013
    test_data='{"managerRoleId":68333400011,"level":2,"musicClubId":'$music_club_id',"name":"压力测试'$i'"}'
    (
        result=$(curl -s -X POST "$API_URL" -H "$CONTENT_TYPE" -d "$test_data" 2>&1)
        echo "Stress $i (ID $music_club_id): $result"
    ) &
done
wait

echo ""
echo "========================================="
echo "Concurrency test completed!"
