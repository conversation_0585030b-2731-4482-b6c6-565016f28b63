#!/bin/bash

# Test script for strict parameter validation
# Tests various invalid parameter combinations

API_URL="http://localhost:9992/qnm/music_club/update"
CONTENT_TYPE="Content-Type: application/json"

echo "Testing strict parameter validation for music club update API..."
echo "========================================="

test_request() {
    local test_name="$1"
    local test_data="$2"
    local expected_result="$3"

    echo "Test: $test_name"
    echo "Data: $test_data"

    result=$(curl -s -X POST "$API_URL" -H "$CONTENT_TYPE" -d "$test_data" 2>&1)
    echo "Result: $result"

    if [[ "$result" == *"$expected_result"* ]]; then
        echo "✅ PASS"
    else
        echo "❌ FAIL - Expected: $expected_result"
    fi
    echo "---"
}

# Test 1: All invalid parameters (-1 values)
test_request "All invalid parameters" \
    '{"managerRoleId":-1,"level":-1,"musicClubId":300011,"name":""}' \
    "-20"

# Test 2: Level out of range (too high)
test_request "Level too high" \
    '{"managerRoleId":68333400011,"level":101,"musicClubId":300011,"name":"测试"}' \
    "-20"

# Test 3: Level out of range (zero)
test_request "Level zero" \
    '{"managerRoleId":68333400011,"level":0,"musicClubId":300011,"name":"测试"}' \
    "-20"

# Test 4: ManagerRoleId invalid (zero)
test_request "ManagerRoleId zero" \
    '{"managerRoleId":0,"level":2,"musicClubId":300011,"name":"测试"}' \
    "-20"

# Test 5: ManagerRoleId invalid (negative)
test_request "ManagerRoleId negative" \
    '{"managerRoleId":-1,"level":2,"musicClubId":300011,"name":"测试"}' \
    "-20"

# Test 6: Name too long (over 14 characters)
test_request "Name too long" \
    '{"managerRoleId":68333400011,"level":2,"musicClubId":300011,"name":"123456789012345678901234567890123456789012345678901"}' \
    "-20"

# Test 7: Empty name
test_request "Empty name" \
    '{"managerRoleId":68333400011,"level":2,"musicClubId":300011,"name":""}' \
    "-20"

# Test 8: Only whitespace name
test_request "Whitespace name" \
    '{"managerRoleId":68333400011,"level":2,"musicClubId":300011,"name":"   "}' \
    "40004"

# Test 9: No update parameters (only musicClubId)
test_request "No update parameters" \
    '{"musicClubId":300011}' \
    "40004"

# Test 10: Missing musicClubId
test_request "Missing musicClubId" \
    '{"level":2,"name":"测试"}' \
    "400"

echo "========================================="

echo "Testing valid parameters (should succeed)..."

# Test 11: Valid level only
test_request "Valid level only" \
    '{"musicClubId":300011,"level":5}' \
    "code\":0"

# Test 12: Valid managerRoleId only
test_request "Valid managerRoleId only" \
    '{"musicClubId":300011,"managerRoleId":68333400011}' \
    "code\":0"

# Test 13: Valid name only
test_request "Valid name only" \
    '{"musicClubId":300011,"name":"有效的乐团名称"}' \
    "code\":0"

# Test 14: All valid parameters
test_request "All valid parameters" \
    '{"musicClubId":300011,"level":3,"managerRoleId":68333400011,"name":"完整测试"}' \
    "code\":0"

# Test 15: Edge case - minimum valid values
test_request "Minimum valid values" \
    '{"musicClubId":300011,"level":1,"managerRoleId":1,"name":"A"}' \
    "code\":0"

# Test 16: Edge case - maximum valid values
test_request "Maximum valid values" \
    '{"musicClubId":300011,"level":100,"name":"这是一个长度刚好50个字符的乐团名称这是一个长度刚好50个字符的乐团名称"}' \
    "code\":0"

echo "========================================="
echo "Strict validation test completed!"
