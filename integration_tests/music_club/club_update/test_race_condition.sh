#!/bin/bash

# Focused race condition test for music club update API
# This script tests for potential race conditions and timing issues

API_URL="http://localhost:9992/qnm/music_club/update"
CONTENT_TYPE="Content-Type: application/json"

echo "Testing race conditions for music club update API..."
echo "========================================="

# Test 1: Multiple concurrent updates with the same exact data
echo "Test 1: 10 concurrent identical updates..."
TEST_DATA='{"managerRoleId":68333400011,"level":3,"musicClubId":300011,"name":"竞态测试"}'
for i in {1..10}; do
    (
        start_time=$(date +%s%N)
        result=$(curl -s -X POST "$API_URL" -H "$CONTENT_TYPE" -d "$TEST_DATA")
        end_time=$(date +%s%N)
        duration=$(( (end_time - start_time) / 1000000 ))
        echo "[$i] Duration: ${duration}ms | Result: $result"
    ) &
done
wait

echo ""
echo "========================================="

# Test 2: Concurrent updates with conflicting data (different levels)
echo "Test 2: Concurrent updates with conflicting levels..."
for i in {1..8}; do
    level=$((i % 4 + 1))
    test_data='{"managerRoleId":68333400011,"level":'$level',"musicClubId":300011,"name":"冲突测试'$i'"}'
    (
        start_time=$(date +%s%N)
        result=$(curl -s -X POST "$API_URL" -H "$CONTENT_TYPE" -d "$test_data")
        end_time=$(date +%s%N)
        duration=$(( (end_time - start_time) / 1000000 ))
        echo "[$i] Level $level | Duration: ${duration}ms | Result: $result"
    ) &
done
wait

echo ""
echo "========================================="

# Test 3: Very rapid succession (no delay)
echo "Test 3: Very rapid succession updates..."
for i in {1..15}; do
    test_data='{"managerRoleId":68333400011,"level":2,"musicClubId":300011,"name":"快速更新'$i'"}'
    (
        result=$(curl -s -X POST "$API_URL" -H "$CONTENT_TYPE" -d "$test_data")
        echo "[$i] $result"
    ) &
done
wait

echo ""
echo "========================================="

# Test 4: Test with -1 values (edge case)
echo "Test 4: Testing with -1 values (should be filtered out)..."
for i in {1..5}; do
    test_data='{"managerRoleId":68333400011,"level":-1,"musicClubId":300011,"name":"边界测试'$i'"}'
    (
        result=$(curl -s -X POST "$API_URL" -H "$CONTENT_TYPE" -d "$test_data")
        echo "[$i] Level -1 test: $result"
    ) &
done
wait

echo ""
echo "========================================="

# Test 5: Mixed valid and invalid requests
echo "Test 5: Mixed valid and invalid requests..."
for i in {1..6}; do
    if [ $((i % 2)) -eq 0 ]; then
        # Valid request
        test_data='{"managerRoleId":68333400011,"level":2,"musicClubId":300011,"name":"有效请求'$i'"}'
        type="VALID"
    else
        # Invalid music club ID
        test_data='{"managerRoleId":68333400011,"level":2,"musicClubId":999999,"name":"无效请求'$i'"}'
        type="INVALID"
    fi
    (
        result=$(curl -s -X POST "$API_URL" -H "$CONTENT_TYPE" -d "$test_data")
        echo "[$i] $type: $result"
    ) &
done
wait

echo ""
echo "========================================="
echo "Race condition test completed!"
echo "Summary:"
echo "- Check if all successful requests have different updateTime values"
echo "- Look for any database errors or race condition issues"
echo "- Verify that -1 values are properly filtered out"
