#!/bin/bash

# 乐团创建接口并发测试脚本
# 此脚本发送多个并发请求来测试竞态条件

API_URL="http://127.0.0.1:9992/qnm/music_club/create"
# API_URL="http://10.246.204.179:88/qnm/music_club/create"
CONTENT_TYPE="Content-Type: application/json"

echo "开始乐团创建接口并发测试..."
echo "测试接口: $API_URL"
echo "========================================="

# 统计变量
total_requests=0
success_count=0
error_count=0
conflict_count=0

# 分析响应结果的函数
analyze_response() {
    local response=$1
    local test_name=$2
    local request_id=$3

    total_requests=$((total_requests + 1))

    if [[ $response == *"\"code\":0"* ]]; then
        success_count=$((success_count + 1))
        echo "✅ $test_name $request_id: 成功创建"
    elif [[ $response == *"\"code\":10001"* ]]; then
        error_count=$((error_count + 1))
        echo "❌ $test_name $request_id: 参数错误"
    elif [[ $response == *"\"code\":10002"* ]]; then
        error_count=$((error_count + 1))
        echo "💥 $test_name $request_id: 数据库错误"
    elif [[ $response == *"\"code\""* ]]; then
        conflict_count=$((conflict_count + 1))
        echo "⚠️ $test_name $request_id: 其他错误"
    else
        error_count=$((error_count + 1))
        echo "🔥 $test_name $request_id: 网络错误或异常响应"
    fi
    echo "   响应详情: $response"
}

# 发送单个请求的函数
send_request() {
    local id=$1
    local music_club_id=$2
    local test_name=$3
    local test_data='{"managerRoleId":100500001,"serverId":1,"musicClubId":'$music_club_id',"name":"乐团'$id'"}'
    local result=$(curl -s -X POST "$API_URL" -H "$CONTENT_TYPE" -d "$test_data" 2>&1)
    analyze_response "$result" "$test_name" "$id"
}

# 测试1: 发送5个不同ID的并发请求
echo "📋 测试1: 不同乐团ID的并发创建测试"
echo "说明: 使用不同的乐团ID并发创建，预期全部成功"
echo "请求数量: 5个"
echo "乐团ID范围: 300001-300005"
echo "----------------------------------------"
for i in {1..5}; do
    music_club_id=$((300000 + i))
    send_request $i $music_club_id "测试1" &
done
wait

echo ""
echo "========================================="

# 测试2: 发送10个相同ID的并发请求（应该产生冲突）
echo "📋 测试2: 相同乐团ID的并发创建测试（冲突测试）"
echo "说明: 使用相同的乐团ID并发创建，预期只有一个成功，其他应该失败"
echo "请求数量: 10个"
echo "乐团ID: 300100（所有请求使用同一个ID）"
echo "----------------------------------------"
CONFLICT_ID=300100
for i in {1..10}; do
    (
        test_data='{"managerRoleId":100500001,"serverId":1,"musicClubId":'$CONFLICT_ID',"name":"冲突测试'$i'"}'
        result=$(curl -s -X POST "$API_URL" -H "$CONTENT_TYPE" -d "$test_data" 2>&1)
        analyze_response "$result" "冲突测试" "$i"
    ) &
done
wait

echo ""
echo "========================================="

# 测试3: 快速连续请求测试
echo "📋 测试3: 快速连续请求测试"
echo "说明: 快速连续发送请求，测试系统在高频请求下的表现"
echo "请求数量: 15个"
echo "乐团ID范围: 300201-300215"
echo "请求间隔: 0.01秒"
echo "----------------------------------------"
for i in {1..15}; do
    music_club_id=$((300200 + i))
    (
        test_data='{"managerRoleId":100500001,"serverId":1,"musicClubId":'$music_club_id',"name":"快速测试'$i'"}'
        result=$(curl -s -X POST "$API_URL" -H "$CONTENT_TYPE" -d "$test_data" 2>&1)
        analyze_response "$result" "快速测试" "$i"
    ) &
    # 小延迟防止压垮服务器
    sleep 0.01
done
wait

echo ""
echo "========================================="

# 测试4: 不同服务器ID的压力测试
echo "📋 测试4: 不同服务器ID的压力测试"
echo "说明: 使用不同的服务器ID和管理员角色ID进行压力测试"
echo "请求数量: 12个"
echo "乐团ID范围: 300301-300312"
echo "服务器ID: 1,2,3 循环使用"
echo "----------------------------------------"
for i in {1..12}; do
    music_club_id=$((300300 + i))
    server_id=$((i % 3 + 1))  # 使用服务器ID 1, 2, 3
    manager_role_id=$((100500000 + i))
    (
        test_data='{"managerRoleId":'$manager_role_id',"serverId":'$server_id',"musicClubId":'$music_club_id',"name":"压力测试'$i'"}'
        result=$(curl -s -X POST "$API_URL" -H "$CONTENT_TYPE" -d "$test_data" 2>&1)
        analyze_response "$result" "压力测试" "$i (服务器$server_id)"
    ) &
done
wait

echo ""
echo "========================================="

# 测试5: 重复创建尝试（测试数据库约束）
echo "📋 测试5: 重复创建尝试测试"
echo "说明: 测试数据库约束，使用相同ID进行重复创建"
echo "请求数量: 5个"
echo "乐团ID: 300500（所有请求使用同一个ID）"
echo "请求间隔: 0.05秒"
echo "----------------------------------------"
DUPLICATE_TEST_ID=300500
for i in {1..5}; do
    (
        test_data='{"managerRoleId":100500001,"serverId":1,"musicClubId":'$DUPLICATE_TEST_ID',"name":"重复测试'$i'"}'
        result=$(curl -s -X POST "$API_URL" -H "$CONTENT_TYPE" -d "$test_data" 2>&1)
        analyze_response "$result" "重复测试" "$i"
    ) &
    sleep 0.05  # 轻微延迟模拟真实世界的时序
done
wait

echo ""
echo "========================================="
echo "🎯 乐团创建并发测试完成！"
echo ""
echo "📊 测试结果统计:"
echo "总请求数: $total_requests"
echo "成功请求: $success_count"
echo "错误请求: $error_count"
echo "冲突请求: $conflict_count"
echo ""
echo "🔍 预期行为分析:"
echo "- 不同ID的请求应该成功"
echo "- 相同ID的请求应该显示适当的错误处理"
echo "- 不应该出现数据库死锁"
echo "- 响应时间应该合理"
echo ""
if [ $success_count -eq $total_requests ]; then
    echo "⚠️ 注意: 所有请求都成功了，这可能表明重复ID约束没有正确工作"
elif [ $error_count -gt 0 ] && [ $success_count -gt 0 ]; then
    echo "✅ 正常: 系统正确处理了成功和失败的情况"
else
    echo "❓ 需要检查: 请分析上述结果是否符合预期"
fi