/**
 * Simple test to check one flower sending request
 */

const http = require('http');

function makeRequest(roleId, targetId) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({
      roleid: roleId,
      targetid: targetId,
      token: "",
      skey: "CHEAT_SKEY_ONLY_FOR_TEST",
      type: 3, // SEND_FLOWER
      parameter:'21100031$以爱之名$1$200'
    });

    const options = {
      hostname: '127.0.0.1',
      port: 9992,
      path: '/qnm/addevent',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          data: data
        });
      });
    });

    req.on('error', (err) => {
      reject({
        error: err.message
      });
    });

    req.write(postData);
    req.end();
  });
}

async function testSingle() {
  console.log('Testing single flower send request...');
  
  try {
    const result = await makeRequest('26300001009', '35320700013');
    console.log('✅ SUCCESS');
    console.log('Status:', result.statusCode);
    console.log('Response:', result.data);
    
    // Check for database error in response
    try {
      const responseData = JSON.parse(result.data);
      if (responseData.code === 10002) {
        console.log('🎯 DATABASE ERROR (DEADLOCK) DETECTED IN RESPONSE!');
      } else if (responseData.code === 0) {
        console.log('✅ SUCCESS - No error');
      } else {
        console.log(`⚠️ Other error (code: ${responseData.code}):`, responseData.msg);
      }
    } catch (parseError) {
      console.log('Warning: Could not parse response as JSON');
    }
  } catch (error) {
    console.log('❌ ERROR:', error.error);
  }
}

testSingle().catch(console.error);