/**
 * Test concurrent flower sending to verify deadlock fix
 */

const http = require('http');

function makeRequest(roleId, targetId) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({
      roleid: roleId,
      targetid: targetId,
      token: "",
      skey: "CHEAT_SKEY_ONLY_FOR_TEST",
      type: 3, // SEND_FLOWER
      parameter:'21100031$以爱之名$1$200'
    });

    const options = {
      hostname: '127.0.0.1',
      port: 9992,
      path: '/qnm/addevent',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          data: data,
          roleId: roleId,
          targetId: targetId
        });
      });
    });

    req.on('error', (err) => {
      reject({
        error: err.message,
        roleId: roleId,
        targetId: targetId
      });
    });

    req.write(postData);
    req.end();
  });
}

async function testConcurrent() {
  console.log('Testing concurrent flower sending...');
  console.log('User A (26300001009) → User B (35320700013)');
  console.log('User B (35320700013) → User A (26300001009)');
  console.log('Sending concurrent requests...\n');

  const startTime = Date.now();

  try {
    // Send concurrent requests that should cause deadlock in the old version
    const results = await Promise.allSettled([
      makeRequest('26300001009', '35320700013'),
      makeRequest('35320700013', '26300001009')
    ]);

    const endTime = Date.now();
    console.log(`Requests completed in ${endTime - startTime}ms\n`);

    let deadlockCount = 0;
    let successCount = 0;

    results.forEach((result, index) => {
      const direction = index === 0 ? '26300001009 → 35320700013' : '35320700013 → 26300001009';

      if (result.status === 'fulfilled') {
        console.log(`✅ ${direction}: SUCCESS`);
        console.log(`   Status: ${result.value.statusCode}`);
        console.log(`   Response: ${result.value.data}`);
        
        // Check API response format {code: int, msg: data}
        try {
          const responseData = JSON.parse(result.value.data);
          if (responseData.code === 10002) {
            console.log('   🎯 DATABASE ERROR (DEADLOCK) DETECTED IN API RESPONSE!\n');
            deadlockCount++;
          } else if (responseData.code === 0) {
            console.log('   ✅ SUCCESS - No deadlock\n');
            successCount++;
          } else {
            console.log(`   ⚠️ Other error (code: ${responseData.code}): ${responseData.msg}\n`);
          }
        } catch (parseError) {
          console.log('   Warning: Could not parse response as JSON\n');
        }
      } else {
        console.log(`❌ ${direction}: ERROR`);
        console.log(`   Error: ${result.reason.error}`);
        console.log('');
      }
    });

    console.log('=== SUMMARY ===');
    console.log(`Total requests: 2`);
    console.log(`Successful: ${successCount}`);
    console.log(`Deadlocks: ${deadlockCount}`);
    
    if (deadlockCount === 0) {
      console.log('🎉 DEADLOCK ISSUE APPEARS TO BE FIXED!');
    } else {
      console.log('⚠️ Deadlocks still occurring');
    }

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

testConcurrent().catch(console.error);