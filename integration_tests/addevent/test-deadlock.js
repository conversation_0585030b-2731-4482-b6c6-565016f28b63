/**
 * Test script to reproduce the deadlock issue
 * This simulates two users sending flowers to each other concurrently
 */

const http = require('http');

// Configuration
const API_BASE = 'http://localhost:3000/pyq';
const USER_A = '26300001009';
const USER_B = '35320700013';

function makeRequest(roleId, targetId) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({
      roleid: roleId,
      targetid: targetId,
      token: "",
      skey: "CHEAT_SKEY_ONLY_FOR_TEST",
      type: 3, // SEND_FLOWER
      parameter:'21100031$以爱之名$1$200'
    });

    const options = {
      hostname: '127.0.0.1',
      port: 9992,
      path: '/qnm/addevent',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          data: data,
          roleId: roleId,
          targetId: targetId
        });
      });
    });

    req.on('error', (err) => {
      reject({
        error: err.message,
        roleId: roleId,
        targetId: targetId
      });
    });

    req.write(postData);
    req.end();
  });
}

async function reproduceDeadlock() {
  console.log('Starting deadlock reproduction test...');
  console.log(`User A (${USER_A}) → User B (${USER_B})`);
  console.log(`User B (${USER_B}) → User A (${USER_A})`);
  console.log('Sending concurrent requests...\n');

  const startTime = Date.now();

  try {
    // Send concurrent requests that should cause deadlock
    const results = await Promise.allSettled([
      makeRequest(USER_A, USER_B),
      makeRequest(USER_B, USER_A)
    ]);

    const endTime = Date.now();
    console.log(`Requests completed in ${endTime - startTime}ms\n`);

    results.forEach((result, index) => {
      const direction = index === 0 ? `${USER_A} → ${USER_B}` : `${USER_B} → ${USER_A}`;

      if (result.status === 'fulfilled') {
        console.log(`✅ ${direction}: SUCCESS`);
        console.log(`   Status: ${result.value.statusCode}`);
        console.log(`   Response: ${result.value.data}`);
        
        // Check API response format {code: int, msg: data}
        try {
          const responseData = JSON.parse(result.value.data);
          if (responseData.code === 10002) {
            console.log('   🎯 DATABASE ERROR (DEADLOCK) DETECTED IN API RESPONSE!\n');
          } else {
            console.log('');
          }
        } catch (parseError) {
          console.log('   Warning: Could not parse response as JSON\n');
        }
      } else {
        console.log(`❌ ${direction}: ERROR`);
        console.log(`   Error: ${result.reason.error}`);
        console.log('');
      }
    });

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run multiple iterations to increase chance of hitting deadlock
async function runTest() {
  console.log('🔄 Running deadlock reproduction test (10 iterations)...\n');

  for (let i = 1; i <= 10; i++) {
    console.log(`--- Iteration ${i} ---`);
    await reproduceDeadlock();

    // Small delay between iterations
    if (i < 10) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  console.log('Test completed. Check server logs for deadlock errors.');
}

runTest().catch(console.error);