#  送花并发死锁问题修复

## 变更概述

本文档记录了 refs #138778 任务中的两次重要提交，涉及数据库死锁问题的修复和事件服务的模块化重构。

## 提交信息

### 1. 重构提交 (96a98a83)
- **提交时间**: 2025-07-04 21:04:43
- **提交信息**: refactor: refs #138778 模块化拆分 event 服务及新增集成测试

### 2. 修复提交 (0953ecad)
- **提交时间**: 2025-07-04 21:25:59
- **提交信息**: fix: refs #138778 修复数据库死锁问题及集成测试检测逻辑

## 死锁问题分析

### 问题原因
在并发场景下，两个用户同时互相发送鲜花时，会出现数据库死锁：
- 用户A向用户B发送鲜花，需要更新用户A和用户B的profile记录
- 用户B向用户A发送鲜花，需要更新用户B和用户A的profile记录
- 两个事务以不同顺序获取数据库行锁，导致死锁

#### 死锁发生示意图

```mermaid
sequenceDiagram
    participant T1 as 事务1<br/>(用户A→用户B)
    participant DB as 数据库
    participant T2 as 事务2<br/>(用户B→用户A)
    
    Note over T1,T2: 并发执行开始
    
    T1->>DB: 锁定用户A的profile记录
    activate T1
    Note right of T1: 获取用户A锁成功
    
    T2->>DB: 锁定用户B的profile记录
    activate T2
    Note right of T2: 获取用户B锁成功
    
    T1->>DB: 请求锁定用户B的profile记录
    Note right of T1: 等待用户B锁...
    
    T2->>DB: 请求锁定用户A的profile记录
    Note right of T2: 等待用户A锁...
    
    Note over T1,T2: 💥 死锁发生！<br/>T1等待T2释放用户B锁<br/>T2等待T1释放用户A锁
    
    DB-->>T1: ❌ 死锁检测，回滚事务1
    deactivate T1
    DB-->>T2: ✅ 事务2继续执行
    deactivate T2
```

### 解决方案

#### 1. 新增防死锁函数
在 `service/qnm/pyq/events/common/processors.ts` 中新增了 `updateBothProfiles` 函数：

```typescript
// 按升序同时更新两个用户profile，避免死锁
export async function updateBothProfiles(
  senderId: number,
  senderUpdateVal: IUpdateValues,
  targetId: number,  
  targetUpdateVal: IUpdateValues,
  eventType: EventTypes,
  conn: any
): Promise<void> {
  logger.debug({ senderId, targetId, eventType }, "startBothProfilesUpdate");

  // 按RoleId升序更新，确保锁定顺序一致避免死锁
  if (senderId < targetId) {
    await updateSenderProfile(senderId, senderUpdateVal, eventType, conn);
    await updateTargetProfile(targetId, targetUpdateVal, eventType, conn);
  } else {
    await updateTargetProfile(targetId, targetUpdateVal, eventType, conn);
    await updateSenderProfile(senderId, senderUpdateVal, eventType, conn);
  }

  logger.debug({ senderId, targetId }, "bothProfilesUpdateCompleted");
}
```

#### 2. 修改送花事件处理逻辑
在 `service/qnm/pyq/events/flower.ts` 中，将原来的分别更新改为使用新的防死锁函数：

```typescript
// 原代码：
await updateTargetProfile(context.targetId, updateVal, EventTypes.SEND_FLOWER, conn);
await updateSenderProfile(context.roleId, updateSelfVal, EventTypes.SEND_FLOWER, conn);

// 新代码：
await updateBothProfiles(
  context.roleId, updateSelfVal,
  context.targetId, updateVal,
  EventTypes.SEND_FLOWER, conn
);
```

#### 3. 改进集成测试检测逻辑
更新集成测试的错误检测逻辑，使用具体错误码 `10002` 来识别数据库错误，而非依赖错误信息文本匹配。

#### 解决方案示意图
```mermaid
sequenceDiagram
    participant T1 as 事务1<br/>(用户A→用户B)
    participant DB as 数据库
    participant T2 as 事务2<br/>(用户B→用户A)
    
    Note over T1,T2: 使用升序锁定策略
    
    Note over T1: 比较RoleId: A(26300001009) < B(35320700013)<br/>按升序锁定: A → B
    Note over T2: 比较RoleId: B(35320700013) > A(26300001009)<br/>按升序锁定: A → B
    
    T1->>DB: 锁定用户A的profile记录
    activate T1
    Note right of T1: 获取用户A锁成功
    
    T2->>DB: 请求锁定用户A的profile记录
    Note right of T2: 等待用户A锁...
    
    T1->>DB: 锁定用户B的profile记录
    Note right of T1: 获取用户B锁成功
    
    T1->>DB: 更新用户A和用户B的数据
    T1->>DB: 提交事务，释放所有锁
    deactivate T1
    Note right of T1: ✅ 事务1完成
    
    T2->>DB: 获取用户A的profile记录锁
    activate T2
    Note right of T2: 现在可以获取用户A锁
    
    T2->>DB: 锁定用户B的profile记录
    Note right of T2: 获取用户B锁成功
    
    T2->>DB: 更新用户A和用户B的数据
    T2->>DB: 提交事务，释放所有锁
    deactivate T2
    Note right of T2: ✅ 事务2完成
    
    Note over T1,T2: 🎉 死锁已解决！<br/>所有事务按相同顺序获取锁
```

#### 修复前后对比

```mermaid
graph TB
    subgraph "修复前 - 容易发生死锁"
        A1[事务1: A→B发送鲜花] --> A2[锁定用户A]
        A2 --> A3[锁定用户B]
        
        B1[事务2: B→A发送鲜花] --> B2[锁定用户B]
        B2 --> B3[锁定用户A]
        
        A2 -.等待.-> B2
        B2 -.等待.-> A2
        
        style A2 fill:#ffcccc
        style B2 fill:#ffcccc
    end
    
    subgraph "修复后 - 避免死锁"
        C1[事务1: A→B发送鲜花] --> C2[比较RoleId: A < B]
        C2 --> C3[按升序锁定: A → B]
        
        D1[事务2: B→A发送鲜花] --> D2[比较RoleId: B > A]
        D2 --> D3[按升序锁定: A → B]
        
        C3 --> E1[统一锁定顺序]
        D3 --> E1
        E1 --> E2[串行执行，无死锁]
        
        style C3 fill:#ccffcc
        style D3 fill:#ccffcc
        style E2 fill:#ccffcc
    end
```

## 事件服务重构

### 重构目标
将原有的单一大文件 `service/qnm/pyq/event.ts` 拆分为多个专门的子模块，提高代码的可维护性和可扩展性。

### 重构内容

#### 1. 模块化拆分
原有的 `service/qnm/pyq/event.ts` (1126行) 拆分为以下模块：

```
service/qnm/pyq/events/
├── common/
│   ├── base.ts         # 基础类型定义和通用函数
│   ├── processors.ts   # 事件处理器
│   └── validators.ts   # 参数验证器
├── flower.ts          # 鲜花相关事件
├── gift.ts            # 礼物相关事件
├── popularity.ts      # 人气相关事件
└── visitSpaceTodayMarker.ts  # 访问空间标记事件
```

#### 2. 新增集成测试
创建了三个集成测试文件：
- `integration_tests/addevent/test-simple.js` - 单次请求测试
- `integration_tests/addevent/test-concurrent.js` - 并发请求测试
- `integration_tests/addevent/test-deadlock.js` - 死锁复现测试

#### 3. 接口更新
- 控制器 `pyq-server/controllers/events.ts` 中的 `addEvent` 函数改为异步
- 更新引用路径以使用新的模块化结构

### 其他修改
- 修复函数命名拼写错误：`getMergetFlowerId` → `getMergeFlowerId`
- 添加国际化支持，新增 `DatabaseError` 错误信息
- 禁用千影风华活动配置 (`qyfhCfg.enable: false`)
- 调整测试环境配置 (`skip_auth: true`)

## 影响的功能和接口

### 核心功能
1. **送花事件处理** - 主要修复目标，解决了死锁问题
2. **事件查询服务** - 接口保持兼容，内部实现重构
3. **集成测试** - 新增了完整的测试套件

### 受影响的接口
1. **POST /qnm/addevent** - 送花事件接口，修复死锁问题
2. **GET /qnm/events** - 事件查询接口，内部重构但接口兼容

### 受影响的服务
1. **定时任务服务** - 更新了 `syncGiftEventToDB` 函数的引用路径
2. **千影风华活动** - 活动被禁用，需要确认是否影响相关功能

## QA 回归测试清单

### 高优先级测试项
1. **送花功能测试**
   - [ ] 单用户发送鲜花功能正常
   - [ ] 双用户互相发送鲜花无死锁
   - [ ] 高并发发送鲜花稳定性测试
   - [ ] 鲜花数据统计正确性验证

2. **事件查询功能测试**
   - [ ] 获取鲜花事件列表
   - [ ] 获取人气事件列表
   - [ ] 获取礼物事件列表
   - [ ] 事件分页功能正常

3. **数据库事务测试**
   - [ ] 发送鲜花事务完整性
   - [ ] 并发场景下数据一致性
   - [ ] 事务回滚机制正常

### 中优先级测试项
4. **集成测试验证**
   - [ ] 运行 `test-simple.js` 测试通过
   - [ ] 运行 `test-concurrent.js` 测试通过
   - [ ] 运行 `test-deadlock.js` 确认死锁已修复

5. **定时任务测试**
   - [ ] 鲜花事件同步任务正常
   - [ ] 周榜更新任务正常
   - [ ] 热门动态刷新任务正常

### 低优先级测试项
6. **千影风华活动测试**
   - [ ] 确认活动禁用对现有功能无影响
   - [ ] 验证活动相关接口返回正确状态

7. **国际化功能测试**
   - [ ] 数据库错误信息多语言显示正确
   - [ ] 各语言环境下错误码正确

8. **性能测试**
   - [ ] 事件处理性能对比重构前
   - [ ] 内存使用情况监控
   - [ ] 数据库连接池使用情况

### 测试环境要求
- 测试数据库需要包含足够的用户数据用于并发测试
- 建议使用压力测试工具模拟高并发场景
- 监控数据库死锁日志，确认问题完全解决

## 风险评估

### 低风险项
- 事件查询接口保持向后兼容
- 核心业务逻辑未发生变化
- 添加了完整的集成测试保障

### 中风险项
- 大规模代码重构可能引入新的问题
- 定时任务引用路径变更需要验证
- 千影风华活动禁用可能影响部分用户体验

### 高风险项
- 数据库事务处理逻辑变更，需要充分测试
- 并发场景下的数据一致性需要重点验证

## 部署建议

1. **灰度发布**：建议先在测试环境充分验证后，再进行生产环境的灰度发布
2. **监控告警**：重点监控数据库死锁日志和事件处理失败率
3. **回滚准备**：准备快速回滚方案，以防发现严重问题
4. **性能监控**：关注事件处理性能和数据库连接情况

## 总结

本次修复成功解决了数据库死锁问题，并通过模块化重构提高了代码的可维护性。修复采用了经典的"锁顺序一致化"解决方案，通过按 RoleId 升序更新来避免死锁。同时，完整的集成测试保障了修复的有效性。

重构将单一大文件拆分为多个专门模块，符合单一职责原则，为后续功能扩展打下了良好基础。

建议QA团队重点关注送花功能的并发测试和事件查询功能的完整性测试。

**注意**: 本次修改仅涉及送花事件和相关服务模块化，关注功能本身没有变动，关注相关的业务逻辑和接口保持不变。