接口地址：
	http://54.222.201.78/audit/batch_cmd

======= 手游空间部分 =======
【查看玩家个人签名】
	输入部分参数说明:
		cmd:getsign
		playerIds:批量的玩家ID,ID之间以分号隔开,请不要同时输入大量ID,可能会卡服务器
		serverId:目标服务器,数字类型
		
	输出部分说明:
		成功输出
			{"status":"OK","result":[{"RoleId":100100001,"Signature":"test"},{"RoleId":100200001,"Signature":"signature111"}]}
		失败输出
			{"status":"fail","result":失败原因}
			
【分页查看玩家发的朋友圈状态】
	输入部分参数说明:
		cmd:getmoment
		page:查询页数
		pageSize:查询数量
		playerId:玩家ID,数字类型
		serverId:目标服务器,数字类型
		
	输出部分说明:
		成功输出
			{"status":"OK","result":[{"ID":2,"RoleId":100200001,"Text":"这是一条除自己外不可以评论的动态","ImgList":"http://hi-163-qnm.nosdn.127.net/photo/201606/06/433dd6202bc011e6b331db1ee3b655d2.png,http://hi-163-qnm.nosdn.127.net/upload/201605/30/9b975ce0263111e69704510ece65ff48.jpg","VideoList":null,"ZanList":"1001,100200001,108700015","CreateTime":1462515507245,"Status":-1,"ImgAudit":"1,1"}]}
		失败输出
			{"status":"fail","result":失败原因}
		
【分页查看玩家留言】
	输入部分参数说明:
		cmd:getmessage
		page:查询页数
		pageSize:查询数量
		playerId:玩家ID,数字类型
		serverId:目标服务器,数字类型
		
	输出部分说明:
		成功输出
			{"status":"OK","result":[{"ID":1,"RoleId":100200001,"TargetId":100200001,"ReplyId":100300001,"Text":"留言留言","CreateTime":1466090437602,"Status":0}]}
		失败输出
			{"status":"fail","result":失败原因}
		
【删除玩家头像】
	输入部分参数说明:
		cmd:delphoto
		playerIds:批量的玩家ID,ID之间以分号隔开,请不要同时输入大量ID,可能会卡服务器
		serverId:目标服务器,数字类型
		
	输出部分说明:
		成功输出
			{"status":"OK"}
		失败输出
			{"status":"fail","result":失败原因}
		
【删除玩家个人签名】
	输入部分参数说明:
		cmd:delsign
		playerIds:批量的玩家ID,ID之间以分号隔开,请不要同时输入大量ID,可能会卡服务器
		serverId:目标服务器,数字类型
		
	输出部分说明:
		成功输出
			{"status":"OK"}
		失败输出
			{"status":"fail","result":失败原因}
		
【删除玩家朋友圈状态】
	输入部分参数说明:
		cmd:delmoment
		momentIds:批量的状态ID,ID之间以分号隔开,请不要同时输入大量ID。若传all,则删除指定玩家的所有状态
		playerId:玩家ID,数字类型
		serverId:目标服务器,数字类型
		
	输出部分说明:
		成功输出
			{"status":"OK"}
		失败输出
			{"status":"fail","result":失败原因}
		
【删除玩家空间留言】
	输入部分参数说明:
		cmd:delmessage
		messageIds:批量的留言ID,ID之间以分号隔开,请不要同时输入大量ID。若传all,则删除指定玩家的所有留言
		playerId:玩家ID,数字类型
		serverId:目标服务器,数字类型
		
	输出部分说明:
		成功输出
			{"status":"OK"}
		失败输出
			{"status":"fail","result":失败原因}
			
【删除玩家状态评论】
	输入部分参数说明:
		cmd:delcomment
		commentIds:批量的状态ID,ID之间以分号隔开,请不要同时输入大量ID。若传all,则删除指定玩家的所有留言
		playerId:玩家ID,数字类型
		targetId:评论的玩家ID,数字类型
		serverId:目标服务器,数字类型
		
	输出部分说明:
		成功输出
			{"status":"OK"}
		失败输出
			{"status":"fail","result":失败原因}

【禁止玩家上传个性签名】
	输入部分参数说明:
		cmd:disablesign
		playerIds:批量的玩家ID,ID之间以分号隔开,请不要同时输入大量ID,可能会卡服务器
		serverId:目标服务器,数字类型
		duration:禁止时间,数字类型,单位分钟

	输出部分说明:
		本指令需求目标在线
		成功输出
			{"status":"OK"}
		失败输出
			{"status":"fail","result":失败原因}

【解禁玩家上传个性签名】
	输入部分参数说明:
		cmd:enablesign
		playerIds:批量的玩家ID,ID之间以分号隔开,请不要同时输入大量ID,可能会卡服务器
		serverId:目标服务器,数字类型
		
	输出部分说明:
		本指令需求目标在线
		成功输出
			{"status":"OK"}
		失败输出
			{"status":"fail","result":失败原因

【禁止玩家上传头像】
	输入部分参数说明:
		cmd:disablephoto
		playerIds:批量的玩家ID,ID之间以分号隔开,请不要同时输入大量ID,可能会卡服务器
		serverId:目标服务器,数字类型
		duration:禁止时间,数字类型,单位分钟

	输出部分说明:
		本指令需求目标在线
		成功输出
			{"status":"OK"}
		失败输出
			{"status":"fail","result":失败原因}

【解禁玩家上传头像】
	输入部分参数说明:
		cmd:enablephoto
		playerIds:批量的玩家ID,ID之间以分号隔开,请不要同时输入大量ID,可能会卡服务器
		serverId:目标服务器,数字类型
		
	输出部分说明:
		本指令需求目标在线
		成功输出
			{"status":"OK"}
		失败输出
			{"status":"fail","result":失败原因

【禁止玩家朋友圈发状态+评论】
	输入部分参数说明:
		cmd:disablemoment
		playerIds:批量的玩家ID,ID之间以分号隔开,请不要同时输入大量ID,可能会卡服务器
		serverId:目标服务器,数字类型
		duration:禁止时间,数字类型,单位分钟

	输出部分说明:
		本指令需求目标在线
		成功输出
			{"status":"OK"}
		失败输出
			{"status":"fail","result":失败原因}

【解禁玩家朋友圈发状态+评论】
	输入部分参数说明:
		cmd:enablemoment
		playerIds:批量的玩家ID,ID之间以分号隔开,请不要同时输入大量ID,可能会卡服务器
		serverId:目标服务器,数字类型
		
	输出部分说明:
		本指令需求目标在线
		成功输出
			{"status":"OK"}
		失败输出
			{"status":"fail","result":失败原因}

【禁止玩家空间留言】
	输入部分参数说明:
		cmd:disablemessage
		playerIds:批量的玩家ID,ID之间以分号隔开,请不要同时输入大量ID,可能会卡服务器
		serverId:目标服务器,数字类型
		duration:禁止时间,数字类型,单位分钟

	输出部分说明:
		本指令需求目标在线
		成功输出
			{"status":"OK"}
		失败输出
			{"status":"fail","result":失败原因}

【解禁玩家空间留言】
	输入部分参数说明:
		cmd:enablemessage
		playerIds:批量的玩家ID,ID之间以分号隔开,请不要同时输入大量ID,可能会卡服务器
		serverId:目标服务器,数字类型
		
	输出部分说明:
		本指令需求目标在线
		成功输出
			{"status":"OK"}
		失败输出
			{"status":"fail","result":失败原因}

【禁止玩家心情上全服热门】
	输入部分参数说明:
		cmd:disablehotmoment
		playerIds:批量的玩家ID,ID之间以分号隔开,请不要同时输入大量ID,可能会卡服务器
		duration:禁止时间,数字类型,单位分钟

	输出部分说明:
		成功输出
			{"status":"OK"}
		失败输出
			{"status":"fail","result":失败原因}

【解禁玩家心情上全服热门】
	输入部分参数说明:
		cmd: enablehotmoment
		playerIds:批量的玩家ID,ID之间以分号隔开,请不要同时输入大量ID,可能会卡服务器

	输出部分说明:
		成功输出
			{"status":"OK"}
		失败输出
			{"status":"fail","result":失败原因}


【使得某条心情上全服热门】
	输入部分参数说明:
		cmd: uphotmoment
		playerId: 热门心情的玩家的id
		momentIds:批量的状态ID,ID之间以分号隔开,请不要同时输入大量ID

	输出部分说明:
		成功输出
			{"status":"OK"}
		失败输出
			{"status":"fail","result":失败原因}

【使得某条心情下全服热门】
	输入部分参数说明:
		cmd: downhotmoment
		playerId: 热门心情的玩家的id
		momentIds:批量的状态ID,ID之间以分号隔开,请不要同时输入大量ID

	输出部分说明:
		成功输出
			{"status":"OK"}
		失败输出
			{"status":"fail","result":失败原因}


【使得某条心情上本服热门】
	输入部分参数说明:
		cmd: uphotmoment
		playerId: 热门心情的玩家的id
		momentIds:批量的心情ID,ID之间以分号隔开,请不要同时输入大量ID

	输出部分说明:
		成功输出
			{"status":"OK"}
		失败输出
			{"status":"fail","result":失败原因}

【使得某条心情下本服热门】
	输入部分参数说明:
		cmd: downhotmoment
		playerId: 热门心情的玩家的id
		momentIds:批量的心情ID,ID之间以分号隔开,请不要同时输入大量ID
		serverId:目标服务器,数字类型

	输出部分说明:
		成功输出
			{"status":"OK"}
		失败输出
			{"status":"fail","result":失败原因}

	
======= 网页梦岛部分 =======
【删除新鲜事】
	输入部分参数说明:
		cmd:delmdmoment
		momentIds:批量的状态ID,ID之间以分号隔开,请不要同时输入大量ID。若传all,则删除指定玩家的所有状态
		playerId:梦岛帐号ID,数字类型
		
	输出部分说明:
		成功输出
			{"status":"OK"}
		失败输出
			{"status":"fail","result":失败原因}
【删除新鲜事评论】
	输入部分参数说明:
		cmd:delmdcomment
		commentIds:批量的状态ID,ID之间以分号隔开,请不要同时输入大量ID。若传all,则删除指定玩家的所有评论
		playerId:梦岛帐号ID,数字类型
		targetId:评论的玩家ID,数字类型
		
	输出部分说明:
		成功输出
			{"status":"OK"}
		失败输出
			{"status":"fail","result":失败原因}
		
【删除玩家空间留言】
	输入部分参数说明:
		cmd:delmdmessage
		messageIds:批量的留言ID,ID之间以分号隔开,请不要同时输入大量ID。若传all,则删除指定玩家的所有留言
		playerId:梦岛帐号ID,数字类型
		serverId:目标服务器,数字类型
		
	输出部分说明:
		成功输出
			{"status":"OK"}
		失败输出
			{"status":"fail","result":失败原因}
			
【删除相册】
	输入部分参数说明:
		cmd:delmdalbum
		albumIds:批量的状态ID,ID之间以分号隔开,请不要同时输入大量ID
		playerId:梦岛帐号ID,数字类型
		
	输出部分说明:
		成功输出
			{"status":"OK"}
		失败输出
			{"status":"fail","result":失败原因}
			
【删除相片】
	输入部分参数说明:
		cmd:delmdphoto
		photoIds:批量的状态ID,ID之间以分号隔开,请不要同时输入大量ID
		playerId:梦岛帐号ID,数字类型
		
	输出部分说明:
		成功输出
			{"status":"OK"}
		失败输出
			{"status":"fail","result":失败原因}
			

【禁止玩家评论兵器谱】
	输入部分参数说明:
		cmd:disableEquipComment
		playerIds:批量的玩家ID,ID之间以分号隔开,请不要同时输入大量ID,可能会卡服务器
		duration:禁止时间,数字类型,单位分钟

	输出部分说明:
		成功输出
			{"status":"OK"}
		失败输出
			{"status":"fail","result":失败原因}

【解禁玩家评论兵器谱】
	输入部分参数说明:
		cmd: enableEquipComment
		playerIds:批量的玩家ID,ID之间以分号隔开,请不要同时输入大量ID,可能会卡服务器

	输出部分说明:
		成功输出
			{"status":"OK"}
		失败输出
			{"status":"fail","result":失败原因}


  [删除玩家兵器谱评论]
		cmd:delEquipComment
		playerId:玩家ID,数字类型
		targetId:兵器谱日志中的targetId
		equipGk: 兵器谱日志中的equipGk

	输出部分说明:
		成功输出
			{"status":"OK"}
		失败输出
			{"status":"fail","result":失败原因}