# 功能简介
提供游戏内朋友圈抽奖功能

## 发布链接
### 环境天穹发布链接

|应用|天穹地址|
|---|---|
|倩女手游朋友圈-接口应用| https://ccc-apipub.leihuo.netease.com/#/project/detail/566 |
|倩女手游朋友圈-日志消费| https://ccc-apipub.leihuo.netease.com/#/project/detail/566 |


## 接口文档

变更接口简易说明 [接口文档链接](https://l10-md-dev.apps-hp.danlu.netease.com/docs/scalar#/)
| URL | 描述 |
| --- | --- |
| ✨ /server/moment_lottery/add | 动态抽奖-添加，游戏服务器调用 |
| ✨ /moment_lottery/show | 动态抽奖-展示，用于包含动态抽奖的动态展示当前的抽奖信息 |
| ✨ /moment_lottery/winners | 动态抽奖-中奖玩家名单 |
| ✨ /gm_cmd/moment_lottery/draw | GM指令-抽奖动态触发开奖 |
| ✨ /gm_cmd/moment_lottery/attend | GM指令-用于测试互动后，是否参与成功 |
| ✨ /gm_cmd/gms/postReceiveLimit.php | GM指令-抽奖动态发奖指令 |
| ✏️ /delmoment | 删除动态, 删除抽奖动态的被删除的逻辑处理以及转发动态被删除的逻辑处理|
| ✏️ /likemoment | 点赞动态， 涉及抽奖资格参与|
| ✏️ /momentforward| 转发动态， 涉及抽奖资格参与|
| ✏️ /addcomment | 评论动态， 涉及抽奖资格参与|
| ✏️ /delcomment | 删除评论， 涉及抽奖资格参与|
| ✏️ /follow/add | 关注抽奖发起人， 涉及抽奖资格参与|
| ✏️ /follow/cancel | 取消关注抽奖发起人， 涉及抽奖资格参与|
| ✨ /kafka/CreditScore | 日志消费模拟-信用分数日志 |
| ✨ /kafka/LoginRole_Additional | 日志消费模拟-角色登录额外信息日志, 用于同步等级 |
| ✨ /kafka/PlayerLevelUp | 日志消费模拟-玩家升级日志, 用于同步等级 |

## 数据库表结构变更

```sql
--- 动态抽奖数据表
CREATE TABLE `pyq_moment_lottery` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `MomentId` bigint(20) NOT NULL COMMENT '动态ID',
  `RoleId` bigint(20) NOT NULL COMMENT '动态所属角色ID',
  `SN` varchar(24) NOT NULL COMMENT '抽奖动态的唯一id',
  `Type` tinyint(4) NOT NULL COMMENT '动态抽奖类型 1:阳光普照 2:天选之人',
  `Prizes` varchar(255) NOT NULL COMMENT '奖品列表',
  `Jade` bigint(20) NOT NULL COMMENT '奖品灵玉总值',
  `WinnerNum` int(10) NOT NULL COMMENT '中奖人数',
  `DrawTime` bigint(20) NOT NULL COMMENT '开奖时间',
  `ServerScope` tinyint(4) NOT NULL COMMENT '开奖服务器范围 1:全服 2:本服',
  `RequireLike` tinyint(4) NOT NULL COMMENT '是否需要点赞',
  `RequireComment` tinyint(4) NOT NULL COMMENT '是否需要评论',
  `RequireForward` tinyint(4) NOT NULL COMMENT '是否需要转发',
  `RequireFollow` tinyint(4) NOT NULL COMMENT '是否需要关注',
  `MinLevel` int(10) NOT NULL COMMENT '中奖最低等级',
  `DrawStatus` tinyint(4) NOT NULL DEFAULT 0 COMMENT '开奖状态 0待开奖 1已开奖 2被取消 3开奖失败',
  `CreateTime` bigint(20) NOT NULL COMMENT '创建时间',
  `UpdateTime` bigint(20) NOT NULL COMMENT '更新时间',
  `Winners` varchar(2048) NOT NULL COMMENT '中奖者ID列表,逗号分隔的字符串',
  `RetryCount` INT NOT NULL DEFAULT 0 COMMENT '重试次数',
  `NextRetryTime` BIGINT NOT NULL DEFAULT 0 COMMENT '下次重试时间',
  `LastError` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '上次失败原因',
  PRIMARY KEY (`ID`),
  KEY `idx_role_id` (`RoleId`),
  UNIQUE KEY `idx_moment_id` (`MomentId`),
  UNIQUE KEY `idx_sn` (`SN`),
  KEY `idx_next_retry_draw_status` (`NextRetryTime`, `DrawStatus`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='动态抽奖数据表';


CREATE TABLE `pyq_moment_lottery_attend` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `RoleId` bigint(20) NOT NULL COMMENT '角色ID',
  `IsWin` tinyint(4) NOT NULL COMMENT '是否中奖',
  `MomentId` bigint(20) NOT NULL COMMENT '动态ID',
  `DS` char(8) NOT NULL COMMENT '当日日期, 格式为yyyyMMdd',
  `Status` tinyint(4) DEFAULT '0',
  `CreateTime` bigint(20) NOT NULL COMMENT '创建时间',
  `UpdateTime` bigint(20) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `idx_role_id_moment_id` (`RoleId`,`MomentId`),
  KEY `idx_moment_id` (`MomentId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='动态抽奖参与表';


CREATE TABLE `pyq_moment_lottery_attend_action` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `RoleId` bigint(20) NOT NULL COMMENT '角色ID',
  `MomentId` bigint(20) NOT NULL COMMENT '动态ID',
  `Action` tinyint(4) NOT NULL COMMENT '动作类型, 点赞1, 评论2, 转发4, 关注8',
  `Notes` varchar(255) NOT NULL COMMENT '备注',
  `CreateTime` bigint(20) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `idx_role_id_moment_id_action` (`RoleId`,`MomentId`, `Action`),
  KEY `idx_moment_id` (`MomentId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='动态抽奖参与表';


CREATE TABLE `pyq_transfer_table_status` (
    `id` bigint(11) unsigned NOT NULL AUTO_INCREMENT,
    `oldId` bigint(20) NOT NULL COMMENT '转服前id',
    `newId` bigint(20) NOT NULL COMMENT '转服后id',
    `tableName` varchar(64) NOT NULL COMMENT '迁移的数据库名',
    `hashKey` varchar(32) NOT NULL COMMENT '迁移的任务的hash',
    `col` varchar(64) NOT NULL COMMENT '迁移的数据库列名',
    `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:迁移开始, 1:迁移成功, 2:迁移失败',
    `cost` bigint(20) NOT NULL DEFAULT '0' COMMENT '耗时, 单位ms',
    `log` varchar(255) NOT NULL DEFAULT '' COMMENT '迁移日志',
    `createTime` bigint(20) NOT NULL COMMENT '创建时间',
    `updateTime` bigint(20) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx-transfer` (`hashKey`),
    KEY `idx-newId` (`newId`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8 COMMENT = '倩女手游数据表转服状态迁移表';


ALTER TABLE `qnm_roleinfo` ADD COLUMN `MaxLevel` INT NOT NULL DEFAULT 0 COMMENT '角色最大等级', ALGORITHM=INSTANT;

CREATE TABLE `pyq_credit_score` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `AccountId` varchar(255) NOT NULL,
  `RoleId` bigint(20) NOT NULL COMMENT '角色ID',
  `ServerId` int(10) unsigned NOT NULL COMMENT '服务器ID',
  `Uid` varchar(255) NOT NULL COMMENT '角色UID',
  `MpayAccount` varchar(255) NOT NULL COMMENT '角色MPAY账号',
  `CreditScore` int NOT NULL COMMENT '信用分',
  `AllScore` int NOT NULL COMMENT '总信用分',
  `CreateTime` bigint(20) NOT NULL COMMENT '创建时间',
  `UpdateTime` bigint(20) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `idx_role_id` (`RoleId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='玩法信用分表';

```


## 日志相关

### 投递变更
- 🔼 **上游**: 运营日志L10
- 🔽 **下游Topic**: l10_game_media_log
- 🎯 **指定关键字**:
  - FashionLottery_LotteryOnce
  - FashionLotteryLongterm_LotteryOnce
  - LoginRole_Additional
  - PlayerLevelUp
  - CreditScore
- 🔑 **ACL账号**: other_dep295_l10_media_game_log


### 投递变更检查
- 测试环境变更已完成: <EMAIL>
- 线上环境配置已变更完成

## 日志消费

### LoginRole_Additional 日志
使用 `max_level` 作为角色等级

**字段说明**:

| 序号 | 字段名 | 类型 | 描述 |
|:---:|:------|:-----|:-----|
| 1 | ip | string | IP地址 |
| 2 | ipv6 | string | IPv6地址 |
| 3 | device_model | string | 设备型号 |
| 4 | device_height | int | 设备屏幕高度 |
| 5 | device_width | int | 设备屏幕宽度 |
| 6 | os_name | string | 操作系统名称 |
| 7 | os_ver | string | 操作系统版本 |
| 8 | mac_addr | string | 设备MAC地址 |
| 9 | udid | string | 设备唯一标识 |
| 10 | nation | int | 国家地区编号 |
| 11 | isp | string | 运营商 |
| 12 | network | string | 网络类型 |
| 13 | app_channel | string | 应用渠道 |
| 14 | app_ver | string | 应用版本 |
| 15 | server | string | 服务器ID |
| 16 | account_id | string | 账号ID |
| 17 | old_accountid | string | 旧账号ID |
| 18 | role_id | string | 角色ID |
| 19 | role_name | string | 角色名称 |
| 20 | role_level | int | 角色等级 |
| 21 | u_vip | int | VIP等级 |
| 22 | u_icon | string | 用户头像 |
| 23 | u_sch | string | 用户学院 |
| 24 | mf | int | 幸运值 |
| 25 | xiuwei | int | 修为值 |
| 26 | xiulian | int | 修炼值 |
| 27 | current_shifu | string | 当前师傅 |
| 28 | current_tudi | string | 当前徒弟 |
| 29 | all_shifu | table | 所有师傅 |
| 30 | all_tudi | table | 所有徒弟 |
| 31 | partner_id | string | 侠侣ID |
| 32 | isCommonDevice | int | 是否常用设备 |
| 33 | u_dtls | table | 用户详细信息 |
| 34 | country_code | string | 国家地区编号 |
| 35 | max_level | int | 角色仙凡身等级取较大值 |

**示例**:
```json
[2025-02-14 20:08:29][LoginRole_Additional],{"ip":"************", "ipv6":"", "device_model":"Handheld#iPad11,3#6#Metal", "device_height":1668, "device_width":2224, "os_name":"ios", "os_ver":"iOS 13.7", "mac_addr":"5FCF4F44-033E-4F2F-B07B-ECF9E94586B0", "udid":"A11E6BFA-628F-4441-8952-54A87FE8718A", "nation":86, "isp":"", "network":"wifi", "app_channel":"app_store", "app_ver":"641100", "server":"2043", "account_id":"aebflnbaqab6gh2h@ios.app_store.win.163.com", "old_accountid":"", "role_id":"**********", "role_name":".。红衣洛洛", "role_level":154, "u_vip":6, "u_icon":"1", "u_sch":"1", "mf":301, "xiuwei":118, "xiulian":55, "current_shifu":0, "current_tudi":"***********,***********", "all_shifu":[*********,*********], "all_tudi":[*********,**********,***********,***********,***********,***********], "partner_id":"0", "isCommonDevice":1, "u_dtls":{"monthcard_left":16,"jade_left":5223,"bigmonthcard_left":0,"is_feisheng":1,"bind_jade_left":673}, "country_code":"CN", "max_level":154}
```

### PlayerLevelUp 日志

| 序号 | 字段名 | 类型 | 描述 |
|:---:|:------|:-----|:-----|
| 1 | server | string | |
| 2 | account_id | string | |
| 3 | old_accountid | string | |
| 4 | role_id | string | |
| 5 | role_name | string | |
| 6 | role_level | int | |
| 7 | u_vip | int | |
| 8 | u_icon | string | |
| 9 | u_sch | string | |
| 10 | reason | string | |
| 11 | u_dtls | table | |
| 12 | ip | string | |
| 13 | ipv6 | string | |
| 14 | device_model | string | |
| 15 | device_height | int | |
| 16 | device_width | int | |
| 17 | os_name | string | |
| 18 | app_channel | string | |
| 19 | os_ver | string | |
| 20 | online_time | string | |
| 21 | pc_login | int | 是否PC端登录 |
| 22 | max_level | int | 角色仙凡身等级取较大值 |

**示例**:
```json
[2025-02-14 20:12:50][PlayerLevelUp],{"server":"2483", "account_id":"<EMAIL>", "old_accountid":"", "role_id":"**********", "role_name":"子夜", "role_level":2, "u_vip":0, "u_icon":"0", "u_sch":"9", "reason":"LevelUp", "u_dtls":38, "ip":"***************", "ipv6":"", "device_model":"Handheld#HONOR PGT-AN10#8#OpenGLES3", "device_height":1080, "device_width":2344, "os_name":"ad", "app_channel":"netease.sub40406_toutiaoys3_cpc_dec", "os_ver":"Android OS 15 \/ API-35 (HONORPGT-AN10\/9.", "online_time":"197", "pc_login":0, "max_level":2}
```

### CreditScore 日志
这个是信用分，看credit_score这个，登录和更新都会记

| 序号 | 字段名 | 类型 | 描述 |
|:---:|:------|:-----|:-----|
| 1 | server | string | |
| 2 | account_id | string | |
| 3 | role_id | string | |
| 4 | role_name | string | |
| 5 | role_level | int | |
| 6 | uid | string | |
| 7 | mpay_account | string | mpay账号 |
| 8 | ip | string | |
| 9 | credit_score | int | 信用分 |
| 10 | all_score | int | 所有分 |


**示例**:
```json
[2025-02-14 20:13:18][CreditScore],{"server":"2010", "account_id":"aebfpp4tlufvpc7z@ios.app_store.win.163.com", "role_id":"**********", "role_name":"卿若惜", "role_level":157, "uid":"aebfpp4tlufvpc7z", "mpay_account":"<EMAIL>", "ip":"**************", "credit_score":667, "all_score":500}
```

## 环境数据库配置

### 测试环境-MySQL
 ```js
exports.DB_CFG = {
     host: 'apps-hp.danlu.netease.com',
     user: 'root',
     port: 37778
     connectionLimit: 5,
     password: 'nodePassword',
     charset: 'utf8mb4',
     database: 'nodejs_yxb'
};
```


### 测试环境-Redis
```js
exports.REDIS_CFG = {
    hosts: ["apps-hp.danlu.netease.com"],
    host: "apps-hp.danlu.netease.com",
    password: "nodePassword",
    db: 0,
    port: 37778,
    no_ready_check: false,
    prefix: "qnm_md:"
};
```

### 测试环境-Kafka
##### Topic监控地址
 https://shuyuan.fuxi.netease.com/kafka/topic/3552?tab=dataQuery

##### 具体配置
```js
  kafkaCfg.brokers = ["kafka-game.inner.fuxi.netease.com:9092"]
  kafkaCfg.topic = "l10_operation_log_test";
  kafkaCfg.sasl.username = "ccc_l10_operation_log_test_read";
  kafkaCfg.sasl.password = "kyT05TJ6XJXi2h7J";
  kafkaCfg.clientId = "l10_ccc_md";
  kafkaCfg.groupId = "ccc_l10_operation_log_test_moment_lottery";
  kafkaCfg.fromBeginning = false
```

### 正式环境-Kafka
#### Topic监控地址
https://kafka.nie.netease.com/_dep295/avatar/topics/detail/6678e634a0d3bf5e3f969630/tailer
#### 具体配置
```js
  kafkaCfg.brokers = ["grand.brokers.canal.netease.com:9093"]
  kafkaCfg.topic = "l10_game_media_log";
  kafkaCfg.sasl.username = "other_dep295_l10_media_game_log";
  kafkaCfg.sasl.password = ""
  kafkaCfg.clientId = "l10_ccc_md_release";
  kafkaCfg.groupId = "l10_ccc_md_fashion_lottery_release";
  kafkaCfg.fromBeginning = false
```

## 业务流程图

### 处理是否完成抽奖资格(开奖逻辑)
```mermaid
graph TD
    A[开始: handlePlayerAttendMomentLottery] --> B{获取抽奖信息: getLotteryByMomentId}
    B -- 抽奖不存在 --> C[记录日志并返回]
    B -- 抽奖存在 --> D{是否自己参与: roleId === momentRoleId}
    D -- 是 --> E[抛出错误: LotteryAttendSelf]
    D -- 否 --> F{获取抽奖相关动作: getLotteryRelatedActions}
    F --> G{action 是否在 lotteryActions 中}
    G -- 否 --> H[记录日志并返回]
    G -- 是 --> I{抽奖是否等待开奖: isLotteryWaitDraw}
    I -- 否 --> J[记录日志并返回]
    I -- 是 --> K{action 是否为反向操作: action < 0}
    K -- 是 --> L{获取正向操作: opAction = -action}
    L --> M{检查正向操作是否存在: isLotteryPlayerActionFinished}
    M -- 存在 --> N[删除正向操作记录: deleteByCondition]
    N --> O{检查是否已参与抽奖: isQualifyForDraw}
    O -- 是 --> P[取消抽奖资格: cancelDrawQualify]
    P --> Q[减少抽奖参与计数: decrMomentLotteryAttendCount]
    Q --> R[返回]
    O -- 否 --> R[返回]
    M -- 不存在 --> O
    K -- 否 --> S{ServerScope 是否为 LocalServer}
    S -- 是 --> T{是否在同一服务器: isPlayerInSameServer}
    T -- 否 --> U[抛出错误: LotteryNeedSameServer]
    T -- 是 --> V{MinLevel 是否大于 0}
    V -- 是 --> W{获取玩家等级: getRoleLevel}
    W --> X{玩家等级是否小于 MinLevel}
    X -- 是 --> Y[抛出错误: LotteryNeedMinLevel]
    X -- 否 --> Z{检查每日参与次数是否超过限制}
    Z -- 是 --> AA[记录日志并返回]
    Z -- 否 --> AB{检查信用分是否足够}
    AB -- 否 --> AC[记录日志并返回]
    AB -- 是 --> AD{检查操作是否已存在: isLotteryPlayerActionFinished}
    AD -- 是 --> AE[抛出错误: LotteryAttendActionExist]
    AD -- 否 --> AF[添加操作记录: addAction]
    AF --> AG{检查是否满足抽奖资格: isQualifyForAttend}
    AG -- 否 --> AH[抛出错误: LotteryRequireNotAllMatch]
    AG -- 是 --> AI[增加每日参与记录: playerDailyAttendMomentSetAdd]
    AI --> AJ[增加抽奖资格: addDrawQualify]
    AJ --> AK[增加抽奖参与计数: incrMomentLotteryAttendCount]
    AK --> AL[设置 resp.isQualifyForAttend = true, resp.attendId = attendId]
    AL --> AM[返回 resp]
    V -- 否 --> Z
    S -- 否 --> V
```

### 开奖逻辑示意图
```mermaid
graph TD
    A[drawMomentLottery] --> B[获取Redis锁]
    B --> C{是否获取到锁?}
    C -->|是| D[获取抽奖信息]
    C -->|否| E[抛出错误: LotteryIsDrawing]
    D --> F{抽奖状态检查}
    F -->|已开奖| G[返回已开奖状态]
    F -->|已取消| H[返回已取消状态]
    F -->|待开奖| I{是否有中奖者名单?}
    I -->|是| J[复用已有中奖者名单]
    I -->|否| K[获取候选玩家列表]
    K --> L{候选玩家数量是否满足条件?}
    L -->|否| M[取消抽奖]
    L -->|是| N[确定中奖者]
    N --> O[保存中奖者信息]
    O --> P[通知游戏发奖]
    P --> Q[保存中奖者详细信息]
    Q --> R[更新开奖状态为已开奖]
    R --> S[发送开奖通知]
    S --> T[返回成功]
    M --> U[更新开奖状态为已取消]
    U --> V[返回已取消状态]
    J --> P
```

### 抽奖重试策略逻辑
```mermaid
graph TD
    A[drawMomentLotteryChecker] --> B[查询待开奖的抽奖记录]
    B --> C{是否有待开奖记录?}
    C -->|是| D[标记为开奖中状态]
    D --> E[调用drawMomentLottery进行开奖]
    E --> F{开奖成功?}
    F -->|是| G[记录成功]
    F -->|否| H{是否可重试错误?}
    H -->|是| I[更新重试次数和下次重试时间]
    H -->|否| J[记录失败]
    I --> K{重试次数是否超过最大限制?}
    K -->|是| J
    K -->|否| L[记录重试]
    C -->|否| M[结束]
```

### 业务配置说明

| 配置项 | 描述 | 默认值 |
|--------|------|--------|
| gameTimeOffsetAllowMaxSeconds | 游戏服务器和当前服务器允许的最大偏移时间 | 600 (10分钟) |
| luckShineAllMaxWinnerNum | 阳光普照抽奖最大中奖人数 | 100 |
| prizeCategoryMaxNum | 单份奖品最多种类 | 9 |
| prizeItemMaxNumPerCategory | 单份奖品每种种类最多数量 | 999 |
| drawTimeMinDurationHours | 开奖时间选择最短时长 | 24 |
| drawTimeMaxDurationHours | 开奖时间选择最大时长 | 720 (30天) |
| lotteryGongShiSeconds | 动态抽奖公示期时间 | 3600 (1小时) |
| authTokenSalt | 抽奖接口验证token salt | "&7%^!*(_)$#@!~_$%^#+#" |
| skipAuthTokenCheck | 是否跳过抽奖接口token验证 | false |
| lotteryMinJade | 奖品总价值最低100灵玉 | 100 |
| addLotteryLockTTLMs | 发布抽奖动态的并发锁过期时间 | 600000 (10分钟) |
| lotteryInfoCacheSeconds | 动态抽奖信息缓存时间 | 600 (10分钟) |
| attendLotteryMinCreditScore | 参与抽奖的最低信用分 | 550 |
| attendLotteryPerPlayerPerDayMaxTimes | 每个玩家每天最低参与抽奖的次数 | 3 |
| lotterySnMaxLength | 抽奖动态的sn最大长度 | 24 |
| officialServerLotteryCheck | 默认关闭官服玩家发起抽奖的检查 | false |
| gmsPostReceiveLimitApiUrl | 游戏抽奖接口地址配置 | "http://apps-sl.danlu.netease.com:37975/qnm/gms/postReceiveLimit.php" |
| gmsPostReceiveLimitApiMock.enable | 是否开启mock | false |
| gmsPostReceiveLimitApiMock.mockApiRes | 用来模拟抽奖接口返回的结果 | '{"status":"OK"}' |
| gmsPostReceiveLimitApiTimeout | 游戏抽奖接口超时时间 | 5000 (5秒) |
| minLotteryCandidateNum | 开奖符合要求的最小人数 | 10 |
| drawCheckerCron | 定时任务触发自动开奖 | "*/5 * * * * *" (每5秒检查1次) |
| maxRetryCount | 开奖接口失败的时候最大重试次数 | 20 |
| retryDelayMsBase | 开奖接口失败的时候重试的延迟时间 | 10000 (10秒) |
| retryDelayMsMax | 开奖接口失败的时候重试的延迟时间最大值 | 3600000 (1小时) |


## 压测相关配置

### 压测部署丹炉地址
 https://danlu.netease.com/service/#/service/deployments/detail/103523?project_id=1659&cloud_type=private

### API接口前缀
https://qnm-pyq-server-stress.apps-sl.danlu.netease.com

健康检查接口
```bash
curl "https://qnm-pyq-server-stress.apps-sl.danlu.netease.com/qnm/healthCheck"
```

### 压测数据库配置
```js
exports.DB_CFG = {
  connectionLimit: 100,
  host: '**************',
  port: 4106,
  user: 'lhpp_preview',
  password: 'yfzBHbLU3iH87nIE',
  charset: 'utf8mb4',
  database: 'lhpp_preview'
};


exports.REDIS_CFG = {
    hosts: ['**************'],
    host: '**************',
    port: 7621,
    db: 0,
    password: 'RUyVFGbidEO1hyD5',
    no_ready_check: false,
    keyPrefix: "l10_md_stress:",
};
```

### 需要Mock的第三方接口
参考gmsPostReceiveLimitApiUrl配置项,
接口参数和访问参考: https://l10-md-dev.apps-hp.danlu.netease.com/docs/scalar#/paths/gm_cmd-gms-postReceiveLimit.php/post