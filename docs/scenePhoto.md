### 风景相册接口

#### 接口授权
通过公共参数和接口参数共同生成token来校验

#### 公共参数

#### 参数说明
|参数名| 类型 | 说明|
|:------ |:----- | --- |
|roleid| int| roleid|
|server| int| 服务器id|
|timestamp| int| 时间戳(单位s) |
|nonce | int | 随机数 |
|token | string | 授权token |

#### token 计算方法
1. 对所有请求参数（包括公有参数和私有参数，但不包括 token 参数），按照参数名ASCII码表升序顺序排序。 如：server=2， roleid=1002 排序后的顺序是 roleid=1002， server=2， 
2. 生成签名字符串, 将所有参数值相加,v1+v2+...+vn 根据上面的示例得到的构造结果为：10022
3. 生成token, token=md5(sign + secret), secret由网站提供



#### 上传图片
POST {{apiHost}}/qnm/scene_photo/add

#### 参数说明
|参数名| 类型 | 说明|
|:------ |:----- | --- |
|roleid| int| roleid|
|index| int| 图片索引|
|url | string | 图片地址|


###  返回示例
```json
{
  "code": 0,
  "data": {
    "id": 1
  }
}
```

#### 列出图片
POST {{apiHost}}/qnm/scene_photo/list

#### 参数说明
|参数名| 类型 | 说明|
|:------ |:----- | --- |
|roleid| int| roleid|

###  返回示例
```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "ID": 1,
        "Index": 1,
        "Url": "1.png",
        "CreateTime": 1559630552193
      }
    ]
  }
}
```

#### 补充说明
以上传图片为例

curl --request GET \
  --url 'http://***************:88/qnm/scene_photo/list?roleid=1002&server=2&timestamp=1559628920&nonce=1234&token=c3adc53a9df5993849769bb3f5b0c240'

1. 参数排序: [ 'index', 'nonce', 'roleid', 'server', 'timestamp', 'url' ]
2. 签名的串:  "112341002215596289201.png${secret}"  为s
3. token: md5(s)
