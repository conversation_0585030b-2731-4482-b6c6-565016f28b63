-- #inclue pyq.sql
-- #inclue md.sql


DROP table IF EXISTS `qn_roleinfo`;
DROP table IF EXISTS `qn_roleprop`;
DROP table IF EXISTS `qn_roleanimal`;
DROP table IF EXISTS `qn_rolechild`;
DROP table IF EXISTS `qn_roleevent`;
DROP table IF EXISTS `qn_rolehome`;
DROP table IF EXISTS `qn_roleskill`;
DROP table IF EXISTS `qn_rolefriend`;


-- 角色基本信息
CREATE TABLE `qn_roleinfo` (
  `RoleId` bigint(20) NOT NULL COMMENT '角色ID',
  `UserId` bigint(20) DEFAULT 0,
  `UserName` varchar(127) COMMENT '玩家账号',
  `ServerId` int(11) COMMENT '服务器ID',
  `RoleName` varchar(63) COMMENT '角色昵称',
  `Gender` smallint(4) DEFAULT 0 COMMENT '性别',
  `Level` int(11) DEFAULT 0 COMMENT '等级',
  `JobId` smallint(6) DEFAULT 0 COMMENT '职业',
  `CreateTime` bigint(20) NOT NULL COMMENT '角色创建时间',
  `Title` varchar(127) COMMENT '称号',
  `GangId` varchar(127),
  `Gang` varchar(63) COMMENT '帮会名称',
  `ImageUrl` varchar(255) COMMENT '形象图片地址',
  `State` smallint(6) DEFAULT 0 COMMENT '状态',
  `UpdateTime` bigint(20) NOT NULL COMMENT '资料最新更新时间',
  `Xiuwei` bigint(20) DEFAULT 0 COMMENT '修为等级',
  `Outfit` bigint(20) DEFAULT 0 COMMENT '装备总评分',
  `Money` bigint(20) DEFAULT 0 COMMENT '银两值',
  `bucketno` smallint(6),
  `FTRoleName` varchar(255) COMMENT '角色名分词',
  `SnsGender` char(1) COMMENT '社区性别',
  `OtherInfo` text,
  PRIMARY KEY (`RoleId`),
  KEY `IDx_upt_udi` (`UpdateTime`,`UserId`),
  KEY `UserName` (`UserName`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='玩家基本信息';

-- 角色装备信息
CREATE TABLE `qn_roleprop` (
  `RoleId` bigint(20) PRIMARY KEY NOT NULL COMMENT '角色ID',
  `EquipInfo` text COMMENT '装备信息',
  `PropertyInfo` text COMMENT '基本属性',
  `OtherInfo` text COMMENT '抗性等其它属性',
  `PointInfo` text COMMENT '角色能力',
  `UpdateTime` bigint(20),
  `bucketno` int(11)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='玩家装备等信息';

CREATE TABLE `qn_roleanimal` (
  `RoleId` bigint(20) PRIMARY KEY NOT NULL COMMENT '角色ID',
  `UpdateTime` bigint(20),
  `Count` varchar(255),
  `AnimalInfo` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `qn_rolechild` (
  `RoleId` bigint(20) PRIMARY KEY NOT NULL COMMENT '角色ID',
  `UpdateTime` bigint(20),
  `Count` varchar(255),
  `ChildInfo` text,
  `ChildImg` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `qn_roleevent` (
  `ID` bigint(20) AUTO_INCREMENT PRIMARY KEY NOT NULL,
  `RoleId` bigint(20) NOT NULL COMMENT '角色ID',
  `Type` tinyint(4),
  `Parameter` varchar(255),
  `Priority` tinyint(4),
  `GameId` smallint(8),
  `PublishTime` bigint(20),
  `bucketno` varchar(255),
  `UserId` int(11)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `qn_rolehome` (
  `RoleId` bigint(20) PRIMARY KEY NOT NULL COMMENT '角色ID',
  `Level` int(11),
  `ImageUrl` varchar(255),
  `LevelInfo` text,
  `FengShui` text,
  `CjbInfo` text,
  `UpdateTime` bigint(20)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `qn_roleskill` (
  `RoleId` bigint(20) PRIMARY KEY NOT NULL COMMENT '角色ID',
  `FightSkill` text,
  `UpdateTime` bigint(20)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `qn_rolefriend` (
  `RoleId` bigint(20) NOT NULL COMMENT '角色ID',
  `FriendList` text COMMENT '朋友列表',
  `EnemyList` text COMMENT '仇敌列表',
  `UpdateTime` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`RoleId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;