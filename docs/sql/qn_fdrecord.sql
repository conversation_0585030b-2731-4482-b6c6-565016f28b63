DROP TABLE IF EXISTS `qn_fdrecord`;
CREATE TABLE `qn_fdrecord` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `ServerId` varchar(127) DEFAULT NULL COMMENT '服务器ID',
  `BossType` smallint(3) NOT NULL DEFAULT '0' COMMENT '怪物类型',
  `GuildId` bigint(20) NOT NULL DEFAULT '0' COMMENT '帮会id',
  `WeekNum` bigint(20) NOT NULL DEFAULT '0' COMMENT '属于哪一周的榜',
  `PublishTime` bigint(20) NOT NULL DEFAULT '0' COMMENT '击杀时间',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `udx_wn_bt_gid` (`WeekNum`,`BossType`,`GuildId`),
  KEY `IDX_GID_BT` (`GuildId`,`BossType`),
  KEY `idx_wn_sid_btp_ptime` (`WeekNum`,`ServerId`,`BossType`,`PublishTime`),
  KEY `idx_wn_btp_ptime` (`WeekNum`,`BossType`,`PublishTime`),
  KEY `idx_btp_pt` (`BossType`,`PublishTime`),
  KEY `idx_sid_btp_pt` (`ServerId`,`BossType`,`PublishTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='首杀历史记录';
