
DROP table IF EXISTS `md_user`;
CREATE TABLE `md_user` (
  `ID` bigint(20) AUTO_INCREMENT PRIMARY KEY NOT NULL,
  `UserName` varchar(127) NOT NULL,
  `NickName` varchar(127) UNIQUE,
  `Birthday` date,
  `Province` int(11),
  `City` int(11),
  `Gender` tinyint(4),		/*0 女 1 男*/
  `Signature` varchar(100) COMMENT '个性签名',
  `Avatar` varchar(255) COMMENT '用户头像',
  `AvaAuthStatus` int(11) DEFAULT 0 COMMENT '头像审核状态：0 未审核 1 通过 -1 不通过',
  `VisitList` text COMMENT '最近访客列表',
  `UnbanTime` bigint(20),
  `UserType` int(11) DEFAULT 0,
  `bucketno` smallint(6),
  `CreateTime` bigint(20),
  `LastLogin` bigint(20) COMMENT '上次登录时间',
  `BanTime` bigint(20) DEFAULT 0 COMMENT '账号封禁时间',
  `Status` int(11) DEFAULT 0 COMMENT '标识账号状态',
  UNIQUE KEY `ak1_Account` (`UserName`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP table IF EXISTS `md_bindrole`;
CREATE TABLE `md_bindrole` (
  `RoleId` bigint(20) NOT NULL COMMENT '角色ID',
  `UserId` bigint(20) NOT NULL,
  `GameId` smallint(8) NOT NULL COMMENT '游戏代号',
  `BindTime` bigint(20) DEFAULT 0 COMMENT '绑定时间',
  `Type` tinyint(4) DEFAULT 0 COMMENT '角色类型：2 主角色',
  `Auths` text NOT NULL COMMENT '权限信息',
  PRIMARY KEY (`RoleId`,`GameId`),
  KEY `uid` (`UserId`),
  KEY `roleid` (`RoleId`),
  KEY `gameid` (`GameId`),
  KEY `userid-gameid` (`UserId`,`GameId`),
  KEY `Type` (`Type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='角色绑定表';

/*CREATE TABLE `aboutme_new` (
  `UserId` bigint(20) PRIMARY KEY NOT NULL,
  `UserName` varchar(127) NOT NULL,
  `NickName` varchar(127),
  `birthday` date,
  `Province` int(11),
  `City` int(11),
  `Gender` tinyint(4),
  `avaAuthStatus` int(11),
  `accountStatus` int(11),
  `unbanTime` bigint(20),
  `GameRoleId` varchar(127) COMMENT '用户绑定的游戏角色',
  `UserType` int(11) DEFAULT 0,
  KEY `idx1_AboutMeBasic` (`UserName`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;*/

-- ----------------------------
-- Table structure for md_moment
-- ----------------------------
DROP TABLE IF EXISTS `md_moment`;
CREATE TABLE `md_moment` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `UserId` bigint(20) NOT NULL,
  `RoleId` bigint(20) COMMENT '角色ID',
  `GameMomentId` bigint(20) COMMENT '对应的游戏动态ID',
  `GameId` smallint(8) COMMENT '游戏代号',
  `Text` varchar(200),
  `ImgList` text COMMENT '图片列表',
  `ImgAudit` text COMMENT '图片审核状态：0 未审核 1 通过 -1 不通过',
  `VideoList` text COMMENT '视频列表',
  `ZanList` text COMMENT '点赞列表',
  `CreateTime` bigint(20) NOT NULL,
  `Status` tinyint(4) DEFAULT 0 COMMENT '动态状态：0 正常 -1 已删除',
  `Hot` bigint(20) NOT NULL DEFAULT 0 COMMENT '新鲜事热门度，点赞和评论各加1',
  `IsTop` tinyint(4) NOT NULL COMMENT '-1：热度置0  0： 非置顶  1-3： 三级置顶',
  PRIMARY KEY (`ID`),
  KEY `userid` (`UserId`,`Status`),
  KEY `createtime` (`CreateTime`),
  KEY `hot-createtime` (`IsTop`,`Hot`,`CreateTime`) USING BTREE,
  KEY `istop-hot` (`IsTop`,`Hot`,`Status`,`CreateTime`) USING BTREE,
  KEY `GameMomentId` (`GameMomentId`,`GameId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for md_comment
-- ----------------------------
DROP TABLE IF EXISTS `md_comment`;
CREATE TABLE `md_comment` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `UserId` bigint(20) NOT NULL,
  `TargetId` bigint(20) NOT NULL COMMENT '评论对象ID',
  `ReplyId` bigint(20) COMMENT '回复对象ID',
  `Text` varchar(200) COMMENT '评论内容',
  `CreateTime` bigint(20) NOT NULL,
  `Status` tinyint(4) DEFAULT 0 COMMENT '评论状态：0 正常 -1 已删除',
  PRIMARY KEY (`ID`),
  KEY `CreateTime` (`CreateTime`),
  KEY `target_status` (`TargetId`,`Status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for md_message
-- ----------------------------
DROP TABLE IF EXISTS `md_message`;
CREATE TABLE `md_message` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `UserId` bigint(20) NOT NULL,
  `TargetId` bigint(20) NOT NULL COMMENT '留言对象ID',
  `Text` varchar(200) COMMENT '留言内容',
  `CreateTime` bigint(20) NOT NULL,
  `Status` tinyint(4) DEFAULT 0 COMMENT '留言状态：0 未读 1 已读 -1已删除',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for md_inform
-- ----------------------------
DROP TABLE IF EXISTS `md_inform`;
CREATE TABLE `md_inform` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `UserId` bigint(20) COMMENT '通知发起者id',
  `Type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '消息类别：0 被关注 1 新鲜事被评论 2 新鲜事被回复 3 留言被回复 4 点赞',
  `TargetId` bigint(20) NOT NULL COMMENT '通知接收者ID',
  `Status` tinyint(4) NOT NULL DEFAULT 0,
  `RelateId` bigint(20) COMMENT '如果是评论，为评论ID',
  `CreateTime` bigint(20),
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- Table structure for md_contacts
-- ----------------------------
DROP TABLE IF EXISTS `md_contacts`;
CREATE TABLE `md_contacts` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `TargetId` bigint(20) COMMENT '被关注的UserId',
  `UserId` bigint(20) COMMENT '主动关注的UserId',
  `Status` tinyint(4) DEFAULT 0,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `ak1_Account` (`TargetId`,`UserId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


DROP TABLE IF EXISTS `md_answer`;
CREATE TABLE `md_answer` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `UserId` bigint(20) NOT NULL,
  `TargetId` bigint(20) NOT NULL COMMENT '评论对象ID',
  `ReplyId` bigint(20) COMMENT '回复对象ID',
  `Text` varchar(200) COMMENT '评论内容',
  `CreateTime` bigint(20) NOT NULL,
  `Status` tinyint(4) DEFAULT 0 COMMENT '评论状态：0 正常 -1 已删除',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


DROP TABLE IF EXISTS `md_nickname_histories`;
CREATE TABLE `md_nickname_histories` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `UserId` bigint(20) NOT NULL,
  `OldNickName` varchar(127) NOT NULL COMMENT '老昵称',
  `NewNickName` varchar(127) NOT NULL COMMENT '新昵称',
  `UpdateTime` bigint(20) NOT NULL,
  PRIMARY KEY (`ID`),
  KEY `userid_key` (`UserId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='昵称修改记录表';