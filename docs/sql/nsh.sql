DROP table `md_nsh_bind`;
CREATE TABLE `md_nsh_bind` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `UserId` bigint(20) DEFAULT NULL COMMENT '梦岛用户ID',
  `Accid` varchar(128)  NOT NULL COMMENT '云信用户ID',
  `BindTime` bigint(20) DEFAULT NULL COMMENT '绑定云信ID时间',
  `Token` varchar(255)  DEFAULT NULL COMMENT '云信的token',
  PRIMARY KEY (`ID`),
  KEY `userid` (`UserId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP table `nsh_team`;
CREATE TABLE `nsh_team` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `Tid` varchar(128) NOT NULL,
  `Name` varchar(128)  NOT NULL,
  `Location` varchar(50)  DEFAULT NULL COMMENT '群区域标示',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `tid` (`Tid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='逆水寒群';
