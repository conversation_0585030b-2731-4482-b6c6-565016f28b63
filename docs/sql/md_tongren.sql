-- ----------------------------
-- Table structure for md_tongren_user
-- ----------------------------
DROP TABLE IF EXISTS `md_tongren_user`;
CREATE TABLE `md_tongren_user` (
  `UserId` bigint(20) NOT NULL COMMENT '用户ID' PRIMARY KEY NOT NULL,
  `CurrentScore` bigint(20) DEFAULT 0 COMMENT '现有积分',
  `HistoryScore` bigint(20) DEFAULT 0 COMMENT '历史积分',
  `CreateTime` bigint(20) NOT NULL,
  `Extra` text COMMENT '可扩展的数据'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户列表';


-- ----------------------------
-- Table structure for md_tongren_works
-- ----------------------------
DROP TABLE IF EXISTS `md_tongren_works`;
CREATE TABLE `md_tongren_works` (
  `ID` bigint(20) AUTO_INCREMENT PRIMARY KEY NOT NULL,
  `Title` varchar(255) COMMENT '作品标题',
  `Description` varchar(255) COMMENT '作品说明',
  `CoverImageUrl` varchar(255) COMMENT '封面图片地址',
  `AttachmentList` text COMMENT '作品链接地址列表',
  `TagList` text COMMENT '标签列表',
  `Type` tinyint(4) NOT NULL COMMENT '作品类型：1 COS  2 视频  3 音频  4 小说  5 插画  6 漫画',
  `AuthorId` bigint(20) NOT NULL COMMENT '作者ID',
  `CreateTime` bigint(20) NOT NULL COMMENT '投稿时间',
  `IsTop` tinyint(4) COMMENT '置顶： 0 正常   1 置顶',
  `LikeCount` int(11) DEFAULT 0 COMMENT '点赞数',
  `CommentCount` int(11) DEFAULT 0 COMMENT '评论数',
  `Score` int(11) DEFAULT 0 COMMENT '分数',
  `ScoreComment` varchar(255) COMMENT '评语',
  `AuditAdminName` varchar(255) COMMENT '审核管理员',
  `Status` tinyint(4) DEFAULT 0 COMMENT '作品状态： 0 审核中  1 审核成功  -1 已删除  -2 审核不通过',
  `Hot` int(11) DEFAULT 0 COMMENT '热度',
  KEY `uid` (`AuthorId`),
  KEY `type_status` (`Type`, `Status`),
  KEY `title_dec` (`Title`, `Description`),
  KEY `hot` (`Hot`),
  KEY `createTime` (`CreateTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='作品列表';


-- ----------------------------
-- Table structure for md_tongren_like
-- ----------------------------
DROP TABLE IF EXISTS `md_tongren_like`;
CREATE TABLE `md_tongren_like` (
  `ID` bigint(20) AUTO_INCREMENT PRIMARY KEY NOT NULL,
  `UserId` bigint(20) NOT NULL COMMENT '用户ID',
  `AuthorId` bigint(20) NOT NULL COMMENT '作者ID',
  `WorkId` bigint(20) NOT NULL COMMENT '作品ID',
  `Status` bigint(20) DEFAULT 0 COMMENT '点赞状态：0 点赞  -1 取消点赞',
  `CreateTime` bigint(20) NOT NULL COMMENT '点赞时间',
  KEY `uid` (`UserId`),
  KEY `workId_status` (`WorkId`, `Status`),
  KEY `createTime` (`CreateTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='作品点赞列表';


-- ----------------------------
-- Table structure for md_tongren_comment
-- ----------------------------
DROP TABLE IF EXISTS `md_tongren_comment`;
CREATE TABLE `md_tongren_comment` (
  `ID` bigint(20) AUTO_INCREMENT PRIMARY KEY NOT NULL,
  `UserId` bigint(20) NOT NULL COMMENT '用户ID',
  `AuthorId` bigint(20) COMMENT '作者ID',
  `WorkId` bigint(20) NOT NULL COMMENT '作品ID',
  `ReplyId` bigint(20) COMMENT '回复的评论ID',
  `Text` varchar(255) NOT NULL COMMENT '评论内容',
  `CreateTime` bigint(20) NOT NULL COMMENT '评论时间',
  `Status` tinyint(4) DEFAULT 0 COMMENT '状态：0 正常  -1 已删除',
  KEY `uid` (`UserId`),
  KEY `workId_status` (`WorkId`, `Status`),
  KEY `createTime` (`CreateTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='评论列表';

-- ----------------------------
-- Table structure for md_tongren_score_record
-- ----------------------------
DROP TABLE IF EXISTS `md_tongren_score_record`;
CREATE TABLE `md_tongren_score_record` (
  `ID` bigint(20) AUTO_INCREMENT PRIMARY KEY NOT NULL,
  `UserId` bigint(20) NOT NULL COMMENT '用户ID',
  `WorkId` bigint(20) COMMENT '作品ID',
  `GoodId` bigint(20) COMMENT '商品ID',
  `DeltaScore` int(11) NOT NULL COMMENT '增减的积分',
  `CreateTime` bigint(20) NOT NULL,
  KEY `uid` (`UserId`),
  KEY `wid` (`WorkId`),
  KEY `gid` (`GoodId`),
  KEY `createTime` (`CreateTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='积分增减记录表';


-- ----------------------------
-- Table structure for md_tongren_goods
-- ----------------------------
DROP TABLE IF EXISTS `md_tongren_goods`;
CREATE TABLE `md_tongren_goods` (
  `ID` bigint(20) AUTO_INCREMENT PRIMARY KEY NOT NULL,
  `Type` tinyint(4) COMMENT '商品分类',
  `Description` varchar(255) COMMENT '商品说明',
  `ImageList` text COMMENT '商品图片列表',
  `Price` int(11) DEFAULT 0 COMMENT '商品现价',
  `OriginalPrice` int(11) COMMENT '商品原价',
  `TotalCount` int(11) DEFAULT 0 COMMENT '当前商品总量',
  `CreateTime` bigint(20) NOT NULL,
  KEY `price` (`Price`),
  KEY `count` (`TotalCount`),
  KEY `createTime` (`CreateTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商品列表';

-- ----------------------------
-- Table structure for md_tongren_good_record
-- ----------------------------
DROP TABLE IF EXISTS `md_tongren_good_record`;
CREATE TABLE `md_tongren_good_record` (
  `ID` bigint(20) AUTO_INCREMENT PRIMARY KEY NOT NULL,
  `GoodId` tinyint(4) NOT NULL COMMENT '商品ID',
  `DeltaCount` int(11) NOT NULL COMMENT '商品增减数量',
  `UserId` bigint(20) COMMENT '用户ID',
  `CreateTime` bigint(20) NOT NULL,
  KEY `goodId` (`GoodId`),
  KEY `createTime` (`CreateTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商品增减记录表';