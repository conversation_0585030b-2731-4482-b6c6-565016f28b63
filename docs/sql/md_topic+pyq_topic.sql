DROP TABLE IF EXISTS `md_topic`;
CREATE TABLE `md_topic` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `Subject` varchar(20) NOT NULL,
  `MomentList` text COMMENT '相关动态列表',
  `Hot` int(11) DEFAULT 0 COMMENT '话题热度',
  `CreateTime` bigint(20) NOT NULL,
  `UpdateTime` bigint(20),
  `Status` tinyint(4) DEFAULT 0 COMMENT '0 正常 -1 已删除',
  PRIMARY KEY (`ID`),
  KEY `Subject` (`Subject`),
  KEY `Hot` (`Hot`),
  KEY `UpdateTime` (`UpdateTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='朋友圈话题';

DROP TABLE IF EXISTS `pyq_topic`;
CREATE TABLE `pyq_topic` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `Subject` varchar(20) NOT NULL,
  `MomentList` text COMMENT '相关动态列表',
  `Hot` int(11) DEFAULT 0 COMMENT '话题热度',
  `CreateTime` bigint(20) NOT NULL,
  `UpdateTime` bigint(20),
  `Status` tinyint(4) DEFAULT 0 COMMENT '0 正常 -1 已删除',
  PRIMARY KEY (`ID`),
  KEY `Subject` (`Subject`),
  KEY `Hot` (`Hot`),
  KEY `UpdateTime` (`UpdateTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='朋友圈话题';
