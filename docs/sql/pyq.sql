
DROP table IF EXISTS `qnm_roleinfo`;
DROP table IF EXISTS `qnm_roleprop`;

-- 角色基本信息
CREATE TABLE `qnm_roleinfo` (
  `RoleId` bigint(20) NOT NULL COMMENT '角色ID',
  /*`UserId` bigint(20) DEFAULT 0,*/
  `UserName` varchar(127) COMMENT '玩家账号',
  `ServerId` int(11) COMMENT '服务器ID',
  `RoleName` varchar(63) COMMENT '角色昵称',
  `Gender` smallint(4) DEFAULT 0 COMMENT '性别',
  `Level` int(11) DEFAULT 0 COMMENT '等级',
  `JobId` smallint(6) DEFAULT 0 COMMENT '职业',
  `CreateTime` bigint(20) NOT NULL COMMENT '角色创建时间',
  `VIP` int(11) COMMENT 'VIP等级',
  `Title` varchar(127) COMMENT '称号',
  `TitleId` int(11) COMMENT '称号ID',
  `GangId` varchar(127),
  `Gang` varchar(63) COMMENT '帮会名称',
  `ImageUrl` varchar(255) COMMENT '形象图片地址',
  `State` smallint(6) DEFAULT 0 COMMENT '状态',
  `UpdateTime` bigint(20) NOT NULL COMMENT '资料最新更新时间',
  `Xiuwei` bigint(20) DEFAULT 0 COMMENT '修为等级',
  `Outfit` bigint(20) DEFAULT 0 COMMENT '装备总评分',
  `Money` bigint(20) DEFAULT 0 COMMENT '银两值',
  `bucketno` smallint(6),
  `FTRoleName` varchar(255) COMMENT '角色名分词',
  `SnsGender` char(1) COMMENT '社区性别',
  `OtherInfo` text,
  PRIMARY KEY (`RoleId`),
  KEY `UserName` (`UserName`),
  KEY `ServerId` (`ServerId`)/*,
  KEY `IDx_upt_udi` (`UpdateTime`,`UserId`)*/
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='玩家基本信息';

-- 角色装备信息
CREATE TABLE `qnm_roleprop` (
  `RoleId` bigint(20) PRIMARY KEY NOT NULL COMMENT '角色ID',
  `EquipInfo` text COMMENT '装备信息',
  `PropertyInfo` text COMMENT '基本属性',
  `OtherInfo` text COMMENT '抗性等其它属性',
  `PointInfo` text COMMENT '角色能力',
  `UpdateTime` bigint(20),
  `bucketno` int(11)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='玩家装备等信息';



DROP table IF EXISTS `pyq_profile`;
DROP table IF EXISTS `pyq_moment`;
DROP table IF EXISTS `pyq_comment`;
DROP table IF EXISTS `pyq_message`;
-- DROP table IF EXISTS `pyq_answer`;
DROP table IF EXISTS `pyq_event`;
DROP table IF EXISTS `pyq_tag`;


-- 角色朋友圈基本信息
CREATE TABLE `pyq_profile` (
  `RoleId` bigint(20) NOT NULL,
  `Location` varchar(50),
  `Signature` varchar(100) COMMENT '签名文字',
  `SignatureVoice` varchar(250) COMMENT '录音信息',
  `Photo` varchar(255) COMMENT '玩家真实头像',
  `ShowPhoto` tinyint(4) DEFAULT 1 COMMENT '默认显示玩家真实头像',
  `PhotoAudit` tinyint(4) DEFAULT 0 COMMENT '头像审核状态：0 未审核 1 通过 -1 不通过',
  `RenQi` bigint(20) DEFAULT 0 COMMENT '人气值',
  `Gift` bigint(20) DEFAULT 0 COMMENT '礼物数',
  `Flower` bigint(20) DEFAULT 0 COMMENT '获得鲜花数',
  `FlowerRenQi` bigint(20) DEFAULT 0 COMMENT '鲜花累计人气',
  `SendFlowerRenQi` int(11) DEFAULT 0 COMMENT '送出的鲜花人气',
  `Privacy` varchar(50) COMMENT '空间隐私设置（地理位置,空间,曾用名）',
  `BanState` varchar(300) COMMENT '空间违禁状态（发状态+回复，留言，个性签名）',
  `FriendList` text COMMENT '朋友列表',
  `HideList` text COMMENT '屏蔽的用户列表',
  `BlackList` text COMMENT '黑名单用户列表',
  `TagList` text COMMENT '个性标签列表',
  `ShiFu` varchar(5000),
  `TuDi` varchar(5000),
  `Wedding` varchar(5000),
  `UpdateTime` bigint(20),
  `LastLogin` bigint(20) COMMENT '上次登录时间',
  PRIMARY KEY (`RoleId`),
  KEY `SendFlowerRenQi` (`SendFlowerRenQi`),
  KEY `FlowerRenQi` (`FlowerRenQi`),
  KEY `RenQi` (`RenQi`),
  KEY `Gift` (`Gift`),
  KEY `Flower` (`Flower`),
  KEY `UpdateTime` (`UpdateTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- 角色朋友圈动态
CREATE TABLE `pyq_moment` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `RoleId` bigint(20) NOT NULL,
  `Text` varchar(200),
  `ImgList` text COMMENT '图片列表',
  `ImgAudit` text COMMENT '图片审核状态：0 未审核 1 通过 -1 不通过',
  `VideoList` text COMMENT '视频列表',
  `ZanList` text COMMENT '点赞列表',
  `CreateTime` bigint(20) NOT NULL,
  `Status` tinyint(4) DEFAULT 0 COMMENT '动态状态：0 正常 -1 已删除'
  PRIMARY KEY (`ID`),
  KEY `RoleId` (`RoleId`),
  KEY `CreateTime` (`CreateTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='角色朋友圈动态';

-- 评论
CREATE TABLE `pyq_comment` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `RoleId` bigint(20) NOT NULL,
  `TargetId` bigint(20) NOT NULL COMMENT '评论对象ID',
  `ReplyId` bigint(20) COMMENT '回复对象ID',
  `Text` varchar(200) COMMENT '评论内容',
  `CreateTime` bigint(20) NOT NULL,
  `Status` tinyint(4) DEFAULT 0 COMMENT '评论状态：0 正常 -1 已删除'
  `Type` tinyint(4) DEFAULT '0' COMMENT '评论类型',
  PRIMARY KEY (`ID`),
  KEY `RoleId` (`RoleId`),
  KEY `TargetId` (`TargetId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='评论';

-- 留言
CREATE TABLE `pyq_message` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `RoleId` bigint(20) NOT NULL,
  `TargetId` bigint(20) NOT NULL COMMENT '留言对象ID',
  `ReplyId` bigint(20) COMMENT '回复对象ID',
  `Text` varchar(200) COMMENT '留言内容',
  `CreateTime` bigint(20) NOT NULL,
  `Status` tinyint(4) DEFAULT 0 COMMENT '留言状态：0 未读 1 已读 -1已删除'
  PRIMARY KEY (`ID`),
  KEY `target-status` (`TargetId`,`Status`),
  KEY `target-status-time` (`TargetId`,`Status`,`CreateTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='空间留言';

-- 留言回复
/* CREATE TABLE `pyq_answer` (
  `ID` bigint(20) AUTO_INCREMENT PRIMARY KEY NOT NULL,
  `RoleId` bigint(20) NOT NULL,
  `TargetId` bigint(20) NOT NULL COMMENT '回复留言ID',
  `ReplyId` bigint(20) COMMENT '回复对象ID',
  `Text` varchar(200) COMMENT '回复内容',
  `CreateTime` bigint(20) NOT NULL,
  `Status` tinyint(4) DEFAULT 0 COMMENT '回复状态：0 正常 -1 已删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='留言回复'; */

-- 送礼物等事件
CREATE TABLE `pyq_event` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `RoleId` bigint(20) NOT NULL,
  `TargetId` bigint(20) NOT NULL COMMENT '对象ID',
  `Type` tinyint(4) COMMENT '事件类型：1 踩空间 2 获得礼物 3 送鲜花',
  `Parameter` varchar(255) COMMENT '事件参数',
  `Num` bigint(20) DEFAULT 1 COMMENT '物品数量',
  `RenQi` bigint(20) DEFAULT 0 COMMENT '物品人气',
  `PublishTime` bigint(20) NOT NULL,
  PRIMARY KEY (`ID`),
  KEY `PublishTime` (`PublishTime`),
  KEY `RoleId` (`RoleId`),
  KEY `TargetId-Type` (`TargetId`,`Type`,`PublishTime`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='朋友圈送花等事件';

/*-- 个性标签
CREATE TABLE `pyq_tag` (
  `ID` int PRIMARY KEY NOT NULL,
  `Text` varchar(20),
  `Type` varchar(20) COMMENT '标签分类',
  `Status` tinyint(4)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;*/



DROP table IF EXISTS `nos_file_usage`;

-- NOS文件上传使用记录
CREATE TABLE `nos_file_usage` (
  `Url` varchar(255) PRIMARY KEY COMMENT '文件地址',
  `TargetId` bigint(20) COMMENT '使用对象ID',
  `UseType` varchar(50) COMMENT '使用方式',
  `Status` tinyint(4) DEFAULT 0 COMMENT '使用状态：0 新增 1 使用中 -1 已过期',
  `CreateTime` bigint(20),
  `BindTime` bigint(20)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='文件上传记录';


DROP table IF EXISTS `qnm_roleevent`;
CREATE TABLE `qnm_roleevent` (
  `ID` bigint(20) AUTO_INCREMENT NOT NULL,
  `RoleId` bigint(20) NOT NULL COMMENT '角色ID',
  `ServerId` int(11) COMMENT '服务器ID',
  `RelateId` bigint(20) COMMENT '更新时的关联用ID',
  `Type` tinyint(4) COMMENT '事件类型',
  `Parameter` varchar(255) COMMENT '事件参数',
  `Priority` tinyint(4) COMMENT '是否为大事件',
  `PublishTime` bigint(20),
  `bucketno` varchar(255),
  PRIMARY KEY (`ID`),
  KEY `RoleId` (`RoleId`),
  KEY `ServerId` (`ServerId`),
  KEY `Type` (`Type`),
  KEY `Priority` (`Priority`),
  KEY `Update_Key` (`RoleId`, `RelateId`, `Type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS `qnm_transact_histories`;
CREATE TABLE `qnm_transact_histories` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `RoleId` bigint(20) NOT NULL,
  `RoleName` varchar(63) DEFAULT NULL,
  `TransactTime` bigint(20) DEFAULT NULL COMMENT '充值时间',
  `ProductId` varchar(100) CHARACTER SET latin1 DEFAULT NULL,
  `Status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '-1 充值失败',
  PRIMARY KEY (`ID`),
  KEY `roleid` (`RoleId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;