DROP TABLE IF EXISTS `pyq_inform`;
CREATE TABLE `pyq_inform` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `RoleId` bigint(20) NOT NULL,
  `TargetId` bigint(20) NOT NULL COMMENT '通知接收者ID',
  /*`RelyId` bigint(20) COMMENT '回复者ID',*/
  `ObjectId` bigint(20) COMMENT '操作对象ID',
  `RelateId` varchar(50) COMMENT '关联ID',
  `Text` varchar(200) COMMENT '消息内容',
  `Type` tinyint(4) NOT NULL COMMENT '消息类别',
  `Status` tinyint(4) DEFAULT 0 COMMENT '消息状态：0 未读 1 已读',
  `CreateTime` bigint(20) NOT NULL,
  PRIMARY KEY (`ID`),
  KEY (`TargetId`),
  KEY (`ObjectId`),
  KEY (`RelateId`),
  KEY `target_status` (`TargetId`,`Status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='系统消息';


ALTER TABLE `pyq_message` ADD COLUMN `OwnerId` bigint(20) COMMENT '空间主人ID' AFTER `ReplyId`;