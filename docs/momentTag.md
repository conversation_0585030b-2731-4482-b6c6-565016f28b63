## 问题背景
我观察了一下，从标签功能上线到现在，标签热门数据筛选后好像一直都满不了20页，比如现在我筛选了美拍、时装、捏脸、染色，到第10页就空了。昨天发布的一些动态能上第九页。

## 问题分析
现在热度公式1天内就会把热度衰减到0，上热门是需要热度大于0的，这个用在全服热门那里可以，但是用在标签这里，符合标签的内容如果数量不够多按照1天就衰减完就会出现空的情况

我的想法是，调整下热度公式，让热度到3天内才衰减到0， 但是为了保障全服热门的时效性（全服热门还是需限制近24小时内的动态，按照热度排序，避免长时间占据位置）

## 期望方案
策划方案:
1. 热门不带tag的，用最近24h的，86400 带tag的，86400 * 5 （昨天说的*3盘子觉得太短了）
2. 带tag了，保持原样，需要3天内过期掉

实现方案:

直接修改公式，全服热门公式修改, 如果是不带tag是1天过期，带上tag是5天过期

```mermaid
flowchart TD
    A[开始] --> B{是否有tagId?}
    B -- 是 --> C[计算minCreateTime: endOfDay - hotExpireMaxSeconds]
    B -- 否 --> D[计算minCreateTime: endOfDay - hotExpireMaxSecondsForNoTag]
    C --> E[设置hotCategory为'tag']
    D --> F[设置hotCategory为'noTag']
    E --> G{tagId >= 0?}
    F --> G
    G -- 是 --> H[获取minId: getHotMomentMinIdGetCreateTime]
    H --> I[调用MomentTagService.getHotMomentsByServerIdAndTagIdHandler]
    G -- 否 --> J[构建查询: 选择ID, RoleId, Text等字段]
    J --> K[添加CreateTime和Hot的过滤条件]
    K --> L[按Hot和ID降序排序]
    L --> M{serverId是否为'all'?}
    M -- 否 --> N[添加serverId过滤条件]
    N --> O[执行查询]
    M -- 是 --> O
    O --> P[返回查询结果]
    I --> P
    P --> Q[结束]
```