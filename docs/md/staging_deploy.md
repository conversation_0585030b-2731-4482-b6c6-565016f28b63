## 预发布环境


### 目前推送数据接口 gdc/qnm.do 游戏内依旧使用的是 sites-island-gdc3 (**************)机器


文件地址:  /etc/nginx/sites-enabled/pyq-master#L36

配置需要变更

```
  当前:

  location /gdc/qnm.do {
    access_log /srv/logs/nginx/hi-gdc-qnm-test.access.log post;
    error_log /srv/logs/nginx/hi-gdc-qnm.error.log;
    proxy_set_header Host l10-md-online-rc.apps-hp.danlu.netease.com;
    proxy_pass http://l10-md-online-rc.apps-hp.danlu.netease.com;
  }

  变更为:

  location /gdc/qnm.do {
    access_log /srv/logs/nginx/hi-gdc-qnm-test.access.log post;
    error_log /srv/logs/nginx/hi-gdc-qnm.error.log;
    proxy_set_header Host qnm-gdc-rc.apps-hp.danlu.netease.com;
    proxy_pass http://qnm-gdc-rc.apps-hp.danlu.netease.com；
  }

```