var fs = require('fs'),
    readline = require('readline'),
    iconv = require('iconv-lite');

var inputFile = '论坛用户名关键词.txt',
    outputFile = '../../service/app/data/NameRegList.js';
function processFile(file) {
    var dataList = [];
    var rl = readline.createInterface({
        input: fs.createReadStream(inputFile),
        output: process.stdout,
        terminal: false
    });
    rl.on('line', function(line) {
        var expr = line && line.replace(/\*/g, '.*');
        expr && dataList.push(expr);
    });
    rl.on('close', function() {
        fs.writeFile(outputFile, 'var list=[\r\n"' + dataList.join('",\r\n"') + '"\r\n];\r\nmodule.exports=list;', function(err) {
            console.log('Write done!');
        });
    });
}

processFile(inputFile);