* 备注：在线API文档地址：http://192.168.131.156:4000/public/apidoc/index.html#api-Tongren


1. 获取个人主页信息
/md/tongren/home
请求参数
    targetid    所查看用户的ID(可选)
    page    页码
    page_size    分页大小（默认20）
    work_type    作品分类（默认1）：1 COS  2 视频  3 音频  4 小说  5 插画  6 漫画

返回
{
    code: 0,
    data: {
        ID    当前登录用户的ID
        NickName    昵称
        Avatar    头像图片
        AvaAuthStatus    头像审核状态
        Gender    性别
        GenderStatus    单身状态
        FollowType    关注状态
        Status    认证状态
        RealityShow    真人秀状态
        UserType    用户类型： 1 二次元

        RoleInfo: {    // 主角色信息
            CreateTime
            Gender
            JobId
            RoleId
            RoleName
            ServerId
            Level
            GangId
            Gang
        }
        WorkList: [{...}]   // COS作品信息列表
    }
}


2. 添加投稿
/md/tongren/edit
请求参数
    title    标题(必填)（最多15字）
    description    描述（最多60字）
    cover_image_url    封面图
    attachment_list    作品链接数组（必填，数组大小：1~9）
    tag_list    标签列表（字符串，逗号分隔）
    type    作品分类(必填)：1 COS  2 视频  3 音频  4 小说  5 插画  6 漫画
返回
{
    code: 0,
    msg: "上传成功，请耐心等待审核"
}

3. 获取作品列表
/md/tongren/list
请求参数
    page    第几页
    page_size    分页大小
    sort_by    筛选条件：new 最新  hot 最热
    work_type    作品分类： 1 COS  2 视频  3 音频  4 小说  5 插画  6 漫画
    author_id    用户ID
返回
{
    code: 0,
    data: {
        list: [{  作品列表
            ID    作品ID
            Type    作品分类：1 COS  2 视频  3 音频  4 小说  5 插画  6 漫画
            Title    标题
            Description    作品描述
            CoverImageUrl    封面图
            AttachmentList    作品链接
            LikeCount    点赞数
            CommentCount    评论数
            TagList    标签列表
            CreateTime    作品创建时间
            Duration    时间间隔
            Hot    热度
            Status    作品审核状态：0 审核中  1 审核成功  -1 已删除  -2 审核失败
            AllowLike    是否允许点赞
            FollowType    关注状态： -1 未关注   0 关注我   1 我关注他  2 相互关注  3 我自己

            AuthorAvatar    作者头像
            AuthorNickName    作者昵称
            AuthorId    作者ID
        }, {...}, ...]
    }
}

4. 获取作品详情信息
/md/tongren/work/:id
请求参数
    无
返回
{
    code: 0,
    data: {
        ID    作品ID
        Type    作品分类：1 COS  2 视频  3 音频  4 小说  5 插画  6 漫画
        Title    标题
        Description    作品描述
        CoverImageUrl    封面图
        AttachmentList    作品链接
        LikeCount    点赞数
        CommentCount    评论数
        TagList    标签列表
        CreateTime    作品创建时间
        Duration    时间间隔
        AuthorId    作者ID
        Hot    热度
        Status   作品审核状态
        RealityShow    真人秀状态：-1 审核拒绝  0 审核中  1 审核通过  2 首页推荐
        AllowLike    是否允许点赞
        FollowType    关注状态： -1 未关注   0 关注我   1 我关注他  2 相互关注  3 我自己
        AuthorInfo: {   // 作者信息
            ID    作者ID
            NickName   作者昵称
            Avatar   作者头像
            Gender    性别
            GenderStatus    单身状态
            Status    作者审核状态
            UserType    用户类型： 1 二次元
        }
        CommentList: {  评论列表
            ...  （同评论列表）
        }
        LikeList: [{    // 点赞用户列表
            ID    用户ID
            Avatar     头像
            AvaAuthStatus
            NickName    昵称
        }, {...}, ...]
    }
}

5. 点赞
/md/tongren/like
请求参数
    work_id    作品ID

返回
{
    code: 0,
    msg: "点赞成功",
    data: {
        userInfo: {    用户信息
            ID    ID
            Avatar    头像
            NickName    昵称
            AvaAuthStatus
        }
    }
}


6. 取消点赞
/md/tongren/unlike
请求参数
    work_id    作品ID

返回
{
    code: 0,
    msg: "已取消点赞",
    data: {
        userInfo: {    用户信息
            ID    ID
            Avatar    头像
            NickName    昵称
        }
    }
}

7. 获取评论(默认取10条)
/md/tongren/comment/get
请求参数
    work_id    作品ID

返回
{
    code: 0,
    data: {
        list: [{  评论列表
            ID    评论ID
            Text    评论内容
            UserId    评论者ID
            TargetId    回复的评论ID
            ReplyId    回复者的ID
            AuthorId    作品的作者ID
            CreateTime    评论时间（毫秒数）
            Duration    时间间隔（毫秒数）
            AllowDelete    是否允许当前登录用户删除评论

            ReplyUserInfo: {    回复者信息
                ID    ID
                NickName    昵称
                Avatar    头像
            },
            UserInfo: { ... }    评论者信息（与回复者信息结构相同）
        }, {...}, ...]
    }
}

8. 添加评论
/md/tongren/comment/add
请求参数
    work_id    作品ID
    reply_id    回复的评论ID
    content    评论内容

返回
{
    code: 0,
    data: {
        ID    评论ID
        Text    评论内容
        UserId    评论者ID
        TargetId    回复的评论ID
        ReplyId    回复者的ID
        AuthorId    作品的作者ID
        CreateTime    评论时间（毫秒数）
        Duration    时间间隔（毫秒数）
        AllowDelete    是否允许当前登录用户删除评论

        ReplyUserInfo: {    回复者信息
            ID    ID
            NickName    昵称
            Avatar    头像
        },
        UserInfo: { ... }    评论者信息（与回复者信息结构相同）
    }
}

9. 删除评论
/md/tongren/comment/delete
请求参数
    comment_id    评论ID

返回
{
    code: 0,
    data: {
        msg: "已删除"
    }
}


10. 作品搜索
/md/tongren/search
请求参数
    keyword    关键词（可以是作者、标题、说明）
    page    页码
    page_size   分页大小(默认20)
    work_type    作品分类：1 COS  2 视频  3 音频  4 小说  5 插画  6 漫画
    keyword_type   关键词类型：1 全部  2 作品  3 作者
返回
{
    code: 0,
    data: {
        list: [{  作品列表
            ID    作品ID
            Type    作品分类：1 COS  2 视频  3 音频  4 小说  5 插画  6 漫画
            Title    标题
            Description    作品描述
            CoverImageUrl    封面图
            AttachmentList    作品链接
            LikeCount    点赞数
            CommentCount    评论数
            TagList    标签列表
            CreateTime    作品创建时间
            Duration    时间间隔
            Hot    热度
            Status    作品审核状态：0 审核中  1 审核成功  -1 已删除  -2 审核失败
            FollowType   关注状态

            AllowLike    是否允许点赞

            AuthorAvatar    作者头像
            AuthorNickName    作者昵称
            AuthorId    作者ID

            LikeList: [{    // 点赞用户列表
                ID    用户ID
                Avatar     头像
                NickName    昵称
            }, {...}, ...]
        }, {...}, ...]
    }
}

11. 作品审核
/md/tongren/audit
请求参数
    work_id    作品ID(必填)
    score    分数
    comment    评语（最多250字）
    admin_name    管理员名字
    cover_image_url   封面地址
    attachment_list    作品链接数组（数组大小：1~9，必填）
    status    作品状态： 1 审核成功  -2 审核不通过
    top    置顶：0 正常  1 置顶
返回
{
    code: 0,
    msg: "已提交"
}

12. 删除作品（仅允许删除正在审核中的作品）
/md/tongren/work/:id/delete
请求参数
    无
返回
{
    code: 0,
   data: {
        msg: "已删除"
    }
}

13. 获取本月的积分比例和投稿次数（本月范围：上月21日~本月20日）
/md/tongren/score_ratio
请求参数
    无
返回
{
    code: 0,
    data: {
        UserId    用户ID
        ContributeTimes    本月投稿次数
        LastMonth    本月开始时间，毫秒数（上月21日）
        ThisMonth    本月结束时间，毫秒数（本月20日）
        UserInfo: {}   用户信息
    }
}