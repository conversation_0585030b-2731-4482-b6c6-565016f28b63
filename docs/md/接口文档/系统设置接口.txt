1、获取用户信息
/md/getuserinfo
请求参数
	targetid    指定查询他人信息
	
返回
{code:0,
 data: {
	UserId		用户ID
	NickName	昵称
	Signature	个性签名
	Avatar		头像图片
}

2、设置用户信息
/md/setuserinfo
请求参数
	signature
	nickname
	avatar
返回
{code:0,
 data: $insertedId/0
}

3、获取绑定角色信息
/md/qnm/getbindrole
请求参数
	-

返回
{code:0,
 data:[	 	// code为0即说明更新成功
   RoleId
   RoleName
   ServerId
   Server
   Gender
   Level
   JobId
   Job
   Gang
   GangId
   BindType  1 已绑定 2 绑定主角色 undefined 未绑定
]}

4、绑定角色
/md/qnm/bindrole
请求参数
	roleid		绑定单个角色ID，与roleidlist指定一个即可
	roleidlist	绑定角色ID列表
	mainroleid	主角色ID（可选）

返回
{code:0,
 data: 'ok'
}

5、设置主角色
/md/qnm/setmainrole
请求参数
	roleid		主角色ID

返回
{code:0,
 data: 'ok'
}

6、昵称唯一性校验
/md/checkname
请求参数
	nickname		昵称

返回
{code:0,
 data: 1/0      1表示该昵称已存在，不可用； 0表示可用
}

7、解绑角色
/md/qnm/unbindrole
请求参数
	roleid	解绑角色ID

返回
{code:0,
 data: 'ok'
}

8、获取用户基本信息
/md/user/get_basic_info
请求参数
    userid    用户ID
返回
{code:0,
 data: {
	UserId		   用户ID
	NickName	   昵称
	Signature	   个性签名
	Avatar		   头像图片
	AvaAuthStatus  头像审核状态
}