ps： 所有返回数据中code为-1时 返回错误信息
	{
		code: -1,
		msg: "参数格式错误!"
	}

1. 添加新鲜事
url: http://md-test.163.com:3003/md/qnm/addmoment
请求： 
	{
		text,//可传 字符串形式
		imglist,//可传 数组形式 元素为imgurl字符串
		videolist,//可传 功能暂时未开通
	}
	ps: text 与 imglist 至少传一个
返回：
	{
		code: 0,
		data: {
			momentid,//添加的新鲜事id
			msg//信息
		}
	}

2. 删除新鲜事
url: http://md-test.163.com:3003/md/qnm/delmoment
请求： 
	{
		mid//新鲜事id
	}
返回： 
	{
		code: 0,
		data: '成功'
	}

3. 获取好友新鲜事
url: http://md-test.163.com:3003/md/qnm/getmoments
请求：
	分页请求 
	{
		lastid//上次获取新鲜事最后一条新鲜事的id 第一次获取可不传
	}
返回： 
	{
		code: 0,
		data: [
			{
				"ID",//新鲜事id
				"UserId",//新鲜事发布者userid
				"Text",//新鲜事文字内容
				"ImgList":[],//新鲜事图片内容
				"ImgAudit":[],//图片审核结果
				"VideoList",//新鲜事视频内容 暂未开通
				"ZanList": [],//新鲜事点赞者的userid
				"CreateTime",//新鲜事发布时间
				"Hot",//新鲜事热度值
				"duration",//当前时间与新鲜事发布时间的时间差
				"zanlist": [
					{
						"zanUserid",//点赞者userid
						"userInfo": {//点赞者信息
							"ID",//点赞者userid
							"NickName",//点赞者昵称
							"Avatar",//点赞者头像
							"AvaAuthStatus"//点赞者头像审核结果
						}
					}
				],
				"userInfo": {//发新鲜事者信息
					"ID",//发新鲜事者userid
					"NickName",//发新鲜事者昵称
					"Avatar",//发新鲜事者头像
					"AvaAuthStatus"//发新鲜事者头像审核结果
				},
				"comsNum",//该条新鲜事评论数
				"Type"//该新鲜事发布者与自己的关系 0 自己未关注 1 自己已关注 2 是自己发的
			}
		]
	}

4. 获取热门新鲜事
url: http://md-test.163.com:3003/md/qnm/gethotmoments
请求：
	分页请求 
	{
		page//默认为0
	}
返回： 
	{
		code: 0,
		data: [
			{
				"ID",//新鲜事id
				"UserId",//新鲜事发布者userid
				"Text",//新鲜事文字内容
				"ImgList":[],//新鲜事图片内容
				"ImgAudit":[],//图片审核结果
				"VideoList",//新鲜事视频内容 暂未开通
				"ZanList": [],//新鲜事点赞者的userid
				"CreateTime",//新鲜事发布时间
				"Hot",//新鲜事热度值
				"duration",//当前时间与新鲜事发布时间的时间差
				"zanlist": [
					{
						"zanUserid",//点赞者userid
						"userInfo": {//点赞者信息
							"ID",//点赞者userid
							"NickName",//点赞者昵称
							"Avatar",//点赞者头像
							"AvaAuthStatus"//点赞者头像审核结果
						}
					}
				],
				"userInfo": {//发新鲜事者信息
					"ID",//发新鲜事者userid
					"NickName",//发新鲜事者昵称
					"Avatar",//发新鲜事者头像
					"AvaAuthStatus"//发新鲜事者头像审核结果
				},
				"IsTop",//0 非置顶新鲜事 按值大小置顶级别排列
				"comsNum",//该条新鲜事评论数
				"Type"//该新鲜事发布者与自己的关系 0 自己未关注 1 自己已关注 2 是自己发的
			}
		]
	}

5. 获取单条新鲜事
url: http://md-test.163.com:3003/md/qnm/getmoment
请求：
	data:
	{
		targetid//目标新鲜事id
	}

返回： 
	{
		code: 0,
		data: [
			{
				"ID",//新鲜事id
				"UserId",//新鲜事发布者userid
				"Text",//新鲜事文字内容
				"ImgList":[],//新鲜事图片内容
				"ImgAudit":[],//图片审核结果
				"VideoList",//新鲜事视频内容 暂未开通
				"ZanList": [],//新鲜事点赞者的userid
				"CreateTime",//新鲜事发布时间
				"Hot",//新鲜事热度值
				"duration",//当前时间与新鲜事发布时间的时间差
				"zanlist": [
					{
						"zanUserid",//点赞者userid
						"userInfo": {//点赞者信息
							"ID",//点赞者userid
							"NickName",//点赞者昵称
							"Avatar",//点赞者头像
							"AvaAuthStatus"//点赞者头像审核结果
						}
					}
				],
				"userInfo": {//发新鲜事者信息
					"ID",//发新鲜事者userid
					"NickName",//发新鲜事者昵称
					"Avatar",//发新鲜事者头像
					"AvaAuthStatus"//发新鲜事者头像审核结果
				},
				"Type"//该新鲜事发布者与自己的关系 0 自己未关注 1 自己已关注 2 是自己发的
				"commlist": {
					"commlist": [
						{
							"ID",//评论id
							"UserId",//评论人的userid
							"ReplyId",//为null 表示该条评论为评论新鲜事。非null 表示该条评论为回复评论。被回复者userid
							"TargetId",//新鲜事id
							"Text",//评论内容
							"CreateTime",//评论时间
							"userInfo": {//评论者信息
								"ID",
								"NickName",
								"Avatar",
								"AvaAuthStatus"
							},
							"replyUserInfo": {//被回复者的信息
								"ID",
								"NickName",
								"Avatar",
								"AvaAuthStatus"
							}
						}
					],
					"comsNum": 7
				}
			}
		]
	}
	
6. 获取新鲜事后面的评论 分页请求
url: http://md-test.163.com:3003/md/qnm/getcomosfmom
请求：
	{
		targetid,//新鲜事id
		page//默认为0
	}
返回:
	{
		"commlist": {
			"commlist": [
				{
					"ID",//评论id
					"UserId",//评论人的userid
					"ReplyId",//为null 表示该条评论为评论新鲜事。非null 表示该条评论为回复评论。被回复者userid
					"TargetId",//新鲜事id
					"Text",//评论内容
					"CreateTime",//评论时间
					"userInfo": {//评论者信息
						"ID",
						"NickName",
						"Avatar",
						"AvaAuthStatus"
					},
					"replyUserInfo": {//被回复者的信息
						"ID",
						"NickName",
						"Avatar",
						"AvaAuthStatus"
					}
				}
			],
			"comsNum": 7
		}
	}
	

7. 对新鲜事点赞
url: http://md-test.163.com:3003/md/qnm/setzan
请求： 
	data:{
		mid: 1,
		type: 1
	}
返回： 
	{
		code: 0,
		data: "成功！"
	}

8. 对新鲜事取消点赞
url: http://md-test.163.com:3003/md/qnm/setzan
请求： 
	data:{
		mid: 1,
		type: 0
	}
返回： 
	{
		code: 0,
		data: "成功！"
	}

9. 给新鲜事加评论
url: http://md-test.163.com:3003/md/qnm/addcomm
请求： 
	{
		targetid,//新鲜事id
		text//评论内容
	}
返回：
	{
		code: 0, 
		{
			commentid,//所添加的评论的id
			msg: '成功'
		}
	}

10. 删除评论
url: http://md-test.163.com:3003/md/qnm/delcomm
请求： 
	{
		commentid//评论id
	}
返回： 
	{
		code: 0,
		data: "删除评论成功！"
	}