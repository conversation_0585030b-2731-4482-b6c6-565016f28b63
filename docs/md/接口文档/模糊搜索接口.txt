1. 获取搜索选项
    url: http://md-test.163.com:3003/md/qnm/fuzzysearch
    请求：
        无
    返回：
        {
            jobList: [
                {
                    id,//职业id
                    name//职业名称
                }
            ],
            serverList: [
                {
                    group,//大区名称
                    servers:[//大区下的服务器列表
                        {
                            id,//服务器id
                            name//服务器名称
                        }
                    ]
                }
            ]
        }
2. 进行搜索
    url: http://md-test.163.com:3003/md/qnm/fuzzysearch
    请求：
        {
            serverid,//（必传）服务器
            jobid,//（可传，可多传）1 || [2,3]
            levelrangeid//（可传，可多传）1 || [0,1]
        }
    返回： 
        [
            {
                Avatar,//头像
                Job,//职业
                JobId,//职业id
                NickName,//梦岛昵称
                RoleId,//倩女角色id
                RoleName,//角色名称
                ServerId,//所在服务器id
                UserId,//梦岛id
                UserName,//梦岛账号
                Level,//角色等级
                Server,//服务器名称
                ServerGroup//大区
                Type//0 关注我的 1 我关注的  2 互相关注的。没有任何关系时没有该字段
            }
        ]
3. 关键字模糊搜人
    url: http://md-test.163.com:3003/md/qnm/kwsearch
    请求参数：
        {
            type: 0,//关键字搜索的类型 0 梦岛昵称搜索 1 角色名称搜索
            keyword: ''//关键字
        }
    返回：
        梦岛昵称搜索： 
        [
            {
                AvaAuthStatus,
                Avatar,
                NickName,
                Type,//与自己的关系 0 该用户关注了自己 1 自己关注了他 2 互相关注 3 自己
                UserId,
                mainRoleInfo:{//主角色信息 没有表示这个人未绑定主角色
                    Job,
                    JobId,
                    Level,
                    RoleId,
                    RoleName,
                    UserId
                }
            }
        ]
        角色名称搜索：
        [
            {
                AvaAuthStatus,
                Avatar,
                NickName,
                Type,//与自己的关系 0 该用户关注了自己 1 自己关注了他 2 互相关注 3 自己
                UserId,
                roleInfo:{//角色信息 没有表示这个人未绑定主角色
                    Job,
                    JobId,
                    Level,
                    RoleId,
                    RoleName,
                    UserId
                }
            }
        ]