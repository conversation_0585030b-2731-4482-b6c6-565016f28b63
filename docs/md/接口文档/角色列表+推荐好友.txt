2. 获取urs角色列表
url: http://md-test.163.com:3003/md/qnm/listchar

请求： 无

返回： 
正确：	code为0,返回正确数据,data为数组列表:
	{ "code": 0, 
	  "data": [{ 	
	  	"RoleId", //角色id
		"ServerId",// 服务器id
		"RoleName",// 角色名
		"Gender",// 性别
		"JobId",// 角色职业id
		"Job",//角色职业名称
		"CreateTime",// 角色创建时间
		"ServerGroup",// 服务大区
		"Server"// 服务器名称
	}
	



3. 获取所绑定角色在游戏中的好友对应的梦岛账号信息
url: http://md-test.163.com:3003/md/qnm/recommendFriList

传递参数：
	roleids:[1,2,3]//roleIds为roleId数组

接受参数： 
正确：	code为0,返回正确数据,data为数组列表；
	{ code: 0, 
	  data: [{ 
	  	ID: 8, 
	   	Username: "<EMAIL>",  
	    NickName: "测试测试8",  
	    Signature: "个性签名88888888个性签名" 
	    }] 
	}
错误：	code为0,返回错误数据data
	{
		code: 0,
		data: {
			code: -1,
			msg: "参数格式错误!"
		}
	}