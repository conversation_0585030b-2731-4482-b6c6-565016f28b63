1、添加留言
    url: http://md-test.163.com:3003/md/qnm/addmsg
请求：
    {         
        targetid,//被留言的userid
        text,//留言内容    
    }
返回：
    messageid//留言id

2、删除留言
    url: http://md-test.163.com:3003/md/qnm/delmsg
请求：
    {
        messageid: 2,
        userid: 6
    }
返回
    data//删除成功！

3、回复留言
    url: http://md-test.163.com:3003/md/qnm/ansmsg
请求：
    {
        messageid,
        userid,
        replyid,
        text
    }
返回： 
    {
        answerid//留言回复id,
        msg
    }

4、获取留言
    url: http://md-test.163.com:3003/md/qnm/getmsgs
请求：
    {
        targetid,//选传 获取该用户的id，默认为获取本人的留言
        lastid,//选传 默认为0
        pagesize,//选传 默认为10
    }
返回:
    [{
        "ID": ,//messageid
        "UserId": ,//发留言的用户id
        "TargetId": ,//接收留言的用户id
        "Text": ,//留言内容
        "CreateTime": ,//留言创建时间
        "duration": ,//留言距离目前的时间差
        "userInfo": {//发留言的用户信息
            "ID": ,
            "NickName": ,
            "AvaAuthStatus": ,
            "Avatar": 
        },
        "answerNum": //该条留言的回复数目
    }]

5、获取一条留言
    url: http://md-test.163.com:3003/md/qnm/getmsg
请求：
    {
        messageid
    }
返回:
    [{
        "ID": ,//messageid
        "UserId": ,//发留言的用户id
        "TargetId": ,//接收留言的用户id
        "Text": ,//留言内容
        "CreateTime": ,//留言创建时间
        "duration": ,//留言距离目前的时间差
        "userInfo": {//发留言的用户信息
            "ID",
            "NickName",
            "AvaAuthStatus",
            "Avatar"
        },
        "answerNum",//该条留言的回复数目
        "anslist": {
            "anslist": [//留言回复列表
                {
                    "ID",//回复id
                    "UserId",//回复者用户id
                    "ReplyId",//被回复者用户id
                    "TargetId",//目标留言id
                    "Text",//回复内容
                    "CreateTime",//回复创建时间
                    "userInfo": {//回复者用户信息
                        "ID",
                        "NickName",
                        "AvaAuthStatus",
                        "Avatar"
                    },
                    "replyUserInfo": {//被回复者用户信息
                        "ID",
                        "NickName",
                        "AvaAuthStatus",
                        "Avatar"
                    }
                }
            ],
            "answerNum"//该条留言的回复数目
        }
    }]

6、获取某人的留言数目
    url: http://md-test.163.com:3003/md/qnm/getmsgsnum
请求： 
    targetid: 选传，不传默认为获取当前账号的留言数目
返回：
    data: 28//数目



