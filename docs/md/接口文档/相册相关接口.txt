相册API:
1 获取用户所有相册
GET /md/photo_albums

Success Response Sample:
{
  avatarAlbum: {ID:1, Name:"AVATAR", CoverUrl: "haha.png", Count: 0},
  momentAlbum: {ID:2, Name:"MOMENT", CoverUrl: "abc.png", Count: 1},
  normalAlbums: {
      total: 0,
      albums: [
       {ID: 3, Name: "dogs", CoverUrl: null, Count:3},
       ...
      ]
  }
}


2: 获取某个相册下所有的图片
GET /md/photo_albums/:album_id/photos

Params: album_id 相册ID
Response Sample:
{
    album: {
       {ID: 2, Name: "dogs", CoverUrl: null, Count:3},
    }
    photos: {
       {ID: 1, Url: www.163.com/1.jpg,  PhotoAlbumId: 2}
       {ID: 2, Url: wwww.163.com/2.jpg,  PhotoAlbumId: 2}
    }
}

3. 创建相册
GET /md/photo_albums/new
Params: name: 相册名字
channelId: 相册频道 合法的值
   0 => "真人秀"
   1 => "牛图"
   2 => "壁纸"
   3 => "精彩同人"

Response Sample:
{
   photoAlbum: {
        ID: 3, Type: "normal", Name:"dogs", Count: 0, ChannelId: 1
    }
}

4. 相册重命名
GET /md/photo_albums/:album_id/update
Params: name: 相册新名字
Response Sample:
{
   photoAlbum: {
        ID: 1, Type: "normal", Name:"cats", Count: 0
    }
}

5.相册删除
GET /md/photo_albums/:album_id/delete
Response Success Sample:
{ msg: "OK" }
Response Error Sample:
//表示找不到制定的photo_id
404 {error: {type: "DataNotFound", entity_table: "md_photo", entity_id: "1"}


相册图片API:
1. 图片上传： 复用之前nos getToken接口

2. 图片创建:
GET /md/photos/new
PARAMS:
  url required 前端从nos获得的url
  album_id optional 设定了会关联图片到指定相册中，否则关联到默认相册
Response Sample:
{
   photo: {ID:1, url: "ww.163.com/xxx.jpg", album_id: 2}
}
Response Error Sample:
//表示找不到制定的album_id
404 {error: {type: "DataNotFound", entity_table: "md_photo_ablums", entity_id: "1"}


3. 图片批量移动
GET /md/photos/move
Params:
ids 需要移动的图片id数组
to_album_id 移动到新的相册的id
Response Sample:
{
   photos: [
     {ID:1, url: "ww.163.com/xxx.jpg", album_id: [new album id]},
     {ID:2, url: "ww.163.com/xxx.jpg", album_id: [new album id]},
   ]
}
Response Error Sample:
//表示找不到制定的album_id
404 {error: {type: "DataNotFound", entity_table: "md_photo_ablums", entity_id: "1"}

4. 图片删除
GET /md/photos/:photo_id/delete
Params photo_id required
Response Sucess Sample:
{ msg: "OK" }
Response Error Sample:
404 {error: {type: "DataNotFound", entity_table: "md_photo", entity_id: "1"}


5. 图片批量删除
GET /md/photos/delete
Params: ids
Response Sucess Sample:
{ msg: "OK" }
