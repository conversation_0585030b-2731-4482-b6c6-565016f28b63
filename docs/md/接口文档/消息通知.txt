1、获取通知消息
    url: http://md-test.163.com:3003/md/qnm/getinforms
请求：
    { 
        lastid,//选传 分页 上次获取最后一条消息的id 默认为0 
        pagesize,//选传 默认为10
    }
返回：
    [
        {
            "ID",//通知id
            "UserId",//通知发起人的id
            "TargetId",//通知接收者id
            "Type",//通知类型 1：新鲜事被评论，2：新鲜事被回复，4：新鲜事被点赞
            "Status",//通知状态，0：未读，1：已读
            "CreateTime",//通知创建时间
            "RelateId",//type=1 || 2：评论id，type=4：新鲜事id
            "refer": {//type=4 时相关的新鲜事
                "ID": 1,
                "UserId": 63,
                "Text": "欢欢喜喜过#07",
                "ImgList": null,
                "VideoList": null,
                "ZanList": "60,72,81,100,63",
                "CreateTime": 1466086577666,
                "Status": 0,
                "Hot": -14,
                "RoleId": null,
                "GameMomentId": null,
                "GameId": null,
                "ImgAudit": null,
                "IsTop": 1
            },
            "refer": {//type=1 || 2 相关的评论
                "ID": 129,
                "UserId": 60,
                "TargetId": 89,
                "ReplyId": null,
                "Text": "冬季减肥方法",
                "CreateTime": 1467080160923,
                "Status": 0
            },
            "userInfo": {//通知发起人的信息
                "ID": 100,
                "NickName": "我就是秘密花园",
                "AvaAuthStatus": 0,
                "Avatar": "http://nos.netease.com/hi-163-common/avatar/201606/28/51fdf6903d1311e68cc7f124ac0062df.png"
            }    
        }
    ]

2、获取加好友消息
    url: http://md-test.163.com:3003/md/qnm/getfriinforms
请求：
    { 
        lastid,//选传 默认为0
        pagesize,//选传 默认为10
    }
返回：
    [{
        CreateTime,
        ID,
        Type,//0 
        RelateId,//null
        NickName,//消息的发起者
        UseID,
        Username,
        Status,//0 未读 1 已读
        TargetId,//消息的接收者
        isgz,//1已关注0 未关注
    }]

3、获取留言消息
    url: http://md-test.163.com:3003/md/qnm/getmsginforms
请求：
    { 
        lastid,//选传 默认为0
        pagesize,//选传 默认为10
    }
返回：
    [
        {
            "ID",//通知id
            "UserId",//通知发起人的id
            "TargetId",//通知接收者id
            "Type",//通知类型 3：留言被回复，5：
            "Status",//通知状态，0：未读，1：已读
            "CreateTime",//通知创建时间
            "RelateId",//type=1 || 2：评论id，type=4：新鲜事id
            "refer": {//type=4 时相关的新鲜事
                "ID": 1,
                "UserId": 63,
                "Text": "欢欢喜喜过#07",
                "ImgList": null,
                "VideoList": null,
                "ZanList": "60,72,81,100,63",
                "CreateTime": 1466086577666,
                "Status": 0,
                "Hot": -14,
                "RoleId": null,
                "GameMomentId": null,
                "GameId": null,
                "ImgAudit": null,
                "IsTop": 1
            },
            "refer": {//type=1 || 2 相关的评论
                "ID": 129,
                "UserId": 60,
                "TargetId": 89,
                "ReplyId": null,
                "Text": "冬季减肥方法",
                "CreateTime": 1467080160923,
                "Status": 0
            },
            "userInfo": {//通知发起人的信息
                "ID": 100,
                "NickName": "我就是秘密花园",
                "AvaAuthStatus": 0,
                "Avatar": "http://nos.netease.com/hi-163-common/avatar/201606/28/51fdf6903d1311e68cc7f124ac0062df.png"
            }    
        }
    ]

4、获取消息数量
    url: http://md-test.163.com:3003/md/qnm/getnewnum
请求：无
返回：
    {
        count,//总数
        newfri,//新好友数目
        newinform,//新通知数目
        newmsg,//新留言数目
    }

    
