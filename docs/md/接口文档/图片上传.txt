一、使用NOS直传方法
Step1、从应用服务器获取上传凭证
	/md/nos/gettoken
	请求参数
	   type    上传的图片类型（头像：avatar 新鲜事图片：moment ）
	返回
	{ code: 0, 
	  data: {
		token	上传凭证
		bucketname	上传接口桶名
		objectname	上传文件名
		prefix		文件名前面的部分，用于拼接URL
	  }
	}


Step2、上传图片到NOS服务
	http://nos.netease.com/$bucketname	($bucketname为gettoken中返回的桶名)
	请求类型
		POST
	请求参数
	   x-nos-entity-type: 'json'
	   x-nos-token	上一步获取到的token值
	   Object	  上一步获取到的objectname值
	   file    	文件二进制数据

	返回
	{ code: 0, 
	  data: {
		bucketname	桶名
		url: xxxxx
	  }
	}

	
二、普通文件上传方法
/md/fileupload
post 请求参数：
	type 上传文件类型（同/md/gettoken）
	file 上传的文件对象
返回
	上传成功：http://new.hi.163.com/xxx/blank.html?url=${url}
		url为图片地址
	上传失败：http://new.hi.163.com/xxx/blank.html?errMsg=${errMsg}
		errMsg为错误原因
		
		
/md/base64upload
post 请求参数：
	type 上传文件类型（同/md/gettoken）
	url  操作完成后的跳转url，如 http://new.hi.163.com/xxx/blank.html
	imgdata 图片base64数据
返回
	上传成功：http://new.hi.163.com/xxx/blank.html?url=${url}
		url为图片地址
	上传失败：http://new.hi.163.com/xxx/blank.html?errMsg=${errMsg}
		errMsg为错误原因
