﻿获取随机验证码接口
url: http://md-test.163.com:3003/md/getcode
传递参数： 无
接受参数： 无


1. 添加好友到通讯录中
url: http://md-test.163.com:3003/md/qnm/addfri
传递参数： 
	urs为当前账号，list为添加的好友的账号ID
    data:{
    	targetid: ['1','2','3']||1,可以传数组或者一个用户账号id
    }

接受参数： 
正确：code为0,返回正确数据data:
	{
		code: 0,
		data: {
			msg: "成功!"
		}
	}
错误：code为0,返回错误数据data
	{
		code: 0,
		data: {
			code: -1,
			msg: "参数格式错误!"
		}
	}

2. 获取当前账号的通讯录
url: http://md-test.163.com:3003/md/qnm/getcontacts
传递参数： 
	data
接受参数： 
正确	code为0,返回正确数据data为数组列表,type = 0 关注我的 1 我关注的  2 互相关注的
	{
    "code": 0,
    "data": [
        {
            "ID",
            "NickName",
            "AvaAuthStatus",
            "Avatar",
            "type"0
        }
    ]
}


