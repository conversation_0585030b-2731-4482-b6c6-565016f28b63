# 百相-演奏-乐团数据库设计

## 表结构关系图

```mermaid
erDiagram
    qnm_music_club ||--o{ qnm_music_club_recording : "发布"
    qnm_music_club ||--o{ qnm_music_club_week_hot : "统计"
    qnm_music_club_recording ||--o{ qnm_music_club_recording_rating : "获得"
    qnm_music_club_recording ||--o{ qnm_music_club_recording_request_play_log : "被点播"

    qnm_music_club {
        bigint ID PK "乐团ID"
        varchar Name "乐团名称"
        bigint ServerId "服务器ID"
        int Level "乐团等级"
        bigint Hot "乐团热度"
        bigint ManagerRoleId UK "乐团经理角色ID"
        bigint LeadSingleRecordingId "主打唱片ID"
        bigint CreateTime "创建时间戳"
        bigint UpdateTime "更新时间戳"
        tinyint Status "状态"
        bigint DeleteTime "解散时间戳"
    }

    qnm_music_club_recording {
        bigint ID PK "唱片ID"
        varchar Name "唱片名称"
        bigint TrackId UK "唱片录制游戏唯一ID"
        varchar DataUrl "唱片数据URL"
        int ChorusStart "副歌开始时间戳"
        int VocalOffset "人声偏移时间戳"
        int VocalVolume "人声音量"
        int InstrumentVolume "乐器声音量"
        bigint ServerId "服务器ID"
        bigint MusicClubId "乐团ID"
        bigint RequestPlayCount "点播次数"
        bigint LastRequestPlayTime "上次点播时间"
        bigint RatingCount "评分次数"
        bigint RatingSum "评分总和"
        bigint ReleaseTime "上架时间戳"
        bigint UpdateTime "更新时间戳"
        bigint DeleteTime "删除时间戳"
        tinyint Status "状态"
    }

    qnm_music_club_recording_rating {
        bigint ID PK "评分ID"
        bigint RoleId "评分的角色ID"
        bigint MusicClubId "乐团ID"
        bigint RecordingId "唱片ID"
        int Rating "评分 1-10分"
        bigint CreateTime "创建时间戳"
        bigint UpdateTime "更新时间戳"
    }

    qnm_music_club_recording_request_play_log {
        bigint ID PK "点播记录ID"
        bigint FromRoleId "发起点播角色ID"
        bigint MusicClubId "乐团ID"
        bigint ToRoleId "接收点播角色ID"
        bigint RecordingId "唱片ID"
        bigint EventTime "点播行为时间戳"
        bigint CreateTime "创建时间戳"
    }

    qnm_music_club_week_hot {
        bigint ID PK "乐团热度ID"
        varchar WeekDs "周期开始时间标记"
        bigint MusicClubId "乐团ID"
        bigint Hot "乐团周热度"
        bigint CreateTime "创建时间戳"
    }
```

## 表说明

### qnm_music_club（乐团信息表）
- 存储乐团的基本信息
- 包含乐团名称、等级、热度等基础属性
- 通过 ManagerRoleId 关联乐团经理角色
- 支持乐团状态管理（正常/解散）

### qnm_music_club_recording（唱片信息表）
- 存储乐团发布的唱片信息
- 记录唱片的音频相关参数（人声偏移、音量等）
- 统计唱片的点播次数和评分数据
- 支持唱片状态管理（正常/删除）

### qnm_music_club_recording_rating（唱片评分记录表）
- 记录用户对唱片的评分
- 评分范围为 1-10 分
- 通过 RoleId 关联评分用户
- 支持评分记录的创建和更新

### qnm_music_club_recording_request_play_log（唱片点播记录表）
- 记录唱片的点播历史
- 包含点播发起者和接收者信息
- 记录点播行为的具体时间
- 用于统计和分析点播数据

### qnm_music_club_week_hot（乐团周热度表）
- 记录乐团每周的热度数据
- 使用周一作为周期的开始时间标记
- 用于统计和展示乐团的热度趋势
- 支持热度数据的定期更新

## 同步流程图

```mermaid
graph TD
    A[游戏日志] --> B[日志解析服务]
    B --> C{事件类型}
    C -->|乐团建立| D[创建乐团记录]
    C -->|乐团解散| E[更新乐团状态]
    C -->|乐团等级升级| F[更新乐团等级]
    C -->|经理角色变动| H[更新经理角色]

    D --> G[写入qnm_music_club表]
    E --> G
    F --> G
    H --> G

    subgraph 乐团建立事件
    D1[乐团ID] --> D
    D2[乐团名称] --> D
    D3[服务器ID] --> D
    D4[经理角色ID] --> D
    end

    subgraph 乐团解散事件
    E1[乐团ID] --> E
    end

    subgraph 乐团等级升级事件
    F1[乐团ID] --> F
    F2[新等级] --> F
    end

    subgraph 经理角色变动事件
    H1[乐团ID] --> H
    H2[新经理角色ID] --> H
    end
```

## 事件处理说明

### 1. 乐团建立事件
- 触发条件：游戏内创建新乐团
- 处理流程：
  1. 解析日志获取乐团基本信息
  2. 创建新的乐团记录
  3. 设置初始状态为正常（Status = 0）
  4. 记录创建时间戳

### 2. 乐团解散事件
- 触发条件：游戏内乐团解散
- 处理流程：
  1. 根据乐团ID查找对应记录
  2. 更新状态为解散（Status = -1 ）
  3. 记录解散时间戳（DeleteTime）

### 3. 乐团等级升级事件
- 触发条件：游戏内乐团等级提升
- 处理流程：
  1. 根据乐团ID查找对应记录
  2. 更新乐团等级（Level）
  3. 更新更新时间戳（UpdateTime）

### 4. 经理角色变动事件
- 触发条件：游戏内乐团经理角色变更
- 处理流程：
  1. 根据乐团ID查找对应记录
  2. 更新经理角色ID（ManagerRoleId）
  3. 更新更新时间戳（UpdateTime）
  4. 记录角色变更日志（可选）

## 数据一致性保证

1. 使用事务确保数据操作的原子性
2. 对关键字段（如乐团ID）建立唯一索引
3. 记录操作日志，支持数据回滚
4. 定期进行数据校验，确保游戏数据与数据库同步

## 注意事项

1. 需要处理重复日志的情况
2. 考虑日志延迟到达的处理方案
3. 确保服务器ID的有效性验证
4. 维护经理角色ID的关联关系

## 点播行为数据流程图

```mermaid
sequenceDiagram
    participant U as 用户
    participant S as 点播服务
    participant R as 唱片表
    participant L as 点播记录表
    participant C as 乐团表
    participant Q as 消息队列

    U->>S: 发起唱片点播
    S->>L: 创建点播记录
    Note over L: 记录点播发起者、接收者、唱片ID等信息
    S->>R: 更新点播次数和最后点播时间
    Note over R: RequestPlayCount + 1, LastRequestPlayTime = now()
    S->>Q: 发送更新主打唱片消息
    Note over Q: 包含唱片ID和乐团ID
    Q-->>S: 消息发送成功
    S-->>U: 点播成功响应

    Note over Q: 异步处理
    Q->>R: 获取乐团最高点播唱片
    Note over R: 比较当前唱片点播次数是否超过主打唱片
    alt 需要更新主打唱片
        Q->>C: 更新主打唱片ID
        Note over C: 将当前唱片ID设置为LeadSingleRecordingId
    end
```

## 点播行为数据变化说明

### 1. 点播记录表（qnm_music_club_recording_request_play_log）
- 新增一条点播记录
- 记录字段：
  - FromRoleId：发起点播的角色ID
  - ToRoleId：接收点播的角色ID
  - RecordingId：被点播的唱片ID
  - MusicClubId：所属乐团ID
  - EventTime：点播行为发生时间
  - CreateTime：记录创建时间

### 2. 唱片信息表（qnm_music_club_recording）
- 更新点播次数统计
- 更新最后点播时间
- 更新字段：
  - RequestPlayCount：点播次数 + 1
  - LastRequestPlayTime：最后点播时间戳
  - UpdateTime：更新时间戳

### 3. 乐团信息表（qnm_music_club）
- 更新乐团热度
- 更新字段：
  - Hot：乐团热度 + 1
  - UpdateTime：更新时间戳
- 异步更新主打唱片：
  - 通过消息队列异步处理
  - 当新点播唱片的点播次数超过当前主打唱片时
  - 更新 LeadSingleRecordingId 为新唱片ID

### 注意事项
1. 考虑点播频率限制
2. 处理并发点播的情况
3. 消息队列需要保证消息不丢失
4. 异步处理需要考虑重试机制

## 14天无人点播自动下架流程

### 1. 自动下架流程图

```mermaid
sequenceDiagram
    participant S as 定时任务服务
    participant R as 唱片表
    participant M as 邮件服务
    participant C as 乐团表
    participant Q as 消息队列

    S->>R: 查询需要下架的唱片
    Note over R: Status = 0 AND LastRequestPlayTime < now() - 14天 AND ReleaseTime < now() - 14天
    loop 每个唱片
        S->>C: 获取乐团经理ID
        S->>Q: 发送下架通知消息
        Note over Q: 包含唱片ID、乐团ID、经理ID
        Q-->>S: 消息发送成功
        S->>R: 更新唱片状态为已下架
        Note over R: Status = -1, DeleteTime = now()
    end

    Note over Q: 异步处理
    Q->>M: 发送下架通知邮件
    Note over M: 通知乐团经理唱片已下架
```

### 2. 实现细节
#### 2.1 下架处理流程
1. **定时任务执行**：
   - 执行频率：每天凌晨2点
   - 使用分布式锁确保集群环境下只有一个实例执行
   - 批量处理，避免长时间锁表

2. **下架条件**：
   - 唱片状态为正常（Status = 0）
   - 最后点播时间超过14天
   - 上架时间超过14天（确保新上架唱片有足够的缓冲期）

4. **下架操作**：
   - 更新唱片状态为已下架（Status = -1）
   - 记录下架时间（DeleteTime）
   - 发送下架通知邮件

### 3. 数据一致性保证

1. **事务处理**：
   - 使用事务确保状态更新原子性
   - 记录操作日志支持回滚

2. **消息队列**：
   - 确保通知消息不丢失
   - 支持消息重试机制

3. **异常处理**：
   - 处理邮件发送失败的情况
   - 处理状态更新失败的情况
   - 实现重试机制

### 4. 监控指标

1. **任务监控**：
   - 下架任务执行时间
   - 下架成功率
   - 邮件发送成功率

2. **告警机制**：
   - 任务执行异常告警
   - 下架失败告警
   - 通知发送失败告警

### 5. 注意事项

1. **时区处理**：
   - 统一使用服务器时区
   - 时间戳使用毫秒级

2. **并发处理**：
   - 使用分布式锁避免重复处理
   - 批量处理控制并发数

3. **数据一致性**：
   - 定期检查数据一致性

4. **扩展性考虑**：
   - 支持多服务器部署
   - 预留排行榜扩展字段
   - 考虑未来可能的热度计算规则变更