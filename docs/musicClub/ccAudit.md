# 自定义音频审核流程

## 流程步骤

1. **上传音频文件**
    游戏端上传音频文件（如存储在 FP、S3 等），并将状态设置为【审核中】。建议增加兜底逻辑：如 3 小时内未完成审核则自动标记为【不通过】。

2. **调用审核接口**
    游戏端调用 CC 语音 HTTP 审核接口，需携带玩家 UID、对局 ID、音频 URL 等参数（具体参数见下方）。

3. **服务端处理**
    语音服务端完成 ASR、敏感内容识别、音频存储后，通过语音日志将数据发送给运营技术。

4. **审核结果回调**
    运营技术回调游戏端指令，审核通过则上架音频，不通过则下架音频。

---

## CC 入审接口说明

- **接口 URL**:
  `http://inneraudioms.cc.163.com/inner/v1/audiorectranslatorext/custom_audio_job/create`
- **请求方法**:
  `POST`

### 请求参数示例

```python
import requests

url = "http://inneraudioms.cc.163.com/inner/v1/audiorectranslatorext/custom_audio_job/create"
req = {
     "appid": 10015,                # CC 的 appid，传 l10 即 10015
     "uid": "hello_cc",             # 玩家 uid
     "server": "100",               # 服务器 id
     "stream_name": "uuid",         # 音频唯一 uuid
     "channel_id": "channel_id",    # 房间号，无则可用空字符串 ""
     "channel_type": "bandclub",    # 玩法类型，需与实时语音的 channel type 区分
     "audio_url": "audio_url"       # 音频 url
}
```

> **说明**：
> - `appid` 需传 l10（10015）。
> - `channel_type` 建议固定为 `bandclub`，以区分实时语音。