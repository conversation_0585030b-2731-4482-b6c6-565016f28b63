# 实施计划

- [ ] 1. 扩展登录参数和会话数据
  - 修改 AuthLoginParams 接口，添加 speechLimitStrategy 参数
  - 确保 LoginSession.set 调用中包含 speechLimitStrategy 参数
  - _需求: 1.1, 1.2_

- [ ] 2. 实现言论限制策略常量和辅助函数
  - [ ] 2.1 在 speechLimitPlugin.ts 中定义 SPEECH_LIMIT_STRATEGY 常量
    - 定义 NONE, BLOCK_SPEECH, BLOCK_IMAGE, BLOCK_ALL 常量值
    - 添加注释说明每个常量的二进制表示和用途
    - _需求: 2.1, 2.2, 2.3, 2.4, 2.5_
  
  - [ ] 2.2 实现 getSpeechLimitStrategy 辅助函数
    - 从会话数据中获取玩家的限制策略
    - 处理默认值和错误情况
    - 添加适当的日志记录
    - _需求: 3.1, 3.2_

- [ ] 3. 修改 speechLimitPlugin 函数
  - [ ] 3.1 修改 SPEECH_URLS 处理逻辑
    - 替换 isOverSeaUser 检查为 getSpeechLimitStrategy 调用
    - 使用位运算检查是否应该屏蔽发言
    - _需求: 3.1, 3.3, 3.4_
  
  - [ ] 3.2 修改 ADD_MOMENT_URLS 处理逻辑
    - 添加对 speechLimitStrategy 的检查
    - 使用位运算检查是否应该屏蔽图片/视频
    - 返回适当的错误消息
    - _需求: 3.1, 3.3, 3.4, 5.1, 5.2, 5.3, 5.4_

- [ ] 4. 单元测试
  - [ ] 4.1 为 getSpeechLimitStrategy 函数编写单元测试
    - 测试正常情况
    - 测试默认值
    - 测试错误处理
    - _需求: 3.1, 3.2_
  
  - [ ] 4.2 为修改后的 speechLimitPlugin 函数编写单元测试
    - 测试不同的策略代码
    - 测试不同的 URL 类型
    - 测试错误处理
    - _需求: 3.3, 3.4, 5.1, 5.2, 5.3, 5.4_

- [ ] 5. 集成测试
  - [ ] 5.1 测试非特殊时期的行为
    - 验证即使设置了限制策略，也不会应用限制
    - _需求: 1.4, 1.5_
  
  - [ ] 5.2 测试特殊时期的行为
    - 测试无限制策略
    - 测试屏蔽发言策略
    - 测试屏蔽发图策略
    - 测试屏蔽全部策略
    - _需求: 1.4, 2.1, 2.2, 2.3, 2.4_
  
  - [ ] 5.3 测试错误处理
    - 测试获取策略失败时的默认行为
    - _需求: 3.2, 3.4_

- [ ] 6. 文档和注释
  - 更新代码注释，说明新的限制策略系统
  - 更新相关文档，说明如何使用新的限制策略参数
  - _需求: 4.1, 4.2, 4.3, 4.4_