# 需求文档

## 介绍

本功能对现有的言论限制控制系统进行增强，保留基于时间配置的总体开关，但将当前基于"是否海外用户"的判断方式替换为灵活的、针对单个玩家的策略系统。系统在特殊时期仍然会启用言论限制功能，但不再简单地根据用户是否为海外用户来决定限制，而是支持通过登录时传递的可配置限制策略对单个玩家进行精细化控制。

此增强功能允许对每个玩家应用不同类型的限制（屏蔽发言、屏蔽发图或组合限制），提供更好的控制粒度，并将游戏逻辑与限制实现解耦。重要的是，这些限制策略仅在系统处于特殊时期（isIn64SpecialPeriod 为 true）时才会生效，在非特殊时期，即使传递了限制策略参数也会被忽略。

## 需求

### 需求 1

**用户故事：** 作为系统管理员，我希望通过登录参数控制单个玩家的限制策略，而不是仅依赖于是否为海外用户，以便实现更精细化的言论控制。

#### 验收标准

1. 当玩家登录时，系统应接受一个限制策略参数，该参数定义该玩家应被屏蔽的操作
2. 当提供限制策略参数时，系统应将此信息存储在玩家的会话数据中
3. 当未提供限制策略参数时，系统应使用默认策略（屏蔽发言和发图，策略 = 3）
4. 当系统处于特殊时期（isIn64SpecialPeriod 为 true）时，言论限制总开关才会启用，并应用玩家的限制策略
5. 当系统不处于特殊时期时，即使玩家有限制策略参数，也不会应用任何限制

### 需求 2

**用户故事：** 作为系统管理员，我希望使用数字策略代码来定义限制类型，以便轻松组合不同的限制类型并在将来添加新的限制类型。

#### 验收标准

1. 当策略代码为 1 (二进制 01) 时，系统应屏蔽与发言相关的操作（评论、消息、带文本的动态）
2. 当策略代码为 2 (二进制 10) 时，系统应屏蔽与图片相关的操作（照片上传、带图片/视频的动态）
3. 当策略代码为 3 (二进制 11) 时，系统应屏蔽发言和图片操作（位运算组合 1 | 2）
4. 当策略代码为 0 (二进制 00) 时，系统应允许所有操作（无限制）
5. 当添加新的限制类型时，系统应使用新的二进制位来表示，确保可以通过位运算（如 OR、AND）组合多种限制

### 需求 3

**用户故事：** 作为开发人员，我希望言论限制插件使用玩家特定的策略而不是"是否海外用户"的判断，以便将限制逻辑与用户地理位置解耦。

#### 验收标准

1. 当调用 speechLimitPlugin 时，它应从玩家的会话数据中检查玩家的限制策略，而不是检查 isOverSeaUser
2. 当玩家没有设置限制策略时，插件应允许操作继续进行
3. 当玩家的限制策略阻止当前操作类型时，插件应返回适当的错误消息
4. 当插件阻止操作时，它应为每种限制类型返回一致的错误代码和消息

### 需求 4

**用户故事：** 作为系统维护人员，我希望限制策略易于扩展，以便在不进行重大代码更改的情况下添加新类型的限制。

#### 验收标准

1. 当需要新的限制类型时，应通过添加新的策略代码来实现
2. 当新的 URL 模式需要限制时，应通过现有的 URL 分类系统进行配置
3. 当限制逻辑发生变化时，不应要求更改登录或会话管理代码
4. 当使用策略组合时，系统应支持用于检查多个限制的位运算操作

### 需求 5

**用户故事：** 作为游戏客户端，我希望在操作被阻止时收到清晰的错误消息，以便玩家了解他们的操作为什么被限制。

#### 验收标准

1. 当发言操作被阻止时，系统应返回特定于发言的错误消息
2. 当图片操作被阻止时，系统应返回特定于图片的错误消息
3. 当多种操作类型被阻止时，系统应为尝试的操作返回最相关的错误消息
4. 当操作被阻止时，错误代码应保持一致（-1），但消息应特定于限制类型