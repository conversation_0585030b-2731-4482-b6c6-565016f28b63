# 设计文档：言论限制策略系统

## 概述

本设计文档描述了言论限制策略系统的实现方案。该系统将保留基于时间配置的总体开关，但将当前基于"是否海外用户"的判断方式替换为灵活的、针对单个玩家的策略系统。系统在特殊时期仍然会启用言论限制功能，但不再简单地根据用户是否为海外用户来决定限制，而是支持通过登录时传递的可配置限制策略对单个玩家进行精细化控制。

## 架构

系统架构将保持现有的中间件和插件结构，但会对以下组件进行修改：

1. **登录参数处理**：在 `service/qnm/pyq/auth.ts` 中的 `login` 函数中添加对新的限制策略参数的处理
2. **会话数据存储**：在 `LoginSession.set` 调用中添加限制策略参数
3. **言论限制插件**：修改 `pyq-server/services/speechLimitPlugin.ts` 中的 `speechLimitPlugin` 函数，使用玩家的限制策略而不是 `isOverSeaUser` 判断

整体流程如下：

```mermaid
sequenceDiagram
    participant Client as 游戏客户端
    participant Auth as 认证服务
    participant Session as 会话存储
    participant Middleware as 言论限制中间件
    participant Plugin as 言论限制插件
    
    Client->>Auth: 登录请求(roleid, 限制策略)
    Auth->>Session: 存储会话数据(包含限制策略)
    Auth->>Client: 登录成功
    
    Client->>Middleware: API请求
    Note over Middleware: 检查是否特殊时期
    
    alt 特殊时期
        Middleware->>Plugin: 调用言论限制插件
        Plugin->>Session: 获取玩家限制策略
        
        alt 有限制策略且适用于当前操作
            Plugin->>Client: 返回限制错误
        else 无限制策略或不适用于当前操作
            Plugin->>Client: 允许操作
        end
    else 非特殊时期
        Middleware->>Client: 允许操作
    end
```

## 组件和接口

### 1. 认证服务 (Auth)

修改 `service/qnm/pyq/auth.ts` 中的 `login` 函数，添加对限制策略参数的处理：

```typescript
export async function login(params: AuthLoginParams) {
  // 现有代码...
  
  const speechLimitStrategy = params.speechLimitStrategy || "3"; // 默认为3（屏蔽发言和发图）
  
  const skeyInfo = await LoginSession.set(params.roleid, {
    time: params.time,
    account: account,
    isOverSea: isOverSea,
    server: serverId,
    urs: urs,
    language: language,
    country: country,
    speechLimitStrategy: speechLimitStrategy, // 添加限制策略
  });
  
  // 现有代码...
}
```

### 2. 会话存储 (Session)

不需要修改会话存储组件，因为它已经支持存储任意键值对。

### 3. 言论限制中间件 (Middleware)

保持 `pyq-server/middlewares/speechLimit.ts` 中的 `speechLimitChecker` 函数不变，因为总体开关仍然基于时间配置：

```typescript
export function speechLimitChecker(date?: Date) {
  function checker(req, res, next) {
    const curDate = date || new Date()
    if (isIn64SpecialPeriod(curDate)) {
      speechLimitPlugin(req, res, next)
    } else {
      next()
    }
  }
  return checker
}
```

### 4. 言论限制插件 (Plugin)

修改 `pyq-server/services/speechLimitPlugin.ts` 中的 `speechLimitPlugin` 函数，使用玩家的限制策略而不是 `isOverSeaUser` 判断：

```typescript
// 添加常量定义
const SPEECH_LIMIT_STRATEGY = {
  NONE: 0,          // 0000 - 无限制
  BLOCK_SPEECH: 1,  // 0001 - 屏蔽发言
  BLOCK_IMAGE: 2,   // 0010 - 屏蔽发图
  BLOCK_ALL: 3      // 0011 - 屏蔽发言和发图
};

// 添加辅助函数
async function getSpeechLimitStrategy(roleId: number): Promise<number> {
  try {
    const strategy = await getLoginParams(roleId, "speechLimitStrategy");
    return strategy ? parseInt(strategy, 10) : SPEECH_LIMIT_STRATEGY.BLOCK_ALL; // 默认为屏蔽全部
  } catch (err) {
    logger.error({ err, roleId }, "GetSpeechLimitStrategyFailed");
    return SPEECH_LIMIT_STRATEGY.BLOCK_ALL; // 出错时默认为屏蔽全部
  }
}

// 修改主函数
export async function speechLimitPlugin(req, res, next) {
  let url = req.route.path;
  let params = req.params;
  
  // 对于某些 URL，直接屏蔽，不考虑策略
  if (isUrlHit(url, CHECK_URLS)) {
    return res.send({ code: -1, msg: ALERT_MSG });
  }
  
  // 对于发言相关的 URL，检查策略
  if (isUrlHit(url, SPEECH_URLS) && params.roleid) {
    try {
      const strategy = await getSpeechLimitStrategy(params.roleid);
      
      // 检查是否屏蔽发言 (BLOCK_SPEECH 位被设置)
      if (strategy & SPEECH_LIMIT_STRATEGY.BLOCK_SPEECH) {
        return res.send({ code: -1, msg: ALERT_MSG });
      }
    } catch (err) {
      return res.send({ code: -2, msg: "please reLogin" });
    }
  }
  
  // 对于添加动态的 URL，检查是否包含图片或视频
  if (isUrlHit(url, ADD_MOMENT_URLS)) {
    let params = req.params;
    let imgList = util.getJsonInfo(params.imglist, []);
    let videoList = util.getJsonInfo(params.videolist, []);
    
    if ((imgList && imgList.length) || (videoList && videoList.length)) {
      try {
        const strategy = await getSpeechLimitStrategy(params.roleid);
        
        // 检查是否屏蔽发图 (BLOCK_IMAGE 位被设置)
        if (strategy & SPEECH_LIMIT_STRATEGY.BLOCK_IMAGE) {
          const msg = imgList && imgList.length 
            ? "图片服务器升级，暂不支持发表图片动态" 
            : "视频服务器升级，暂不支持发表视频动态";
          return res.send({ code: -1, msg });
        }
      } catch (err) {
        return res.send({ code: -2, msg: "please reLogin" });
      }
    }
  }
  
  return next();
}
```

## 数据模型

### 1. 登录参数扩展

扩展 `AuthLoginParams` 接口，添加限制策略参数：

```typescript
interface AuthLoginParams {
  // 现有字段...
  speechLimitStrategy?: string; // 言论限制策略，默认为 "3"
}
```

### 2. 会话数据扩展

会话数据将包含新的 `speechLimitStrategy` 字段，存储为字符串。

### 3. 限制策略常量

定义限制策略常量，使用位运算支持组合：

```typescript
const SPEECH_LIMIT_STRATEGY = {
  NONE: 0,          // 0000 - 无限制
  BLOCK_SPEECH: 1,  // 0001 - 屏蔽发言
  BLOCK_IMAGE: 2,   // 0010 - 屏蔽发图
  BLOCK_ALL: 3      // 0011 - 屏蔽发言和发图
};
```

## 错误处理

系统将保持现有的错误处理机制，但会根据限制类型返回不同的错误消息：

1. 对于发言限制：`"因技术升级，该功能暂不可用，对您造成不便深感歉意。"`
2. 对于图片限制：`"图片服务器升级，暂不支持发表图片动态"`
3. 对于视频限制：`"视频服务器升级，暂不支持发表视频动态"`

所有限制错误的错误代码都是 `-1`，保持一致性。

## 测试策略

### 1. 单元测试

为新增和修改的函数编写单元测试：

- `getSpeechLimitStrategy` 函数测试
- `speechLimitPlugin` 函数测试，包括不同策略和 URL 组合的情况

### 2. 集成测试

编写集成测试，测试整个流程：

- 使用不同限制策略登录
- 尝试执行各种受限操作
- 验证系统行为是否符合预期

### 3. 测试场景

测试以下关键场景：

1. **非特殊时期**：
   - 验证即使设置了限制策略，也不会应用限制

2. **特殊时期，无限制策略**：
   - 验证所有操作都被允许

3. **特殊时期，屏蔽发言**：
   - 验证发言操作被屏蔽
   - 验证图片操作被允许

4. **特殊时期，屏蔽发图**：
   - 验证发言操作被允许
   - 验证图片操作被屏蔽

5. **特殊时期，屏蔽全部**：
   - 验证所有操作都被屏蔽

6. **错误处理**：
   - 验证在获取策略失败时的默认行为