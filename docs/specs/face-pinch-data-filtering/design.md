# 捏脸数据筛选功能技术设计文档

## Overview

本设计文档描述了倩女手游捏脸站数据筛选功能的技术实现方案。该功能允许用户在上传捏脸作品时选择允许他人使用的部位（全脸/塑形/妆容），并在浏览和应用作品时根据可用部位进行筛选和选择性应用。同时扩展收藏和我的设计功能的存储容量至300个。

## Architecture

### 系统架构概览

```mermaid
graph TB
    Client[客户端] --> API[API层]
    API --> Service[业务逻辑层]
    Service --> Model[数据模型层]
    Model --> DB[(MySQL数据库)]

    subgraph "核心模块"
        PartFilter[部位筛选模块]
        DataShare[数据分享模块]
        Storage[存储管理模块]
    end

    Service --> PartFilter
    Service --> DataShare
    Service --> Storage
```

### 数据流架构

```mermaid
sequenceDiagram
    participant U as 用户
    participant C as 客户端
    participant A as API服务
    participant D as 数据库

    Note over U,D: 上传作品流程
    U->>C: 选择允许使用的部位
    C->>A: 提交作品数据+部位选择
    A->>D: 保存作品及部位信息

    Note over U,D: 筛选浏览流程
    U->>C: 设置筛选条件
    C->>A: 请求符合条件的作品列表
    A->>D: 查询匹配的作品
    D->>A: 返回作品数据
    A->>C: 返回筛选结果

    Note over U,D: 应用作品流程
    U->>C: 选择要应用的部位
    C->>A: 请求应用指定部位
    A->>D: 记录应用行为
```

## Components and Interfaces

### 1. 数据库扩展设计

#### 1.1 pyq_face_pinch_work 表扩展

在现有的 `pyq_face_pinch_work` 表中新增字段：

```sql
ALTER TABLE `pyq_face_pinch_work`
ADD COLUMN `allowedParts` tinyint(4) NOT NULL DEFAULT 7 COMMENT '允许使用的部位：1=塑形，2=妆容，4=全脸，可组合使用位运算' AFTER `bodyShape`;
```

#### 1.2 配置文件设置

在 `pyq-server/common/config.all.ts` 第356行的 `facePinchCfg` 中添加功能上线时间配置：

```typescript
export const facePinchCfg = {
  // ... 现有配置
  partsFeatureLaunchTime: '2024-01-01T00:00:00+08:00', // UTC+8 时间格式
};
```

#### 1.2 部位定义常量

```javascript
const FACE_PARTS = {
  SHAPE: 1,      // 塑形 - 0001
  MAKEUP: 2,     // 妆容 - 0010
  FULL_FACE: 4   // 全脸 - 0100
};

// 组合使用示例
const SHAPE_AND_MAKEUP = FACE_PARTS.SHAPE | FACE_PARTS.MAKEUP; // 3
const ALL_PARTS = FACE_PARTS.SHAPE | FACE_PARTS.MAKEUP | FACE_PARTS.FULL_FACE; // 7
```

### 2. API接口设计

#### 2.1 作品上传接口扩展

**POST /face_pinch/work/add**

请求参数扩展：
```json
{
  "allowedParts": 3,  // 位运算值：1=塑形，2=妆容，4=全脸
  // ... 其他现有参数
}
```

#### 2.2 作品列表接口扩展

**GET /face_pinch/work/list_public**
**GET /face_pinch/work/list_collect**
**GET /face_pinch/work/list_self**

请求参数扩展：
```json
{
  "filterParts": 3,  // 筛选条件：要求包含的部位
  // ... 其他现有参数
}
```

响应数据扩展：
```json
{
  "data": {
    "list": [
      {
        "id": 123,
        "allowedParts": 3,
        "availableApplyParts": ["塑形", "妆容"],
        // ... 其他现有字段
      }
    ]
  }
}
```

#### 2.3 现有应用接口扩展

现有的 `/face_pinch/work/apply` 接口保持不变，部位应用的具体逻辑由游戏客户端和游戏服务器处理。

我们只需要在接口响应中提供 `availableApplyParts` 信息，告知客户端该作品可以应用哪些部位：

```json
{
  "code": 0,
  "data": {
    "id": 123,
    "availableApplyParts": ["塑形", "妆容"],
    // ... 其他现有字段
  }
}
```

#### 2.4 分享接口扩展

基于现有的分享机制，无需扩展新的分享接口。作品本身已经携带了部位信息，只需要在获取分享信息时输出部位相关字段：

**现有分享机制保持不变：**
- 每个作品在创建时生成唯一的 `shareId`
- 通过 `/face_pinch/work/get_by_share_id` 获取分享作品信息
- 通过 `/face_pinch/dashen/share_info/import` 导入分享作品到收藏

**扩展现有接口响应：**

**GET /face_pinch/work/get_by_share_id** 响应扩展：
```json
{
  "code": 0,
  "data": {
    "id": 123,
    "shareId": "xxx",
    "allowedParts": 3,
    "availableApplyParts": ["塑形", "妆容"],
    // ... 其他现有字段
  }
}
```

客户端可以根据 `allowedParts` 和 `availableApplyParts` 信息来决定如何处理分享的作品。

### 3. 业务逻辑组件

#### 3.1 部位筛选服务 (FacePartFilterService)

```javascript
class FacePartFilterService {
  /**
   * 根据筛选条件查询作品
   * @param {Object} params - 查询参数
   * @param {number} params.allowedParts - 筛选的部位
   * @param {number} params.page - 页码
   * @param {number} params.pageSize - 每页数量
   * @returns {Promise<Object>} 作品列表
   */
  async getFilteredWorks(params) {
    const { filterParts, page = 1, pageSize = 20 } = params;

    // 构建查询条件
    const whereConditions = [];
    if (filterParts) {
      // 使用位运算查询包含指定部位的作品
      whereConditions.push(`(allowedParts & ${filterParts}) = ${filterParts}`);
    }

    // 历史数据特殊处理
    if (filterParts & FACE_PARTS.SHAPE) {
      // 如果筛选包含塑形，排除不支持单独塑形的历史数据
      whereConditions.push(`NOT (createTime < ${facePinchCfg.partsFeatureLaunchTime} AND (allowedParts & ${FACE_PARTS.SHAPE}) > 0)`);
    }

    return await this.queryWorks(whereConditions, page, pageSize);
  }

  /**
   * 验证部位选择的有效性
   * @param {number} allowedParts - 允许的部位
   * @returns {boolean} 是否有效
   */
  validatePartsSelection(allowedParts) {
    return allowedParts > 0 && allowedParts <= 7;
  }

  /**
   * 获取部位的显示名称
   * @param {number} parts - 部位值
   * @returns {Array<string>} 部位名称数组
   */
  getPartsDisplayNames(parts) {
    const names = [];
    if (parts & FACE_PARTS.SHAPE) names.push('塑形');
    if (parts & FACE_PARTS.MAKEUP) names.push('妆容');
    if (parts & FACE_PARTS.FULL_FACE) names.push('全脸');
    return names;
  }
}
```

#### 3.2 分享功能扩展

基于现有的分享机制，只需要扩展现有接口的响应数据，无需新增分享接口：

```javascript
// 扩展现有的 facePinchWorkGetByShareId 函数
export async function facePinchWorkGetByShareId(
  params: FacePinchReq.WorkGetByShareId
): Promise<FacePinchRes.WorkGetByShareId> {
  const r = await WorkModel.findByShareId(params.shareId);
  checkWorkExist(r);
  const accusedRole = await getAccusedRole(r.userId);

  // 计算可用的应用部位（考虑历史数据限制）
  const availableApplyParts = getAvailableApplyParts(r);

  const data: FacePinchRes.WorkGetByShareId = {
    id: r.id,
    shareId: r.shareId,
    createTime: r.createTime,
    dataUrl: r.dataUrl,
    image: r.image,
    title: r.title,
    desc: r.desc,
    roleName: r.roleName,
    gender: r.gender,
    visibility: r.visibility,
    jobId: r.jobId,
    accusedRole,
    // 新增字段
    allowedParts: r.allowedParts,
    availableApplyParts: availableApplyParts
  };
  return data;
}

/**
 * 计算作品的可用应用部位
 * @param {WorkRecord} work - 作品记录
 * @returns {Array<string>} 可用部位名称数组
 */
function getAvailableApplyParts(work: WorkRecord): string[] {
  const isLegacyData = new Date(work.createTime) < new Date(facePinchCfg.partsFeatureLaunchTime);

  if (isLegacyData) {
    // 历史数据只能应用全脸或妆容，不能单独应用塑形
    const availableParts = work.allowedParts & ~FACE_PARTS.SHAPE;
    return FacePartFilterService.getPartsDisplayNames(availableParts);
  }

  return FacePartFilterService.getPartsDisplayNames(work.allowedParts);
}
```

#### 3.3 存储管理服务 (StorageManagementService)

```javascript
class StorageManagementService {
  static MAX_STORAGE_COUNT = 300;

  /**
   * 检查存储容量
   * @param {number} userId - 用户ID
   * @returns {Promise<Object>} 存储状态
   */
  async checkStorageCapacity(userId) {
    const collectCount = await this.getCollectCount(userId);
    const selfWorkCount = await this.getSelfWorkCount(userId);
    const totalCount = collectCount + selfWorkCount;

    return {
      used: totalCount,
      total: StorageManagementService.MAX_STORAGE_COUNT,
      available: StorageManagementService.MAX_STORAGE_COUNT - totalCount,
      canAdd: totalCount < StorageManagementService.MAX_STORAGE_COUNT
    };
  }

  /**
   * 添加收藏前检查容量
   * @param {number} userId - 用户ID
   * @returns {Promise<boolean>} 是否可以添加
   */
  async canAddToCollection(userId) {
    const capacity = await this.checkStorageCapacity(userId);
    return capacity.canAdd;
  }
}
```

## Data Models

### 1. 作品数据模型扩展

```javascript
// PyqFacePinchWork 模型扩展
const PyqFacePinchWork = {
  // ... 现有字段
  allowedParts: {
    type: DataTypes.TINYINT,
    allowNull: false,
    defaultValue: 7, // 默认全部允许
    comment: '允许使用的部位：1=塑形，2=妆容，4=全脸，可组合使用位运算'
  },

  // 虚拟字段
  availableApplyParts: {
    type: DataTypes.VIRTUAL,
    get() {
      const isLegacyData = this.createTime < facePinchCfg.partsFeatureLaunchTime;
      if (isLegacyData) {
        // 历史数据只能应用全脸或妆容，不能单独应用塑形
        const availableParts = this.allowedParts & ~FACE_PARTS.SHAPE;
        return FacePartFilterService.getPartsDisplayNames(availableParts);
      }
      return FacePartFilterService.getPartsDisplayNames(this.allowedParts);
    }
  }
};
```

### 2. 分享数据模型

```javascript
// 扩展分享相关的数据结构
const FacePartShareData = {
  shareId: String,
  workId: Number,
  shareParts: Number,
  shareType: String, // 'qr_code' | 'link'
  createTime: Number,
  expireTime: Number
};
```

## Error Handling

### 1. 错误码定义

```javascript
const FACE_PINCH_ERROR_CODES = {
  // 部位相关错误
  INVALID_PARTS_SELECTION: 40001, // 无效的部位选择
  PARTS_NOT_ALLOWED: 40002,       // 部位不被允许
  LEGACY_DATA_RESTRICTION: 40003, // 历史数据限制

  // 存储相关错误
  STORAGE_LIMIT_EXCEEDED: 40004,  // 存储容量超限

  // 分享相关错误
  INVALID_SHARE_DATA: 40005,      // 无效的分享数据
  SHARE_EXPIRED: 40006,           // 分享已过期
};
```

### 2. 错误处理策略

```javascript
class FacePinchErrorHandler {
  static handlePartsValidationError(allowedParts, requestedParts) {
    if ((allowedParts & requestedParts) !== requestedParts) {
      throw new ApiError(
        FACE_PINCH_ERROR_CODES.PARTS_NOT_ALLOWED,
        '请求的部位超出作品允许范围'
      );
    }
  }

  static handleLegacyDataRestriction(work, requestedParts) {
    const isLegacyData = work.createTime < facePinchCfg.partsFeatureLaunchTime;
    if (isLegacyData && (requestedParts & FACE_PARTS.SHAPE)) {
      throw new ApiError(
        FACE_PINCH_ERROR_CODES.LEGACY_DATA_RESTRICTION,
        '历史数据不支持单独使用塑形部位'
      );
    }
  }

  static handleStorageLimit(currentCount) {
    if (currentCount >= StorageManagementService.MAX_STORAGE_COUNT) {
      throw new ApiError(
        FACE_PINCH_ERROR_CODES.STORAGE_LIMIT_EXCEEDED,
        `存储容量已达上限(${StorageManagementService.MAX_STORAGE_COUNT})，请删除部分内容后重试`
      );
    }
  }
}
```

## Testing Strategy

### 1. 单元测试

#### 1.1 部位筛选逻辑测试

```javascript
describe('FacePartFilterService', () => {
  test('应该正确筛选包含指定部位的作品', async () => {
    const service = new FacePartFilterService();
    const result = await service.getFilteredWorks({
      filterParts: FACE_PARTS.MAKEUP,
      page: 1,
      pageSize: 10
    });

    expect(result.list.every(work =>
      (work.allowedParts & FACE_PARTS.MAKEUP) > 0
    )).toBe(true);
  });

  test('应该排除不支持单独塑形的历史数据', async () => {
    const service = new FacePartFilterService();
    const result = await service.getFilteredWorks({
      filterParts: FACE_PARTS.SHAPE
    });

    expect(result.list.every(work =>
      !(work.createTime < facePinchCfg.partsFeatureLaunchTime && (work.allowedParts & FACE_PARTS.SHAPE))
    )).toBe(true);
  });
});
```

#### 1.2 存储容量测试

```javascript
describe('StorageManagementService', () => {
  test('应该正确计算存储容量', async () => {
    const service = new StorageManagementService();
    const capacity = await service.checkStorageCapacity(testUserId);

    expect(capacity.used).toBeGreaterThanOrEqual(0);
    expect(capacity.total).toBe(300);
    expect(capacity.available).toBe(capacity.total - capacity.used);
  });

  test('应该在达到容量上限时阻止添加', async () => {
    const service = new StorageManagementService();
    // 模拟用户已有300个收藏
    jest.spyOn(service, 'getCollectCount').mockResolvedValue(300);
    jest.spyOn(service, 'getSelfWorkCount').mockResolvedValue(0);

    const canAdd = await service.canAddToCollection(testUserId);
    expect(canAdd).toBe(false);
  });
});
```

### 2. 集成测试

#### 2.1 API接口测试

```javascript
describe('Face Pinch API Integration', () => {
  test('上传作品时应该保存部位选择', async () => {
    const response = await request(app)
      .post('/face_pinch/work/add')
      .send({
        ...baseWorkData,
        allowedParts: FACE_PARTS.MAKEUP | FACE_PARTS.SHAPE
      });

    expect(response.status).toBe(200);

    const work = await PyqFacePinchWork.findById(response.body.data.workId);
    expect(work.allowedParts).toBe(3);
  });

  test('筛选接口应该返回符合条件的作品', async () => {
    const response = await request(app)
      .get('/face_pinch/work/list_public')
      .query({
        filterParts: FACE_PARTS.MAKEUP,
        page: 1,
        pageSize: 10
      });

    expect(response.status).toBe(200);
    expect(response.body.data.list.every(work =>
      (work.allowedParts & FACE_PARTS.MAKEUP) > 0
    )).toBe(true);
  });
});
```

### 3. 端到端测试

#### 3.1 用户流程测试

```javascript
describe('Face Pinch User Journey', () => {
  test('完整的部位筛选和应用流程', async () => {
    // 1. 上传作品并选择允许的部位
    const uploadResponse = await uploadWorkWithParts(FACE_PARTS.MAKEUP);

    // 2. 筛选包含妆容的作品
    const listResponse = await getWorksWithFilter(FACE_PARTS.MAKEUP);
    expect(listResponse.data.list).toContainEqual(
      expect.objectContaining({ id: uploadResponse.data.workId })
    );

    // 3. 应用作品的妆容部位
    const applyResponse = await applyWorkParts(
      uploadResponse.data.workId,
      FACE_PARTS.MAKEUP
    );
    expect(applyResponse.status).toBe(200);
  });
});
```

### 4. 性能测试

#### 4.1 数据库查询性能

```javascript
describe('Database Performance', () => {
  test('部位筛选查询应该在合理时间内完成', async () => {
    const startTime = Date.now();

    await FacePartFilterService.getFilteredWorks({
      filterParts: FACE_PARTS.SHAPE | FACE_PARTS.MAKEUP,
      page: 1,
      pageSize: 50
    });

    const duration = Date.now() - startTime;
    expect(duration).toBeLessThan(1000); // 应该在1秒内完成
  });
});
```

## Implementation Notes

### 1. 数据迁移策略
- 默认部位设置：历史数据的 `allowedParts` 设为 6 (妆容+全脸)
- 分批迁移：避免一次性更新大量数据影响性能

### 2. 缓存策略

- 热门作品列表缓存：按筛选条件缓存常用的作品列表
- 用户存储容量缓存：缓存用户的收藏和设计数量统计
- 分享数据缓存：缓存分享二维码和相关信息

### 3. 监控和日志

- 部位筛选使用统计：记录各部位的筛选频率
- 存储容量监控：监控用户存储使用情况
- 分享行为追踪：记录分享和扫码行为

### 4. 兼容性考虑

- 客户端版本兼容：新功能对老版本客户端透明
- API向下兼容：现有接口保持兼容，新增可选参数
- 数据格式兼容：确保新字段不影响现有数据处理逻辑