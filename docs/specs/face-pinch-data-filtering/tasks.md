# Implementation Plan

- [ ] 1. 数据库结构扩展和配置设置
  - 为 pyq_face_pinch_work 表添加 allowedParts 字段
  - 在 facePinchCfg 配置中添加 partsFeatureLaunchTime 时间配置（UTC+8 字符串格式）
  - 创建数据迁移脚本，为历史数据设置默认的 allowedParts 值
  - _Requirements: 1.1, 1.2, 6.1, 6.2, 6.3, 6.4_

- [ ] 2. 核心常量和工具函数实现
  - [ ] 2.1 定义部位常量和工具函数
    - 在 facePinch 组件中创建 FACE_PARTS 常量定义
    - 实现部位名称转换和验证的工具函数
    - 实现历史数据判断的工具函数
    - _Requirements: 1.1, 6.1, 6.2_

  - [ ] 2.2 实现部位筛选服务类
    - 创建 FacePartFilterService 类，实现部位筛选逻辑
    - 实现位运算查询条件构建
    - 实现历史数据的特殊处理逻辑
    - _Requirements: 2.2, 6.4_

- [ ] 3. 扩展现有API接口支持部位功能
  - [ ] 3.1 扩展作品上传接口
    - 修改 facePinchWorkAdd 函数，支持 allowedParts 参数
    - 添加部位选择的验证逻辑
    - 更新请求参数的 schema 验证
    - _Requirements: 1.1, 1.5, 1.6_

  - [ ] 3.2 扩展作品列表接口
    - 修改 facePinchWorkListPublic 函数，支持 filterParts 筛选参数
    - 修改 facePinchWorkListCollect 函数，支持部位筛选
    - 修改 facePinchWorkListSelf 函数，支持部位筛选
    - 在响应数据中添加 availableApplyParts 字段
    - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5_

  - [ ] 3.3 扩展作品详情和获取接口
    - 修改 facePinchWorkDetail 函数，返回部位相关信息
    - 修改 facePinchWorkGetByShareId 函数，返回 allowedParts 和 availableApplyParts 字段
    - 实现 getAvailableApplyParts 工具函数，处理历史数据的部位限制
    - _Requirements: 3.3, 3.4, 4.1, 4.2, 6.3_

- [ ] 4. 实现存储容量管理功能
  - [ ] 4.1 创建存储管理服务
    - 实现 StorageManagementService 类
    - 实现收藏和设计作品的统一容量检查（300个上限）
    - 添加容量使用情况的查询接口
    - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5_

  - [ ] 4.2 集成容量检查到收藏功能
    - 修改 facePinchWorkCollect 函数，添加容量检查
    - 修改收藏列表接口，返回容量使用情况
    - 修改我的设计列表接口，返回容量使用情况
    - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5_

- [ ] 5. 错误处理和验证逻辑
  - [ ] 5.1 定义部位相关错误码
    - 在 ApiErrors 中添加部位功能相关的错误定义
    - 实现 FacePinchErrorHandler 错误处理类
    - 添加部位验证和历史数据限制的错误处理
    - _Requirements: 1.5, 2.5, 3.4, 6.2_

  - [ ] 5.2 实现输入验证和业务规则检查
    - 添加 allowedParts 参数的格式验证
    - 实现历史数据的部位限制检查
    - 添加存储容量超限的错误处理
    - _Requirements: 1.5, 2.5, 3.4, 5.4, 6.1, 6.2_

- [ ] 6. 更新类型定义和接口文档
  - [ ] 6.1 扩展TypeScript类型定义
    - 更新 FacePinchReq 和 FacePinchRes 类型定义
    - 添加部位相关的请求和响应类型
    - 更新 WorkRecord 模型的类型定义
    - _Requirements: 1.1, 2.1, 4.1_

  - [ ] 6.2 更新API接口文档
    - 更新 OpenAPI 规范文档，添加新的请求参数
    - 更新响应数据结构的文档说明
    - 更新分享相关接口的响应字段文档
    - _Requirements: 1.1, 2.1, 4.1, 4.2_

- [ ] 7. 编写单元测试和集成测试
  - [ ] 7.1 部位筛选功能测试
    - 编写 FacePartFilterService 的单元测试
    - 测试位运算查询逻辑的正确性
    - 测试历史数据的特殊处理逻辑
    - _Requirements: 2.2, 2.6, 6.4_

  - [ ] 7.2 API接口集成测试
    - 编写作品上传接口的部位选择测试
    - 编写作品列表筛选功能的测试
    - 编写分享功能的部位信息返回测试
    - _Requirements: 1.1, 2.1, 4.1, 4.6, 4.7_

  - [ ] 7.3 存储容量管理测试
    - 编写 StorageManagementService 的单元测试
    - 测试容量限制和错误处理逻辑
    - 编写收藏功能的容量检查集成测试
    - _Requirements: 5.1, 5.2, 5.3, 5.4_

- [ ] 8. 性能优化和缓存实现
  - [ ] 8.1 数据库查询优化
    - 为 allowedParts 字段添加合适的数据库索引
    - 优化部位筛选查询的性能
    - 实现分页查询的性能优化
    - _Requirements: 2.2_

  - [ ] 8.2 缓存策略实现
    - 实现热门作品列表的缓存机制
    - 实现用户存储容量的缓存
    - 优化分享数据的缓存策略
    - _Requirements: 2.1, 4.1, 5.1_

- [ ] 9. 数据迁移和部署准备
  - [ ] 9.1 历史数据迁移
    - 编写历史数据的 allowedParts 字段填充脚本
    - 实现分批迁移避免性能影响
    - 验证迁移数据的正确性
    - _Requirements: 6.1, 6.3_

  - [ ] 9.2 监控和日志实现
    - 添加部位筛选使用情况的统计日志
    - 实现存储容量使用的监控
    - 添加分享行为的追踪日志
    - _Requirements: 2.1, 4.1, 5.1_