# Requirements Document

## Introduction

本功能为倩女手游捏脸站增加脸型数据筛选功能，允许用户在上传捏脸作品时选择允许他人使用的部位（全脸/塑形/妆容），并在浏览和应用作品时根据可用部位进行筛选和选择性应用。该功能将提升用户对捏脸数据的精细化控制能力，满足用户对不同部位数据的个性化需求。

## Requirements

### Requirement 1

**User Story:** 作为捏脸站用户，我希望在上传作品时能够选择允许他人使用的部位，以便更精确地控制我的作品被他人使用的方式

#### Acceptance Criteria

1. WHEN 用户进入上传界面 THEN 系统 SHALL 显示允许他人使用部位的选择选项，包含【全脸】【塑形】【妆容】三个选项
2. WHEN 用户选择【全脸】选项 THEN 系统 SHALL 允许他人使用包含妆容+塑形的完整数据，且两项数据绑定不可拆分
3. WHEN 用户选择【塑形】选项 THEN 系统 SHALL 仅允许他人使用脸型的塑形数据，不包含妆容数据
4. WHEN 用户选择【妆容】选项 THEN 系统 SHALL 仅允许他人使用妆容数据，不包含塑形数据
5. WHEN 用户未选择任何选项或全部取消勾选 THEN 系统 SHALL 弹出提醒消息，提醒玩家至少选择一项
6. WHEN 用户点击上传按钮 THEN 系统 SHALL 弹出通用tips弹窗，详细说明各类数据的含义和用途

### Requirement 2

**User Story:** 作为捏脸站用户，我希望在浏览捏脸库、收藏和我的设计时能够筛选可用部位，以便快速找到符合我需求的作品

#### Acceptance Criteria

1. WHEN 用户进入捏脸库、收藏或我的设计界面 THEN 系统 SHALL 在左侧显示筛选可用部位功能，包含【全脸】【塑形】【妆容】选项
2. WHEN 用户选择筛选条件 THEN 系统 SHALL 仅显示包含所选部位数据的脸型作品
3. WHEN 用户在某个界面设置筛选条件 THEN 系统 SHALL 同步应用到捏脸库、收藏、我的设计三个界面
4. WHEN 系统显示脸型预览 THEN 系统 SHALL 在预览处显示角标，标明该脸型的可用部位
5. WHEN 筛选条件为空或全部取消勾选 THEN 系统 SHALL 弹出提醒消息，提醒玩家至少选择一项
6. WHEN 显示历史上传的老数据 THEN 系统 SHALL 仅允许应用【全脸】或【妆容】，不可单独使用【塑形】

### Requirement 3

**User Story:** 作为捏脸站用户，我希望在应用作品时能够选择应用的具体部位，以便只使用我需要的部分数据

#### Acceptance Criteria

1. WHEN 用户点击应用按钮 THEN 系统 SHALL 显示选择应用部位功能，允许用户选择要应用的具体部位
2. WHEN 用户选择应用部位 THEN 系统 SHALL 预览该选择对应的效果（例如只勾选妆容会预览这个妆容在自己脸上的效果）
3. WHEN 作品包含多项数据 THEN 系统 SHALL 仅允许用户选择该作品实际包含的数据项
4. WHEN 作品为历史老数据 THEN 系统 SHALL 仅显示【全脸】或【妆容】选项，不显示【塑形】选项
5. WHEN 用户确认应用 THEN 系统 SHALL 仅应用用户选择的部位数据

### Requirement 4

**User Story:** 作为捏脸站用户，我希望在分享作品时能够选择分享的具体部位数据，以便精确控制分享内容

#### Acceptance Criteria

1. WHEN 用户进入数据分享界面 THEN 系统 SHALL 显示分享全脸/塑形/妆容数据的选项
2. WHEN 用户选择分享选项 THEN 系统 SHALL 显示对应的二维码和该选项的详细信息
3. WHEN 用户选择【全脸】 THEN 系统 SHALL 生成包含完整数据的分享二维码
4. WHEN 用户选择【塑形】 THEN 系统 SHALL 生成仅包含塑形数据的分享二维码
5. WHEN 用户选择【妆容】 THEN 系统 SHALL 生成仅包含妆容数据的分享二维码
6. WHEN 捏脸站外扫描分享二维码 THEN 系统 SHALL 跳转到大神页面，导入到指定roleId的收藏界面，在收藏中选择应用
7. WHEN 捏脸站内扫描分享二维码 THEN 系统 SHALL 直接应用对应数据，不导入捏脸站，也不跳转大神

### Requirement 5

**User Story:** 作为捏脸站用户，我希望收藏和我的设计功能有更大的存储容量，以便保存更多喜欢的作品和自己的设计

#### Acceptance Criteria

1. WHEN 用户使用收藏功能 THEN 系统 SHALL 允许用户收藏最多300个作品
2. WHEN 用户使用我的设计功能 THEN 系统 SHALL 允许用户保存最多300个自己的设计作品
3. WHEN 收藏和我的设计总数量达到300个 THEN 系统 SHALL 共用这个300的上限，不区分收藏和设计的具体数量分配
4. WHEN 用户尝试超过300个上限时 THEN 系统 SHALL 提示用户已达到存储上限，需要删除旧内容才能添加新内容
5. WHEN 用户查看收藏或我的设计列表 THEN 系统 SHALL 显示当前已使用的数量和总上限（如：285/300）

### Requirement 6

**User Story:** 作为系统管理员，我希望系统能够正确处理历史数据的兼容性，确保老数据的正常使用

#### Acceptance Criteria

1. WHEN 系统处理历史上传的老数据 THEN 系统 SHALL 仅允许应用【全脸】或【妆容】选项
2. WHEN 用户尝试单独使用老数据的【塑形】 THEN 系统 SHALL 不允许该操作
3. WHEN 显示老数据的可用部位 THEN 系统 SHALL 正确标识其可用的部位类型
4. WHEN 用户筛选包含【塑形】的作品 THEN 系统 SHALL 不显示不支持单独塑形应用的老数据