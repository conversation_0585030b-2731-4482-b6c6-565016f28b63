var fs = require('fs'),
    iconv = require('iconv-lite'),
    xlsx = require('node-xlsx');

var inputFile = '宝石ID.xlsx',
    outputFile = '../../service/qnm/data/GemImg.js';
    indexHash = {
        黄水晶:		'01',
        紫水晶:		'02',
        蓝宝石:		'03',
        红宝石:		'04',
        紫玉:		'05',
        白玉髓:		'06',
        碧玉髓:		'07',
        萤石:		'08',
        珍珠:		'09',
        玛瑙:		'10',
        珊瑚:		'11',
        翡翠:		'12',
        琥珀:		'13',
        孔雀石:		'14',
        桃花石:		'15',
        锡兰石:		'16',
        夜明珠:		'17',
        堇青石:		'18',
        透辉石:		'19',
        幻色石:		'20',
        碧玺:		'21',
        石榴石:		'22',
        猫眼石:		'23',
        虎眼石:		'24',
        橄榄石:		'25',
        丹泉石:		'26',
        偶泊石:		'27',
        东陵玉:		'28',
        日长石:		'29',
        月长石:		'30',
        雨花石:		'31',
        焕彩石:		'32',
        羊脂玉:		'33',
        青金石:		'34',
        芙蓉石:		'35',
        慕君石:		'36',
        雪花晶:		'37',
        昆吾石:		'38'

    };
function parseXlsx(file) {
    var sheets = xlsx.parse(file),
        sheet = sheets[0],
        dataList = [],
        data = sheet.data;
    for (var j = 0, k = data.length; j < k; j++) {
        var d = data[j],
            id = (d[0] || '') + '',
            name = d[1];
        if (id.length === 8) {
            var item = [name, ' Item_Jewel/00' + indexHash[name.replace(/\d{1,2}级/, '')] + '.png'];
            dataList.push(id + ':' + JSON.stringify(item));
        }
    }

    fs.writeFile(outputFile, 'var hash={\r\n' + dataList.join(',\r\n') + '\r\n};\r\nmodule.exports=hash;', function(err) {
        console.log('Write done!');
    });
}

parseXlsx(inputFile);