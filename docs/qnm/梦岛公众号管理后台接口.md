## 梦岛公众号管理后台接口

### API约定

#### 接口路径前缀
```typescript
let API_PREFIX_FOR_TEST = 'http://***************:88/audit/l10_admin'
let API_PREFIX_FOR_PROC = 'http://api.hi.163.com/audit/l10_admin'
```

#### 接口HTTP方法
所有接口都支持 `GET` 和 `POST`

#### 接口返回格式

Content-Type: application/json

成功示例:
```js
{
  "status": "ok", 
  "result": {id: 3} 
}
```

失败示例
```js
{
  "status": "fail", 
  "result": {"message": "an error"}
}
```

#### 接口授权
ip白名单机制


### 接口方法列表

#### 获取账号信息
GET ${apiPrefix}/getPlayerInfo?roleId=1005

#### 返回示例
```json
{
  "status": "ok",
  "result": {
    "RoleId": 1005,
    "Avatar": "http://hi-163-qnm.nosdn.127.net/upload/201801/26/93afccf0026311e8aa2a81baabacd654",
    "Location": "",
    "Signature": "金陵情报官",
    "RoleName": "哈哈情报官"
  }
}
```

### 更新账号信息
POST ${apiPrefix}/updatePlayerInfo?roleId=1005&roleName=哈哈情报官

#### 参数列表
| 参数      | 必选   | 类型     | 说明          |
| :------ | :--- | :----- | ----------- |
| roleId | true | int    | 账号Id |
| roleName | false | string | 角色名 |
| avatar | false | string | 头像 |
| location | false | string | 位置 |
| signature | false | string | 个人签名 |

> 参数可以传一个或多个， 只更新传递的参数

#### 返回示例
和获取账号信息结构一致， 返回更新后的账号信息

### 获取公众号心情列表
GET ${apiPrefix}/listMoments?lastId=15938801342&limit=1&roleId=1002

> 从新到旧排列

#### 参数列表
| 参数      | 必选   | 类型     | 说明          |
| :------ | :--- | :----- | ----------- |
| roleId | true | int    | 账号Id |
| lastId | false | int    | 心情id， 返回比该id小的（更旧的心情列表） |
| limit | false | int | 数量 { min: 1, default: 10, max: 20 } |


#### 返回示例
```json
{
  "status": "ok",
  "result": {
    "list": [
      {
        "ID": 15938801340,
        "RoleId": 1002,
        "RoleName": "rolename",
        "ImgList": [
          "https://hi-163-qnm.nosdn.127.net/upload/2018-06-27/7b6c1f26e1c2bcc329d1ea9a427e4edd.jpg"
        ],
        "Text": "sb",
        "CreateTime": 1530153785284,
        "LikeCount": 0,
        "CommentCount": 0
      }
    ]
  }
}
```

### 获取心情评论列表
GET ${apiPrefix}/listComments?momentId=12

#### 参数列表
| 参数      | 必选   | 类型     | 说明          |
| :------ | :--- | :----- | ----------- |
| momentId | true | int    | 心情Id  |
| lastId | false | int    | 评论id， 返回比该id小的（更旧的评论列表） |
| limit | false | int | 数量 { min: 1, default: 10, max: 20 } |

#### 返回示例
```json
{
  "status": "ok",
  "result": {
    "list": [
      {
        "ID": 22,
        "RoleId": 101200014,
        "RoleName": "寶寶不乐意了",
        "Text": "fhgfhgh",
        "CreateTime": 1472708638363
      }
    ]
  }
}
```

### 添加心情

#### 请求示例
POST ${apiPrefix}/addMoment

HTTP Header: Content-Type: application/json

Body
```json
{
  "roleId": 1002,
  "text": "testMoment",
  "imgs": ["http://nos.netease.com/hi-163-common/moment/201606/17/b36b8920346111e6ab8c297d0ba6eb36.png"],
  "topAt": 1531465010226 ,
  "topTo": 1
}
```

#### 参数说明
| 参数      | 必选   | 类型     | 说明          |
| :------ | :--- | :----- | ----------- |
| roleId | true | int    | 账号Id |
| text | false | string | 心情文本|
| imgs | false | Array | 心情图片 |
| topAt | true | int | 置顶截止时间（毫秒级时间戳）|
| topTo | true | int | 置顶到  1=> 本服， 2 => 全服 |


####  返回示例
```json
{
  "status": "ok",
  "result": {
    "id": 15938801360
  }
}
```


### 删除心情

POST ${apiPrefix}/delMoment?momentId=15938801341&roleId=1002

#### 返回示例
```json
{
  "status": "ok",
  "result": {
    "message": "(Rows matched: 1  Changed: 0  Warnings: 0"
  }
}
```

### 添加心情评论
POST ${apiPrefix}/addComment?roleId=1002&momentId=15938801187&text=testComment

#### 返回示例
```json
{
  "status": "ok",
  "result": {
    "id": 33094
  }
}
```

### 添加评论回复
POST ${apiPrefix}/replyComment?roleId=1005&commentId=2&text=testComment

#### 参数说明
| 参数      | 必选   | 类型     | 说明          |
| :------ | :--- | :----- | ----------- |
| commentId | true | int    | 评论Id |
| roleId | true | int    | 账号Id |
| text | true | string | 心情文本|

#### 返回示例
```json
{
  "status": "ok",
  "result": {
    "id": 33094
  }
}
```

### 删除评论
POST ${apiPrefix}/delComment?roleId=1005&commentId=2

#### 参数说明
| 参数      | 必选   | 类型     | 说明          |
| :------ | :--- | :----- | ----------- |
| commentId | true | int    | 评论Id |
| roleId | true | int    | 账号Id |

#### 返回示例
```json
{
  "status": "ok",
  "result": null
}
```

### 转发心情
POST ${apiPrefix}/forwardMoment?roleId=1002&momentId=15938801340&text=forwardTest

#### 返回示例
```json
{
  "status": "ok",
  "result": {
    "momentId": 15938801358,
    "forwardId": 15938801340,
    "originId": 15938801340
  }
}
```

### 点赞心情
POST ${apiPrefix}/likeMoment?roleId=1002&momentId=15938801340

#### 返回示例
```json
{
  "status": "ok",
  "result": null
}
```


### 取消点赞心情
POST ${apiPrefix}/cancelLikeMoment?roleId=1002&momentId=15938801340

#### 返回示例
```json
{
  "status": "ok",
  "result": null
}
```

###   列出全服热点
GET ${apiPrefix}/listHotMoments?page=1&pageSize=1
###

#### 返回示例
```json
{
  "status": "ok",
  "result": {
    "list": [
      {
        "ID": 832744,
        "RoleId": 12432500012,
        "RoleName": "会飞的胖露露",
        "ImgList": [],
        "Text": "#18#18hah",
        "CreateTime": 1486436679611,
        "LikeCount": 1,
        "CommentCount": 3
      }
    ]
  }
}
```