var fs = require('fs'),
    iconv = require('iconv-lite'),
    xlsx = require('node-xlsx');

var inputFile = './EquipmentTemplate.xlsx',
//    inputFile = '装备ID.xlsx',
    outputFile = '../../service/qnm/data/EquipImg.js',
    dataFormat = {
        '武器@Equip': 1,
        '盾牌@Equip': 1,
        '头盔@Equip': 1,
        '甲胄@Equip': 1,
        '腰带@Equip': 1,
        '手套@Equip': 1,
        '鞋子@Equip': 1,
        '戒指@Equip': 1,
        '手镯@Equip': 1,
        '项链@Equip': 1,
        '法宝@Equip': 1
    };
function parseXlsx(file) {
    var sheetNum = 0;
    for (var key in dataFormat) {
        sheetNum++;
    }

    var sheets = xlsx.parse(file);

    fs.writeFile(outputFile, 'var hash={\r\n', function(err) {});
    for (var i = 0, l = sheets.length; i < l; i++) {
        var sheet = sheets[i],
            name = sheet.name,
            format = dataFormat[name];
        if (!format) {
            continue;
        }

        var dataList = [],
            data = sheet.data;
        for (var j = 0, k = data.length; j < k; j++) {
            var d = data[j],
                id = (d[0] || '') + '';
            if (id.length === 8) {
//                var item = [d[1], handlePath(d[9], 0), handlePath(d[10], 1)];
                var item = [d[1], handlePath(d[34], 0), handlePath(d[35], name == '法宝@Equip' ? 0 : 1), d[2], d[29]];
                dataList.push(d[0] + ':' + JSON.stringify(item));
            }
        }

        fs.appendFile(outputFile, dataList.join(',\r\n') + (--sheetNum == 0 ? '' : ',\r\n'), function(err) {
            console.log('Write done!');
        });
    }
    fs.appendFile(outputFile, '\r\n};\r\nmodule.exports=hash;', function(err) {});
}

function handlePath(file, bigFile) {
    return file.replace('UI/Texture/', '').replace('Material/', '').replace('.mat', bigFile ? '.png' : '.jpg');
}

parseXlsx(inputFile);
