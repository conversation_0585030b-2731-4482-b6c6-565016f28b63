var fs = require('fs'),
    iconv = require('iconv-lite'),
    xlsx = require('node-xlsx');

var inputFile = '字库.xlsx',
    outputFile = '../../service/qnm/data/TextRegList.js';
function parseXlsx(file) {
    var dataList = [],
        sheets = xlsx.parse(file),
        sheet = sheets[0],
        data = sheet.data;
    for (var j = 0, k = data.length; j < k; j++) {
        var expr = data[j][0];
        if (!expr) {
            return;
        }

        var item = [];
        // 兼容处理
        expr = expr
            .replace(/\(\?#.+?\)/g, '')      // 不支持注释
            .replace('(?i)', function (match) {  // 忽略大小写
                item[1] = 'i';
                return '';
            })
            .replace(/\(\?<\!(.+?)\)/g, function (match, inner) {      // 不支持零宽度正回顾后发断言
                return '([^' + inner + '])';    // Note: 可行？
            });
        item[0] = expr;
        dataList.push(JSON.stringify(item));
    }

    fs.writeFile(outputFile, 'var list=[\r\n' + dataList.join(',\r\n') + '\r\n];\r\nmodule.exports=list;', function(err) {
        console.log('Write done!');
    });
}

parseXlsx(inputFile);