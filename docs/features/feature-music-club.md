# 百相-演奏 功能需求文档

## 1. 文档信息
| 项目 | 内容 |
|------|------|
| 需求名称 | 百相-演奏 |
| 需求背景 | 配合"打歌服"外观上线，推出"百相-演奏"身份，实现玩家唱片内容管理和排行榜功能 |
| 上线时间 | 2025年7月24日 |
| 交付时间 | 2025年6月13日 |
| 接口文档提供时间 | 2025年5月中下旬 |
| 游戏封包时间 | 2025年7月3日开分支，7月10日打包 |

## 2. 功能概述
### 2.1 核心功能
1. 唱片资源管理
2. 排行榜系统
3. 点播交互系统

### 2.2 用户角色
- 乐团成员
- 乐团经理
- 普通用户
- 系统管理员

## 3. 功能详细说明

### 3.1 唱片资源管理
#### 3.1.1 唱片上架
- **上传权限**：乐团所有成员
- **上传内容**：
  - 唱片文件（包含人声、伴奏、歌词文件、人声偏移及音量设定）
  - 副歌试听时间信息
  - 唱片基本信息（名称、乐团名）
- **内容校验**：
  - 唱片名长度限制：最多14个字符
  - 唱片名需通过易盾审核
  - 检查乐团上架数量限制

#### 3.1.2 唱片下架
- **手动下架**：
  - 触发条件：乐团经理操作
  - 操作方式：从乐团作品中删除
- **自动下架**：
  - 触发条件：14天无人点播
  - 系统行为：向乐团经理发送通知
- **强制下架**：
  - 触发条件：
    1. 乐团解散
    2. 被举报后人工审核
    3. 14天内违规两次
  - 处罚措施：违规两次的乐团禁止上架30天

### 3.2 排行榜系统
#### 3.2.1 统计范围
- 官服 + 渠道服（优先满足官服）

#### 3.2.2 热度计算规则
- **乐团热度** = 演出点赞 + 演出送花 + 唱片点播
- **唱片热度** = 唱片点播数 × 点播消耗的灵玉数
- 热度值持续累加，不随时间衰减

#### 3.2.3 排行榜分类
1. **唱片排行榜**
   - **热门榜**：按点播次数排序
   - **高分榜**：按平均评分排序
   - **新晋榜**：本周获得热度最多的唱片（与热门榜不重复）
   - **展示内容**：
     - 唱片名称（最多14字符）
     - 乐团名称
     - 平均评分
     - 播放次数

2. **乐团排行榜**
   - **总热度榜**：展示前50名
   - **新晋榜**：展示前20名（本周热度最高）
   - **展示内容**：
     - 名次
     - 乐团名称（服务器名-乐团名，最多7字）
     - 主打唱片（点播次数最高）
     - 乐团热度

### 热度数据来源

来自游戏日志  日志来源  杨凯 游戏程序 <EMAIL>
```json
[2025-06-25 21:55:20][BandHeat],{"server":"1", "band_id":"300001", "band_name":"yk的乐团", "band_level":3, "heat":60, "week_heat":50, "time":1750859718, "show_amount":10, "recording_amount":0, "u_dtls":[]}
```

#### 日志字段说明
heat是乐团总热度
week_heat是乐团本周获取的热度（每周一0点重置）
time是这次获取的时间，用这个时间判断所在的周

show_amount 本次演出加的热度
recording_amount 本次唱片加的热度



#### 3.2.4 更新机制
- 更新频率：每日0点
- 统计周期：周一0点至周日24点

### 3.3 日志管理
#### 3.3.1 记录内容
- 乐团相关：送花、点赞记录
- 唱片相关：点播、评分记录

## 4. 技术注意事项
1. 需要与游戏端确认每页展示数量
2. 热度值和行为的对应关系待确认（预计5月中旬提供）
3. 渠道服统计可行性需要评估
4. 日志系统需要支持数据分析和统计

## 5. 后续规划
1. 等待游戏端提供具体接口文档
2. 确认热度计算的具体参数
3. 评估渠道服数据统计方案
