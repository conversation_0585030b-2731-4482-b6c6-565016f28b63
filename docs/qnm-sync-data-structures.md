# QNM 数据同步接口分析文档

## 接口概述

**接口路径**: `/gdc/qnm.do`  
**请求方法**: POST  
**服务器**: qnm-server/app.ts  
**端口**: 1594  

该接口是倩女手游客户端数据推送服务器的核心接口，负责接收游戏客户端推送的各种角色数据并同步到数据库。

## 请求参数结构

### 基础参数 (IPushParams)
```typescript
interface IPushParams<T = unknown> {
  rawcontent?: string;    // 原始内容（JSON字符串）
  content: T;             // 解析后的内容对象
  roleid: string;         // 角色ID
  serverid: string;       // 服务器ID
  name: string;           // 同步事件类型
  time: string;           // 时间戳
  lastLoginIp?: string;   // 最后登录IP
  token?: string;         // 验证令牌
}
```

## 同步事件类型 (ESyncEventTypes)

### 1. roleinfo - 角色基本信息同步

**作用**: 同步角色的基本信息，包括等级、职业、称号、帮派等核心数据

**数据结构**:
```typescript
interface RoleInfoContent {
  name: string;           // 角色名
  clazz: number;          // 职业ID
  grade: number;          // 等级
  gender: number;         // 性别
  xiuwei: number;         // 修为
  vip: number;            // VIP等级
  xianfanstatus: number;  // 仙凡状态
  server_name: string;    // 服务器名称
  server_id: string;      // 服务器ID
  used_name: string[];    // 曾用名列表
  
  // 称号信息
  title: {
    id: number;           // 称号ID
    name: string;         // 称号名称
  };
  
  // 帮派信息
  guild: {
    id: number;           // 帮派ID
    name: string;         // 帮派名称
  };

  //这上面存储qnm_roleinfo
  

  //shifu 到 LastLoginIp 字段存储在profile里面
  // 师父信息
  shifu?: {
    roleid: number;       // 师父角色ID
    name: string;         // 师父名称
  };
  
  // 徒弟信息
  tudi?: Array<{
    roleid: number;       // 徒弟角色ID
    name: string;         // 徒弟名称
  }>;
  
  // 结婚信息
  wedding?: { //默认是{"weddingservername":null,"weddingtime":0,"weddingindex":0,"partner":{"id":0,"clazz":null,"name":null,"gender":null,"grade":null,"xianfanstatus":0},"self":{"id":0,"clazz":null,"name":null,"gender":null,"grade":null,"xianfanstatus":0}}
    "weddingservername": string,
    "weddingtime": number,
    "weddingindex": number,
    "partner": {
        "id": number, //玩家id
        "clazz": string, //"5"
        "name": string,  //玩家昵称
        "gender": string,  //"1"
        "grade": string, //"89"
        "xianfanstatus": number //0
    },
    "self": {
        "id": 0,
        "clazz": null,
        "name": null,
        "gender": null,
        "grade": null,
        "xianfanstatus": 0
    }
  };
  
  // 靓号信息
  //默认空是{"id":0,"expiredtime":0,"hideicon":0,"showdigit":0}
  //数据示例 无
  lianghao?: {  //同时存储qnm_lianghao
    id: number;           // 靓号ID
    expiredtime: number;  // 过期时间
    hideicon: number
    showdigit: number
  };
  
  // 表情包信息
  expression_base?: any;

  lastLoginIp : string
}
```

**对应数据库表**:
- `qnm_roleinfo` - 角色基本信息表
- `pyq_profile` - 朋友圈档案表
- `qnm_lianghao` - 靓号表

#### 单日数据量 160w

### 2. roleequip - 角色装备信息同步

**作用**: 同步角色的装备信息

**数据结构**:
```typescript
interface RoleEquipContent {
  [key: `equip${string}`]: {
    templateid: number;   // 装备模板ID
    pos: number;          // 装备位置
    equipname: string;    // 装备名称
    // 其他装备属性...
  };
}
```

**对应数据库表**:
- `qnm_roleprop` - 角色属性表（EquipInfo字段）

数据量: 单日: 160w

### 3. rolelingshou - 角色灵兽信息同步

**作用**: 同步角色的灵兽（宠物）信息

**数据结构**:
```typescript
interface RoleLingshouContent {
  lingshous: Array<{
    lingshouId: string;       // 灵兽唯一ID
    templateName: string;     // 灵兽模板名称
    aliasName: string;        // 灵兽别名
    level: number;            // 灵兽等级
    quality: number;          // 品质
    nature: number;           // 性格
    gender: number;           // 性别
    type: number;             // 类型
    zizhi: number;            // 资质
    chengzhang: string;       // 成长值
    wuxing: string;           // 五行
    zhandouli: number;        // 战斗力
    hp: number;               // 生命值
    attack: number[];         // 攻击力范围 [最小值, 最大值]
    skills: number[];         // 技能ID数组
    isMorphed: boolean;       // 是否变身
    isMarried: boolean;       // 是否结婚
    
    // 资质属性
    strZiZhi: number;         // 力量资质
    agiZiZhi: number;         // 敏捷资质
    intZiZhi: number;         // 智力资质
    staZiZhi: number;         // 体质资质
    corZiZhi: number;         // 韧性资质
  }>;
}
```

数据量: 单日 110w

**对应数据库表**:
- `qnm_roleanimal` - 角色灵兽表

### 4. event - 动态事件同步

**作用**: 同步角色的动态事件，如帮派活动、重要成就等

**数据结构**:
```typescript
interface EventContent {
  type: number;           // 事件类型
  parameter: {            // 事件参数
    [key: string]: any;
  };
}
```
**示例数据**

```
{
  "type": 32,
  "parameter": {"beautyname":"","bossid":0,"bossname":"","beautyid":0,"guildname":"兰溪书苑","duration":1127,"guildid":3501182,"isHero":0}
}
```

**对应数据库表**:
- `qnm_roleevent` - 角色事件表

数量量级: 60万+

### 5. syncfriend - 同步好友列表

**作用**: 全量同步角色的好友列表

**数据结构**:
```typescript
interface SyncFriendContent {
  friends: Array<{
    roleid: number;       // 好友角色ID
    // 其他好友信息...
  }>;
}
```
单日数据量: 300w

**对应数据库表**:
- `pyq_profile` - 朋友圈档案表（FriendList字段）

### 6. addfriend - 添加好友

**作用**: 单向添加好友关系

**数据结构**:
```typescript
interface AddFriendContent {
  roleid: number;         // 要添加的好友角色ID
}
```

**对应数据库表**:
- `pyq_profile` - 朋友圈档案表（FriendList字段）

数据量: 单日 11w

### 7. delfriend - 删除好友

**作用**: 单向删除好友关系

**数据结构**:
```typescript
interface DelFriendContent {
  roleid: number;         // 要删除的好友角色ID
}
```
数据量: 单日 3w

**对应数据库表**:
- `pyq_profile` - 朋友圈档案表（FriendList字段）

### 8. syncBlackList - 同步黑名单

**作用**: 同步角色的黑名单列表

**数据结构**:
```typescript
interface SyncBlackListContent {
  blackList: number[];    // 黑名单角色ID数组
}
```

数据量: 单日: 300w

**对应数据库表**:
- `pyq_profile` - 朋友圈档案表（BlackList字段）

### 9. rolebingqipu - 角色兵器谱装备

**作用**: 同步角色在兵器谱系统中的装备信息

**数据结构**:
```typescript
interface RoleBingQiPuContent {
  equipId: string;        // 装备唯一ID
  equipscore: string;     // 装备评分
  equips: Array<{
    pos: number;          // 装备位置
    templateid: number;   // 装备模板ID
    color: number;        // 装备颜色品质
    equipname: string;    // 装备名称
    type: string;         // 装备类型
    words: string[];      // 装备词条属性
    jewelids: number[];   // 宝石ID数组
    jewelwords: string[]; // 宝石属性描述
    holenum: number;      // 宝石孔数
    duration: string;     // 耐久度 "当前/最大"
    score: number;        // 装备评分
    gemgroup: string;     // 宝石组合名称
    gemwords: string[];   // 宝石组合属性
    levellimit: number;   // 等级限制
    props: string[];      // 基础属性
    perfection: number;   // 完美度
    intensifylevel: number; // 强化等级
  }>;
}
```



**对应数据库表**:
- `qnm_bingqipu_equip` - 兵器谱装备表

数据量级: 29w

## 数据库表结构说明

### 主要数据表

1. **qnm_roleinfo** - 角色基本信息表
   - 存储角色的基本属性：姓名、等级、职业、性别、修为、VIP等级等
   - 包含称号、帮派、服务器等关联信息

2. **qnm_roleprop** - 角色属性表
   - 存储角色的装备信息（EquipInfo字段，JSON格式）
   - 存储角色的基础属性（PropertyInfo字段，JSON格式）
   - 存储角色的抗性等其他属性（OtherInfo字段，JSON格式）

3. **pyq_profile** - 朋友圈档案表
   - 存储角色的社交信息：好友列表、黑名单、师父徒弟、结婚信息等
   - 存储角色的个性化信息：签名、头像、隐私设置等

4. **qnm_roleanimal** - 角色灵兽表
   - 存储角色的灵兽信息（AnimalInfo字段，JSON格式）

5. **qnm_roleevent** - 角色事件表
   - 存储角色的动态事件记录

6. **qnm_lianghao** - 靓号表
   - 存储角色的靓号信息和过期时间

7. **qnm_bingqipu_equip** - 兵器谱装备表
   - 存储角色在兵器谱系统中的装备详情

## 数据流程

1. **接收请求**: 客户端POST数据到 `/gdc/qnm.do`
2. **身份验证**: 通过token验证请求合法性
3. **数据解析**: 解析rawcontent中的JSON数据
4. **队列处理**: 数据先进入Redis队列 `qnm:sync_info_queue`
5. **批量同步**: 定时任务批量处理队列中的数据
6. **数据存储**: 根据事件类型分别存储到对应的数据库表

## 配置说明

- **队列批量大小**: 50条记录
- **黑名单最大数量**: 50个
- **数据分表策略**: 根据服务器ID区分网易服和其他服务器
- **缓存策略**: 使用Redis缓存提高性能

## 安全机制

- **Token验证**: 使用MD5签名验证请求合法性
- **参数校验**: 使用AJV进行严格的参数类型检查
- **错误处理**: 完善的异常捕获和日志记录机制
