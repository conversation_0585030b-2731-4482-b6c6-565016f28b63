需求梳理：5月25日-5月31日@吴萌

后端开发：7月5日-7月19日@王振华

QA接口测试：7月26日-7月31日@孙梦超@张启

游戏交付联调：8月1日交付游戏

游戏验收版本：8月10日
正式上线：9月21日

需求文档：

https://note.youdao.com/ynoteshare/index.html?id=bb6c6e9ee583033d4085b42146ec61e0&type=note&_time=*************

未来捏脸站网页：

https://qnm.163.com/avatar/qrcode/?shareId=

接口文档:

http://md-test.leihuo.netease.com:8881/rapidoc/l10?grep=face_pinch#get-/face_pinch/work/get_by_share_id


Progress:
- [x] 预创建的时候需要保留account然后使用第一个角色的名字作为作者名字
- [x] 修改现有的auth/login支持roleId不存在的时候使用accountId作为身份
- [x] account存在roleId=0的数据需要被新的角色继承
- [ ] 使用新的search方案，非redis-search