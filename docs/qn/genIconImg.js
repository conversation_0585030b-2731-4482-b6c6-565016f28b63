/**
 * Created by zhenhua on 2017/2/21.
 */
const fs = require('fs');
const iconv = require('iconv-lite');
const _ = require('lodash');
const path = require('path');

const readFileGbk = function (filename) {
  const buffer = fs.readFileSync(filename);
  return iconv.decode(buffer, 'gbk');
};

const strToLines = str => str.split(/\r?\n/);

const main = function () {
  const dataFormat = [
    // {filePath: '/tmp/cbg/藏宝阁完整资源列表.txt', output: 'EquipImg.js'},
    // {filePath: '/tmp/cbg/藏宝阁中宝石的16图标.txt', output: 'GemImg.js'},
    {filePath: '/tmp/cbg/人物技能所有等级的中文描述.txt', output: 'SkillImg.js'}
  ];

  _.forEach(dataFormat, format => {
    const content = readFileGbk(format.filePath);
    const lines = _.compact(strToLines(content));
    const newLines = lines.map(line => {
      const tokens = line.split(/\s+/);
      return tokens[0] + ":" + JSON.stringify(_.tail(tokens)) + ',';
    });

    fs.writeFileSync(`../../service/qn/data/${format.output}`,  _.concat(`var hash={`, newLines, '};', 'module.exports=hash;').join('\r\n'));
  });
};

main();