【数据中心接口】
1、职业列表
	0=>'',
	1=>'射手',
	2=>'金刚甲士',
	3=>'刀客',
	4=>'侠客',
	5=>'方士',
	6=>'医师',
	7=>'魅者',
	8=>'异人',
	9=>'偃师',
	10=>'画魂',
	102=>'战魂甲士'

2、服务器列表
http://qn2admin.x.netease.com:8660/confs/qnyh/serverlist_md.ini

3、指定服务器ID、排行分类ID查排行
http://114.113.197.22:8080/api/queryrank/serverid/312/rankid/4181163

4、指定urs帐号查角色列表
http://114.113.197.22:8080/api/listcharacter/account/<EMAIL>

5、指定roleId查角色信息
http://114.113.197.22:8080/api/querycharacterbasicinfo/playerid/**********
http://114.113.197.22:8080/api/querycharacterbasicinfo3/playerid/*********/serverid/308

6、跨服排行
http://114.113.197.22:8080/api/queryattrrank/server/all/rankid/4181001/ranknum/100
这是查询全局排行榜的接口
	server填id的话查找那一个服务器，all的话是全服
	rankid就是之前给你的
	ranknum是排序取前多少个


【游戏接口】
=============1、帮会ID对应帮会名
2、角色信息
[roleinfo <= playerinfo]
	头像		/gdc/qn.do?name=uploadphoto
	服务器ID
	职业		Class
	等级
	帮会ID		Guild
	
	修为
	装备
	财富
	经验 ?
[roleprop	<= usergameproperty]
	装备		/gdc/qn.do  len=xxx&name=roleinfo
	能力		/gdc/qn.do  len=xxx&name=roleinfopoint
[roleanimal	<= roleanimalinifo]
	灵兽		/gdc/qn.do  len=xxx&name=lingshou
[roleevent	<= gameinfo]
	动态		/gdc/qn.do  len=xxx&name=event
[roleskill]	
	技能		/gdc/qn.do  len=xxx&name=skillprop
[rolehome]
	领地缩略图	/gdc/qn.do	name=uploadphoto
					filename=pic_jiayuan
	风水图	/gdc/qn.do	name=uploadphoto
					filename=pic_fengshui
	建筑等级	/gdc/qn.do  len=xxx&name=houselevel
	传家宝、祈福/gdc/qn.do  len=xxx&name=cjbinfo
[rolechild]
	宝宝		/gdc/qn.do  len=xxx&name=childprop
	宝宝图片	/gdc/qn.do	name=uploadphoto
					filename=pic_childxxxxx
		
	
