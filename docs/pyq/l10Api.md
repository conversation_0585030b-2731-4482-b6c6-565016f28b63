
倩女手游朋友圈API文档
-----------
### API 概述
倩女手游的API统一使用POST请求， 返回格式为json， 约定正确情况和错误情况如下

正确时的返回
```javascript
  {
    code: 0,
    data: {}
  }
```

错误时的返回
```javascript
  {
    code: -1,
    msg: "err message"
  }
```
所以api是否正确，需要根据返回json里的code字段来判断


### 登陆验证相关

#### 登陆
POST `/qnm/auth/login`

#### 接口描述
 登陆梦岛获取凭证skey，后续请求携带该skey

#### 请求参数
| 参数      | 必选   | 类型     | 说明          |
| :------ | :--- | :----- | ----------- |
| time    | true | string | 当前时间戳       |
| account | true | string | 账户          |
| server  | true | string | 角色的serverId |
| urs     | true | string | 角色的urs      |
| roleid  | true | string | 玩家的角色Id     |
| token   | true | string | 验证请求的token  |

token的计算方式为 md5(time + account + urs + roleId + API_SECRET), API_SECRET由网站提供给调用方

#### 返回示例
返回的skey需要保存，之后所以接口调用都需要携带该skey
```Javascript
  {
    code: 0,
    data: {
       skey: WzEwMDAsInJvbGUiLDEwMDJd.F23IjafJP2iEdfgZ0GkHfkO6o21ndjlmoVT5wHCQ954
       flower: 12
       flowerqnqi: 13
       ...
    }
  }
```

### 个人信息相关

#### 获取个人信息
POST /qnm/getprofile

#### 接口描述
获取玩家的个人信息

#### 请求参数
| 参数       | 必选    | 类型     | 说明            |
| :------- | :---- | :----- | ------------- |
| roleid   | true  | string | 玩家角色Id        |
| targetid | false | string | 需要查看其他玩家的角色Id |
| skey     | true  | string | 登陆返回的skey     |

> 注意：之后的api请求参数里不在描述skey

#### 返回示例
```Javascript
  {
     code: 0
     data: {
         "roleid": ********,
         "rolename": "指尖哓哓",
         "sendflowerrenqi": 0,
         "server_id": 2,
         "server_name": "未未知服",
         ....
     }
  }
```

#### 设置头像
POST /qnm/setphoto

#### 请求参数
| 参数     | 必选   | 类型     | 说明      |
| :----- | :--- | :----- | ------- |
| roleid | true | string | 玩家角色Id  |
| photo  | true | string | 玩家头像url |

#### 返回示例
```javascript
  {
     code: 0
  }
```

#### 更新个人签名
POST /qnm/changesign

#### 请求参数
| 参数        | 必选   | 类型     | 说明     |
| :-------- | :--- | :----- | ------ |
| roleid    | true | string | 玩家角色Id |
| signature | true | string | 个性签名   |

#### 更新地理位置
POST /qnm/changeloc

#### 请求参数
| 参数       | 必选   | 类型     | 说明        |
| :------- | :--- | :----- | --------- |
| roleid   | true | string | 玩家角色Id    |
| location | true | string | 地理位置，省市信息 |



#### 更新角色名

POST /qnm/setrolename

#### 请求参数

| 参数       | 必选   | 类型     | 说明     |
| :------- | :--- | :----- | ------ |
| roleid   | true | string | 玩家角色Id |
| rolename | true | string | 角色名    |

#### 返回示例

```javascript
{
  "code": 0,
  "msg": "更新成功"
}
```

### 心情相关

#### 发表心情
POST /qnm/addmoment

#### 接口描述
发表一条梦岛状态

#### 请求参数
| 参数      | 必选    | 类型        | 说明     |
| :------ | :---- | :-------- | ------ |
| roleid  | true  | string    | 玩家角色Id |
| imglist | false | jsonArray | 图片urls |
| text    | true  | string    | 心情的文本  |

> jsonArray 是指 JSON.stringify(["1.png", "2.png"]) 之后的string

#### 返回示例
id是指新的动态的id
```javascript
  {
      "code": 0,
      "data": {
          "id": 1157,
          "imglist": []
      }
  }
```

#### 删除心情
POST /qnm/delmoment

#### 接口描述
删除一条心情

#### 请求参数
| 参数     | 必选   | 类型     | 说明     |
| :----- | :--- | :----- | ------ |
| roleid | true | string | 玩家角色Id |
| id     | true | int    | 心情的id  |

#### 返回示例
```javascript
  {
      "code": 0,
      "data": {
      }
  }
```

#### 添加心情评论
POST /qnm/addcomment

#### 请求参数
| 参数      | 必选    | 类型     | 说明                   |
| :------ | :---- | :----- | -------------------- |
| roleid  | true  | string | 玩家角色Id               |
| id      | true  | int    | 心情的id                |
| text    | true  | string | 评论文本                 |
| replyid | false | int    | 当回复其他人的评论时，评论对象的角色ID |

#### 返回示例
```javascript
  {
      "code": 0,
      "data": {
      }
  }
```

#### 删除心情评论
POST /qnm/delcomment

#### 请求参数
| 参数     | 必选   | 类型     | 说明     |
| :----- | :--- | :----- | ------ |
| roleid | true | string | 玩家角色Id |
| id     | true | int    | 心情的id  |
| text   | true | string | 评论文本   |

#### 获取评论列表
POST {{pyqApi}}/comment/more

#### 请求参数
| 参数     | 必选   | 类型     | 说明     |
| :----- | :--- | :----- | ------ |
| roleid | true | string | 玩家角色Id |
| comment_id | true | number | 最新到旧排列， 取lastId |

#### 返回示例
```javascript
{
  "code": 0,
  "data": [
    {
      "id": 19,
      "targetid": 11,
      "roleid": 106000014,
      "replyid": 111800014,
      "text": "你手机大家",
      "createtime": 1472708538355,
      "replyinfo": {
        "roleid": 111800014,
        "photo": "http://hi-163-qnm.nosdn.127.net/photo/201609/01/58d74540700111e6a36399e1934d111d",
        "showphoto": 1,
        "photoaudit": 1,
        "friendlist": "301900014,100600014,101200014,106000014,107800014",
        "privacy": null,
        "expression_base": "{\"expression\":0,\"expression_txt\":2,\"sticker\":0,\"frame\":0}",
        "splevel": 0,
        "lianghao": {
          "id": 0,
          "expiredtime": 0,
          "hideicon": 0,
          "showdigit": 0
        },
        "rolename": "宝宝不爽了",
        "gender": 0,
        "grade": 69,
        "clazz": 1,
        "server_id": 14,
        "used_name": [
          "看看啊",
          "逍遥星辰"
        ],
        "xianfanstatus": 0,
        "server_name": ""
      },
      "photo": "http://hi-163-qnm.nosdn.127.net/photo/201609/01/b5669b80701a11e6a36399e1934d111d",
      "showphoto": 1,
      "photoaudit": 1,
      "friendlist": "100600014,101200014,107000014,111800014,126600014,148500014",
      "privacy": null,
      "expression_base": "{\"expression\":0,\"expression_txt\":0,\"sticker\":0,\"frame\":0}",
      "splevel": 0,
      "lianghao": {
        "id": 0,
        "expiredtime": 0,
        "hideicon": 0,
        "showdigit": 0
      },
      "rolename": "中屁股露珠",
      "gender": 1,
      "grade": 109,
      "clazz": 1,
      "server_id": 14,
      "used_name": [
        "大屁股露珠",
        "小屁股露珠",
        "大屁股露珠"
      ],
      "xianfanstatus": 0,
      "server_name": ""
    },
  ]
}
```



####  对心情点赞
POST /qnm/likemoment

#### 请求参数
| 参数     | 必选    | 类型     | 说明                      |
| :----- | :---- | :----- | ----------------------- |
| roleid | true  | string | 玩家角色Id                  |
| id     | true  | int    | 心情的id                   |
| action | false | string | 当action为'undo'的时候代表取消点赞 |


#### 获取nos上传凭证
POST /qnm/nos/gettoken

#### 请求参数
| 参数     | 必选   | 类型     | 说明     |
| :----- | :--- | :----- | ------ |
| roleid | true | string | 玩家角色Id |
| type   | true | string | 上传资源类型 |

 > type 当前有三种类型，'Tape'=>上传录音签名， 'Photo'=>上传头像， 'Img' => 上传图片

这个接口只是获取nos上传的凭证， 梦岛自身不存储用户上传的资源，所以对象都存在nos上
关于nos如何使用，请参考 [https://c.163.com/wiki/index.php?title=API_&_SDK
]()，  比如客户端使用上传头像接口，流程如下

调用*/qnm/nos/gettoken*接口 => 得到token直传到nos =>   获取上传的url放到setphoto的photo参数里完成头像的设置


#### 全民争霸分享
POST /qnm/activity/qmpk/share

#### 请求参数
| 参数     | 必选    | 类型     | 说明                      |
| :----- | :---- | :----- | ----------------------- |
| roleid | true  | string | 玩家角色Id                  |
| origin_roleid | true  | string | 原服玩家角色id |
| text | true  | string | 分享文本 |
| url | true  | string | 分享图url |

#### 返回示例
```json
  {
    "code": 0,
    "data": {
      "id": 1 // 心情id
    }
  }
```