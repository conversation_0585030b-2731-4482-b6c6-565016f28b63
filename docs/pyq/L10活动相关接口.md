### L10红人活动梦岛分享

POST {{baseUrl}}/activity/pub_event?name=mingRen

#### Http请求头
Content-Type: application/json

#### HttpBody 示例

```json
{
  "payload": [{
    "workId": 1,
    "workName": "title",
    "roleId": 2,
    "status": 1,
    "postType": 1
  }]
}
```

#### 参数说明
| 参数      | 必选   | 类型     | 说明          |
| :------ | :--- | :----- | ----------- |
| workId | true | int | 作品Id |
| workName | true | string | 作品名字 |
| roleId | true | int | 玩家Id |
| status | true | int | 审核状态 1=>通过  2 => 拒绝 |
| postType | true | int | 投稿区 |

#### 补充说明
``` typescript
enum PostType {
  YanZhi = 1,  // 颜值区
  Game = 2,    // 游戏达人区
  GuangYing = 3, // 光影回声区
  MiaoShou = 4 // 妙手丹青区
}
```

#### 返回示例
{
  "code": 0,
  "data": {
    "msg": "ok"
  }
}
