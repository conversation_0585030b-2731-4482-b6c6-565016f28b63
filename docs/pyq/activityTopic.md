### 列出活动话题
POST {{pyqApi}}/topic/list

### 返回示例

```json
{
  "code": 0,
  "data": [{
    "ID": 4,
    "Name": "时装大赛",
    "Banner": "http://hi-163-qnm.nosdn.127.net/moment/201812/21/d9d20aa0050411e98975077e3b9dbbb2",
    "Desc": "时装大赛， 最美时装",
    "CreateTime": 1545913419061
  }, {
    "ID": 3,
    "Name": "侠侣活动",
    "Banner": "http://hi-163-qnm.nosdn.127.net/upload/2018-09-27/4931e5430985018c58b25e87b2ad884e.jpg",
    "Desc": "侠侣双飞",
    "CreateTime": 1545913419962
  }]
}
```

### 参与话题心情讨论
POST {{pyqApi}}/topic/moment/add

#### 参数列表

| 参数      |  类型     | 说明          |
| :------ | :----- | ----------- |
| topicId | int    | 话题Id |
| imglist | string | json string |
| text | string | 心情文本|

### 返回示例
```json
{
  "code": 0,
  "data": {
    "id": 15938801414,
    "imgList": ["http://hi-163-qnm.nosdn.127.net/upload/201801/02/41f73870ef8111e7bcb3edb348826b69", "http://hi-163-qnm.nosdn.127.net/upload/201801/02/442c19d0ef8111e79da945000ae9704d"]
  }
}
```

### 列出话题心情
#### 参数列表

| 参数      |  类型     | 说明          |
| :------ | :----- | ----------- |
| topicId | int    | 话题Id|
| sort | string | hot => 热度, new => 时间， image=> 带图, follow => 关注|
| page |  int | 页码, 从1计数          |
| page_size | | int | 每页大小          |

### 返回示例
> 同心情列表