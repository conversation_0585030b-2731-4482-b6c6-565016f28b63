# 位置服务API文档

## 概述

本文档描述了pyq-server中位置服务相关的三个核心API接口，这些接口基于Redis Geo功能实现，支持玩家位置更新、查询和附近玩家发现功能。

## 技术架构

### 核心技术栈
- **Redis Geo**: 使用georedis库实现地理位置存储和查询
- **数据库**: 使用PyqGeo表存储玩家位置信息
- **缓存**: Redis缓存地理位置数据，分为在线和离线两个集合
- **日志**: 位置更新时记录地理地址信息

### 关键常量
```typescript
const NEARBY_PLAYERS_MAX_SIZE = 500; // 附近的人返回最大人数限制
const NEARBY_PLAYERS_SAMPLE_SIZE = 20; // 附近的人随机取样的大小
const SAME_SERVER_DISPLACEMENT_THRESHOLD = 80000; // 玩家位移超过8万米时过滤同服玩家
```

## API接口详情

### 1. 更新玩家位置

#### 接口信息
- **路径**: `POST /qnm/location/update`
- **控制器**: `LocationController.updatePlayerLocation`
- **功能**: 更新指定玩家的地理位置信息

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| roleid | number | 是 | 玩家角色ID |
| latitude | number | 是 | 纬度 |
| longitude | number | 是 | 经度 |

#### 响应格式
```json
{
  "code": 0,
  "msg": "Ok"
}
```

#### 实现细节

1. **参数验证**: 使用`req.paramsValidator`验证必需参数
2. **位置保存**: 调用`GeoLocation.savePlayerGeo()`保存位置信息
3. **数据库更新**: 更新PyqGeo表中的位置记录，设置在线状态
4. **Redis更新**: 
   - 将玩家位置添加到在线集合(`pyq:geo:locations:online`)
   - 从离线集合中移除玩家位置
5. **日志记录**: 调用`logLocation()`记录地理位置信息到日志文件

#### 核心代码流程
```typescript
export function updatePlayerLocation(req, res, next) {
  return req.paramsValidator
    .param("roleid")
    .param("latitude") 
    .param("longitude")
    .validate()
    .then(function () {
      return GeoLocation.savePlayerGeo(roleId, longitude, latitude);
    })
    .then(function (data) {
      logLocation(roleId, longitude, latitude);
      res.send({ code: 0, msg: "Ok" });
    })
    .catch(function (err) {
      errorHandler(err, req, res, next);
    });
}
```

---

### 2. 获取附近玩家

#### 接口信息
- **路径**: `POST /qnm/location/nearby/players`
- **控制器**: `LocationController.getNearByPlayers`
- **功能**: 获取指定位置附近的玩家列表

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| roleid | number | 是 | 当前玩家角色ID |
| latitude | number | 是 | 查询中心纬度 |
| longitude | number | 是 | 查询中心经度 |
| distance | number | 是 | 查询半径(米) |

#### 响应格式
```json
{
  "code": 0,
  "data": [
    {
      "RoleId": 12345,
      "Gender": 1,
      "ServerId": 1001,
      "longitude": 120.1614,
      "latitude": 30.2537,
      "clazz": 1,
      "canacess": true
    }
  ]
}
```

#### 响应字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| RoleId | number | 玩家角色ID |
| Gender | number | 性别 |
| ServerId | number | 服务器ID |
| longitude | number | 经度 |
| latitude | number | 纬度 |
| clazz | number | 职业ID |
| canacess | boolean | 是否可访问位置信息 |

#### 实现细节

1. **参数验证**: 验证所有必需参数
2. **隐私检查**: 检查当前玩家是否隐藏了LBS功能
3. **位置查询**: 调用`GeoLocation.getNearByPlayerIds()`获取附近玩家
4. **数据聚合**: 并行查询玩家基本信息和LBS设置
5. **权限过滤**: 根据隐私设置过滤可访问的玩家
6. **服务器过滤**: 过滤测试服务器角色ID
7. **黑名单过滤**: 过滤被屏蔽的玩家ID

#### 核心算法流程
```typescript
export function getNearByPlayers(req, res, next) {
  return req.paramsValidator
    .param("roleid")
    .param("longitude")
    .param("latitude")
    .param("distance")
    .validate()
    .then(function () {
      return PyqProfile.isHideLbsByRoleId(curRoleId);
    })
    .then(function () {
      return GeoLocation.getNearByPlayerIds(curRoleId, longitude, latitude, distance);
    })
    .then(function (locations) {
      // 并行查询玩家信息和LBS设置
      const getRoleInfos = QnmRoleInfos.find({ RoleId: roleIds });
      const getRoleLbsSetting = PyqGeo.find({ RoleId: roleIds });
      return [locations, getRoleInfos, getRoleLbsSetting];
    })
    .spread(function (locations, players, playerLbsSettings) {
      // 处理权限和位置信息
      return players;
    })
    .then(function (players) {
      return filterTestServerRoleIds(players);
    })
    .then(function (players) {
      return filterBlockPlayers(players);
    })
    .then(function (roleInfos) {
      res.send({ code: 0, data: formatResult(roleInfos) });
    });
}
```

#### 地理位置查询算法
1. **位移计算**: 计算玩家当前位置与上次位置的位移距离
2. **同服过滤**: 如果位移超过8万米，只显示同服务器玩家
3. **在线查询**: 优先从在线玩家集合中查询
4. **离线补充**: 如果在线玩家不足，从离线玩家集合补充
5. **随机采样**: 对结果进行随机采样，控制返回数量

---

### 3. 获取玩家位置

#### 接口信息
- **路径**: `POST /qnm/location/get`
- **控制器**: `LocationController.getPlayerLocation`
- **功能**: 获取指定玩家的当前位置信息

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| roleid | number | 是 | 玩家角色ID |

#### 响应格式
```json
{
  "code": 0,
  "data": {
    "longitude": 120.1614,
    "latitude": 30.2537
  }
}
```

#### 响应字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| longitude | number | 经度 |
| latitude | number | 纬度 |

#### 实现细节

1. **参数验证**: 验证roleid参数
2. **位置查询**: 调用`GeoLocation.getByRoleId()`获取玩家位置
3. **优先级查询**: 优先查询在线集合，如果不存在则查询离线集合
4. **返回结果**: 返回位置信息或null

#### 核心代码流程
```typescript
export function getPlayerLocation(req, res, next) {
  return req.paramsValidator
    .param("roleid")
    .validate()
    .then(function () {
      return GeoLocation.getByRoleId(roleId);
    })
    .then(function (data) {
      res.send({ code: 0, data: data });
    })
    .catch(function (err) {
      errorHandler(err, req, res, next);
    });
}
```

## 数据模型

### Redis数据结构
- **在线玩家集合**: `pyq:geo:locations:online`
- **离线玩家集合**: `pyq:geo:locations:offline`
- **临时计算集合**: `pyq:geo:locations:tmp`

### 数据库表结构
- **PyqGeo表**: 存储玩家位置信息和在线状态
- **QnmRoleInfos表**: 存储玩家基本信息
- **PyqProfile表**: 存储玩家隐私设置

## 错误处理

### 常见错误码
- `code: 0`: 成功
- 其他错误码: 根据具体错误类型返回相应错误信息

### 异常处理
- 参数验证失败
- Redis连接异常
- 数据库查询异常
- 地理位置计算异常

## 性能优化

### 缓存策略
- 使用Redis缓存地理位置数据
- 黑名单玩家ID缓存
- 地理位置查询结果缓存

### 查询优化
- 并行查询玩家信息和设置
- 随机采样控制返回数量
- 分页查询避免大量数据返回

### 监控指标
- 位置更新频率
- 附近玩家查询响应时间
- Redis连接状态
- 地理位置计算准确性

## 安全考虑

### 隐私保护
- 支持玩家隐藏LBS功能
- 权限控制访问位置信息
- 黑名单机制屏蔽特定玩家

### 数据验证
- 经纬度范围验证
- 距离参数合理性检查
- 角色ID有效性验证

## 部署说明

### 依赖服务
- Redis服务器(支持Geo功能)
- MySQL数据库
- 日志服务

### 配置要求
- Redis Geo模块启用
- 地理位置服务配置
- 日志路径配置

### 监控告警
- Redis连接状态监控
- 地理位置服务可用性监控
- 错误率监控 