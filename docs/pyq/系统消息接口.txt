1、获取新消息及新留言数
/qnm/getnewsnum
请求参数：
roleid（通用的请求参数，应该不用加）

响应数据：
{
	code:0,
	data:{
		inform: xx,	// 新消息数
		message: xx	// 新留言数
	}
}

2、获取未读消息（上限30条）
/qnm/getinforms
请求参数：
roleid（通用的请求参数，应该不用加）

响应数据：
{
	code:0,
	data:{
		list: [{
			id: xx,		// 消息id
			type: xx,	// 消息类型：0 点赞状态 1 回复状态 2 有人在别人的状态下@了你
			text: xx,	// 消息内容，即回复内容
			roleid: xx,	// 发起消息的人
			rolebane: xx,	// 发起消息人的角色名
			photo: xx,	// 发消息人的头像
			objectinfo: {	// 相关的状态信息
				id: xx, 	// 状态id
				text: xx, 	// 状态内容
				imglist: xx, 	// 状态图片列表
			}
		}, {...}, ...]
	}
}

3、获取历史消息（上限30条）
/qnm/getallinforms
请求参数：
roleid（通用的请求参数，应该不用加）

响应数据：
{
	code:0,
	data:{
		list: [{
			id: xx,		// 消息id
			type: xx,	// 消息类型：0 点赞状态 1 回复状态 2 有人在别人的状态下@了你
			text: xx,	// 消息内容，即回复内容
			roleid: xx,	// 发起消息的人
			photo: xx,	// 发消息人的头像
			objectinfo: {	// 相关的状态信息
				id: xx, 	// 状态id
				text: xx, 	// 状态内容
				imglist: xx, 	// 状态图片列表
			}
		}, {...}, ...]
	}
}

4、留言板接口
/qnm/getmessages 接口增加字段
{
	code:0,
	data:{
        isnew: true/false,	// 新留言
        ownerinfo: {		  // 空间主人信息
           roleid: xxx,
           rolename: xxx,
           gender: xxx,
           grade: xxx,
           clazz: xxx,
        },
		....
	}
}