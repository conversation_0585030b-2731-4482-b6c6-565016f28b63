# 热门动态过滤规则

## 概述

热门动态是朋友圈中展示给用户的高质量内容，为了保证内容的质量和合规性，系统会对动态进行一系列的过滤。本文档描述了热门动态的过滤规则，特别是关于转发抽奖动态的过滤规则。

## 过滤规则

热门动态会根据以下规则进行过滤：

1. **被封禁用户的动态**：如果用户被封禁了热门功能，其动态将不会出现在热门列表中。
2. **未成年保护模式**：开启了未成年保护模式的用户，其动态不会出现在热门列表中。
3. **转发抽奖动态**：如果一个动态是转发了抽奖动态，该动态将不会出现在热门列表中。

## 转发抽奖动态过滤

### **背景**

抽奖动态通常会吸引大量用户转发，这些转发内容往往缺乏原创性和价值，会影响热门内容的质量。为了提高热门内容的质量，我们决定过滤掉转发抽奖动态。

### 实现方式

系统通过以下步骤过滤转发抽奖动态：

1. 获取所有候选热门动态的ID
2. 查询 `PyqMomentForward` 表，找出哪些动态是转发动态以及它们的原始动态ID
3. 查询 `MomentLotteryModel`，找出哪些原始动态是抽奖动态
4. 过滤掉那些转发抽奖动态的动态

### 流程图

### 过滤决策流程

```mermaid
flowchart LR
    A[动态] --> B{用户被封禁?}
    B -->|是| C[不上热门]
    B -->|否| D{开启保护模式?}
    D -->|是| C
    D -->|否| E{是转发动态?}
    E -->|否| F[可上热门]
    E -->|是| G{原动态是抽奖?}
    G -->|是| C
    G -->|否| F
```

## 技术实现细节

在 `filterBannedForMoments` 函数中，我们通过以下步骤实现过滤逻辑：

1. 从候选动态中提取所有动态ID和角色ID
2. 查询角色封禁状态和未成年保护模式状态
3. 查询动态转发关系，建立动态ID到原始动态ID的映射
4. 查询原始动态中哪些是抽奖动态
5. 使用过滤条件筛选出符合要求的动态

```mermaid
sequenceDiagram
    participant HotMomentsCache
    participant ProfileModel
    participant RoleMinorModel
    participant PyqMomentForward
    participant MomentLotteryModel

    HotMomentsCache->>HotMomentsCache: 获取候选动态
    HotMomentsCache->>ProfileModel: 查询被封禁用户
    ProfileModel-->>HotMomentsCache: 返回封禁信息
    HotMomentsCache->>RoleMinorModel: 查询未成年保护模式
    RoleMinorModel-->>HotMomentsCache: 返回保护模式信息
    HotMomentsCache->>PyqMomentForward: 查询转发关系
    PyqMomentForward-->>HotMomentsCache: 返回转发关系
    HotMomentsCache->>MomentLotteryModel: 查询抽奖动态
    MomentLotteryModel-->>HotMomentsCache: 返回抽奖动态
    HotMomentsCache->>HotMomentsCache: 应用过滤规则
    HotMomentsCache->>HotMomentsCache: 返回过滤后的动态
```

## 注意事项

1. 此过滤规则仅适用于热门动态，不影响用户在自己的朋友圈中看到的内容。
2. 抽奖动态本身仍然可以上热门，只是转发抽奖动态不能上热门。
3. 过滤逻辑在每次刷新热门动态时执行，确保热门列表的实时更新。

## 如何测试

### 测试工具

使用 GM 指令接口来测试热门动态过滤规则，特别是转发抽奖动态的过滤功能。

### 测试步骤

1. **准备测试数据**：
   - 创建一个普通动态（非转发动态）
   - 创建一个抽奖动态
   - 创建一个转发抽奖动态

2. **设置热度值**：
   - 使用数据库操作或 GM 工具，为这些动态设置相同的高热度值（例如 10000）

3. **刷新热门缓存**：
   - 调用 GM 指令接口刷新热门动态缓存
   - 接口路径：`POST /gm-cmd/hotmoment/refresh`
   - 请求体示例：
     ```json
     {
       "tagIds": [],
       "serverIds": ["all"]
     }
     ```

4. **验证结果**：
   - 查看热门动态列表
   - 预期结果：
     - 普通动态应该出现在热门列表中
     - 抽奖动态本身应该出现在热门列表中
     - 转发抽奖动态不应该出现在热门列表中