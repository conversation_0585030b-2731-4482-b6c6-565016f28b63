"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.restifyPrintRoute = void 0;
const _ = require("lodash");
function restifyPrintRoute(server) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const myServer = server;
    const router = myServer.router;
    const routeMap = myServer.routes;
    const ops = Object.keys(router.mounts);
    console.log(`[Restify-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.`);
    console.log(`- using env:   export NODE_ENV=production\n`);
    if (ops && ops.length > 0) {
        for (let i = 0; i < ops.length; i++) {
            const r = router.mounts[ops[i]];
            const handles = routeMap[ops[i]];
            const method = _.padEnd(r.spec.method.toUpperCase(), 5);
            const lastHandlerName = handles[handles.length - 1].name || "anonymous_func";
            const handlerCount = handles.length;
            const handleInfo = `${lastHandlerName} (${handlerCount} handlers)`;
            console.log(`[Restify-debug] ${method} ${r.spec.path}  --> ${handleInfo} `);
        }
    }
}
exports.restifyPrintRoute = restifyPrintRoute;
//# sourceMappingURL=printRoute.js.map