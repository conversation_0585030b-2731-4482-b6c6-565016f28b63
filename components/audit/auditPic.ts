import * as _ from "lodash";
import { AuditImageResult, EProduct, IAuditNote, PicInfo, PicMediaType, SendAuditOption } from "../../common/auditType";
import * as config from "../../common/config";
import { imageAuditLogger } from "../../common/logger2";
import request = require("request");
import { OutgoingHttpHeaders } from "http";
import { processAuditDupInfo } from "../../audit-server/services/mediaAudit";
import { sendPicBufferCfg } from "../../pyq-server/common/config";
import { removeQueryString } from "../../common/util2";
const logger = imageAuditLogger.child({ clazz: "imageAudit/sendPic" });

async function requestSendPicApi(product: string, picList: PicItem[]): Promise<AuditImageResult> {
  return new Promise((resolve, reject) => {
    const reqBody = {
      product: product,
      pic_list: picList,
    };

    const url = config.picAuditHost + "/send_pic_v2";
    request(
      {
        method: "POST",
        url,
        body: reqBody,
        json: true,
      },
      function (err, res, body) {
        if (err) {
          reject(err);
        } else {
          if (res.statusCode !== 200) {
            reject(
              new HttpRequestError("RequestSendPicV2Error", url, {
                headers: res.headers,
                body: res.body,
                statusCode: res.statusCode,
                reqBody,
              })
            );
          } else {
            const apiRet = body as unknown as AuditImageResult;
            if (apiRet && apiRet.success) {
              resolve(apiRet);
            } else {
              reject(
                new HttpRequestError("RequestSendPicV2Fail", url, {
                  headers: res.headers,
                  body: res.body,
                  statusCode: res.statusCode,
                  reqBody,
                })
              );
            }
          }
        }
      }
    );
  });
}

class HttpRequestError extends Error {
  constructor(
    name: string,
    private url: string,
    private context: {
      reqBody?: object;
      statusCode: number;
      headers: OutgoingHttpHeaders;
      body?: string;
    }
  ) {
    super();
    this.name = name || "HttpRequestError";
  }
}

interface PicItem {
  // #必须
  url: string; // #公有读
  role_id: string | number; // #最好传用户的唯一ID过来,方便查处用户的上传记录
  sid?: number; // sid: '',// #游戏服务器,非必须
  pic_id: string; // #最好是图片在发送端的唯一ID
  media: string; // 审核添加 media 参数
  note?: string; // 用户上传ip
  /*  有该参数为强制人工审核, 没有的话走自动审核流程 */
  manual: number;
  account_id: string; //
  // #非必须
  role_vip?: "";
  role_level?: "";
  nickname?: ""; // #玩家昵称
}

interface SendPicItem {
  product: string;
  picList: PicItem[];
}

class SendPicApiProxyClass {
  private maxSize: number;
  private buffer: SendPicItem[] = [];
  private timer = null;
  private syncInterval: number;

  constructor(size: number, syncInterval: number) {
    this.maxSize = size;
    this.syncInterval = syncInterval;
    this.buffer = [];
  }

  async processApiCall(writeBuffer: SendPicItem[]) {
    const productPicListMap = new Map<string, PicItem[]>();
    for (const item of writeBuffer) {
      if (productPicListMap.has(item.product)) {
        productPicListMap.get(item.product).push(...item.picList);
      } else {
        productPicListMap.set(item.product, item.picList);
      }
    }
    for (const [product, picList] of productPicListMap) {
      try {
        if (picList && picList.length > 0) {
          const ret = await requestSendPicApi(product, picList);
          const handDupRet = await processAuditDupInfo(ret);
          logger.debug({ ret, handDupRet }, "SendPicApiProxySyncOk");
        }
      } catch (err) {
        logger.error(err, "SendPicApiProxySyncFail");
      }
    }
  }

  async sync() {
    const writeBuffer = _.clone(this.buffer);
    this.buffer = [];
    await this.processApiCall(writeBuffer);
  }

  isBufferFull() {
    return this.buffer.length >= this.maxSize;
  }

  async send(picList: SendPicItem[]) {
    if (!sendPicBufferCfg.enable) {
      await this.processApiCall(picList);
    } else {
      if (this.isBufferFull()) {
        await this.sync();
        return true;
      } else if (!this.timer) {
        this.timer = setInterval(async () => {
          await this.sync();
        }, this.syncInterval);
      }
      this.buffer = [].concat(this.buffer, picList);
    }
  }

  static create(bufferSize: number, syncInterval: number) {
    const instance = new SendPicApiProxyClass(bufferSize, syncInterval);
    return instance;
  }
}

export const SendPicApiProxy = SendPicApiProxyClass.create(sendPicBufferCfg.buffersize, sendPicBufferCfg.syncInterval);

export function syncAllImageAuditRequest() {
  return SendPicApiProxy.sync();
}

export class AuditPicService {
  static genPicIdFromInfo(info: PicInfo): string {
    return info.type + ":" + info.id;
  }

  static async sendPic(product: EProduct, files: string[], args: SendAuditOption) {
    const productName = this.getProductName(product);
    const picList = this.getPicList(files, args);
    if (config.testCfg.skip_audit) {
      // 内网测试直接跳过审核
      logger.warn({ productName, picList }, "SkipAudit");
      return;
    }
    return SendPicApiProxy.send([{ product: productName, picList }]);
  }

  static getProductName(product: string) {
    //兼容原因, qnm和md标签都为L10
    const productHash = {
      qnm: "L10",
      md: "L10",
    };
    return productHash[product] || product;
  }

  static normalizeSendUrl(url: string) {
    try {
      return removeQueryString(url);
    } catch (err) {
      logger.error({ err, url }, "normalizeSendUrl");
      return url;
    }
  }

  static getPicList(files: string[] | string, args: SendAuditOption) {
    const urls = Array.isArray(files) ? files : [files];
    const picList = urls.map((passUrl) => {
      const nodeObj: IAuditNote = { ip: args.ip };
      const url = this.normalizeSendUrl(passUrl);
      const picItem: PicItem = {
        // #必须
        url, // #公有读
        role_id: args.roleId, // #最好传用户的唯一ID过来,方便查处用户的上传记录
        sid: args.serverId, // sid: '',// #游戏服务器,非必须
        pic_id: args.picId, // #最好是图片在发送端的唯一ID
        media: args.media || PicMediaType.Image, // 审核添加 media 参数
        note: JSON.stringify(nodeObj), // 用户上传ip
        manual: args.manual || 0,
        account_id: args.accountId || "", //
      };
      return picItem;
    });
    return picList;
  }
}
