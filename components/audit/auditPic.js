"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditPicService = exports.syncAllImageAuditRequest = exports.SendPicApiProxy = void 0;
const _ = require("lodash");
const auditType_1 = require("../../common/auditType");
const config = require("../../common/config");
const logger2_1 = require("../../common/logger2");
const request = require("request");
const mediaAudit_1 = require("../../audit-server/services/mediaAudit");
const config_1 = require("../../pyq-server/common/config");
const util2_1 = require("../../common/util2");
const logger = logger2_1.imageAuditLogger.child({ clazz: "imageAudit/sendPic" });
function requestSendPicApi(product, picList) {
    return __awaiter(this, void 0, void 0, function* () {
        return new Promise((resolve, reject) => {
            const reqBody = {
                product: product,
                pic_list: picList,
            };
            const url = config.picAuditHost + "/send_pic_v2";
            request({
                method: "POST",
                url,
                body: reqBody,
                json: true,
            }, function (err, res, body) {
                if (err) {
                    reject(err);
                }
                else {
                    if (res.statusCode !== 200) {
                        reject(new HttpRequestError("RequestSendPicV2Error", url, {
                            headers: res.headers,
                            body: res.body,
                            statusCode: res.statusCode,
                            reqBody,
                        }));
                    }
                    else {
                        const apiRet = body;
                        if (apiRet && apiRet.success) {
                            resolve(apiRet);
                        }
                        else {
                            reject(new HttpRequestError("RequestSendPicV2Fail", url, {
                                headers: res.headers,
                                body: res.body,
                                statusCode: res.statusCode,
                                reqBody,
                            }));
                        }
                    }
                }
            });
        });
    });
}
class HttpRequestError extends Error {
    constructor(name, url, context) {
        super();
        this.url = url;
        this.context = context;
        this.name = name || "HttpRequestError";
    }
}
class SendPicApiProxyClass {
    constructor(size, syncInterval) {
        this.buffer = [];
        this.timer = null;
        this.maxSize = size;
        this.syncInterval = syncInterval;
        this.buffer = [];
    }
    processApiCall(writeBuffer) {
        return __awaiter(this, void 0, void 0, function* () {
            const productPicListMap = new Map();
            for (const item of writeBuffer) {
                if (productPicListMap.has(item.product)) {
                    productPicListMap.get(item.product).push(...item.picList);
                }
                else {
                    productPicListMap.set(item.product, item.picList);
                }
            }
            for (const [product, picList] of productPicListMap) {
                try {
                    if (picList && picList.length > 0) {
                        const ret = yield requestSendPicApi(product, picList);
                        const handDupRet = yield (0, mediaAudit_1.processAuditDupInfo)(ret);
                        logger.debug({ ret, handDupRet }, "SendPicApiProxySyncOk");
                    }
                }
                catch (err) {
                    logger.error(err, "SendPicApiProxySyncFail");
                }
            }
        });
    }
    sync() {
        return __awaiter(this, void 0, void 0, function* () {
            const writeBuffer = _.clone(this.buffer);
            this.buffer = [];
            yield this.processApiCall(writeBuffer);
        });
    }
    isBufferFull() {
        return this.buffer.length >= this.maxSize;
    }
    send(picList) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!config_1.sendPicBufferCfg.enable) {
                yield this.processApiCall(picList);
            }
            else {
                if (this.isBufferFull()) {
                    yield this.sync();
                    return true;
                }
                else if (!this.timer) {
                    this.timer = setInterval(() => __awaiter(this, void 0, void 0, function* () {
                        yield this.sync();
                    }), this.syncInterval);
                }
                this.buffer = [].concat(this.buffer, picList);
            }
        });
    }
    static create(bufferSize, syncInterval) {
        const instance = new SendPicApiProxyClass(bufferSize, syncInterval);
        return instance;
    }
}
exports.SendPicApiProxy = SendPicApiProxyClass.create(config_1.sendPicBufferCfg.buffersize, config_1.sendPicBufferCfg.syncInterval);
function syncAllImageAuditRequest() {
    return exports.SendPicApiProxy.sync();
}
exports.syncAllImageAuditRequest = syncAllImageAuditRequest;
class AuditPicService {
    static genPicIdFromInfo(info) {
        return info.type + ":" + info.id;
    }
    static sendPic(product, files, args) {
        return __awaiter(this, void 0, void 0, function* () {
            const productName = this.getProductName(product);
            const picList = this.getPicList(files, args);
            if (config.testCfg.skip_audit) {
                // 内网测试直接跳过审核
                logger.warn({ productName, picList }, "SkipAudit");
                return;
            }
            return exports.SendPicApiProxy.send([{ product: productName, picList }]);
        });
    }
    static getProductName(product) {
        //兼容原因, qnm和md标签都为L10
        const productHash = {
            qnm: "L10",
            md: "L10",
        };
        return productHash[product] || product;
    }
    static normalizeSendUrl(url) {
        try {
            return (0, util2_1.removeQueryString)(url);
        }
        catch (err) {
            logger.error({ err, url }, "normalizeSendUrl");
            return url;
        }
    }
    static getPicList(files, args) {
        const urls = Array.isArray(files) ? files : [files];
        const picList = urls.map((passUrl) => {
            const nodeObj = { ip: args.ip };
            const url = this.normalizeSendUrl(passUrl);
            const picItem = {
                // #必须
                url,
                role_id: args.roleId,
                sid: args.serverId,
                pic_id: args.picId,
                media: args.media || auditType_1.PicMediaType.Image,
                note: JSON.stringify(nodeObj),
                manual: args.manual || 0,
                account_id: args.accountId || "", //
            };
            return picItem;
        });
        return picList;
    }
}
exports.AuditPicService = AuditPicService;
//# sourceMappingURL=auditPic.js.map