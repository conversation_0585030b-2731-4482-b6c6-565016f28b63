import { Router } from "./index";
import { MusicClubAuditAdminController } from "../controllers/musicClubAuditAdmin";
import { musicClubAuditAdminCfg } from "../common/config.all";
import { preUseWhenUrlPrefix, skipSkeyAuthMiddleware } from "../helper";
import * as restify from "restify";
import * as path from "path";

const router = new Router({
  path: "/admin/api/music_club/audit",
  skipAuth: true
});

// API路由
router.get("/list", MusicClubAuditAdminController.getAuditList);
router.get("/detail/:id", MusicClubAuditAdminController.getAuditDetail);
router.get("/statistics", MusicClubAuditAdminController.getStatistics);
router.post("/retry/:id", MusicClubAuditAdminController.retryAuditTask);

export function mount(server: restify.Server) {
  // 只有在启用管理后台时才注册路由
  if (musicClubAuditAdminCfg.enabled) {
    // 注册API路由
    router.mount(server);

    // 为静态文件服务跳过认证
    preUseWhenUrlPrefix(server, "/qnm/admin/music_club/audit/web", skipSkeyAuthMiddleware);

    // 注册静态文件服务 - 将 /qnm/admin/music_club/audit/web/* 映射到 public/admin/music_club_audit/*
    server.get(
      /^\/qnm\/admin\/music_club\/audit\/web\/?(.*)$/,
      (req, res, next) => {
        // 重写请求路径，将 /qnm/admin/music_club/audit/web/xxx 映射到 /admin/music_club_audit/xxx
        req.url = req.url.replace(/^\/qnm\/admin\/music_club\/audit\/web\/?/, '/admin/music_club_audit/');
        if (req.url === '/admin/music_club_audit/' || req.url === '/admin/music_club_audit') {
          req.url = '/admin/music_club_audit/index.html';
        }

        const staticHandler = restify.serveStatic({
          directory: path.join(process.cwd(), "public"),
          default: "index.html",
          maxAge: 0 // 开发环境不缓存
        });

        staticHandler(req, res, next);
      }
    );

    console.log("🎵 音乐社团审核管理后台已启用");
    console.log("📱 访问地址: http://localhost:9992/qnm/admin/music_club/audit/web/");
  }
}
