"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MusicClubRecordingAuditTaskModel = exports.AuditTaskStatus = void 0;
const baseModel_1 = require("../baseModel");
const logger_1 = require("../../logger");
const logger = (0, logger_1.clazzLogger)("models.musicClubRecordingAuditTaskModel");
/**
 * 审核任务状态枚举
 */
var AuditTaskStatus;
(function (AuditTaskStatus) {
    /** 待审核 */
    AuditTaskStatus[AuditTaskStatus["PENDING"] = 0] = "PENDING";
    /** 已送审 */
    AuditTaskStatus[AuditTaskStatus["SUBMITTED"] = 1] = "SUBMITTED";
    /** 审核通过 */
    AuditTaskStatus[AuditTaskStatus["PASSED"] = 2] = "PASSED";
    /** 审核拒绝 */
    AuditTaskStatus[AuditTaskStatus["REJECTED"] = 3] = "REJECTED";
    /** 审核超时 */
    AuditTaskStatus[AuditTaskStatus["TIMEOUT"] = 4] = "TIMEOUT";
})(AuditTaskStatus = exports.AuditTaskStatus || (exports.AuditTaskStatus = {}));
class MusicClubRecordingAuditTaskModel extends baseModel_1.BaseModelClass {
    constructor() {
        super('qnm_music_club_recording_audit_task');
    }
    static getInstance() {
        if (!MusicClubRecordingAuditTaskModel.instance) {
            MusicClubRecordingAuditTaskModel.instance = new MusicClubRecordingAuditTaskModel();
        }
        return MusicClubRecordingAuditTaskModel.instance;
    }
    /**
     * 创建审核任务
     */
    createAuditTask(ctx, recordingId, vocalUrl, roleId, serverId) {
        return __awaiter(this, void 0, void 0, function* () {
            const now = Date.now();
            const task = {
                RecordingId: recordingId,
                VocalUrl: vocalUrl,
                RoleId: roleId,
                ServerId: serverId,
                Status: AuditTaskStatus.PENDING,
                RetryCount: 0,
                CreateTime: now,
                UpdateTime: now
            };
            const insertId = yield this.insert(task);
            logger.info({ ctx, recordingId, vocalUrl, taskId: insertId }, "CreateAuditTaskSuccess");
            return insertId;
        });
    }
    /**
     * 根据人声URL查找审核任务
     */
    findByVocalUrl(vocalUrl) {
        return __awaiter(this, void 0, void 0, function* () {
            const tasks = yield this.find({ VocalUrl: vocalUrl });
            return tasks.length > 0 ? tasks[0] : null;
        });
    }
    /**
     * 根据录音ID查找审核任务
     */
    findByRecordingId(recordingId) {
        return __awaiter(this, void 0, void 0, function* () {
            const tasks = yield this.find({ RecordingId: recordingId });
            return tasks.length > 0 ? tasks[0] : null;
        });
    }
    /**
     * 查找待处理的任务（状态为PENDING且重试次数未超限）
     */
    findPendingTasks(maxRetries) {
        return __awaiter(this, void 0, void 0, function* () {
            const query = this.scope()
                .where('Status', AuditTaskStatus.PENDING)
                .andWhere('RetryCount', '<', maxRetries)
                .orderBy('CreateTime', 'ASC')
                .limit(100);
            return yield this.executeByQuery(query);
        });
    }
    /**
     * 查找超时的任务（状态为SUBMITTED且创建时间超过阈值）
     */
    findTimeoutTasks(timeoutThreshold) {
        return __awaiter(this, void 0, void 0, function* () {
            const query = this.scope()
                .where('Status', AuditTaskStatus.SUBMITTED)
                .andWhere('CreateTime', '<', timeoutThreshold)
                .orderBy('CreateTime', 'ASC')
                .limit(100);
            return yield this.executeByQuery(query);
        });
    }
    /**
     * 更新任务状态
     */
    updateTaskStatus(taskId, status, options) {
        return __awaiter(this, void 0, void 0, function* () {
            const updateData = {
                Status: status,
                UpdateTime: Date.now()
            };
            if ((options === null || options === void 0 ? void 0 : options.taskIdStr) !== undefined) {
                updateData.TaskId = options.taskIdStr;
            }
            if ((options === null || options === void 0 ? void 0 : options.rejectReason) !== undefined) {
                updateData.RejectReason = options.rejectReason;
            }
            if ((options === null || options === void 0 ? void 0 : options.retryCount) !== undefined) {
                updateData.RetryCount = options.retryCount;
            }
            yield this.updateByCondition({ ID: taskId }, updateData);
        });
    }
    /**
     * 增加重试次数
     */
    incrementRetryCount(taskId) {
        return __awaiter(this, void 0, void 0, function* () {
            // 先获取当前任务
            const task = yield this.findById(taskId);
            if (task) {
                yield this.updateByCondition({ ID: taskId }, {
                    RetryCount: task.RetryCount + 1,
                    UpdateTime: Date.now()
                });
            }
        });
    }
    /**
     * 获取任务统计信息
     */
    getTaskStats() {
        return __awaiter(this, void 0, void 0, function* () {
            const stats = {
                pending: 0,
                submitted: 0,
                passed: 0,
                rejected: 0,
                timeout: 0
            };
            // 分别查询每种状态的数量
            stats.pending = yield this.count({ Status: AuditTaskStatus.PENDING });
            stats.submitted = yield this.count({ Status: AuditTaskStatus.SUBMITTED });
            stats.passed = yield this.count({ Status: AuditTaskStatus.PASSED });
            stats.rejected = yield this.count({ Status: AuditTaskStatus.REJECTED });
            stats.timeout = yield this.count({ Status: AuditTaskStatus.TIMEOUT });
            return stats;
        });
    }
}
exports.MusicClubRecordingAuditTaskModel = MusicClubRecordingAuditTaskModel;
//# sourceMappingURL=musicClubRecordingAuditTaskModel.js.map