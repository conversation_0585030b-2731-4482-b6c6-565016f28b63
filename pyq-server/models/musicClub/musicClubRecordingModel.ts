import { Statues, AuditStatues } from "../../../common/constants";
import { UpdateResult } from "../../../common/type";
import { MusicClubReq } from "../../components/musicClub/type";
import { Context } from "../../context";
import { errorCodes } from "../../errorCodes";
import { clazzLogger } from "../../logger";
import { schemas } from "../../types/type";
import { BaseModelClass } from "../baseModel";

export interface MusicClubRecordingRecord {
    /** 唱片ID */
    ID: number;
    /** 唱片名称 */
    Name: string;
    /** 唱片录制游戏唯一ID */
    TrackId: string;
    /** 唱片数据URL */
    DataUrl: string;
    /** 人声音频文件URL */
    VocalUrl: string;
    /** 副歌开始时间戳 */
    ChorusStart: number;
    /** 人声偏移时间戳, 正数表示人声提前，负数表示人声延后 */
    VocalOffset: number;
    /** 人声音量, 0-100 */
    VocalVolume: number;
    /** 乐器声音量, 0-100 */
    InstrumentVolume: number;
    /** 服务器ID */
    ServerId: number;
    /** 乐团ID */
    MusicClubId: number;
    /** 点播次数 */
    RequestPlayCount: number;
    /** 唱片热度 */
    Hot: number;
    /** 上次点播时间戳 */
    LastRequestPlayTime: number;
    /** 评分次数 */
    RatingCount: number;
    /** 评分总和 */
    RatingSum: number;
    /** 平均评分 */
    AvgRating: number;
    /** 上架时间戳 */
    ReleaseTime: number;
    /** 更新时间戳 */
    UpdateTime: number;
    /** 状态, 0-正常, -1-已删除 */
    Status: number;
    /** 唱片时长，单位毫秒 */
    Duration: number;
    /** 审核状态: 0-审核中, 1-通过, -1-拒绝 */
    AuditStatus: number;
    /** 审核完成时间 */
    AuditTime: number;
    /** 审核拒绝原因 */
    AuditRejectReason?: string;
}

const logger = clazzLogger("models.musicClubRecordingModel");

export class MusicClubRecordingModel extends BaseModelClass<MusicClubRecordingRecord> {
    private static instance: MusicClubRecordingModel;
    constructor() {
        super('qnm_music_club_recording');
    }

    static getInstance() {
        if (!MusicClubRecordingModel.instance) {
            MusicClubRecordingModel.instance = new MusicClubRecordingModel();
        }
        return MusicClubRecordingModel.instance;
    }

    async getRankRecordingInfoMap(ctx: Context, recordingIds: number[]): Promise<Record<number, schemas['music-club-recording-lead-single']>> {
        const resp: Record<number, schemas['music-club-recording-lead-single']> = {}
        if (recordingIds.length === 0) return resp
        try {
            const query = this.normalScope().whereIn('ID', recordingIds)
            const rows = await this.smartQuery(query, ['ID', 'Name', 'Hot'])
            for (const row of rows) {
                resp[row.ID] = {
                    id: row.ID,
                    name: row.Name,
                    hot: row.Hot,
                }
            }
            return resp
        } catch (error) {
            logger.error({ ctx, error, recordingIds }, "GetRankRecordingInfoMapError")
            return resp
        }
    }

    async incrRequestPlayCountAndUpdateLastPlayTime(ctx: Context, log: schemas['music-club-radio-request-play-req']) {
        try {
            const query = this.scope().where('ID', log.recordingId).update({
                RequestPlayCount: this.raw('RequestPlayCount + 1'),
                LastRequestPlayTime: log.eventTime,
            })
            const ret = await this.executeByQuery<UpdateResult>(query)
            if (ret.affectedRows === 0) {
                logger.warn({ ctx, log }, "OnRequestPlayLogEventRecordingNotFound")
            } else {
                logger.info({ ctx, log }, "OnRequestPlayLogEventSuccess")
            }
        } catch (err) {
            logger.error({ ctx, err, log }, "IncrRequestPlayCountAndUpdateLastPlayTimeError")
            throw err
        }
    }

    async getMusicClubIdByRecordingId(ctx: Context, recordingId: number): Promise<number> {
        try {
            const query = this.scope().where('ID', recordingId).select('MusicClubId')
            const rows = await this.executeByQuery<{ MusicClubId: number }[]>(query)
            if (rows.length === 0) {
                return 0
            }
            return rows[0].MusicClubId
        } catch (error) {
            logger.error({ ctx, error, recordingId }, "GetMusicClubIdByRecordingIdError")
            return 0
        }
    }

    async verifyRecordingExist(ctx: Context, recordingId: number): Promise<Pick<MusicClubRecordingRecord, 'ID' | 'MusicClubId'>> {
        try {
            const r = await this.findOne({ ID: recordingId, Status: Statues.Normal }, ['ID', 'MusicClubId'])
            if (!r) {
                return Promise.reject(errorCodes.RecordingNotFound)
            }
            return r
        } catch (error) {
            logger.error({ ctx, error, recordingId }, "VerifyRecordingExistError")
            throw errorCodes.DatabaseError
        }
    }


    async chooseMaxRequestPlayCountRecordingId(ctx: Context, musicClubId: number, recordingIds: number[]): Promise<number> {
        try {
            const query = this.scope().whereIn('ID', recordingIds).where('MusicClubId', musicClubId)
            const rows = await this.findMany({
                initQuery: query,
                orderBy: [['RequestPlayCount', 'desc'], ['UpdateTime', 'desc']],
                pagination: {
                    page: 1,
                    pageSize: 1,
                },
            })
            if (rows.length === 0) {
                return 0
            }
            return rows[0].ID
        } catch (error) {
            logger.error({ ctx, error, musicClubId, recordingIds }, "ChooseMaxRequestPlayCountRecordingIdError")
            return 0
        }
    }

    async addRecording(ctx: Context, params: MusicClubReq.RecordingRelease): Promise<MusicClubRecordingRecord> {
        try {
            const curTimestamp = Date.now()
            const record: Omit<MusicClubRecordingRecord, 'ID'> = {
                MusicClubId: params.musicClubId,
                RequestPlayCount: 0,
                Hot: 0,
                Name: params.name,
                TrackId: params.trackId,
                DataUrl: params.dataUrl,
                VocalUrl: params.vocalUrl,
                ChorusStart: params.chorusStart,
                VocalOffset: params.vocalOffset,
                VocalVolume: params.vocalVolume,
                InstrumentVolume: params.instrumentVolume,
                ServerId: params.serverId,
                LastRequestPlayTime: 0,
                RatingCount: 0,
                RatingSum: 0,
                AvgRating: 0,
                ReleaseTime: 0,
                UpdateTime: curTimestamp,
                Status: Statues.Normal,
                Duration: params.duration,
                AuditStatus: AuditStatues.Auditing,
                AuditTime: 0,
                AuditRejectReason: undefined
            }
            const upRet = await this.createOrUpdate(record, {
                ReleaseTime: 0,
                UpdateTime: curTimestamp,
                Status: Statues.Normal,
                Duration: params.duration,
                AuditStatus: AuditStatues.Auditing,
                AuditTime: 0,
                AuditRejectReason: undefined
            })
            return { ID: upRet.insertId, ...record }
        } catch (error) {
            logger.error({ ctx, error, params }, "AddRecordingError")
            throw error
        }
    }

    async updateRatingStat(ctx: Context, recordingId: number, rating: number) {
        try {
            const query = this.scope().where('ID', recordingId).update({
                RatingCount: this.raw('RatingCount + 1'),
                RatingSum: this.raw('RatingSum + ?', [rating]),
                AvgRating: this.raw('(RatingSum + ?) / (RatingCount + 1)', [rating]),
            })
            const ret = await this.executeByQuery<UpdateResult>(query)
            if (ret.affectedRows === 0) {
                logger.warn({ ctx, recordingId, rating }, "UpdateRatingStatRecordingNotFound")
            } else {
                logger.info({ ctx, recordingId, rating }, "UpdateRatingStatSuccess")
            }
        } catch (error) {
            logger.error({ ctx, error, recordingId, rating }, "UpdateRatingStatError")
            throw error
        }
    }

    async getMaxId(ctx: Context): Promise<number> {
        try {
            const query = this.scope().max('id as maxId')
            const rows = await this.executeByQuery<{ maxId: number }[]>(query)
            if (rows.length === 0) {
                return 0
            }
            return rows[0].maxId
        } catch (error) {
            logger.error({ ctx, error }, "GetMaxIdError")
            return 0
        }
    }

    async countByMusicClubId(ctx: Context, musicClubId: number): Promise<number> {
        try {
            const query = this.normalScope().where('MusicClubId', musicClubId).count('ID as count')
            const rows = await this.executeByQuery<{ count: number }[]>(query)
            return rows[0].count
        } catch (error) {
            logger.error({ ctx, error, musicClubId }, "CountByMusicClubIdError")
            return 0
        }
    }

    async getLeadingRecordingInfo(ctx: Context, id: number): Promise<schemas['music-club-recording-lead-single']> {
        try {
            let resp: schemas['music-club-recording-lead-single'] = {
                id: 0,
                name: "",
                hot: 0,
            }
            const record = await this.findOne({ ID: id, Status: Statues.Normal }, ['ID', 'Name', 'Hot'])
            if (!record) {
                return resp
            }
            resp.id = record.ID
            resp.name = record.Name
            resp.hot = record.Hot
            return resp
        } catch (error) {
            logger.error({ ctx, error, id }, "GetLeadingRecordingInfoError")
            throw errorCodes.DatabaseError
        }
    }

    async removeAllRecordingsByMusicClubId(ctx: Context, musicClubId: number): Promise<number> {
        try {
            const query = this.scope().where('MusicClubId', musicClubId).where('Status', Statues.Normal).update({
                Status: Statues.Deleted,
            })
            const ret = await this.executeByQuery<{ affectedRows: number }>(query)
            logger.info({ ctx, musicClubId, affectedRows: ret.affectedRows }, "RemoveAllRecordingsByMusicClubIdSuccess")
            return ret.affectedRows
        } catch (error) {
            logger.error({ ctx, error, musicClubId }, "RemoveAllRecordingsByMusicClubIdError")
            throw errorCodes.DatabaseError
        }
    }

    /** 只查询审核通过的唱片 */
    auditPassScope() {
        return this.normalScope().where('AuditStatus', AuditStatues.PASS);
    }

    /** 查询待审核的唱片 */
    auditPendingScope() {
        return this.normalScope().where('AuditStatus', AuditStatues.Auditing);
    }

    /** 查询审核拒绝的唱片 */
    auditRejectScope() {
        return this.normalScope().where('AuditStatus', AuditStatues.Reject);
    }

    /** 处理审核回调 */
    async handleAuditCallback(ctx: Context, vocalUrl: string, auditStatus: number, rejectReason?: string): Promise<void> {
        try {
            const recording = await this.findOne({
                VocalUrl: vocalUrl,
                Status: Statues.Normal
            }, ['ID', 'VocalUrl', 'AuditStatus']);

            if (!recording) {
                logger.warn({ ctx, vocalUrl, auditStatus, rejectReason }, "HandleAuditCallbackRecordingNotFound");
                throw errorCodes.RecordingNotFound;
            }

            const updateData: Partial<MusicClubRecordingRecord> = {
                AuditStatus: auditStatus,
                AuditTime: Date.now(),
                AuditRejectReason: rejectReason || undefined
            };

            const query = this.scope().where('ID', recording.ID).update(updateData);
            const ret = await this.executeByQuery<UpdateResult>(query);

            if (ret.affectedRows === 0) {
                logger.warn({ ctx, vocalUrl, auditStatus, rejectReason }, "HandleAuditCallbackUpdateFailed");
            } else {
                logger.info({ ctx, vocalUrl, auditStatus, rejectReason, recordingId: recording.ID }, "HandleAuditCallbackSuccess");
            }
        } catch (error) {
            logger.error({ ctx, error, vocalUrl, auditStatus, rejectReason }, "HandleAuditCallbackError");
            throw error;
        }
    }

}