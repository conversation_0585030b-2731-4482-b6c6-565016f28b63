"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MusicClubRecordingModel = void 0;
const constants_1 = require("../../../common/constants");
const errorCodes_1 = require("../../errorCodes");
const logger_1 = require("../../logger");
const baseModel_1 = require("../baseModel");
const logger = (0, logger_1.clazzLogger)("models.musicClubRecordingModel");
class MusicClubRecordingModel extends baseModel_1.BaseModelClass {
    constructor() {
        super('qnm_music_club_recording');
    }
    static getInstance() {
        if (!MusicClubRecordingModel.instance) {
            MusicClubRecordingModel.instance = new MusicClubRecordingModel();
        }
        return MusicClubRecordingModel.instance;
    }
    getRankRecordingInfoMap(ctx, recordingIds) {
        return __awaiter(this, void 0, void 0, function* () {
            const resp = {};
            if (recordingIds.length === 0)
                return resp;
            try {
                const query = this.normalScope().whereIn('ID', recordingIds);
                const rows = yield this.smartQuery(query, ['ID', 'Name', 'Hot']);
                for (const row of rows) {
                    resp[row.ID] = {
                        id: row.ID,
                        name: row.Name,
                        hot: row.Hot,
                    };
                }
                return resp;
            }
            catch (error) {
                logger.error({ ctx, error, recordingIds }, "GetRankRecordingInfoMapError");
                return resp;
            }
        });
    }
    incrRequestPlayCountAndUpdateLastPlayTime(ctx, log) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const query = this.scope().where('ID', log.recordingId).update({
                    RequestPlayCount: this.raw('RequestPlayCount + 1'),
                    LastRequestPlayTime: log.eventTime,
                });
                const ret = yield this.executeByQuery(query);
                if (ret.affectedRows === 0) {
                    logger.warn({ ctx, log }, "OnRequestPlayLogEventRecordingNotFound");
                }
                else {
                    logger.info({ ctx, log }, "OnRequestPlayLogEventSuccess");
                }
            }
            catch (err) {
                logger.error({ ctx, err, log }, "IncrRequestPlayCountAndUpdateLastPlayTimeError");
                throw err;
            }
        });
    }
    getMusicClubIdByRecordingId(ctx, recordingId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const query = this.scope().where('ID', recordingId).select('MusicClubId');
                const rows = yield this.executeByQuery(query);
                if (rows.length === 0) {
                    return 0;
                }
                return rows[0].MusicClubId;
            }
            catch (error) {
                logger.error({ ctx, error, recordingId }, "GetMusicClubIdByRecordingIdError");
                return 0;
            }
        });
    }
    verifyRecordingExist(ctx, recordingId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const r = yield this.findOne({ ID: recordingId, Status: constants_1.Statues.Normal }, ['ID', 'MusicClubId']);
                if (!r) {
                    return Promise.reject(errorCodes_1.errorCodes.RecordingNotFound);
                }
                return r;
            }
            catch (error) {
                logger.error({ ctx, error, recordingId }, "VerifyRecordingExistError");
                throw errorCodes_1.errorCodes.DatabaseError;
            }
        });
    }
    chooseMaxRequestPlayCountRecordingId(ctx, musicClubId, recordingIds) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const query = this.scope().whereIn('ID', recordingIds).where('MusicClubId', musicClubId);
                const rows = yield this.findMany({
                    initQuery: query,
                    orderBy: [['RequestPlayCount', 'desc'], ['UpdateTime', 'desc']],
                    pagination: {
                        page: 1,
                        pageSize: 1,
                    },
                });
                if (rows.length === 0) {
                    return 0;
                }
                return rows[0].ID;
            }
            catch (error) {
                logger.error({ ctx, error, musicClubId, recordingIds }, "ChooseMaxRequestPlayCountRecordingIdError");
                return 0;
            }
        });
    }
    addRecording(ctx, params) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const curTimestamp = Date.now();
                const record = {
                    MusicClubId: params.musicClubId,
                    RequestPlayCount: 0,
                    Hot: 0,
                    Name: params.name,
                    TrackId: params.trackId,
                    DataUrl: params.dataUrl,
                    VocalUrl: params.vocalUrl,
                    ChorusStart: params.chorusStart,
                    VocalOffset: params.vocalOffset,
                    VocalVolume: params.vocalVolume,
                    InstrumentVolume: params.instrumentVolume,
                    ServerId: params.serverId,
                    LastRequestPlayTime: 0,
                    RatingCount: 0,
                    RatingSum: 0,
                    AvgRating: 0,
                    ReleaseTime: 0,
                    UpdateTime: curTimestamp,
                    Status: constants_1.Statues.Normal,
                    Duration: params.duration,
                    AuditStatus: constants_1.AuditStatues.Auditing,
                    AuditTime: 0,
                    AuditRejectReason: undefined
                };
                const upRet = yield this.createOrUpdate(record, {
                    ReleaseTime: 0,
                    UpdateTime: curTimestamp,
                    Status: constants_1.Statues.Normal,
                    Duration: params.duration,
                    AuditStatus: constants_1.AuditStatues.Auditing,
                    AuditTime: 0,
                    AuditRejectReason: undefined
                });
                return Object.assign({ ID: upRet.insertId }, record);
            }
            catch (error) {
                logger.error({ ctx, error, params }, "AddRecordingError");
                throw error;
            }
        });
    }
    updateRatingStat(ctx, recordingId, rating) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const query = this.scope().where('ID', recordingId).update({
                    RatingCount: this.raw('RatingCount + 1'),
                    RatingSum: this.raw('RatingSum + ?', [rating]),
                    AvgRating: this.raw('(RatingSum + ?) / (RatingCount + 1)', [rating]),
                });
                const ret = yield this.executeByQuery(query);
                if (ret.affectedRows === 0) {
                    logger.warn({ ctx, recordingId, rating }, "UpdateRatingStatRecordingNotFound");
                }
                else {
                    logger.info({ ctx, recordingId, rating }, "UpdateRatingStatSuccess");
                }
            }
            catch (error) {
                logger.error({ ctx, error, recordingId, rating }, "UpdateRatingStatError");
                throw error;
            }
        });
    }
    getMaxId(ctx) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const query = this.scope().max('id as maxId');
                const rows = yield this.executeByQuery(query);
                if (rows.length === 0) {
                    return 0;
                }
                return rows[0].maxId;
            }
            catch (error) {
                logger.error({ ctx, error }, "GetMaxIdError");
                return 0;
            }
        });
    }
    countByMusicClubId(ctx, musicClubId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const query = this.normalScope().where('MusicClubId', musicClubId).count('ID as count');
                const rows = yield this.executeByQuery(query);
                return rows[0].count;
            }
            catch (error) {
                logger.error({ ctx, error, musicClubId }, "CountByMusicClubIdError");
                return 0;
            }
        });
    }
    getLeadingRecordingInfo(ctx, id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                let resp = {
                    id: 0,
                    name: "",
                    hot: 0,
                };
                const record = yield this.findOne({ ID: id, Status: constants_1.Statues.Normal }, ['ID', 'Name', 'Hot']);
                if (!record) {
                    return resp;
                }
                resp.id = record.ID;
                resp.name = record.Name;
                resp.hot = record.Hot;
                return resp;
            }
            catch (error) {
                logger.error({ ctx, error, id }, "GetLeadingRecordingInfoError");
                throw errorCodes_1.errorCodes.DatabaseError;
            }
        });
    }
    removeAllRecordingsByMusicClubId(ctx, musicClubId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const query = this.scope().where('MusicClubId', musicClubId).where('Status', constants_1.Statues.Normal).update({
                    Status: constants_1.Statues.Deleted,
                });
                const ret = yield this.executeByQuery(query);
                logger.info({ ctx, musicClubId, affectedRows: ret.affectedRows }, "RemoveAllRecordingsByMusicClubIdSuccess");
                return ret.affectedRows;
            }
            catch (error) {
                logger.error({ ctx, error, musicClubId }, "RemoveAllRecordingsByMusicClubIdError");
                throw errorCodes_1.errorCodes.DatabaseError;
            }
        });
    }
    /** 只查询审核通过的唱片 */
    auditPassScope() {
        return this.normalScope().where('AuditStatus', constants_1.AuditStatues.PASS);
    }
    /** 查询待审核的唱片 */
    auditPendingScope() {
        return this.normalScope().where('AuditStatus', constants_1.AuditStatues.Auditing);
    }
    /** 查询审核拒绝的唱片 */
    auditRejectScope() {
        return this.normalScope().where('AuditStatus', constants_1.AuditStatues.Reject);
    }
    /** 处理审核回调 */
    handleAuditCallback(ctx, vocalUrl, auditStatus, rejectReason) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const recording = yield this.findOne({
                    VocalUrl: vocalUrl,
                    Status: constants_1.Statues.Normal
                }, ['ID', 'VocalUrl', 'AuditStatus']);
                if (!recording) {
                    logger.warn({ ctx, vocalUrl, auditStatus, rejectReason }, "HandleAuditCallbackRecordingNotFound");
                    throw errorCodes_1.errorCodes.RecordingNotFound;
                }
                const updateData = {
                    AuditStatus: auditStatus,
                    AuditTime: Date.now(),
                    AuditRejectReason: rejectReason || undefined
                };
                const query = this.scope().where('ID', recording.ID).update(updateData);
                const ret = yield this.executeByQuery(query);
                if (ret.affectedRows === 0) {
                    logger.warn({ ctx, vocalUrl, auditStatus, rejectReason }, "HandleAuditCallbackUpdateFailed");
                }
                else {
                    logger.info({ ctx, vocalUrl, auditStatus, rejectReason, recordingId: recording.ID }, "HandleAuditCallbackSuccess");
                }
            }
            catch (error) {
                logger.error({ ctx, error, vocalUrl, auditStatus, rejectReason }, "HandleAuditCallbackError");
                throw error;
            }
        });
    }
}
exports.MusicClubRecordingModel = MusicClubRecordingModel;
//# sourceMappingURL=musicClubRecordingModel.js.map