import { BaseModelClass } from "../baseModel";
import { Context } from "../../context";
import { clazzLogger } from "../../logger";

const logger = clazzLogger("models.musicClubRecordingAuditTaskModel");

/**
 * 审核任务状态枚举
 */
export enum AuditTaskStatus {
  /** 待审核 */
  PENDING = 0,
  /** 已送审 */
  SUBMITTED = 1,
  /** 审核通过 */
  PASSED = 2,
  /** 审核拒绝 */
  REJECTED = 3,
  /** 审核超时 */
  TIMEOUT = 4
}

/**
 * 音乐俱乐部录音审核任务记录
 */
export interface MusicClubRecordingAuditTaskRecord {
  /** 任务ID */
  ID: number;
  /** 唱片ID */
  RecordingId: number;
  /** 人声URL */
  VocalUrl: string;
  /** 角色ID */
  RoleId: number;
  /** 服务器ID */
  ServerId: number;
  /** 状态 */
  Status: AuditTaskStatus;
  /** 重试次数 */
  RetryCount: number;
  /** 审核任务ID */
  TaskId?: string;
  /** 拒绝原因 */
  RejectReason?: string;
  /** 创建时间戳 */
  CreateTime: number;
  /** 更新时间戳 */
  UpdateTime: number;
}

export class MusicClubRecordingAuditTaskModel extends BaseModelClass<MusicClubRecordingAuditTaskRecord> {
  private static instance: MusicClubRecordingAuditTaskModel;

  constructor() {
    super('qnm_music_club_recording_audit_task');
  }

  static getInstance(): MusicClubRecordingAuditTaskModel {
    if (!MusicClubRecordingAuditTaskModel.instance) {
      MusicClubRecordingAuditTaskModel.instance = new MusicClubRecordingAuditTaskModel();
    }
    return MusicClubRecordingAuditTaskModel.instance;
  }

  /**
   * 创建审核任务
   */
  async createAuditTask(
    ctx: Context,
    recordingId: number,
    vocalUrl: string,
    roleId: number,
    serverId: number
  ): Promise<number> {
    const now = Date.now();
    const task: Omit<MusicClubRecordingAuditTaskRecord, 'ID'> = {
      RecordingId: recordingId,
      VocalUrl: vocalUrl,
      RoleId: roleId,
      ServerId: serverId,
      Status: AuditTaskStatus.PENDING,
      RetryCount: 0,
      CreateTime: now,
      UpdateTime: now
    };

    const insertId = await this.insert(task);
    logger.info({ ctx, recordingId, vocalUrl, taskId: insertId }, "CreateAuditTaskSuccess");
    return insertId;
  }

  /**
   * 根据人声URL查找审核任务
   */
  async findByVocalUrl(vocalUrl: string): Promise<MusicClubRecordingAuditTaskRecord | null> {
    const tasks = await this.find({ VocalUrl: vocalUrl });
    return tasks.length > 0 ? tasks[0] : null;
  }

  /**
   * 根据录音ID查找审核任务
   */
  async findByRecordingId(recordingId: number): Promise<MusicClubRecordingAuditTaskRecord | null> {
    const tasks = await this.find({ RecordingId: recordingId });
    return tasks.length > 0 ? tasks[0] : null;
  }

  /**
   * 查找待处理的任务（状态为PENDING且重试次数未超限）
   */
  async findPendingTasks(maxRetries: number): Promise<MusicClubRecordingAuditTaskRecord[]> {
    const query = this.scope()
      .where('Status', AuditTaskStatus.PENDING)
      .andWhere('RetryCount', '<', maxRetries)
      .orderBy('CreateTime', 'ASC')
      .limit(100);

    return await this.executeByQuery(query);
  }

  /**
   * 查找超时的任务（状态为SUBMITTED且创建时间超过阈值）
   */
  async findTimeoutTasks(timeoutThreshold: number): Promise<MusicClubRecordingAuditTaskRecord[]> {
    const query = this.scope()
      .where('Status', AuditTaskStatus.SUBMITTED)
      .andWhere('CreateTime', '<', timeoutThreshold)
      .orderBy('CreateTime', 'ASC')
      .limit(100);

    return await this.executeByQuery(query);
  }

  /**
   * 更新任务状态
   */
  async updateTaskStatus(
    taskId: number,
    status: AuditTaskStatus,
    options?: {
      taskIdStr?: string;
      rejectReason?: string;
      retryCount?: number;
    }
  ): Promise<void> {
    const updateData: Partial<MusicClubRecordingAuditTaskRecord> = {
      Status: status,
      UpdateTime: Date.now()
    };

    if (options?.taskIdStr !== undefined) {
      updateData.TaskId = options.taskIdStr;
    }
    if (options?.rejectReason !== undefined) {
      updateData.RejectReason = options.rejectReason;
    }
    if (options?.retryCount !== undefined) {
      updateData.RetryCount = options.retryCount;
    }

    await this.updateByCondition({ ID: taskId }, updateData);
  }

  /**
   * 增加重试次数
   */
  async incrementRetryCount(taskId: number): Promise<void> {
    // 先获取当前任务
    const task = await this.findById(taskId);
    if (task) {
      await this.updateByCondition({ ID: taskId }, {
        RetryCount: task.RetryCount + 1,
        UpdateTime: Date.now()
      });
    }
  }

  /**
   * 获取任务统计信息
   */
  async getTaskStats(): Promise<{
    pending: number;
    submitted: number;
    passed: number;
    rejected: number;
    timeout: number;
  }> {
    const stats = {
      pending: 0,
      submitted: 0,
      passed: 0,
      rejected: 0,
      timeout: 0
    };

    // 分别查询每种状态的数量
    stats.pending = await this.count({ Status: AuditTaskStatus.PENDING });
    stats.submitted = await this.count({ Status: AuditTaskStatus.SUBMITTED });
    stats.passed = await this.count({ Status: AuditTaskStatus.PASSED });
    stats.rejected = await this.count({ Status: AuditTaskStatus.REJECTED });
    stats.timeout = await this.count({ Status: AuditTaskStatus.TIMEOUT });

    return stats;
  }
}
