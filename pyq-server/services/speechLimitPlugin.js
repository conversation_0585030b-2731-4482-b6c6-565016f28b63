"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.speechLimitStrictPlugin = exports.speechLimitPlugin = exports.SPEECH_LIMIT_STRATEGY = void 0;
const _ = require("lodash");
const util = require("../../common/util");
const auth_1 = require("../../service/qnm/pyq/auth");
const logger_1 = require("../logger");
const logger = (0, logger_1.clazzLogger)("speechLimitPlugin");
const CHECK_URLS = ["/qnm/setphoto", "/qnm/setrolename", "/qnm/changesign", "/qnm/changeloc"];
const ADD_MOMENT_URLS = ["/qnm/addmoment", "/qnm/addmoment2"];
const SPEECH_URLS = [
    "/qnm/addmoment",
    "/qnm/moment/forward",
    "/qnm/addmoment2",
    "/qnm/addcomment",
    "/qnm/addmessage",
    "/qnm/answermessage",
    "/qnm/card/notion/add",
    "/qnm/card/comment/add"
];
function isUrlHit(url, checkUrls) {
    return _.some(checkUrls, (r) => {
        return url.startsWith(r);
    });
}
const ALERT_MSG = "因技术升级，该功能暂不可用，对您造成不便深感歉意。";
exports.SPEECH_LIMIT_STRATEGY = {
    NONE: 0,
    BLOCK_SPEECH: 1,
    BLOCK_IMAGE: 2,
    BLOCK_ALL: 3 // 0011 - 屏蔽发言和发图
};
function getSpeechLimitStrategy(roleId) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const strategy = yield (0, auth_1.getLoginParams)(roleId, "speechLimitStrategy");
            return strategy ? parseInt(strategy, 10) : exports.SPEECH_LIMIT_STRATEGY.BLOCK_ALL;
        }
        catch (err) {
            logger.error({ err, roleId }, "GetSpeechLimitStrategyFailed");
            return exports.SPEECH_LIMIT_STRATEGY.BLOCK_ALL;
        }
    });
}
function speechLimitPlugin(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        let url = req.route.path;
        let params = req.params;
        // 对于某些 URL，直接屏蔽，不考虑策略
        if (isUrlHit(url, CHECK_URLS)) {
            return res.send({ code: -1, msg: ALERT_MSG });
        }
        // 对于发言相关的 URL，检查策略
        if (isUrlHit(url, SPEECH_URLS) && params.roleid) {
            try {
                const strategy = yield getSpeechLimitStrategy(params.roleid);
                // 检查是否屏蔽发言 (BLOCK_SPEECH 位被设置)
                if (strategy & exports.SPEECH_LIMIT_STRATEGY.BLOCK_SPEECH) {
                    return res.send({ code: -1, msg: ALERT_MSG });
                }
            }
            catch (err) {
                return res.send({ code: -2, msg: "please reLogin" });
            }
        }
        // 对于添加动态的 URL，检查是否包含图片或视频
        if (isUrlHit(url, ADD_MOMENT_URLS)) {
            let params = req.params;
            let imgList = util.getJsonInfo(params.imglist, []);
            let videoList = util.getJsonInfo(params.videolist, []);
            if ((imgList && imgList.length) || (videoList && videoList.length)) {
                try {
                    const strategy = yield getSpeechLimitStrategy(params.roleid);
                    // 检查是否屏蔽发图 (BLOCK_IMAGE 位被设置)
                    if (strategy & exports.SPEECH_LIMIT_STRATEGY.BLOCK_IMAGE) {
                        const msg = imgList && imgList.length
                            ? "图片服务器升级，暂不支持发表图片动态"
                            : "视频服务器升级，暂不支持发表视频动态";
                        return res.send({ code: -1, msg });
                    }
                }
                catch (err) {
                    return res.send({ code: -2, msg: "please reLogin" });
                }
            }
        }
        return next();
    });
}
exports.speechLimitPlugin = speechLimitPlugin;
function speechLimitStrictPlugin(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        let url = req.route.path;
        if (isUrlHit(url, _.concat(CHECK_URLS, SPEECH_URLS))) {
            return res.send({ code: -1, msg: ALERT_MSG });
        }
        else {
            return next();
        }
    });
}
exports.speechLimitStrictPlugin = speechLimitStrictPlugin;
//# sourceMappingURL=speechLimitPlugin.js.map