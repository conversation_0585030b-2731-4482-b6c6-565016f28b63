import * as _ from "lodash";
import util = require("../../common/util");
import { getLoginParams } from "../../service/qnm/pyq/auth";
import { clazzLogger } from "../logger";

const logger = clazzLogger("speechLimitPlugin");

const CHECK_URLS = ["/qnm/setphoto", "/qnm/setrolename", "/qnm/changesign", "/qnm/changeloc"];

const ADD_MOMENT_URLS = ["/qnm/addmoment", "/qnm/addmoment2"];

const SPEECH_URLS = [
  "/qnm/addmoment",
  "/qnm/moment/forward",
  "/qnm/addmoment2",
  "/qnm/addcomment",
  "/qnm/addmessage",
  "/qnm/answermessage",

  "/qnm/card/notion/add",
  "/qnm/card/comment/add"
];

function isUrlHit(url: string, checkUrls: string[]) {
  return _.some(checkUrls, (r) => {
    return url.startsWith(r);
  });
}

const ALERT_MSG = "因技术升级，该功能暂不可用，对您造成不便深感歉意。";

export const SPEECH_LIMIT_STRATEGY = {
  NONE: 0,          // 0000 - 无限制
  BLOCK_SPEECH: 1,  // 0001 - 屏蔽发言
  BLOCK_IMAGE: 2,   // 0010 - 屏蔽发图
  BLOCK_ALL: 3      // 0011 - 屏蔽发言和发图
};

async function getSpeechLimitStrategy(roleId: number): Promise<number> {
  try {
    const strategy = await getLoginParams(roleId, "speechLimitStrategy");
    return strategy ? parseInt(strategy, 10) : SPEECH_LIMIT_STRATEGY.BLOCK_ALL;
  } catch (err) {
    logger.error({ err, roleId }, "GetSpeechLimitStrategyFailed");
    return SPEECH_LIMIT_STRATEGY.BLOCK_ALL;
  }
}

export async function speechLimitPlugin(req, res, next) {
  let url = req.route.path;
  let params = req.params;
  
  // 对于某些 URL，直接屏蔽，不考虑策略
  if (isUrlHit(url, CHECK_URLS)) {
    return res.send({ code: -1, msg: ALERT_MSG });
  }
  
  // 对于发言相关的 URL，检查策略
  if (isUrlHit(url, SPEECH_URLS) && params.roleid) {
    try {
      const strategy = await getSpeechLimitStrategy(params.roleid);
      
      // 检查是否屏蔽发言 (BLOCK_SPEECH 位被设置)
      if (strategy & SPEECH_LIMIT_STRATEGY.BLOCK_SPEECH) {
        return res.send({ code: -1, msg: ALERT_MSG });
      }
    } catch (err) {
      return res.send({ code: -2, msg: "please reLogin" });
    }
  }
  
  // 对于添加动态的 URL，检查是否包含图片或视频
  if (isUrlHit(url, ADD_MOMENT_URLS)) {
    let params = req.params;
    let imgList = util.getJsonInfo(params.imglist, []);
    let videoList = util.getJsonInfo(params.videolist, []);
    
    if ((imgList && imgList.length) || (videoList && videoList.length)) {
      try {
        const strategy = await getSpeechLimitStrategy(params.roleid);
        
        // 检查是否屏蔽发图 (BLOCK_IMAGE 位被设置)
        if (strategy & SPEECH_LIMIT_STRATEGY.BLOCK_IMAGE) {
          const msg = imgList && imgList.length 
            ? "图片服务器升级，暂不支持发表图片动态" 
            : "视频服务器升级，暂不支持发表视频动态";
          return res.send({ code: -1, msg });
        }
      } catch (err) {
        return res.send({ code: -2, msg: "please reLogin" });
      }
    }
  }
  
  return next();
}

export async function speechLimitStrictPlugin(req, res, next) {
  let url = req.route.path;
  if (isUrlHit(url, _.concat(CHECK_URLS, SPEECH_URLS))) {
    return res.send({ code: -1, msg: ALERT_MSG });
  }  else {
    return next()
  }
}
