"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MusicClubRecordingAuditTaskService = void 0;
const logger_1 = require("../../logger");
const errorCodes_1 = require("../../errorCodes");
const musicClubRecordingAuditTaskModel_1 = require("../../models/musicClub/musicClubRecordingAuditTaskModel");
const musicClubRecordingModel_1 = require("../../models/musicClub/musicClubRecordingModel");
const ccAudioAuditService_1 = require("./ccAudioAuditService");
const config_1 = require("../../common/config");
const logger = (0, logger_1.clazzLogger)("services.musicClubRecordingAuditTaskService");
/**
 * 音乐俱乐部录音审核任务服务
 * 负责管理录音审核任务的生命周期，包括创建、提交、重试、超时检查等
 */
class MusicClubRecordingAuditTaskService {
    constructor() {
        this.auditTaskModel = musicClubRecordingAuditTaskModel_1.MusicClubRecordingAuditTaskModel.getInstance();
        this.recordingModel = musicClubRecordingModel_1.MusicClubRecordingModel.getInstance();
        this.ccAudioAuditService = ccAudioAuditService_1.CCAudioAuditService.getInstance();
    }
    static getInstance() {
        if (!MusicClubRecordingAuditTaskService.instance) {
            MusicClubRecordingAuditTaskService.instance = new MusicClubRecordingAuditTaskService();
        }
        return MusicClubRecordingAuditTaskService.instance;
    }
    /**
     * 创建审核任务并立即尝试提交
     * @param ctx 上下文
     * @param recordingId 录音ID
     * @param vocalUrl 人声URL
     * @param roleId 角色ID
     * @param serverId 服务器ID
     * @returns 任务ID
     */
    createAuditTask(ctx, recordingId, vocalUrl, roleId, serverId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // 检查是否已存在该录音的审核任务
                const existingTask = yield this.auditTaskModel.findByRecordingId(recordingId);
                if (existingTask) {
                    logger.warn({ ctx, recordingId, existingTaskId: existingTask.ID }, "AuditTaskAlreadyExists");
                    return existingTask.ID;
                }
                // 创建新的审核任务
                const taskId = yield this.auditTaskModel.createAuditTask(ctx, recordingId, vocalUrl, roleId, serverId);
                // 立即尝试提交审核
                this.submitAuditTask(ctx, taskId).catch(err => {
                    logger.error({ ctx, err, taskId }, "InitialSubmitAuditTaskFailed");
                });
                return taskId;
            }
            catch (error) {
                logger.error({ ctx, error, recordingId, vocalUrl }, "CreateAuditTaskError");
                throw errorCodes_1.errorCodes.DatabaseError;
            }
        });
    }
    /**
     * 提交审核任务到CC审核系统
     * @param ctx 上下文
     * @param taskId 任务ID
     * @param skipRetryLimit 是否跳过重试次数限制（管理后台使用）
     * @returns 是否提交成功
     */
    submitAuditTask(ctx, taskId, skipRetryLimit = false) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const task = yield this.auditTaskModel.findById(taskId);
                if (!task) {
                    logger.error({ ctx, taskId }, "AuditTaskNotFound");
                    return false;
                }
                if (task.Status !== musicClubRecordingAuditTaskModel_1.AuditTaskStatus.PENDING) {
                    logger.warn({ ctx, taskId, status: task.Status }, "AuditTaskNotPending");
                    return false;
                }
                // 检查重试次数是否超限（管理后台可以跳过此限制）
                if (!skipRetryLimit && task.RetryCount >= config_1.musicClubCfg.auditMaxRetries) {
                    logger.warn({ ctx, taskId, retryCount: task.RetryCount }, "AuditTaskMaxRetriesExceeded");
                    return false;
                }
                // 生成唯一的流名称
                const streamName = this.generateStreamName(task.RoleId, task.RecordingId);
                // 调用CC审核服务
                const auditResult = yield this.ccAudioAuditService.submitAudioAudit(ctx, {
                    roleId: task.RoleId,
                    serverId: task.ServerId,
                    audioUrl: task.VocalUrl,
                    streamName: streamName
                });
                if (auditResult.success) {
                    // 更新任务状态为已送审
                    yield this.auditTaskModel.updateTaskStatus(taskId, musicClubRecordingAuditTaskModel_1.AuditTaskStatus.SUBMITTED, {
                        taskIdStr: auditResult.taskId,
                        retryCount: task.RetryCount + 1
                    });
                    logger.info({ ctx, taskId, ccTaskId: auditResult.taskId }, "SubmitAuditTaskSuccess");
                    return true;
                }
                else {
                    // 增加重试次数
                    yield this.auditTaskModel.incrementRetryCount(taskId);
                    logger.warn({ ctx, taskId, error: auditResult.error }, "SubmitAuditTaskFailed");
                    return false;
                }
            }
            catch (error) {
                logger.error({ ctx, taskId, error }, "SubmitAuditTaskError");
                // 增加重试次数
                try {
                    yield this.auditTaskModel.incrementRetryCount(taskId);
                }
                catch (updateError) {
                    logger.error({ ctx, taskId, updateError }, "IncrementRetryCountError");
                }
                return false;
            }
        });
    }
    /**
     * 处理审核回调
     * @param ctx 上下文
     * @param vocalUrl 人声URL
     * @param auditStatus 审核状态 (1-通过, -1-拒绝)
     * @param rejectReason 拒绝原因
     * @returns 是否处理成功
     */
    handleAuditCallback(ctx, vocalUrl, auditStatus, rejectReason) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // 根据vocalUrl查找对应的任务
                const task = yield this.auditTaskModel.findByVocalUrl(vocalUrl);
                if (!task) {
                    logger.error({ ctx, vocalUrl }, "AuditTaskNotFoundByVocalUrl");
                    return false;
                }
                // 检查任务状态是否为已送审
                if (task.Status !== musicClubRecordingAuditTaskModel_1.AuditTaskStatus.SUBMITTED) {
                    logger.warn({ ctx, taskId: task.ID, currentStatus: task.Status }, "AuditTaskNotSubmitted");
                    return false;
                }
                const newStatus = auditStatus === 1 ? musicClubRecordingAuditTaskModel_1.AuditTaskStatus.PASSED : musicClubRecordingAuditTaskModel_1.AuditTaskStatus.REJECTED;
                // 更新任务状态
                yield this.auditTaskModel.updateTaskStatus(task.ID, newStatus, {
                    rejectReason: rejectReason
                });
                // 更新录音的审核状态
                yield this.updateRecordingAuditStatus(ctx, task.RecordingId, auditStatus, rejectReason);
                logger.info({
                    ctx,
                    taskId: task.ID,
                    recordingId: task.RecordingId,
                    auditStatus,
                    rejectReason
                }, "HandleAuditCallbackSuccess");
                return true;
            }
            catch (error) {
                logger.error({ ctx, error, vocalUrl, auditStatus, rejectReason }, "HandleAuditCallbackError");
                return false;
            }
        });
    }
    /**
     * 更新录音的审核状态
     * @param ctx 上下文
     * @param recordingId 录音ID
     * @param auditStatus 审核状态 (1-通过, -1-拒绝)
     * @param rejectReason 拒绝原因
     */
    updateRecordingAuditStatus(ctx, recordingId, auditStatus, rejectReason) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const recording = yield this.recordingModel.findById(recordingId);
                if (!recording) {
                    logger.error({ ctx, recordingId }, "RecordingNotFoundForAuditUpdate");
                    return;
                }
                // 根据审核结果更新录音状态
                const updateData = {
                    AuditStatus: auditStatus === 1 ? 1 : -1,
                    UpdateTime: Date.now()
                };
                if (auditStatus !== 1 && rejectReason) {
                    updateData.RejectReason = rejectReason;
                }
                yield this.recordingModel.updateByCondition({ ID: recordingId }, updateData);
                logger.info({ ctx, recordingId, auditStatus }, "UpdateRecordingAuditStatusSuccess");
            }
            catch (error) {
                logger.error({ ctx, error, recordingId, auditStatus }, "UpdateRecordingAuditStatusError");
                throw error;
            }
        });
    }
    /**
     * 检查并处理超时任务
     * @param ctx 上下文
     * @param timeoutMinutes 超时时间（分钟）
     * @returns 处理的任务数量
     */
    checkTimeoutTasks(ctx, timeoutMinutes = 30) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const timeoutThreshold = Date.now() - timeoutMinutes * 60 * 1000;
                const timeoutTasks = yield this.auditTaskModel.findTimeoutTasks(timeoutThreshold);
                let processedCount = 0;
                for (const task of timeoutTasks) {
                    yield this.auditTaskModel.updateTaskStatus(task.ID, musicClubRecordingAuditTaskModel_1.AuditTaskStatus.TIMEOUT);
                    // 记录超时日志，便于后续告警或人工处理
                    logger.warn({
                        ctx,
                        taskId: task.ID,
                        recordingId: task.RecordingId,
                        createTime: new Date(task.CreateTime).toISOString(),
                        elapsedMinutes: Math.floor((Date.now() - task.CreateTime) / (60 * 1000))
                    }, "AuditTaskTimeout");
                    processedCount++;
                }
                if (processedCount > 0) {
                    logger.info({ ctx, processedCount }, "CheckTimeoutTasksCompleted");
                }
                return processedCount;
            }
            catch (error) {
                logger.error({ ctx, error }, "CheckTimeoutTasksError");
                return 0;
            }
        });
    }
    /**
     * 重试待处理任务
     * @param ctx 上下文
     * @param maxRetries 最大重试次数
     * @returns 成功重试的任务数量
     */
    retryPendingTasks(ctx, maxRetries = 3) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const pendingTasks = yield this.auditTaskModel.findPendingTasks(maxRetries);
                let successCount = 0;
                for (const task of pendingTasks) {
                    const success = yield this.submitAuditTask(ctx, task.ID);
                    if (success) {
                        successCount++;
                    }
                }
                if (pendingTasks.length > 0) {
                    logger.info({
                        ctx,
                        totalCount: pendingTasks.length,
                        successCount
                    }, "RetryPendingTasksCompleted");
                }
                return successCount;
            }
            catch (error) {
                logger.error({ ctx, error }, "RetryPendingTasksError");
                return 0;
            }
        });
    }
    /**
     * 获取任务统计信息
     * @param ctx 上下文
     * @returns 任务统计信息
     */
    getTaskStats(ctx) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                return yield this.auditTaskModel.getTaskStats();
            }
            catch (error) {
                logger.error({ ctx, error }, "GetTaskStatsError");
                return {
                    pending: 0,
                    submitted: 0,
                    passed: 0,
                    rejected: 0,
                    timeout: 0
                };
            }
        });
    }
    /**
     * 重新提交审核任务（管理后台使用，跳过重试次数限制）
     * @param taskId 任务ID
     * @returns 是否成功
     */
    retryTask(taskId) {
        return __awaiter(this, void 0, void 0, function* () {
            const ctx = { requestId: `retry_${taskId}_${Date.now()}` };
            try {
                // 首先将任务状态重置为待审核，以便可以重新提交
                yield this.auditTaskModel.updateTaskStatus(taskId, musicClubRecordingAuditTaskModel_1.AuditTaskStatus.PENDING);
                // 调用submitAuditTask方法，跳过重试次数限制
                const success = yield this.submitAuditTask(ctx, taskId, true);
                if (success) {
                    logger.info({ ctx, taskId }, "AdminRetryTaskSuccess");
                    return true;
                }
                else {
                    logger.error({ ctx, taskId }, "AdminRetryTaskFailed");
                    return false;
                }
            }
            catch (error) {
                logger.error({ ctx, error, taskId }, "AdminRetryTaskError");
                return false;
            }
        });
    }
    /**
     * 生成审核流名称
     * @param roleId 角色ID
     * @param recordingId 录音ID
     * @returns 流名称
     */
    generateStreamName(roleId, recordingId) {
        return `recording_${roleId}_${recordingId}_${Date.now()}`;
    }
}
exports.MusicClubRecordingAuditTaskService = MusicClubRecordingAuditTaskService;
//# sourceMappingURL=musicClubRecordingAuditTaskService.js.map