import { Context } from "../../context";
import { clazzLogger } from "../../logger";
import { errorCodes } from "../../errorCodes";
import {
  MusicClubRecordingAuditTaskModel,
  AuditTaskStatus
} from "../../models/musicClub/musicClubRecordingAuditTaskModel";
import { MusicClubRecordingModel } from "../../models/musicClub/musicClubRecordingModel";
import { CCAudioAuditService } from "./ccAudioAuditService";
import { musicClubCfg } from "../../common/config";

const logger = clazzLogger("services.musicClubRecordingAuditTaskService");

/**
 * 音乐俱乐部录音审核任务服务
 * 负责管理录音审核任务的生命周期，包括创建、提交、重试、超时检查等
 */
export class MusicClubRecordingAuditTaskService {
  private static instance: MusicClubRecordingAuditTaskService;
  private auditTaskModel: MusicClubRecordingAuditTaskModel;
  private recordingModel: MusicClubRecordingModel;
  private ccAudioAuditService: CCAudioAuditService;

  constructor() {
    this.auditTaskModel = MusicClubRecordingAuditTaskModel.getInstance();
    this.recordingModel = MusicClubRecordingModel.getInstance();
    this.ccAudioAuditService = CCAudioAuditService.getInstance();
  }

  static getInstance(): MusicClubRecordingAuditTaskService {
    if (!MusicClubRecordingAuditTaskService.instance) {
      MusicClubRecordingAuditTaskService.instance = new MusicClubRecordingAuditTaskService();
    }
    return MusicClubRecordingAuditTaskService.instance;
  }

  /**
   * 创建审核任务并立即尝试提交
   * @param ctx 上下文
   * @param recordingId 录音ID
   * @param vocalUrl 人声URL
   * @param roleId 角色ID
   * @param serverId 服务器ID
   * @returns 任务ID
   */
  async createAuditTask(ctx: Context, recordingId: number, vocalUrl: string, roleId: number, serverId: number): Promise<number> {
    try {
      // 检查是否已存在该录音的审核任务
      const existingTask = await this.auditTaskModel.findByRecordingId(recordingId);
      if (existingTask) {
        logger.warn({ ctx, recordingId, existingTaskId: existingTask.ID }, "AuditTaskAlreadyExists");
        return existingTask.ID;
      }

      // 创建新的审核任务
      const taskId = await this.auditTaskModel.createAuditTask(ctx, recordingId, vocalUrl, roleId, serverId);

      // 立即尝试提交审核
      this.submitAuditTask(ctx, taskId).catch(err => {
        logger.error({ ctx, err, taskId }, "InitialSubmitAuditTaskFailed");
      });

      return taskId;
    } catch (error) {
      logger.error({ ctx, error, recordingId, vocalUrl }, "CreateAuditTaskError");
      throw errorCodes.DatabaseError;
    }
  }

  /**
   * 提交审核任务到CC审核系统
   * @param ctx 上下文
   * @param taskId 任务ID
   * @param skipRetryLimit 是否跳过重试次数限制（管理后台使用）
   * @returns 是否提交成功
   */
  async submitAuditTask(ctx: Context, taskId: number, skipRetryLimit: boolean = false): Promise<boolean> {
    try {
      const task = await this.auditTaskModel.findById(taskId);
      if (!task) {
        logger.error({ ctx, taskId }, "AuditTaskNotFound");
        return false;
      }

      if (task.Status !== AuditTaskStatus.PENDING) {
        logger.warn({ ctx, taskId, status: task.Status }, "AuditTaskNotPending");
        return false;
      }

      // 检查重试次数是否超限（管理后台可以跳过此限制）
      if (!skipRetryLimit && task.RetryCount >= musicClubCfg.auditMaxRetries) {
        logger.warn({ ctx, taskId, retryCount: task.RetryCount }, "AuditTaskMaxRetriesExceeded");
        return false;
      }

      // 生成唯一的流名称
      const streamName = this.generateStreamName(task.RoleId, task.RecordingId);

      // 调用CC审核服务
      const auditResult = await this.ccAudioAuditService.submitAudioAudit(ctx, {
        roleId: task.RoleId,
        serverId: task.ServerId,
        audioUrl: task.VocalUrl,
        streamName: streamName
      });

      if (auditResult.success) {
        // 更新任务状态为已送审
        await this.auditTaskModel.updateTaskStatus(taskId, AuditTaskStatus.SUBMITTED, {
          taskIdStr: auditResult.taskId,
          retryCount: task.RetryCount + 1
        });

        logger.info({ ctx, taskId, ccTaskId: auditResult.taskId }, "SubmitAuditTaskSuccess");
        return true;
      } else {
        // 增加重试次数
        await this.auditTaskModel.incrementRetryCount(taskId);
        logger.warn({ ctx, taskId, error: auditResult.error }, "SubmitAuditTaskFailed");
        return false;
      }
    } catch (error) {
      logger.error({ ctx, taskId, error }, "SubmitAuditTaskError");

      // 增加重试次数
      try {
        await this.auditTaskModel.incrementRetryCount(taskId);
      } catch (updateError) {
        logger.error({ ctx, taskId, updateError }, "IncrementRetryCountError");
      }

      return false;
    }
  }

  /**
   * 处理审核回调
   * @param ctx 上下文
   * @param vocalUrl 人声URL
   * @param auditStatus 审核状态 (1-通过, -1-拒绝)
   * @param rejectReason 拒绝原因
   * @returns 是否处理成功
   */
  async handleAuditCallback(
    ctx: Context,
    vocalUrl: string,
    auditStatus: number,
    rejectReason?: string
  ): Promise<boolean> {
    try {
      // 根据vocalUrl查找对应的任务
      const task = await this.auditTaskModel.findByVocalUrl(vocalUrl);
      if (!task) {
        logger.error({ ctx, vocalUrl }, "AuditTaskNotFoundByVocalUrl");
        return false;
      }

      // 检查任务状态是否为已送审
      if (task.Status !== AuditTaskStatus.SUBMITTED) {
        logger.warn({ ctx, taskId: task.ID, currentStatus: task.Status }, "AuditTaskNotSubmitted");
        return false;
      }

      const newStatus = auditStatus === 1 ? AuditTaskStatus.PASSED : AuditTaskStatus.REJECTED;

      // 更新任务状态
      await this.auditTaskModel.updateTaskStatus(task.ID, newStatus, {
        rejectReason: rejectReason
      });

      // 更新录音的审核状态
      await this.updateRecordingAuditStatus(ctx, task.RecordingId, auditStatus, rejectReason);

      logger.info({
        ctx,
        taskId: task.ID,
        recordingId: task.RecordingId,
        auditStatus,
        rejectReason
      }, "HandleAuditCallbackSuccess");

      return true;
    } catch (error) {
      logger.error({ ctx, error, vocalUrl, auditStatus, rejectReason }, "HandleAuditCallbackError");
      return false;
    }
  }

  /**
   * 更新录音的审核状态
   * @param ctx 上下文
   * @param recordingId 录音ID
   * @param auditStatus 审核状态 (1-通过, -1-拒绝)
   * @param rejectReason 拒绝原因
   */
  private async updateRecordingAuditStatus(
    ctx: Context,
    recordingId: number,
    auditStatus: number,
    rejectReason?: string
  ): Promise<void> {
    try {
      const recording = await this.recordingModel.findById(recordingId);
      if (!recording) {
        logger.error({ ctx, recordingId }, "RecordingNotFoundForAuditUpdate");
        return;
      }

      // 根据审核结果更新录音状态
      const updateData: any = {
        AuditStatus: auditStatus === 1 ? 1 : -1, // 1-通过, -1-拒绝
        UpdateTime: Date.now()
      };

      if (auditStatus !== 1 && rejectReason) {
        updateData.RejectReason = rejectReason;
      }

      await this.recordingModel.updateByCondition({ ID: recordingId }, updateData);

      logger.info({ ctx, recordingId, auditStatus }, "UpdateRecordingAuditStatusSuccess");
    } catch (error) {
      logger.error({ ctx, error, recordingId, auditStatus }, "UpdateRecordingAuditStatusError");
      throw error;
    }
  }

  /**
   * 检查并处理超时任务
   * @param ctx 上下文
   * @param timeoutMinutes 超时时间（分钟）
   * @returns 处理的任务数量
   */
  async checkTimeoutTasks(ctx: Context, timeoutMinutes: number = 30): Promise<number> {
    try {
      const timeoutThreshold = Date.now() - timeoutMinutes * 60 * 1000;
      const timeoutTasks = await this.auditTaskModel.findTimeoutTasks(timeoutThreshold);

      let processedCount = 0;
      for (const task of timeoutTasks) {
        await this.auditTaskModel.updateTaskStatus(task.ID, AuditTaskStatus.TIMEOUT);

        // 记录超时日志，便于后续告警或人工处理
        logger.warn({
          ctx,
          taskId: task.ID,
          recordingId: task.RecordingId,
          createTime: new Date(task.CreateTime).toISOString(),
          elapsedMinutes: Math.floor((Date.now() - task.CreateTime) / (60 * 1000))
        }, "AuditTaskTimeout");

        processedCount++;
      }

      if (processedCount > 0) {
        logger.info({ ctx, processedCount }, "CheckTimeoutTasksCompleted");
      }

      return processedCount;
    } catch (error) {
      logger.error({ ctx, error }, "CheckTimeoutTasksError");
      return 0;
    }
  }

  /**
   * 重试待处理任务
   * @param ctx 上下文
   * @param maxRetries 最大重试次数
   * @returns 成功重试的任务数量
   */
  async retryPendingTasks(ctx: Context, maxRetries: number = 3): Promise<number> {
    try {
      const pendingTasks = await this.auditTaskModel.findPendingTasks(maxRetries);

      let successCount = 0;
      for (const task of pendingTasks) {
        const success = await this.submitAuditTask(ctx, task.ID);
        if (success) {
          successCount++;
        }
      }

      if (pendingTasks.length > 0) {
        logger.info({
          ctx,
          totalCount: pendingTasks.length,
          successCount
        }, "RetryPendingTasksCompleted");
      }

      return successCount;
    } catch (error) {
      logger.error({ ctx, error }, "RetryPendingTasksError");
      return 0;
    }
  }

  /**
   * 获取任务统计信息
   * @param ctx 上下文
   * @returns 任务统计信息
   */
  async getTaskStats(ctx: Context): Promise<{
    pending: number;
    submitted: number;
    passed: number;
    rejected: number;
    timeout: number;
  }> {
    try {
      return await this.auditTaskModel.getTaskStats();
    } catch (error) {
      logger.error({ ctx, error }, "GetTaskStatsError");
      return {
        pending: 0,
        submitted: 0,
        passed: 0,
        rejected: 0,
        timeout: 0
      };
    }
  }

  /**
   * 重新提交审核任务（管理后台使用，跳过重试次数限制）
   * @param taskId 任务ID
   * @returns 是否成功
   */
  async retryTask(taskId: number): Promise<boolean> {
    const ctx = { requestId: `retry_${taskId}_${Date.now()}` } as any;

    try {
      // 首先将任务状态重置为待审核，以便可以重新提交
      await this.auditTaskModel.updateTaskStatus(taskId, AuditTaskStatus.PENDING);

      // 调用submitAuditTask方法，跳过重试次数限制
      const success = await this.submitAuditTask(ctx, taskId, true);

      if (success) {
        logger.info({ ctx, taskId }, "AdminRetryTaskSuccess");
        return true;
      } else {
        logger.error({ ctx, taskId }, "AdminRetryTaskFailed");
        return false;
      }

    } catch (error) {
      logger.error({ ctx, error, taskId }, "AdminRetryTaskError");
      return false;
    }
  }

  /**
   * 生成审核流名称
   * @param roleId 角色ID
   * @param recordingId 录音ID
   * @returns 流名称
   */
  private generateStreamName(roleId: number, recordingId: number): string {
    return `recording_${roleId}_${recordingId}_${Date.now()}`;
  }
}
