post:
  tags:
    - auth
  operationId: authLogin
  summary: 角色登录梦岛
  security: []
  description: 角色登录梦岛, 获取接口访问凭证
  parameters:
    - $ref: '../../components/parameters/auth/authTime.yaml'
    - $ref: '../../components/parameters/auth/authAccount.yaml'
    - $ref: '../../components/parameters/auth/authUrs.yaml'
    - $ref: '../../components/parameters/auth/authAccountId.yaml'
    - $ref: '../../components/parameters/roleid.yaml'
    - $ref: '../../components/parameters/common/server.yaml'
    - $ref: '../../components/parameters/common/roleLevel.yaml'
    - $ref: '../../components/parameters/common/language.yaml'
    - $ref: '../../components/parameters/common/country.yaml'
    - $ref: '../../components/parameters/auth/authToken.yaml'
    - name: isOverSea
      in: query
      description: |
        【废弃】是否为海外用户, 0: 否, 1: 是
        （该字段已废弃，请使用 speechLimitStrategy 字段进行精细化控制）
      deprecated: true
      schema:
        type: integer
        enum: [0, 1]
        example: true
    - name: speechLimitStrategy
      in: query
      description: |
        发言限制策略，使用二进制标志位控制：
        - 0 (0000): 无限制
        - 1 (0001): 屏蔽发言 (BLOCK_SPEECH)
        - 2 (0010): 屏蔽发图 (BLOCK_IMAGE)
        - 3 (0011): 屏蔽发言和发图 (BLOCK_ALL)
      schema:
        type: string
        enum: ["0", "1", "2", "3"]
        example: "3"
  responses:
    "200":
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: "../../components/schemas/auth/auth-login-resp.yaml"