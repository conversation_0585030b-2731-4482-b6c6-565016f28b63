-- nodejs_yxb.pyq_face_pinch_apply definition

CREATE TABLE `pyq_face_pinch_apply` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `userId` bigint(20) NOT NULL DEFAULT '0' COMMENT '用户id',
  `roleId` bigint(20) NOT NULL COMMENT '用户id',
  `workId` bigint(20) NOT NULL COMMENT '作品ID',
  `targetId` bigint(20) NOT NULL COMMENT '心情对应的用户ID',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '-1=>取消使用， 0=>使用',
  `createTime` bigint(20) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `userId` (`userId`),
  KEY `createTime` (`createTime`)
) ENGINE=InnoDB AUTO_INCREMENT=1655 DEFAULT CHARSET=utf8 COMMENT='倩女手游捏脸使用记录表';


-- nodejs_yxb.pyq_face_pinch_collect definition

CREATE TABLE `pyq_face_pinch_collect` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `userId` bigint(20) NOT NULL DEFAULT '0' COMMENT '用户id',
  `roleId` bigint(20) NOT NULL COMMENT '用户id',
  `workId` bigint(20) NOT NULL COMMENT '作品ID',
  `targetId` bigint(20) NOT NULL COMMENT '心情对应的用户ID',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '-1=>取消收藏， 0=>收藏',
  `createTime` bigint(20) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_user_collect` (`userId`,`workId`) USING BTREE,
  KEY `userId` (`userId`),
  KEY `createTime` (`createTime`)
) ENGINE=InnoDB AUTO_INCREMENT=2097 DEFAULT CHARSET=utf8 COMMENT='倩女手游捏脸收藏表';


-- nodejs_yxb.pyq_face_pinch_like definition

CREATE TABLE `pyq_face_pinch_like` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `userId` bigint(20) NOT NULL DEFAULT '0' COMMENT '用户id',
  `roleId` bigint(20) NOT NULL COMMENT '用户id',
  `workId` bigint(20) NOT NULL COMMENT '作品ID',
  `targetId` bigint(20) NOT NULL COMMENT '心情对应的用户ID',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '-1=>取消点赞， 0=>点赞',
  `createTime` bigint(20) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `userId` (`userId`),
  KEY `createTime` (`createTime`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8 COMMENT='倩女手游捏脸点赞表';


-- nodejs_yxb.pyq_face_pinch_share_video definition

CREATE TABLE `pyq_face_pinch_share_video` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `userId` bigint(20) NOT NULL DEFAULT '0' COMMENT '用户id',
  `roleId` bigint(20) NOT NULL COMMENT '角色 id',
  `video` varchar(255) NOT NULL DEFAULT '' COMMENT '作品分享视频 游戏内传来的视频链接',
  `shareId` char(21) NOT NULL DEFAULT '' COMMENT '分享Id',
  `status` tinyint(4) DEFAULT '0' COMMENT '作品状态：0 正常 -1 已删除',
  `createTime` bigint(20) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `shareId` (`shareId`),
  KEY `roleId` (`roleId`),
  KEY `userId` (`userId`)
) ENGINE=InnoDB AUTO_INCREMENT=454 DEFAULT CHARSET=utf8 COMMENT='捏脸站视频分享链接';


-- nodejs_yxb.pyq_face_pinch_user definition

CREATE TABLE `pyq_face_pinch_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户id',
  `roleId` bigint(20) NOT NULL DEFAULT '0' COMMENT '角色id',
  `roleName` varchar(63) DEFAULT NULL COMMENT '角色昵称',
  `account` varchar(255) DEFAULT NULL COMMENT '账号',
  `createTime` bigint(20) NOT NULL COMMENT '创建时间',
  `updatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `account_role` (`account`,`roleId`) USING BTREE,
  KEY `roleId` (`roleId`),
  KEY `createTime` (`createTime`)
) ENGINE=InnoDB AUTO_INCREMENT=654276 DEFAULT CHARSET=utf8 COMMENT='倩女手游捏脸站用户';


-- nodejs_yxb.pyq_face_pinch_week_hot definition

CREATE TABLE `pyq_face_pinch_week_hot` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `week` char(10) NOT NULL COMMENT '一周开始，记录当周周一',
  `workId` bigint(20) NOT NULL COMMENT '作品ID',
  `userId` bigint(20) NOT NULL DEFAULT '0' COMMENT '用户id',
  `roleId` bigint(20) NOT NULL COMMENT '角色 id',
  `gender` tinyint(4) NOT NULL DEFAULT '0' COMMENT '作品所能应用性别',
  `jobId` tinyint(4) NOT NULL DEFAULT '0' COMMENT '作品所能应用职业id',
  `hot` bigint(20) DEFAULT '0' COMMENT '周热度',
  PRIMARY KEY (`id`),
  KEY `workId` (`workId`),
  KEY `userId` (`userId`),
  KEY `week` (`week`,`workId`)
) ENGINE=InnoDB AUTO_INCREMENT=1869 DEFAULT CHARSET=utf8mb4 COMMENT='玩家作品周人气排行';


-- nodejs_yxb.pyq_face_pinch_work definition

CREATE TABLE `pyq_face_pinch_work` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `userId` bigint(20) NOT NULL DEFAULT '0' COMMENT '用户id',
  `roleId` bigint(20) NOT NULL DEFAULT '0' COMMENT '角色id',
  `roleName` varchar(63) DEFAULT NULL COMMENT '角色昵称',
  `account` varchar(255) DEFAULT NULL COMMENT '账号',
  `dataUrl` varchar(255) NOT NULL DEFAULT '' COMMENT '捏脸数据对应的url',
  `image` varchar(255) NOT NULL DEFAULT '' COMMENT '作品大图 游戏内直接传来的一张',
  `title` varchar(32) DEFAULT '' COMMENT '标题',
  `desc` varchar(500) DEFAULT '' COMMENT '描述',
  `shareId` char(21) NOT NULL DEFAULT '' COMMENT '分享Id',
  `gender` tinyint(4) NOT NULL DEFAULT '0' COMMENT '作品所能应用性别',
  `jobId` tinyint(4) NOT NULL DEFAULT '0' COMMENT '作品所能应用职业id',
  `workType` tinyint(4) NOT NULL DEFAULT '0' COMMENT '作品类型 0 => 标准 1 => (不出现在作品列表中, 只是为了生成二维码数据)',
  `visibility` tinyint(4) NOT NULL DEFAULT '0' COMMENT '公开级别， 0 => 公开 1 => 私有',
  `hot` bigint(20) DEFAULT '0' COMMENT '热度',
  `likeCount` bigint(20) NOT NULL DEFAULT '0' COMMENT '点赞数',
  `collectCount` bigint(20) NOT NULL DEFAULT '0' COMMENT '收藏数',
  `applyCount` bigint(20) NOT NULL DEFAULT '0' COMMENT '被使用数',
  `status` tinyint(4) DEFAULT '0' COMMENT '作品状态：0 正常 -1 已删除',
  `auditStatus` tinyint(4) DEFAULT '0' COMMENT '审核状态 0 => 审核中, -1 => 未通过, 1 => 通过',
  `createTime` bigint(20) NOT NULL COMMENT '创建时间',
  `bodyShape` tinyint(4) NOT NULL DEFAULT '0' COMMENT '作品所能应用体型 0=>成男 1=>成女 2=>壮男 3=>萝莉',
  PRIMARY KEY (`id`),
  UNIQUE KEY `shareId` (`shareId`),
  KEY `roleId` (`roleId`),
  KEY `userId` (`userId`),
  KEY `createTime` (`createTime`),
  KEY `hot` (`hot`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13933 DEFAULT CHARSET=utf8 COMMENT='倩女手游捏脸作品';


-- nodejs_yxb.pyq_face_pinch_work_need_name definition

CREATE TABLE `pyq_face_pinch_work_need_name` (
  `workId` bigint(20) NOT NULL COMMENT '作品ID',
  `account` varchar(255) DEFAULT NULL COMMENT '账号',
  PRIMARY KEY (`workId`),
  KEY `account` (`account`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='倩女手游捏脸作品缺失作者表';