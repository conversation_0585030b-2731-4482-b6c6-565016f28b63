"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenericCache = exports.smartMemorize = void 0;
const redisTypes_1 = require("../../common/redisTypes");
const util_1 = require("../../common/util");
const util2_1 = require("../../common/util2");
const logger_1 = require("../logger");
const config_1 = require("./config");
const constants_1 = require("./constants");
const redis_1 = require("./redis");
const logger = (0, logger_1.clazzLogger)("common/cacheUtil");
function smartMemorize(fn, option) {
    const seconds = option.expireSeconds || constants_1.DEFAULT_EXPIRE;
    return function (...args) {
        return __awaiter(this, void 0, void 0, function* () {
            if (config_1.smartMemorizeCfg.disableCache) {
                const refreshValue = yield fn(...args);
                return refreshValue;
            }
            const key = option.keyGen(...args);
            const cacheValueStr = yield (0, redis_1.getRedis)().getAsync(key);
            if ((0, util2_1.isNullOrUndefined)(cacheValueStr)) {
                const newValue = yield fn(...args);
                const newValueStr = JSON.stringify(newValue);
                yield (0, redis_1.getRedis)().setAsync(key, newValueStr, redisTypes_1.ExpireType.EX, seconds);
                return newValue;
            }
            else {
                const cacheValue = (0, util_1.getJsonInfo)(cacheValueStr);
                return cacheValue;
            }
        });
    };
}
exports.smartMemorize = smartMemorize;
class GenericCache {
    get(params, opt) {
        return __awaiter(this, void 0, void 0, function* () {
            const str = yield (0, redis_1.getRedis)().getAsync(this.getKey(params, opt));
            if ((0, util2_1.isNullOrUndefined)(str)) {
                const result = yield this.refresh(params, opt);
                return result;
            }
            else {
                try {
                    const result = JSON.parse(str);
                    return result;
                }
                catch (err) {
                    logger.error({ err, params, opt, str }, "GenericCacheGetParseFailed");
                    return null;
                }
            }
        });
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    getExpireTime(params) {
        return null;
    }
    getWithoutRefresh(params) {
        return __awaiter(this, void 0, void 0, function* () {
            const str = yield (0, redis_1.getRedis)().getAsync(this.getKey(params));
            if ((0, util2_1.isNullOrUndefined)(str)) {
                return null;
            }
            const result = (0, util_1.getJsonInfo)(str, {});
            return result;
        });
    }
    refresh(params, opt) {
        return __awaiter(this, void 0, void 0, function* () {
            const content = yield this.fetchDataSource(params, opt);
            yield this.writeToCache(params, content, opt);
            return content;
        });
    }
    writeToCache(params, content, opt) {
        return __awaiter(this, void 0, void 0, function* () {
            const value = (0, util2_1.isNullOrUndefined)(content) ? "null" : JSON.stringify(content);
            yield (0, redis_1.getRedis)().setAsync(this.getKey(params, opt), value);
            const expire = this.getExpireTime(params);
            if (expire !== null) {
                yield (0, redis_1.getRedis)().expireAsync(this.getKey(params, opt), expire);
            }
        });
    }
    del(params) {
        return __awaiter(this, void 0, void 0, function* () {
            const ret = yield (0, redis_1.getRedis)().delAsync(this.getKey(params));
            return ret;
        });
    }
}
exports.GenericCache = GenericCache;
//# sourceMappingURL=cacheUtil.js.map