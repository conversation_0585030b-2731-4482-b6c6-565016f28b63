import Ajv, { ValidateFunction } from "ajv";
import { JTDDataType } from "ajv/dist/core";
import { errorCodes } from "../errorCodes";
const ajv = new Ajv({
  useDefaults: true, //使用默认值
  coerceTypes: true, //强制类型转换
});

const validateGlobal = new Map<string, ValidateFunction<JTDDataType<any>>>();

export function checkParamsByAjv(params, schema) {
  const schemaStr = JSON.stringify(schema);

  let validate: ValidateFunction<JTDDataType<unknown>>;
  if (validateGlobal.get(schemaStr)) {
    validate = validateGlobal.get(schemaStr);
  } else {
    validate = ajv.compile(schema);
    validateGlobal.set(schemaStr, validate);
  }
  if (validate(params)) {
    return true;
  } else {
    const error = validate.errors[0];
    let msg = error.message;
    if (error.keyword === "enum") {
      msg += ": " + error.params.allowedValues.join(",");
    }
    throw { code: errorCodes.InvalidParams.code, msg: error.instancePath + " " + msg };
  }
}
