/* eslint-disable @typescript-eslint/no-var-requires */
import * as defaultCfg from "./config.all";

let config = Object.assign({}, defaultCfg);

if (process.env.NODE_ENV === "test") {
  const configTest = require("./config.ci");
  config = configTest;
} else {
  config.logCfg.dir = "log";
  config.bunyanPoPoAlertCfg = {
    enable: true,
    level: "error",
    webhookUrl: "https://lhpp-popo-server-test.apps.danlu.netease.com/popo/popo/msg/send",
    secretKey: "tFoyJ/qzNa/020a6EyI8Kw==",
    project: "10",
    biz: "l10-md-local",
    env: "test",
    minNotifyInterval: 60,
    atUids: ["<EMAIL>"],
  };
}

export = config;
