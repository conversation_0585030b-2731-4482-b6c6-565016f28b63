"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SERVER_OFFICIAL_CHANNEL = exports.DEFAULT_MEMORY_EXPIRE_MS = exports.DEFAULT_MOMENT_NOS_DEL_DAY = exports.PlayerMinLevel = exports.ONE_HOUR_SECONDS = exports.ESlientLoveStatus = exports.DEFAULT_EXPIRE = void 0;
exports.DEFAULT_EXPIRE = 5 * 60; // 5 min
var ESlientLoveStatus;
(function (ESlientLoveStatus) {
    /** 无任何暗恋状态  */
    ESlientLoveStatus[ESlientLoveStatus["NoRelation"] = 0] = "NoRelation";
    /** 暗恋中  */
    ESlientLoveStatus[ESlientLoveStatus["Loving"] = 1] = "Loving";
    /** 互相暗恋 */
    ESlientLoveStatus[ESlientLoveStatus["LovingEach"] = 2] = "LovingEach";
})(ESlientLoveStatus = exports.ESlientLoveStatus || (exports.ESlientLoveStatus = {}));
exports.ONE_HOUR_SECONDS = 3600; // 1hour
exports.PlayerMinLevel = 1;
exports.DEFAULT_MOMENT_NOS_DEL_DAY = 30;
exports.DEFAULT_MEMORY_EXPIRE_MS = 2 * 60 * 1000; // 2min
exports.SERVER_OFFICIAL_CHANNEL = "网易";
//# sourceMappingURL=constants.js.map