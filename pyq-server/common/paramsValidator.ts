/* eslint-disable @typescript-eslint/no-this-alias */
import * as _ from "lodash";
import { csvStrToArray } from "../../common/util";

const WholeRules = {
  RequiredAny: "RequiredAny",
};

const REGEXP_TPYE = {
  url: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_+.~#?&//=]*)/,
};

export const PARAM_TYPES = {
  JSON_ARRAY: "JSON_ARRAY",
  CSV_ARRAY: "CSV_ARRAY",
};

function formatString(str) {
  return _.trim(str);
}

export class ParamsValidator {
  private params;
  private singlePropRules;
  private wholeRules;

  constructor(params) {
    this.params = params;
    this.singlePropRules = {};
    this.wholeRules = {};
  }

  static from(params) {
    return new ParamsValidator(params);
  }

  param(prop: string, rule?) {
    let required = true;
    // eslint-disable-next-line no-prototype-builtins
    if (rule && rule.hasOwnProperty("default")) {
      required = false;
    }
    const curRule = { type: String, required: required };
    _.merge(curRule, rule);
    this.singlePropRules[prop] = this.singlePropRules[prop] || [];
    this.singlePropRules[prop].push(curRule);
    return this;
  }

  requiredAny(propList) {
    this.wholeRules[WholeRules.RequiredAny] = propList;
    return this;
  }

  private handleWholeRules() {
    const self = this;
    return new Promise(function (resolve, reject) {
      _.forEach(self.wholeRules, function (value, ruleKey) {
        if (ruleKey === WholeRules.RequiredAny) {
          const notContainsAnyProps = _.isEmpty(
            _.intersection(
              _.keys(
                _.pickBy(self.params, function (val) {
                  return !_.isEmpty(formatString(val));
                })
              ),
              value
            )
          );
          if (notContainsAnyProps) {
            reject({ errorType: "ParamRequired", needOneOfParams: value });
          }
        }
      });
      resolve(true);
    });
  }

  private validateStringType(resolve, reject, checkProp, checkValue, rule) {
    if (rule.maxlen && checkValue.length > rule.maxlen) {
      reject({ errorType: "ParamTooLong", param: checkProp, maxlen: rule.maxlen, paramValue: checkValue });
    }
    if (rule.minlen && checkValue.length < rule.minlen) {
      reject({ errorType: "ParamTooShort", param: checkProp, minlen: rule.minlen, paramValue: checkValue });
    }
    if (rule.startsWith && !checkValue.startsWith(rule.startsWith)) {
      reject({ errorType: "ParamNotMatch", param: checkProp, mustStartsWith: rule.startsWith });
    }
    if (rule.textType && rule.textType === "url") {
      if (!REGEXP_TPYE.url.test(checkValue)) {
        reject({ errorType: "ParamNotUrl", param: checkProp, value: checkValue });
      }
    }
    if (rule.values) {
      if (!_.includes(rule.values, checkValue)) {
        reject({ errorType: "ParamInvalid", param: checkProp, permitValues: rule.values });
      }
    }
  }

  private validateNumberType(resolve, reject, checkProp, checkValue, rule, context) {
    if (checkValue) {
      const parseValue = parseInt(checkValue);
      if (Number.isNaN(parseValue)) {
        reject({ errorType: "ParamIsNotNumber", param: checkProp, paramValue: checkValue });
      } else {
        context.params[checkProp] = checkValue = parseValue;
      }
    }
    if (_.isNumber(rule.min) && checkValue < rule.min) {
      reject({ errorType: "ParamTooMin", param: checkProp, minValue: rule.min });
    }
    if (_.isNumber(rule.max) && checkValue > rule.max) {
      reject({ errorType: "ParamTooMax", param: checkProp, maxValue: rule.max });
    }

    if (rule.values) {
      if (!_.includes(rule.values, checkValue)) {
        reject({ errorType: "ParamInvalid", param: checkProp, permitValues: rule.values });
      }
    }
  }

  private validateArrayType(resolve, reject, checkProp, checkValue, rule, context) {
    checkValue = _.filter(checkValue, (s) => !!_.trim(s));
    const isSubSet = function (arr1, arr2) {
      return arr1.length === _.intersection(arr1, arr2).length;
    };
    if (!_.isArray(checkValue)) {
      reject({ errorType: "ParamTypeArray", param: checkProp, needType: "Array" });
    }
    if (rule.maxSize && checkValue.length > rule.maxSize) {
      reject({ errorType: "ParamArraySizeTooLarge", param: checkProp, maxSize: rule.maxSize });
    }
    if (rule.minSize && checkValue.length < rule.minSize) {
      reject({ errorType: "ParamArraySizeTooSmall", param: checkProp, minSize: rule.minSize });
    }
    if (rule.subSet && !isSubSet(checkValue, rule.subSet)) {
      reject({ errorType: "ParamInvalid", param: checkProp, mustSubSet: rule.subSet });
    }
    if (rule.textType && rule.textType === "url") {
      const isUrl = _.every(checkValue, function (value) {
        return REGEXP_TPYE.url.test(value);
      });
      if (!isUrl) {
        reject({ errorType: "ParamNotUrl", param: checkProp, value: checkValue });
      }
    }
    if (rule.each) {
      const ruleForEach = rule.each;
      if (ruleForEach.type === Number) {
        context.params[checkProp] = _.map(checkValue, function (item) {
          return parseInt(item, 10);
        });
      }
    }
  }

  private validateJsonArrayType(resolve, reject, checkProp, checkValue, rule, context) {
    let newCheckValue;
    try {
      newCheckValue = JSON.parse(checkValue);
      context.params[checkProp] = newCheckValue;
    } catch (err) {
      reject({ errorType: "ParamNotJson", param: checkProp });
    }
    if (!_.isArray(newCheckValue)) {
      reject({ errorType: "ParamNotJsonArray", param: checkProp });
    } else {
      this.validateArrayType(resolve, reject, checkProp, newCheckValue, rule, context);
    }
  }

  private validateCsvArrayType(resolve, reject, checkProp, checkValue, rule, context) {
    let newCheckValue;
    newCheckValue = csvStrToArray(checkValue);
    context.params[checkProp] = newCheckValue;
    if (!_.isArray(newCheckValue)) {
      reject({ errorType: "ParamNotCsvArray", param: checkProp });
    } else {
      this.validateArrayType(resolve, reject, checkProp, newCheckValue, rule, context);
    }
  }

  private handleSingleRules() {
    const self = this;
    let checkValue;

    return new Promise(function (resolve, reject) {
      _.forEach(self.singlePropRules, function (rules, checkProp) {
        _.forEach(rules, function (rule) {
          checkValue = self.params[checkProp];
          if (_.isString(checkValue)) {
            checkValue = formatString(checkValue);
            self.params[checkProp] = checkValue;
          }
          if (!self.params[checkProp] && rule.default !== undefined) {
            self.params[checkProp] = rule.default;
          }
          if (checkValue !== undefined && checkValue !== null && checkValue !== "") {
            if (rule.type === String) {
              self.validateStringType(resolve, reject, checkProp, checkValue, rule);
            } else if (rule.type === Number || rule.type === "integer") {
              self.validateNumberType(resolve, reject, checkProp, checkValue, rule, self);
            } else if (rule.type === Array) {
              self.validateArrayType(resolve, reject, checkProp, checkValue, rule, self);
            } else if (rule.type === PARAM_TYPES.JSON_ARRAY) {
              self.validateJsonArrayType(resolve, reject, checkProp, checkValue, rule, self);
            } else if (rule.type === PARAM_TYPES.CSV_ARRAY) {
              self.validateCsvArrayType(resolve, reject, checkProp, checkValue, rule, self);
            }
          } else {
            if (rule.required) {
              reject({ errorType: "ParamRequired", param: checkProp });
            }
          }
        });
      });
      resolve(true);
    });
  }

  validate() {
    const self = this;
    return self.handleWholeRules().then(function () {
      return self.handleSingleRules();
    });
  }
}
