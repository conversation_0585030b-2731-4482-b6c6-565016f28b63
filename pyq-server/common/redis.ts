/* eslint-disable @typescript-eslint/no-this-alias */
/* eslint-disable prefer-rest-params */
import * as redis from "redis";
import { ExtendRedisClient } from "../../common/redis";
import * as bluebird from "bluebird";
import { IRedisCfg } from "../../common/type";
import * as _ from "lodash";
import { apiLogger as logger } from "../logger";
import * as L10Config from "../common/config";

const redisCfg = L10Config.redisCfg;

bluebird.promisifyAll(redis.RedisClient.prototype, {
  promisifier: promisifier,
});

bluebird.promisifyAll(redis.Multi.prototype, {
  promisifier: promisifier,
});

function promisifier(originalMethod) {
  return function promisified() {
    const args = [].slice.call(arguments);
    const self = this;
    return new Promise(function (resolve, reject) {
      args.push(function (err, data) {
        if (err) {
          reject(err);
        } else {
          resolve(data);
        }
      });
      originalMethod.apply(self, args);
    }).catch((err) => {
      const debugArgs = args.filter((ele) => !_.isFunction(ele));
      logger.error("RedisCommendError", { method: originalMethod.name, args: debugArgs });
      throw err;
    });
  };
}

export function getRedisClient(config: IRedisCfg) {
  const client = redis.createClient(config) as ExtendRedisClient;
  return client;
}

class RedisClass {
  private instance: ExtendRedisClient;
  private redisConfig: IRedisCfg;

  constructor(config: IRedisCfg) {
    this.instance = null;
    this.redisConfig = config;
  }

  retry_strategy() {
    this.instance = null;
  }

  getInstance() {
    const redisConfig = _.assign({}, this.redisConfig, { retry_strategy: this.retry_strategy.bind(this) });
    if (this.instance === null) {
      this.instance = getRedisClient(redisConfig);
    }
    return this.instance;
  }
}

class RedisPool {
  private pool: RedisClass[];

  constructor(config: IRedisCfg) {
    this.pool = [];
    const hosts = config.hosts || [config.host];
    for (const h of hosts) {
      const cfg: IRedisCfg = Object.assign({}, config, { host: h });
      const rc = new RedisClass(cfg);
      this.pool.push(rc);
    }
  }

  getInstance() {
    const idx = _.random(0, this.pool.length - 1);
    return this.pool[idx].getInstance();
  }
}

const redisPool = new RedisPool(redisCfg);

export function getRedis() {
  return redisPool.getInstance();
}
