import { isIn64SpecialPeriod } from "../../common/helper"
import textRegList = require("../../service/qnm/data/TextRegList")
import textRegListFor64 = require("../../service/qnm/data/TextRegListFor64")

/**
 * 限制恶意构造重复文本通过卡死正则
 * example "aaabbbccc" => "aabbcc"
 */
function fixTooLongRepeat(str: string) {
  let repeat = 1
  let newStr = ""
  let preChar = ""
  for (const c of str) {
    if (preChar === c) {
      repeat += 1
    } else {
      repeat = 1
    }
    if (repeat <= 2) {
      newStr += c
    }
    preChar = c
  }
  return newStr
}

export function textFilterByRegList(str: string, regStrList: string[]) {
  const checkStr = getCleanText(str)
  if (!checkStr) {
    return null
  }
  for (let i = 0, l = regStrList.length; i < l; i++) {
    const item = regStrList[i]
    const expr = item[0]
    const flag = item[1]
    const regex = new RegExp(expr, flag)
    if (regex.test(str)) {
      return { sensitive: true, matchIdx: i }
    }
  }
  return null
}
function getCleanText(str: string) {
  let checkStr = str.trim()
  checkStr = str.replace(/<link\s(.+?)=(.+?)>/gi, "") // 链接不参与不过滤
  checkStr = fixTooLongRepeat(str)
  return checkStr
}

export function sensitiveCheckForText(text: string, date = new Date()) {
  if (isIn64SpecialPeriod(date)) {
    const checkFor64 = textFilterByRegList(text, textRegListFor64)
    if (checkFor64 && checkFor64.sensitive) {
      return checkFor64
    }
  }
  return textFilterByRegList(text, textRegList)
}

export function sensitiveCheckForTextList(texts: string[], date = new Date()): boolean {
  return texts.some((text) => sensitiveCheckForText(text, date))
}