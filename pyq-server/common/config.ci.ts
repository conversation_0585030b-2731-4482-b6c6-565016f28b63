import * as defaultCfg from "./config.all";

const config = Object.assign({}, defaultCfg);

config.logCfg.yunyingdir = 'log'
config.logCfg.dir = "log"
config.testCfg.skip_skey_check_full = true
config.testCfg.skip_token_check = true;

config.Features.slientLove = true
config.Features.thisDayThatYear = true

config.logCfg.printInConsole = process.env.LOG_CONSOLE === "true"
config.yunyingLog.printInConsole = config.logCfg.printInConsole
config.lbsCfg.openNearby = true

if (config.bunyanPoPoAlertCfg) {
  config.bunyanPoPoAlertCfg.enable = false
}

if (config.sendPicBufferCfg) {
  config.sendPicBufferCfg.enable = true
  config.sendPicBufferCfg.buffersize = 1
  config.sendPicBufferCfg.syncInterval = 20
}

export = config;