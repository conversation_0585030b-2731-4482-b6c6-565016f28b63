import { ExpireType } from "../../common/redisTypes";
import { getJsonInfo } from "../../common/util";
import { isNullOrUndefined } from "../../common/util2";
import { clazzLogger } from "../logger";
import { smartMemorizeCfg } from "./config";
import { DEFAULT_EXPIRE } from "./constants";
import { getRedis } from "./redis";
const logger = clazzLogger("common/cacheUtil");

export function smartMemorize<A extends unknown[], R>(
  fn: (...a: A) => Promise<R>,
  option: {
    /** generate key for memorize */
    keyGen: (...k: A) => string;
    /** expire seconds*/
    expireSeconds?: number;
  }
) {
  const seconds = option.expireSeconds || DEFAULT_EXPIRE;
  return async function (...args: A): Promise<R> {
    if (smartMemorizeCfg.disableCache) {
      const refreshValue = await fn(...args);
      return refreshValue;
    }
    const key = option.keyGen(...args);
    const cacheValueStr = await getRedis().getAsync(key);
    if (isNullOrUndefined(cacheValueStr)) {
      const newValue = await fn(...args);
      const newValueStr = JSON.stringify(newValue);
      await getRedis().setAsync(key, newValueStr, ExpireType.EX, seconds);
      return newValue;
    } else {
      const cacheValue = getJsonInfo<R>(cacheValueStr);
      return cacheValue;
    }
  };
}

export abstract class GenericCache<T, F> {
  public async get(params: T, opt?): Promise<F> {
    const str = await getRedis().getAsync(this.getKey(params, opt));
    if (isNullOrUndefined(str)) {
      const result = await this.refresh(params, opt);
      return result;
    } else {
      try {
        const result: F = JSON.parse(str);
        return result;
      } catch (err) {
        logger.error({ err, params, opt, str }, "GenericCacheGetParseFailed");
        return null;
      }
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  getExpireTime(params: T) {
    return null;
  }

  async getWithoutRefresh(params: Partial<T>) {
    const str = await getRedis().getAsync(this.getKey(params));
    if (isNullOrUndefined(str)) {
      return null;
    }
    const result = getJsonInfo<F>(str, {} as unknown as F);
    return result;
  }

  abstract getKey(params: Partial<T>, opt?): string;

  abstract fetchDataSource(params: T, opt?): Promise<F>;

  async refresh(params: T, opt?): Promise<F> {
    const content = await this.fetchDataSource(params, opt);
    await this.writeToCache(params, content, opt);
    return content;
  }

  protected async writeToCache(params: T, content: F, opt?) {
    const value = isNullOrUndefined(content) ? "null" : JSON.stringify(content);
    await getRedis().setAsync(this.getKey(params, opt), value);
    const expire = this.getExpireTime(params);
    if (expire !== null) {
      await getRedis().expireAsync(this.getKey(params, opt), expire);
    }
  }

  async del(params: T) {
    const ret = await getRedis().delAsync(this.getKey(params));
    return ret;
  }
}
