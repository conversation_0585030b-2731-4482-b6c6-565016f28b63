"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkParamsByAjv = void 0;
const ajv_1 = require("ajv");
const errorCodes_1 = require("../errorCodes");
const ajv = new ajv_1.default({
    useDefaults: true,
    coerceTypes: true, //强制类型转换
});
const validateGlobal = new Map();
function checkParamsByAjv(params, schema) {
    const schemaStr = JSON.stringify(schema);
    let validate;
    if (validateGlobal.get(schemaStr)) {
        validate = validateGlobal.get(schemaStr);
    }
    else {
        validate = ajv.compile(schema);
        validateGlobal.set(schemaStr, validate);
    }
    if (validate(params)) {
        return true;
    }
    else {
        const error = validate.errors[0];
        let msg = error.message;
        if (error.keyword === "enum") {
            msg += ": " + error.params.allowedValues.join(",");
        }
        throw { code: errorCodes_1.errorCodes.InvalidParams.code, msg: error.instancePath + " " + msg };
    }
}
exports.checkParamsByAjv = checkParamsByAjv;
//# sourceMappingURL=ajvCheck.js.map