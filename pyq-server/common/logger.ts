import * as <PERSON><PERSON>yan from "bunyan";
import * as fs from "fs";
import * as path from "path";
import * as _ from "lodash";
import { bunyanPoPoAlertCfg, logCfg } from "./config";
import { formatDate } from "../../common/util";
import { BunyanPoPoAlertStream } from "../../common/bunyanPoPoAlert";

const logPath = (fileName) => path.join(logCfg.dir, fileName);

function FileStream(option) {
  this.name = option.name;
}

FileStream.prototype.write = function (srcRec) {
  const rec = { ...srcRec };
  const fullName = this.name + ".log";
  rec.schema = logCfg.schema;
  rec.ts = formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
  rec.env = logCfg.env;
  fs.appendFile(logPath(fullName), JSON.stringify(rec) + "\n", (err) => {
    if (err) {
      console.error("FileStreamWriteError", err);
    }
  });
};

export function createLogger(params) {
  params = _.defaultsDeep(params, {
    serializers: {
      req: Bunyan.stdSerializers.req,
      res: Bunyan.stdSerializers.res,
      err: Bunyan.stdSerializers.err,
    },
  });
  return new Bunyan(params);
}

export function getLogger(name: string, level?: Bunyan.LogLevel) {
  const streams = [
    {
      level: level || logCfg.level || "info",
      type: "raw",
      stream: new FileStream({ name: name }),
    },
    {
      level: "error",
      type: "raw",
      stream: new FileStream({ name: name + "_error" }),
    },
  ];

  if (logCfg.printInConsole && process.env.NODE_ENV !== "production") {
    streams.push({
      level: "debug",
      type: null,
      stream: process.stdout,
    });
  }

  if (bunyanPoPoAlertCfg.enable) {
    const cfg = bunyanPoPoAlertCfg;
    let atUids = "";
    if (cfg.atUids) {
      atUids = cfg.atUids.join(",");
    }
    streams.push({
      level: cfg.level || "error",
      type: "raw",
      stream: new BunyanPoPoAlertStream({
        webhookUrl: cfg.webhookUrl,
        secretKey: cfg.secretKey,
        project: cfg.project,
        biz: cfg.biz,
        env: cfg.env,
        minNotifyInterval: cfg.minNotifyInterval,
        atUids,
        onError: (err) => {
          console.error(err);
        },
      }),
    });
  }

  return createLogger({
    name: name,
    streams: streams,
  });
}
