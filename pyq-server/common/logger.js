"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getLogger = exports.createLogger = void 0;
const Bunyan = require("bunyan");
const fs = require("fs");
const path = require("path");
const _ = require("lodash");
const config_1 = require("./config");
const util_1 = require("../../common/util");
const bunyanPoPoAlert_1 = require("../../common/bunyanPoPoAlert");
const logPath = (fileName) => path.join(config_1.logCfg.dir, fileName);
function FileStream(option) {
    this.name = option.name;
}
FileStream.prototype.write = function (srcRec) {
    const rec = Object.assign({}, srcRec);
    const fullName = this.name + ".log";
    rec.schema = config_1.logCfg.schema;
    rec.ts = (0, util_1.formatDate)(new Date(), "yyyy-MM-dd HH:mm:ss");
    rec.env = config_1.logCfg.env;
    fs.appendFile(logPath(fullName), JSON.stringify(rec) + "\n", (err) => {
        if (err) {
            console.error("FileStreamWriteError", err);
        }
    });
};
function createLogger(params) {
    params = _.defaultsDeep(params, {
        serializers: {
            req: Bunyan.stdSerializers.req,
            res: Bunyan.stdSerializers.res,
            err: Bunyan.stdSerializers.err,
        },
    });
    return new Bunyan(params);
}
exports.createLogger = createLogger;
function getLogger(name, level) {
    const streams = [
        {
            level: level || config_1.logCfg.level || "info",
            type: "raw",
            stream: new FileStream({ name: name }),
        },
        {
            level: "error",
            type: "raw",
            stream: new FileStream({ name: name + "_error" }),
        },
    ];
    if (config_1.logCfg.printInConsole && process.env.NODE_ENV !== "production") {
        streams.push({
            level: "debug",
            type: null,
            stream: process.stdout,
        });
    }
    if (config_1.bunyanPoPoAlertCfg.enable) {
        const cfg = config_1.bunyanPoPoAlertCfg;
        let atUids = "";
        if (cfg.atUids) {
            atUids = cfg.atUids.join(",");
        }
        streams.push({
            level: cfg.level || "error",
            type: "raw",
            stream: new bunyanPoPoAlert_1.BunyanPoPoAlertStream({
                webhookUrl: cfg.webhookUrl,
                secretKey: cfg.secretKey,
                project: cfg.project,
                biz: cfg.biz,
                env: cfg.env,
                minNotifyInterval: cfg.minNotifyInterval,
                atUids,
                onError: (err) => {
                    console.error(err);
                },
            }),
        });
    }
    return createLogger({
        name: name,
        streams: streams,
    });
}
exports.getLogger = getLogger;
//# sourceMappingURL=logger.js.map