"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getYunyingLogger = exports.unlinkLog = void 0;
const fs = require("fs");
const path = require("path");
const config = require("./config");
const moment = require("moment");
const lodash_1 = require("lodash");
const config_1 = require("../../common/config");
const LogConfig = config.yunyingLog;
const LogDir = LogConfig.dir || path.join(__dirname, "../../log");
const logPath = (fileName) => path.join(LogDir, fileName);
function getDayStr() {
    return moment().format("YYYY-MM-DD HH:mm:ss");
}
function getLogPath(time) {
    const date = moment(time).format('YYYY-MM-DD');
    const logName = `${LogConfig.prefix}_yunying_${date}.log`;
    return logPath(logName);
}
function filterYunyingLog(strs) {
    return (0, lodash_1.filter)(strs, (str) => {
        if (!str.endsWith(".log") || !str.startsWith(`${LogConfig.prefix}_yunying`)) {
            return false;
        }
        const parts = str.replace(".log", "").split("_");
        return parts.length == 4;
    });
}
function getLogTime(str) {
    const parts = str.replace(".log", "").split("_");
    const timeStr = parts[parts.length - 1];
    const numParts = timeStr.split("-");
    if (numParts.length !== 4 || numParts.some((item) => Number.isNaN(item))) {
        return Date.now();
    }
    const nums = numParts.map((item) => Number(item));
    return new Date(nums[0], nums[1] - 1, nums[2], nums[3]);
}
function yunyingFormat(tag, rec) {
    return `[${getDayStr()}][${tag}],` + JSON.stringify(rec) + "\n";
}
class FileStreamOfYunying {
    constructor() { }
    log(tag, rec) {
        const line = yunyingFormat(tag, rec);
        if (LogConfig.printInConsole) {
            console.log(line.replace(/(\[\S+\])/, "\x1b[31m$1\x1b[0m"));
        }
        fs.appendFile(getLogPath(Date.now()), line, (err) => {
            if (err) {
                console.error("FileStreamWriteError", err);
            }
        });
        return;
    }
}
function unlinkLog() {
    const filenames = fs.readdirSync(LogDir);
    const yunyingFiles = filterYunyingLog(filenames);
    const now = Date.now();
    for (const filename of yunyingFiles) {
        const logTime = getLogTime(filename);
        const diff = now - logTime.valueOf();
        if (diff >= LogConfig.unlinkIntervalDays * config_1.ONE_DAY_SECONDS * 1000) {
            const realPath = logPath(filename);
            if (fs.existsSync(realPath)) {
                fs.unlinkSync(realPath);
            }
        }
    }
}
exports.unlinkLog = unlinkLog;
function getYunyingLogger() {
    return new FileStreamOfYunying();
}
exports.getYunyingLogger = getYunyingLogger;
//# sourceMappingURL=yunyingLogger.js.map