"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getRedis = exports.getRedisClient = void 0;
/* eslint-disable @typescript-eslint/no-this-alias */
/* eslint-disable prefer-rest-params */
const redis = require("redis");
const bluebird = require("bluebird");
const _ = require("lodash");
const logger_1 = require("../logger");
const L10Config = require("../common/config");
const redisCfg = L10Config.redisCfg;
bluebird.promisifyAll(redis.RedisClient.prototype, {
    promisifier: promisifier,
});
bluebird.promisifyAll(redis.Multi.prototype, {
    promisifier: promisifier,
});
function promisifier(originalMethod) {
    return function promisified() {
        const args = [].slice.call(arguments);
        const self = this;
        return new Promise(function (resolve, reject) {
            args.push(function (err, data) {
                if (err) {
                    reject(err);
                }
                else {
                    resolve(data);
                }
            });
            originalMethod.apply(self, args);
        }).catch((err) => {
            const debugArgs = args.filter((ele) => !_.isFunction(ele));
            logger_1.apiLogger.error("RedisCommendError", { method: originalMethod.name, args: debugArgs });
            throw err;
        });
    };
}
function getRedisClient(config) {
    const client = redis.createClient(config);
    return client;
}
exports.getRedisClient = getRedisClient;
class RedisClass {
    constructor(config) {
        this.instance = null;
        this.redisConfig = config;
    }
    retry_strategy() {
        this.instance = null;
    }
    getInstance() {
        const redisConfig = _.assign({}, this.redisConfig, { retry_strategy: this.retry_strategy.bind(this) });
        if (this.instance === null) {
            this.instance = getRedisClient(redisConfig);
        }
        return this.instance;
    }
}
class RedisPool {
    constructor(config) {
        this.pool = [];
        const hosts = config.hosts || [config.host];
        for (const h of hosts) {
            const cfg = Object.assign({}, config, { host: h });
            const rc = new RedisClass(cfg);
            this.pool.push(rc);
        }
    }
    getInstance() {
        const idx = _.random(0, this.pool.length - 1);
        return this.pool[idx].getInstance();
    }
}
const redisPool = new RedisPool(redisCfg);
function getRedis() {
    return redisPool.getInstance();
}
exports.getRedis = getRedis;
//# sourceMappingURL=redis.js.map