/* eslint-disable @typescript-eslint/no-var-requires */
import * as _ from "lodash";
const huyitian = require("./huyitianfans");
const GoodVoiceMatch = require("./goodVoiceMatch");
const HouseDesignMatch = require("./houseDesignMatch");
const FengyunGang2018 = require("./fengyunGang2018");
import { enroll as mingRenEnroll } from "./mingren";
import { HouseDesignMatch2019Activity } from "./houseDesignMatch2019";
import { jiaNianHua2019ShareMoment } from "./simpleActivities";
import { qyfhShareToMd } from "./qianYingFengHua";
import { ActivityErrors } from "../errorCodes";
import { qyfhCfg } from "../common/config";

const EventMap = {
  huyitianfanEnroll: huyitian.enroll,
  goodVoiceEnroll: GoodVoiceMatch.enroll,
  houseDesignMatch: HouseDesignMatch.enroll,
  fengyunGang2018: FengyunGang2018.enroll,
  mingRen: mingRenEnroll,
  jianianhua2019: jiaNianHua2019ShareMoment,
};

function getEvents() {
  return _.keys(EventMap);
}

export function dispatch(name, payload) {
  return EventMap[name](payload);
}

function initActivityListen() {}

initActivityListen();

export const Events = getEvents();

export function dispatchActivityShare(req, res, next) {
  const activity: string = req.params.activity || "";
  if (activity === qyfhCfg.name) {
    qyfhShareToMd(req, res, next);
  } else {
    res.send(ActivityErrors.ActivityUnDefined);
  }
}
