import { isBefore } from '../../common/dateUtil'
import * as PyqMoments from '../../models/PyqMoments'
import { getRedis, IExistResult } from '../../common/redis'
import { Flower } from '../models/flower'
import * as ParamsValidator from '../../common/paramsValidator'

export interface WorkInfo {
  status: string
  roleId: number
  workId: number
  workName: string
  urls: string[]
}

enum AuditStatus {
  PASS = 1,
  REJECT = 2
}

interface IActivity {
  name: string
  endDate: Date
  isCountFlowerRenQi: boolean
  isCountFlowerVote: boolean
}

export abstract class Activity {
  private attendSetKey: string
  public name: string
  private endDate: Date
  public isCountFlowerRenQi: boolean
  public isCountFlowerVote: boolean

  constructor(option: IActivity) {
    this.name = option.name
    this.endDate = option.endDate
    this.attendSetKey = 'qnm:' + this.name + ':attend_player_ids'
    this.isCountFlowerRenQi = option.isCountFlowerRenQi
    this.isCountFlowerVote = option.isCountFlowerVote
  }

  isOpen(): boolean {
    return isBefore(new Date(), this.endDate)
  }

  protected abstract getPreviewUrl(workId: number): string

  protected abstract getShareText(workInfo: WorkInfo): string

  protected getValidator(workInfo: WorkInfo) {
    let validator = ParamsValidator.from(workInfo)
      .param('status', { type: Number, values: [AuditStatus.PASS, AuditStatus.REJECT] })
      .param('roleId', { type: Number })
      .param('workId', { type: Number })
      .param('workName', { type: String, required: false })
      .param('urls', { type: Array, required: false })
    return validator
  }


  async checkParams(workInfo: WorkInfo) {
    let result = await this.getValidator(workInfo).validate()
    return result
  }

  async addShareMoment(workInfo: WorkInfo) {
    let isOpen = await this.isOpen()
    if (!isOpen) return
    await this.checkParams(workInfo)
    let roleId = workInfo.roleId
    let urls = workInfo.urls || []
    let imgList = urls.join(',')
    let imgAudit = (new Array(urls.length)).fill(1).join(',')
    let insertInfo = await PyqMoments.insert({
      RoleId: roleId,
      ImgList: imgList,
      imgAudit: imgAudit,
      Text: this.getShareText(workInfo),
      CreateTime: Date.now()
    })
    await this.attend(workInfo.roleId)
    return insertInfo.insertId
  }

  private async attend(roleId: number) {
    return getRedis().saddAsync(this.attendSetKey, '' + roleId)
  }

  async isAttend(roleId: number): Promise<boolean> {
    let result = await getRedis().sismemberAsync(this.attendSetKey, '' + roleId)
    return result === IExistResult.Exist
  }
}

const flowerIdToVote = {
  21100029: 1, // 玫瑰
  21100030: 10, // 绚丽玫瑰
  21100031: 220, // 以爱之名
  21101865: 280 // 知己之名
}

export class FlowerRenQiCounter {
  private renqiKey: string
  private voteKey: string

  constructor(private activity: Activity) {
    this.renqiKey = 'qnm:' + activity.name + 'flower_renqi'
    this.voteKey = 'qnm:' + activity.name + 'flower_vote'
  }

  async incr(roleId: number, flower: Flower): Promise<{ renqi: number, vote: number }> {
    let isAttend = await this.activity.isAttend(roleId)
    let renqi = null
    let vote = null
    if (isAttend) {
      if (this.activity.isCountFlowerRenQi) {
        renqi = await getRedis().hincrbyAsync(this.renqiKey, '' + roleId, flower.renqi)
      }
      if (this.activity.isCountFlowerVote) {
        let incr = flowerIdToVote[flower.id] * flower.count
        vote = await getRedis().hincrbyAsync(this.voteKey, '' + roleId, incr)
      }
    }
    return { renqi, vote }
  }
}