"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.onAttendTopic = exports.TOPIC_NAME = void 0;
const logger2_1 = require("../../common/logger2");
const PyqActivityTopic_1 = require("../../models/PyqActivityTopic");
const Moment = require("../../models/PyqMoments");
const util_1 = require("../../common/util");
const httpLib = require("../../common/request");
const util2_1 = require("../../common/util2");
const activityHelper_1 = require("./activityHelper");
const activityTools_1 = require("./activityTools");
let logger = (0, logger2_1.getLogger)('l10_flute');
exports.TOPIC_NAME = '解曲知音';
const ATTEND_MIN_VIDEO_NUM = 1;
const APPLY_API = activityHelper_1.API_HOST + '/public/share/common_activity_vote/qnm/flute2019/server/apply/chusai';
function onAttendTopic(params) {
    return __awaiter(this, void 0, void 0, function* () {
        let name = yield PyqActivityTopic_1.ActivityTopic.getNameById(params.topicId);
        if (name === exports.TOPIC_NAME) {
            return attendByMomentId(params.momentId);
        }
    });
}
exports.onAttendTopic = onAttendTopic;
function attendByMomentId(momentId) {
    return __awaiter(this, void 0, void 0, function* () {
        let record = yield Moment.findById(momentId, ['Text', 'VideoList', 'RoleId']);
        logger.info('attendByMomentId', record);
        if (record) {
            let videoList = (0, util_1.csvStrToArray)(record.VideoList);
            if (videoList.length >= ATTEND_MIN_VIDEO_NUM && !(0, activityTools_1.isOnlyContainTopic)(record.Text)) {
                let text = (0, activityTools_1.removeTopicLink)(record.Text);
                return requestAttendApi(record.RoleId, videoList, text);
            }
        }
    });
}
function requestAttendApi(roleId, videos, intr) {
    return __awaiter(this, void 0, void 0, function* () {
        videos = videos.map(url => (0, util2_1.replaceHttps)(url));
        let data = { roleid: roleId, worksurl: videos, mark: intr, source: activityHelper_1.MD_SOURCE };
        logger.info('Prepare attend', data);
        let result = yield httpLib.request({
            method: "POST",
            url: APPLY_API,
            body: data
        });
        logger.info('Request apply api result', { result: result, data: data });
        return result;
    });
}
//# sourceMappingURL=flute.js.map