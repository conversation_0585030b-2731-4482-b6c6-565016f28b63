"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getProxiedUrl = exports.getActivityLogger = exports.removeTopicLink = exports.isOnlyContainTopic = exports.isInDateRange = void 0;
const DateUtil = require("../../common/dateUtil");
const logger2_1 = require("../../common/logger2");
const forwardProxy_1 = require("../services/forwardProxy");
function isInDateRange(date, dr) {
    return DateUtil.isWithInRange(date, new Date(dr.startDate), new Date(dr.endDate));
}
exports.isInDateRange = isInDateRange;
const LINK_REGEX = /^<link\sbutton=.*>$/;
function isOnlyContainTopic(text) {
    if (text) {
        return text.match(LINK_REGEX);
    }
    else {
        return false;
    }
}
exports.isOnlyContainTopic = isOnlyContainTopic;
const TOPIC_REGEX = /<link\sbutton=.*>/g;
function removeTopicLink(text) {
    return text.replace(TOPIC_REGEX, "");
}
exports.removeTopicLink = removeTopicLink;
let logger = (0, logger2_1.getLogger)("l10_activity");
function getActivityLogger(name) {
    return logger.child({ activity: name });
}
exports.getActivityLogger = getActivityLogger;
function getProxiedUrl(url) {
    return (0, forwardProxy_1.transToForwardProxyUrl)(url, forwardProxy_1.ForwardProxy);
}
exports.getProxiedUrl = getProxiedUrl;
//# sourceMappingURL=activityTools.js.map