import * as DateUtil from "../../common/dateUtil";
import { Flower } from "../models/flower";
import { getLogger } from "../../common/logger2";
import { transToForwardProxyUrl, ForwardProxy } from "../services/forwardProxy";

export interface DateRange {
  startDate: string;
  endDate: string;
}

export function isInDateRange(date: Date, dr: DateRange) {
  return DateUtil.isWithInRange(date, new Date(dr.startDate), new Date(dr.endDate));
}

export interface SendFlowerPayload {
  sender: number;
  receiver: number;
  flower: Flower;
}

const LINK_REGEX = /^<link\sbutton=.*>$/;

export function isOnlyContainTopic(text: string) {
  if (text) {
    return text.match(LINK_REGEX);
  } else {
    return false;
  }
}

const TOPIC_REGEX = /<link\sbutton=.*>/g;

export function removeTopicLink(text: string) {
  return text.replace(TOPIC_REGEX, "");
}

let logger = getLogger("l10_activity");

export function getActivityLogger(name: string) {
  return logger.child({ activity: name });
}

export function getProxiedUrl(url: string): string {
  return transToForwardProxyUrl(url, ForwardProxy);
}
