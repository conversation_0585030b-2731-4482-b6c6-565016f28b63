"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.mostLikedMomentHandler = exports.recentHelpHandler = exports.wishHelpRankHandler = exports.getRecentHelps = exports.getRank = exports.onWishHelpFinish = exports.getHelpSum = exports.onAddWishShareNDZJ2020Moment = exports.getShareText = void 0;
const _ = require("lodash");
const util_1 = require("util");
const cacheUtil_1 = require("../../common/cacheUtil");
const helper_1 = require("../helper");
const config_1 = require("../common/config");
const wishlist_1 = require("../models/wishlist");
const wishlistHelp_1 = require("../models/wishlistHelp");
const activity_1 = require("../services/activity");
const roleInfo_1 = require("../services/roleInfo");
const activityTools_1 = require("./activityTools");
const modelProxy_1 = require("../services/modelProxy");
const util_2 = require("../../common/util");
const PyqActivityTopic_1 = require("../../models/PyqActivityTopic");
const activityTopic_1 = require("../services/activityTopic");
let logger = (0, activityTools_1.getActivityLogger)(config_1.ndzj2020Cfg.activityName);
function getTopicId() {
    return __awaiter(this, void 0, void 0, function* () {
        return PyqActivityTopic_1.ActivityTopicModel.getIdByName(config_1.ndzj2020Cfg.topicName);
    });
}
function getShareText(wishId, topicId) {
    let topicButton = (0, activityTopic_1.getActivityTopicLinkButton)(topicId, config_1.ndzj2020Cfg.topicName);
    let text = (0, util_1.format)(config_1.ndzj2020Cfg.momentTextTemplate, wishId, topicButton);
    return text;
}
exports.getShareText = getShareText;
function onAddWishShareNDZJ2020Moment(params) {
    return __awaiter(this, void 0, void 0, function* () {
        let topicId = yield getTopicId();
        let text = getShareText(params.wishId, topicId);
        logger.info(params, "onAddWishShareNDZJ2020Moment");
        let id = yield (0, activity_1.addMomentForActivity)({ roleId: params.roleid, text: text, imgs: params.shareImgs, skipImgAudit: 1 });
        return id;
    });
}
exports.onAddWishShareNDZJ2020Moment = onAddWishShareNDZJ2020Moment;
const HelpRankList = cacheUtil_1.RankList.create(config_1.ndzj2020Cfg.helpRankKey, config_1.ndzj2020Cfg.helpRankExpireTime);
function getHelpSum(roleId) {
    return __awaiter(this, void 0, void 0, function* () {
        let query = wishlistHelp_1.WishlistHelpModel.scope()
            .sum("Progress as sum")
            .from(wishlistHelp_1.WishlistHelpModel.tableName + " as h")
            .innerJoin(wishlist_1.WishlistModel.tableName + " as w", "h.WishId", "w.WishId")
            .where("w.ActivityId", wishlist_1.EActivityType.NDZJ2020)
            .where("w.Type", wishlist_1.EWishType.Item)
            .where("h.RoleId", roleId);
        let rows = yield wishlistHelp_1.WishlistHelpModel.executeByQuery(query);
        logger.debug({ rows, roleId }, "getHelpSum");
        if (rows && rows.length > 0) {
            return rows[0].sum || 0;
        }
        else {
            return 0;
        }
    });
}
exports.getHelpSum = getHelpSum;
function onWishHelpFinish(payload) {
    return __awaiter(this, void 0, void 0, function* () {
        let { roleId } = payload;
        let progress = yield getHelpSum(roleId);
        logger.info({ roleId, progress: progress }, "onWishHelpFinish");
        if (progress > 0) {
            yield HelpRankList.add(roleId, progress);
        }
    });
}
exports.onWishHelpFinish = onWishHelpFinish;
function getRank() {
    return __awaiter(this, void 0, void 0, function* () {
        let list = yield HelpRankList.list(config_1.ndzj2020Cfg.rankSize);
        let myList = list.map(function (r) {
            return { roleId: _.toNumber(r.member), progress: _.toNumber(r.score) };
        });
        let roleIds = myList.map((r) => r.roleId);
        let roleInfoMap = yield (0, roleInfo_1.getRoleInfoMap)(roleIds);
        let rank = 1;
        let retList = myList.map((r) => {
            let roleInfo = roleInfoMap.get(r.roleId) || { RoleName: "", server_id: 0 };
            let item = {
                rank: rank,
                roleId: r.roleId,
                serverId: roleInfo.server_id,
                roleName: roleInfo.RoleName,
                progress: r.progress,
            };
            rank += 1;
            return item;
        });
        return retList;
    });
}
exports.getRank = getRank;
function getRecentHelps() {
    return __awaiter(this, void 0, void 0, function* () {
        let query = wishlistHelp_1.WishlistHelpModel.scope()
            .from(wishlistHelp_1.WishlistHelpModel.tableName + " as h")
            .innerJoin(wishlist_1.WishlistModel.tableName + " as w", "h.WishId", "w.WishId")
            .where("w.ActivityId", wishlist_1.EActivityType.NDZJ2020)
            .where("w.Type", wishlist_1.EWishType.Item)
            .select("h.*")
            .orderBy("h.CreateTime", "desc")
            .limit(config_1.ndzj2020Cfg.recentHelpSize);
        let rows = yield wishlistHelp_1.WishlistHelpModel.executeByQuery(query);
        let roleIds = _.flatMap(rows, (r) => [r.RoleId, r.TargetId]);
        let roleInfoMap = yield (0, roleInfo_1.getRoleInfoMap)(roleIds);
        let getName = function (roleId) {
            let record = roleInfoMap.get(roleId) || { RoleName: "" };
            return record.RoleName;
        };
        let list = rows.map((r) => {
            let item = {
                id: r.ID,
                roleId: r.RoleId,
                roleName: getName(r.RoleId),
                targetId: r.RoleId,
                targetName: getName(r.TargetId),
                createTime: r.CreateTime,
            };
            return item;
        });
        return list;
    });
}
exports.getRecentHelps = getRecentHelps;
function wishHelpRankHandler(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            let rank = yield getRank();
            let data = { list: rank };
            res.send({ code: 0, data: data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.wishHelpRankHandler = wishHelpRankHandler;
function recentHelpHandler(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            let helps = yield getRecentHelps();
            let data = { list: helps, count: helps.length };
            res.send({ code: 0, data: data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.recentHelpHandler = recentHelpHandler;
function mostLikedMomentHandler(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            let schema = {
                roleId: { type: Number },
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            let params = req.params;
            let startTs = new Date(config_1.ndzj2020Cfg.startDate).getTime();
            let endTs = new Date(config_1.ndzj2020Cfg.endDate).getTime();
            let query = modelProxy_1.MomentModel.normalScope()
                .where("RoleId", params.roleId)
                // 点赞数大于100
                .whereRaw("HotState regexp '\"like\":[[:digit:]]{3}'")
                .whereBetween("CreateTime", [startTs, endTs]);
            let rows = yield modelProxy_1.MomentModel.executeByQuery(query);
            let maxLikedMoment = _.maxBy(rows, (r) => {
                let state = modelProxy_1.MomentModel.formatHotState(r.HotState);
                return state.like || 0;
            });
            let hasHotMoment = !!maxLikedMoment;
            let data = {
                hasHotMoment: hasHotMoment,
                moment: null,
            };
            if (hasHotMoment) {
                let likeCount = modelProxy_1.MomentModel.formatHotState(maxLikedMoment.HotState).like;
                let imgList = (0, util_2.csvStrToArray)(maxLikedMoment.ImgList);
                let videoList = (0, util_2.csvStrToArray)(maxLikedMoment.VideoList);
                data.moment = {
                    id: maxLikedMoment.ID,
                    likeCount: likeCount,
                    text: maxLikedMoment.Text,
                    imgList: imgList,
                    videoList: videoList,
                    createTime: maxLikedMoment.CreateTime,
                };
            }
            res.send({ code: 0, data: data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.mostLikedMomentHandler = mostLikedMomentHandler;
//# sourceMappingURL=ndzj2020.js.map