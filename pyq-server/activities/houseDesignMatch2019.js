"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HouseDesignMatch2019Activity = void 0;
const HouseDesignMatchClass_1 = require("./HouseDesignMatchClass");
class HouseDesignMatch2019Class extends HouseDesignMatchClass_1.HouseDesignMatch {
    getMatchDateRange() {
        return {
            [HouseDesignMatchClass_1.EMatchStage.PRE]: { start: new Date('2019-07-09 10:00'), end: new Date('2019-07-15 23:59') },
            [HouseDesignMatchClass_1.EMatchStage.SEMI_FINAL]: { start: new Date('2019-07-18 10:00'), end: new Date('2019-07-24 23:59') },
            [HouseDesignMatchClass_1.EMatchStage.FINAL]: { start: new Date('2019-07-31 10:00'), end: new Date('2019-08-03 23:59') },
        };
    }
    getKeyPrefix() {
        return 'l10:activity:house_design_match_2019';
    }
    getPreviewUrl(workId) {
        return `https://qnm.163.com/2019/jysj/mh/##/list/${workId}`;
    }
    getShareTextByNameAndLink(workName, link) {
        return `我进入家园设计大赛总决赛啦，欢迎参观投票哦${link}`;
    }
    getPreviewUrlButtonName() {
        return '点击投票吧';
    }
}
exports.HouseDesignMatch2019Activity = new HouseDesignMatch2019Class();
//# sourceMappingURL=houseDesignMatch2019.js.map