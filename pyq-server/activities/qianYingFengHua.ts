import * as _ from "lodash";
import { getRedis } from "../../common/redis";
import * as httpLib from "../../common/request";
import { getJsonInfo } from "../../common/util";
import { qyfhCfg } from "../common/config";
import { ActivityErrors, BussError } from "../errorCodes";
import { checkParams } from "../helper";
import { addTopicMoment, getActivityTopicId } from "../services/activityTopic";
import { MomentModel, RoleInfoModel } from "../services/modelProxy";
import { checkIsUrl } from "../services/util";
import { getActivityLogger, getProxiedUrl, SendFlowerPayload } from "./activityTools";
import errorHandler = require("../errorHandler");
import EventBus = require("../eventBus");

const logger = getActivityLogger(qyfhCfg.name);

if (qyfhCfg.enable) {
  EventBus.on(EventBus.Events.SEND_FLOWER, function (payload: SendFlowerPayload) {
    const receiver = payload.receiver;
    recordPlayerRenQi(receiver, payload);
  });
} else {
  logger.warn("activity:qyfhguild is disabled");
}

export function getCoverSmart(cover: string) {
  if (checkIsUrl(cover)) {
    return cover;
  } else {
    const obj = getJsonInfo(cover, {}) as { type: string; url: string };
    if (obj && obj.url && checkIsUrl(obj.url)) {
      return obj.url;
    }
  }
  return "";
}

async function addMdMoment(work: WorkInfoItem) {
  const topicId = getActivityTopicId(work.text);
  const now = Date.now();
  let momentId = null;
  if (work.roleid) {
    const roleInfo = await RoleInfoModel.findOne({ RoleId: work.roleid });
    if (!roleInfo) {
      throw ActivityErrors.RoleIdNotFound;
    }
  }

  const cover = getCoverSmart(work.cover);

  if (topicId) {
    const ret = await MomentModel.insert({
      RoleId: work.roleid,
      Text: work.text,
      ImgList: cover,
      ImgAudit: "1",
      CreateTime: now,
    });
    momentId = ret.insertId;
    await addTopicMoment(work.roleid, momentId, topicId, []);
  } else {
    return BussError(ActivityErrors.ShareTextNeedTopicId);
  }
  await markPlayerAttend(work.roleid);
  return momentId;
}

function getAttendSetKey() {
  return `qnm:activity:${qyfhCfg.name}:attend_set`;
}

export async function markPlayerAttend(roleId: number) {
  return getRedis().saddAsync(getAttendSetKey(), "" + roleId);
}

export async function isRoleIdAttend(roleId: number) {
  const isAttend = await getRedis().sismember(getAttendSetKey(), "" + roleId);
  return isAttend;
}

export async function recordPlayerRenQi(roleId: number, sendFlower: SendFlowerPayload) {
  const isAttend = await isRoleIdAttend(roleId);
  logger.info({ roleId, sendFlower, isAttend }, "PrepareUpdateRenQi");
  if (isAttend) {
    const flower = sendFlower.flower;
    if (flower && flower.renqi) {
      const incrRenQi = flower.renqi;
      await notifyRenQiChange(roleId, incrRenQi);
    } else {
      logger.error({ roleId, sendFlower, isAttend }, "SendFlowerParseRenQiFailed");
    }
  } else {
    logger.info({ roleId, sendFlower, isAttend }, "PlayerRenQiChangeNotSync");
  }
}

export interface WorkInfoItem {
  roleid: number;
  workid: number;
  cover: string;
  text: string;
}

interface IShareToMd {
  works: WorkInfoItem[];
}

export async function qyfhShareToMd(req, res, next) {
  const params: IShareToMd = req.params;
  try {
    const schema = {
      works: { type: Array },
    };
    logger.info({ req, params: req.params }, "qyfhShareToMd");
    await checkParams(req.params, schema);
    const ids = [];
    for (const w of params.works) {
      const id = await addMdMoment(w);
      ids.push(id);
    }
    res.send({ code: 0, data: { ids: ids } });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

interface IVoteRenQiParams {
  roleId: number;
  isLimitRoleId: boolean;
  limitRoleIds: number[];
}

export function isCanVoteRenQi(params: IVoteRenQiParams): boolean {
  if (params.isLimitRoleId) {
    return _.includes(params.limitRoleIds, params.roleId);
  } else {
    return true;
  }
}

async function notifyRenQiChange(roleId: number, incrRenQi: number) {
  const url = getProxiedUrl(qyfhCfg.activityApiHost + qyfhCfg.renqiChangeApiPath.replace("{{qyfhName}}", qyfhCfg.name));
  const rqOption = { method: "POST", url, formData: { roleid: roleId, renqi: incrRenQi } };
  logger.info({ roleId: roleId, rqOption }, "CallNotifyRenQiChangeStart");
  try {
    const ret = await httpLib.request(rqOption);
    logger.info({ roleId: roleId, ret: ret, rqOption }, "CallNotifyRenQiChangeOK");
    return ret;
  } catch (err) {
    logger.error({ roleId: roleId, err, rqOption }, "CallNotifyRenQiChangeError");
    return false;
  }
}
