/**
 * Created by <PERSON>hen<PERSON> on 2017/1/17.
 */

const FeatureToggle = require('../../common/FeatureToggle')

function startAll () {
  FeatureToggle.whenActive('chuanJiaBao', () => {
    require('./chuanJiaBao')
  })
  FeatureToggle.whenActive('mingRenTang', () => {
    require('./mingRenTang')
  })
  // require('./battleSongMatch');
  // require('./homeLandMatch');
  FeatureToggle.whenActive('qingyuan_activity', () => {
    require('./qingyuan')
  })

  FeatureToggle.whenActive('qnm_oneyear_fengyun', () => {
    require('./oneyearFengyun')
  })

  FeatureToggle.whenActive('qnmCoupleHouseActivity', () => {
    require('./coupleHouse')
  })
}

module.exports = {
  startAll: startAll
}
