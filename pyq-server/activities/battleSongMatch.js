/**
 * Created by <PERSON><PERSON><PERSON> on 16-12-13.
 */

const PyqTopic = require('../../models/PyqTopic');
const PyqMoment = require('../../models/PyqMoments');
const PyqProfile = require('../../models/PyqProfile');
const PyqEvent = require('../../models/PyqEvent');
const QnmRoleInfo = require('../../models/QNMRoleInfos');
const util = require('../../common/util');
const _ = require('lodash');
const rq = require('../../common/request');
const logger = require('../../common/logger');

const BATTLE_SONG_MATCH = "战歌大赛";

PyqTopic.on(PyqTopic.Events.ADD_MOMENT_TO_TOPIC, function (payload) {
  const subject = payload.subject;
  const momentId = payload.momentId;
  if(subject === BATTLE_SONG_MATCH) {
    playerAttendBattleSongHandler(momentId);
  }
});

function onSendFlowerHandler(roleId) {
  return PyqProfile.findOne({RoleId:roleId}, ['FlowerRenQi'])
    .then(function (profile) {
      sendDataFlowerRenQi({
        roleid: roleId,
        flower: profile.FlowerRenQi
      });
    })
}

PyqEvent.on(PyqEvent.Events.SEND_FLOWER, function (payload) {
  const receiver = payload.receiver;
  onSendFlowerHandler(receiver);
});

/**
 * 获取玩家投稿
 * @param momentId
 */
function getPlayerContribution(momentId) {
  return PyqMoment.findById(momentId, ['ID', 'Text', 'RoleId', 'ImgList'])
    .then(function (moment) {
      if(!moment) {
        return Promise.reject({errorType: "MomentIdInvalid"});
      }
      return moment;
    }).then(function (moment) {
      const roleId  = moment.RoleId;
      let getRoleInfos = function () {
        return QnmRoleInfo.findOne({RoleId: roleId}, ['RoleId', 'RoleName', 'ServerId', 'Level', 'JobId'])
          .then(function (roleInfo) {
            return QnmRoleInfo.getRoleInfosWithServerAndJob([roleInfo])
          }).then(function (records) {
            return _.first(records);
          })
      };
      let getFlowerRenQi = function () {
        return PyqProfile.findOne({RoleId:roleId}, ['FlowerRenQi'])
          .then(function (profile) {
            if(profile) {
              return profile.FlowerRenQi || null;
            }
          })
      };

      return Promise.all([
        PyqProfile.findOne({RoleId: roleId}, ['RoleId', 'SignatureVoice']),
        getRoleInfos(),
        moment,
        getFlowerRenQi(),
      ])
    }).spread(function(profile, roleInfo, moment, flower) {
      let voiceUrl = null;
      if(profile.SignatureVoice) {
        voiceUrl = JSON.parse(profile.SignatureVoice).url || "";
      }
      let photos = util.csvStrToArray(moment.ImgList);
      let imgUrl = _.first(photos) || null;
      let serverName = "测试组-测试服";
      if(roleInfo.Server) {
        serverName = roleInfo.Server.group + "-" + roleInfo.Server.name;
      }
      return {
        roleid: profile.RoleId,
        sname: serverName,
        sid:roleInfo.ServerId,
        nickname: roleInfo.RoleName,
        imgurl: imgUrl,
        voiceurl: voiceUrl,
        dec: moment.Text,
        flower: flower
      }
    })
}

function playerAttendBattleSongHandler(momentId) {
  return getPlayerContribution(momentId).then(function (data) {
    return sendWorks(data);
  })
}


function sendWorks(qs) {
  qs.action = "send_works";
  return zhanGeRequest(qs);
}

function sendDataFlowerRenQi(qs) {
  qs.action = "send_flower";
  return zhanGeRequest(qs);
}

function zhanGeRequest(qs) {
  const ZHAN_GE_API_HOST = "http://file.mg.163.com/qnm/2016/zhange/api.php";
  return rq.get(ZHAN_GE_API_HOST, qs).then(function (data) {
    logger.add("zhange_api_return", JSON.stringify({data: data}));
  }).catch(function (err) {
    logger.add("zhange_api_return", JSON.stringify({error:err}));
  });
}

exports.getPlayerContribution =  getPlayerContribution;
