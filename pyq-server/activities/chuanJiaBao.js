/**
 * Created by <PERSON><PERSON><PERSON> on 2017/1/16.
 * 传家宝设计大赛
 */

const PyqTopic = require('../../models/PyqTopic');
const PyqMoment = require('../../models/PyqMoments');
const util = require('../../common/util');
const _ = require('lodash');
const rq = require('../../common/request');
const logger = require('../../common/logger');
const ActivityHelper = require('./activityHelper');
const { getRedis } = require('../../common/redis');

const CategoryHash = {
  "传世经典传家宝" :  1,
  "标新立异传家宝" : 2,
  "我身边的传家宝" : 3
};


PyqTopic.on(PyqTopic.Events.ADD_MOMENT_TO_TOPIC, function (payload) {
  const subject = payload.subject;
  const momentId = payload.momentId;
  if(CategoryHash.hasOwnProperty(subject)) {
    chuanJiaBaoHandler(momentId, CategoryHash[subject]);
  }
});

PyqMoment.on(PyqMoment.Events.LIKE_MOMENT, function (payload) {
  const momentId = payload.momentId;
  return updateMomentLikeValue(momentId, 1);
});

PyqMoment.on(PyqMoment.Events.Cancel_LIKE_MOMENT, function (payload) {
  const momentId = payload.momentId;
  return updateMomentLikeValue(momentId, -1);
});

function updateMomentLikeValue(momentId, changeValue) {
  const key = "qnm:chuanjiabao:mdvote";
  return getRedis().hincrbyAsync(key, momentId, changeValue);
}

function getMoment(momentId) {
  return ActivityHelper.getMoment(momentId, ['ID', 'Text', 'RoleId', 'ImgList'])
    .then(moment => {
      if(_.isEmpty(moment.ImgList)) {
        return Promise.reject({msg: "必须包含图片!"});
      }
      return moment;
    })
}

function getPlayerInfo(roleId) {
  return ActivityHelper.getPlayerInfo(roleId, ['RoleId', 'RoleName', 'UserName', 'ServerId', 'Level', 'JobId']);
}

/**
 * 获取传家宝的比赛的类型
 * @param {String} text
 * @return {Number} category
 */
function getCategory(text) {
  let hashedText = util.getHashedText(text);
  let categoryText = hashedText[1];
  return CategoryHash[categoryText];
}

function getWorkInfo(momentId, category) {
  return getMoment(momentId)
    .then(moment => {
      return [moment, getPlayerInfo(moment.RoleId)]
    }).spread((moment, roleInfo) => {
      if(category) {
        return {
          work_id: moment.ID,
          category: category,
          role_id: roleInfo.RoleId,
          images: JSON.stringify(moment.ImgList),
          urs: roleInfo.UserName,
          nick_name: roleInfo.RoleName,
          server_name: roleInfo.ServerName,
          server_id: roleInfo.ServerId,
          description: moment.Text,
        }
      }
    })
}

function chuanJiaBaoHandler(momentId, category) {
  return getWorkInfo(momentId, category).then(sendWork)
}


function sendWork(work) {
  const API_PATH = "http://file.mg.163.com/qnm/2017/chuanjiabao/index.php";
  work.act = "send_works";
  return rq.post(API_PATH, work).then(info => {
    logger.add("chuan_jia_bao_api_return", JSON.stringify(info));
    return info;
  }).catch(err => {
    logger.add("chuan_jia_bao_api_return", JSON.stringify(Object.getOwnPropertyNames(err)));
  });
}

module.exports = {
  getWorkInfo: getWorkInfo,
  sendWork: sendWork
};
