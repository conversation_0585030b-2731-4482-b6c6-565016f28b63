import { HouseDesignMatch, MatchDateRang<PERSON>, EMatchStage } from "./HouseDesignMatchClass";

class HouseDesignMatch2019Class extends HouseDesignMatch {
    getMatchDateRange(): MatchDateRange {
        return {
            [EMatchStage.PRE]: { start: new Date('2019-07-09 10:00'), end: new Date('2019-07-15 23:59') },
            [EMatchStage.SEMI_FINAL]: { start: new Date('2019-07-18 10:00'), end: new Date('2019-07-24 23:59') },
            [EMatchStage.FINAL]: { start: new Date('2019-07-31 10:00'), end: new Date('2019-08-03 23:59') },
        }
    }

    getKeyPrefix(): string {
        return 'l10:activity:house_design_match_2019'
    }

    getPreviewUrl(workId: number): string {
        return `https://qnm.163.com/2019/jysj/mh/##/list/${workId}`
    }

    getShareTextByNameAndLink(workName: string, link: string): string {
        return `我进入家园设计大赛总决赛啦，欢迎参观投票哦${link}`
    }

    getPreviewUrlButtonName(): string {
        return '点击投票吧'
    }
}

export let HouseDesignMatch2019Activity = new HouseDesignMatch2019Class()
