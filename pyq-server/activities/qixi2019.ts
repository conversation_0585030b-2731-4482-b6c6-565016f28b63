import { getLogger } from "../../common/logger2";
import { ActivityTopic } from "../../models/PyqActivityTopic";
import * as Moment from '../../models/PyqMoments'
import { csvStrToArray } from "../../common/util";
import * as httpLib from '../../common/request'
import * as _ from 'lodash'
import * as bluebird from 'bluebird'
import { checkParams } from "../helper";
import errorHandler = require("../errorHandler");
import { getActivityTopicFullText, addTopicMoment } from "../services/activityTopic";
import { replaceHttps } from "../../common/util2";
import { API_HOST } from "./activityHelper";
import { isOnlyContainTopic, removeTopicLink } from "./activityTools";

let logger = getLogger('l10_qixi2019')

export const TOPIC_NAME = '七夕星光印记'

const ATTEND_MIN_IMAGE_NUM = 2

const APPLY_API = API_HOST + '/public/qnm/qixi_2019/client/apply/mengdao'

interface IAttendTopic {
    roleId: number
    momentId: number
    topicId: number
}

export async function onAttendTopic(params: IAttendTopic) {
    let name = await ActivityTopic.getNameById(params.topicId)
    if (name === TOPIC_NAME) {
        return attendByMomentId(params.momentId)
    }
}


interface AttendMoment {
    RoleId: number
    Text: string
    ImgList: string
}

async function attendByMomentId(momentId: number) {
    let record = await Moment.findById(momentId, ['Text', 'ImgList', 'RoleId']) as AttendMoment
    if (record) {
        let imgUrls = csvStrToArray(record.ImgList)
        if (imgUrls.length >= ATTEND_MIN_IMAGE_NUM && !isOnlyContainTopic(record.Text)) {
            let text = removeTopicLink(record.Text)
            return requestAttendApi(record.RoleId, imgUrls, text)
        }
    }
}

async function requestAttendApi(roleId: number, imgs: string[], intr: string) {
    imgs = imgs.map(url => replaceHttps(url))
    let data = { roleId: roleId, workImgs: imgs, mark: intr }
    logger.info('Prepare attend', data)
    let result = await httpLib.request({
        method: "POST",
        url: APPLY_API,
        body: data
    })
    logger.info('Request apply api result', { result: result, data: data })
    return result
}

function getShareText(work: WorkInfoItem) {
    return `我点亮了七夕夜空中的一颗星星，摘下送给你<link button=点击查看,OpenUrlInGame,https://qnm.163.com/2019/qxjt/mh/##/list/${work.workid}>`
}

async function addMdMoment(work: WorkInfoItem) {
    let topicId = await ActivityTopic.getIdByName(TOPIC_NAME)
    let momentId
    let now = Date.now()

    let shareText = getShareText(work)
    if (topicId) {
        let text = getActivityTopicFullText(topicId, TOPIC_NAME, shareText)
        let ret = await Moment.insert({ RoleId: work.roleid, Text: text, ImgList: work.cover, ImgAudit: '1', CreateTime: now })
        momentId = ret.insertId
        await addTopicMoment(work.roleid, momentId, topicId, [])
    } else {
        logger.error({ work: work }, 'topic not create for share moment')
        await bluebird.reject({ errorType: 'TopicNotExist', message: `topic: ${TOPIC_NAME} not exist` })
    }
    return momentId
}

export interface WorkInfoItem {
    roleid: number
    workid: number
    cover: string
}

interface IShareToMd {
    works: WorkInfoItem[]
}

export async function shareToMd(req, res, next) {
    try {
        let schema = {
            works: { type: Array }
        }
        await checkParams(req.params, schema)
        let params: IShareToMd = req.params
        let ids = []
        for (let w of params.works) {
            let id = await addMdMoment(w)
            ids.push(id)
        }
        res.send({ code: 0, data: { ids: ids } })
    } catch (err) {
        errorHandler(err, req, res, next)
    }
}
