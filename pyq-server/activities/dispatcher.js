"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.dispatchActivityShare = exports.Events = exports.dispatch = void 0;
/* eslint-disable @typescript-eslint/no-var-requires */
const _ = require("lodash");
const huyitian = require("./huyitianfans");
const GoodVoiceMatch = require("./goodVoiceMatch");
const HouseDesignMatch = require("./houseDesignMatch");
const FengyunGang2018 = require("./fengyunGang2018");
const mingren_1 = require("./mingren");
const simpleActivities_1 = require("./simpleActivities");
const qianYingFengHua_1 = require("./qianYingFengHua");
const errorCodes_1 = require("../errorCodes");
const config_1 = require("../common/config");
const EventMap = {
    huyitianfanEnroll: huyitian.enroll,
    goodVoiceEnroll: GoodVoiceMatch.enroll,
    houseDesignMatch: HouseDesignMatch.enroll,
    fengyunGang2018: FengyunGang2018.enroll,
    mingRen: mingren_1.enroll,
    jianianhua2019: simpleActivities_1.jiaNianHua2019ShareMoment,
};
function getEvents() {
    return _.keys(EventMap);
}
function dispatch(name, payload) {
    return EventMap[name](payload);
}
exports.dispatch = dispatch;
function initActivityListen() { }
initActivityListen();
exports.Events = getEvents();
function dispatchActivityShare(req, res, next) {
    const activity = req.params.activity || "";
    if (activity === config_1.qyfhCfg.name) {
        (0, qianYingFengHua_1.qyfhShareToMd)(req, res, next);
    }
    else {
        res.send(errorCodes_1.ActivityErrors.ActivityUnDefined);
    }
}
exports.dispatchActivityShare = dispatchActivityShare;
//# sourceMappingURL=dispatcher.js.map