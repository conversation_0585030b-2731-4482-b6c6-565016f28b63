"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.jiaNianHua2019ShareMoment = void 0;
const PyqMoments = require("../../models/PyqMoments");
const helper_1 = require("../helper");
function jiaNianHua2019ShareMoment(params) {
    return __awaiter(this, void 0, void 0, function* () {
        let schema = {
            roleid: { type: Number },
            shareUrl: { type: String },
            imgUrl: { type: String }
        };
        yield (0, helper_1.checkParams)(params, schema);
        let link = PyqMoments.genLinkButton(params.shareUrl, '2019嘉年华双人同行一人免单');
        let text = `点击${link} 集赞免费领亲友票啦！你不赞我不赞，亲友想去怎么办！你也赞我也赞，门票买一送一超划算！`;
        let props = {
            RoleId: params.roleid,
            ImgList: params.imgUrl,
            imgAudit: '1',
            Text: text,
            CreateTime: Date.now()
        };
        let ret = yield PyqMoments.insert(props);
        return { id: ret.insertId };
    });
}
exports.jiaNianHua2019ShareMoment = jiaNianHua2019ShareMoment;
//# sourceMappingURL=simpleActivities.js.map