const util = require('../../common/util')
const { getRedis } = require('../../common/redis')
const PyqMoments = require('../../models/PyqMoments')
const co = util.co
const _ = require('lodash')
const passedStatus = 1

const redisNameSpacePrefix = 'qnm:activity:sticker_fight:'

function cacheKey (name) {
  return redisNameSpacePrefix + name
}

PyqMoments.on(PyqMoments.Events.LIKE_MOMENT, function (payload) {
  const momentId = payload.momentId
  return getWorkIdByMomentId(momentId).then(workId => {
    if (workId) {
      updateWorkLikeValue(workId, 1)
    }
  })
})

PyqMoments.on(PyqMoments.Events.Cancel_LIKE_MOMENT, function (payload) {
  const momentId = payload.momentId
  return getWorkIdByMomentId(momentId).then(workId => {
    if (workId) {
      updateWorkLikeValue(workId, -1)
    }
  })
})

function updateWorkLikeValue (workId, changeValue) {
  return getRedis().hincrbyAsync(cacheKey('md_likes'), workId, changeValue)
}

function getPreviewUrl (workId) {
  return `https://qnm.163.com/m/dtdsh/#/list/${workId}`
}

function getShareText (workId) {
  const link = PyqMoments.genLinkButton(getPreviewUrl(workId), '点赞')
  return `我参加倩女斗图大赛通过审核啦，快来为我${link}吧。`
}

function insertMoment (roleId, workId, urls) {
  urls = urls || []
  let imgList = urls.join(',')
  let imgAudit = _.fill(Array(urls.length), '1').join(',')
  return PyqMoments.insert({
    RoleId: roleId,
    ImgList: imgList,
    imgAudit: imgAudit,
    Text: getShareText(workId),
    CreateTime: Date.now()
  }).then(insertInfo => {
    return insertInfo.insertId
  })
}

function auditResult (workInfos) {
  let relation = []
  return co(function * () {
    for (let i = 0; i < workInfos.length; i++) {
      let item = workInfos[i]
      const auditStatus = parseInt(item.status, 10)
      if (auditStatus === passedStatus) {
        let mId = yield insertMoment(item.roleId, item.workId, item.urls)
        yield getRedis().hsetAsync(cacheKey('momentId_to_workId'), mId, item.workId)
        relation[item.workId] = mId
      }
    }
    return relation
  })
}

function getWorkIdByMomentId (momentId) {
  return getRedis().hgetAsync(cacheKey('momentId_to_workId'), momentId)
}

function getWorkIdLikesCount (workId) {
  return getRedis().hgetAsync(cacheKey('md_likes'), workId)
    .then(count => {
      return parseInt(count, 10) || 0
    })
}

module.exports = {
  auditResult: auditResult,
  getWorkIdLikesCount: getWorkIdLikesCount
}
