// 宗派选秀2021 活动

import { csvStrToArray } from "../../common/util";
import * as httpLib from "../../common/request";
import * as _ from "lodash";
import { getRedis, IExistResult } from "../../common/redis";
import { replaceHttps } from "../../common/util2";
import { isInDateRange, SendFlowerPayload, getActivityLogger, removeTopicLink, getProxiedUrl } from "./activityTools";
import { GenericCache } from "../../common/cacheUtil";
import { MomentModel, ProfileModel } from "../services/modelProxy";
import { zpxx2021Cfg } from "../common/config";
import { ActivityTopic } from "../../models/PyqActivityTopic";
import { Zpxx2021Apply, Zpxx2021WorkImage } from "../type/req";
import { AddMomentToTopicOrigin, IAttendTopic } from "./type";
import { isAfter } from "../../common/dateUtil";

let logger = getActivityLogger(zpxx2021Cfg.name);

type MatchStage = "chusai" | "fusai";

const INIT_RENQI_HASH_KEY = `qnm:activity:${zpxx2021Cfg.name}:init_renqi`;

export function getMatchStageRenQiKey(stage: MatchStage) {
  return `qnm:activity:${zpxx2021Cfg.name}:${stage}_renqi`;
}

async function getPlayerRenQi(roleId: number) {
  let profile = (await ProfileModel.findOne({ RoleId: roleId }, ["RenQi"])) || { RenQi: 0 };
  return profile.RenQi;
}

export async function onAttendTopic(params: IAttendTopic) {
  logger.debug(params, "onAttendTopic");
  let name = await ActivityTopic.getNameById(params.topicId);
  logger.debug({ name, params }, "onAttendTopicName");
  if (name === zpxx2021Cfg.topicName) {
    if (params.origin === AddMomentToTopicOrigin.Game) {
      attendByMomentId(params.momentId);
    } else if (params.origin === AddMomentToTopicOrigin.Activity) {
      // 活动报名成功后会分享一条动态到活动话题下的梦岛
      logger.info({ params }, "initPlayerRenQiAfterSignUpSuccess");
      initPlayerRenQi(params.roleId);
    }
  }
}

export async function onSendFlower(payload: SendFlowerPayload) {
  const receiver = payload.receiver;
  recordPlayerRenQi(receiver, payload);
}

const linkRegex = /^<link\sbutton=.*>$/;

function isAddAttendText(text: string) {
  if (text) {
    return !text.match(linkRegex);
  } else {
    return false;
  }
}

async function attendByMomentId(momentId: number) {
  let record = await MomentModel.findOne({ ID: momentId }, ["Text", "ImgList", "RoleId"]);
  if (record) {
    let imgUrls = csvStrToArray(record.ImgList);
    if (imgUrls.length >= zpxx2021Cfg.attendMinImage && isAddAttendText(record.Text)) {
      try {
        await requestAttendApi(record.RoleId, imgUrls, record.Text);
      } catch (err) {
        logger.error({ err: err }, "AttendByMomentIdFailed");
      }
    }
  }
}

async function requestAttendApi(roleId: number, imgs: string[], intr: string) {
  const worksUrl: Zpxx2021WorkImage[] = imgs.map((url) => {
    return {
      type: "img",
      url: replaceHttps(url),
    };
  });
  let mark = removeTopicLink(intr);
  const coverUrl = worksUrl[0].url;
  let data: Zpxx2021Apply = {
    roleid: roleId,
    worksUrl,
    cover: { type: "img", url: coverUrl },
    source: "md",
    mark: mark,
  };
  const applyApi = getProxiedUrl(zpxx2021Cfg.applyApi);
  logger.info({ data, url: applyApi }, "PrepareAttend");
  let result = await httpLib.request({
    method: "POST",
    url: applyApi,
    body: data,
  });
  logger.info({ result: result, data: data }, "ApplyApiReturn");
}

async function initPlayerRenQi(roleId: number) {
  let renqi = await getPlayerRenQi(roleId);
  return getRedis().hsetnxAsync(INIT_RENQI_HASH_KEY, "" + roleId, renqi);
}

async function isRoleIdAttend(roleId: number) {
  let ret = await getRedis().hexistsAsync(INIT_RENQI_HASH_KEY, "" + roleId);
  return ret === IExistResult.Exist;
}

export async function recordPlayerRenQi(roleId: number, sendFlower: SendFlowerPayload) {
  let isAttend = await isRoleIdAttend(roleId);
  let isCanIncrRenQi = false
  if(isAttend) {
    isCanIncrRenQi = await isCanVoteInMatch(roleId, new Date());
  }
  let date = new Date();
  let stage = getMatchStage(date);
  logger.info({ roleId, sendFlower, stage, isAttend, isCanIncrRenQi }, "PrepareUpdateRenQi");
  if (isAttend && isCanIncrRenQi) {
    const isVoteRenqiStart = isAfter(new Date(), new Date(zpxx2021Cfg.renqiStartDate));
    if (isVoteRenqiStart) {
      let flower = sendFlower.flower;
      let hashKey = getMatchStageRenQiKey(stage);
      await getRedis().hincrbyAsync(hashKey, "" + roleId, flower.renqi);
      await notifyRenQiChange(roleId);
    } else {
      logger.info(
        { roleId, sendFlower, stage, isAttend, isCanIncrRenQi },
        "SendFlowAfterApplyBeforeVoteWillReInitRenQi"
      );
      initPlayerRenQi(roleId);
    }
  }
}

export interface WorkInfoItem {
  roleid: number;
  workid: number;
  cover: string;
}

interface IVoteRenQiParams {
  roleId: number;
  isLimitRoleId: boolean;
  limitRoleIds: number[];
}

export function isCanVoteRenQi(params: IVoteRenQiParams): boolean {
  if (params.isLimitRoleId) {
    return _.includes(params.limitRoleIds, params.roleId);
  } else {
    return true;
  }
}

function getApiByMatchStage(stage: MatchStage) {
  if (stage === "fusai") {
    return zpxx2021Cfg.fusaiRoleIdApi;
  } else {
    return "";
  }
}

interface AttendRoleIdApiReturn {
  code: number;
  msg?: any;
  data: { [key: string]: string };
}

class GetAttendRoleIdsCache extends GenericCache<MatchStage, number[]> {
  getExpireTime() {
    return 8 * 3600;
  }

  getKey(stage: MatchStage): string {
    return `l10_${zpxx2021Cfg.name}:match:${stage}:roleIds`;
  }

  async fetchDataSource(stage: MatchStage): Promise<number[]> {
    return getVotableRoleIds(stage);
  }
}

async function getVotableRoleIds(stage: MatchStage) {
  let apiUrl = getApiByMatchStage(stage);
  if (apiUrl) {
    let apiReturn: AttendRoleIdApiReturn = await httpLib.get(apiUrl);
    if (apiReturn.code === 1) {
      let list = Object.keys(apiReturn.data);
      let roleIds = list.map((x) => parseInt(x, 10));
      return roleIds;
    } else {
      logger.error({ state: stage, apiReturn }, "GetAttendRoleidFailed");
      return [];
    }
  } else {
    return [];
  }
}

let GetAttendRoleIds = new GetAttendRoleIdsCache();

export async function getFuSaiRoleIds(): Promise<number[]> {
  if (zpxx2021Cfg.skip_api_cache) {
    return getVotableRoleIds("fusai");
  } else {
    return GetAttendRoleIds.get("fusai");
  }
}

export async function isCanVoteInMatch(roleId: number, date: Date) {
  let stage = getMatchStage(date);
  if (stage) {
    switch (stage) {
      case "chusai":
        return true;
      case "fusai":
        let fusaiRoleIds = await getFuSaiRoleIds();
        const isInFuSai = _.includes(fusaiRoleIds, roleId);
        if(!isInFuSai) {
          logger.warn({stage: "fusai", fusaiRoleIds, roleId}, "isCanVoteInMatchFailed")
        }
        return isInFuSai
    }
  } else {
    return false;
  }
}

export function getMatchStage(date: Date): MatchStage {
  if (isInDateRange(date, zpxx2021Cfg.timeRange.chusai)) {
    return "chusai";
  } else if (isInDateRange(date, zpxx2021Cfg.timeRange.fusai)) {
    return "fusai";
  } else {
    return null;
  }
}

async function notifyRenQiChange(roleId: number) {
  const rqOption = {
    method: "POST",
    url: getProxiedUrl(zpxx2021Cfg.renqiChangeApi),
    qs: { roleid: roleId },
  };
  logger.info({ roleId: roleId, rqOption }, "NotifyRenQiChangeBegin");
  let ret = await httpLib.request(rqOption);
  logger.info({ rqOption, ret: ret }, "NotifyRenQiChangeResult");
  return ret;
}
