/* eslint-disable prefer-promise-reject-errors */
const bluebird = require('bluebird')
const { getRedis } = require('../../common/redis')
const ActivityHelper = require('./activityHelper')
const ModelManager = require('../../models/ModelManager')
const PyqMoment = ModelManager.getModelByTableName('pyq_moment')
const PyqEvent = ModelManager.getModelByTableName('pyq_event')
const FeatureToggle = require('../../common/FeatureToggle')
const { Constants } = require('../../common/data')
const _ = require('lodash')

const INIT_FLOWER_HASH_KEY = 'qnm:activity:oneyear_fengyun_2018:initflower'
const FLOWER_HASH_KEY = 'qnm:activity:oneyear_fengyun_2018:curflower'

FeatureToggle.whenActive('qnmFengyun2018', () => {
  PyqEvent.on(PyqEvent.Events.SEND_FLOWER, function (payload) {
    const receiver = payload.receiver
    onSendFlowerHandler(receiver)
  })
})

function isRoleIdAttend (roleId) {
  return getRedis().hexistsAsync(INIT_FLOWER_HASH_KEY, roleId)
}

function onSendFlowerHandler (roleId) {
  return isRoleIdAttend(roleId).then(isAttend => {
    if (isAttend) {
      return ActivityHelper.getFlowerRenQi(roleId)
        .then(renQi => {
          return getRedis().hsetAsync(FLOWER_HASH_KEY, roleId, renQi)
        })
    }
  })
}

function setInitialFlowerRenQiToRedis (roleid) {
  return ActivityHelper.getFlowerRenQi(roleid)
    .then(renQi => {
      return getRedis().hsetnxAsync(INIT_FLOWER_HASH_KEY, roleid, renQi)
    })
}

function getShareUrl (workId) {
  return 'https://qnm.163.com/m/fybhh/#/group/' + workId + '/'
}

function getShareText (workId) {
  const link = PyqMoment.genLinkButton(getShareUrl(workId), '点击去投票')
  return `这一天，还是来了！我发起的倩女手游最具影响力帮会已经通过审核了！兄弟们都操练起来！为我们帮会投票，奖励多到爆炸~${link}>>`
}

function addShareMoment (roleid, workId, imgList) {
  const text = getShareText(workId)
  const imgListStr = imgList.join(',')
  const imgAudit = _.fill(Array(imgList.length), Constants.STATUS_AUDIT_PASS).join(',')
  const values = {RoleId: roleid, CreateTime: Date.now(), Text: text, ImgList: imgListStr, ImgAudit: imgAudit}
  return PyqMoment.insert(values)
}

function enroll (payload) {
  const roleid = payload.roleid
  const workId = payload.workid
  const imgs = payload.imgs
  if (!roleid || !workId || !imgs) {
    return Promise.reject({code: -1, msg: 'InvalidArgument'})
  }
  if (FeatureToggle.isActive('qnmFengyun2018')) {
    return bluebird.all([
      setInitialFlowerRenQiToRedis(roleid),
      addShareMoment(roleid, workId, imgs)
    ])
  } else {
    return Promise.reject({code: -1, msg: 'activity is not opened!'})
  }
}

module.exports = {
  enroll: enroll,
  INIT_FLOWER_HASH_KEY: INIT_FLOWER_HASH_KEY,
  FLOWER_HASH_KEY: FLOWER_HASH_KEY
}
