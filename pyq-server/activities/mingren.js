"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.enroll = exports.MingRen = void 0;
const baseActivity_1 = require("./baseActivity");
const PyqMoments = require("../../models/PyqMoments");
var PostType;
(function (PostType) {
    PostType[PostType["YanZhi"] = 1] = "YanZhi";
    PostType[PostType["Game"] = 2] = "Game";
    PostType[PostType["GuangYing"] = 3] = "GuangYing";
    PostType[PostType["MiaoShou"] = 4] = "MiaoShou";
})(PostType || (PostType = {}));
let PostTypeToNames = {
    [PostType.YanZhi]: '颜值区',
    [PostType.Game]: '游戏达人区',
    [PostType.GuangYing]: '光影回声区',
    [PostType.MiaoShou]: '妙手丹青区'
};
class MingRenActivity extends baseActivity_1.Activity {
    getPreviewUrl(workId) {
        return `https://qnm.163.com/mingshi/mh/#/list/${workId}`;
    }
    getValidator(workInfo) {
        let validator = super.getValidator(workInfo);
        validator = validator.param('postType', { type: Number, values: [PostType.YanZhi, PostType.Game, PostType.GuangYing, PostType.MiaoShou] });
        return validator;
    }
    getShareText(workInfo) {
        const link = PyqMoments.genLinkButton(this.getPreviewUrl(workInfo.workId), '欣赏作品');
        let postTypeName = PostTypeToNames[workInfo.postType];
        return `我参加了倩女名士争夺战活动，参赛作品已经通过【${postTypeName}】的审核，小伙伴们快来为我点赞助力吧，还有机会参与抽奖哦~${link}`;
    }
}
exports.MingRen = new MingRenActivity({
    name: 'mingren',
    endDate: new Date('2018-09-07 23:59:59'),
    isCountFlowerRenQi: false,
    isCountFlowerVote: false
});
function enroll(workInfos) {
    return __awaiter(this, void 0, void 0, function* () {
        for (let w of workInfos) {
            yield exports.MingRen.addShareMoment(w);
        }
    });
}
exports.enroll = enroll;
//# sourceMappingURL=mingren.js.map