"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getMatchStage = exports.isCanVoteInMatch = exports.getJueSaiRoleIds = exports.getFuSaiRoleIds = exports.isCanVoteRenQi = exports.shareToMd = exports.recordPlayerRenQi = exports.onAttendTopic = exports.TOPIC_NAME = void 0;
const logger2_1 = require("../../common/logger2");
const PyqActivityTopic_1 = require("../../models/PyqActivityTopic");
const Moment = require("../../models/PyqMoments");
const util_1 = require("../../common/util");
const httpLib = require("../../common/request");
const _ = require("lodash");
const helper_1 = require("../helper");
const errorHandler = require("../errorHandler");
const activityTopic_1 = require("../services/activityTopic");
const PyqProfile = require("../../models/PyqProfile");
const redis_1 = require("../../common/redis");
const util2_1 = require("../../common/util2");
const activityTools_1 = require("./activityTools");
const cacheUtil_1 = require("../../common/cacheUtil");
const ModelManager = require('../../models/ModelManager');
const PyqEvent = ModelManager.getModelByTableName('pyq_event');
let logger = (0, logger2_1.getLogger)('l10_mingren2019');
let config = require('../../common/config');
let mrtConfig = config.mingRenTang2019;
const INIT_RENQI_HASH_KEY = 'qnm:activity:mingrentang_2019:init_renqi';
const CUR_RENQI_HASH_KEY = 'qnm:activity:mingrentang_2019:cur_renqi';
function getPlayerRenQi(roleId) {
    return __awaiter(this, void 0, void 0, function* () {
        let profile = (yield PyqProfile.findOne({ RoleId: roleId }, ['RenQi'])) || { RenQi: 0 };
        return profile.RenQi;
    });
}
exports.TOPIC_NAME = '#倩影风华#';
const ATTEND_MIN_IMAGE_NUM = 2;
const APPLY_API = 'http://file.mg.163.com/public/qnm/mingrentang_2019/mengdao/apply';
const NOTIFY_RENQI_CHANGE = 'http://file.mg.163.com/public/qnm/mingrentang_2019/mengdao/renqichange';
function onAttendTopic(params) {
    return __awaiter(this, void 0, void 0, function* () {
        let name = yield PyqActivityTopic_1.ActivityTopic.getNameById(params.topicId);
        if (name === exports.TOPIC_NAME) {
            attendByMomentId(params.momentId);
        }
    });
}
exports.onAttendTopic = onAttendTopic;
PyqEvent.on(PyqEvent.Events.SEND_FLOWER, function (payload) {
    const receiver = payload.receiver;
    recordPlayerRenQi(receiver, payload);
});
const linkRegex = /^<link\sbutton=.*>$/;
function isAddAttendText(text) {
    if (text) {
        return !text.match(linkRegex);
    }
    else {
        return false;
    }
}
function attendByMomentId(momentId) {
    return __awaiter(this, void 0, void 0, function* () {
        let record = yield Moment.findById(momentId, ['Text', 'ImgList', 'RoleId']);
        if (record) {
            let imgUrls = (0, util_1.csvStrToArray)(record.ImgList);
            if (imgUrls.length >= ATTEND_MIN_IMAGE_NUM && isAddAttendText(record.Text)) {
                requestAttendApi(record.RoleId, imgUrls, record.Text);
            }
        }
    });
}
function requestAttendApi(roleId, imgs, intr) {
    return __awaiter(this, void 0, void 0, function* () {
        imgs = imgs.map(url => (0, util2_1.replaceHttps)(url));
        let data = { roleid: roleId, 'imgs[]': imgs, intr: intr };
        logger.info('Prepare attend', data);
        let result = yield httpLib.request({
            method: "POST",
            url: APPLY_API,
            formData: data
        });
        logger.info('Request apply api result', { result: result, data: data });
    });
}
function getShareText(work) {
    return `我正在晒自拍赢大奖, 能否逆风翻盘等你一票<link button=点击投票吧,OpenUrlInGame,https://qnm.163.com/2019/mrt/mh/##/list/${work.workid}>`;
}
function addMdMoment(work) {
    return __awaiter(this, void 0, void 0, function* () {
        let topicId = yield PyqActivityTopic_1.ActivityTopic.getIdByName(exports.TOPIC_NAME);
        let momentId;
        let now = Date.now();
        let shareText = getShareText(work);
        if (topicId) {
            let text = (0, activityTopic_1.getActivityTopicFullText)(topicId, exports.TOPIC_NAME, shareText);
            let ret = yield Moment.insert({ RoleId: work.roleid, Text: text, ImgList: work.cover, ImgAudit: '1', CreateTime: now });
            momentId = ret.insertId;
            yield (0, activityTopic_1.addTopicMoment)(work.roleid, momentId, topicId, []);
        }
        else {
            let ret = yield Moment.insert({ RoleId: work.roleid, Text: shareText, ImgList: work.cover, ImgAudit: '1', CreateTime: now });
            momentId = ret.insertId;
        }
        yield initPlayerRenQi(work.roleid);
        return momentId;
    });
}
function initPlayerRenQi(roleId) {
    return __awaiter(this, void 0, void 0, function* () {
        let renqi = yield getPlayerRenQi(roleId);
        return (0, redis_1.getRedis)().hsetnxAsync(INIT_RENQI_HASH_KEY, '' + roleId, renqi);
    });
}
function isRoleIdAttend(roleId) {
    return __awaiter(this, void 0, void 0, function* () {
        let ret = yield (0, redis_1.getRedis)().hexistsAsync(INIT_RENQI_HASH_KEY, '' + roleId);
        return ret === redis_1.IExistResult.Exist;
    });
}
function notifyRenQiChange(roleId) {
    return __awaiter(this, void 0, void 0, function* () {
        logger.info('notifyRenQiChange', { roleId: roleId });
        let ret = yield httpLib.request({
            method: "POST",
            url: NOTIFY_RENQI_CHANGE,
            formData: { roleid: roleId }
        });
        logger.info('notifyRenQiChangeResult', { roleId: roleId, ret: ret });
        return ret;
    });
}
function recordPlayerRenQi(roleId, sendFlower) {
    return __awaiter(this, void 0, void 0, function* () {
        let isAttend = yield isRoleIdAttend(roleId);
        let isCanIncrRenQi = yield isCanVoteInMatch(roleId, new Date(), mrtConfig);
        let date = new Date();
        let stage = getMatchStage(date, mrtConfig);
        if (isAttend && isCanIncrRenQi) {
            let flower = sendFlower.flower;
            logger.info('RecordPlayerRenQi', { roleId: roleId, sendFlower: sendFlower });
            if (stage === "chusai") {
                let renQi = yield getPlayerRenQi(roleId);
                yield (0, redis_1.getRedis)().hsetAsync(CUR_RENQI_HASH_KEY, '' + roleId, renQi);
                yield notifyRenQiChange(roleId);
            }
            else if (stage === "fusai" || stage === "juesai") {
                yield (0, redis_1.getRedis)().hincrbyAsync(CUR_RENQI_HASH_KEY, '' + roleId, flower.renqi);
                yield notifyRenQiChange(roleId);
            }
        }
    });
}
exports.recordPlayerRenQi = recordPlayerRenQi;
function shareToMd(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            let schema = {
                works: { type: Array }
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            let params = req.params;
            let ids = [];
            for (let w of params.works) {
                let id = yield addMdMoment(w);
                ids.push(id);
            }
            res.send({ code: 0, data: { ids: ids } });
        }
        catch (err) {
            errorHandler(err, req, res, next);
        }
    });
}
exports.shareToMd = shareToMd;
function getCallApiPrefix() {
    if (config.testCfg.test_env) {
        return 'http://***************:8081';
    }
    else {
        return 'http://file.mg.163.com';
    }
}
function isCanVoteRenQi(params) {
    if (params.isLimitRoleId) {
        return _.includes(params.limitRoleIds, params.roleId);
    }
    else {
        return true;
    }
}
exports.isCanVoteRenQi = isCanVoteRenQi;
const fusaiRoleIdApi = getCallApiPrefix() + "/public/qnm/mingrentang_2019/mengdao/fusaidata";
const juesaiRoleidApi = getCallApiPrefix() + "/public/qnm/mingrentang_2019/mengdao/fusaidata";
function getApiByMatchStage(stage) {
    if (stage === "fusai") {
        return fusaiRoleIdApi;
    }
    else if (stage === "juesai") {
        return juesaiRoleidApi;
    }
    else {
        return "";
    }
}
class GetAttendRoleIdsCache extends cacheUtil_1.GenericCache {
    getExpireTime() {
        return 8 * 3600;
    }
    getKey(stage) {
        return "mingrentang2019:match:" + stage + ":roleIds";
    }
    fetchDataSource(stage) {
        return __awaiter(this, void 0, void 0, function* () {
            return getVotableRoleIds(stage);
        });
    }
}
function getVotableRoleIds(stage) {
    return __awaiter(this, void 0, void 0, function* () {
        let apiUrl = getApiByMatchStage(stage);
        if (apiUrl) {
            let apiReturn = yield httpLib.get(apiUrl);
            if (apiReturn.code === 1) {
                let list = Object.keys(apiReturn.data);
                let roleIds = list.map(x => parseInt(x, 10));
                return roleIds;
            }
            else {
                logger.error('GetAttendRoleidFailed', { state: stage, apiReturn });
                return [];
            }
        }
        else {
            return [];
        }
    });
}
let GetAttendRoleIds = new GetAttendRoleIdsCache();
function getFuSaiRoleIds() {
    return __awaiter(this, void 0, void 0, function* () {
        if (mrtConfig.skip_api_cache) {
            return getVotableRoleIds("fusai");
        }
        else {
            return GetAttendRoleIds.get("fusai");
        }
    });
}
exports.getFuSaiRoleIds = getFuSaiRoleIds;
function getJueSaiRoleIds() {
    return __awaiter(this, void 0, void 0, function* () {
        if (mrtConfig.skip_api_cache) {
            return getVotableRoleIds("juesai");
        }
        else {
            return GetAttendRoleIds.get("juesai");
        }
    });
}
exports.getJueSaiRoleIds = getJueSaiRoleIds;
function isCanVoteInMatch(roleId, date, mrtConfig) {
    return __awaiter(this, void 0, void 0, function* () {
        let stage = getMatchStage(date, mrtConfig);
        if (stage) {
            switch (stage) {
                case "chusai":
                    return true;
                case "fusai":
                    let fusaiRoleIds = yield getFuSaiRoleIds();
                    return _.includes(fusaiRoleIds, roleId);
                case "juesai":
                    let juesaiRoleIds = yield getJueSaiRoleIds();
                    return _.includes(juesaiRoleIds, roleId);
            }
        }
        else {
            return false;
        }
    });
}
exports.isCanVoteInMatch = isCanVoteInMatch;
function getMatchStage(date, mrtConfig) {
    if ((0, activityTools_1.isInDateRange)(date, mrtConfig.chusai)) {
        return "chusai";
    }
    else if ((0, activityTools_1.isInDateRange)(date, mrtConfig.fusai)) {
        return "fusai";
    }
    else if ((0, activityTools_1.isInDateRange)(date, mrtConfig.juesai)) {
        return "juesai";
    }
    else {
        return null;
    }
}
exports.getMatchStage = getMatchStage;
//# sourceMappingURL=mingRenTang2019.js.map