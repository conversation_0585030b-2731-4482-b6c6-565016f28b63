"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HouseDesignMatch = exports.EMatchStage = void 0;
const redis_1 = require("../../common/redis");
const PyqEvent = require("../../models/PyqEvent");
const PyqMoments = require("../../models/PyqMoments");
const DateUtil = require("../../common/dateUtil");
const bluebird = require("bluebird");
const helper_1 = require("../helper");
const util_1 = require("../../common/util");
const util2_1 = require("../../common/util2");
const logger_1 = require("../logger");
const activityTopic_1 = require("../services/activityTopic");
const PyqActivityTopic_1 = require("../../models/PyqActivityTopic");
var EMatchStage;
(function (EMatchStage) {
    EMatchStage["PRE"] = "pre";
    EMatchStage["SEMI_FINAL"] = "simi_final";
    EMatchStage["FINAL"] = "final";
})(EMatchStage = exports.EMatchStage || (exports.EMatchStage = {}));
const AuditStatus = {
    PASS: 0,
    Reject: -1
};
class HouseDesignMatch {
    constructor(option) {
        this.topicName = option.topicName;
        this.logger = logger_1.activityLogger.child({ activity: option.logName, topicName: this.topicName });
    }
    getMatchTypeByDate(date = new Date()) {
        let dateRange = this.getMatchDateRange();
        let types = Object.keys(dateRange);
        for (let p of types) {
            let r = dateRange[p];
            if (DateUtil.isWithInRange(date, r.start, r.end)) {
                return p;
            }
        }
        return null;
    }
    getAttendSetKey(matchType) {
        let prefix = this.getKeyPrefix();
        return `${prefix}:roleids:${matchType}`;
    }
    getFlowerVoteHashKey(matchType) {
        let prefix = this.getKeyPrefix();
        return `${prefix}:flower_vote:${matchType}_match`;
    }
    getFlowerRenQiHashKey(matchType) {
        let prefix = this.getKeyPrefix();
        return `${prefix}:flower_renqi:${matchType}_match`;
    }
    workAuditRejectHandler(roleid) {
        return __awaiter(this, void 0, void 0, function* () {
            let matchType = this.getMatchTypeByDate();
            if (matchType) {
                let voteKey = this.getFlowerVoteHashKey(matchType);
                let renqiKey = this.getFlowerRenQiHashKey(matchType);
                yield bluebird.all([
                    (0, redis_1.getRedis)().hdelAsync(voteKey, '' + roleid),
                    (0, redis_1.getRedis)().hdelAsync(renqiKey, '' + roleid),
                ]);
                yield this.removeFromAttendRoleIds(roleid);
            }
            else {
                return bluebird.resolve();
            }
        });
    }
    isRoleIdAttend(roleid) {
        return __awaiter(this, void 0, void 0, function* () {
            let matchType = this.getMatchTypeByDate();
            if (matchType) {
                let key = this.getAttendSetKey(matchType);
                return (0, redis_1.getRedis)().sismemberAsync(key, roleid);
            }
            else {
                return bluebird.resolve(false);
            }
        });
    }
    listenFlowerEvent() {
        let self = this;
        this.logger.info({ topicName: this.topicName }, 'ListenFlowerEvent');
        PyqEvent.on(PyqEvent.Events.SEND_FLOWER, function (payload) {
            return __awaiter(this, void 0, void 0, function* () {
                const receiver = payload.receiver;
                const flower = payload.flower;
                let matchType = self.getMatchTypeByDate();
                if (matchType) {
                    return self.isRoleIdAttend(receiver).then(isAttend => {
                        if (isAttend) {
                            let voteKey = self.getFlowerVoteHashKey(matchType);
                            let amount = HouseDesignMatch.flowerIdToVote[flower.id] * flower.count;
                            self.logger.info({ flower: payload, amount: amount }, 'ReceiveFlowerEvent');
                            let renqiKey = self.getFlowerRenQiHashKey(matchType);
                            return bluebird.all([
                                (0, redis_1.getRedis)().hincrbyAsync(voteKey, receiver, amount),
                                (0, redis_1.getRedis)().hincrbyAsync(renqiKey, receiver, flower.renqi)
                            ]);
                        }
                    });
                }
            });
        });
    }
    start() {
        this.listenFlowerEvent();
    }
    getShareText(workId, workName, topicId) {
        return __awaiter(this, void 0, void 0, function* () {
            let buttonName = this.getPreviewUrlButtonName();
            let link = this.getPreviewButton(workId, buttonName);
            let text = this.getShareTextByNameAndLink(workName, link);
            if (topicId) {
                return (0, activityTopic_1.getActivityTopicFullText)(topicId, this.topicName, text);
            }
            else {
                this.logger.warn({ text: text }, 'TopicNotCreated');
                return text;
            }
        });
    }
    getTopicId() {
        return __awaiter(this, void 0, void 0, function* () {
            let topicId = yield PyqActivityTopic_1.ActivityTopic.getIdByName(this.topicName);
            return topicId;
        });
    }
    getPreviewButton(workId, buttonName) {
        let url = this.getPreviewUrl(workId);
        const link = PyqMoments.genLinkButton(url, buttonName);
        return link;
    }
    roleAttendActivity(roleId) {
        let matchType = this.getMatchTypeByDate();
        let key = this.getAttendSetKey(matchType);
        if (matchType) {
            return (0, redis_1.getRedis)().saddAsync(key, roleId);
        }
        else {
            return bluebird.resolve();
        }
    }
    removeFromAttendRoleIds(roleId) {
        let matchType = this.getMatchTypeByDate();
        let key = this.getAttendSetKey(matchType);
        if (matchType) {
            return (0, redis_1.getRedis)().sremAsync(key, roleId);
        }
        else {
            return bluebird.resolve();
        }
    }
    enroll(params) {
        return __awaiter(this, void 0, void 0, function* () {
            let schema = {
                roleid: { type: Number },
                workid: { type: Number },
                workname: { type: String },
                imgs: { type: String },
                status: { type: Number }
            };
            yield (0, helper_1.checkParams)(params, schema);
            let roleId = params.roleid;
            let topicId = yield this.getTopicId();
            this.logger.info(params, 'ReceiveWork');
            let text = yield this.getShareText(params.workid, params.workname, topicId);
            if (params.status === AuditStatus.Reject) {
                return this.workAuditRejectHandler(roleId);
            }
            else {
                let imgList = (0, util_1.csvStrToArray)(params.imgs);
                let imgListStr = imgList.join(',');
                let imgAuditStr = (0, util2_1.fillCsvStr)('1', imgList.length);
                let record = {
                    RoleId: roleId,
                    Text: text,
                    ImgList: imgListStr,
                    ImgAudit: imgAuditStr,
                    CreateTime: Date.now()
                };
                let info = yield PyqMoments.insert(record);
                this.logger.info(record, 'InsertRecord');
                let momentId = info.insertId;
                if (topicId) {
                    yield (0, activityTopic_1.addTopicMoment)(params.roleid, momentId, topicId, imgList);
                    this.logger.info(record, 'InsertRecordToTopic');
                }
                yield this.roleAttendActivity(roleId);
                return { id: momentId };
            }
        });
    }
}
exports.HouseDesignMatch = HouseDesignMatch;
HouseDesignMatch.flowerIdToVote = {
    21100029: 1,
    21100030: 10,
    21100031: 220,
    21101865: 280 // 知己之名
};
//# sourceMappingURL=houseDesignMatchClass.js.map