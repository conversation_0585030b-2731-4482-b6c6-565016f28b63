/* eslint-disable prefer-promise-reject-errors */

const ModelManager = require('../../models/ModelManager')
const PyqMoments = ModelManager.getModelByTableName('pyq_moment')
const PyqEvent = require('../../models/PyqEvent')
const { getRedis } = require('../../common/redis')
const DateUtil = require('../../common/dateUtil')
const _ = require('lodash')
const Promise = require('bluebird')

const MatchTypes = {
  PRE: 'pre',
  SEMI_FINAL: 'simi_final',
  FINAL: 'final'
}

const MatchInfos = [
  { type: MatchTypes.PRE, start: new Date('2018-03-20 19:00'), end: new Date('2018-03-28 23:59') },
  { type: MatchTypes.SEMI_FINAL, start: new Date('2018-03-31 00:00'), end: new Date('2018-04-05 23:59') },
  { type: MatchTypes.FINAL, start: new Date('2018-04-11 19:00'), end: new Date('2018-04-11 21:00') }
]

const flowerIdToVote = {
  21100029: 1, // 玫瑰
  21100030: 10, // 绚丽玫瑰
  21100031: 220, // 以爱之名
  21101865: 280 // 知己之名
}

function getMatchTypeByDate (date) {
  date = date || new Date()
  let item = _.find(MatchInfos, info => DateUtil.isWithInRange(date, info.start, info.end))
  return _.get(item, 'type') || null
}

function roleAttendActivity (roleid) {
  let matchType = getMatchTypeByDate()
  if (matchType) {
    return getRedis().saddAsync(getAttendSetKey(matchType), roleid)
  } else {
    return Promise.resolve()
  }
}

function removeFromAttendRoleIds (roleid) {
  let matchType = getMatchTypeByDate()
  if (matchType) {
    return getRedis().sremAsync(getAttendSetKey(matchType), roleid)
  } else {
    return Promise.resolve()
  }
}

function isRoleIdAttend (roleid) {
  let matchType = getMatchTypeByDate()
  if (matchType) {
    return getRedis().sismemberAsync(getAttendSetKey(matchType), roleid)
  } else {
    return Promise.resolve(false)
  }
}

function getFlowerVoteHashKey (matchType) {
  return `qnm:activity:house_design:flower_vote:${matchType}_match`
}

function getFlowerRenQiHashKey (matchType) {
  return `qnm:activity:house_design:flower_renqi:${matchType}_match`
}

function getAttendSetKey (matchType) {
  return 'qnm:activity:house_design:roleids:' + matchType
}

PyqEvent.on(PyqEvent.Events.SEND_FLOWER, function (payload) {
  const receiver = payload.receiver
  const flower = payload.flower
  let matchType = getMatchTypeByDate()
  if (matchType) {
    return isRoleIdAttend(receiver).then(isAttend => {
      if (isAttend) {
        let voteKey = getFlowerVoteHashKey(matchType)
        let amount = flowerIdToVote[flower.id] * flower.count

        let renqiKey = getFlowerRenQiHashKey(matchType)

        return Promise.all([
          getRedis().hincrbyAsync(voteKey, receiver, amount),
          getRedis().hincrbyAsync(renqiKey, receiver, flower.renqi)
        ])
      }
    })
  }
})

function getPreviewUrl (workId) {
  return `https://qnm.163.com/m/jysjh/#/userwork/${workId}/`
}

function getShareText (workId, workName) {
  const link = PyqMoments.genLinkButton(getPreviewUrl(workId), '立刻围观')
  return `我参加了倩女手游的家园设计大赛，我的作品《${workName}》已经通过审核啦！小伙伴们快来为我打call送花吧，为我的家园增添人气！${link}`
}

const AuditStatus = {
  PASS: 0,
  Reject: -1
}

function workAuditRejectHandler (roleid) {
  let matchType = getMatchTypeByDate()
  if (matchType) {
    let voteKey = getFlowerVoteHashKey(matchType)
    let renqiKey = getFlowerRenQiHashKey(matchType)
    return Promise.all([
      getRedis().hdelAsync(voteKey, roleid),
      getRedis().hdelAsync(renqiKey, roleid)
    ]).then(() => {
      return removeFromAttendRoleIds(roleid)
    })
  } else {
    return Promise.resolve()
  }
}

function enroll (payload) {
  let roleid = payload.roleid
  let workid = payload.workid
  let workname = payload.workname
  let status = payload.status
  let text = getShareText(workid, workname)
  if (!roleid || !workid || !workname) {
    return Promise.reject({ code: -1, msg: 'InvalidArgument' })
  }
  let mid
  if (status === AuditStatus.Reject) {
    return workAuditRejectHandler(roleid)
  } else {
    return PyqMoments.insert({
      RoleId: roleid,
      Text: text,
      CreateTime: Date.now()
    }).then(info => {
      mid = info.insertId
      return roleAttendActivity(roleid)
    }).then(() => {
      return mid
    })
  }
}

module.exports = {
  enroll: enroll
}
