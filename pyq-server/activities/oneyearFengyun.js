/**
 * Created by <PERSON><PERSON><PERSON> on 2017/5/22.
 */

const PyqEvent = require('../../models/PyqEvent');
const PyqMoment = require('../../models/PyqMoments');
const ActivityHelper = require('./activityHelper');
const Promise = require('bluebird');
const { getRedis } = require('../../common/redis');
const FeatureToggle = require('../../common/FeatureToggle');

const INFLUENTIAL_MAN = 1;
const INFLUENTIAL_GANG = 2;

PyqEvent.on(PyqEvent.Events.SEND_FLOWER, function (payload) {
  const receiver = payload.receiver;
  onSendFlowerHandler(receiver);
});

const INIT_FLOWER_HASH_KEY = "qnm:activity:oneyear_fengyun:initflower";
const FLOWER_HASH_KEY = "qnm:activity:oneyear_fengyun:curflower";
function onSendFlowerHandler(roleId) {
  return isRoleIdAttend(roleId).then(isAttend => {
    if(isAttend) {
      return ActivityHelper.getFlowerRenQi(roleId)
        .then(renQi => {
          return setCurFlowerRenQiToRedis(roleId, renQi);
        });
    }
  });
}

function setCurFlowerRenQiToRedis(roleId, renQi) {
  FeatureToggle.whenActive("qnm_oneyear_fengyun", () => {
    return getRedis().hsetAsync(FLOWER_HASH_KEY, roleId, renQi);
  });
}

function setInitialFlowerRenQiToRedis(roleId, renQi) {
  FeatureToggle.whenActive("qnm_oneyear_fengyun", () => {
    return isRoleIdAttend(roleId).then((isAttend) => {
      if(!isAttend) {
        return getRedis().hsetAsync(INIT_FLOWER_HASH_KEY, roleId, renQi);
      }
    });
  });
}

function isRoleIdAttend(roleId) {
  return getRedis().hexistsAsync(INIT_FLOWER_HASH_KEY, roleId);
}

function addPyqMomentAndLink(roleId, workIds, type)  {
  return Promise.mapSeries(workIds, workId => {
    let text;
    if(type === INFLUENTIAL_MAN) {
      const link = PyqMoment.genLinkButton(`https://qnm.163.com/m/znpxh/#/person/${workId}/`, '查看详情');
      text = `我感觉我要火了！我被提名为倩女手游十大风云人物了！小伙伴快来为我投票吧，还有海量奖励拿~${link}>>`
    } else {
      const link = PyqMoment.genLinkButton(`https://qnm.163.com/m/znpxh/#/group/${workId}/`, '查看详情');
      text =  `这一天，还是来了！我发起的倩女手游最具影响力帮会已经通过审核了！兄弟们都操练起来！为我们帮会投票，奖励多到爆炸~${link}>>`;
    }
    return PyqMoment.insert({RoleId: roleId, CreateTime: Date.now(), Text: text});
  });
}


module.exports = {
  INFLUENTIAL_MAN: INFLUENTIAL_MAN,
  INFLUENTIAL_GANG: INFLUENTIAL_GANG,
  setCurFlowerRenQiToRedis: setCurFlowerRenQiToRedis,
  setInitialFlowerRenQiToRedis: setInitialFlowerRenQiToRedis,
  addPyqMomentAndLink: addPyqMomentAndLink,
};
