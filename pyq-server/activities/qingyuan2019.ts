
const PyqEvent = require('../../models/PyqEvent');
import * as PyqEvent from '../../models/PyqEvent'
const PyqMoment = require('../../models/PyqMoments');
const ActivityHelper = require('./activityHelper');
const { getRedis } = require('../../common/redis');
const FeatureToggle = require('../../common/FeatureToggle');
import * as bluebird from 'bluebird'
import { errorHandler } from '../helper';

PyqEvent.on(PyqEvent.Events.SEND_FLOWER, function (payload) {
    const receiver = payload.receiver;
    onSendFlowerHandler(receiver);
});

const activityName = 'qingyuan2019_activity'


const INIT_FLOWER_HASH_KEY = `qnm:${activityName}:initflower`;
const FLOWER_HASH_KEY = `qnm:${activityName}:curflower`;

function onSendFlowerHandler(roleId) {
    return isRoleIdAttend(roleId).then(isAttend => {
        if (isAttend) {
            return ActivityHelper.getFlowerRenQi(roleId)
                .then(renQi => {
                    return setCurFlowerRenQiToRedis(roleId, renQi);
                });
        }
    });
}

export function setCurFlowerRenQiToRedis(roleId, renQi) {
    FeatureToggle.whenActive(activityName, () => {
        return getRedis().hsetAsync(FLOWER_HASH_KEY, roleId, renQi);
    });
}

export function setInitialFlowerRenQiToRedis(roleId, renQi) {
    FeatureToggle.whenActive(activityName, () => {
        return isRoleIdAttend(roleId).then((isAttend) => {
            if (!isAttend) {
                return getRedis().hsetAsync(INIT_FLOWER_HASH_KEY, roleId, renQi);
            }
        });
    });
}

function isRoleIdAttend(roleId) {
    return getRedis().hexistsAsync(INIT_FLOWER_HASH_KEY, roleId);
}

export function addPyqMomentAndLink(roleId: number, workIds: number[], titleHash) {
    return bluebird.mapSeries(workIds, workId => {
        let viewLink = `https://qnm.163.com/2019/qygs/mh/#/list/${workId}`
        const link = PyqMoment.genLinkButton(viewLink, titleHash[workId]);
        return PyqMoment.insert({
            RoleId: roleId,
            CreateTime: Date.now(),
            Text: `点击《${link}》看看我和Ta的故事，为我们投上一票，一起参赛，幸运称号100%得!`
        });
    });
}

interface WorkInfo {
    roleid: number
    titles: { [key: string]: string }
    workids: number[]
}

export async function attendQingyuan2019(req, res, next) {
    try {
        let workInfos: WorkInfo[] = JSON.parse(req.params.work_infos)
        for (let w of workInfos) {
            const roleId = w.roleid
            const workIds = w.workids
            const titlesHash = w.titles
            if (roleId && workIds && titlesHash) {
                // set initial renqi
                let renqi = await ActivityHelper.getFlowerRenQi(roleId)
                await setInitialFlowerRenQiToRedis(roleId, renqi)

                // add moment
                addPyqMomentAndLink(roleId, workIds, titlesHash)
            } else {
                await bluebird.reject({ errorType: 'InvalidWork', message: '作品参数格式错误' })
            }
        }
        res.send({ code: 0, data: null })
    } catch (err) {
        errorHandler(err, req, res, next)
    }
}