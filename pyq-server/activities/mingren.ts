import { Activity, WorkInfo } from './baseActivity'
import * as PyqMoments from '../../models/PyqMoments'


enum PostType {
  YanZhi = 1,
  Game = 2,
  GuangYing = 3,
  MiaoShou = 4
}

interface MingRenWorkInfo extends WorkInfo {
  postType: PostType
}

let PostTypeToNames = {
  [PostType.YanZhi]: '颜值区',
  [PostType.Game]: '游戏达人区',
  [PostType.GuangYing]: '光影回声区',
  [PostType.MiaoShou]: '妙手丹青区'
}

class MingRenActivity extends Activity {
  protected getPreviewUrl(workId: number): string {
    return `https://qnm.163.com/mingshi/mh/#/list/${workId}`
  }

  getValidator(workInfo: MingRenWorkInfo) {
    let validator = super.getValidator(workInfo)
    validator = validator.param('postType', { type: Number, values: [PostType.YanZhi, PostType.Game, PostType.GuangYing, PostType.MiaoShou] })
    return validator
  }

  protected getShareText(workInfo: MingRenWorkInfo): string {
    const link = PyqMoments.genLinkButton(this.getPreviewUrl(workInfo.workId), '欣赏作品')
    let postTypeName = PostTypeToNames[workInfo.postType]
    return `我参加了倩女名士争夺战活动，参赛作品已经通过【${postTypeName}】的审核，小伙伴们快来为我点赞助力吧，还有机会参与抽奖哦~${link}`
  }
}

export let MingRen = new MingRenActivity({
  name: 'mingren',
  endDate: new Date('2018-09-07 23:59:59'),
  isCountFlowerRenQi: false,
  isCountFlowerVote: false
})

export async function enroll(workInfos: MingRenWorkInfo[]) {
  for (let w of workInfos) {
    await MingRen.addShareMoment(w)
  }
}