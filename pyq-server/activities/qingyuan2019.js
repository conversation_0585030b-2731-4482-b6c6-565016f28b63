"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.attendQingyuan2019 = exports.addPyqMomentAndLink = exports.setInitialFlowerRenQiToRedis = exports.setCurFlowerRenQiToRedis = void 0;
const PyqEvent = require('../../models/PyqEvent');
const PyqMoment = require('../../models/PyqMoments');
const ActivityHelper = require('./activityHelper');
const { getRedis } = require('../../common/redis');
const FeatureToggle = require('../../common/FeatureToggle');
const bluebird = require("bluebird");
const helper_1 = require("../helper");
PyqEvent.on(PyqEvent.Events.SEND_FLOWER, function (payload) {
    const receiver = payload.receiver;
    onSendFlowerHandler(receiver);
});
const activityName = 'qingyuan2019_activity';
const INIT_FLOWER_HASH_KEY = `qnm:${activityName}:initflower`;
const FLOWER_HASH_KEY = `qnm:${activityName}:curflower`;
function onSendFlowerHandler(roleId) {
    return isRoleIdAttend(roleId).then(isAttend => {
        if (isAttend) {
            return ActivityHelper.getFlowerRenQi(roleId)
                .then(renQi => {
                return setCurFlowerRenQiToRedis(roleId, renQi);
            });
        }
    });
}
function setCurFlowerRenQiToRedis(roleId, renQi) {
    FeatureToggle.whenActive(activityName, () => {
        return getRedis().hsetAsync(FLOWER_HASH_KEY, roleId, renQi);
    });
}
exports.setCurFlowerRenQiToRedis = setCurFlowerRenQiToRedis;
function setInitialFlowerRenQiToRedis(roleId, renQi) {
    FeatureToggle.whenActive(activityName, () => {
        return isRoleIdAttend(roleId).then((isAttend) => {
            if (!isAttend) {
                return getRedis().hsetAsync(INIT_FLOWER_HASH_KEY, roleId, renQi);
            }
        });
    });
}
exports.setInitialFlowerRenQiToRedis = setInitialFlowerRenQiToRedis;
function isRoleIdAttend(roleId) {
    return getRedis().hexistsAsync(INIT_FLOWER_HASH_KEY, roleId);
}
function addPyqMomentAndLink(roleId, workIds, titleHash) {
    return bluebird.mapSeries(workIds, workId => {
        let viewLink = `https://qnm.163.com/2019/qygs/mh/#/list/${workId}`;
        const link = PyqMoment.genLinkButton(viewLink, titleHash[workId]);
        return PyqMoment.insert({
            RoleId: roleId,
            CreateTime: Date.now(),
            Text: `点击《${link}》看看我和Ta的故事，为我们投上一票，一起参赛，幸运称号100%得!`
        });
    });
}
exports.addPyqMomentAndLink = addPyqMomentAndLink;
function attendQingyuan2019(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            let workInfos = JSON.parse(req.params.work_infos);
            for (let w of workInfos) {
                const roleId = w.roleid;
                const workIds = w.workids;
                const titlesHash = w.titles;
                if (roleId && workIds && titlesHash) {
                    // set initial renqi
                    let renqi = yield ActivityHelper.getFlowerRenQi(roleId);
                    yield setInitialFlowerRenQiToRedis(roleId, renqi);
                    // add moment
                    addPyqMomentAndLink(roleId, workIds, titlesHash);
                }
                else {
                    yield bluebird.reject({ errorType: 'InvalidWork', message: '作品参数格式错误' });
                }
            }
            res.send({ code: 0, data: null });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.attendQingyuan2019 = attendQingyuan2019;
//# sourceMappingURL=qingyuan2019.js.map