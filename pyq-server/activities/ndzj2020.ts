import _ = require("lodash");
import { format } from "util";
import { RankList } from "../../common/cacheUtil";
import { errorHandler, checkParams } from "../helper";
import { ndzj2020Cfg } from "../common/config";
import { EActivityType, EWishType, WishlistModel, WishlistRecord } from "../models/wishlist";
import { WishlistHelpModel, WishlistHelpRecord } from "../models/wishlistHelp";
import { addMomentForActivity } from "../services/activity";
import { getRoleInfoMap } from "../services/roleInfo";
import { wishlistReq } from "../type/req";
import { IWishHelpFinishPayload } from "../types";
import { getActivityLogger } from "./activityTools";
import { MomentModel } from "../services/modelProxy";
import { PyqMomentRecord } from "../../common/type";
import { csvStrToArray } from "../../common/util";
import { ActivityTopicModel } from "../../models/PyqActivityTopic";
import { getActivityTopicLinkButton } from "../services/activityTopic";
let logger = getActivityLogger(ndzj2020Cfg.activityName);

async function getTopicId() {
  return ActivityTopicModel.getIdByName(ndzj2020Cfg.topicName)
}

export function getShareText(wishId: string, topicId: number) {
  let topicButton = getActivityTopicLinkButton(topicId, ndzj2020Cfg.topicName)
  let text = format(ndzj2020Cfg.momentTextTemplate, wishId, topicButton);
  return text
}

export async function onAddWishShareNDZJ2020Moment(params: wishlistReq.add) {
  let topicId = await getTopicId()
  let text = getShareText(params.wishId, topicId)
  logger.info(params, "onAddWishShareNDZJ2020Moment");
  let id = await addMomentForActivity({ roleId: params.roleid, text: text, imgs: params.shareImgs, skipImgAudit: 1 });
  return id;
}

const HelpRankList = RankList.create(ndzj2020Cfg.helpRankKey, ndzj2020Cfg.helpRankExpireTime);

export async function getHelpSum(roleId: number) {
  let query = WishlistHelpModel.scope()
    .sum("Progress as sum")
    .from(WishlistHelpModel.tableName + " as h")
    .innerJoin(WishlistModel.tableName + " as w", "h.WishId", "w.WishId")
    .where("w.ActivityId", EActivityType.NDZJ2020)
    .where("w.Type", EWishType.Item)
    .where("h.RoleId", roleId);
  let rows: { sum: number }[] = await WishlistHelpModel.executeByQuery(query);
  logger.debug({ rows, roleId }, "getHelpSum");
  if (rows && rows.length > 0) {
    return rows[0].sum || 0;
  } else {
    return 0;
  }
}

export async function onWishHelpFinish(payload: IWishHelpFinishPayload) {
  let { roleId } = payload;
  let progress = await getHelpSum(roleId);
  logger.info({ roleId, progress: progress }, "onWishHelpFinish");
  if (progress > 0) {
    await HelpRankList.add(roleId, progress);
  }
}

interface HelpFullFillWishPlayerItem {
  rank: number;
  roleId: number;
  serverId: number
  roleName: string;
  progress: number;
}

export async function getRank(): Promise<HelpFullFillWishPlayerItem[]> {
  let list = await HelpRankList.list(ndzj2020Cfg.rankSize);
  let myList = list.map(function (r) {
    return { roleId: _.toNumber(r.member), progress: _.toNumber(r.score) };
  });
  let roleIds = myList.map((r) => r.roleId);
  let roleInfoMap = await getRoleInfoMap(roleIds);
  let rank = 1;
  let retList = myList.map((r) => {
    let roleInfo = roleInfoMap.get(r.roleId) || { RoleName: "", server_id: 0 };
    let item: HelpFullFillWishPlayerItem = {
      rank: rank,
      roleId: r.roleId,
      serverId: roleInfo.server_id,
      roleName: roleInfo.RoleName,
      progress: r.progress,
    };
    rank += 1;
    return item;
  });
  return retList;
}

export async function getRecentHelps() {
  let query = WishlistHelpModel.scope()
    .from(WishlistHelpModel.tableName + " as h")
    .innerJoin(WishlistModel.tableName + " as w", "h.WishId", "w.WishId")
    .where("w.ActivityId", EActivityType.NDZJ2020)
    .where("w.Type", EWishType.Item)
    .select("h.*")
    .orderBy("h.CreateTime", "desc")
    .limit(ndzj2020Cfg.recentHelpSize);
  let rows: WishlistHelpRecord[] = await WishlistHelpModel.executeByQuery(query);
  let roleIds = _.flatMap(rows, (r) => [r.RoleId, r.TargetId]);
  let roleInfoMap = await getRoleInfoMap(roleIds);
  let getName = function (roleId: number) {
    let record = roleInfoMap.get(roleId) || { RoleName: "" };
    return record.RoleName;
  };
  let list = rows.map((r) => {
    let item = {
      id: r.ID,
      roleId: r.RoleId,
      roleName: getName(r.RoleId),
      targetId: r.RoleId,
      targetName: getName(r.TargetId),
      createTime: r.CreateTime,
    };
    return item;
  });
  return list;
}

export async function wishHelpRankHandler(req, res, next) {
  try {
    let rank = await getRank();
    let data = { list: rank };
    res.send({ code: 0, data: data });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function recentHelpHandler(req, res, next) {
  try {
    let helps = await getRecentHelps();
    let data = { list: helps, count: helps.length };
    res.send({ code: 0, data: data });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function mostLikedMomentHandler(req, res, next) {
  try {
    let schema = {
      roleId: { type: Number },
    };
    await checkParams(req.params, schema);
    let params: { roleId: number } = req.params;

    let startTs = new Date(ndzj2020Cfg.startDate).getTime();
    let endTs = new Date(ndzj2020Cfg.endDate).getTime();
    let query = MomentModel.normalScope()
      .where("RoleId", params.roleId)
      // 点赞数大于100
      .whereRaw("HotState regexp '\"like\":[[:digit:]]{3}'")
      .whereBetween("CreateTime", [startTs, endTs]);

    let rows: PyqMomentRecord[] = await MomentModel.executeByQuery(query);

    let maxLikedMoment = _.maxBy(rows, (r) => {
      let state = MomentModel.formatHotState(r.HotState);
      return state.like || 0;
    });

    let hasHotMoment = !!maxLikedMoment;

    let data = {
      hasHotMoment: hasHotMoment,
      moment: null,
    };

    if (hasHotMoment) {
      let likeCount = MomentModel.formatHotState(maxLikedMoment.HotState).like;
      let imgList = csvStrToArray(maxLikedMoment.ImgList);
      let videoList = csvStrToArray(maxLikedMoment.VideoList);
      data.moment = {
        id: maxLikedMoment.ID,
        likeCount: likeCount,
        text: maxLikedMoment.Text,
        imgList: imgList,
        videoList: videoList,
        createTime: maxLikedMoment.CreateTime,
      };
    }

    res.send({ code: 0, data: data });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}