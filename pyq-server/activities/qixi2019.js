"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.shareToMd = exports.onAttendTopic = exports.TOPIC_NAME = void 0;
const logger2_1 = require("../../common/logger2");
const PyqActivityTopic_1 = require("../../models/PyqActivityTopic");
const Moment = require("../../models/PyqMoments");
const util_1 = require("../../common/util");
const httpLib = require("../../common/request");
const bluebird = require("bluebird");
const helper_1 = require("../helper");
const errorHandler = require("../errorHandler");
const activityTopic_1 = require("../services/activityTopic");
const util2_1 = require("../../common/util2");
const activityHelper_1 = require("./activityHelper");
const activityTools_1 = require("./activityTools");
let logger = (0, logger2_1.getLogger)('l10_qixi2019');
exports.TOPIC_NAME = '七夕星光印记';
const ATTEND_MIN_IMAGE_NUM = 2;
const APPLY_API = activityHelper_1.API_HOST + '/public/qnm/qixi_2019/client/apply/mengdao';
function onAttendTopic(params) {
    return __awaiter(this, void 0, void 0, function* () {
        let name = yield PyqActivityTopic_1.ActivityTopic.getNameById(params.topicId);
        if (name === exports.TOPIC_NAME) {
            return attendByMomentId(params.momentId);
        }
    });
}
exports.onAttendTopic = onAttendTopic;
function attendByMomentId(momentId) {
    return __awaiter(this, void 0, void 0, function* () {
        let record = yield Moment.findById(momentId, ['Text', 'ImgList', 'RoleId']);
        if (record) {
            let imgUrls = (0, util_1.csvStrToArray)(record.ImgList);
            if (imgUrls.length >= ATTEND_MIN_IMAGE_NUM && !(0, activityTools_1.isOnlyContainTopic)(record.Text)) {
                let text = (0, activityTools_1.removeTopicLink)(record.Text);
                return requestAttendApi(record.RoleId, imgUrls, text);
            }
        }
    });
}
function requestAttendApi(roleId, imgs, intr) {
    return __awaiter(this, void 0, void 0, function* () {
        imgs = imgs.map(url => (0, util2_1.replaceHttps)(url));
        let data = { roleId: roleId, workImgs: imgs, mark: intr };
        logger.info('Prepare attend', data);
        let result = yield httpLib.request({
            method: "POST",
            url: APPLY_API,
            body: data
        });
        logger.info('Request apply api result', { result: result, data: data });
        return result;
    });
}
function getShareText(work) {
    return `我点亮了七夕夜空中的一颗星星，摘下送给你<link button=点击查看,OpenUrlInGame,https://qnm.163.com/2019/qxjt/mh/##/list/${work.workid}>`;
}
function addMdMoment(work) {
    return __awaiter(this, void 0, void 0, function* () {
        let topicId = yield PyqActivityTopic_1.ActivityTopic.getIdByName(exports.TOPIC_NAME);
        let momentId;
        let now = Date.now();
        let shareText = getShareText(work);
        if (topicId) {
            let text = (0, activityTopic_1.getActivityTopicFullText)(topicId, exports.TOPIC_NAME, shareText);
            let ret = yield Moment.insert({ RoleId: work.roleid, Text: text, ImgList: work.cover, ImgAudit: '1', CreateTime: now });
            momentId = ret.insertId;
            yield (0, activityTopic_1.addTopicMoment)(work.roleid, momentId, topicId, []);
        }
        else {
            logger.error({ work: work }, 'topic not create for share moment');
            yield bluebird.reject({ errorType: 'TopicNotExist', message: `topic: ${exports.TOPIC_NAME} not exist` });
        }
        return momentId;
    });
}
function shareToMd(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            let schema = {
                works: { type: Array }
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            let params = req.params;
            let ids = [];
            for (let w of params.works) {
                let id = yield addMdMoment(w);
                ids.push(id);
            }
            res.send({ code: 0, data: { ids: ids } });
        }
        catch (err) {
            errorHandler(err, req, res, next);
        }
    });
}
exports.shareToMd = shareToMd;
//# sourceMappingURL=qixi2019.js.map