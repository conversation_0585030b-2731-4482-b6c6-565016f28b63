/* eslint-disable prefer-promise-reject-errors */
const PyqEvent = require('../../models/PyqEvent')
const ModelManager = require('../../models/ModelManager')
const PyqMoments = ModelManager.getModelByTableName('pyq_moment')
const ActivityHelper = require('./activityHelper')
const { getRedis } = require('../../common/redis')

const INIT_FLOWER_HASH_KEY = 'qnm:activity:good_voice:initflower'
const FLOWER_HASH_KEY = 'qnm:activity:good_voice:curflower'
const MD_VOTE_KEY = 'qnm:activity:good_voice:md_vote'
const MOMENT_ID_TO_WORK_ID_KEY = 'qnm:activity:good_voice:mid_to_workid'

PyqEvent.on(PyqEvent.Events.SEND_FLOWER, function (payload) {
  const receiver = payload.receiver
  onSendFlowerHandler(receiver)
})

// record flower to redis
function isRoleIdAttend (roleId) {
  return getRedis().hexistsAsync(INIT_FLOWER_HASH_KEY, roleId)
}

ActivityHelper.subsribeMomentLikesChange(payload => {
  let momentId = payload.momentId
  return getRedis().hgetAsync(MOMENT_ID_TO_WORK_ID_KEY, momentId).then(workId => {
    if (workId) {
      return getRedis().hincrbyAsync(MD_VOTE_KEY, workId, payload.change)
    }
  })
})

function onSendFlowerHandler (roleId) {
  return isRoleIdAttend(roleId).then(isAttend => {
    if (isAttend) {
      return ActivityHelper.getFlowerRenQi(roleId)
        .then(renQi => {
          return getRedis().hsetAsync(FLOWER_HASH_KEY, roleId, renQi)
        })
    }
  })
}

function setInitialFlowerRenQiToRedis (roleId) {
  return isRoleIdAttend(roleId).then(isAttend => {
    if (!isAttend) {
      return ActivityHelper.getFlowerRenQi(roleId).then(renQi => {
        return getRedis().hsetAsync(INIT_FLOWER_HASH_KEY, roleId, renQi)
      })
    }
  })
}

// handle share loginc
function getPreviewUrl (workId) {
  return `https://qnm.163.com/m/hsyh/#/list/${workId}/`
}

function getShareText (workId, workName) {
  const link = PyqMoments.genLinkButton(getPreviewUrl(workId), '立刻围观')
  return `我参加了倩女好声音活动，作品 "${workName}" 已经通过审核啦，想听听我的声音吗？那就快来为我加油打call吧!!!${link}`
}

function setInitalState (roleid, workid, mid) {
  return Promise.all([
    setInitialFlowerRenQiToRedis(roleid),
    getRedis().hsetAsync(MOMENT_ID_TO_WORK_ID_KEY, mid, workid)
  ])
}

function enroll (payload) {
  let roleid = payload.roleid
  let workid = payload.workid
  let workname = payload.workname
  let text = getShareText(workid, workname)
  if (!roleid || !workid || !workname) {
    return Promise.reject({code: -1, msg: 'InvalidArgument'})
  }
  let mid
  return PyqMoments.insert({
    RoleId: roleid,
    Text: text,
    CreateTime: Date.now()
  }).then(info => {
    mid = info.insertId
    return setInitalState(roleid, workid, mid)
  }).then(() => {
    return mid
  })
}

module.exports = {
  enroll: enroll
}
