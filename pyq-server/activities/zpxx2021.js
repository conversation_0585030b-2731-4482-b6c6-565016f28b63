"use strict";
// 宗派选秀2021 活动
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getMatchStage = exports.isCanVoteInMatch = exports.getFuSaiRoleIds = exports.isCanVoteRenQi = exports.recordPlayerRenQi = exports.onSendFlower = exports.onAttendTopic = exports.getMatchStageRenQiKey = void 0;
const util_1 = require("../../common/util");
const httpLib = require("../../common/request");
const _ = require("lodash");
const redis_1 = require("../../common/redis");
const util2_1 = require("../../common/util2");
const activityTools_1 = require("./activityTools");
const cacheUtil_1 = require("../../common/cacheUtil");
const modelProxy_1 = require("../services/modelProxy");
const config_1 = require("../common/config");
const PyqActivityTopic_1 = require("../../models/PyqActivityTopic");
const type_1 = require("./type");
const dateUtil_1 = require("../../common/dateUtil");
let logger = (0, activityTools_1.getActivityLogger)(config_1.zpxx2021Cfg.name);
const INIT_RENQI_HASH_KEY = `qnm:activity:${config_1.zpxx2021Cfg.name}:init_renqi`;
function getMatchStageRenQiKey(stage) {
    return `qnm:activity:${config_1.zpxx2021Cfg.name}:${stage}_renqi`;
}
exports.getMatchStageRenQiKey = getMatchStageRenQiKey;
function getPlayerRenQi(roleId) {
    return __awaiter(this, void 0, void 0, function* () {
        let profile = (yield modelProxy_1.ProfileModel.findOne({ RoleId: roleId }, ["RenQi"])) || { RenQi: 0 };
        return profile.RenQi;
    });
}
function onAttendTopic(params) {
    return __awaiter(this, void 0, void 0, function* () {
        logger.debug(params, "onAttendTopic");
        let name = yield PyqActivityTopic_1.ActivityTopic.getNameById(params.topicId);
        logger.debug({ name, params }, "onAttendTopicName");
        if (name === config_1.zpxx2021Cfg.topicName) {
            if (params.origin === type_1.AddMomentToTopicOrigin.Game) {
                attendByMomentId(params.momentId);
            }
            else if (params.origin === type_1.AddMomentToTopicOrigin.Activity) {
                // 活动报名成功后会分享一条动态到活动话题下的梦岛
                logger.info({ params }, "initPlayerRenQiAfterSignUpSuccess");
                initPlayerRenQi(params.roleId);
            }
        }
    });
}
exports.onAttendTopic = onAttendTopic;
function onSendFlower(payload) {
    return __awaiter(this, void 0, void 0, function* () {
        const receiver = payload.receiver;
        recordPlayerRenQi(receiver, payload);
    });
}
exports.onSendFlower = onSendFlower;
const linkRegex = /^<link\sbutton=.*>$/;
function isAddAttendText(text) {
    if (text) {
        return !text.match(linkRegex);
    }
    else {
        return false;
    }
}
function attendByMomentId(momentId) {
    return __awaiter(this, void 0, void 0, function* () {
        let record = yield modelProxy_1.MomentModel.findOne({ ID: momentId }, ["Text", "ImgList", "RoleId"]);
        if (record) {
            let imgUrls = (0, util_1.csvStrToArray)(record.ImgList);
            if (imgUrls.length >= config_1.zpxx2021Cfg.attendMinImage && isAddAttendText(record.Text)) {
                try {
                    yield requestAttendApi(record.RoleId, imgUrls, record.Text);
                }
                catch (err) {
                    logger.error({ err: err }, "AttendByMomentIdFailed");
                }
            }
        }
    });
}
function requestAttendApi(roleId, imgs, intr) {
    return __awaiter(this, void 0, void 0, function* () {
        const worksUrl = imgs.map((url) => {
            return {
                type: "img",
                url: (0, util2_1.replaceHttps)(url),
            };
        });
        let mark = (0, activityTools_1.removeTopicLink)(intr);
        const coverUrl = worksUrl[0].url;
        let data = {
            roleid: roleId,
            worksUrl,
            cover: { type: "img", url: coverUrl },
            source: "md",
            mark: mark,
        };
        const applyApi = (0, activityTools_1.getProxiedUrl)(config_1.zpxx2021Cfg.applyApi);
        logger.info({ data, url: applyApi }, "PrepareAttend");
        let result = yield httpLib.request({
            method: "POST",
            url: applyApi,
            body: data,
        });
        logger.info({ result: result, data: data }, "ApplyApiReturn");
    });
}
function initPlayerRenQi(roleId) {
    return __awaiter(this, void 0, void 0, function* () {
        let renqi = yield getPlayerRenQi(roleId);
        return (0, redis_1.getRedis)().hsetnxAsync(INIT_RENQI_HASH_KEY, "" + roleId, renqi);
    });
}
function isRoleIdAttend(roleId) {
    return __awaiter(this, void 0, void 0, function* () {
        let ret = yield (0, redis_1.getRedis)().hexistsAsync(INIT_RENQI_HASH_KEY, "" + roleId);
        return ret === redis_1.IExistResult.Exist;
    });
}
function recordPlayerRenQi(roleId, sendFlower) {
    return __awaiter(this, void 0, void 0, function* () {
        let isAttend = yield isRoleIdAttend(roleId);
        let isCanIncrRenQi = false;
        if (isAttend) {
            isCanIncrRenQi = yield isCanVoteInMatch(roleId, new Date());
        }
        let date = new Date();
        let stage = getMatchStage(date);
        logger.info({ roleId, sendFlower, stage, isAttend, isCanIncrRenQi }, "PrepareUpdateRenQi");
        if (isAttend && isCanIncrRenQi) {
            const isVoteRenqiStart = (0, dateUtil_1.isAfter)(new Date(), new Date(config_1.zpxx2021Cfg.renqiStartDate));
            if (isVoteRenqiStart) {
                let flower = sendFlower.flower;
                let hashKey = getMatchStageRenQiKey(stage);
                yield (0, redis_1.getRedis)().hincrbyAsync(hashKey, "" + roleId, flower.renqi);
                yield notifyRenQiChange(roleId);
            }
            else {
                logger.info({ roleId, sendFlower, stage, isAttend, isCanIncrRenQi }, "SendFlowAfterApplyBeforeVoteWillReInitRenQi");
                initPlayerRenQi(roleId);
            }
        }
    });
}
exports.recordPlayerRenQi = recordPlayerRenQi;
function isCanVoteRenQi(params) {
    if (params.isLimitRoleId) {
        return _.includes(params.limitRoleIds, params.roleId);
    }
    else {
        return true;
    }
}
exports.isCanVoteRenQi = isCanVoteRenQi;
function getApiByMatchStage(stage) {
    if (stage === "fusai") {
        return config_1.zpxx2021Cfg.fusaiRoleIdApi;
    }
    else {
        return "";
    }
}
class GetAttendRoleIdsCache extends cacheUtil_1.GenericCache {
    getExpireTime() {
        return 8 * 3600;
    }
    getKey(stage) {
        return `l10_${config_1.zpxx2021Cfg.name}:match:${stage}:roleIds`;
    }
    fetchDataSource(stage) {
        return __awaiter(this, void 0, void 0, function* () {
            return getVotableRoleIds(stage);
        });
    }
}
function getVotableRoleIds(stage) {
    return __awaiter(this, void 0, void 0, function* () {
        let apiUrl = getApiByMatchStage(stage);
        if (apiUrl) {
            let apiReturn = yield httpLib.get(apiUrl);
            if (apiReturn.code === 1) {
                let list = Object.keys(apiReturn.data);
                let roleIds = list.map((x) => parseInt(x, 10));
                return roleIds;
            }
            else {
                logger.error({ state: stage, apiReturn }, "GetAttendRoleidFailed");
                return [];
            }
        }
        else {
            return [];
        }
    });
}
let GetAttendRoleIds = new GetAttendRoleIdsCache();
function getFuSaiRoleIds() {
    return __awaiter(this, void 0, void 0, function* () {
        if (config_1.zpxx2021Cfg.skip_api_cache) {
            return getVotableRoleIds("fusai");
        }
        else {
            return GetAttendRoleIds.get("fusai");
        }
    });
}
exports.getFuSaiRoleIds = getFuSaiRoleIds;
function isCanVoteInMatch(roleId, date) {
    return __awaiter(this, void 0, void 0, function* () {
        let stage = getMatchStage(date);
        if (stage) {
            switch (stage) {
                case "chusai":
                    return true;
                case "fusai":
                    let fusaiRoleIds = yield getFuSaiRoleIds();
                    const isInFuSai = _.includes(fusaiRoleIds, roleId);
                    if (!isInFuSai) {
                        logger.warn({ stage: "fusai", fusaiRoleIds, roleId }, "isCanVoteInMatchFailed");
                    }
                    return isInFuSai;
            }
        }
        else {
            return false;
        }
    });
}
exports.isCanVoteInMatch = isCanVoteInMatch;
function getMatchStage(date) {
    if ((0, activityTools_1.isInDateRange)(date, config_1.zpxx2021Cfg.timeRange.chusai)) {
        return "chusai";
    }
    else if ((0, activityTools_1.isInDateRange)(date, config_1.zpxx2021Cfg.timeRange.fusai)) {
        return "fusai";
    }
    else {
        return null;
    }
}
exports.getMatchStage = getMatchStage;
function notifyRenQiChange(roleId) {
    return __awaiter(this, void 0, void 0, function* () {
        const rqOption = {
            method: "POST",
            url: (0, activityTools_1.getProxiedUrl)(config_1.zpxx2021Cfg.renqiChangeApi),
            qs: { roleid: roleId },
        };
        logger.info({ roleId: roleId, rqOption }, "NotifyRenQiChangeBegin");
        let ret = yield httpLib.request(rqOption);
        logger.info({ rqOption, ret: ret }, "NotifyRenQiChangeResult");
        return ret;
    });
}
//# sourceMappingURL=zpxx2021.js.map