/* eslint-disable prefer-promise-reject-errors */
// 侠侣家园大赛
const Promise = require('bluebird')
const PyqMoments = require('../../models/PyqMoments')
const util = require('../../common/util')
const { getRedis } = require('../../common/redis')
const co = util.co
const _ = require('lodash')

const redisNameSpacePrefix = 'qnm:activity:couple_house:'
const passedStatus = 1
const rejectStatus = 2

PyqMoments.on(PyqMoments.Events.LIKE_MOMENT, function (payload) {
  const momentId = payload.momentId
  return getWorkIdByMomentId(momentId).then(workId => {
    if (workId) {
      updateWorkLikeValue(workId, 1)
    }
  })
})

PyqMoments.on(PyqMoments.Events.Cancel_LIKE_MOMENT, function (payload) {
  const momentId = payload.momentId
  return getWorkIdByMomentId(momentId).then(workId => {
    if (workId) {
      updateWorkLikeValue(workId, -1)
    }
  })
})

function getPreviewUrl (workId) {
  return `https://qnm.163.com/m/xljyh/#/exhibition/detail/${workId}`
}

function getShareText (workId) {
  const link = PyqMoments.genLinkButton(getPreviewUrl(workId), '我的家园')
  return `我正在参加《侠侣家园PK赛》，请各位大佬、小仙女帮我点赞啦～点赞有机会获得红色称号哦 #163欢迎参观${link}`
}

function insertMoment (roleId, workId) {
  return PyqMoments.insert({
    RoleId: roleId,
    Text: getShareText(workId),
    CreateTime: Date.now()
  }).then(insertInfo => {
    return insertInfo.insertId
  })
}

function updateWorkLikeValue (workId, changeValue) {
  return getRedis().hincrbyAsync(getWorkLikesKey(), workId, changeValue)
}

function getWorkIdLikesCount (workId) {
  return getRedis().hgetAsync(getWorkLikesKey(), workId)
  .then(count => {
    return count || 0
  })
}

function buildMapping (mIds, workId) {
  return Promise.map(mIds, mId => {
    return getRedis().hsetAsync(getMappingKeyForMomentId(), mId, workId)
  }).then(() => {
    return getRedis().hsetAsync(getMappingKeyForWorkId(), workId, JSON.stringify(mIds))
  })
}

function getWorkLikesKey () {
  return redisNameSpacePrefix + 'md_likes'
}

function getMappingKeyForMomentId () {
  return redisNameSpacePrefix + 'moment_id_to_work_id'
}

function getMappingKeyForWorkId () {
  return redisNameSpacePrefix + 'work_id_to_moment_ids'
}

function getWorkIdByMomentId (momentId) {
  return getRedis().hgetAsync(getMappingKeyForMomentId(), momentId)
}

function getMomentIdsByWorkId (workId) {
  return getRedis().hgetAsync(getMappingKeyForWorkId(), workId)
  .then(result => {
    return util.getJsonInfo(result, [])
  })
}

function clearMapping (workId) {
  return co(function * () {
    const momentIds = yield getMomentIdsByWorkId(workId)
    if (!_.isEmpty(momentIds)) {
      yield Promise.map(momentIds, mId => {
        return getRedis().hdelAsync(getMappingKeyForMomentId(), mId)
      })
    }
    yield getRedis().hdelAsync(getMappingKeyForWorkId(), workId)
    yield getRedis().hdelAsync(getWorkLikesKey(), workId)
  })
}

/**
 * 审核后台传过来的作品信息
 * @param {Object[]} workInfos
 * @param {Number} workInfos[].roleId    玩家roleId
 * @param {Number} workInfos[].coupleRoleId 侠侣的roleId
 * @param {Number} workInfos[].workId 作品Id
 */
function auditResult (workInfos) {
  const insertInfos = []
  return co(function * () {
    for (let i = 0; i < workInfos.length; i++) {
      const workInfo = workInfos[i]
      const workId = workInfo.workId
      const auditStatus = parseInt(workInfo.status, 10)
      if (auditStatus === passedStatus) {
        const mId = yield insertMoment(workInfo.roleId, workId)
        const mId2 = yield insertMoment(workInfo.coupleRoleId, workId)
        yield buildMapping([mId, mId2], workId)
        insertInfos.push({workId: workId, momentIds: [mId, mId2]})
      } else if (auditStatus === rejectStatus) {
        yield clearMapping(workId)
      } else {
        return Promise.reject({msg: '审核状态非法'})
      }
    }
    return insertInfos
  })
}

module.exports = {
  auditResult: auditResult,
  getWorkIdByMomentId: getWorkIdByMomentId,
  getWorkIdLikesCount: getWorkIdLikesCount
}
