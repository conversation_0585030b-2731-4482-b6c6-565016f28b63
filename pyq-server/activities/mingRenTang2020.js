"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getMatchStage = exports.isCanVoteInMatch = exports.getJueSaiRoleIds = exports.getFuSaiRoleIds = exports.isCanVoteRenQi = exports.shareToMd = exports.recordPlayerRenQi = exports.onAttendTopic = exports.getMatchStageRenQiKey = void 0;
const PyqActivityTopic_1 = require("../../models/PyqActivityTopic");
const util_1 = require("../../common/util");
const httpLib = require("../../common/request");
const _ = require("lodash");
const helper_1 = require("../helper");
const errorHandler = require("../errorHandler");
const activityTopic_1 = require("../services/activityTopic");
const redis_1 = require("../../common/redis");
const util2_1 = require("../../common/util2");
const activityTools_1 = require("./activityTools");
const cacheUtil_1 = require("../../common/cacheUtil");
const modelProxy_1 = require("../services/modelProxy");
const config_1 = require("../common/config");
const EventBus = require("../eventBus");
let logger = (0, activityTools_1.getActivityLogger)(config_1.mrt2020Cfg.name);
const INIT_RENQI_HASH_KEY = `qnm:activity:${config_1.mrt2020Cfg.name}:init_renqi`;
function getMatchStageRenQiKey(stage) {
    return `qnm:activity:${config_1.mrt2020Cfg.name}:${stage}_renqi`;
}
exports.getMatchStageRenQiKey = getMatchStageRenQiKey;
function getPlayerRenQi(roleId) {
    return __awaiter(this, void 0, void 0, function* () {
        let profile = (yield modelProxy_1.ProfileModel.findOne({ RoleId: roleId }, ["RenQi"])) || { RenQi: 0 };
        return profile.RenQi;
    });
}
function onAttendTopic(params) {
    return __awaiter(this, void 0, void 0, function* () {
        let name = yield PyqActivityTopic_1.ActivityTopic.getNameById(params.topicId);
        if (name === config_1.mrt2020Cfg.topicName) {
            attendByMomentId(params.momentId);
        }
    });
}
exports.onAttendTopic = onAttendTopic;
EventBus.on(EventBus.Events.SEND_FLOWER, function (payload) {
    const receiver = payload.receiver;
    recordPlayerRenQi(receiver, payload);
});
const linkRegex = /^<link\sbutton=.*>$/;
function isAddAttendText(text) {
    if (text) {
        return !text.match(linkRegex);
    }
    else {
        return false;
    }
}
function attendByMomentId(momentId) {
    return __awaiter(this, void 0, void 0, function* () {
        let record = yield modelProxy_1.MomentModel.findOne({ ID: momentId }, ["Text", "ImgList", "RoleId"]);
        if (record) {
            let imgUrls = (0, util_1.csvStrToArray)(record.ImgList);
            if (imgUrls.length >= config_1.mrt2020Cfg.attendMinImage && isAddAttendText(record.Text)) {
                try {
                    yield requestAttendApi(record.RoleId, imgUrls, record.Text);
                }
                catch (err) {
                    logger.error({ err: err }, "AttendByMomentIdFailed");
                }
            }
        }
    });
}
function requestAttendApi(roleId, imgs, intr) {
    return __awaiter(this, void 0, void 0, function* () {
        imgs = imgs.map((url) => (0, util2_1.replaceHttps)(url));
        let mark = (0, activityTools_1.removeTopicLink)(intr);
        let data = { roleid: roleId, worksUrl: imgs, cover: imgs[0], source: "md", mark: mark };
        logger.info(data, "PrepareData");
        let result = yield httpLib.request({
            method: "POST",
            url: config_1.mrt2020Cfg.applyApi,
            body: data,
        });
        logger.info({ result: result, data: data }, "ApplyApiReturn");
    });
}
function getShareText(work) {
    return `我正在晒自拍赢大奖, 能否逆风翻盘等你一票<link button=点击投票吧,OpenUrlInGame,https://qnm.163.com/2020/mrt/mh/##/list/${work.workid}>`;
}
function addMdMoment(work) {
    return __awaiter(this, void 0, void 0, function* () {
        let topicId = yield PyqActivityTopic_1.ActivityTopic.getIdByName(config_1.mrt2020Cfg.topicName);
        let now = Date.now();
        let momentId = null;
        let shareText = getShareText(work);
        if (topicId) {
            let text = (0, activityTopic_1.getActivityTopicFullText)(topicId, config_1.mrt2020Cfg.topicName, shareText);
            let ret = yield modelProxy_1.MomentModel.insert({
                RoleId: work.roleid,
                Text: text,
                ImgList: work.cover,
                ImgAudit: "1",
                CreateTime: now,
            });
            momentId = ret.insertId;
            yield (0, activityTopic_1.addTopicMoment)(work.roleid, momentId, topicId, []);
        }
        else {
            let ret = yield modelProxy_1.MomentModel.insert({
                RoleId: work.roleid,
                Text: shareText,
                ImgList: work.cover,
                ImgAudit: "1",
                CreateTime: now,
            });
            momentId = ret.insertId;
        }
        yield initPlayerRenQi(work.roleid);
        return momentId;
    });
}
function initPlayerRenQi(roleId) {
    return __awaiter(this, void 0, void 0, function* () {
        let renqi = yield getPlayerRenQi(roleId);
        return (0, redis_1.getRedis)().hsetnxAsync(INIT_RENQI_HASH_KEY, "" + roleId, renqi);
    });
}
function isRoleIdAttend(roleId) {
    return __awaiter(this, void 0, void 0, function* () {
        let ret = yield (0, redis_1.getRedis)().hexistsAsync(INIT_RENQI_HASH_KEY, "" + roleId);
        return ret === redis_1.IExistResult.Exist;
    });
}
function recordPlayerRenQi(roleId, sendFlower) {
    return __awaiter(this, void 0, void 0, function* () {
        let isAttend = yield isRoleIdAttend(roleId);
        let isCanIncrRenQi = yield isCanVoteInMatch(roleId, new Date());
        let date = new Date();
        let stage = getMatchStage(date);
        logger.info({ roleId, sendFlower, stage, isAttend, isCanIncrRenQi }, "PrepareUpdateRenQi");
        if (isAttend && isCanIncrRenQi) {
            let flower = sendFlower.flower;
            let hashKey = getMatchStageRenQiKey(stage);
            yield (0, redis_1.getRedis)().hincrbyAsync(hashKey, "" + roleId, flower.renqi);
            yield notifyRenQiChange(roleId);
        }
    });
}
exports.recordPlayerRenQi = recordPlayerRenQi;
function shareToMd(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            let schema = {
                works: { type: Array },
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            let params = req.params;
            let ids = [];
            for (let w of params.works) {
                let id = yield addMdMoment(w);
                ids.push(id);
            }
            res.send({ code: 0, data: { ids: ids } });
        }
        catch (err) {
            errorHandler(err, req, res, next);
        }
    });
}
exports.shareToMd = shareToMd;
function isCanVoteRenQi(params) {
    if (params.isLimitRoleId) {
        return _.includes(params.limitRoleIds, params.roleId);
    }
    else {
        return true;
    }
}
exports.isCanVoteRenQi = isCanVoteRenQi;
function getApiByMatchStage(stage) {
    if (stage === "fusai") {
        return config_1.mrt2020Cfg.fusaiRoleIdApi;
    }
    else if (stage === "juesai") {
        return config_1.mrt2020Cfg.juesaiRoleIdApi;
    }
    else {
        return "";
    }
}
class GetAttendRoleIdsCache extends cacheUtil_1.GenericCache {
    getExpireTime() {
        return 8 * 3600;
    }
    getKey(stage) {
        return `l10_${config_1.mrt2020Cfg.name}:match:${stage}:roleIds`;
    }
    fetchDataSource(stage) {
        return __awaiter(this, void 0, void 0, function* () {
            return getVotableRoleIds(stage);
        });
    }
}
function getVotableRoleIds(stage) {
    return __awaiter(this, void 0, void 0, function* () {
        let apiUrl = getApiByMatchStage(stage);
        if (apiUrl) {
            let apiReturn = yield httpLib.get(apiUrl);
            if (apiReturn.code === 1) {
                let list = Object.keys(apiReturn.data);
                let roleIds = list.map((x) => parseInt(x, 10));
                return roleIds;
            }
            else {
                logger.error({ state: stage, apiReturn }, "GetAttendRoleidFailed");
                return [];
            }
        }
        else {
            return [];
        }
    });
}
let GetAttendRoleIds = new GetAttendRoleIdsCache();
function getFuSaiRoleIds() {
    return __awaiter(this, void 0, void 0, function* () {
        if (config_1.mrt2020Cfg.skip_api_cache) {
            return getVotableRoleIds("fusai");
        }
        else {
            return GetAttendRoleIds.get("fusai");
        }
    });
}
exports.getFuSaiRoleIds = getFuSaiRoleIds;
function getJueSaiRoleIds() {
    return __awaiter(this, void 0, void 0, function* () {
        if (config_1.mrt2020Cfg.skip_api_cache) {
            return getVotableRoleIds("juesai");
        }
        else {
            return GetAttendRoleIds.get("juesai");
        }
    });
}
exports.getJueSaiRoleIds = getJueSaiRoleIds;
function isCanVoteInMatch(roleId, date) {
    return __awaiter(this, void 0, void 0, function* () {
        let stage = getMatchStage(date);
        if (stage) {
            switch (stage) {
                case "chusai":
                    return true;
                case "fusai":
                    let fusaiRoleIds = yield getFuSaiRoleIds();
                    return _.includes(fusaiRoleIds, roleId);
                case "juesai":
                    let juesaiRoleIds = yield getJueSaiRoleIds();
                    return _.includes(juesaiRoleIds, roleId);
            }
        }
        else {
            return false;
        }
    });
}
exports.isCanVoteInMatch = isCanVoteInMatch;
function getMatchStage(date) {
    if ((0, activityTools_1.isInDateRange)(date, config_1.mrt2020Cfg.timeRange.chusai)) {
        return "chusai";
    }
    else if ((0, activityTools_1.isInDateRange)(date, config_1.mrt2020Cfg.timeRange.fusai)) {
        return "fusai";
    }
    else if ((0, activityTools_1.isInDateRange)(date, config_1.mrt2020Cfg.timeRange.juesai)) {
        return "juesai";
    }
    else {
        return null;
    }
}
exports.getMatchStage = getMatchStage;
function notifyRenQiChange(roleId) {
    return __awaiter(this, void 0, void 0, function* () {
        logger.info({ roleId: roleId }, "NotifyRenQiChangeBegin");
        let ret = yield httpLib.request({
            method: "POST",
            url: config_1.mrt2020Cfg.renqiChangeApi,
            formData: { roleid: roleId },
        });
        logger.info({ roleId: roleId, ret: ret }, "NotifyRenQiChangeResult");
        return ret;
    });
}
//# sourceMappingURL=mingRenTang2020.js.map