"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.isCanVoteRenQi = exports.qyfhShareToMd = exports.recordPlayerRenQi = exports.isRoleIdAttend = exports.markPlayerAttend = exports.getCoverSmart = void 0;
const _ = require("lodash");
const redis_1 = require("../../common/redis");
const httpLib = require("../../common/request");
const util_1 = require("../../common/util");
const config_1 = require("../common/config");
const errorCodes_1 = require("../errorCodes");
const helper_1 = require("../helper");
const activityTopic_1 = require("../services/activityTopic");
const modelProxy_1 = require("../services/modelProxy");
const util_2 = require("../services/util");
const activityTools_1 = require("./activityTools");
const errorHandler = require("../errorHandler");
const EventBus = require("../eventBus");
const logger = (0, activityTools_1.getActivityLogger)(config_1.qyfhCfg.name);
if (config_1.qyfhCfg.enable) {
    EventBus.on(EventBus.Events.SEND_FLOWER, function (payload) {
        const receiver = payload.receiver;
        recordPlayerRenQi(receiver, payload);
    });
}
else {
    logger.warn("activity:qyfhguild is disabled");
}
function getCoverSmart(cover) {
    if ((0, util_2.checkIsUrl)(cover)) {
        return cover;
    }
    else {
        const obj = (0, util_1.getJsonInfo)(cover, {});
        if (obj && obj.url && (0, util_2.checkIsUrl)(obj.url)) {
            return obj.url;
        }
    }
    return "";
}
exports.getCoverSmart = getCoverSmart;
function addMdMoment(work) {
    return __awaiter(this, void 0, void 0, function* () {
        const topicId = (0, activityTopic_1.getActivityTopicId)(work.text);
        const now = Date.now();
        let momentId = null;
        if (work.roleid) {
            const roleInfo = yield modelProxy_1.RoleInfoModel.findOne({ RoleId: work.roleid });
            if (!roleInfo) {
                throw errorCodes_1.ActivityErrors.RoleIdNotFound;
            }
        }
        const cover = getCoverSmart(work.cover);
        if (topicId) {
            const ret = yield modelProxy_1.MomentModel.insert({
                RoleId: work.roleid,
                Text: work.text,
                ImgList: cover,
                ImgAudit: "1",
                CreateTime: now,
            });
            momentId = ret.insertId;
            yield (0, activityTopic_1.addTopicMoment)(work.roleid, momentId, topicId, []);
        }
        else {
            return (0, errorCodes_1.BussError)(errorCodes_1.ActivityErrors.ShareTextNeedTopicId);
        }
        yield markPlayerAttend(work.roleid);
        return momentId;
    });
}
function getAttendSetKey() {
    return `qnm:activity:${config_1.qyfhCfg.name}:attend_set`;
}
function markPlayerAttend(roleId) {
    return __awaiter(this, void 0, void 0, function* () {
        return (0, redis_1.getRedis)().saddAsync(getAttendSetKey(), "" + roleId);
    });
}
exports.markPlayerAttend = markPlayerAttend;
function isRoleIdAttend(roleId) {
    return __awaiter(this, void 0, void 0, function* () {
        const isAttend = yield (0, redis_1.getRedis)().sismember(getAttendSetKey(), "" + roleId);
        return isAttend;
    });
}
exports.isRoleIdAttend = isRoleIdAttend;
function recordPlayerRenQi(roleId, sendFlower) {
    return __awaiter(this, void 0, void 0, function* () {
        const isAttend = yield isRoleIdAttend(roleId);
        logger.info({ roleId, sendFlower, isAttend }, "PrepareUpdateRenQi");
        if (isAttend) {
            const flower = sendFlower.flower;
            if (flower && flower.renqi) {
                const incrRenQi = flower.renqi;
                yield notifyRenQiChange(roleId, incrRenQi);
            }
            else {
                logger.error({ roleId, sendFlower, isAttend }, "SendFlowerParseRenQiFailed");
            }
        }
        else {
            logger.info({ roleId, sendFlower, isAttend }, "PlayerRenQiChangeNotSync");
        }
    });
}
exports.recordPlayerRenQi = recordPlayerRenQi;
function qyfhShareToMd(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        const params = req.params;
        try {
            const schema = {
                works: { type: Array },
            };
            logger.info({ req, params: req.params }, "qyfhShareToMd");
            yield (0, helper_1.checkParams)(req.params, schema);
            const ids = [];
            for (const w of params.works) {
                const id = yield addMdMoment(w);
                ids.push(id);
            }
            res.send({ code: 0, data: { ids: ids } });
        }
        catch (err) {
            errorHandler(err, req, res, next);
        }
    });
}
exports.qyfhShareToMd = qyfhShareToMd;
function isCanVoteRenQi(params) {
    if (params.isLimitRoleId) {
        return _.includes(params.limitRoleIds, params.roleId);
    }
    else {
        return true;
    }
}
exports.isCanVoteRenQi = isCanVoteRenQi;
function notifyRenQiChange(roleId, incrRenQi) {
    return __awaiter(this, void 0, void 0, function* () {
        const url = (0, activityTools_1.getProxiedUrl)(config_1.qyfhCfg.activityApiHost + config_1.qyfhCfg.renqiChangeApiPath.replace("{{qyfhName}}", config_1.qyfhCfg.name));
        const rqOption = { method: "POST", url, formData: { roleid: roleId, renqi: incrRenQi } };
        logger.info({ roleId: roleId, rqOption }, "CallNotifyRenQiChangeStart");
        try {
            const ret = yield httpLib.request(rqOption);
            logger.info({ roleId: roleId, ret: ret, rqOption }, "CallNotifyRenQiChangeOK");
            return ret;
        }
        catch (err) {
            logger.error({ roleId: roleId, err, rqOption }, "CallNotifyRenQiChangeError");
            return false;
        }
    });
}
//# sourceMappingURL=qianYingFengHua.js.map