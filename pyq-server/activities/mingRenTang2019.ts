import { getLogger } from "../../common/logger2";
import { ActivityTopic } from "../../models/PyqActivityTopic";
import * as Moment from '../../models/PyqMoments'
import { csvStrToArray, mkDirsSync } from "../../common/util";
import * as httpLib from '../../common/request'
import * as _ from 'lodash'
import { checkParams } from "../helper";
import errorHandler = require("../errorHandler");
import { getActivityTopicFullText, addTopicMoment } from "../services/activityTopic";
import * as PyqProfile from '../../models/PyqProfile'
import { getRedis, IExistResult } from '../../common/redis'
import { replaceHttps } from "../../common/util2";
import { DateRange, isInDateRange, SendFlowerPayload } from "./activityTools";
import { GenericCache } from "../../common/cacheUtil";
const ModelManager = require('../../models/ModelManager')
const PyqEvent = ModelManager.getModelByTableName('pyq_event')

let logger = getLogger('l10_mingren2019')
let config = require('../../common/config')

let mrtConfig: mrtConfig = config.mingRenTang2019

interface mrtConfig {
    chusai: DateRange
    fusai: DateRange
    juesai: DateRange
    skip_api_cache: boolean
}

type MatchStage = "chusai" | "fusai" | "juesai"

const INIT_RENQI_HASH_KEY = 'qnm:activity:mingrentang_2019:init_renqi'
const CUR_RENQI_HASH_KEY = 'qnm:activity:mingrentang_2019:cur_renqi'

async function getPlayerRenQi(roleId: number) {
    let profile: { RenQi: number } = await PyqProfile.findOne({ RoleId: roleId }, ['RenQi']) || { RenQi: 0 }
    return profile.RenQi
}

interface AttendMoment {
    RoleId: number
    Text: string
    ImgList: string
}

export const TOPIC_NAME = '#倩影风华#'

const ATTEND_MIN_IMAGE_NUM = 2

const APPLY_API = 'http://file.mg.163.com/public/qnm/mingrentang_2019/mengdao/apply'
const NOTIFY_RENQI_CHANGE = 'http://file.mg.163.com/public/qnm/mingrentang_2019/mengdao/renqichange'

interface IAttendTopic {
    roleId: number
    momentId: number
    topicId: number
}

export async function onAttendTopic(params: IAttendTopic) {
    let name = await ActivityTopic.getNameById(params.topicId)
    if (name === TOPIC_NAME) {
        attendByMomentId(params.momentId)
    }
}

PyqEvent.on(PyqEvent.Events.SEND_FLOWER, function (payload: SendFlowerPayload) {
    const receiver = payload.receiver
    recordPlayerRenQi(receiver, payload)
})



const linkRegex = /^<link\sbutton=.*>$/

function isAddAttendText(text: string) {
    if (text) {
        return !text.match(linkRegex)
    } else {
        return false
    }
}

async function attendByMomentId(momentId: number) {
    let record = await Moment.findById(momentId, ['Text', 'ImgList', 'RoleId']) as AttendMoment
    if (record) {
        let imgUrls = csvStrToArray(record.ImgList)
        if (imgUrls.length >= ATTEND_MIN_IMAGE_NUM && isAddAttendText(record.Text)) {
            requestAttendApi(record.RoleId, imgUrls, record.Text)
        }
    }
}

async function requestAttendApi(roleId: number, imgs: string[], intr: string) {
    imgs = imgs.map(url => replaceHttps(url))
    let data = { roleid: roleId, 'imgs[]': imgs, intr: intr }
    logger.info('Prepare attend', data)
    let result = await httpLib.request({
        method: "POST",
        url: APPLY_API,
        formData: data
    })
    logger.info('Request apply api result', { result: result, data: data })
}

function getShareText(work: WorkInfoItem) {
    return `我正在晒自拍赢大奖, 能否逆风翻盘等你一票<link button=点击投票吧,OpenUrlInGame,https://qnm.163.com/2019/mrt/mh/##/list/${work.workid}>`
}

async function addMdMoment(work: WorkInfoItem) {
    let topicId = await ActivityTopic.getIdByName(TOPIC_NAME)
    let momentId
    let now = Date.now()

    let shareText = getShareText(work)
    if (topicId) {
        let text = getActivityTopicFullText(topicId, TOPIC_NAME, shareText)
        let ret = await Moment.insert({ RoleId: work.roleid, Text: text, ImgList: work.cover, ImgAudit: '1', CreateTime: now })
        momentId = ret.insertId
        await addTopicMoment(work.roleid, momentId, topicId, [])
    } else {
        let ret = await Moment.insert({ RoleId: work.roleid, Text: shareText, ImgList: work.cover, ImgAudit: '1', CreateTime: now })
        momentId = ret.insertId
    }
    await initPlayerRenQi(work.roleid)
    return momentId
}

async function initPlayerRenQi(roleId: number) {
    let renqi = await getPlayerRenQi(roleId)
    return getRedis().hsetnxAsync(INIT_RENQI_HASH_KEY, '' + roleId, renqi)
}

async function isRoleIdAttend(roleId: number) {
    let ret = await getRedis().hexistsAsync(INIT_RENQI_HASH_KEY, '' + roleId)
    return ret === IExistResult.Exist
}

async function notifyRenQiChange(roleId: number) {
    logger.info('notifyRenQiChange', { roleId: roleId })
    let ret = await httpLib.request({
        method: "POST",
        url: NOTIFY_RENQI_CHANGE,
        formData: { roleid: roleId }
    })
    logger.info('notifyRenQiChangeResult', { roleId: roleId, ret: ret })
    return ret
}

export async function recordPlayerRenQi(roleId: number, sendFlower: SendFlowerPayload) {
    let isAttend = await isRoleIdAttend(roleId)
    let isCanIncrRenQi = await isCanVoteInMatch(roleId, new Date(), mrtConfig)
    let date = new Date()
    let stage = getMatchStage(date, mrtConfig)
    if (isAttend && isCanIncrRenQi) {
        let flower = sendFlower.flower
        logger.info('RecordPlayerRenQi', { roleId: roleId, sendFlower: sendFlower })
        if (stage === "chusai") {
            let renQi = await getPlayerRenQi(roleId)
            await getRedis().hsetAsync(CUR_RENQI_HASH_KEY, '' + roleId, renQi)
            await notifyRenQiChange(roleId)
        } else if (stage === "fusai" || stage === "juesai") {
            await getRedis().hincrbyAsync(CUR_RENQI_HASH_KEY, '' + roleId, flower.renqi)
            await notifyRenQiChange(roleId)
        }
    }
}

export interface WorkInfoItem {
    roleid: number
    workid: number
    cover: string
}

interface IShareToMd {
    works: WorkInfoItem[]
}

export async function shareToMd(req, res, next) {
    try {
        let schema = {
            works: { type: Array }
        }
        await checkParams(req.params, schema)
        let params: IShareToMd = req.params
        let ids = []
        for (let w of params.works) {
            let id = await addMdMoment(w)
            ids.push(id)
        }
        res.send({ code: 0, data: { ids: ids } })
    } catch (err) {
        errorHandler(err, req, res, next)
    }
}

interface IVoteRenQiParams {
    roleId: number
    isLimitRoleId: boolean
    limitRoleIds: number[]
}

function getCallApiPrefix() {
    if (config.testCfg.test_env) {
        return 'http://***************:8081'
    } else {
        return 'http://file.mg.163.com'
    }
}

export function isCanVoteRenQi(params: IVoteRenQiParams): boolean {
    if (params.isLimitRoleId) {
        return _.includes(params.limitRoleIds, params.roleId)
    } else {
        return true
    }
}

const fusaiRoleIdApi = getCallApiPrefix() + "/public/qnm/mingrentang_2019/mengdao/fusaidata"
const juesaiRoleidApi = getCallApiPrefix() + "/public/qnm/mingrentang_2019/mengdao/fusaidata"

function getApiByMatchStage(stage: MatchStage) {
    if (stage === "fusai") {
        return fusaiRoleIdApi
    } else if (stage === "juesai") {
        return juesaiRoleidApi
    } else {
        return ""
    }
}

interface AttendRoleIdApiReturn {
    code: number;
    msg?: any;
    data: { [key: string]: string };
}

class GetAttendRoleIdsCache extends GenericCache<MatchStage, number[]> {
    getExpireTime() {
        return 8 * 3600
    }

    getKey(stage: MatchStage): string {
        return "mingrentang2019:match:" + stage + ":roleIds"
    }

    async fetchDataSource(stage: MatchStage): Promise<number[]> {
        return getVotableRoleIds(stage)
    }
}

async function getVotableRoleIds(stage: MatchStage) {
    let apiUrl = getApiByMatchStage(stage)
    if (apiUrl) {
        let apiReturn: AttendRoleIdApiReturn = await httpLib.get(apiUrl)
        if (apiReturn.code === 1) {
            let list = Object.keys(apiReturn.data)
            let roleIds = list.map(x => parseInt(x, 10))
            return roleIds
        } else {
            logger.error('GetAttendRoleidFailed', { state: stage, apiReturn })
            return []
        }
    } else {
        return []
    }
}

let GetAttendRoleIds = new GetAttendRoleIdsCache()


export async function getFuSaiRoleIds(): Promise<number[]> {
    if (mrtConfig.skip_api_cache) {
        return getVotableRoleIds("fusai")
    } else {
        return GetAttendRoleIds.get("fusai")
    }
}

export async function getJueSaiRoleIds(): Promise<number[]> {
    if (mrtConfig.skip_api_cache) {
        return getVotableRoleIds("juesai")
    } else {
        return GetAttendRoleIds.get("juesai")
    }
}

export async function isCanVoteInMatch(roleId: number, date: Date, mrtConfig: mrtConfig) {
    let stage = getMatchStage(date, mrtConfig)
    if (stage) {
        switch (stage) {
            case "chusai":
                return true
            case "fusai":
                let fusaiRoleIds = await getFuSaiRoleIds()
                return _.includes(fusaiRoleIds, roleId)
            case "juesai":
                let juesaiRoleIds = await getJueSaiRoleIds()
                return _.includes(juesaiRoleIds, roleId)
        }
    } else {
        return false
    }
}


export function getMatchStage(date: Date, mrtConfig: mrtConfig): MatchStage {
    if (isInDateRange(date, mrtConfig.chusai)) {
        return "chusai"
    } else if (isInDateRange(date, mrtConfig.fusai)) {
        return "fusai"
    } else if (isInDateRange(date, mrtConfig.juesai)) {
        return "juesai"
    } else {
        return null
    }
}
