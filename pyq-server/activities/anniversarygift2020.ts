import { ActivityTopic } from "../../models/PyqActivityTopic";
import {ActivityNs, AnniversaryGift2020Apply} from "../type/req";
import * as _ from 'lodash'
import {anniversarygift2020Cfg} from "../common/config";
import {MomentModel} from "../services/modelProxy";
import {csvStrToArray} from "../../common/util";
import {replaceHttps} from "../../common/util2";
import {removeTopicLink, isOnlyContainTopic, getActivityLogger} from "./activityTools";
import * as httpLib from '../../common/request'
import {PyqMomentRecord} from "../../common/type";
let logger = getActivityLogger(anniversarygift2020Cfg.name)

export async function onAttendTopic(params: ActivityNs.IAttendTopic) {
  let name = await ActivityTopic.getNameById(params.topicId)
  if(_.includes(anniversarygift2020Cfg.topicNames, name)) {
    return attendByMomentId(params.momentId, params.topicId)
  }
}

async function attendByMomentId(momentId: number, topicId: number) {
  let record = await MomentModel.findOne({ID: momentId})
  if (record) {
    let imgUrls = csvStrToArray(record.ImgList)
    let isMatchApply = (imgUrls.length >= anniversarygift2020Cfg.minImgLimit) && !isOnlyContainTopic(record.Text)
    if (isMatchApply) {
      return requestAttendApi(topicId, record)
    }
  }
}

async function requestAttendApi(topicId:number, record: PyqMomentRecord) {
  let imgs = csvStrToArray(record.ImgList)
  imgs = imgs.map(url => replaceHttps(url))
  let text = removeTopicLink(record.Text)
  let data: AnniversaryGift2020Apply = {
    roleId:  record.RoleId,
    pics: imgs,
    description: text,
    topicId: topicId
  }
  logger.info('Prepare attend', data)
  let result = await httpLib.request({
    method: "POST",
    url: anniversarygift2020Cfg.applyApi,
    body: data
  })
  logger.info('Request apply api result', { result: result, data: data })
  return result
}
