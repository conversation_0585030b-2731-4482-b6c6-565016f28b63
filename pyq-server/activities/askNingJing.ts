import { ActivityTopic } from "../../models/PyqActivityTopic";
import { ActivityNs } from "../type/req";
import { MomentModel, RoleInfoModel } from "../services/modelProxy";
import { askNingJingCfg } from "../common/config";
import { removeTopicLink, getActivityLogger, getProxiedUrl } from "./activityTools";
import * as httpLib from "../../common/request";
import { PyqMomentRecord } from "../../common/type";
import { REDIS_PREFIX } from "../constants";
import { AllowOnceCache } from "../../common/cacheUtil";
import { ONE_DAY_SECONDS } from "../../common/config";
let logger = getActivityLogger(askNingJingCfg.name);

export async function onAttendTopic(params: ActivityNs.IAttendTopic) {
  let name = await ActivityTopic.getNameById(params.topicId);
  if (name === askNingJingCfg.topicName) {
    return attendByMomentId(params.momentId);
  }
}

async function isFirstAttend(roleId: number): Promise<boolean> {
  let expire = 30 * ONE_DAY_SECONDS;
  let allowOnce = new AllowOnceCache(
    REDIS_PREFIX + "actvity:" + askNingJingCfg.name + ":player" + roleId + ":first_attend",
    expire
  );
  return allowOnce.isAllow();
}

async function attendByMomentId(momentId: number) {
  let record = await MomentModel.findOne({ ID: momentId });
  let text = removeTopicLink(record.Text);
  // only allow text not empty
  if (text) {
    let isFirst = await isFirstAttend(record.RoleId);
    logger.debug({ record, isFirst }, "attendByMomentId");
    if (isFirst) {
      requestAttendApi(record);
    }
  }
}

interface AttendAskNing {
  roleId: number;
  serverId: number;
  nickname: string;
  content: string;
}

async function requestAttendApi(record: PyqMomentRecord) {
  let text = removeTopicLink(record.Text);
  let roleInfo = await RoleInfoModel.findOne({ RoleId: record.RoleId }, ["RoleName", "ServerId"]);
  let data: AttendAskNing = {
    roleId: record.RoleId,
    serverId: 0,
    nickname: "",
    content: text,
  };
  if (roleInfo) {
    data.nickname = roleInfo.RoleName;
    data.serverId = roleInfo.ServerId;
  }
  logger.info("Prepare attend", data);
  let result = await httpLib.request({
    method: "POST",
    url: getProxiedUrl(askNingJingCfg.applyApi),
    body: data,
  });
  logger.info("Request apply api result", { result: result, data: data });
  return result;
}
