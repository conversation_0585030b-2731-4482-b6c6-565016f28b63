
import { getLogger } from "../../common/logger2";
import { ActivityTopic } from "../../models/PyqActivityTopic";
import * as Moment from '../../models/PyqMoments'
import { csvStrToArray } from "../../common/util";
import * as httpLib from '../../common/request'
import * as _ from 'lodash'
import { replaceHttps } from "../../common/util2";
import { API_HOST, MD_SOURCE } from "./activityHelper";
import { isOnlyContainTopic, removeTopicLink } from "./activityTools";

let logger = getLogger('l10_flute')

export const TOPIC_NAME = '解曲知音'

const ATTEND_MIN_VIDEO_NUM = 1

const APPLY_API = API_HOST + '/public/share/common_activity_vote/qnm/flute2019/server/apply/chusai'

interface IAttendTopic {
    roleId: number
    momentId: number
    topicId: number
}

export async function onAttendTopic(params: IAttendTopic) {
    let name = await ActivityTopic.getNameById(params.topicId)
    if (name === TOPIC_NAME) {
        return attendByMomentId(params.momentId)
    }
}


interface AttendMoment {
    RoleId: number
    Text: string
    VideoList: string
}

async function attendByMomentId(momentId: number) {
    let record = await Moment.findById(momentId, ['Text', 'VideoList', 'RoleId']) as AttendMoment
    logger.info('attendByMomentId', record)
    if (record) {
        let videoList = csvStrToArray(record.VideoList)

        if (videoList.length >= ATTEND_MIN_VIDEO_NUM && !isOnlyContainTopic(record.Text)) {
            let text = removeTopicLink(record.Text)
            return requestAttendApi(record.RoleId, videoList, text)
        }
    }
}

interface AttendBody {
    roleid: number
    mark: string
    worksurl: string[]
    source: string
}


async function requestAttendApi(roleId: number, videos: string[], intr: string) {
    videos = videos.map(url => replaceHttps(url))
    let data: AttendBody = { roleid: roleId, worksurl: videos, mark: intr, source: MD_SOURCE }
    logger.info('Prepare attend', data)
    let result = await httpLib.request({
        method: "POST",
        url: APPLY_API,
        body: data
    })
    logger.info('Request apply api result', { result: result, data: data })
    return result
}
