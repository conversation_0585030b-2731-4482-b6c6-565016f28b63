/* eslint-disable prefer-promise-reject-errors */
const ModelManager = require('../../models/ModelManager')
const PyqMoments = ModelManager.getModelByTableName('pyq_moment')

function getPreviewUrl (workId) {
  return `https://qnm.163.com/m/fszmh/#/list/${workId}`
}

function getShareText (workId) {
  const link = PyqMoments.genLinkButton(getPreviewUrl(workId), '立刻查看')
  return `我报名了表白胡一天活动，报名信息已经通过审核啦!小伙伴们快来为我点赞吧，让胡一天演绎我的专属动作！${link}`
}

function enroll (payload) {
  let roleid = payload.roleid
  let workid = payload.workid
  let text = getShareText(workid)
  if (!roleid || !workid) {
    return Promise.reject({code: -1, msg: 'InvalidArgument'})
  }
  return PyqMoments.insert({
    RoleId: roleid,
    Text: text,
    CreateTime: Date.now()
  }).then(info => {
    return info.insertId
  })
}

module.exports = {
  enroll: enroll
}
