/**
 * Created by <PERSON><PERSON><PERSON> on 2016/12/24.
 */

const PyqTopic = require('../../models/PyqTopic');
const PyqMoment = require('../../models/PyqMoments');
const PyqProfile = require('../../models/PyqProfile');
const PyqEvent = require('../../models/PyqEvent');
const QnmRoleInfo = require('../../models/QNMRoleInfos');
const util = require('../../common/util');
const _ = require('lodash');
const rq = require('../../common/request');
const logger = require('../../common/logger');
const { getRedis } = require('../../common/redis');

const HOME_LAND_MATCH = "家园设计大赛";

const JIA_YUAN_MOMENT_LIKE_HASH_KEY = "qnm:jiayuan:mdvote";
const JIA_YUAN_API_HOST = "http://file.mg.163.com/qnm/2016/jiayuan/api.php";

PyqTopic.on(PyqTopic.Events.ADD_MOMENT_TO_TOPIC, function (payload) {
  const subject = payload.subject;
  const momentId = payload.momentId;
  if(subject === HOME_LAND_MATCH) {
    playerHomeLandMatchHandler(momentId);
  }
});


PyqMoment.on(PyqMoment.Events.LIKE_MOMENT, function (payload) {
  const momentId = payload.momentId;
  return updateMomentLikeValue(momentId, 1);
});

PyqMoment.on(PyqMoment.Events.Cancel_LIKE_MOMENT, function (payload) {
  const momentId = payload.momentId;
  return updateMomentLikeValue(momentId, -1);
});

function updateMomentLikeValue(momentId, changeValue) {
  return getRedis().hexistsAsync(JIA_YUAN_MOMENT_LIKE_HASH_KEY, momentId)
    .then(function (isAttend) {
      if(isAttend == 1) {
        return getRedis().hincrbyAsync(JIA_YUAN_MOMENT_LIKE_HASH_KEY, momentId, changeValue);
      }
    });
}

/**
 * 获取玩家投稿
 * @param momentId
 */
function getPlayerContribution(momentId) {
  return PyqMoment.findById(momentId, ['ID', 'Text', 'RoleId', 'ImgList'])
    .then(function (moment) {
      if(!moment) {
        return Promise.reject({errorType: "MomentIdInvalid"});
      }
      return moment;
    }).then(function (moment) {
      const roleId  = moment.RoleId;
      let getRoleInfos = function () {
        return QnmRoleInfo.findOne({RoleId: roleId}, ['RoleId', 'RoleName', 'UserName', 'ServerId', 'Level', 'JobId'])
          .then(function (roleInfo) {
            return QnmRoleInfo.getRoleInfosWithServerAndJob([roleInfo])
          }).then(function (records) {
            return _.first(records);
          })
      };

      return Promise.all([
        getRoleInfos(),
        moment,
      ])
    }).spread(function(roleInfo, moment) {
      let serverName = "测试组-测试服";
      if(roleInfo.Server) {
        serverName = roleInfo.Server.group + "-" + roleInfo.Server.name;
      }
      let imgList = util.csvStrToArray(moment.ImgList);
      return {
        id: moment.ID,
        urs: roleInfo.UserName,
        roleid: roleInfo.RoleId,
        servername: serverName,
        serverid:roleInfo.ServerId,
        nickname: roleInfo.RoleName,
        imgs: JSON.stringify(imgList),
        dec: moment.Text,
      }
    })
}

function playerHomeLandMatchHandler(momentId) {
  return getRedis().hsetAsync(JIA_YUAN_MOMENT_LIKE_HASH_KEY, momentId, 0)
    .then(function () {
      return getPlayerContribution(momentId);
    }).then(function (data) {
      return sendWorks(data);
    })
}


function sendWorks(qs) {
  qs.action = "send_works";
  return rq.get(JIA_YUAN_API_HOST, qs).then(function (data) {
    logger.add("jiayuan_api_return", JSON.stringify({data: data}));
    return data;
  }).catch(function (err) {
    logger.add("jiayuan_api_return", JSON.stringify({error:err}));
    return err;
  });
}


module.exports = {
  JIA_YUAN_MOMENT_LIKE_HASH_KEY: JIA_YUAN_MOMENT_LIKE_HASH_KEY,
  getPlayerContribution: getPlayerContribution,
  sendWorks: sendWorks,
  playerHomeLandMatchHandler: playerHomeLandMatchHandler,
  updateMomentLikeValue: updateMomentLikeValue
};
