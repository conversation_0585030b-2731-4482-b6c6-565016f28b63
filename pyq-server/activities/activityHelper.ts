const QnmRoleInfo = require('../../models/QNMRoleInfos');
const PyqMoment = require('../../models/PyqMoments');
const PyqProfile = require('../../models/PyqProfile');
const _ = require('lodash');
const util = require('../../common/util');
import { testCfg } from '../../common/config'

export function getPlayerInfo(roleId, selectProps) {
  return QnmRoleInfo.findOne({ RoleId: roleId }, selectProps)
    .then(roleInfo => {
      if (!roleInfo) {
        return Promise.reject({ errorType: "RoleIdInvalid", msg: roleId + " : 找不到该角色" })
      }
      return QnmRoleInfo.getRoleInfosWithServerAndJob([roleInfo])
    }).then(records => {
      let info = _.first(records) || {};
      if (info.Server) {
        info.ServerName = info.Server.group + "-" + info.Server.name;
      } else if (info.ServerId && info.ServerId < 20) {
        info.ServerName = "测试组-测试服";
      } else {
        info.ServerName = "";
      }
      return info;
    })
}

export function getMoment(momentId, selectProps) {
  return PyqMoment.findByIdForce(momentId, selectProps)
    .then(moment => {
      moment.ImgList = util.csvStrToArray(moment.ImgList);
      return moment;
    });
}

export function getFlowerRenQi(roleId) {
  return PyqProfile.findOne({ RoleId: roleId }, ['FlowerRenQi'])
    .then(function (profile) {
      return _.get(profile, 'FlowerRenQi') || 0;
    })
}

export function subsribeMomentLikesChange(listener) {
  PyqMoment.on(PyqMoment.Events.LIKE_MOMENT, function (payload) {
    const momentId = payload.momentId
    listener({ momentId: momentId, change: 1 })
  })

  PyqMoment.on(PyqMoment.Events.Cancel_LIKE_MOMENT, function (payload) {
    const momentId = payload.momentId
    listener({ momentId: momentId, change: -1 })
  })
}

export function getActivityApiHost() {
  if (testCfg.test_env) {
    return 'http://***************:8081'
  } else {
    return 'http://file.mg.163.com'
  }
}

export let API_HOST = getActivityApiHost()

export const MD_SOURCE = 'md'