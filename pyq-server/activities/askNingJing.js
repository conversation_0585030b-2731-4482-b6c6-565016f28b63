"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.onAttendTopic = void 0;
const PyqActivityTopic_1 = require("../../models/PyqActivityTopic");
const modelProxy_1 = require("../services/modelProxy");
const config_1 = require("../common/config");
const activityTools_1 = require("./activityTools");
const httpLib = require("../../common/request");
const constants_1 = require("../constants");
const cacheUtil_1 = require("../../common/cacheUtil");
const config_2 = require("../../common/config");
let logger = (0, activityTools_1.getActivityLogger)(config_1.askNingJingCfg.name);
function onAttendTopic(params) {
    return __awaiter(this, void 0, void 0, function* () {
        let name = yield PyqActivityTopic_1.ActivityTopic.getNameById(params.topicId);
        if (name === config_1.askNingJingCfg.topicName) {
            return attendByMomentId(params.momentId);
        }
    });
}
exports.onAttendTopic = onAttendTopic;
function isFirstAttend(roleId) {
    return __awaiter(this, void 0, void 0, function* () {
        let expire = 30 * config_2.ONE_DAY_SECONDS;
        let allowOnce = new cacheUtil_1.AllowOnceCache(constants_1.REDIS_PREFIX + "actvity:" + config_1.askNingJingCfg.name + ":player" + roleId + ":first_attend", expire);
        return allowOnce.isAllow();
    });
}
function attendByMomentId(momentId) {
    return __awaiter(this, void 0, void 0, function* () {
        let record = yield modelProxy_1.MomentModel.findOne({ ID: momentId });
        let text = (0, activityTools_1.removeTopicLink)(record.Text);
        // only allow text not empty
        if (text) {
            let isFirst = yield isFirstAttend(record.RoleId);
            logger.debug({ record, isFirst }, "attendByMomentId");
            if (isFirst) {
                requestAttendApi(record);
            }
        }
    });
}
function requestAttendApi(record) {
    return __awaiter(this, void 0, void 0, function* () {
        let text = (0, activityTools_1.removeTopicLink)(record.Text);
        let roleInfo = yield modelProxy_1.RoleInfoModel.findOne({ RoleId: record.RoleId }, ["RoleName", "ServerId"]);
        let data = {
            roleId: record.RoleId,
            serverId: 0,
            nickname: "",
            content: text,
        };
        if (roleInfo) {
            data.nickname = roleInfo.RoleName;
            data.serverId = roleInfo.ServerId;
        }
        logger.info("Prepare attend", data);
        let result = yield httpLib.request({
            method: "POST",
            url: (0, activityTools_1.getProxiedUrl)(config_1.askNingJingCfg.applyApi),
            body: data,
        });
        logger.info("Request apply api result", { result: result, data: data });
        return result;
    });
}
//# sourceMappingURL=askNingJing.js.map