let errorHandler = require('../errorHandler')
import * as PyqMoments from '../../models/PyqMoments'
import * as PyqEvent from '../../models/PyqEvent'
import { getRedis, IExistResult } from '../../common/redis'
import * as ActivityHelper from './activityHelper'


PyqEvent.on(PyqEvent.Events.SEND_FLOWER, async function (payload) {
  const receiver = payload.receiver
  await FlowerCounter.onSendFlowerHandler(receiver)
})

interface WorkInfo {
  status: string
  roleId: number
  workId: number
  workName: string
  urls: string[]
}

interface IParams {
  work_infos: WorkInfo[]
}

enum AuditStatus {
  PASS = 1
}

export async function auditResult(req, res, next) {
  let params = req.params as IParams
  let workInfos = params.work_infos
  try {
    let mIds = await auditWorkInfos(workInfos)
    res.send({ code: 0, data: { momentIds: mIds } })
  } catch (err) {
    errorHandler(err, req, res, next)
  }
}

function getPreviewUrl(workId) {
  return `https://qnm.163.com/m/2018/qixih/#/list/${workId}`
}

function getShareText(workInfo: WorkInfo) {
  const link = PyqMoments.genLinkButton(getPreviewUrl(workInfo.workId), '点击前往')
  let workName = workInfo.workName || ''
  return `我参加了倩女七夕土味情话battle活动，我的作品#${workName}###已经通过审核啦，浪漫情话说给你听，还不快来为我加油打call！${link}`
}

function addShareMoment(workInfo: WorkInfo) {
  let roleId = workInfo.roleId
  let urls = workInfo.urls || []
  let imgList = urls.join(',')
  let imgAudit = (new Array(urls.length)).fill(1).join(',')
  return PyqMoments.insert({
    RoleId: roleId,
    ImgList: imgList,
    imgAudit: imgAudit,
    Text: getShareText(workInfo),
    CreateTime: Date.now()
  }).then(insertInfo => {
    return insertInfo.insertId
  })
}

async function auditWorkInfos(workInfos: WorkInfo[]) {
  let mIds = []
  for (let item of workInfos) {
    let auditStatus = parseInt(item.status, 10)
    if (auditStatus === AuditStatus.PASS) {
      let mId = await addShareMoment(item)
      await FlowerCounter.initFlowerRenQi(item.roleId)
      mIds.push(mId)
    }
  }
  return mIds
}

class FlowerCounter {
  static KEY_PREFIX = 'qnm:activity:qixi_2018:'
  static INIT_FLOWER_HASH_KEY = FlowerCounter.KEY_PREFIX + 'initflower'
  static CUR_FLOWER_HASH_KEY = FlowerCounter.KEY_PREFIX + 'curflower'

  static async isRoleIdAttend(roleId: number) {
    let ret = await getRedis().hexistsAsync(this.INIT_FLOWER_HASH_KEY, roleId)
    return ret === IExistResult.Exist
  }

  static async initFlowerRenQi(roleId: number) {
    let renQi = await ActivityHelper.getFlowerRenQi(roleId)
    let ret = await getRedis().hsetnxAsync(this.INIT_FLOWER_HASH_KEY, '' + roleId, renQi)
    return ret
  }

  static async onSendFlowerHandler(roleId: number) {
    let isAttend = await this.isRoleIdAttend(roleId)
    if (isAttend) {
      let renQi = await ActivityHelper.getFlowerRenQi(roleId)
      let ret = await getRedis().hsetAsync(this.CUR_FLOWER_HASH_KEY, '' + roleId, renQi)
      return ret
    }
  }
}