/**
 * Created by z<PERSON><PERSON> on 2017/3/14.
 */

const PyqEvent = require('../../models/PyqEvent');
const PyqMoment = require('../../models/PyqMoments');
const ActivityHelper = require('./activityHelper');
const Promise = require('bluebird');
const { getRedis } = require('../../common/redis');
const FeatureToggle = require('../../common/FeatureToggle');

PyqEvent.on(PyqEvent.Events.SEND_FLOWER, function (payload) {
  const receiver = payload.receiver;
  onSendFlowerHandler(receiver);
});

const INIT_FLOWER_HASH_KEY = "qnm:activity:qingyuan:initflower";
const FLOWER_HASH_KEY = "qnm:activity:qingyuan:curflower";
function onSendFlowerHandler(roleId) {
  return isRoleIdAttend(roleId).then(isAttend => {
    if(isAttend) {
      return ActivityHelper.getFlowerRenQi(roleId)
      .then(renQi => {
        return setCurFlowerRenQiToRedis(roleId, renQi);
      });
    }
  });
}

function setCurFlowerRenQiToRedis(roleId, renQi) {
  FeatureToggle.whenActive("qingyuan_activity", () => {
    return getRedis().hsetAsync(FLOWER_HASH_KEY, roleId, renQi);
  });
}

function setInitialFlowerRenQiToRedis(roleId, renQi) {
  FeatureToggle.whenActive("qingyuan_activity", () => {
    return isRoleIdAttend(roleId).then((isAttend) => {
      if(!isAttend) {
        return getRedis().hsetAsync(INIT_FLOWER_HASH_KEY, roleId, renQi);
      }
    });
  });
}

function isRoleIdAttend(roleId) {
  return getRedis().hexistsAsync(INIT_FLOWER_HASH_KEY, roleId);
}

function addPyqMomentAndLink(roleId, workIds, titleHash)  {
  return Promise.mapSeries(workIds, workId => {
    const link = PyqMoment.genLinkButton(`https://qnm.163.com/m/qygsh/index.html#previewid=${workId}`, titleHash[workId]);
    return PyqMoment.insert({
      RoleId: roleId,
      CreateTime: Date.now(),
      Text: `点击《${link}》看看我和Ta的故事，为我们投上一票，一起参赛，幸运称号100%得!`});
  });
}

module.exports = {
  setCurFlowerRenQiToRedis: setCurFlowerRenQiToRedis,
  setInitialFlowerRenQiToRedis: setInitialFlowerRenQiToRedis,
  addPyqMomentAndLink: addPyqMomentAndLink
};
