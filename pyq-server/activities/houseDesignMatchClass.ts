
import { getRedis } from "../../common/redis";
import * as PyqEvent from '../../models/PyqEvent'
import * as PyqMoments from '../../models/PyqMoments'
import * as DateUtil from '../../common/dateUtil'
import * as bluebird from 'bluebird'
import * as _ from 'lodash'
import { checkParams } from "../helper";
import { csvStrToArray } from "../../common/util";
import { fillCsvStr } from "../../common/util2";
import { activityLogger } from "../logger";
import { getActivityTopicFullText, addTopicMoment } from "../services/activityTopic";
import { ActivityTopic } from "../../models/PyqActivityTopic";

export enum EMatchStage {
    PRE = 'pre',
    SEMI_FINAL = 'simi_final',
    FINAL = 'final'
}

interface DateRange {
    start: Date
    end: Date
}

export interface MatchDateRange {
    [EMatchStage.PRE]: DateRange
    [EMatchStage.SEMI_FINAL]: DateRange
    [EMatchStage.FINAL]: DateRange
}

const AuditStatus = {
    PASS: 0,
    Reject: -1
}


interface WorkPayload {
    roleid: number
    workid: number
    imgs: string
    workname: string
    status: number
}

export abstract class HouseDesignMatch {
    private logger
    private topicName: string

    constructor(option: { logName: string, topicName: string }) {
        this.topicName = option.topicName
        this.logger = activityLogger.child({ activity: option.logName, topicName: this.topicName })
    }


    static flowerIdToVote = {
        21100029: 1, // 玫瑰
        21100030: 10, // 绚丽玫瑰
        21100031: 220, // 以爱之名
        21101865: 280 // 知己之名
    }

    abstract getMatchDateRange(): MatchDateRange

    abstract getKeyPrefix(): string


    getMatchTypeByDate(date = new Date()) {
        let dateRange = this.getMatchDateRange()
        let types = Object.keys(dateRange) as EMatchStage[]
        for (let p of types) {
            let r = dateRange[p]
            if (DateUtil.isWithInRange(date, r.start, r.end)) {
                return p
            }
        }
        return null
    }

    getAttendSetKey(matchType: EMatchStage) {
        let prefix = this.getKeyPrefix()
        return `${prefix}:roleids:${matchType}`
    }


    getFlowerVoteHashKey(matchType: EMatchStage) {
        let prefix = this.getKeyPrefix()
        return `${prefix}:flower_vote:${matchType}_match`
    }

    getFlowerRenQiHashKey(matchType: EMatchStage) {
        let prefix = this.getKeyPrefix()
        return `${prefix}:flower_renqi:${matchType}_match`
    }


    async workAuditRejectHandler(roleid: number) {
        let matchType = this.getMatchTypeByDate()
        if (matchType) {
            let voteKey = this.getFlowerVoteHashKey(matchType)
            let renqiKey = this.getFlowerRenQiHashKey(matchType)
            await bluebird.all([
                getRedis().hdelAsync(voteKey, '' + roleid),
                getRedis().hdelAsync(renqiKey, '' + roleid),
            ])
            await this.removeFromAttendRoleIds(roleid)
        } else {
            return bluebird.resolve()
        }
    }

    async isRoleIdAttend(roleid) {
        let matchType = this.getMatchTypeByDate()
        if (matchType) {
            let key = this.getAttendSetKey(matchType)
            return getRedis().sismemberAsync(key, roleid)
        } else {
            return bluebird.resolve(false)
        }
    }


    listenFlowerEvent() {
        let self = this
        this.logger.info({ topicName: this.topicName }, 'ListenFlowerEvent')
        PyqEvent.on(PyqEvent.Events.SEND_FLOWER, async function (payload) {
            const receiver = payload.receiver
            const flower = payload.flower
            let matchType = self.getMatchTypeByDate()
            if (matchType) {
                return self.isRoleIdAttend(receiver).then(isAttend => {
                    if (isAttend) {
                        let voteKey = self.getFlowerVoteHashKey(matchType)
                        let amount = HouseDesignMatch.flowerIdToVote[flower.id] * flower.count

                        self.logger.info({ flower: payload, amount: amount }, 'ReceiveFlowerEvent')

                        let renqiKey = self.getFlowerRenQiHashKey(matchType)

                        return bluebird.all([
                            getRedis().hincrbyAsync(voteKey, receiver, amount),
                            getRedis().hincrbyAsync(renqiKey, receiver, flower.renqi)
                        ])
                    }
                })
            }
        })
    }

    start() {
        this.listenFlowerEvent()
    }

    abstract getPreviewUrl(workId: number): string


    async getShareText(workId: number, workName: string, topicId: number) {
        let buttonName = this.getPreviewUrlButtonName()
        let link = this.getPreviewButton(workId, buttonName)
        let text = this.getShareTextByNameAndLink(workName, link)
        if (topicId) {
            return getActivityTopicFullText(topicId, this.topicName, text)
        } else {
            this.logger.warn({ text: text }, 'TopicNotCreated')
            return text
        }
    }

    async getTopicId() {
        let topicId = await ActivityTopic.getIdByName(this.topicName)
        return topicId
    }

    abstract getShareTextByNameAndLink(workName: string, link: string): string

    abstract getPreviewUrlButtonName(): string


    getPreviewButton(workId: number, buttonName: string) {
        let url = this.getPreviewUrl(workId)
        const link = PyqMoments.genLinkButton(url, buttonName)
        return link
    }



    roleAttendActivity(roleId: number) {
        let matchType = this.getMatchTypeByDate()
        let key = this.getAttendSetKey(matchType)
        if (matchType) {
            return getRedis().saddAsync(key, roleId)
        } else {
            return bluebird.resolve()
        }
    }

    removeFromAttendRoleIds(roleId: number) {
        let matchType = this.getMatchTypeByDate()
        let key = this.getAttendSetKey(matchType)
        if (matchType) {
            return getRedis().sremAsync(key, roleId)
        } else {
            return bluebird.resolve()
        }
    }


    async enroll(params: WorkPayload) {
        let schema = {
            roleid: { type: Number },
            workid: { type: Number },
            workname: { type: String },
            imgs: { type: String },
            status: { type: Number }
        }

        await checkParams(params, schema)
        let roleId = params.roleid
        let topicId = await this.getTopicId()

        this.logger.info(params, 'ReceiveWork')

        let text = await this.getShareText(params.workid, params.workname, topicId)
        if (params.status === AuditStatus.Reject) {
            return this.workAuditRejectHandler(roleId)
        } else {
            let imgList = csvStrToArray(params.imgs)
            let imgListStr = imgList.join(',')
            let imgAuditStr = fillCsvStr('1', imgList.length)
            let record = {
                RoleId: roleId,
                Text: text,
                ImgList: imgListStr,
                ImgAudit: imgAuditStr,
                CreateTime: Date.now()
            }
            let info = await PyqMoments.insert(record)
            this.logger.info(record, 'InsertRecord')
            let momentId = info.insertId
            if (topicId) {
                await addTopicMoment(params.roleid, momentId, topicId, imgList)
                this.logger.info(record, 'InsertRecordToTopic')
            }
            await this.roleAttendActivity(roleId)
            return { id: momentId }
        }
    }
}