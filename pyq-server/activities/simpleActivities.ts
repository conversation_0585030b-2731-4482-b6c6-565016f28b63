import * as PyqMoments from '../../models/PyqMoments'
import { checkParams } from '../helper';
import _ = require('lodash');

interface BasicActivityParams {
    roleid: number
}

interface IJiaNianhuaParams extends BasicActivityParams {
    shareUrl: string
    imgUrl: string
}

export async function jiaNianHua2019ShareMoment(params: IJiaNianhuaParams) {
    let schema = {
        roleid: { type: Number },
        shareUrl: { type: String },
        imgUrl: { type: String }
    }
    await checkParams(params, schema)
    let link = PyqMoments.genLinkButton(params.shareUrl, '2019嘉年华双人同行一人免单')
    let text = `点击${link} 集赞免费领亲友票啦！你不赞我不赞，亲友想去怎么办！你也赞我也赞，门票买一送一超划算！`
    let props = {
        RoleId: params.roleid,
        ImgList: params.imgUrl,
        imgAudit: '1',
        Text: text,
        CreateTime: Date.now()
    }
    let ret = await PyqMoments.insert(props)
    return { id: ret.insertId }
}