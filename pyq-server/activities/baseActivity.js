"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FlowerRenQiCounter = exports.Activity = void 0;
const dateUtil_1 = require("../../common/dateUtil");
const PyqMoments = require("../../models/PyqMoments");
const redis_1 = require("../../common/redis");
const ParamsValidator = require("../../common/paramsValidator");
var AuditStatus;
(function (AuditStatus) {
    AuditStatus[AuditStatus["PASS"] = 1] = "PASS";
    AuditStatus[AuditStatus["REJECT"] = 2] = "REJECT";
})(AuditStatus || (AuditStatus = {}));
class Activity {
    constructor(option) {
        this.name = option.name;
        this.endDate = option.endDate;
        this.attendSetKey = 'qnm:' + this.name + ':attend_player_ids';
        this.isCountFlowerRenQi = option.isCountFlowerRenQi;
        this.isCountFlowerVote = option.isCountFlowerVote;
    }
    isOpen() {
        return (0, dateUtil_1.isBefore)(new Date(), this.endDate);
    }
    getValidator(workInfo) {
        let validator = ParamsValidator.from(workInfo)
            .param('status', { type: Number, values: [AuditStatus.PASS, AuditStatus.REJECT] })
            .param('roleId', { type: Number })
            .param('workId', { type: Number })
            .param('workName', { type: String, required: false })
            .param('urls', { type: Array, required: false });
        return validator;
    }
    checkParams(workInfo) {
        return __awaiter(this, void 0, void 0, function* () {
            let result = yield this.getValidator(workInfo).validate();
            return result;
        });
    }
    addShareMoment(workInfo) {
        return __awaiter(this, void 0, void 0, function* () {
            let isOpen = yield this.isOpen();
            if (!isOpen)
                return;
            yield this.checkParams(workInfo);
            let roleId = workInfo.roleId;
            let urls = workInfo.urls || [];
            let imgList = urls.join(',');
            let imgAudit = (new Array(urls.length)).fill(1).join(',');
            let insertInfo = yield PyqMoments.insert({
                RoleId: roleId,
                ImgList: imgList,
                imgAudit: imgAudit,
                Text: this.getShareText(workInfo),
                CreateTime: Date.now()
            });
            yield this.attend(workInfo.roleId);
            return insertInfo.insertId;
        });
    }
    attend(roleId) {
        return __awaiter(this, void 0, void 0, function* () {
            return (0, redis_1.getRedis)().saddAsync(this.attendSetKey, '' + roleId);
        });
    }
    isAttend(roleId) {
        return __awaiter(this, void 0, void 0, function* () {
            let result = yield (0, redis_1.getRedis)().sismemberAsync(this.attendSetKey, '' + roleId);
            return result === redis_1.IExistResult.Exist;
        });
    }
}
exports.Activity = Activity;
const flowerIdToVote = {
    21100029: 1,
    21100030: 10,
    21100031: 220,
    21101865: 280 // 知己之名
};
class FlowerRenQiCounter {
    constructor(activity) {
        this.activity = activity;
        this.renqiKey = 'qnm:' + activity.name + 'flower_renqi';
        this.voteKey = 'qnm:' + activity.name + 'flower_vote';
    }
    incr(roleId, flower) {
        return __awaiter(this, void 0, void 0, function* () {
            let isAttend = yield this.activity.isAttend(roleId);
            let renqi = null;
            let vote = null;
            if (isAttend) {
                if (this.activity.isCountFlowerRenQi) {
                    renqi = yield (0, redis_1.getRedis)().hincrbyAsync(this.renqiKey, '' + roleId, flower.renqi);
                }
                if (this.activity.isCountFlowerVote) {
                    let incr = flowerIdToVote[flower.id] * flower.count;
                    vote = yield (0, redis_1.getRedis)().hincrbyAsync(this.voteKey, '' + roleId, incr);
                }
            }
            return { renqi, vote };
        });
    }
}
exports.FlowerRenQiCounter = FlowerRenQiCounter;
//# sourceMappingURL=baseActivity.js.map