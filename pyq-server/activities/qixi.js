"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.auditResult = void 0;
let errorHandler = require('../errorHandler');
const PyqMoments = require("../../models/PyqMoments");
const PyqEvent = require("../../models/PyqEvent");
const redis_1 = require("../../common/redis");
const ActivityHelper = require("./activityHelper");
PyqEvent.on(PyqEvent.Events.SEND_FLOWER, function (payload) {
    return __awaiter(this, void 0, void 0, function* () {
        const receiver = payload.receiver;
        yield FlowerCounter.onSendFlowerHandler(receiver);
    });
});
var AuditStatus;
(function (AuditStatus) {
    AuditStatus[AuditStatus["PASS"] = 1] = "PASS";
})(AuditStatus || (AuditStatus = {}));
function auditResult(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        let params = req.params;
        let workInfos = params.work_infos;
        try {
            let mIds = yield auditWorkInfos(workInfos);
            res.send({ code: 0, data: { momentIds: mIds } });
        }
        catch (err) {
            errorHandler(err, req, res, next);
        }
    });
}
exports.auditResult = auditResult;
function getPreviewUrl(workId) {
    return `https://qnm.163.com/m/2018/qixih/#/list/${workId}`;
}
function getShareText(workInfo) {
    const link = PyqMoments.genLinkButton(getPreviewUrl(workInfo.workId), '点击前往');
    let workName = workInfo.workName || '';
    return `我参加了倩女七夕土味情话battle活动，我的作品#${workName}###已经通过审核啦，浪漫情话说给你听，还不快来为我加油打call！${link}`;
}
function addShareMoment(workInfo) {
    let roleId = workInfo.roleId;
    let urls = workInfo.urls || [];
    let imgList = urls.join(',');
    let imgAudit = (new Array(urls.length)).fill(1).join(',');
    return PyqMoments.insert({
        RoleId: roleId,
        ImgList: imgList,
        imgAudit: imgAudit,
        Text: getShareText(workInfo),
        CreateTime: Date.now()
    }).then(insertInfo => {
        return insertInfo.insertId;
    });
}
function auditWorkInfos(workInfos) {
    return __awaiter(this, void 0, void 0, function* () {
        let mIds = [];
        for (let item of workInfos) {
            let auditStatus = parseInt(item.status, 10);
            if (auditStatus === AuditStatus.PASS) {
                let mId = yield addShareMoment(item);
                yield FlowerCounter.initFlowerRenQi(item.roleId);
                mIds.push(mId);
            }
        }
        return mIds;
    });
}
class FlowerCounter {
    static isRoleIdAttend(roleId) {
        return __awaiter(this, void 0, void 0, function* () {
            let ret = yield (0, redis_1.getRedis)().hexistsAsync(this.INIT_FLOWER_HASH_KEY, roleId);
            return ret === redis_1.IExistResult.Exist;
        });
    }
    static initFlowerRenQi(roleId) {
        return __awaiter(this, void 0, void 0, function* () {
            let renQi = yield ActivityHelper.getFlowerRenQi(roleId);
            let ret = yield (0, redis_1.getRedis)().hsetnxAsync(this.INIT_FLOWER_HASH_KEY, '' + roleId, renQi);
            return ret;
        });
    }
    static onSendFlowerHandler(roleId) {
        return __awaiter(this, void 0, void 0, function* () {
            let isAttend = yield this.isRoleIdAttend(roleId);
            if (isAttend) {
                let renQi = yield ActivityHelper.getFlowerRenQi(roleId);
                let ret = yield (0, redis_1.getRedis)().hsetAsync(this.CUR_FLOWER_HASH_KEY, '' + roleId, renQi);
                return ret;
            }
        });
    }
}
FlowerCounter.KEY_PREFIX = 'qnm:activity:qixi_2018:';
FlowerCounter.INIT_FLOWER_HASH_KEY = FlowerCounter.KEY_PREFIX + 'initflower';
FlowerCounter.CUR_FLOWER_HASH_KEY = FlowerCounter.KEY_PREFIX + 'curflower';
//# sourceMappingURL=qixi.js.map