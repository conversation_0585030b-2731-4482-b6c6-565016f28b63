"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.onAttendTopic = void 0;
const PyqActivityTopic_1 = require("../../models/PyqActivityTopic");
const _ = require("lodash");
const config_1 = require("../common/config");
const modelProxy_1 = require("../services/modelProxy");
const util_1 = require("../../common/util");
const util2_1 = require("../../common/util2");
const activityTools_1 = require("./activityTools");
const httpLib = require("../../common/request");
let logger = (0, activityTools_1.getActivityLogger)(config_1.anniversarygift2020Cfg.name);
function onAttendTopic(params) {
    return __awaiter(this, void 0, void 0, function* () {
        let name = yield PyqActivityTopic_1.ActivityTopic.getNameById(params.topicId);
        if (_.includes(config_1.anniversarygift2020Cfg.topicNames, name)) {
            return attendByMomentId(params.momentId, params.topicId);
        }
    });
}
exports.onAttendTopic = onAttendTopic;
function attendByMomentId(momentId, topicId) {
    return __awaiter(this, void 0, void 0, function* () {
        let record = yield modelProxy_1.MomentModel.findOne({ ID: momentId });
        if (record) {
            let imgUrls = (0, util_1.csvStrToArray)(record.ImgList);
            let isMatchApply = (imgUrls.length >= config_1.anniversarygift2020Cfg.minImgLimit) && !(0, activityTools_1.isOnlyContainTopic)(record.Text);
            if (isMatchApply) {
                return requestAttendApi(topicId, record);
            }
        }
    });
}
function requestAttendApi(topicId, record) {
    return __awaiter(this, void 0, void 0, function* () {
        let imgs = (0, util_1.csvStrToArray)(record.ImgList);
        imgs = imgs.map(url => (0, util2_1.replaceHttps)(url));
        let text = (0, activityTools_1.removeTopicLink)(record.Text);
        let data = {
            roleId: record.RoleId,
            pics: imgs,
            description: text,
            topicId: topicId
        };
        logger.info('Prepare attend', data);
        let result = yield httpLib.request({
            method: "POST",
            url: config_1.anniversarygift2020Cfg.applyApi,
            body: data
        });
        logger.info('Request apply api result', { result: result, data: data });
        return result;
    });
}
//# sourceMappingURL=anniversarygift2020.js.map