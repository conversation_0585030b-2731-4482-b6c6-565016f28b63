import { getJsonInfo } from "../../common/util";
import { checkParamsByAjv } from "../common/ajvCheck";
import { RoleInfoModel } from "../models/roleInfoModel";
import { RawLogItem } from "./gameLog";
import { clazzLogger } from "../logger";

const logger = clazzLogger("consumers.onPlayerLevelUpLog");

interface PlayerLevelUpLog {
  role_id: number;
  role_level: number;
  max_level: number;
}

// 玩家升级日志
const playerLevelUpLogSchema = {
  type: "object",
  required: ["role_id", "role_level", "max_level"],
  properties: {
    role_id: { type: "number" },
    role_level: { type: "number" },
    max_level: { type: "number" },
  },
};

export async function onPlayerLevelUpLog(rawlog: RawLogItem) {
  const log = getJsonInfo<PlayerLevelUpLog>(rawlog.message);
  await checkParamsByAjv(log, playerLevelUpLogSchema);
  const upRet = await RoleInfoModel.updateByCondition(
    { RoleId: log.role_id },
    { MaxLevel: log.max_level, Level: log.role_level, UpdateTime: Date.now() }
  );
  logger.info(
    { roleId: log.role_id, roleLevel: log.role_level, max_level: log.max_level, affectedRows: upRet.affectedRows },
    "UpdateLevelByPlayerLevelUpOK"
  );
  return upRet;
}
