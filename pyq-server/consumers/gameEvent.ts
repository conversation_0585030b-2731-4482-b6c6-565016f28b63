export enum GameLogEvents {
  // 时装抽奖日志
  FashionLottery_LotteryOnce = "FashionLottery_LotteryOnce",
  // 时装抽奖长期日志
  FashionLotteryLongTerm_LotteryOnce = "FashionLotteryLongterm_LotteryOnce",

  // 信用分日志
  CreditScore = "CreditScore",
  // 登录角色额外信息日志
  LoginRole_Additional = "LoginRole_Additional",
  // 玩家升级日志
  PlayerLevelUp = "PlayerLevelUp",

  /**
   * BandHeat
   * [2025-06-25 21:55:20][BandHeat],{"server":"1", "band_id":"300001", "band_name":"yk的乐团", "band_level":3, "heat":60, "week_heat":50, "time":1750859718, "show_amount":10, "recording_amount":0, "u_dtls":[]}
  */
  BandHeat = "BandHeat",
}


export function getEventIds(): string[] {
  return Object.values(GameLogEvents);
}