"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getEventIds = exports.GameLogEvents = void 0;
var GameLogEvents;
(function (GameLogEvents) {
    // 时装抽奖日志
    GameLogEvents["FashionLottery_LotteryOnce"] = "FashionLottery_LotteryOnce";
    // 时装抽奖长期日志
    GameLogEvents["FashionLotteryLongTerm_LotteryOnce"] = "FashionLotteryLongterm_LotteryOnce";
    // 信用分日志
    GameLogEvents["CreditScore"] = "CreditScore";
    // 登录角色额外信息日志
    GameLogEvents["LoginRole_Additional"] = "LoginRole_Additional";
    // 玩家升级日志
    GameLogEvents["PlayerLevelUp"] = "PlayerLevelUp";
    /**
     * BandHeat
     * [2025-06-25 21:55:20][BandHeat],{"server":"1", "band_id":"300001", "band_name":"yk的乐团", "band_level":3, "heat":60, "week_heat":50, "time":1750859718, "show_amount":10, "recording_amount":0, "u_dtls":[]}
    */
    GameLogEvents["BandHeat"] = "BandHeat";
})(GameLogEvents = exports.GameLogEvents || (exports.GameLogEvents = {}));
function getEventIds() {
    return Object.values(GameLogEvents);
}
exports.getEventIds = getEventIds;
//# sourceMappingURL=gameEvent.js.map