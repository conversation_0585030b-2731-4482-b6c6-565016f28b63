import { getJsonInfo } from "../../common/util";
import { checkParamsByAjv } from "../common/ajvCheck";
import { RawLogItem } from "./gameLog";
import { clazzLogger } from "../logger";
import { PlayerCreditScoreService } from "../services/playerCreditScoreService";

const logger = clazzLogger("consumers.onCreditScoreLog");

export interface CreditScoreLog {
  server: number;
  account_id: string;
  role_id: number;
  role_name: string;
  role_level: number;
  uid: string;
  mpay_account: string;
  ip: string;
  credit_score: number;
  all_score: number;
}

// 定义 CreditScoreLog 的 AJV 验证模式
const creditScoreLogSchema = {
  type: "object",
  required: ["server", "account_id", "role_id", "role_level", "uid", "mpay_account", "credit_score", "all_score"],
  properties: {
    server: { type: "string" },
    account_id: { type: "string" },
    role_id: { type: "number" },
    role_level: { type: "number" },
    uid: { type: "string" },
    mpay_account: { type: "string" },
    credit_score: { type: "number" },
    all_score: { type: "number" },
  },
};

export async function onCreditScoreLog(log: RawLogItem) {
  const creditScoreLog = getJsonInfo<CreditScoreLog>(log.message);
  try {
    await checkParamsByAjv(creditScoreLog, creditScoreLogSchema);
    const scoreRecord = {
      RoleId: creditScoreLog.role_id,
      ServerId: creditScoreLog.server,
      AccountId: creditScoreLog.account_id,
      Uid: creditScoreLog.uid,
      MpayAccount: creditScoreLog.mpay_account,
      CreditScore: creditScoreLog.credit_score,
      AllScore: creditScoreLog.all_score,
    };
    const upRet = await PlayerCreditScoreService.updatePlayerCreditScore(creditScoreLog.role_id, scoreRecord);
    return upRet;
  } catch (error) {
    logger.error({ error, creditScoreLog }, "CreditScoreLogInvalid");
    return;
  }
}
