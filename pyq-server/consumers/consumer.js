"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.transformKafkaMessage = exports.processKafkaMessage = exports.processKafkaMessageWithConfig = exports.listenKafkaTopic = exports.registerEventHandler = exports.configureConsumer = exports.EventHandlerRegistry = void 0;
//@ts-ignore
const kafkajs_1 = require("kafkajs");
const logger_1 = require("../common/logger");
const config_1 = require("../common/config");
const gameLog_1 = require("./gameLog");
const gameEvent_1 = require("./gameEvent");
const onCreditScoreLog_1 = require("./onCreditScoreLog");
const onLoginRoleAdditionalLog_1 = require("./onLoginRoleAdditionalLog");
const onPlayerLevelUpLog_1 = require("./onPlayerLevelUpLog");
const onBandHeatLog_1 = require("./onBandHeatLog");
const LZ4 = require("kafkajs-lz4");
//@ts-ignore
kafkajs_1.CompressionCodecs[kafkajs_1.CompressionTypes.LZ4] = new LZ4().codec;
// 事件处理器注册表
class EventHandlerRegistry {
    constructor() {
        this.handlers = new Map();
    }
    /** 注册事件处理器 */
    register(eventName, handler) {
        this.handlers.set(eventName, handler);
    }
    /** 获取事件处理器 */
    getHandler(eventName) {
        return this.handlers.get(eventName);
    }
    /** 获取所有已注册的事件名称 */
    getRegisteredEvents() {
        return Array.from(this.handlers.keys());
    }
    /** 检查事件是否已注册 */
    hasHandler(eventName) {
        return this.handlers.has(eventName);
    }
}
exports.EventHandlerRegistry = EventHandlerRegistry;
// 全局事件处理器注册表
const globalEventRegistry = new EventHandlerRegistry();
// 注册默认的事件处理器
globalEventRegistry.register(gameEvent_1.GameLogEvents.CreditScore, onCreditScoreLog_1.onCreditScoreLog);
globalEventRegistry.register(gameEvent_1.GameLogEvents.LoginRole_Additional, onLoginRoleAdditionalLog_1.onLoginRoleAdditionalLog);
globalEventRegistry.register(gameEvent_1.GameLogEvents.PlayerLevelUp, onPlayerLevelUpLog_1.onPlayerLevelUpLog);
globalEventRegistry.register(gameEvent_1.GameLogEvents.BandHeat, onBandHeatLog_1.onBandHeatLog);
// 全局配置，用于当前进程的Consumer配置
let currentConsumerConfig = {};
// 配置当前进程的Consumer
function configureConsumer(config) {
    currentConsumerConfig = Object.assign(Object.assign({}, currentConsumerConfig), config);
}
exports.configureConsumer = configureConsumer;
// 注册事件处理器
function registerEventHandler(eventName, handler) {
    globalEventRegistry.register(eventName, handler);
}
exports.registerEventHandler = registerEventHandler;
// 获取当前配置的允许事件列表
function getAllowedEvents() {
    return currentConsumerConfig.allowedEvents || globalEventRegistry.getRegisteredEvents();
}
// 检查消息是否应该被处理（支持配置化过滤）
function isRawMessageShouldBeProcessedWithConfig(message) {
    const allowedEvents = getAllowedEvents();
    for (let eventId of allowedEvents) {
        if (message.includes(`[${eventId}]`)) {
            return true;
        }
    }
    return false;
}
// 持久化日志消息（支持配置化过滤）
function persistLogMessageWithConfig(consumer, logItem) {
    return __awaiter(this, void 0, void 0, function* () {
        if (!logItem)
            return;
        const allowedEvents = getAllowedEvents();
        // 如果配置了允许的事件列表，检查当前事件是否在允许列表中
        if (currentConsumerConfig.allowedEvents && !allowedEvents.includes(logItem.eventName)) {
            consumer.logger().debug("PersistLogMessageSkipCauseEventNotAllowed", {
                logItem,
                allowedEvents
            });
            return;
        }
        try {
            const handler = globalEventRegistry.getHandler(logItem.eventName);
            if (handler) {
                yield handler(logItem);
            }
            else {
                consumer.logger().debug("PersistLogMessageSkipCauseNotMatch", { logItem });
            }
        }
        catch (err) {
            consumer.logger().error("PersistLogMessageFail", { err, logItem });
        }
    });
}
// 向后兼容的全局变量
let kafka = null;
let consumer = null;
const bunyanLoggerCreator = (level) => {
    const logger = (0, logger_1.getLogger)("kafka_consumer", (0, gameLog_1.toBunyanLogLevel)(level));
    logger.info("initKafkaLog");
    return ({ namespace, level, label, log }) => {
        const { message } = log, extra = __rest(log, ["message"]);
        if (level === kafkajs_1.logLevel.ERROR) {
            logger.warn({ label, extra, namespace }, message);
        }
        else {
            logger.info({ label, extra, namespace }, message);
        }
    };
};
// 向后兼容的函数
function createKafkaClient() {
    const clientOption = {
        clientId: config_1.kafkaCfg.clientId,
        brokers: config_1.kafkaCfg.brokers,
        logLevel: kafkajs_1.logLevel[config_1.kafkaCfg.logLevel],
        logCreator: bunyanLoggerCreator,
        requestTimeout: 60000,
        retry: {
            initialRetryTime: 1000,
            retries: 10 // KafkaJS 默认会对超时等可重试错误进行重试
        },
    };
    if (config_1.kafkaCfg.sasl && config_1.kafkaCfg.sasl.username && config_1.kafkaCfg.sasl.password) {
        clientOption.sasl = config_1.kafkaCfg.sasl;
    }
    const kafka = new kafkajs_1.Kafka(clientOption);
    return kafka;
}
function getKafkaClient() {
    if (kafka === null) {
        kafka = createKafkaClient();
    }
    return kafka;
}
function getConsumer() {
    if (consumer === null) {
        consumer = createConsumer();
    }
    return consumer;
}
function createConsumer() {
    const groupId = currentConsumerConfig.groupId || config_1.kafkaCfg.groupId;
    const consumer = getKafkaClient().consumer({ groupId });
    consumer.logger().info("createConsumerOK", { groupId, allowedEvents: getAllowedEvents() });
    return consumer;
}
// 向后兼容的API - 保持现有接口不变
function listenKafkaTopic() {
    var _a, _b, _c;
    return __awaiter(this, void 0, void 0, function* () {
        const consumer = getConsumer();
        try {
            const connected = yield consumer.connect();
            consumer.logger().info("connectOK", { connected });
            yield consumer.subscribe({
                topic: config_1.kafkaCfg.topic,
                fromBeginning: (_a = currentConsumerConfig.fromBeginning) !== null && _a !== void 0 ? _a : config_1.kafkaCfg.fromBeginning,
            });
            consumer.logger().info("subscribeOK", {
                topic: config_1.kafkaCfg.topic,
                fromBeginning: (_b = currentConsumerConfig.fromBeginning) !== null && _b !== void 0 ? _b : config_1.kafkaCfg.fromBeginning,
                allowedEvents: getAllowedEvents()
            });
            yield consumer.run({
                partitionsConsumedConcurrently: (_c = currentConsumerConfig.partitionsConsumedConcurrently) !== null && _c !== void 0 ? _c : config_1.kafkaCfg.partitionsConsumedConcurrently,
                eachMessage: ({ topic, partition, message }) => __awaiter(this, void 0, void 0, function* () {
                    return processKafkaMessageWithConfig(consumer, topic, partition, message);
                }),
            });
        }
        catch (err) {
            consumer.logger().error("ListenKafkaTopicFail", { err, kafkaCfg: config_1.kafkaCfg });
        }
    });
}
exports.listenKafkaTopic = listenKafkaTopic;
// 新的配置化消息处理函数
function processKafkaMessageWithConfig(consumer, topic, partition, message) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            if (!message.value) {
                return;
            }
            const logMessage = message.value.toString();
            if (!isRawMessageShouldBeProcessedWithConfig(logMessage)) {
                return;
            }
            const logItem = yield transformKafkaMessage(consumer, logMessage);
            yield persistLogMessageWithConfig(consumer, logItem);
        }
        catch (err) {
            consumer.logger().error("ProcessKafkaMessageFail", { err, topic, partition, message });
        }
    });
}
exports.processKafkaMessageWithConfig = processKafkaMessageWithConfig;
// 向后兼容的函数 - 保持原有API不变
function isRawMessageShouldBeProcessed(message) {
    const eventIds = (0, gameEvent_1.getEventIds)();
    for (let eventId of eventIds) {
        if (message.includes(`[${eventId}]`)) {
            return true;
        }
    }
    return false;
}
function processKafkaMessage(consumer, topic, partition, message) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            if (!message.value) {
                return;
            }
            const logMessage = message.value.toString();
            if (!isRawMessageShouldBeProcessed(logMessage)) {
                return;
            }
            const logItem = yield transformKafkaMessage(consumer, logMessage);
            yield persistLogMessage(consumer, logItem);
        }
        catch (err) {
            consumer.logger().error("ProcessKafkaMessageFail", { err, topic, partition, message });
        }
    });
}
exports.processKafkaMessage = processKafkaMessage;
function persistLogMessage(consumer, logItem) {
    return __awaiter(this, void 0, void 0, function* () {
        if (!logItem)
            return;
        try {
            // 使用新的事件处理器注册表
            const handler = globalEventRegistry.getHandler(logItem.eventName);
            if (handler) {
                yield handler(logItem);
            }
            else {
                consumer.logger().debug("PersistLogMessageSkipCauseNotMatch", { logItem });
            }
        }
        catch (err) {
            consumer.logger().error("PersistLogMessageFail", { err, logItem });
        }
    });
}
function transformKafkaMessage(consumer, message) {
    return __awaiter(this, void 0, void 0, function* () {
        const logItem = (0, gameLog_1.parseLogRawMessage)(message);
        if (logItem && logItem.eventName && logItem.message) {
            consumer.logger().debug("TransformKafkaMessageOK", { logItem });
            return logItem;
        }
        else {
            consumer.logger().warn("TransformKafkaMessageFail", { logItem, message });
            return null;
        }
    });
}
exports.transformKafkaMessage = transformKafkaMessage;
//# sourceMappingURL=consumer.js.map