"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.onLoginRoleAdditionalLog = void 0;
const util_1 = require("../../common/util");
const ajvCheck_1 = require("../common/ajvCheck");
const roleInfoModel_1 = require("../models/roleInfoModel");
const logger_1 = require("../logger");
const logger = (0, logger_1.clazzLogger)("consumers.onLoginRoleAdditionalLog");
// 角色登录额外信息日志
const loginRoleLogSchema = {
    type: "object",
    required: ["role_id", "role_level", "max_level"],
    properties: {
        role_id: { type: "number" },
        role_level: { type: "number" },
        max_level: { type: "number" },
    },
};
function onLoginRoleAdditionalLog(rawlog) {
    return __awaiter(this, void 0, void 0, function* () {
        const log = (0, util_1.getJsonInfo)(rawlog.message);
        yield (0, ajvCheck_1.checkParamsByAjv)(log, loginRoleLogSchema);
        const upRet = yield roleInfoModel_1.RoleInfoModel.updateByCondition({ RoleId: log.role_id }, { MaxLevel: log.max_level, Level: log.role_level, UpdateTime: Date.now() });
        logger.info({ roleId: log.role_id, roleLevel: log.role_level, max_level: log.max_level, affectedRows: upRet.affectedRows }, "UpdateLevelByLoginRoleAdditionalOK");
        return upRet;
    });
}
exports.onLoginRoleAdditionalLog = onLoginRoleAdditionalLog;
//# sourceMappingURL=onLoginRoleAdditionalLog.js.map