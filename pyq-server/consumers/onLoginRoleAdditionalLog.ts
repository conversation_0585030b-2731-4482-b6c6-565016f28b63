import { getJsonInfo } from "../../common/util";
import { checkParamsByAjv } from "../common/ajvCheck";
import { RoleInfoModel } from "../models/roleInfoModel";
import { RawLogItem } from "./gameLog";
import { clazzLogger } from "../logger";

const logger = clazzLogger("consumers.onLoginRoleAdditionalLog");

// 角色登录额外信息日志
const loginRoleLogSchema = {
  type: "object",
  required: ["role_id", "role_level", "max_level"],
  properties: {
    role_id: { type: "number" },
    role_level: { type: "number" },
    max_level: { type: "number" },
  },
};

interface LoginRoleLog {
  role_id: number;
  role_level: number;
  max_level: number;
}

export async function onLoginRoleAdditionalLog(rawlog: RawLogItem) {
  const log = getJsonInfo<LoginRoleLog>(rawlog.message);
  await checkParamsByAjv(log, loginRoleLogSchema);
  const upRet = await RoleInfoModel.updateByCondition(
    { RoleId: log.role_id },
    { MaxLevel: log.max_level, Level: log.role_level, UpdateTime: Date.now() }
  );
  logger.info(
    { roleId: log.role_id, roleLevel: log.role_level, max_level: log.max_level, affectedRows: upRet.affectedRows },
    "UpdateLevelByLoginRoleAdditionalOK"
  );
  return upRet;
}
