"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.onCreditScoreLog = void 0;
const util_1 = require("../../common/util");
const ajvCheck_1 = require("../common/ajvCheck");
const logger_1 = require("../logger");
const playerCreditScoreService_1 = require("../services/playerCreditScoreService");
const logger = (0, logger_1.clazzLogger)("consumers.onCreditScoreLog");
// 定义 CreditScoreLog 的 AJV 验证模式
const creditScoreLogSchema = {
    type: "object",
    required: ["server", "account_id", "role_id", "role_level", "uid", "mpay_account", "credit_score", "all_score"],
    properties: {
        server: { type: "string" },
        account_id: { type: "string" },
        role_id: { type: "number" },
        role_level: { type: "number" },
        uid: { type: "string" },
        mpay_account: { type: "string" },
        credit_score: { type: "number" },
        all_score: { type: "number" },
    },
};
function onCreditScoreLog(log) {
    return __awaiter(this, void 0, void 0, function* () {
        const creditScoreLog = (0, util_1.getJsonInfo)(log.message);
        try {
            yield (0, ajvCheck_1.checkParamsByAjv)(creditScoreLog, creditScoreLogSchema);
            const scoreRecord = {
                RoleId: creditScoreLog.role_id,
                ServerId: creditScoreLog.server,
                AccountId: creditScoreLog.account_id,
                Uid: creditScoreLog.uid,
                MpayAccount: creditScoreLog.mpay_account,
                CreditScore: creditScoreLog.credit_score,
                AllScore: creditScoreLog.all_score,
            };
            const upRet = yield playerCreditScoreService_1.PlayerCreditScoreService.updatePlayerCreditScore(creditScoreLog.role_id, scoreRecord);
            return upRet;
        }
        catch (error) {
            logger.error({ error, creditScoreLog }, "CreditScoreLogInvalid");
            return;
        }
    });
}
exports.onCreditScoreLog = onCreditScoreLog;
//# sourceMappingURL=onCreditScoreLog.js.map