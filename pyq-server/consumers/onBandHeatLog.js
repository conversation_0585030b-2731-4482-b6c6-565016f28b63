"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.onBandHeatLog = void 0;
const util_1 = require("../../common/util");
const dateUtil_1 = require("../../common/dateUtil");
const ajvCheck_1 = require("../common/ajvCheck");
const musicClubModel_1 = require("../models/musicClub/musicClubModel");
const musicClubWeekHotModel_1 = require("../models/musicClub/musicClubWeekHotModel");
const logger_1 = require("../logger");
const context_1 = require("../context");
const logger = (0, logger_1.clazzLogger)("consumers.onBandHeatLog");
// 乐团热度日志
const bandHeatLogSchema = {
    type: "object",
    required: ["server", "band_id", "band_name", "band_level", "heat", "week_heat", "time"],
    properties: {
        server: { type: "string" },
        band_id: { type: "string" },
        band_name: { type: "string" },
        band_level: { type: "number" },
        heat: { type: "number" },
        week_heat: { type: "number" },
        time: { type: "number" },
        show_amount: { type: "number" },
        recording_amount: { type: "number" },
    },
};
function onBandHeatLog(rawlog) {
    return __awaiter(this, void 0, void 0, function* () {
        const log = (0, util_1.getJsonInfo)(rawlog.message);
        yield (0, ajvCheck_1.checkParamsByAjv)(log, bandHeatLogSchema);
        const ctx = context_1.Context.emptyContext();
        ctx.set("log_id", log.band_id + "_" + log.time);
        const musicClubId = parseInt(log.band_id);
        const serverId = parseInt(log.server);
        const currentTime = log.time * 1000; // 转换为毫秒时间戳
        const weekDs = (0, dateUtil_1.getWeekDs)(currentTime);
        const musicClubModel = musicClubModel_1.MusicClubModel.getInstance();
        const musicClubWeekHotModel = musicClubWeekHotModel_1.MusicClubWeekHotModel.getInstance();
        try {
            // 更新乐团总热度
            const updateHotRet = yield musicClubModel.updateByCondition({ ID: musicClubId, Status: 0 }, { Hot: log.heat, LastUpdateHotTime: currentTime });
            if (updateHotRet.affectedRows > 0) {
                logger.info({ ctx, musicClubId, serverId, bandName: log.band_name, heat: log.heat, affectedRows: updateHotRet.affectedRows }, "UpdateBandHotSuccess");
            }
            else {
                logger.error({ ctx, musicClubId, serverId, bandName: log.band_name, heat: log.heat }, "UpdateBandHotNotFound");
            }
            // 创建周热度记录
            const updateWeekHotRet = yield musicClubWeekHotModel.createOrUpdate({
                MusicClubId: musicClubId,
                WeekDs: weekDs,
                Hot: log.week_heat,
                UpdateTime: Date.now(),
                Level: log.band_level,
                LastUpdateHotTime: currentTime
            }, {
                Hot: log.week_heat,
                UpdateTime: Date.now(),
                Level: log.band_level,
                LastUpdateHotTime: currentTime
            });
            logger.info({ ctx, musicClubId, serverId, bandName: log.band_name, weekDs, weekHeat: log.week_heat, totalHeat: log.heat, updateHotAffectedRows: updateHotRet.affectedRows, updateWeekHotAffectedRows: (updateWeekHotRet === null || updateWeekHotRet === void 0 ? void 0 : updateWeekHotRet.affectedRows) || 0 }, "ProcessBandHeatLogSuccess");
            return { updateHotRet, updateWeekHotRet };
        }
        catch (error) {
            logger.error({ ctx, error, musicClubId, serverId, bandName: log.band_name, log }, "ProcessBandHeatLogError");
            throw error;
        }
    });
}
exports.onBandHeatLog = onBandHeatLog;
//# sourceMappingURL=onBandHeatLog.js.map