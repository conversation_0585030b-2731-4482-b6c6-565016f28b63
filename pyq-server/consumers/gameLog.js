"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.parseLogRawMessage = exports.toBunyanLogLevel = exports.LOG_ITEM_REGEX = void 0;
const kafkajs_1 = require("kafkajs");
//@ts-ignore
exports.LOG_ITEM_REGEX = /\[(?<date>.*?)\]\[(?<eventName>.*?)\],(?<message>\{.*\})/;
function toBunyanLogLevel(level) {
    switch (level) {
        case kafkajs_1.logLevel.ERROR:
            return "error";
        case kafkajs_1.logLevel.NOTHING:
            return "trace";
        case kafkajs_1.logLevel.WARN:
            return "warn";
        case kafkajs_1.logLevel.INFO:
            return "info";
        case kafkajs_1.logLevel.DEBUG:
            return "debug";
    }
}
exports.toBunyanLogLevel = toBunyanLogLevel;
function parseLogRawMessage(message) {
    const match = message.match(exports.LOG_ITEM_REGEX);
    if (match && match.groups) {
        const item = {
            logFile: match.groups.logFile || "",
            date: match.groups.date || "",
            eventName: match.groups.eventName,
            message: match.groups.message || "",
        };
        return item;
    }
    else {
        return null;
    }
}
exports.parseLogRawMessage = parseLogRawMessage;
//# sourceMappingURL=gameLog.js.map