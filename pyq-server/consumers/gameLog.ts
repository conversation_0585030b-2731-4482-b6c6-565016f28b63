import { logLevel } from "kafkajs";

export interface RawLogItem {
  logFile?: string;
  date: string;
  eventName: string;
  message: string;
}

//@ts-ignore
export const LOG_ITEM_REGEX = /\[(?<date>.*?)\]\[(?<eventName>.*?)\],(?<message>\{.*\})/;

export function toBunyanLogLevel(level: logLevel) {
  switch (level) {
    case logLevel.ERROR:
      return "error";
    case logLevel.NOTHING:
      return "trace";
    case logLevel.WARN:
      return "warn";
    case logLevel.INFO:
      return "info";
    case logLevel.DEBUG:
      return "debug";
  }
}

export function parseLogRawMessage(message: string): RawLogItem {
  const match = message.match(LOG_ITEM_REGEX);
  if (match && match.groups) {
    const item: RawLogItem = {
      logFile: match.groups.logFile || "",
      date: match.groups.date || "",
      eventName: match.groups.eventName as string,
      message: match.groups.message || "",
    };
    return item;
  } else {
    return null;
  }
}
