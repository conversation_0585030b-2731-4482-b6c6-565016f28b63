import { getJsonInfo } from "../../common/util";
import { getWeekDs } from "../../common/dateUtil";
import { checkParamsByAjv } from "../common/ajvCheck";
import { MusicClubModel } from "../models/musicClub/musicClubModel";
import { MusicClubWeekHotModel } from "../models/musicClub/musicClubWeekHotModel";
import { RawLogItem } from "./gameLog";
import { clazzLogger } from "../logger";
import { Context } from "../context";

const logger = clazzLogger("consumers.onBandHeatLog");

interface BandHeatLog {
    server: string;
    band_id: string;
    band_name: string;
    band_level: number;
    heat: number;
    week_heat: number;
    time: number;
    show_amount: number;
    recording_amount: number;
}

// 乐团热度日志
const bandHeatLogSchema = {
    type: "object",
    required: ["server", "band_id", "band_name", "band_level", "heat", "week_heat", "time"],
    properties: {
        server: { type: "string" },
        band_id: { type: "string" },
        band_name: { type: "string" },
        band_level: { type: "number" },
        heat: { type: "number" },
        week_heat: { type: "number" },
        time: { type: "number" },
        show_amount: { type: "number" },
        recording_amount: { type: "number" },
    },
};

export async function onBandHeatLog(rawlog: RawLogItem) {
    const log = getJsonInfo<BandHeatLog>(rawlog.message);
    await checkParamsByAjv(log, bandHeatLogSchema);

    const ctx = Context.emptyContext();
    ctx.set("log_id", log.band_id + "_" + log.time)
    const musicClubId = parseInt(log.band_id);
    const serverId = parseInt(log.server);
    const currentTime = log.time * 1000; // 转换为毫秒时间戳
    const weekDs = getWeekDs(currentTime);

    const musicClubModel = MusicClubModel.getInstance();
    const musicClubWeekHotModel = MusicClubWeekHotModel.getInstance();

    try {
        // 更新乐团总热度
        const updateHotRet = await musicClubModel.updateByCondition(
            { ID: musicClubId, Status: 0 },
            { Hot: log.heat, LastUpdateHotTime: currentTime }
        );

        if (updateHotRet.affectedRows > 0) {
            logger.info({ ctx, musicClubId, serverId, bandName: log.band_name, heat: log.heat, affectedRows: updateHotRet.affectedRows }, "UpdateBandHotSuccess");
        } else {
            logger.error({ ctx, musicClubId, serverId, bandName: log.band_name, heat: log.heat }, "UpdateBandHotNotFound");
        }

        // 创建周热度记录
        const updateWeekHotRet = await musicClubWeekHotModel.createOrUpdate({
            MusicClubId: musicClubId,
            WeekDs: weekDs,
            Hot: log.week_heat,
            UpdateTime: Date.now(),
            Level: log.band_level,
            LastUpdateHotTime: currentTime
        }, {
            Hot: log.week_heat,
            UpdateTime: Date.now(),
            Level: log.band_level,
            LastUpdateHotTime: currentTime
        });

        logger.info({ ctx, musicClubId, serverId, bandName: log.band_name, weekDs, weekHeat: log.week_heat, totalHeat: log.heat, updateHotAffectedRows: updateHotRet.affectedRows, updateWeekHotAffectedRows: updateWeekHotRet?.affectedRows || 0 }, "ProcessBandHeatLogSuccess");
        return { updateHotRet, updateWeekHotRet };
    } catch (error) {
        logger.error({ ctx, error, musicClubId, serverId, bandName: log.band_name, log }, "ProcessBandHeatLogError");
        throw error;
    }
}