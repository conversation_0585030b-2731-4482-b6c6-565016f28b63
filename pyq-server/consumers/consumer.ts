//@ts-ignore
import { Kafka, KafkaConfig, KafkaMessage, Consumer, logLevel, CompressionTypes, CompressionCodecs } from "kafkajs";
import { getLogger } from "../common/logger";
import { kafkaCfg } from "../common/config";
import { parseLogRawMessage, RawLogItem, toBunyanLogLevel } from "./gameLog";
import { GameLogEvents, getEventIds } from "./gameEvent";
import { onCreditScoreLog } from "./onCreditScoreLog";
import { onLoginRoleAdditionalLog } from "./onLoginRoleAdditionalLog";
import { onPlayerLevelUpLog } from "./onPlayerLevelUpLog";
import { onBandHeatLog } from "./onBandHeatLog";
import * as LZ4 from "kafkajs-lz4";
//@ts-ignore
CompressionCodecs[CompressionTypes.LZ4] = new LZ4().codec;

// 事件处理器类型定义
export type EventHandler = (logItem: RawLogItem) => Promise<any>;

// 简化的Consumer配置接口
export interface ConsumerConfig {
  /** Consumer Group ID，如果不指定则使用默认配置 */
  groupId?: string;
  /** 要监听的事件列表，如果为空则监听所有已注册的事件 */
  allowedEvents?: string[];
  /** 是否从头开始消费 */
  fromBeginning?: boolean;
  /** 并发处理的分区数 */
  partitionsConsumedConcurrently?: number;
}

// 事件处理器注册表
export class EventHandlerRegistry {
  private handlers = new Map<string, EventHandler>();

  /** 注册事件处理器 */
  register(eventName: string, handler: EventHandler): void {
    this.handlers.set(eventName, handler);
  }

  /** 获取事件处理器 */
  getHandler(eventName: string): EventHandler | undefined {
    return this.handlers.get(eventName);
  }

  /** 获取所有已注册的事件名称 */
  getRegisteredEvents(): string[] {
    return Array.from(this.handlers.keys());
  }

  /** 检查事件是否已注册 */
  hasHandler(eventName: string): boolean {
    return this.handlers.has(eventName);
  }
}

// 全局事件处理器注册表
const globalEventRegistry = new EventHandlerRegistry();

// 注册默认的事件处理器
globalEventRegistry.register(GameLogEvents.CreditScore, onCreditScoreLog);
globalEventRegistry.register(GameLogEvents.LoginRole_Additional, onLoginRoleAdditionalLog);
globalEventRegistry.register(GameLogEvents.PlayerLevelUp, onPlayerLevelUpLog);
globalEventRegistry.register(GameLogEvents.BandHeat, onBandHeatLog);

// 全局配置，用于当前进程的Consumer配置
let currentConsumerConfig: ConsumerConfig = {};

// 配置当前进程的Consumer
export function configureConsumer(config: ConsumerConfig): void {
  currentConsumerConfig = { ...currentConsumerConfig, ...config };
}

// 注册事件处理器
export function registerEventHandler(eventName: string, handler: EventHandler): void {
  globalEventRegistry.register(eventName, handler);
}

// 获取当前配置的允许事件列表
function getAllowedEvents(): string[] {
  return currentConsumerConfig.allowedEvents || globalEventRegistry.getRegisteredEvents();
}

// 检查消息是否应该被处理（支持配置化过滤）
function isRawMessageShouldBeProcessedWithConfig(message: string): boolean {
  const allowedEvents = getAllowedEvents();
  for (let eventId of allowedEvents) {
    if (message.includes(`[${eventId}]`)) {
      return true;
    }
  }
  return false;
}

// 持久化日志消息（支持配置化过滤）
async function persistLogMessageWithConfig(consumer: Consumer, logItem: RawLogItem): Promise<void> {
  if (!logItem) return;

  const allowedEvents = getAllowedEvents();
  // 如果配置了允许的事件列表，检查当前事件是否在允许列表中
  if (currentConsumerConfig.allowedEvents && !allowedEvents.includes(logItem.eventName)) {
    consumer.logger().debug("PersistLogMessageSkipCauseEventNotAllowed", {
      logItem,
      allowedEvents
    });
    return;
  }

  try {
    const handler = globalEventRegistry.getHandler(logItem.eventName);
    if (handler) {
      await handler(logItem);
    } else {
      consumer.logger().debug("PersistLogMessageSkipCauseNotMatch", { logItem });
    }
  } catch (err) {
    consumer.logger().error("PersistLogMessageFail", { err, logItem });
  }
}

// 向后兼容的全局变量
let kafka: Kafka = null;
let consumer: Consumer = null;

const bunyanLoggerCreator = (level: logLevel) => {
  const logger = getLogger("kafka_consumer", toBunyanLogLevel(level));
  logger.info("initKafkaLog");
  return ({ namespace, level, label, log }) => {
    const { message, ...extra } = log;
    if (level === logLevel.ERROR) {
      logger.warn({ label, extra, namespace }, message);
    } else {
      logger.info({ label, extra, namespace }, message);
    }
  };
};

// 向后兼容的函数
function createKafkaClient() {
  const clientOption: KafkaConfig = {
    clientId: kafkaCfg.clientId,
    brokers: kafkaCfg.brokers,
    logLevel: logLevel[kafkaCfg.logLevel],
    logCreator: bunyanLoggerCreator,
    requestTimeout: 60000, // 设置为 60000ms (60秒)
    retry: {
        initialRetryTime: 1000,
        retries: 10 // KafkaJS 默认会对超时等可重试错误进行重试
    },
  };
  if (kafkaCfg.sasl && kafkaCfg.sasl.username && kafkaCfg.sasl.password) {
    clientOption.sasl = kafkaCfg.sasl;
  }

  const kafka = new Kafka(clientOption);
  return kafka;
}

function getKafkaClient() {
  if (kafka === null) {
    kafka = createKafkaClient();
  }
  return kafka;
}

function getConsumer() {
  if (consumer === null) {
    consumer = createConsumer();
  }
  return consumer;
}

function createConsumer() {
  const groupId = currentConsumerConfig.groupId || kafkaCfg.groupId;
  const consumer = getKafkaClient().consumer({ groupId });
  consumer.logger().info("createConsumerOK", { groupId, allowedEvents: getAllowedEvents() });
  return consumer;
}

// 向后兼容的API - 保持现有接口不变
export async function listenKafkaTopic() {
  const consumer = getConsumer();
  try {
    const connected = await consumer.connect();
    consumer.logger().info("connectOK", { connected });

    await consumer.subscribe({
      topic: kafkaCfg.topic,
      fromBeginning: currentConsumerConfig.fromBeginning ?? kafkaCfg.fromBeginning,
    });

    consumer.logger().info("subscribeOK", {
      topic: kafkaCfg.topic,
      fromBeginning: currentConsumerConfig.fromBeginning ?? kafkaCfg.fromBeginning,
      allowedEvents: getAllowedEvents()
    });

    await consumer.run({
      partitionsConsumedConcurrently: currentConsumerConfig.partitionsConsumedConcurrently ?? kafkaCfg.partitionsConsumedConcurrently,
      eachMessage: async ({ topic, partition, message }) => {
        return processKafkaMessageWithConfig(consumer, topic, partition, message);
      },
    });
  } catch (err) {
    consumer.logger().error("ListenKafkaTopicFail", { err, kafkaCfg });
  }
}

// 新的配置化消息处理函数
export async function processKafkaMessageWithConfig(consumer: Consumer, topic: string, partition: number, message: KafkaMessage) {
  try {
    if (!message.value) {
      return;
    }
    const logMessage = message.value.toString();
    if (!isRawMessageShouldBeProcessedWithConfig(logMessage)) {
      return;
    }
    const logItem = await transformKafkaMessage(consumer, logMessage);
    await persistLogMessageWithConfig(consumer, logItem);
  } catch (err) {
    consumer.logger().error("ProcessKafkaMessageFail", { err, topic, partition, message });
  }
}



// 向后兼容的函数 - 保持原有API不变
function isRawMessageShouldBeProcessed(message: string) {
  const eventIds = getEventIds();
  for (let eventId of eventIds) {
    if (message.includes(`[${eventId}]`)) {
      return true;
    }
  }
  return false;
}

export async function processKafkaMessage(consumer: Consumer, topic: string, partition: number, message: KafkaMessage) {
  try {
    if (!message.value) {
      return;
    }
    const logMessage = message.value.toString();
    if (!isRawMessageShouldBeProcessed(logMessage)) {
      return;
    }
    const logItem = await transformKafkaMessage(consumer, logMessage);
    await persistLogMessage(consumer, logItem);
  } catch (err) {
    consumer.logger().error("ProcessKafkaMessageFail", { err, topic, partition, message });
  }
}

async function persistLogMessage(consumer: Consumer, logItem: RawLogItem) {
  if (!logItem) return;
  try {
    // 使用新的事件处理器注册表
    const handler = globalEventRegistry.getHandler(logItem.eventName);
    if (handler) {
      await handler(logItem);
    } else {
      consumer.logger().debug("PersistLogMessageSkipCauseNotMatch", { logItem });
    }
  } catch (err) {
    consumer.logger().error("PersistLogMessageFail", { err, logItem });
  }
}

export async function transformKafkaMessage(consumer: Consumer, message: string) {
  const logItem = parseLogRawMessage(message);
  if (logItem && logItem.eventName && logItem.message) {
    consumer.logger().debug("TransformKafkaMessageOK", { logItem });
    return logItem;
  } else {
    consumer.logger().warn("TransformKafkaMessageFail", { logItem, message });
    return null;
  }
}
