import { error<PERSON><PERSON><PERSON>, checkParams } from "../helper";
import * as bluebird from "bluebird";
import { nosClient } from "../../common/nosSdkClient";
import { setRank } from "../components/activity/qmpk";
import { refreshByServerIdsAndTagIds } from "../../service/qnm/pyq/HotMomentsCache";
import { checkParamsByAjv } from "../common/ajvCheck";
import { ActionScoreMap } from "../types/momentLotteryType";
import { MomentModel, MomentRecord } from "../models";
import { BussError, errorCodes } from "../errorCodes";
import { handlePlayerAttendMomentLottery } from "../services/momentLotteryAttendService";
import { momentLotteryCfg } from "../common/config";
import { gmsDrawMomentLotteryService } from "../services/gms_services/drawMomentLotteryService";
import * as momentLotteryService from "../services/momentLotteryService";
import { csvStrToArray, formatDate } from "../../common/util";
import { InformService } from "../services/inform";
import { clazzLogger } from "../logger";
const logger = clazzLogger("controllers/gmCmd");

export const quanMinPKSetRank = setRank;

export async function uploadJobAvatar(req, res, next) {
  try {
    const schema = {
      gender: { type: String, values: ["male", "female"] },
      clazz: { type: Number },
    };
    await checkParams(req.params, schema);
    const params = req.params;
    logger.debug({ files: req.files }, "uploadJobAvatar");
    const avatarPath = req.files?.avatar?.path || "";
    if (!avatarPath) {
      await bluebird.reject({ message: "图片未上传" });
    }

    const key = `avatars/job_${params.clazz}_${params.gender}.jpg`;
    const bucketName = "hi-163-qnm";

    await nosClient.put_file_async({
      bucket: bucketName,
      key: key,
      filepath: avatarPath,
    });
    const url = `https://${bucketName}.nosdn.127.net/` + key + "?imageView";
    res.send({ code: 0, data: { url } });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function updateHotMomentsCache(req, res, next) {
  try {
    const reqBodySchema = {
      properties: {
        serverIds: { type: "array", items: { type: "string" } },
        tagIds: { type: "array", items: { type: "integer" } },
      },
      required: ["serverIds", "tagIds"],
    };
    await checkParamsByAjv(req.body, reqBodySchema);
    const ret = await refreshByServerIdsAndTagIds(req.body.serverIds, req.body.tagIds);
    return res.send({ code: 0, data: ret });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function attendMomentLottery(req, res, next) {
  try {
    const reqBodySchema = {
      type: "object",
      properties: {
        momentId: { type: "integer" },
        roleid: { type: "integer" },
        action: { type: "string", enum: ["like", "comment", "forward", "follow"] },
      },
      required: ["momentId", "roleid", "action"],
    };
    const params = req.params as { momentId: number; roleid: number; action: string };
    await checkParamsByAjv(req.params, reqBodySchema);
    const moment = await MomentModel.findOne({ ID: params.momentId }, ["ID", "RoleId"]);
    if (!moment) {
      await BussError(errorCodes.MomentNotFound);
    }

    const notes = `gm_fake_${params.action}`;
    const actionScore = ActionScoreMap.get(params.action);

    const data = await handlePlayerAttendMomentLottery(
      params.roleid,
      params.momentId,
      moment.RoleId,
      actionScore,
      notes,
      new Date()
    );

    return res.send({ code: 0, data });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function drawMomentLottery(req, res, next) {
  try {
    const reqBodySchema = {
      type: "object",
      properties: {
        momentId: { type: "integer" },
      },
      required: ["momentId"],
    };
    const params = req.params as { momentId: number };
    await checkParamsByAjv(req.params, reqBodySchema);
    const data = await momentLotteryService.drawMomentLottery(params.momentId);
    res.send({ code: 0, data });
  } catch (err) {
    logger.warn({ err, req }, "DrawMomentLotteryApiFail");
    res.send({ status: "Fail", message: err.message });
  }
}

export async function postReceiveLimit(req, res, next) {
  try {
    const cmdRes = await gmsDrawMomentLotteryService.drawMomentLotteryRaw(
      momentLotteryCfg.gmsPostReceiveLimitApiUrl,
      0,
      req.body
    );
    res.send(cmdRes);
  } catch (err) {
    if (err instanceof Error) {
      logger.error({ err, req }, "RequestPostReceiveLimitApiFail");
    } else {
      logger.warn({ err, req }, "RequestPostReceiveLimitApiBizErr");
    }
    res.send({ status: "Fail", message: err.message });
  }
}

export async function momentSearchHandler(req, res, next) {
  const params = req.params as { roleId: string; kw: string; size: number };
  try {
    const schemas = {
      type: "object",
      properties: {
        roleId: { type: "number" },
        kw: { type: "string" },
        size: { type: "number", minimum: 1, maximum: 100, default: 10 },
      },
      required: ["roleId"],
    };
    checkParamsByAjv(params, schemas);
    let query = MomentModel.scope().where({ RoleId: params.roleId }).orderBy("createTime", "desc").limit(params.size);
    if (params.kw) {
      query = query.where("Text", "like", `%${params.kw}%`);
    }
    const moments = (await MomentModel.executeByQuery(query)) as MomentRecord[];
    const list = moments.map((m) => ({
      id: m.ID,
      text: m.Text,
      imgList: csvStrToArray(m.ImgList),
      videoList: csvStrToArray(m.VideoList),
      createTime: formatDate(m.CreateTime),
    }));
    res.send({ code: 0, data: { list } });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}


export async function markAllInformUnread(req, res, next) {
  try {
    const params = req.params as { roleid: number };
    const reqBodySchema = {
      type: "object",
      properties: {
        roleid: { type: "number" },
      },
      required: ["roleid"],
    };
    await checkParamsByAjv(req.params, reqBodySchema);
    const ret = await InformService.markAllInformUnread(params.roleid);
    logger.info({ roleId: params.roleid, markedCount: ret.affectedRows }, "MarkAllInformUnreadOK");
    const data = { markedCount: ret.affectedRows };
    res.send({ code: 0, data });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}
