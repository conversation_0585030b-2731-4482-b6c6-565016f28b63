import errorHandler = require("../errorHandler");
import { PyqScenePhoto } from "../../models/PyqScenePhoto";
import { checkParams } from "../helper";
import * as _ from 'lodash'

export async function add(req, res, next) {
    try {
        let schema = {
            index: { type: Number },
            url: { type: String }
        }
        await checkParams(req.params, schema)
        let params = req.params as { roleid: number, index: number, url: string }
        let id = await PyqScenePhoto.add(params.roleid, params.index, params.url)
        res.send({ code: 0, data: { id: id } })
    }
    catch (err) {
        errorHandler(err, req, res, next)
    }
}

export async function list(req, res, next) {
    try {
        let params = req.params as { roleid: number }
        let list = await PyqScenePhoto.listAllByRoleId(params.roleid, ['ID', 'Index', 'Url', 'CreateTime'])
        res.send({ code: 0, data: { list: list } })
    } catch (err) {
        errorHandler(err, req, res, next)
    }
}