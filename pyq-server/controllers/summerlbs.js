"use strict";
/**
 * Created by zhenhua on 2017/5/12.
 */
Object.defineProperty(exports, "__esModule", { value: true });
const BaseController = require('./base');
const SummerLbsController = new BaseController();
const errorHandler = require('../errorHandler');
const _ = require('lodash');
const util = require('../../common/util');
const SummerLbsService = require('../services/summerlbs');
const PyqSummerLbsMsgBoard = require('../../models/PyqSummerLbsMsgBoard');
const PyqSummerLbsGeo = require('../../models/PyqSummerLbsGeo');
const PyqProfile = require('../../models/PyqProfile');
const Promise = require('bluebird');
const config = require('../../common/config');
const summerLbsGuildRank = require('../services/summerlbsGuildRank');
const QnmRoleAble = require('../../models/mixins/QnmRoleAble');
const QnmRoleInfo = require('../../models/QNMRoleInfos');
const cacheService = require('../../common/cacheService');
/**
 * @api {POST} /qnm/summerlbs/mark_food_area 标记食物区域
 * @apiGroup  SummerLbsForServer
 * @apiParam {String} roleid 角色id
 * @apiParam {Number} longitude 经度 range [-180，180]
 * @apiParam {Number} latitude 纬度 range [-90，90]
 * @apiParam {Number} foodid 食物id
 * @apiParam {Number} guildid 帮会id
 * @apiParam {String="common","special"} foodtype 食物类型
 * @apiParam {String="discover","place"} actiontype="discover" 标记动作类型, 分为探索发现与放置两种
 * @apiParam {Number} time 时间戳， 单位秒
 * @apiParam {String} auth_token 授权口令
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *        code: 0,
 *        data: {
 *          id: 2,    梦岛这个标记的主键id
 *        }
 *     }
 */
SummerLbsController.markFoodArea = function (req, res, next) {
    return req.paramsValidator
        .param('roleid')
        .param('longitude')
        .param('latitude')
        .param('foodid')
        .param('guildid')
        .param('foodtype', { required: true, values: ['common', 'special'] })
        .param('actiontype', { required: false, default: "discover", values: ['discover', 'place'] })
        .param('time', { type: Number })
        .validate()
        .then(() => {
        if (req.params.actiontype === 'discover') {
            return SummerLbsService.markFoodArea(req.params);
        }
        else {
            return SummerLbsService.placeFoodMark(req.params);
        }
    })
        .then(id => {
        res.send({ code: 0, data: { id: id } });
    })
        .catch(err => {
        errorHandler(err, req, res, next);
    });
};
/**
 * @api {POST} /qnm/summerlbs/get_nearby_food 获取附近的食物
 * @apiGroup  SummerLbsForServer
 * @apiParam {String} roleid 角色id
 * @apiParam {Number} longitude 经度
 * @apiParam {Number} latitude 纬度
 * @apiParam {String} auth_token 授权口令
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       code: 0,
 *       data: {
 *         id: 1,
 *         foodid: 12,
 *         foodtype: "common"
*          location: {
            continent: "北美洲",
            country: "United States",
            province: "Colorado"
          }
 *       }
 *     }
 *
 */
SummerLbsController.getNearbyFood = function (req, res, next) {
    let params;
    return req.paramsValidator
        .param('roleid')
        .param('longitude')
        .param('latitude')
        .validate()
        .then(() => {
        params = req.params;
        return Promise.all([
            SummerLbsService.getNearbyFood(params),
            SummerLbsService.getLocationInfo(params.roleid, params.longitude, params.latitude)
        ]);
    }).spread((food, location) => {
        let data = _.assign({}, food, { location: location });
        data = util.toLowerCaseKey(data, true);
        res.send({ code: 0, data: data });
    }).catch(err => {
        errorHandler(err, req, res, next);
    });
};
/**
 * @api {POST} /qnm/summerlbs/has_msg_on_scenery 是否在指定景区留过言
 * @apiGroup  SummerLbsForServer
 * @apiParam {String} roleid 角色id
 * @apiParam {String} scene_id 景区编号ID
 * @apiParam {String} auth_token 授权口令，TODO 校验是否是合法的服务器调用
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       code: 0,
 *       data: {
 *          has_msg: true
 *       }
 *     }
 *
 */
SummerLbsController.hasMsgOnScenery = function (req, res, next) {
    let params;
    return req.paramsValidator
        .param('roleid')
        .param('scene_id')
        .validate()
        .then(() => {
        params = req.params;
        return PyqSummerLbsMsgBoard.hasMsgOnScenery(params.roleid, params.scene_id);
    }).then(hasMsg => {
        SummerLbsController.succSend(res, { has_msg: hasMsg });
    }).catch(err => {
        errorHandler(err, req, res, next);
    });
};
/**
 * @api {POST} /qnm/summerlbs/get_guild_rank 获取服务器帮会积分相关排名信息
 * @apiGroup  SummerLbsForServer
 * @apiParam {Number} serverid 服务器id
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       code: 0,
 *       data: {
 *         cur_server_ranks: [600021, 610021, 740021]
 *         all_server_ranks: [800034]
 *       }
 *     }
 *
 */
SummerLbsController.getGuildRank = function (req, res, next) {
    const ServerList = require('../../service/qnm/server/list');
    const dateUtil = require('../../common/dateUtil');
    return req.paramsValidator
        .param('serverid')
        .validate()
        .then(() => {
        const serverId = req.params.serverid;
        return ServerList.getRootMergedServerId(serverId);
    }).then(serverId => {
        const isFinalRank = SummerLbsService.isActivityClosed();
        return Promise.props({
            cur_server_ranks: summerLbsGuildRank.getGuildRank(serverId, 3),
            all_server_ranks: summerLbsGuildRank.getGuildRankInAllServers(1),
            is_final_rank: isFinalRank
        });
    }).then(data => {
        SummerLbsController.succSend(res, data);
    }).catch(err => {
        errorHandler(err, req, res, next);
    });
};
/**
 * @api {POST} /qnm/summerlbs/get_location_info 获取GPS对应的地理位置信息
 * @apiGroup  SummerLbsForClient
 * @apiParam {String} roleid 角色id
 * @apiParam {Number} longitude 经度
 * @apiParam {Number} latitude 纬度
 * @apiSuccessExample {json} Success-Response:
 * 国内例子:
 * {
 *   "code": 0,
 *   "data": {
 *    "ischina": true,
 *     "continent": "亚洲",
 *     "country": "中国",
 *     "province": "浙江省"
 *   }
 * }
 *
 * //百度地图SDK限制， 国外输出的都是英文， 现在只有大洲翻译成中文
 * 国外例子:
 * {
 *  "code": 0,
 *  "data": {
 *    "ischina": false,
 *    "continent": "澳洲",
 *    "country": "Australia",
 *    "province": "Western Australia"
 *    }
 *  }
 */
SummerLbsController.getLocationInfo = function (req, res, next) {
    return req.paramsValidator
        .param('roleid')
        .param('longitude')
        .param('latitude')
        .validate()
        .then(() => {
        let params = req.params;
        return SummerLbsService.getLocationInfo(params.roleid, params.longitude, params.latitude);
    }).then(location => {
        SummerLbsController.succSend(res, location);
    }).catch(err => {
        errorHandler(err, req, res, next);
    });
};
/** @api {POST} /qnm/summerlbs/get_nearby_marks 获取附近的标记物(包括特俗食物和景点)
 * @apiGroup  SummerLbsForClient
 * @apiParam {String} roleid 角色id
 * @apiParam {Number} longitude 经度
 * @apiParam {Number} latitude 纬度
 * @apiParam {Number} distance  扫描距离，单位米
 *
 * @apiSuccessExample {json} Success-Response:
 *  {
*    code: 0,
*    data: {
*      scenes: [
*        {
*          "id": 1,
*          "mark_id": 1, //由策划表定义
*          "mark_type": "scene",
*          "name": "坎昆",
*          "longitude": -86.51,
*          "latitude": 21.1
*        }
*      ],
*      foods: [
*        {
*            "id": 1,
*            "mark_id": 12, //由策划表定义
*            "mark_type": "food", //探索对应的食物标记
*            "longitude": 113.25,
*            "latitude": 23.116699
*        }
*        {
*            "id": 2,
*            "mark_id": 14, //由策划表定义
*            "mark_type": "food_placed", //放置对应的食物标记
*            "longitude": 113.25,
*            "latitude": 23.116699
*        }
*      ]
*    }
*  }
 */
SummerLbsController.getNearbyMarks = function (req, res, next) {
    return req.paramsValidator
        .param('roleid')
        .param('longitude')
        .param('latitude')
        .param('distance', { required: false, default: 5000000 }) // 单位米默认5km
        .validate()
        .then(() => {
        return SummerLbsService.getNearbyMarks(req.params);
    }).then(data => {
        SummerLbsController.succSend(res, data);
    }).catch(err => {
        errorHandler(err, req, res, next);
    });
};
const FeatureToggle = require('../../common/FeatureToggle');
SummerLbsController.getNearbyMc = function (req, res, next) {
    return req.paramsValidator
        .param('roleid')
        .param('longitude')
        .param('latitude')
        .validate()
        .then(() => {
        if (FeatureToggle.isActive('qnmSummerLbsMcdonaldActivity')) {
            return SummerLbsService.getNearbyMc(req.params);
        }
        else {
            return [];
        }
    }).then(data => {
        SummerLbsController.succSend(res, data);
    }).catch(err => {
        errorHandler(err, req, res, next);
    });
};
/**
 * @api {POST} /qnm/summerlbs/msg_board/add 发表留言到留言板
 * @apiGroup  SummerLbsForClient
 * @apiParam {String} roleid 角色id
 * @apiParam {String} board_type  留言版类型， ["food", "scene", "province", "continent"]
 * @apiParam {Number} board_id  留言版类型对于的id
 * @apiParam {String} text 发表内容
 *
 * @apiSuccessExample {json} Success-Response:
 * {
 *   code: 0,
 *   data: {
 *     id: 4 //梦岛留言表的主键id
 *   }
 * }
 */
SummerLbsController.postOnMsgBoard = function (req, res, next) {
    let params;
    return req.paramsValidator
        .param('roleid')
        .param('board_type', { values: ["food", "scene", "province", "continent"] })
        .param('board_id')
        .param('text')
        .validate()
        .then(() => {
        params = req.params;
        const name = PyqSummerLbsMsgBoard.getBoardName(params.board_type, params.board_id);
        return PyqSummerLbsMsgBoard.insert({
            RoleId: params.roleid,
            Name: name,
            Text: params.text,
            CreateTime: Date.now(),
        });
    }).then(insertInfo => {
        SummerLbsController.succSend(res, { id: insertInfo.insertId });
    }).catch(err => {
        errorHandler(err, req, res, next);
    });
};
/**
 * @api {POST} /qnm/summerlbs/msg_board/list 列出留言板的内容
 * @apiGroup  SummerLbsForClient
 * @apiParam {String} roleid 角色id
 * @apiParam {String} board_type  留言版类型， ["food", "scene", "province", "continent"]
 * @apiParam {Number} board_id  留言版类型对于的id
 *
 * @apiSuccessExample {json} Success-Response:
 * {
 *   "code": 0,
 *   "data": [
 *     {
 *       "id": 5,
 *       "roleid": 1002,
 *       "text": "上海真好",
 *       "createtime": 1495966805839,
 *       "roleinfo": {
 *         "playerid": 1002,
 *         "name": "rolename",
 *         "level": 109,
 *         "gender": 1,
 *         "clazz": 5
 *      }
 *    }
 *  ]
 * }
 *
 */
SummerLbsController.listMsgBoard = function (req, res, next) {
    let params;
    return req.paramsValidator
        .param('roleid')
        .param('board_type', { values: _.values(PyqSummerLbsMsgBoard.BoardTypes) })
        .param('board_id')
        .validate()
        .then(() => {
        params = req.params;
        const name = PyqSummerLbsMsgBoard.getBoardName(params.board_type, params.board_id);
        return PyqSummerLbsMsgBoard.listBoardByName(name);
    })
        .then(msgs => {
        const roleIds = _.map(msgs, 'RoleId');
        return Promise.all([
            msgs,
            QnmRoleInfo.find({ RoleId: roleIds }, { cols: ['RoleId as playerId', 'RoleName as name',
                    'level', 'gender', 'JobId as clazz'] })
        ]);
    })
        .spread((msgs, roleInfos) => {
        msgs = util.toLowerCaseKey(msgs, true);
        msgs = util.embeddedOn(msgs, roleInfos, 'roleid', 'playerId', 'roleinfo');
        res.send({ code: 0, data: msgs });
    }).catch(err => {
        errorHandler(err, req, res, next);
    });
};
SummerLbsController.checkGeoRange = function (req, res, next) {
    const params = req.params;
    const inRange = (number, start, end) => {
        return number >= start && number <= end;
    };
    if (!inRange(params.longitude, -180, 180)) {
        res.send({ code: -1, msg: "参数错误! 经度不在合法范围" });
    }
    else if (!inRange(params.latitude, -90, 90)) {
        res.send({ code: -1, msg: "参数错误! 纬度不在合法范围" });
    }
    else {
        next();
    }
};
SummerLbsController.checkAuthTokenForServer = function (req, res, next) {
    if (config.testCfg.skip_auth) {
        next();
        return;
    }
    const auth_token = req.params.auth_token;
    const roleId = req.params.roleid;
    const chatAuthSalt = ')xF3ta~?*.<;(pv5<GR8!a:B$BnYp@w';
    if (auth_token) {
        const parts = _.split(auth_token, ':');
        //授权code由两部分组成， 第一部分为明文的时间， 第二部分为生成的签名, 用:来分隔
        if (parts.length === 2) {
            const time = parts[0];
            const sign = parts[1];
            const genSign = util.hexMd5("" + time + roleId + chatAuthSalt);
            if (sign === genSign) {
                next();
            }
            else {
                res.send({ code: -1, msg: "口令错误!" });
            }
        }
        else {
            res.send({ code: -1, msg: "口令错误!" });
        }
    }
    else {
        res.send({ code: -1, msg: "参数错误!" });
    }
};
function getGuildNameById(guildId) {
    const guildCache = require('../services/guildCache');
    const ModelManager = require('../../models/ModelManager');
    const QnmRoleInfo = ModelManager.getModelByTableName('qnm_roleinfo', 'SLAVE');
    return guildCache.get(guildId).then(name => {
        if (name) {
            return name;
        }
        else {
            const query = QnmRoleInfo.scope()
                .where('GangId', guildId)
                .select(['Gang as GangName'])
                .orderBy('UpdateTime', 'desc')
                .limit(1);
            return QnmRoleInfo.executeByQuery(query)
                .then(record => {
                return _.get(record[0], 'GangName');
            }).then(name => {
                guildCache.put(guildId, name);
                return name;
            });
        }
    });
}
function getPlayerGuildInfo(roleId) {
    return QnmRoleInfo.findOne({ RoleId: roleId }, ['GangId as guild_id', 'Gang as name']);
}
/**
 * @api {GET} /qnm/summerlbs/web/get_guild_rank 提供给web的获取summeerlbs相关信息
 * @apiGroup  SummerLbsForWeb
 * @apiParam {Number} roleid roleid
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
*       code: 0,
*       data: {
*         self: [
*           {"rank": 1, "name": "帮会名字", "score": 83, "guild_id": 200001}
*         ]
*         all: [
*           {"rank": 1, "name": "帮会名字", "score": 1, "guild_id": 100001}
*         ]
*       }
*     }
 */
SummerLbsController.getGuildRankForWeb = function (req, res, next) {
    let params;
    const RANK_SIZE = 10;
    const formatRanks = ranks => {
        return Promise.map(ranks, (rank, index) => {
            return getGuildNameById(rank.guildId).then(name => {
                return { rank: index + 1, name: name, score: rank.score, guild_id: rank.guildId };
            });
        });
    };
    return req.paramsValidator
        .param('roleid')
        .validate()
        .then(() => {
        params = req.params;
        return QnmRoleAble.getServerIdByRoleId(params.roleid);
    })
        .then(serverId => {
        return Promise.all([
            require('../../service/qnm/server/list').getRootMergedServerId(serverId),
            getPlayerGuildInfo(params.roleid)
        ]);
    })
        .spread((serverId, guildInfo) => {
        return Promise.all([
            summerLbsGuildRank.getGuildRank(serverId, RANK_SIZE).then(formatRanks),
            summerLbsGuildRank.getGuildRankInAllServers(RANK_SIZE).then(formatRanks),
            guildInfo,
            summerLbsGuildRank.getGuildScore(guildInfo.guild_id)
        ]);
    })
        .spread((rankInSelfServer, rankInAllServer, guildInfo, score) => {
        guildInfo = _.defaults(guildInfo, { guild_id: null, name: '', score: score, selfRank: 0, allRank: 0 });
        guildInfo.name = guildInfo.name || '暂无帮会';
        guildInfo.guild_id = parseInt(guildInfo.guild_id);
        guildInfo.selfRank = _.findIndex(rankInSelfServer, { guild_id: guildInfo.guild_id }) + 1;
        guildInfo.allRank = _.findIndex(rankInAllServer, { guild_id: guildInfo.guild_id }) + 1;
        return {
            me: guildInfo,
            self: rankInSelfServer,
            all: rankInAllServer,
        };
    })
        .then(data => {
        SummerLbsController.succSend(res, data);
    }).catch(err => {
        errorHandler(err, req, res, next);
    });
};
function getGuildServerInfo(guildId) {
    return require('../../common/data').qnm
        .getServerHash()
        .then(serverHash => {
        const serverId = QnmRoleInfo.getServerIdByGuildId(guildId);
        return serverHash[serverId] || { name: '测试服', group: '测试组' };
    });
}
function getLbsPrivacyHash(roleIds) {
    return PyqProfile.find({ RoleId: roleIds }, ['RoleId', 'Privacy'])
        .then(rows => {
        const roleIdToHideLbs = {};
        _.forEach(rows, item => {
            const privacy = PyqProfile.Privacy.fromPrivacyStr(item.Privacy);
            roleIdToHideLbs[item.RoleId] = privacy.hide_lbs;
        });
        return roleIdToHideLbs;
    });
}
SummerLbsController.getGuildMapData = function (req, res, next) {
    // 一个帮会总共最多产生 帮会人员数 * 2 的标记数据， 上限是210 * 2 = 420个点
    const limitSize = 420;
    let guildId;
    return req.paramsValidator
        .param('guild_id')
        .validate()
        .then(() => {
        guildId = req.params.guild_id;
        const query = PyqSummerLbsGeo
            .discoverActionScope()
            .where('GuildId', guildId)
            .orderBy('ID', 'desc')
            .select(['RoleId', 'Longitude', 'Latitude', 'FoodType'])
            .limit(limitSize);
        return PyqSummerLbsGeo.executeByQuery(query);
    })
        .then(geos => {
        const roleIds = _.map(geos, 'RoleId');
        return Promise.all([
            geos,
            QnmRoleInfo.find({ RoleId: roleIds }, { cols: ['RoleId', 'RoleName', 'JobId', 'Gender'] }),
            getGuildServerInfo(guildId),
            getGuildNameById(guildId),
            getLbsPrivacyHash(roleIds)
        ]);
    })
        .spread((geos, roleInfos, guildServerInfo, guildName, lbsPrivacyHash) => {
        _.forEach(roleInfos, roleInfo => {
            roleInfo.avatar = util.toHttps(QnmRoleInfo.getJobAvatar(roleInfo));
            roleInfo.server = `${guildServerInfo.group}-${guildServerInfo.name}`;
            roleInfo.Job = QnmRoleInfo.getJobById(roleInfo.JobId);
            roleInfo.guildName = guildName;
        });
        let data = util.embeddedOn(geos, roleInfos, 'RoleId', 'RoleId', 'RoleInfo');
        _.forEach(data, (row, index) => {
            row.Order = index + 1;
            row.Special = row.FoodType === PyqSummerLbsGeo.FoodTypes.special;
            const roleInfo = row.RoleInfo;
            if (lbsPrivacyHash[roleInfo.RoleId]) {
                roleInfo.RoleName = '该玩家设置了LBS隐私';
                roleInfo.Server = '';
                roleInfo.RoleId = '';
            }
        });
        data = _.map(data, row => _.pick(row, ['Order', 'Longitude', 'Latitude', 'Special', 'RoleInfo']));
        SummerLbsController.succSend(res, data);
    })
        .catch(err => {
        errorHandler(err, req, res, next);
    });
};
const MAX_POINTS_IN_WEB_MAP = 2500;
const guildMapDataExpire = 10 * 60 * 1000;
SummerLbsController.getAllGuildMapData = function (req, res, next) {
    const _getData = () => {
        const query = PyqSummerLbsGeo
            .discoverActionScope()
            .select(['RoleId', 'Longitude', 'Latitude'])
            .orderBy('ID', 'desc')
            .limit(MAX_POINTS_IN_WEB_MAP);
        return PyqSummerLbsGeo.executeByQuery(query);
    };
    const getData = cacheService.cacheInRedis(_getData, {
        cacheKey: 'pyq:summerlbs:web:guilds_data:all_server',
        expire: guildMapDataExpire
    });
    return getData().then(data => {
        SummerLbsController.succSend(res, data);
    }).catch(err => {
        errorHandler(err, req, res, next);
    });
};
SummerLbsController.getServerGuildMapData = function (req, res, next) {
    const roleId = req.params.roleid;
    const _getData = function (serverId) {
        const query = PyqSummerLbsGeo
            .discoverActionScope()
            .select(['RoleId', 'Longitude', 'Latitude'])
            .orderBy('ID', 'desc')
            .limit(MAX_POINTS_IN_WEB_MAP);
        return QnmRoleInfo.queryByServerId(query, serverId)
            .then(queryObj => {
            return PyqSummerLbsGeo.executeByQuery(queryObj.query);
        });
    };
    const getData = cacheService.cacheInRedis(_getData, {
        cacheKey: function (serverId) {
            return `pyq:summerlbs:web:guilds_data:${serverId}_server`;
        },
        expire: guildMapDataExpire
    });
    return Promise.resolve(QnmRoleInfo.getServerIdByRoleId(roleId))
        .then(serverId => {
        return getData(serverId);
    })
        .then(data => {
        SummerLbsController.succSend(res, data);
    }).catch(err => {
        errorHandler(err, req, res, next);
    });
};
module.exports = SummerLbsController;
//# sourceMappingURL=summerlbs.js.map