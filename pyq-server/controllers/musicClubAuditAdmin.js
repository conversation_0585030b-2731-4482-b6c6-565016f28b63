"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MusicClubAuditAdminController = void 0;
const config_all_1 = require("../common/config.all");
const musicClubRecordingAuditTaskService_1 = require("../services/music-club/musicClubRecordingAuditTaskService");
const musicClubRecordingAuditTaskModel_1 = require("../models/musicClub/musicClubRecordingAuditTaskModel");
/**
 * 音乐社团审核管理后台控制器
 */
class MusicClubAuditAdminController {
    /**
     * 检查管理后台是否启用
     */
    static checkEnabled(req, res, next) {
        if (!config_all_1.musicClubAuditAdminCfg.enabled) {
            res.send(404, { error: "Admin panel is disabled" });
            return false;
        }
        return true;
    }
    /**
     * 获取审核任务列表
     */
    static getAuditList(req, res, next) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            if (!MusicClubAuditAdminController.checkEnabled(req, res, next))
                return;
            try {
                const { recordingId, userId, serverId, status, startTime, endTime, page = 1, pageSize = 20 } = req.query;
                const pageNum = parseInt(page) || 1;
                const pageSizeNum = Math.min(parseInt(pageSize) || 20, 100); // 限制最大页面大小
                const offset = (pageNum - 1) * pageSizeNum;
                const auditTaskModel = musicClubRecordingAuditTaskModel_1.MusicClubRecordingAuditTaskModel.getInstance();
                // 构建查询条件
                let countQuery = auditTaskModel.scope()
                    .leftJoin('qnm_music_club_recording as r', 't.RecordingId', 'r.ID')
                    .from('qnm_music_club_recording_audit_task as t');
                let dataQuery = auditTaskModel.scope()
                    .leftJoin('qnm_music_club_recording as r', 't.RecordingId', 'r.ID')
                    .from('qnm_music_club_recording_audit_task as t')
                    .select([
                    't.*',
                    'r.Name as recording_name',
                    'r.ServerId as recording_server_id',
                    'r.MusicClubId as recording_music_club_id',
                    'r.Duration as recording_duration',
                    'r.DataUrl as recording_data_url',
                    'r.AuditStatus as recording_audit_status'
                ]);
                // 应用筛选条件
                if (recordingId) {
                    countQuery = countQuery.where('t.RecordingId', recordingId);
                    dataQuery = dataQuery.where('t.RecordingId', recordingId);
                }
                if (userId) {
                    countQuery = countQuery.where('t.RoleId', userId);
                    dataQuery = dataQuery.where('t.RoleId', userId);
                }
                if (serverId) {
                    countQuery = countQuery.where('r.ServerId', serverId);
                    dataQuery = dataQuery.where('r.ServerId', serverId);
                }
                if (status !== undefined && status !== '') {
                    countQuery = countQuery.where('t.Status', parseInt(status));
                    dataQuery = dataQuery.where('t.Status', parseInt(status));
                }
                if (startTime) {
                    // 将datetime-local格式转换为时间戳（毫秒）
                    const startTimestamp = new Date(startTime).getTime();
                    countQuery = countQuery.where('t.CreateTime', '>=', startTimestamp);
                    dataQuery = dataQuery.where('t.CreateTime', '>=', startTimestamp);
                }
                if (endTime) {
                    // 将datetime-local格式转换为时间戳（毫秒）
                    const endTimestamp = new Date(endTime).getTime();
                    countQuery = countQuery.where('t.CreateTime', '<=', endTimestamp);
                    dataQuery = dataQuery.where('t.CreateTime', '<=', endTimestamp);
                }
                // 查询总数
                const countResult = yield auditTaskModel.executeByQuery(countQuery.count('* as total'));
                const total = ((_a = countResult[0]) === null || _a === void 0 ? void 0 : _a.total) || 0;
                const totalPages = Math.ceil(total / pageSizeNum);
                // 查询数据
                dataQuery = dataQuery.orderBy('t.CreateTime', 'desc').limit(pageSizeNum).offset(offset);
                const dataResult = yield auditTaskModel.executeByQuery(dataQuery);
                // 格式化数据
                const data = dataResult.map((row) => ({
                    ID: row.ID,
                    RecordingId: row.RecordingId,
                    VocalUrl: row.VocalUrl,
                    Status: row.Status,
                    RetryCount: row.RetryCount,
                    CcTaskId: row.CcTaskId,
                    RejectReason: row.RejectReason,
                    CreateTime: row.CreateTime,
                    UpdateTime: row.UpdateTime,
                    recording: {
                        Name: row.recording_name,
                        RoleId: row.recording_role_id,
                        ServerId: row.recording_server_id,
                        MusicClubId: row.recording_music_club_id,
                        Duration: row.recording_duration,
                        DataUrl: row.recording_data_url,
                        AuditStatus: row.recording_audit_status
                    }
                }));
                res.send({
                    success: true,
                    data,
                    total,
                    page: pageNum,
                    pageSize: pageSizeNum,
                    totalPages
                });
            }
            catch (error) {
                console.error("获取审核任务列表失败:", error);
                res.send(500, { success: false, message: "获取数据失败" });
            }
            return next();
        });
    }
    /**
     * 获取审核任务详情
     */
    static getAuditDetail(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!MusicClubAuditAdminController.checkEnabled(req, res, next))
                return;
            try {
                const taskId = parseInt(req.params.id);
                if (!taskId) {
                    res.send(400, { success: false, message: "无效的任务ID" });
                    return next();
                }
                const auditTaskModel = musicClubRecordingAuditTaskModel_1.MusicClubRecordingAuditTaskModel.getInstance();
                const query = auditTaskModel.scope()
                    .leftJoin('qnm_music_club_recording as r', 't.RecordingId', 'r.ID')
                    .from('qnm_music_club_recording_audit_task as t')
                    .where('t.ID', taskId)
                    .select([
                    't.*',
                    'r.Name as recording_name',
                    'r.ServerId as recording_server_id',
                    'r.MusicClubId as recording_music_club_id',
                    'r.Duration as recording_duration',
                    'r.DataUrl as recording_data_url',
                    'r.AuditStatus as recording_audit_status',
                    'r.TrackId as recording_track_id'
                ]);
                const result = yield auditTaskModel.executeByQuery(query);
                if (!result || result.length === 0) {
                    res.send(404, { success: false, message: "任务不存在" });
                    return next();
                }
                const row = result[0];
                const data = {
                    ID: row.ID,
                    RecordingId: row.RecordingId,
                    VocalUrl: row.VocalUrl,
                    Status: row.Status,
                    RetryCount: row.RetryCount,
                    CcTaskId: row.CcTaskId,
                    RejectReason: row.RejectReason,
                    CreateTime: row.CreateTime,
                    UpdateTime: row.UpdateTime,
                    recording: {
                        Name: row.recording_name,
                        ServerId: row.recording_server_id,
                        MusicClubId: row.recording_music_club_id,
                        Duration: row.recording_duration,
                        DataUrl: row.recording_data_url,
                        AuditStatus: row.recording_audit_status,
                        TrackId: row.recording_track_id
                    }
                };
                res.send({ success: true, data });
            }
            catch (error) {
                console.error("获取审核任务详情失败:", error);
                res.send(500, { success: false, message: "获取详情失败" });
            }
            return next();
        });
    }
    /**
     * 获取统计信息
     */
    static getStatistics(req, res, next) {
        var _a, _b, _c, _d, _e;
        return __awaiter(this, void 0, void 0, function* () {
            if (!MusicClubAuditAdminController.checkEnabled(req, res, next))
                return;
            try {
                const auditTaskModel = musicClubRecordingAuditTaskModel_1.MusicClubRecordingAuditTaskModel.getInstance();
                // 分别查询各种状态的数量
                const totalQuery = auditTaskModel.scope().count('* as count');
                const pendingQuery = auditTaskModel.scope().where('Status', 0).count('* as count');
                const submittedQuery = auditTaskModel.scope().where('Status', 1).count('* as count');
                const passedQuery = auditTaskModel.scope().where('Status', 2).count('* as count');
                const rejectedQuery = auditTaskModel.scope().where('Status', 3).count('* as count');
                const [totalResult, pendingResult, submittedResult, passedResult, rejectedResult] = yield Promise.all([
                    auditTaskModel.executeByQuery(totalQuery),
                    auditTaskModel.executeByQuery(pendingQuery),
                    auditTaskModel.executeByQuery(submittedQuery),
                    auditTaskModel.executeByQuery(passedQuery),
                    auditTaskModel.executeByQuery(rejectedQuery)
                ]);
                res.send({
                    success: true,
                    data: {
                        total: ((_a = totalResult[0]) === null || _a === void 0 ? void 0 : _a.count) || 0,
                        pending: ((_b = pendingResult[0]) === null || _b === void 0 ? void 0 : _b.count) || 0,
                        submitted: ((_c = submittedResult[0]) === null || _c === void 0 ? void 0 : _c.count) || 0,
                        passed: ((_d = passedResult[0]) === null || _d === void 0 ? void 0 : _d.count) || 0,
                        rejected: ((_e = rejectedResult[0]) === null || _e === void 0 ? void 0 : _e.count) || 0
                    }
                });
            }
            catch (error) {
                console.error("获取统计信息失败:", error);
                res.send(500, { success: false, message: "获取统计信息失败" });
            }
            return next();
        });
    }
    /**
     * 重新提交审核任务
     */
    static retryAuditTask(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!MusicClubAuditAdminController.checkEnabled(req, res, next))
                return;
            try {
                const taskId = parseInt(req.params.id);
                if (!taskId) {
                    res.send(400, { success: false, message: "无效的任务ID" });
                    return next();
                }
                const auditTaskService = new musicClubRecordingAuditTaskService_1.MusicClubRecordingAuditTaskService();
                const success = yield auditTaskService.retryTask(taskId);
                if (success) {
                    res.send({ success: true, message: "重新提交成功" });
                }
                else {
                    res.send(400, { success: false, message: "重新提交失败，请检查任务状态" });
                }
            }
            catch (error) {
                console.error("重新提交审核任务失败:", error);
                res.send(500, { success: false, message: "重新提交失败" });
            }
            return next();
        });
    }
}
exports.MusicClubAuditAdminController = MusicClubAuditAdminController;
//# sourceMappingURL=musicClubAuditAdmin.js.map