"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.syncBackgroundId = exports.setProfilePhoto = exports.getProfile = exports.setLianghao = exports.updateVipLevel = exports.checkTokenForUpdateVipLevel = void 0;
const ProfileService = require("../../service/qnm/pyq/profile");
const helper_1 = require("../helper");
const profile_1 = require("../../service/qnm/pyq/profile");
const util = require("../../common/util");
/**
 * Created by zhenhua on 2017/5/10.
 */
const BaseController = require('./base');
const ProfileController = new BaseController();
const PyqProfile = require('../../models/PyqProfile');
const errorHandler = require('../errorHandler');
const config = require('../../common/config');
let EnumVipLevels = {
    NO_VIP: 0,
    VIP: 1
};
const MAX_BACKGROUND_ID = 9;
function checkTokenForUpdateVipLevel(req, res, next) {
    let params = req.params;
    const calToken = util.hexMd5(params.roleid + params.viplevel + params.time + config.TOKEN_SALT);
    if (calToken === params.token) {
        next();
    }
    else {
        res.send({ code: -1, msg: 'Token Invalid!' });
    }
}
exports.checkTokenForUpdateVipLevel = checkTokenForUpdateVipLevel;
function updateVipLevel(req, res, next) {
    let roleId, vipLevel;
    return req.paramsValidator
        .param('roleid', { type: Number })
        .param('viplevel', { type: Number, values: [EnumVipLevels.NO_VIP, EnumVipLevels.VIP] })
        .validate()
        .then(() => {
        roleId = req.params.roleid;
        vipLevel = req.params.viplevel;
        return PyqProfile.findOne({ RoleId: roleId }, ['SpLevel']);
    }).then(profile => {
        if (!profile) {
            return Promise.reject({ msg: 'roleId not found' });
        }
        else {
            if (profile.SpLevel === vipLevel) {
                if (vipLevel === EnumVipLevels.NO_VIP) {
                    return Promise.reject({ msg: 'Already NoVip Status' });
                }
                else {
                    return Promise.reject({ msg: 'Already Vip Status' });
                }
            }
            else {
                return PyqProfile.updateByCondition({ RoleId: roleId }, { SpLevel: vipLevel });
            }
        }
    }).then(() => {
        ProfileController.succSend(res, { msg: 'Ok!' });
    }).catch(err => {
        errorHandler(err, req, res, next);
    });
}
exports.updateVipLevel = updateVipLevel;
function setLianghao(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            let schema = {
                roleid: { type: Number },
                lianghao: { type: String }
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            let params = req.params;
            let data = yield (0, profile_1.updateLianghao)(params.roleid, params.lianghao);
            res.send({ code: 0, data: data });
        }
        catch (err) {
            errorHandler(err, req, res, next);
        }
    });
}
exports.setLianghao = setLianghao;
function getProfile(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            let schema = {
                roleid: { type: Number },
                targetid: { type: Number, required: false }
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            let params = req.params;
            let queryRoleId = params.targetid || params.roleid;
            let data = yield (0, profile_1.getProfileRecord)(queryRoleId, params);
            res.send({ code: 0, data: data });
        }
        catch (err) {
            errorHandler(err, req, res, next);
        }
    });
}
exports.getProfile = getProfile;
function setProfilePhoto(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        let ip = util.getIp(req);
        req.params.ip = ip;
        ProfileService.set(req.params, function () {
            res.send(util.response.apply(null, arguments));
        });
    });
}
exports.setProfilePhoto = setProfilePhoto;
function syncBackgroundId(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            let schema = {
                roleid: { type: Number },
                id: { type: Number, max: MAX_BACKGROUND_ID },
                validity: { type: Number }
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            let params = req.params;
            let data = yield (0, profile_1.syncBackgroundRecord)(params);
            res.send({ code: 0, data });
        }
        catch (err) {
            errorHandler(err, req, res, next);
        }
    });
}
exports.syncBackgroundId = syncBackgroundId;
//# sourceMappingURL=profile.js.map