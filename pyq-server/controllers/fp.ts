import { errorHandler } from "../helper";
import * as FpService from "../services/fp";

export function getToken(req, res, next) {
  try {
    const roleId = req.params.roleid;
    const token = FpService.getToken({
      uid: roleId,
      review: FpService.EFpReviewStatus.PRPASSED,
      extraInfo: {},
    });
    res.send({ code: 0, data: token });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}
