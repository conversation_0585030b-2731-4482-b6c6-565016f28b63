"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkRedDot = void 0;
const errorHandler = require("../errorHandler.js");
const RedDotService = require("../services/redDot");
function checkRedDot(req, res, next) {
    const roleid = req.params.roleid;
    return RedDotService.check(roleid)
        .then((redDot) => {
        res.send({ code: 0, data: redDot });
        return next();
    })
        .catch((err) => {
        return errorHandler(err, req, res, next);
    });
}
exports.checkRedDot = checkRedDot;
//# sourceMappingURL=notify.js.map