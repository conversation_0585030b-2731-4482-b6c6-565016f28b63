"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.gdcQnmDoHandler = void 0;
const GdcService = require("../../service/qnm/client/gdc");
const helper_1 = require("../helper");
function gdcQnmDoHandler(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const data = yield GdcService.post(req);
            res.send({ code: 0, data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.gdcQnmDoHandler = gdcQnmDoHandler;
//# sourceMappingURL=gdc_controller.js.map