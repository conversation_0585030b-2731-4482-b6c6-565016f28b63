"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.notifyGameSurveyCompleted = exports.submit = exports.SubmitErrorCode = void 0;
const httpLib = require("../../common/request");
const Url = require("url");
const helper_1 = require("../helper");
const config_1 = require("../../common/config");
const logger_1 = require("../logger");
var SubmitErrorCode;
(function (SubmitErrorCode) {
    SubmitErrorCode[SubmitErrorCode["NOT_FOUND_ROLE_ID"] = 404] = "NOT_FOUND_ROLE_ID";
})(SubmitErrorCode = exports.SubmitErrorCode || (exports.SubmitErrorCode = {}));
function submit(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        logger_1.surveyLogger.info({ req: req, params: req.params }, 'surveySubmit');
        try {
            const schema = {
                question: { type: Number },
                url: { type: String },
                serverId: { type: Number, required: false }
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            const params = req.params;
            const roleId = getSubmitRoleId(req);
            if (roleId) {
                const rootServer = yield (0, helper_1.queryServerIdByRoleId)(roleId);
                const serverId = params.serverId || rootServer;
                const postData = { playerId: roleId, serverId: serverId, surveyId: params.question };
                const ret = yield notifyGameSurveyCompleted(postData);
                const retData = { resultCode: 200, resultDesc: "成功", apiRet: '' };
                if (config_1.testCfg.test_env) {
                    retData.apiRet = ret;
                }
                res.send(retData);
            }
            else {
                res.send({ resultCode: SubmitErrorCode.NOT_FOUND_ROLE_ID, resultDesc: 'playerId not found' });
            }
        }
        catch (err) {
            logger_1.surveyLogger.error(err);
            // 返回正常让问卷能正常收集
            const resultCode = err.retCode || -1;
            const resultDesc = err.message || '系统错误';
            res.send({ resultCode: resultCode, resultDesc: resultDesc });
        }
    });
}
exports.submit = submit;
function getSubmitRoleId(req) {
    const urlObj = Url.parse(req.params.url, true);
    const qs = urlObj.query;
    const mdInfo = qs.qnm_md_info;
    if (mdInfo) {
        const tokens = mdInfo.split('.');
        const roleIdStr = tokens[0];
        const roleId = parseInt(roleIdStr, 10);
        if (Number.isNaN(roleId)) {
            return null;
        }
        else {
            return roleId;
        }
    }
    else {
        return null;
    }
}
function getGameServiceApi() {
    return config_1.outApi.gms;
}
function notifyGameSurveyCompleted(option) {
    return __awaiter(this, void 0, void 0, function* () {
        const apiUrl = getGameServiceApi();
        const data = {
            cmd: 'playerfinishsurvey',
            operatorInfo: "l10_md_server",
            playerId: option.playerId,
            serverId: option.serverId,
            surveyId: option.surveyId
        };
        try {
            logger_1.surveyLogger.info('PrepareNotifyGame', { data: data });
            const ret = yield httpLib.request({
                method: 'POST',
                url: apiUrl,
                body: data
            });
            logger_1.surveyLogger.info('FinishNotifyGame', { data: data, ret: ret });
            return ret;
        }
        catch (err) {
            logger_1.surveyLogger.info('NotifyGameError', { data: data, err: err });
        }
    });
}
exports.notifyGameSurveyCompleted = notifyGameSurveyCompleted;
//# sourceMappingURL=survey.js.map