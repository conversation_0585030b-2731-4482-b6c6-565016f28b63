﻿/* eslint-disable @typescript-eslint/no-var-requires */
/**
 * Created by zhen<PERSON> on 16-11-3.
 */

import { QnmRoleInfoRecord } from "../../models/modelInterface";
import { FriendCircleMinLevel } from "../common/config";
import { EXianFanStatus } from "../constants";
import { checkParams } from "../helper";
import { getLangTrans } from "../i18n/i18n";
import { clazzLogger } from "../logger";
import { ProfileModel, RoleInfoModel } from "../models/modelProxy";
import { getLangFromReq } from "./base2";

const BaseController = require("./base");
const info = new BaseController();
const errorHandler = require("../errorHandler");
const QnmRoleInfos = require("../../models/QNMRoleInfos");
const PyqProfile = require("../../models/PyqProfile");
const _ = require("lodash");
const ParamsTypes = require("../../common/paramsValidator").PARAM_TYPES;
const Constants = require("../../common/data").Constants;
const logger = clazzLogger("controllers/info");

info.setFightingCapacity = function (req, res, next) {
  let roleId, fightCapacity;
  return req.paramsValidator
    .param("roleid")
    .param("value")
    .validate()
    .then(function () {
      roleId = req.params.roleid;
      fightCapacity = req.params.value;
      const query = QnmRoleInfos.scope().where("RoleId", roleId).update({ FightingCapacity: fightCapacity });
      return QnmRoleInfos.executeByQuery(query);
    })
    .then(function () {
      info.succSend(res, { msg: "OK" });
    })
    .catch(function (err) {
      errorHandler(err, req, res, next);
    });
};

info.getFightingCapacity = function (req, res, next) {
  let roleId;
  return req.paramsValidator
    .param("roleid")
    .param("targetid", { required: false })
    .validate()
    .then(function () {
      roleId = req.params.targetid || req.params.roleid;
      return QnmRoleInfos.findOne({ RoleId: roleId }, ["FightingCapacity"]);
    })
    .then(function (fc) {
      const fcField = _.get(fc, "FightingCapacity") || "";
      res.send({ code: 0, msg: fcField });
    })
    .catch(function (err) {
      errorHandler(err, req, res, next);
    });
};

info.getLocations = function (req, res, next) {
  return req.paramsValidator
    .param("targetids", { type: ParamsTypes.JSON_ARRAY })
    .validate()
    .then(function () {
      const roleIds = req.params.targetids;
      return PyqProfile.where({ RoleId: roleIds }, { cols: ["RoleId", "Location", "Privacy"] });
    })
    .then(function (rows) {
      const data = _.map(rows, (r) => {
        const p = PyqProfile.Privacy.fromPrivacyStr(r.Privacy);
        const hideLocation = p.location;
        if (hideLocation) {
          return { RoleId: r.RoleId, Location: "" };
        } else {
          return { RoleId: r.RoleId, Location: r.Location };
        }
      });
      info.succSend(res, data, { toLowerCase: false });
    })
    .catch(function (err) {
      errorHandler(err, req, res, next);
    });
};

info.getAvatar = function (req, res, next) {
  let roleId;
  return req.paramsValidator
    .param("roleid")
    .validate()
    .then(function () {
      roleId = req.params.roleid;
      return PyqProfile.findOne({ RoleId: roleId }, ["RoleId", "Photo", "ShowPhoto", "PhotoAudit"]);
    })
    .then(function (row) {
      if (row) {
        require("../../common/data").pyq.setPhotoView(row, roleId);
        const photo = _.get(row, "Photo") || "";
        info.succSend(res, { msg: photo });
      } else {
        info.succSend(res, { msg: "" });
      }
    })
    .catch(function (err) {
      errorHandler(err, req, res, next);
    });
};

function isRoleLevelMatch(roleInfo: Pick<QnmRoleInfoRecord, "Level" | "XianFanStatus">): boolean {
  if (roleInfo.XianFanStatus === EXianFanStatus.XianFan) {
    return true;
  } else {
    return roleInfo.Level >= FriendCircleMinLevel;
  }
}

async function isEnableIslandHandle(req, res, next) {
  try {
    const schema = {
      targetid: { type: Number, required: false },
    };
    await checkParams(req.params, schema);
    const roleId: number = req.params.targetid || req.params.roleid;
    const [profile, roleInfo] = await Promise.all([
      ProfileModel.findOne({ RoleId: roleId }, ["RoleId"]),
      RoleInfoModel.findOne({ RoleId: roleId }, ["RoleId", "RoleName", "Level", "XianFanStatus"]),
    ]);
    if (profile && roleInfo && roleInfo.RoleId && isRoleLevelMatch(roleInfo)) {
      res.send({ code: 0, data: { msg: true } });
    } else {
      logger.warn({ roleId, profile, roleInfo }, "PlayerNotEnableIsland");
      res.send({ code: -1, data: { msg: false } });
    }
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

info.isEnableIsland = isEnableIslandHandle;

info.setRoleName = function (req, res, next) {
  return req.paramsValidator
    .param("roleid", { type: Number, values: [Constants.GM_ROLE_ID, Constants.JL_ROLE_ID] }) // 现在只允许GM, 和金陵情报官
    .param("rolename", { type: String })
    .validate()
    .then(() => {
      const roleId = req.params.roleid;
      const roleName = req.params.rolename;
      return QnmRoleInfos.updateByCondition({ RoleId: roleId }, { RoleName: roleName });
    })
    .then(() => {
      res.send({ code: 0, msg: "ok" });
    })
    .catch((err) => {
      errorHandler(err, req, res, next);
    });
};

/**
 * @api {POST} /qnm/hideusedname 设置是否隐藏曾用名
 * @apiGroup Users
 * @apiUse CommonParamFields
 * @apiParam {String="true","false"} hide 是否隐藏
 *
 */
info.setHideUsedName = function (req, res, next) {
  const UsedNameStatuses = QnmRoleInfos.UsedNameStatuses;
  const lang = getLangFromReq(req);
  const t = getLangTrans(lang);
  let roleId;
  return req.paramsValidator
    .param("hide", { values: ["true", "false"] })
    .param("roleid")
    .validate()
    .then(() => {
      roleId = req.params.roleid;
      const isHide = JSON.parse(req.params.hide);
      const newUsedNameStatus = isHide ? UsedNameStatuses.HIDE : UsedNameStatuses.PUBLIC;
      return QnmRoleInfos.findOne({ RoleId: roleId }, ["UsedName", "UsedNameStatus"]).then((roleInfo) => {
        if (roleInfo) {
          if (_.isEmpty(roleInfo.UsedName)) {
            return Promise.reject({ msg: t("youHaveNotSetPreviousName") });
          } else {
            return [roleInfo.UsedNameStatus, newUsedNameStatus];
          }
        } else {
          return Promise.reject({ msg: t("roleInfoNotFound") });
        }
      });
    })
    .spread((curUsedNameStatus, newUsedNameStatus) => {
      if (curUsedNameStatus === newUsedNameStatus) {
        const msg =
          curUsedNameStatus === UsedNameStatuses.HIDE
            ? t("yourPreviousNameIsAlreadyHidden")
            : t("yourPreviousNameIsAlreadyPublic");
        return Promise.reject({ msg: msg });
      } else {
        return QnmRoleInfos.updateByCondition({ RoleId: roleId }, { UsedNameStatus: newUsedNameStatus });
      }
    })
    .then(() => {
      info.succSend(res, { msg: t("modifySuccess") });
    })
    .catch((err) => {
      errorHandler(err, req, res, next);
    });
};

module.exports = info;
export {};
