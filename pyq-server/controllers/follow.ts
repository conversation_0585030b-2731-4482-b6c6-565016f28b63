import * as PyqFollow from "../../models/PyqFollow";
import * as ProfileService from "../../service/qnm/pyq/profile";
import * as _ from "lodash";
import { EventTypes } from "../../service/qnm/pyq/events/common/base";
import { checkParams, errorHandler, formatResStyle } from "../helper";
import { getFollowIds } from "../../service/qnm/pyq/follow";
import EventBus = require("../eventBus");
import { FOLLOW_PAYLOAD, FOLLOW_BATCH_PAYLOAD } from "../types";
import { getLoginSession } from "./auth";
import * as FollowService from "../services/follow";
import { FollowReq } from "../type/req";
import { PARAM_TYPES, ParamsValidator } from "../common/paramsValidator";
import { handlePlayerAttendMomentLotteryForFollow } from "../services/momentLotteryAttendService";
import { AttendMomentLotteryAction } from "../types/momentLotteryType";

export async function follow(req, res, next) {
  try {
    const schema = {
      roleid: { type: Number },
      targetid: { type: Number },
    };

    await checkParams(req.params, schema);
    const params: FollowReq.Add = req.params;
    const data = await FollowService.addFollow(params);
    res.send({ code: 0, data: data });

    const session = getLoginSession(req);
    const payload: FOLLOW_PAYLOAD = { roleId: params.roleid, targetId: params.targetid, session: session };
    EventBus.emit(EventBus.Events.ADD_FOLLOW, payload);
    handlePlayerAttendMomentLotteryForFollow(
      params.roleid,
      params.targetid,
      AttendMomentLotteryAction.Follow,
      new Date()
    );
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function cancelFollow(req, res, next) {
  try {
    const schema = {
      roleid: { type: Number },
      targetid: { type: Number },
    };
    await checkParams(req.params, schema);
    const params: FollowReq.Cancel = req.params;
    const data = await FollowService.cancelFollow(params);
    res.send({ code: 0, data: data });

    const session = getLoginSession(req);
    const payload: FOLLOW_PAYLOAD = { roleId: params.roleid, targetId: params.targetid, session: session };
    EventBus.emit(EventBus.Events.CANCEL_FOLLOW, payload);
    handlePlayerAttendMomentLotteryForFollow(
      params.roleid,
      params.targetid,
      AttendMomentLotteryAction.FollowCancel,
      new Date()
    );
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function cancelFollowBatch(req, res, next) {
  try {
    const schema = {
      roleid: { type: Number },
      targetids: { type: PARAM_TYPES.CSV_ARRAY, maxSize: 20, each: { type: Number } },
    };
    await checkParams(req.params, schema);
    const params: FollowReq.CancelBatch = req.params;
    const data = await FollowService.cancelFollowBatch(params);
    res.send({ code: 0, data: data });

    const session = getLoginSession(req);
    const payload: FOLLOW_BATCH_PAYLOAD = { roleId: params.roleid, targetIds: params.targetids, session: session };
    EventBus.emit(EventBus.Events.CANCEL_FOLLOW_BATCH, payload);
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export function getFollowing(req, res, next) {
  let curRoleId;
  return ParamsValidator.from(req.params)
    .param("roleid")
    .validate()
    .then(function () {
      curRoleId = req.params.roleid;
      return getFollowIds(curRoleId);
    })
    .then(function (followingIds) {
      return ProfileService.getRoleInfo(followingIds, curRoleId);
    })
    .then(function (following) {
      const followingIds = _.map(following, "RoleId");
      return [following, PyqFollow.filterFollowingMe(followingIds, curRoleId)];
    })
    .then(function (info: any[]) {
      const followingMeIds = info[0];
      const following = info[1];
      return _.map(following, (r) => {
        const followEach = _.includes(followingMeIds, r.RoleId);
        //和 "/qnm/get_events" 接口输出保持兼容
        return {
          RoleId: r.RoleId,
          RoleName: r.RoleName,
          FollowingEach: followEach,
          type: EventTypes.Following,
          roleInfo: r,
        };
      });
    })
    .then(function (players) {
      players = formatResStyle(players);
      res.send({ code: 0, data: players });
    })
    .catch(function (err) {
      errorHandler(err, req, res, next);
    });
}

export async function getFollowers(req, res, next) {
  try {
    const curRoleId = req.params.roleid;
    const followingIds = await PyqFollow.getFollowerIds(curRoleId);
    const following = await ProfileService.getRoleInfo(followingIds, curRoleId);

    const meFollowIds = await PyqFollow.filterMeFollow(followingIds, curRoleId);
    let players = _.map(following, (r) => {
      const followEach = _.includes(meFollowIds, r.RoleId);
      //和 "/qnm/get_events" 接口输出保持兼容
      return {
        RoleId: r.RoleId,
        RoleName: r.RoleName,
        FollowingEach: followEach,
        type: EventTypes.Following,
        roleInfo: r,
      };
    });
    players = formatResStyle(players);
    res.send({ code: 0, data: players });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function getFollowList(req, res, next) {
  const schema = {
    roleid: { type: Number },
    page: { type: Number, default: 1, min: 1 },
    pageSize: { type: Number, default: 10, min: 1, max: 20 },
  };
  try {
    await checkParams(req.params, schema);
    const params: FollowReq.FollowList = req.params;
    const data = await FollowService.getFollowListPage(params);
    res.send({ code: 0, data: formatResStyle(data) });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}
