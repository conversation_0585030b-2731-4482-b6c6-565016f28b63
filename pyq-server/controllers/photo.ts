﻿/* eslint-disable @typescript-eslint/no-var-requires */
/**
 * Created by zhen<PERSON> on 16-11-28.
 */

import { getLangFromRequest } from "../../common/util2";
import { getLangTrans } from "../i18n/i18n";

const BaseController = require("./base");
const PhotoController = new BaseController();
const errorHandler = require("../errorHandler");
const _ = require("lodash");
const Photo = require("../models/photo");

PhotoController.create = function (req, res, next) {
  let roleId, url, type;
  return req.paramsValidator
    .param("roleid")
    .param("pictype", { values: ["cjbpic", "cjbsharepic"] })
    .param("url")
    .validate()
    .then(function () {
      roleId = req.params.roleid;
      url = req.params.url;
      type = req.params.pictype;
      return Photo.create({ RoleId: roleId, Url: url, Type: Photo.Types[type] });
    })
    .then(function (photo) {
      Photo.sendToAudit(photo);
      return PhotoController.succSend(res, photo.ID);
    })
    .catch(function (err) {
      errorHandler(err, req, res, next);
    });
};

PhotoController.get = function (req, res, next) {
  let roleId, photoId;
  const lang = getLangFromRequest(req);
  const t = getLangTrans(lang);
  return req.paramsValidator
    .param("roleid")
    .param("photo_id")
    .validate()
    .then(function () {
      roleId = req.params.roleid;
      photoId = req.params.photo_id;
      return Photo.checkRecordById(photoId, t("imageNotExist"));
    })
    .then(function (photo) {
      photo = Photo.setUrlByAudit(photo);
      return PhotoController.succSend(res, {
        ID: photo.ID,
        Url: photo.Url,
        PicType: Photo.picTypeToStr(photo.Type),
        CreateTime: photo.CreateTime,
      });
    })
    .catch(function (err) {
      errorHandler(err, req, res, next);
    });
};

module.exports = PhotoController;
export {};
