"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.answerMessage = exports.delMessage = exports.getMessages = exports.addMessage = void 0;
/* eslint-disable prefer-spread */
/* eslint-disable prefer-rest-params */
const util = require("../../common/util");
const MessageService = require("../../service/qnm/pyq/message");
const ajvCheck_1 = require("../common/ajvCheck");
const helper_1 = require("../helper");
const profileBanStateService_1 = require("../services/profileBanStateService");
const messageTypes_1 = require("../types/messageTypes");
function addMessage(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            (0, ajvCheck_1.checkParamsByAjv)(req.params, messageTypes_1.IAddMessageSchema);
            yield (0, profileBanStateService_1.checkStateBannedForAction)(req, req.params.roleid, "Message");
            const info = yield MessageService.addMsg(req);
            res.send({ code: 0, data: info });
        }
        catch (error) {
            (0, helper_1.errorHandler)(error, req, res, next);
        }
    });
}
exports.addMessage = addMessage;
function getMessages(req, res, next) {
    MessageService.get(req.params, function () {
        res.send(util.response.apply(null, arguments));
    });
}
exports.getMessages = getMessages;
function delMessage(req, res, next) {
    MessageService.del(req.params, function () {
        res.send(util.response.apply(null, arguments));
    });
}
exports.delMessage = delMessage;
function answerMessage(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            (0, ajvCheck_1.checkParamsByAjv)(req.params, messageTypes_1.IAnswerMessageSchema);
            yield (0, profileBanStateService_1.checkStateBannedForAction)(req, req.params.roleid, "Message");
            const info = yield MessageService.addMsg(req);
            res.send({ code: 0, data: info });
        }
        catch (error) {
            (0, helper_1.errorHandler)(error, req, res, next);
        }
    });
}
exports.answerMessage = answerMessage;
//# sourceMappingURL=message.js.map