"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.momentLotteryWinnersHandler = exports.momentLotteryShowHandler = void 0;
const ajvCheck_1 = require("../common/ajvCheck");
const errorCodes_1 = require("../errorCodes");
const helper_1 = require("../helper");
const bluebird = require("bluebird");
const momentLotteryAttendService_1 = require("../services/momentLotteryAttendService");
const MomentLotteryAttendService = require("../services/momentLotteryAttendService");
const momentLotteryService_1 = require("../services/momentLotteryService");
const momentLotteryType_1 = require("../types/momentLotteryType");
const RoleInfoServiceV2 = require("../services/roleInfoServiceV2");
const serverList_1 = require("../../service/qnm/server/serverList");
const momentLotteryAttendModel_1 = require("../models/momentLotteryAttendModel");
const logger_1 = require("../logger");
const list_1 = require("../../service/qnm/server/list");
const util2_1 = require("../../common/util2");
function momentLotteryShowHandler(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const params = req.params;
            const schema = {
                type: "object",
                required: ["roleid", "momentId"],
                properties: {
                    roleid: { type: "integer", minimum: 1 },
                    momentId: { type: "integer", minimum: 1 },
                },
            };
            yield (0, ajvCheck_1.checkParamsByAjv)(req.params, schema);
            const lottery = yield (0, momentLotteryService_1.getShowLotteryByMomentId)(params.momentId);
            if (!lottery) {
                yield (0, errorCodes_1.BussError)(errorCodes_1.errorCodes.DataNotFound);
            }
            const prizeList = (0, momentLotteryService_1.lotteryPrizeFromString)(lottery.Prizes);
            const winPrizes = prizeList.map((p) => {
                return {
                    id: p.itemId,
                    num: p.num,
                };
            });
            const hostPlayerRoleId = lottery.RoleId;
            const hostPlayerServerId = yield (0, list_1.getRootMergedServerId)((0, util2_1.getServerIdFromRoleId)(hostPlayerRoleId));
            const isMyLottery = lottery.RoleId == params.roleid;
            const data = {
                isMyLottery,
                isParticipate: false,
                type: lottery.Type,
                momentId: lottery.MomentId,
                winnerNum: lottery.WinnerNum,
                minLevel: lottery.MinLevel,
                participateNum: 0,
                winPrizes,
                drawTime: lottery.DrawTime,
                winnerRoleName: "",
                requirements: {
                    like: !!lottery.RequireLike,
                    comment: !!lottery.RequireComment,
                    forward: !!lottery.RequireForward,
                    follow: !!lottery.RequireFollow,
                },
                serverScope: (0, momentLotteryType_1.getLotteryServerScope)(lottery.ServerScope),
                hostPlayer: {
                    roleId: hostPlayerRoleId,
                    serverId: hostPlayerServerId,
                },
                myAction: {
                    isLike: false,
                    isComment: false,
                    isForward: false,
                    isFollow: false,
                },
                drawStatus: lottery.DrawStatus,
                isWin: false,
            };
            if (!isMyLottery) {
                const myActionIsLike = yield MomentLotteryAttendService.isPlayerLikeMoment(params.roleid, lottery.MomentId);
                const myActionIsComment = yield MomentLotteryAttendService.isPlayerCommentMoment(params.roleid, lottery.MomentId);
                const myActionIsFollow = yield MomentLotteryAttendService.isPlayerFollowMomentRoleId(params.roleid, lottery.RoleId);
                const myActionIsForward = yield MomentLotteryAttendService.isPlayerForwardMoment(params.roleid, lottery.MomentId);
                data.myAction = {
                    isLike: myActionIsLike,
                    isComment: myActionIsComment,
                    isForward: myActionIsForward,
                    isFollow: myActionIsFollow,
                };
            }
            // 获取动态抽奖的参与人数
            const participateNum = yield (0, momentLotteryAttendService_1.getMomentLotteryAttendCount)(lottery.MomentId);
            data.participateNum = participateNum;
            if (!data.isMyLottery) {
                // 只有不是我的奖励，才需要是否参与了抽奖
                const isParticipate = yield (0, momentLotteryAttendService_1.isPlayerParticipateLottery)(params.roleid, lottery.MomentId);
                data.isParticipate = isParticipate;
            }
            // 如果动态已经开奖，计算winnerRoleName字段
            if (lottery.DrawStatus === 1 /* Drawn */) {
                const firstAttendWinner = yield momentLotteryAttendModel_1.MomentLotteryAttendModel.getFirstAttendWinner(lottery.MomentId);
                if (firstAttendWinner) {
                    const winnerRoleId = firstAttendWinner.RoleId;
                    const winnerRoleInfo = yield RoleInfoServiceV2.getRoleInfo(winnerRoleId);
                    if (winnerRoleInfo) {
                        data.winnerRoleName = winnerRoleInfo.RoleName;
                    }
                    else {
                        logger_1.logger.warn({ winnerRoleId }, "WinnerRoleInfoNotExist");
                    }
                }
                else {
                    logger_1.logger.warn({ momentId: lottery.MomentId }, "FirstAttendWinnerNotExist");
                }
            }
            res.send({ code: 0, data: data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.momentLotteryShowHandler = momentLotteryShowHandler;
function momentLotteryWinnersHandler(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const params = req.params;
            const schema = {
                type: "object",
                required: ["roleid", "momentId", "page", "pageSize"],
                properties: {
                    roleid: { type: "number" },
                    momentId: { type: "number" },
                    page: { type: "number", default: 1, minimum: 1 },
                    pageSize: { type: "number", default: 10, minimum: 1, maximum: 20 },
                },
            };
            yield (0, ajvCheck_1.checkParamsByAjv)(params, schema);
            const curRoleId = params.roleid;
            const lottery = yield (0, momentLotteryService_1.getShowLotteryByMomentId)(params.momentId);
            if (!lottery) {
                yield (0, errorCodes_1.BussError)(errorCodes_1.errorCodes.DataNotFound);
            }
            const serverInfoMap = yield (0, serverList_1.getServerInfoMap)();
            let winners = [];
            const hostPlayer = {
                roleId: lottery.RoleId,
                roleName: "",
                serverId: 0,
                serverName: "",
            };
            const hostRoleInfo = yield RoleInfoServiceV2.getRoleInfo(lottery.RoleId);
            if (hostRoleInfo) {
                hostPlayer.roleName = hostRoleInfo.RoleName;
                hostPlayer.serverId = hostRoleInfo.ServerId;
                if (hostRoleInfo.ServerId) {
                    const serverInfo = serverInfoMap.get(hostRoleInfo.ServerId);
                    if (serverInfo) {
                        hostPlayer.serverName = serverInfo.name;
                    }
                }
            }
            if (lottery.DrawStatus === 1 /* Drawn */) {
                const winRoleIds = yield (0, momentLotteryAttendService_1.getWinLotteryPlayerRoleIds)(curRoleId, lottery, {
                    page: params.page,
                    pageSize: params.pageSize,
                });
                winners = yield bluebird.map(winRoleIds, function (roleId) {
                    return __awaiter(this, void 0, void 0, function* () {
                        const roleInfo = yield RoleInfoServiceV2.getRoleInfo(roleId);
                        const showInfo = {
                            roleId: roleId,
                            roleName: "",
                            serverId: 0,
                            serverName: "",
                        };
                        if (roleInfo) {
                            showInfo.roleName = roleInfo.RoleName;
                            showInfo.serverId = roleInfo.ServerId;
                        }
                        if (showInfo.serverId) {
                            const serverInfo = serverInfoMap.get(showInfo.serverId);
                            if (serverInfo) {
                                showInfo.serverName = serverInfo.name;
                            }
                        }
                        return showInfo;
                    });
                });
            }
            const data = {
                momentId: lottery.MomentId,
                winners,
                drawStatus: lottery.DrawStatus,
                winnerNum: lottery.WinnerNum,
                hostPlayer: hostPlayer,
                drawTime: lottery.DrawTime,
            };
            res.send({ code: 0, data: data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.momentLotteryWinnersHandler = momentLotteryWinnersHandler;
//# sourceMappingURL=momentLotteryController.js.map