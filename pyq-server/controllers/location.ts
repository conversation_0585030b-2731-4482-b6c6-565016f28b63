/* eslint-disable @typescript-eslint/no-var-requires */
import { Context } from "../context";
import { errorHandler, formatResult } from "../helper";
import { LocationService } from "../services/v2/locationService";
const PyqProfile = require("../../models/PyqProfile");
const PyqGeo = require("../../models/PyqGeo");
const QnmRoleInfos = require("../../models/QNMRoleInfos");
const util = require("../../common/util");
const _ = require("lodash");
const GeoLocation = require("../models/location");
const getRoleIdFunc = require("../data/block_lbs_player_ids").getRoleIds;
const cache = require("../../common/cacheService");
const QnmRoleAble = require("../../models/mixins/QnmRoleAble");
const logger = require("../../common/logger");
const MapService = require("../services/map");

function logLocation(roleId, longitude, latitude) {
  const today = util.formatDate(new Date(), "yyyy-MM-dd");
  const LOG_NAME = "pyq_lbs_locations_" + today;
  return MapService.getGeoAddress(longitude, latitude)
    .then(function (address) {
      if (address && address.province && address.city) {
        logger.add(
          LOG_NAME,
          "[PyqLbsLocation]," +
          JSON.stringify({ roleId: roleId, country: address.nation, province: address.province, city: address.city }),
          { subPath: "/pyq_geo_log" }
        );
      }
    })
    .catch(function (err) {
      logger.error(err);
    });
}

export function updatePlayerLocation(req, res, next) {
  let latitude, longitude, roleId;
  let isHideLbs;
  return req.paramsValidator
    .param("roleid")
    .param("latitude")
    .param("longitude")
    .validate()
    .then(function () {
      latitude = req.params.latitude;
      longitude = req.params.longitude;
      roleId = req.params.roleid;
    })
    .then(function () {
      return GeoLocation.savePlayerGeo(roleId, longitude, latitude);
    })
    .then(function (data) {
      logLocation(roleId, longitude, latitude);
      res.send({ code: 0, msg: "Ok" });
    })
    .catch(function (err) {
      errorHandler(err, req, res, next);
    });
}

export function getNearByPlayers(req, res, next) {
  let curRoleId, latitude, longitude, distance;
  let isCurPlayerHideLbs;
  return req.paramsValidator
    .param("roleid")
    .param("longitude")
    .param("latitude")
    .param("distance")
    .validate()
    .then(function () {
      curRoleId = req.params.roleid;
      latitude = req.params.latitude;
      longitude = req.params.longitude;
      distance = req.params.distance;
    })
    .then(function () {
      return PyqProfile.isHideLbsByRoleId(curRoleId);
    })
    .then(function (value) {
      isCurPlayerHideLbs = value;
    })
    .then(function () {
      return GeoLocation.getNearByPlayerIds(curRoleId, longitude, latitude, distance);
    })
    .then(function (locations) {
      const roleIds = _.map(locations, "roleId");
      const getRoleInfos = QnmRoleInfos.find({ RoleId: roleIds }, { cols: ["RoleId", "Gender", "ServerId", "JobId"] });
      const getRoleLbsSetting = PyqGeo.find({ RoleId: roleIds }, { cols: ["RoleId", "HideLbs"] });
      return [locations, getRoleInfos, getRoleLbsSetting];
    })
    .spread(function (locations, players, playerLbsSettings) {
      const roleIdToLocation = util.keyToRecordHash(locations, "roleId");
      const roleIdToLbsSetting = util.keyToRecordHash(playerLbsSettings, "RoleId");
      let canAccess;
      players = _.map(players, function (p) {
        const lbsSetting = roleIdToLbsSetting[p.RoleId];
        if (isCurPlayerHideLbs || !lbsSetting || PyqGeo.isHideLbs(lbsSetting)) {
          canAccess = false;
        } else {
          canAccess = true;
        }
        const loc = roleIdToLocation[p.RoleId];
        p.longitude = loc.longitude;
        p.latitude = loc.latitude;
        p.clazz = p.JobId;
        p.canacess = canAccess;
        delete p.JobId;
        return p;
      });
      return players;
    })
    .then(function (players) {
      return filterTestServerRoleIds(players);
    })
    .then(function (players) {
      return filterBlockPlayers(players);
    })
    .then(function (roleInfos) {
      const data = formatResult(roleInfos);
      res.send({ code: 0, data: data });
    })
    .catch(function (err) {
      errorHandler(err, req, res, next);
    });
}

export const getLbsBlockPalyerIds = cache.cacheInRedis(getRoleIdFunc, {
  cacheKey: "pyq:block_lbs_player_ids",
});

export function filterBlockPlayers(players) {
  return getLbsBlockPalyerIds().then(function (blockLbsPlayerIds) {
    const blockPlayerIdSet = new Set(blockLbsPlayerIds);
    return _.reject(players, function (p) {
      return blockPlayerIdSet.has(p.RoleId);
    });
  });
}

export function filterTestServerRoleIds(players) {
  const TestConfig = require("../../common/config").testCfg;
  if (TestConfig.ignore_lbs_test_account) {
    return players;
  } else {
    const roleIds = _.map(players, "RoleId");
    const roleIdToPlayer = util.keyToRecordHash(players, "RoleId");
    return QnmRoleAble.filterRoleIdsByValidServerId(roleIds).then(function (validRoleIds) {
      return _.map(validRoleIds, (roleId) => {
        return roleIdToPlayer[roleId];
      });
    });
  }
}

export function switchToOffline(req, res, next) {
  let roleId;
  return req.paramsValidator
    .param("roleid")
    .validate()
    .then(function () {
      roleId = req.params.roleid;
      const ctx = Context.createWithRequest(req);
      const locationService = new LocationService();
      return locationService.switchToOffline(ctx, roleId);
    })
    .then(function () {
      res.send({ code: 0, msg: "Ok" });
    })
    .catch(function (err) {
      errorHandler(err, req, res, next);
    });
}

export function getPlayerLocation(req, res, next) {
  let roleId;
  return req.paramsValidator
    .param("roleid")
    .validate()
    .then(function () {
      roleId = req.params.roleid;
      return GeoLocation.getByRoleId(roleId);
    })
    .then(function (data) {
      res.send({ code: 0, data: data });
    })
    .catch(function (err) {
      errorHandler(err, req, res, next);
    });
}
