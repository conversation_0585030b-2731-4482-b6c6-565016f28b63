"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.list = exports.add = void 0;
const errorHandler = require("../errorHandler");
const PyqScenePhoto_1 = require("../../models/PyqScenePhoto");
const helper_1 = require("../helper");
function add(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            let schema = {
                index: { type: Number },
                url: { type: String }
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            let params = req.params;
            let id = yield PyqScenePhoto_1.PyqScenePhoto.add(params.roleid, params.index, params.url);
            res.send({ code: 0, data: { id: id } });
        }
        catch (err) {
            errorHandler(err, req, res, next);
        }
    });
}
exports.add = add;
function list(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            let params = req.params;
            let list = yield PyqScenePhoto_1.PyqScenePhoto.listAllByRoleId(params.roleid, ['ID', 'Index', 'Url', 'CreateTime']);
            res.send({ code: 0, data: { list: list } });
        }
        catch (err) {
            errorHandler(err, req, res, next);
        }
    });
}
exports.list = list;
//# sourceMappingURL=scenePhoto.js.map