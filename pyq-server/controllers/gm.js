"use strict";
/* eslint-disable @typescript-eslint/no-var-requires */
/**
 * Created by zhenhua on 16-11-28.
 */
/* eslint-disable prefer-promise-reject-errors */
Object.defineProperty(exports, "__esModule", { value: true });
const BaseController = require("./base");
const GmController = new BaseController();
const errorHandler = require("../errorHandler");
const _ = require("lodash");
const PyqGmProfile = require("../../models/PyqGmProfile");
const PyqGmSendFlower = require("../../models/PyqGmSendFlower");
const PyqProfile = require("../../models/PyqProfile");
const QnmRoleInfos = require("../../models/QNMRoleInfos");
const PyqGmComment = require("../../models/PyqGmComment");
const PyqGmLike = require("../../models/PyqGmLike");
const Promise = require("bluebird");
const gmReviewService = require("../services/gmReviewInfos");
const GmRenQiService = require("../services/gmMonthRenqi.js");
const config = require("../../common/config");
const util = require("../../common/util");
const db = require("../../common/db");
const flower_1 = require("../models/flower");
const WeekRank = require("../../service/qnm/cache/WeekRank");
const data_1 = require("../../common/data");
function getGmReview(gmId, roleId) {
    return gmReviewService.getGmReview(gmId, roleId);
}
function getGmCard(gmId, roleId) {
    return Promise.all([
        PyqGmProfile.getProfile(gmId),
        getGmReview(gmId, roleId),
        PyqGmLike.countGmLike(gmId),
        PyqGmLike.isRecentLiked(gmId, roleId),
    ]).spread((profile, reviewInfo, likesCount, isRecentLiked) => {
        const result = _.merge({}, profile, reviewInfo);
        result.likesCount = likesCount;
        result.isRecentLiked = isRecentLiked;
        return result;
    });
}
function addGmComment(gmId, roleId, text) {
    return PyqGmComment.insert({ RoleId: roleId, GmId: gmId, Text: text }).then((id) => {
        return { id: id };
    });
}
function addGmLike(gmId, roleId) {
    return PyqGmLike.addLike(gmId, roleId);
}
const Service = {};
Service.getGmCard = getGmCard;
Service.addGmComment = addGmComment;
Service.addGmLike = addGmLike;
GmController.Service = Service;
GmController.getCard = function (req, res, next) {
    return Service.getGmCard(req.params.gmid, req.params.roleid)
        .then((data) => {
        GmController.succSend(res, data);
    })
        .catch(function (err) {
        errorHandler(err, req, res, next);
    });
};
GmController.addGmComment = function (req, res, next) {
    return req.paramsValidator
        .param("text", { type: "String", maxlen: 72 })
        .validate()
        .then(() => {
        return Service.addGmComment(req.params.gmid, req.params.roleid, req.params.text);
    })
        .then((data) => {
        GmController.succSend(res, data);
    })
        .catch(function (err) {
        errorHandler(err, req, res, next);
    });
};
GmController.like = function (req, res, next) {
    return Service.addGmLike(req.params.gmid, req.params.roleid)
        .then((data) => {
        GmController.succSend(res, data);
    })
        .catch(function (err) {
        errorHandler(err, req, res, next);
    });
};
GmController.checkGmIdAndRoleId = function (req, res, next) {
    let params;
    return req.paramsValidator
        .param("roleid", { type: "Number" })
        .param("gmid", { type: "Number" })
        .validate()
        .then(() => {
        params = req.params;
        return PyqGmProfile.findOne({ GmId: params.gmid });
    })
        .then((gmProfile) => {
        if (!gmProfile) {
            return Promise.reject({ errorType: "DataNotFound", msg: "找不到gmid对应的记录" });
        }
        else {
            return QnmRoleInfos.findOne({ RoleId: params.roleid });
        }
    })
        .then((roleInfo) => {
        if (!roleInfo) {
            return Promise.reject({ errorType: "DataNotFound", msg: "找不到roleid对应的记录" });
        }
    })
        .then(() => {
        return next();
    })
        .catch((err) => {
        errorHandler(err, req, res, next);
    });
};
GmController.getGmUserList = function getGmUserList(req, res, next) {
    const start = Math.max(req.params.start || 0, 0);
    const step = Math.min(req.params.step || 10, 20);
    return GmRenQiService.listByRenQi(start, start + step - 1)
        .then((list) => {
        return GmController.succSend(res, list);
    })
        .catch((err) => {
        errorHandler(err, req, res, next);
    });
};
GmController.authSendFlower = function authSendFlower(req, res, next) {
    if (config.testCfg.skip_auth) {
        next();
    }
    else {
        if (!req.params.flower) {
            res.send({ code: -1, msg: "flower parameter is required!" });
        }
        else {
            const calToken = util.hexMd5(req.params.flower + config.AUTH_TOKEN_SALT);
            if (calToken !== req.params.token) {
                res.send({ code: -2, msg: "Invalid Token" });
            }
            else {
                next();
            }
        }
    }
};
GmController.sendFlower = function sendFlower(req, res, next) {
    const roleId = req.params.roleid;
    const serverId = req.params.server;
    const gmId = req.params.gmid;
    const flowerStr = req.params.flower;
    const flower = flower_1.Flower.fromString(flowerStr);
    if (!flower) {
        res.send({ code: -1, msg: "flower parameter has wrong synatax!" });
        return;
    }
    return db
        .transactByKnexQuerys([
        PyqGmSendFlower.scope().insert({
            Roleid: roleId,
            GmId: gmId,
            FlowerId: flower.id,
            Count: flower.count,
            RenQi: flower.renqi,
            CreateTime: Date.now(),
        }),
        PyqProfile.scope().where("RoleId", roleId).increment("SendFlowerRenQi", flower.renqi),
        PyqProfile.scope().where("RoleId", data_1.Constants.GM_ROLE_ID).increment("FlowerRenQi", flower.renqi), // 鲜花人气增加
    ])
        .then(() => {
        return Promise.all([
            WeekRank.addAllScore(serverId, roleId, { SendFlowerRenQi: flower.renqi }),
            GmRenQiService.incrRenQi(gmId, flower.renqi),
        ]);
    })
        .then((result) => {
        const renqi = result[1];
        GmController.succSend(res, { gmid: gmId, renqi: renqi });
    })
        .catch((err) => {
        errorHandler(err, req, res, next);
    });
};
GmController.getMonthlyStar = function (req, res, next) {
    return GmRenQiService.getMonthlyStar()
        .then((star) => {
        GmController.succSend(res, star);
    })
        .catch((err) => {
        errorHandler(err, req, res, next);
    });
};
module.exports = GmController;
//# sourceMappingURL=gm.js.map