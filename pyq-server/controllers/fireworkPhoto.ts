import { getIp } from "../../common/util";
import { PARAM_TYPES } from "../common/paramsValidator";
import {  checkParams, errorHandler } from "../helper";
import * as FireworkPhotoService from "../services/fireworkPhoto";
import { FireworkPhotoReq } from "../type/req";

export async function list(req, res, next) {
  try {
    let schema = {
      roleid: { type: Number },
      targetid: { type: Number },
    };
    let params = req.params;
    await checkParams(params, schema);
    let ret = await FireworkPhotoService.list(params);
    res.send({ code: 0, data: ret });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function getByIds(req, res, next) {
  try {
    let schema = {
      roleid: { type: Number },
      ids: { type: PARAM_TYPES.CSV_ARRAY, maxSize: 4, each: { type: Number } },
    };
    await checkParams(req.params, schema);
    let params = req.params;
    let ret = await FireworkPhotoService.getByIds(params);
    res.send({ code: 0, data: ret });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function update(req, res, next) {
  try {
    let schema = {
      roleid: { type: Number },
      url: { type: String },
      index: { type: Number, min:0, max: 9},
    };
    await checkParams(req.params, schema);
    let params: FireworkPhotoReq.Update = req.params;
    let ret = await FireworkPhotoService.update(params);
    let ip = getIp(req);
    FireworkPhotoService.auditFireWorkPhoto(params.roleid, [params.url], ret.id, ip);
    res.send({ code: 0, data: ret });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function remove(req, res, next) {
  try {
    let schema = {
      roleid: { type: Number },
      index: { type: Number, min:0, max: 9},
    };
    await checkParams(req.params, schema);
    let params: FireworkPhotoReq.Remove = req.params;
    let ret = await FireworkPhotoService.remove(params);
    res.send({ code: 0, data: ret });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}
