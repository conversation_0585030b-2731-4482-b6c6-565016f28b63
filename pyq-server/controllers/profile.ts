import * as ProfileService from '../../service/qnm/pyq/profile';
import { BasicTargetParams } from '../../common/type';
import { checkParams } from '../helper';
import { getProfileRecord, update<PERSON>ianghao, syncBackgroundRecord } from '../../service/qnm/pyq/profile';
import util = require('../../common/util');
import { syncBackgroundReq } from '../type/req';
/**
 * Created by zhenhua on 2017/5/10.
 */
const BaseController = require('./base')
const ProfileController = new BaseController()
const PyqProfile = require('../../models/PyqProfile')
const errorHandler = require('../errorHandler')
const config = require('../../common/config')

let EnumVipLevels = {
  NO_VIP: 0,
  VIP: 1
}

const MAX_BACKGROUND_ID = 9;
export function checkTokenForUpdateVipLevel(req, res, next) {
  let params = req.params
  const calToken = util.hexMd5(params.roleid + params.viplevel + params.time + config.TOKEN_SALT)
  if (calToken === params.token) {
    next()
  } else {
    res.send({ code: -1, msg: 'Token Invalid!' })
  }
}

export function updateVipLevel(req, res, next) {
  let roleId, vipLevel
  return req.paramsValidator
    .param('roleid', { type: Number })
    .param('viplevel', { type: Number, values: [EnumVipLevels.NO_VIP, EnumVipLevels.VIP] })
    .validate()
    .then(() => {
      roleId = req.params.roleid
      vipLevel = req.params.viplevel
      return PyqProfile.findOne({ RoleId: roleId }, ['SpLevel'])
    }).then(profile => {
      if (!profile) {
        return Promise.reject({ msg: 'roleId not found' })
      } else {
        if (profile.SpLevel === vipLevel) {
          if (vipLevel === EnumVipLevels.NO_VIP) {
            return Promise.reject({ msg: 'Already NoVip Status' })
          } else {
            return Promise.reject({ msg: 'Already Vip Status' })
          }
        } else {
          return PyqProfile.updateByCondition({ RoleId: roleId }, { SpLevel: vipLevel })
        }
      }
    }).then(() => {
      ProfileController.succSend(res, { msg: 'Ok!' })
    }).catch(err => {
      errorHandler(err, req, res, next)
    })
}

export async function setLianghao(req, res, next) {
  try {
    let schema = {
      roleid: { type: Number },
      lianghao: { type: String }
    }
    await checkParams(req.params, schema)
    let params = req.params as { roleid: number, lianghao: string }
    let data = await updateLianghao(params.roleid, params.lianghao)
    res.send({ code: 0, data: data })
  } catch (err) {
    errorHandler(err, req, res, next)
  }
}

export async function getProfile(req, res, next) {
  try {
    let schema = {
      roleid: { type: Number },
      targetid: { type: Number, required: false }
    }
    await checkParams(req.params, schema)
    let params: BasicTargetParams = req.params
    let queryRoleId = params.targetid || params.roleid;
    let data = await getProfileRecord(queryRoleId, params)
    res.send({ code: 0, data: data })
  } catch (err) {
    errorHandler(err, req, res, next)
  }
}

export async function setProfilePhoto(req, res, next) {
  let ip = util.getIp(req)
  req.params.ip = ip
  ProfileService.set(req.params, function () {
    res.send(util.response.apply(null, arguments));
  })
}

export async function syncBackgroundId(req, res, next) {
  try {
    let schema = {
      roleid: { type: Number },
      id: { type: Number, max: MAX_BACKGROUND_ID },
      validity: { type: Number }
    }
    await checkParams(req.params, schema)
    let params: syncBackgroundReq = req.params
    let data = await syncBackgroundRecord(params)
    res.send({ code: 0, data })
  } catch (err) {
    errorHandler(err, req, res, next)
  }
}
