"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.queryEvents = exports.getEvents = exports.addEvent = void 0;
const EventService = require("../../service/qnm/pyq/event");
const util = require("../../common/util");
const follow_1 = require("./follow");
const helper_1 = require("../helper");
const base_1 = require("../../service/qnm/pyq/events/common/base");
function addEvent(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const data = yield EventService.dispatchEvent(req.params);
            res.send({ code: 0, data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.addEvent = addEvent;
function getEvents(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            // do some basic type cast
            const eventType = parseInt(req.params.type, 10);
            const targetId = parseInt(req.params.targetid, 10);
            req.params.type = eventType;
            req.params.targetid = targetId;
            if (req.params.type == base_1.EventTypes.Following) {
                return (0, follow_1.getFollowing)(req, res, next);
            }
            else {
                const data = yield EventService.get(req.params);
                res.send({ code: 0, data: data });
            }
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.getEvents = getEvents;
function queryEvents(req, res, next) {
    EventService.query(req.params, function () {
        res.send(util.response.apply(null, arguments));
    });
}
exports.queryEvents = queryEvents;
//# sourceMappingURL=events.js.map