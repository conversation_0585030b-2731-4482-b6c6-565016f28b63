"use strict";
/* eslint-disable @typescript-eslint/no-var-requires */
/**
 * Created by zhenhua on 16-11-3.
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const config_1 = require("../common/config");
const helper_1 = require("../helper");
const i18n_1 = require("../i18n/i18n");
const logger_1 = require("../logger");
const modelProxy_1 = require("../models/modelProxy");
const base2_1 = require("./base2");
const BaseController = require("./base");
const info = new BaseController();
const errorHandler = require("../errorHandler");
const QnmRoleInfos = require("../../models/QNMRoleInfos");
const PyqProfile = require("../../models/PyqProfile");
const _ = require("lodash");
const ParamsTypes = require("../../common/paramsValidator").PARAM_TYPES;
const Constants = require("../../common/data").Constants;
const logger = (0, logger_1.clazzLogger)("controllers/info");
info.setFightingCapacity = function (req, res, next) {
    let roleId, fightCapacity;
    return req.paramsValidator
        .param("roleid")
        .param("value")
        .validate()
        .then(function () {
        roleId = req.params.roleid;
        fightCapacity = req.params.value;
        const query = QnmRoleInfos.scope().where("RoleId", roleId).update({ FightingCapacity: fightCapacity });
        return QnmRoleInfos.executeByQuery(query);
    })
        .then(function () {
        info.succSend(res, { msg: "OK" });
    })
        .catch(function (err) {
        errorHandler(err, req, res, next);
    });
};
info.getFightingCapacity = function (req, res, next) {
    let roleId;
    return req.paramsValidator
        .param("roleid")
        .param("targetid", { required: false })
        .validate()
        .then(function () {
        roleId = req.params.targetid || req.params.roleid;
        return QnmRoleInfos.findOne({ RoleId: roleId }, ["FightingCapacity"]);
    })
        .then(function (fc) {
        const fcField = _.get(fc, "FightingCapacity") || "";
        res.send({ code: 0, msg: fcField });
    })
        .catch(function (err) {
        errorHandler(err, req, res, next);
    });
};
info.getLocations = function (req, res, next) {
    return req.paramsValidator
        .param("targetids", { type: ParamsTypes.JSON_ARRAY })
        .validate()
        .then(function () {
        const roleIds = req.params.targetids;
        return PyqProfile.where({ RoleId: roleIds }, { cols: ["RoleId", "Location", "Privacy"] });
    })
        .then(function (rows) {
        const data = _.map(rows, (r) => {
            const p = PyqProfile.Privacy.fromPrivacyStr(r.Privacy);
            const hideLocation = p.location;
            if (hideLocation) {
                return { RoleId: r.RoleId, Location: "" };
            }
            else {
                return { RoleId: r.RoleId, Location: r.Location };
            }
        });
        info.succSend(res, data, { toLowerCase: false });
    })
        .catch(function (err) {
        errorHandler(err, req, res, next);
    });
};
info.getAvatar = function (req, res, next) {
    let roleId;
    return req.paramsValidator
        .param("roleid")
        .validate()
        .then(function () {
        roleId = req.params.roleid;
        return PyqProfile.findOne({ RoleId: roleId }, ["RoleId", "Photo", "ShowPhoto", "PhotoAudit"]);
    })
        .then(function (row) {
        if (row) {
            require("../../common/data").pyq.setPhotoView(row, roleId);
            const photo = _.get(row, "Photo") || "";
            info.succSend(res, { msg: photo });
        }
        else {
            info.succSend(res, { msg: "" });
        }
    })
        .catch(function (err) {
        errorHandler(err, req, res, next);
    });
};
function isRoleLevelMatch(roleInfo) {
    if (roleInfo.XianFanStatus === 1 /* XianFan */) {
        return true;
    }
    else {
        return roleInfo.Level >= config_1.FriendCircleMinLevel;
    }
}
function isEnableIslandHandle(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const schema = {
                targetid: { type: Number, required: false },
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            const roleId = req.params.targetid || req.params.roleid;
            const [profile, roleInfo] = yield Promise.all([
                modelProxy_1.ProfileModel.findOne({ RoleId: roleId }, ["RoleId"]),
                modelProxy_1.RoleInfoModel.findOne({ RoleId: roleId }, ["RoleId", "RoleName", "Level", "XianFanStatus"]),
            ]);
            if (profile && roleInfo && roleInfo.RoleId && isRoleLevelMatch(roleInfo)) {
                res.send({ code: 0, data: { msg: true } });
            }
            else {
                logger.warn({ roleId, profile, roleInfo }, "PlayerNotEnableIsland");
                res.send({ code: -1, data: { msg: false } });
            }
        }
        catch (err) {
            errorHandler(err, req, res, next);
        }
    });
}
info.isEnableIsland = isEnableIslandHandle;
info.setRoleName = function (req, res, next) {
    return req.paramsValidator
        .param("roleid", { type: Number, values: [Constants.GM_ROLE_ID, Constants.JL_ROLE_ID] }) // 现在只允许GM, 和金陵情报官
        .param("rolename", { type: String })
        .validate()
        .then(() => {
        const roleId = req.params.roleid;
        const roleName = req.params.rolename;
        return QnmRoleInfos.updateByCondition({ RoleId: roleId }, { RoleName: roleName });
    })
        .then(() => {
        res.send({ code: 0, msg: "ok" });
    })
        .catch((err) => {
        errorHandler(err, req, res, next);
    });
};
/**
 * @api {POST} /qnm/hideusedname 设置是否隐藏曾用名
 * @apiGroup Users
 * @apiUse CommonParamFields
 * @apiParam {String="true","false"} hide 是否隐藏
 *
 */
info.setHideUsedName = function (req, res, next) {
    const UsedNameStatuses = QnmRoleInfos.UsedNameStatuses;
    const lang = (0, base2_1.getLangFromReq)(req);
    const t = (0, i18n_1.getLangTrans)(lang);
    let roleId;
    return req.paramsValidator
        .param("hide", { values: ["true", "false"] })
        .param("roleid")
        .validate()
        .then(() => {
        roleId = req.params.roleid;
        const isHide = JSON.parse(req.params.hide);
        const newUsedNameStatus = isHide ? UsedNameStatuses.HIDE : UsedNameStatuses.PUBLIC;
        return QnmRoleInfos.findOne({ RoleId: roleId }, ["UsedName", "UsedNameStatus"]).then((roleInfo) => {
            if (roleInfo) {
                if (_.isEmpty(roleInfo.UsedName)) {
                    return Promise.reject({ msg: t("youHaveNotSetPreviousName") });
                }
                else {
                    return [roleInfo.UsedNameStatus, newUsedNameStatus];
                }
            }
            else {
                return Promise.reject({ msg: t("roleInfoNotFound") });
            }
        });
    })
        .spread((curUsedNameStatus, newUsedNameStatus) => {
        if (curUsedNameStatus === newUsedNameStatus) {
            const msg = curUsedNameStatus === UsedNameStatuses.HIDE
                ? t("yourPreviousNameIsAlreadyHidden")
                : t("yourPreviousNameIsAlreadyPublic");
            return Promise.reject({ msg: msg });
        }
        else {
            return QnmRoleInfos.updateByCondition({ RoleId: roleId }, { UsedNameStatus: newUsedNameStatus });
        }
    })
        .then(() => {
        info.succSend(res, { msg: t("modifySuccess") });
    })
        .catch((err) => {
        errorHandler(err, req, res, next);
    });
};
module.exports = info;
//# sourceMappingURL=info.js.map