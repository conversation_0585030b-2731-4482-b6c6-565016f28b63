/* eslint-disable @typescript-eslint/no-var-requires */
const BaseController = require("./base");
const AuthController = new BaseController();
const util = require("../../common/util");
const config = require("../../common/config");
const SKIP_AUTH_LIST = require("../data/routerAuthList").SKIP_AUTH_LIST;
const jwt = require("jwt-simple");
const _ = require("lodash");
import * as bluebird from "bluebird";
import * as authUtil from "../../common/auth";
import { LOGIN_TIME_LONG_TERM_MARK, SKEY_CHECK_VALID_TIME_OFFSET, Statues } from "../../common/constants";
import * as sessionUtil from "../../common/session";
import { CookieCfg } from "../../common/type";
import { getServerIdFromRoleId, getUnixTimeStamp } from "../../common/util2";
import { getProfileRecord } from "../../service/qnm/pyq/profile";
import { testCfg } from "../common/config";
import { CHEAT_SKEY, DS_WEB_CONFIG, ErrorTypes, L10_SESS_COOKIE, apiPrefix } from "../constants";
import { errorCodes } from "../errorCodes";
import { BusinessError, checkParams, errorHandler, handleOperation } from "../helper";
import { clazzLogger } from "../logger";
import * as DashenService from "../services/daShen";
import { RoleInfoModel } from "../services/modelProxy";
import { IMarkRoleStatusParams } from "../type/req";
import { SESS_INFO } from "../types";
import { AuthLoginIdType, LoginJwtPayload, LoginJwtPayloadTuple } from "../types/type";
import PyqAuthService = require("../../service/qnm/pyq/auth");
const logger = clazzLogger("controllers/auth");

const secret = config.JWT_TOKEN_SECRET;
const skeyHeader = jwt.encode("", secret).split(".")[0] + ".";

export function login(req, res, next) {
  return handleOperation(PyqAuthService.login)(req, res, next);
}

export function logout(req, res, next) {
  return handleOperation(PyqAuthService.logOut)(req, res, next);
}

async function assistLoginProcess(roleId, serverId): Promise<{ skey: string }> {
  const idType = "assist_role";
  const idVal = roleId;
  const sessKey = idType + ":" + idVal;
  const loginTime = Date.now();
  await sessionUtil.set(sessKey, { roleid: roleId, server: serverId, time: loginTime });
  await sessionUtil.setExpire(sessKey, config.ONE_DAY_SECONDS * 60);
  const skey = authUtil.getSkey(loginTime, idType, idVal);
  return { skey: skey };
}

export function assistLogin(req, res, next) {
  return req.paramsValidator
    .param("roleid", { type: Number })
    .param("serverid", { type: Number })
    .validate()
    .then(() => {
      return assistLoginProcess(req.params.roleid, req.params.serverid);
    })
    .then((skey) => {
      AuthController.succSend(res, skey);
    })
    .catch((err) => {
      errorHandler(err, req, res, next);
    });
}

/**
 * 检查skey中的颁发时间是否过期， 问题来源可见 https://cloud.pm.netease.com/v6/issues/86970
 * @param timeInSession
 * @param timeInSkey
 * @returns
 */
function isTimeInSkeyValid(timeInSession: string, timeInSkey: string) {
  if (timeInSkey === LOGIN_TIME_LONG_TERM_MARK) {
    return true;
  } else {
    const timeInSkeyInt = parseInt(timeInSkey, 10);
    const timeInSessionInt = parseInt(timeInSession, 10);
    return Math.abs(timeInSessionInt - timeInSkeyInt) <= SKEY_CHECK_VALID_TIME_OFFSET;
  }
}

function decodeSkey(skey: string): LoginJwtPayload {
  const decoded: LoginJwtPayloadTuple = jwt.decode(skey, secret, false, "HS256");
  const loginInfo = authUtil.LoginPayload.toObj(decoded);
  return loginInfo;
}

/**
 * validate login by roleid and skey
 * @param {Object} req.
 * @param {Object} req.params
 * @param {Number} req.params.roleid
 * @param {String} req.params.skey
 */
export async function validateLogin(req) {
  const userSkey = req.params.skey || req.cookies[L10_SESS_COOKIE.name];
  const skey = skeyHeader + userSkey;
  const roleId = "" + req.params.roleid;
  const routePath = req.route.path as string;
  let loginInfo: LoginJwtPayload = { time: "", idType: "", idVal: "", account: "" };
  let cheatSkeyEnable = false;
  try {
    loginInfo = decodeSkey(skey);
  } catch (err) {
    if (userSkey !== CHEAT_SKEY) {
      logger.warn({ req, err, skey }, "ParseSkeyInValid");
    }
    if ((testCfg.skip_skey_check && userSkey === CHEAT_SKEY) || testCfg.skip_skey_check_full) {
      cheatSkeyEnable = true;
      const account = req.params.account || "<EMAIL>"
      loginInfo = { idType: "role", idVal: roleId, time: `${Date.now()}`, account };
    } else {
      return bluebird.reject("Invalid_key"); // 1、非法skey
    }
  }
  if (loginInfo.idType === AuthLoginIdType.FacePinchCloud) {
    if (!routePath.startsWith(apiPrefix + "/face_pinch/")) {
      throw errorCodes.AuthScopeNotMatch;
    }
  }
  if (roleId != loginInfo.idVal) {
    if (!req.skipIdMatchCheck) {
      return bluebird.reject("Id_mismatch"); // 2、id不匹配
    }
  }
  const sessionId = loginInfo.idType + ":" + loginInfo.idVal;
  const sessionData = await sessionUtil.get(sessionId);

  if (cheatSkeyEnable || req.skipSkeySessionCheck) {
    // do nothing
  } else {
    if (!sessionData) {
      return bluebird.reject("No_session"); // 3、session已过期
    }
    const loginTimeInSession: string = _.toString(sessionData.time); // redis取出来的数据不会保持Int类型， 需要手动转换下
    if (!isTimeInSkeyValid(loginTimeInSession, loginInfo.time)) {
      return bluebird.reject("Key_outdated"); // 4、skey已过期
    }
  }

  // 和老的实现保持兼容
  req._SESS_INFO_ = { id: sessionId, ...loginInfo } as SESS_INFO;
  return sessionData;
}

export function isLoginByDs(session: SESS_INFO) {
  return session.idType === DS_WEB_CONFIG.sessionIdType;
}

export async function isLoginByDsForRoleId(roleId: number) {
  const sessionId = DS_WEB_CONFIG.sessionIdType + ":" + roleId;
  const sessionData = await sessionUtil.get(sessionId);
  return !!sessionData;
}

export function getLoginSession(req): SESS_INFO {
  return req._SESS_INFO_ || { id: 0, idType: "", idVal: "" };
}

export function checkAuthMiddleware(req, res, next) {
  const path = req.route.path + "";
  const isRouteSkipAuth = req.route.skipAuth || req.skipSkey;
  if (isRouteSkipAuth || SKIP_AUTH_LIST[path]) {
    next();
  } else {
    validateLogin(req)
      .then(function (info) {
        req.params.server = info ? info.server : 0; // 从session中取出服务器ID
        req.session = {
          language: info ? info.language : "cn",
          country: info ? Number(info.country) : 0,
        };
        next();
      })
      .catch(function (err: any) {
        errorHandlerForLoginCheck(err, req, res);
      });
  }
}

interface ILoginByDs {
  accessToken: string;
  mockRoleId?: number;
}

/**
 * legacy code not follow error process pattern, use standalone error handler
 * @param err
 * @param req
 * @param res
 */
function errorHandlerForLoginCheck(err: any, req: any, res: any) {
  let failMsg = "auth failed";
  if (err && err.message) {
    failMsg = err.message;
  }
  if (err && err.msg) {
    failMsg = err.msg;
  }
  if (typeof err === "string") {
    failMsg = err;
  }
  const code = err.code || -2;
  const errorResult = { code, msg: "非法请求：" + failMsg };
  logger.warn({ req, res, err, params: req.params }, "CheckLoginAuthFailed");
  res.send(util.response(errorResult));
}

async function dashenWebLoginProcess(roleId: number, serverId: number) {
  const idType = DS_WEB_CONFIG.sessionIdType;
  const idVal = roleId;
  const sessKey = idType + ":" + idVal;
  const loginTime = Date.now();
  await sessionUtil.set(sessKey, { roleid: roleId, server: serverId, time: loginTime });
  await sessionUtil.setExpire(sessKey, config.ONE_DAY_SECONDS * 60);
  const skey = authUtil.getSkey(loginTime, idType, idVal);
  return { skey: skey };
}

export async function markRoleStatus(req, res, next) {
  try {
    const params: IMarkRoleStatusParams = req.params;
    const schema = {
      roleid: { type: Number },
      status: { type: Number, values: [Statues.Normal, Statues.Deleted] },
    };
    await checkParams(params, schema);
    const record = await RoleInfoModel.findOne({ RoleId: params.roleid as unknown as number }, ["RoleId"]);
    let isOk = false;
    let isChanged = false;
    if (record && record.RoleId) {
      const ret = await RoleInfoModel.updateByCondition({ RoleId: params.roleid }, { State: params.status });
      isOk = ret.affectedRows > 0;
      isChanged = ret.changedRows > 0;
    } else {
      await bluebird.reject(errorCodes.RoleInfoNotFound);
    }
    res.send({ code: 0, data: { isOk: isOk, isChanged: isChanged } });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function loginByDS(req, res, next) {
  try {
    const schema = {
      accessToken: { type: String },
    };
    await checkParams(req.params, schema);
    const params: ILoginByDs = req.params;
    let role = { roleId: 0, server: 0 };
    let mdRoleList = [];
    if (config.testCfg.test_env && params.mockRoleId) {
      role.roleId = params.mockRoleId;
      role.server = getServerIdFromRoleId(role.roleId);
    } else {
      const roleList = await DashenService.getBindRoleList(params.accessToken);
      role = await DashenService.getActiveBindRole(roleList);
      mdRoleList = DashenService.filterMdRoles(roleList);
    }
    if (role) {
      const data = await dashenWebLoginProcess(role.roleId, role.server);
      const profile = await getProfileRecord(role.roleId, {});
      const cookieCfg: CookieCfg = {
        path: L10_SESS_COOKIE.path,
        domain: L10_SESS_COOKIE.domain,
        httpOnly: L10_SESS_COOKIE.httpOnly,
      };
      res.setCookie(L10_SESS_COOKIE.name, data.skey, cookieCfg);
      res.send({ code: 0, data: profile });
    } else {
      if (mdRoleList.length > 0) {
        await BusinessError(ErrorTypes.DSRoleNotActive, "绑定角色尚未被选中");
      } else {
        await BusinessError(ErrorTypes.DSRoleNotBind, "大神尚未绑定手游角色");
      }
    }
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function loginByDSMock(req, res, next) {
  try {
    const schema = {
      accessToken: { type: String },
      roleid: { type: Number, required: false, default: 100100001 },
    };
    await checkParams(req.params, schema);
    const roleId = req.params.roleid;
    const profile = await getProfileRecord(roleId, {});
    res.send({ code: 0, data: profile });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function loginByDSDispatch(req, res, next) {
  const params: { mockRoleId: number } = req.params;
  if (config.testCfg.test_env && params.mockRoleId) {
    return loginByDSMock(req, res, next);
  } else {
    return loginByDS(req, res, next);
  }
}

export async function validateAuthForFuxi(req, res, next) {
  next();
}

export function validateSecureApi(req, res, next) {
  const MAX_TIME_DIFF_ALLOW = 300;
  if (config.testCfg.skip_token_check) {
    next();
  } else {
    const params = req.params;
    const time = params.time,
      urs = params.urs,
      account = params.account,
      roleId = params.roleid,
      token = util.hexMd5(time + account + urs + roleId + config.AUTH_TOKEN_SALT);

    if (testCfg.token_time_diff_check) {
      const serverTs = getUnixTimeStamp();
      if (Math.abs(serverTs - time) > MAX_TIME_DIFF_ALLOW) {
        logger.warn({ time, serverTs, req }, "LoginTokenTimeMisMatch");
        res.send(errorCodes.AuthTimeDeltaTooLarge);
      }
    }

    if (params.token != token) {
      logger.warn({ actual: params.token, expect: token, req }, "LoginTokenMismatch");
      res.send({ code: -2, msg: "Token misMatch" });
    } else {
      next();
    }
  }
}
