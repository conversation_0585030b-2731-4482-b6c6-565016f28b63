import { checkPageParams, checkParams, errorHandler } from "../helper";
import * as ReportLogService from "../services/reportLog";


export async function deviceAllowChecker(req, res, next) {
  try {
    const deviceId = req.params.device_id;
    await ReportLogService.checkDeviceAllow(deviceId);
    next();
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}


export async function getNosToken(req, res, next) {
  let schema = {
    objectname: { type: String },
  };
  try {
    await checkParams(req.params, schema);
    let data = await ReportLogService.getNosToken(req.params);
    res.send({ code: 0, data: data });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function addFile(req, res, next) {
  let schema = {
    device_id: { type: String },
    url: { type: String },
  };
  try {
    await checkParams(req.params, schema);
    let data = await ReportLogService.addFile(req.params);
    res.send({ code: 0, data: data });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function listFiles(req, res, next) {
  let schema = {
    start_time: { type: String, required: false },
    end_time: { type: String, required: false },
    device_id: { type: String, max: 100, default: 100 },
  };
  try {
    await checkPageParams(req.params);
    await checkParams(req.params, schema);
    let data = await ReportLogService.listFiles(req.params);
    res.send({ code: 0, data: data });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function listAllowDevice(req, res, next) {
  try {
    let data = await ReportLogService.listAllowDevice(req.params);
    res.send({ code: 0, data: data });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function addAllowDevice(req, res, next) {
  let schema = {
    device_id: { type: String },
  };
  try {
    await checkParams(req.params, schema);
    let data = await ReportLogService.addAllowDevice(req.params);
    res.send({ code: 0, data: data });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function removeAllowDevice(req, res, next) {
  let schema = {
    device_id: { type: String },
  };
  try {
    await checkParams(req.params, schema);
    let data = await ReportLogService.removeAllowDevice(req.params);
    res.send({ code: 0, data: data });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}
