/* eslint-disable prefer-spread */
/* eslint-disable prefer-rest-params */
import * as util from "../../common/util";
import * as MessageService from "../../service/qnm/pyq/message";
import { checkParamsByAjv } from "../common/ajvCheck";
import { errorHandler } from "../helper";
import { checkStateBannedForAction } from "../services/profileBanStateService";
import { IAddMessageSchema, IAnswerMessageSchema } from "../types/messageTypes";

export async function addMessage(req, res, next) {
  try {
    checkParamsByAjv(req.params, IAddMessageSchema);
    await checkStateBannedForAction(req, req.params.roleid, "Message");
    const info = await MessageService.addMsg(req);
    res.send({ code: 0, data: info });
  } catch (error) {
    errorHandler(error, req, res, next);
  }
}

export function getMessages(req, res, next) {
  MessageService.get(req.params, function () {
    res.send(util.response.apply(null, arguments));
  });
}

export function delMessage(req, res, next) {
  MessageService.del(req.params, function () {
    res.send(util.response.apply(null, arguments));
  });
}

export async function answerMessage(req, res, next) {
  try {
    checkParamsByAjv(req.params, IAnswerMessageSchema);
    await checkStateBannedForAction(req, req.params.roleid, "Message");
    const info = await MessageService.addMsg(req);
    res.send({ code: 0, data: info });
  } catch (error) {
    errorHandler(error, req, res, next);
  }
}
