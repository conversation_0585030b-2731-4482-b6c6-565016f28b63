"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getToken = void 0;
const helper_1 = require("../helper");
const FpService = require("../services/fp");
function getToken(req, res, next) {
    try {
        const roleId = req.params.roleid;
        const token = FpService.getToken({
            uid: roleId,
            review: 1 /* PRPASSED */,
            extraInfo: {},
        });
        res.send({ code: 0, data: token });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
exports.getToken = getToken;
//# sourceMappingURL=fp.js.map