import { checkParamsByAjv } from "../common/ajvCheck";
import { BussError, errorCodes } from "../errorCodes";
import { errorHandler } from "../helper";
import * as bluebird from "bluebird";
import {
  getMomentLotteryAttendCount,
  getWinLotteryPlayerRoleIds,
  isPlayerParticipateLottery,
} from "../services/momentLotteryAttendService";

import * as MomentLotteryAttendService from "../services/momentLotteryAttendService";
import { getShowLotteryByMomentId, lotteryPrizeFromString } from "../services/momentLotteryService";
import {
  getLotteryServerScope,
  MomentLotteryShowReq,
  MomentLotteryShowResp,
  MomentLotteryWinnersReq,
  MomentLotteryWinnersResp,
} from "../types/momentLotteryType";
import * as RoleInfoServiceV2 from "../services/roleInfoServiceV2";
import { getServerInfoMap } from "../../service/qnm/server/serverList";
import { EDrawStatus } from "../models/momentLotteryModel";
import { MomentLotteryAttendModel } from "../models/momentLotteryAttendModel";
import { logger } from "../logger";
import { getRootMergedServerId } from "../../service/qnm/server/list";
import { getServerIdFromRoleId } from "../../common/util2";

export async function momentLotteryShowHandler(req, res, next) {
  try {
    const params = req.params as MomentLotteryShowReq;
    const schema = {
      type: "object",
      required: ["roleid", "momentId"],
      properties: {
        roleid: { type: "integer", minimum: 1 },
        momentId: { type: "integer", minimum: 1 },
      },
    };
    await checkParamsByAjv(req.params, schema);
    const lottery = await getShowLotteryByMomentId(params.momentId);
    if (!lottery) {
      await BussError(errorCodes.DataNotFound);
    }
    const prizeList = lotteryPrizeFromString(lottery.Prizes);
    const winPrizes = prizeList.map((p) => {
      return {
        id: p.itemId,
        num: p.num,
      };
    });

    const hostPlayerRoleId = lottery.RoleId;
    const hostPlayerServerId = await getRootMergedServerId(getServerIdFromRoleId(hostPlayerRoleId));

    const isMyLottery = lottery.RoleId == params.roleid;
    const data: MomentLotteryShowResp = {
      isMyLottery,
      isParticipate: false,
      type: lottery.Type,
      momentId: lottery.MomentId,
      winnerNum: lottery.WinnerNum,
      minLevel: lottery.MinLevel,
      participateNum: 0,
      winPrizes,
      drawTime: lottery.DrawTime,
      winnerRoleName: "",
      requirements: {
        like: !!lottery.RequireLike,
        comment: !!lottery.RequireComment,
        forward: !!lottery.RequireForward,
        follow: !!lottery.RequireFollow,
      },
      serverScope: getLotteryServerScope(lottery.ServerScope),
      hostPlayer: {
        roleId: hostPlayerRoleId,
        serverId: hostPlayerServerId,
      },
      myAction: {
        isLike: false,
        isComment: false,
        isForward: false,
        isFollow: false,
      },
      drawStatus: lottery.DrawStatus,
      isWin: false,
    };


    if (!isMyLottery) {
      const myActionIsLike = await MomentLotteryAttendService.isPlayerLikeMoment(params.roleid, lottery.MomentId);
      const myActionIsComment = await MomentLotteryAttendService.isPlayerCommentMoment(params.roleid, lottery.MomentId);
      const myActionIsFollow = await MomentLotteryAttendService.isPlayerFollowMomentRoleId(
        params.roleid,
        lottery.RoleId
      );
      const myActionIsForward = await MomentLotteryAttendService.isPlayerForwardMoment(params.roleid, lottery.MomentId);
      data.myAction = {
        isLike: myActionIsLike,
        isComment: myActionIsComment,
        isForward: myActionIsForward,
        isFollow: myActionIsFollow,
      };
    }

    // 获取动态抽奖的参与人数
    const participateNum = await getMomentLotteryAttendCount(lottery.MomentId);
    data.participateNum = participateNum;

    if (!data.isMyLottery) {
      // 只有不是我的奖励，才需要是否参与了抽奖
      const isParticipate = await isPlayerParticipateLottery(params.roleid, lottery.MomentId);
      data.isParticipate = isParticipate;
    }

    // 如果动态已经开奖，计算winnerRoleName字段
    if (lottery.DrawStatus === EDrawStatus.Drawn) {
      const firstAttendWinner = await MomentLotteryAttendModel.getFirstAttendWinner(lottery.MomentId);
      if (firstAttendWinner) {
        const winnerRoleId = firstAttendWinner.RoleId;
        const winnerRoleInfo = await RoleInfoServiceV2.getRoleInfo(winnerRoleId);
        if (winnerRoleInfo) {
          data.winnerRoleName = winnerRoleInfo.RoleName;
        } else {
          logger.warn({ winnerRoleId }, "WinnerRoleInfoNotExist");
        }
      } else {
        logger.warn({ momentId: lottery.MomentId }, "FirstAttendWinnerNotExist");
      }
    }

    res.send({ code: 0, data: data });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function momentLotteryWinnersHandler(req, res, next) {
  try {
    const params = req.params as MomentLotteryWinnersReq;
    const schema = {
      type: "object",
      required: ["roleid", "momentId", "page", "pageSize"],
      properties: {
        roleid: { type: "number" },
        momentId: { type: "number" },
        page: { type: "number", default: 1, minimum: 1 },
        pageSize: { type: "number", default: 10, minimum: 1, maximum: 20 },
      },
    };
    await checkParamsByAjv(params, schema);
    const curRoleId = params.roleid;
    const lottery = await getShowLotteryByMomentId(params.momentId);
    if (!lottery) {
      await BussError(errorCodes.DataNotFound);
    }
    const serverInfoMap = await getServerInfoMap();
    let winners: MomentLotteryWinnersResp["winners"] = [];
    const hostPlayer: MomentLotteryWinnersResp["hostPlayer"] = {
      roleId: lottery.RoleId,
      roleName: "",
      serverId: 0,
      serverName: "",
    };
    const hostRoleInfo = await RoleInfoServiceV2.getRoleInfo(lottery.RoleId);
    if (hostRoleInfo) {
      hostPlayer.roleName = hostRoleInfo.RoleName;
      hostPlayer.serverId = hostRoleInfo.ServerId;
      if (hostRoleInfo.ServerId) {
        const serverInfo = serverInfoMap.get(hostRoleInfo.ServerId);
        if (serverInfo) {
          hostPlayer.serverName = serverInfo.name;
        }
      }
    }

    if (lottery.DrawStatus === EDrawStatus.Drawn) {
      const winRoleIds = await getWinLotteryPlayerRoleIds(curRoleId, lottery, {
        page: params.page,
        pageSize: params.pageSize,
      });
      winners = await bluebird.map(winRoleIds, async function (roleId) {
        const roleInfo = await RoleInfoServiceV2.getRoleInfo(roleId);
        const showInfo = {
          roleId: roleId,
          roleName: "",
          serverId: 0,
          serverName: "",
        };
        if (roleInfo) {
          showInfo.roleName = roleInfo.RoleName;
          showInfo.serverId = roleInfo.ServerId;
        }
        if (showInfo.serverId) {
          const serverInfo = serverInfoMap.get(showInfo.serverId);
          if (serverInfo) {
            showInfo.serverName = serverInfo.name;
          }
        }
        return showInfo;
      });
    }

    const data: MomentLotteryWinnersResp = {
      momentId: lottery.MomentId,
      winners,
      drawStatus: lottery.DrawStatus,
      winnerNum: lottery.WinnerNum,
      hostPlayer: hostPlayer,
      drawTime: lottery.DrawTime,
    };
    res.send({ code: 0, data: data });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}
