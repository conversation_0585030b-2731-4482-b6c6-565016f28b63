"use strict";
/* eslint-disable @typescript-eslint/no-var-requires */
/**
 * Created by zhenhua on 2017/4/13.
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.delMomentHandler = exports.getMomentByIdHandler = exports.getMomentTags = exports.searchAtPlayers = exports.listAtPlayers = exports.addMomentHandler = exports.addArticle = exports.likeMoment = exports.cancelTopMoment = exports.setTopMoment = exports.getMoments = exports.listByGuild = exports.forward = void 0;
const MomentForwardModel = require("../../models/PyqMomentForward");
const EventBus = require("../eventBus");
const Events = EventBus.Events;
const getMomentTrimText = require("../../service/qnm/pyq/PyqMomentUtil").getMomentTrimText;
const ModelManager = require("../../models/ModelManager");
const PyqMoments = ModelManager.getModelByTableName("pyq_moment", "SLAVE");
const QnmRoleInfo = ModelManager.getModelByTableName("qnm_roleinfo", "SLAVE");
const _ = require("lodash");
const util2_1 = require("../../common/util2");
const UserPermission_1 = require("../../models/UserPermission");
const follow_1 = require("../../service/qnm/pyq/follow");
const LegacyMomentService = require("../../service/qnm/pyq/moment");
const personalMoment_1 = require("../../service/qnm/pyq/personalMoment");
const ajvCheck_1 = require("../common/ajvCheck");
const errorCodes_1 = require("../errorCodes");
const helper_1 = require("../helper");
const i18n_1 = require("../i18n/i18n");
const models_1 = require("../models");
const modelProxy_1 = require("../services/modelProxy");
const MomentService = require("../services/moment");
const momentAt_1 = require("../services/momentAt");
const profile_1 = require("../services/profile");
const util_1 = require("../services/util");
const auth_1 = require("./auth");
const base2_1 = require("./base2");
const momentTagService_1 = require("../../service/qnm/pyq/momentTagService");
const momentTagCfgModel_1 = require("../models/momentTagCfgModel");
function forward(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        // 兼容参数
        req.params.moment_id = req.params.id || req.params.moment_id;
        const lang = (0, base2_1.getLangFromReq)(req);
        const t = (0, i18n_1.getLangTrans)(lang);
        try {
            const schema = {
                roleid: { type: Number },
                moment_id: { type: Number, required: false },
                id: { type: Number, required: false },
                text: { required: true, type: String, maxlen: 800 },
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            const params = req.params;
            const roleId = params.roleid;
            const momentId = params.moment_id || params.id;
            if (!momentId) {
                throw errorCodes_1.errorCodes.InvalidParams;
            }
            yield (0, UserPermission_1.checkPermission)(roleId, UserPermission_1.Permission.Moment, "您已被管理员禁止转发朋友圈状态");
            const text = getMomentTrimText(req.params.text, { pureTextLimit: 32 });
            const isSensitive = yield (0, util_1.checkSensitiveWordByEnvSdk)(text);
            if (!isSensitive) {
                throw errorCodes_1.errorCodes.ContainSensitive;
            }
            else {
                const data = yield MomentForwardModel.forwardMoment(roleId, momentId, text);
                if (data.momentId) {
                    const session = (0, auth_1.getLoginSession)(req);
                    const payload = {
                        session: session,
                        forwardId: momentId,
                        originId: data.originId,
                        momentId: data.momentId,
                    };
                    EventBus.emit(Events.FORWARD_MOMENT, payload);
                }
                res.send({ code: 0, data: { id: data.momentId }, msg: t("forwardSuccess") });
            }
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.forward = forward;
function getMomentByGuildRoleIds(queryRoleIds, params) {
    return __awaiter(this, void 0, void 0, function* () {
        const limit = params.pagesize || 10;
        const maxId = params.lastid;
        let query = PyqMoments.normalScope()
            .whereIn("RoleId", queryRoleIds)
            .select(models_1.MomentCols)
            .orderBy("ID", "desc")
            .limit(limit);
        if (maxId) {
            query = query.where("ID", "<", maxId);
        }
        const moments = yield PyqMoments.executeByQuery(query);
        return moments;
    });
}
function getGuildMomentsSteamHandler(req, params, lang) {
    return __awaiter(this, void 0, void 0, function* () {
        const guildId = params.guildid;
        if (!guildId) {
            return [];
        }
        const guildRoleIds = yield QnmRoleInfo.getGuildRoleIds(guildId);
        if (guildRoleIds.length === 0) {
            return [];
        }
        const queryRoleIds = yield filterRoleIdsWithProtectMode(params.roleid, guildRoleIds);
        if (queryRoleIds.length === 0) {
            return [];
        }
        const selectAllTags = yield momentTagCfgModel_1.default.isSelectAllTags(params.selectAllTags, params.tagIds);
        let moments;
        const hasTagFilter = !selectAllTags && params.tagIds && params.tagIds.length > 0;
        if (hasTagFilter) {
            moments = yield momentTagService_1.MomentTagService.getHomeMomentByTagWithParams(req, {
                tagIds: params.tagIds,
                maxMomentId: params.lastid,
                limit: params.pagesize,
                curRoleId: params.roleid,
                viewRoleIds: queryRoleIds,
            });
        }
        else {
            moments = yield getMomentByGuildRoleIds(queryRoleIds, params);
        }
        const showMoments = yield LegacyMomentService.fillMomentsInfo(params, moments, lang);
        return showMoments;
    });
}
function filterRoleIdsWithProtectMode(roleId, roleIds) {
    return __awaiter(this, void 0, void 0, function* () {
        const roleMinors = yield models_1.RoleMinorModel.find({ roleId: roleIds, protectMode: 1 });
        if (roleMinors && roleMinors.length > 0) {
            const openProtectModeIds = roleMinors.map((item) => item.roleId);
            let friends = yield (0, profile_1.getFriendIds)(roleId);
            friends.push(Number(roleId));
            const followIds = yield (0, follow_1.getFollowIds)(roleId);
            const followOpenProtectModeIds = _.intersection(followIds, openProtectModeIds);
            if (followOpenProtectModeIds.length) {
                const followMeIds = yield modelProxy_1.FollowModel.filterFollowingMe(followOpenProtectModeIds, roleId);
                friends = friends.concat(followMeIds);
            }
            const needHideIds = _.difference(openProtectModeIds, friends);
            return _.difference(roleIds, needHideIds);
        }
        else {
            return roleIds;
        }
    });
}
function listByGuild(req, res, next) {
    const minGuildLimit = 10000; // 帮会id至少是个5位数
    const lang = (0, util2_1.getLangFromRequest)(req);
    return req.paramsValidator
        .param("guildid", { type: Number })
        .param("lastid", { type: Number, required: false })
        .param("pagesize", { type: Number, default: 10, max: 20 })
        .validate()
        .then(() => {
        const guildId = parseInt(req.params.guildid, 10);
        if (guildId < minGuildLimit) {
            return [];
        }
        else {
            return getGuildMomentsSteamHandler(req, req.params, lang);
        }
    })
        .then((data) => {
        res.send({ code: 0, data: { list: data } });
    })
        .catch((err) => {
        (0, helper_1.errorHandler)(err, req, res, next);
    });
}
exports.listByGuild = listByGuild;
function fixPageParam(page) {
    page = page || "0";
    if (page) {
        return _.toNumber(page) + 1;
    }
    else {
        return 1;
    }
}
function getMoments(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            req.params.pageSize = req.params.pagesize; //兼容以前参数
            req.params.page = fixPageParam(req.params.page); // 老的page参数从0计数
            const schema = {
                roleid: { type: Number },
                selectAllTags: { type: "boolean", required: false },
                tagIds: { type: Array, required: false },
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            if (req.params.targetid && !req.params.lastid) {
                // 新的个人心情置顶不支持lastid分页
                return getPlayerPersonalMoment(req, res, next);
            }
            else {
                return getPlayerHomeMoment(req, res, next);
            }
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.getMoments = getMoments;
function getPlayerHomeMoment(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const schema = {
                roleid: { type: Number },
                page: { type: Number, default: 1, min: 1 },
                pageSize: { type: Number, default: 10, max: 20 },
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            const params = req.params;
            const lang = (0, util2_1.getLangFromRequest)(req);
            const data = yield LegacyMomentService.get(req, params, lang);
            res.send({ code: 0, data: data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
function getPlayerPersonalMoment(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const schema = {
                roleid: { type: Number },
                targetid: { type: Number },
                page: { type: Number, default: 1, min: 1 },
                pageSize: { type: Number, default: 10, max: 20 },
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            const data = yield (0, personalMoment_1.getPlayerPersonalMoments)(req, req.params);
            res.send({ code: 0, data: data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
function setTopMoment(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const schema = {
                roleid: { type: Number },
                momentId: { type: Number },
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            const params = req.params;
            const data = yield (0, personalMoment_1.setTopPlayerMoment)(params.roleid, params.momentId);
            res.send({ code: 0, data: data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.setTopMoment = setTopMoment;
function cancelTopMoment(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const schema = {
                roleid: { type: Number },
                momentId: { type: Number },
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            const params = req.params;
            const data = yield (0, personalMoment_1.cancelTopPlayerMoment)(params.roleid, params.momentId);
            res.send({ code: 0, data: data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.cancelTopMoment = cancelTopMoment;
function likeMoment(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const schema = {
                type: "object",
                properties: {
                    roleid: { type: "integer" },
                    id: { type: "integer" },
                    action: { type: "string" },
                },
                required: ["roleid", "id"],
            };
            yield (0, ajvCheck_1.checkParamsByAjv)(req.params, schema);
            const data = yield LegacyMomentService.likeMoment(req);
            res.send({ code: 0, data: data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.likeMoment = likeMoment;
function addArticle(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        const sensitiveRule = { check: true, error: errorCodes_1.errorCodes.ContainSensitive };
        try {
            const schema = {
                roleid: { type: Number },
                title: { type: String, sensitive: sensitiveRule },
                content: { type: String, sensitive: sensitiveRule },
                imglist: { type: "JSON_ARRAY", maxSize: 3 },
                videolist: { type: "JSON_ARRAY", maxSize: 3 },
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            const params = req.params;
            const data = yield MomentService.addArticle(params, req);
            res.send({ code: 0, data: data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.addArticle = addArticle;
function addMomentHandler(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const data = yield MomentService.addMoment(req);
            res.send({ code: 0, data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.addMomentHandler = addMomentHandler;
function listAtPlayers(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const schema = {
                type: "object",
                properties: {
                    roleid: { type: "integer" },
                    page: { type: "integer", default: 1, minimum: 1 },
                    pageSize: { type: "integer", minimum: 1, maximum: 20, default: 10 },
                },
                required: ["roleid"],
            };
            (0, ajvCheck_1.checkParamsByAjv)(req.params, schema);
            const params = req.params;
            const data = yield momentAt_1.MomentAtService.listAtPlayers(params.roleid, { page: params.page, pageSize: params.pageSize });
            res.send({ code: 0, data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.listAtPlayers = listAtPlayers;
function searchAtPlayers(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const schema = {
                type: "object",
                properties: { roleid: { type: "number" }, kw: { type: "string" } },
                required: ["roleid", "kw"],
            };
            (0, ajvCheck_1.checkParamsByAjv)(req.params, schema);
            const params = req.params;
            const data = yield momentAt_1.MomentAtService.searchAtPlayers(params.roleid, params.kw);
            res.send({ code: 0, data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.searchAtPlayers = searchAtPlayers;
function getMomentTags(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const list = yield momentTagCfgModel_1.default.getTagList();
            const data = { list };
            res.send({ code: 0, data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.getMomentTags = getMomentTags;
function getMomentByIdHandler(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const params = req.params;
            const lang = (0, util2_1.getLangFromRequest)(req);
            params.lang = lang;
            const schema = {
                type: "object",
                required: ["id", "roleid"],
                properties: {
                    id: { type: "integer", minimum: 1 },
                    roleid: { type: "integer", minimum: 1 },
                    textStyle: { type: "string", enum: ["plain", "html"] },
                },
            };
            yield (0, ajvCheck_1.checkParamsByAjv)(req.params, schema);
            const data = yield LegacyMomentService.getMomentById(req, req.params);
            res.send({ code: 0, data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.getMomentByIdHandler = getMomentByIdHandler;
function delMomentHandler(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const schema = {
                type: "object",
                required: ["id", "roleid"],
                properties: {
                    roleid: { type: "integer", minimum: 1 },
                    id: { type: "integer", minimum: 1 },
                },
            };
            yield (0, ajvCheck_1.checkParamsByAjv)(req.params, schema);
            const delResp = yield LegacyMomentService.delMoment(req, req.params);
            res.send({ code: delResp.code, data: delResp.data, msg: delResp.msg });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.delMomentHandler = delMomentHandler;
//# sourceMappingURL=moment.js.map