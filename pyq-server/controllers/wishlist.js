"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.delHelpText = exports.addHelpText = exports.getHelps = exports.detail = exports.list = exports.listByRole = exports.syncWishlist = exports.updateStatus = exports.addHelps = exports.add = void 0;
const WishlistService = require("../services/wishlist");
const helper_1 = require("../helper");
const eventBus2_1 = require("../eventBus2");
const constants_1 = require("../constants");
function add(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const schema = {
                roleid: { type: Number },
                wishId: { type: String },
                type: { type: Number },
                text: { type: String, required: false, maxlen: 1000 },
                templateId: { type: Number, required: false },
                num: { type: Number, required: false },
                totalProgress: { type: Number, required: false },
                startTime: { type: Number },
                endTime: { type: Number },
                shareType: { type: Number, required: false },
                shareImgs: { type: Array, required: false, each: { type: String } },
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            const ret = yield WishlistService.add(req.params, req);
            res.send({ code: 0, data: ret });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.add = add;
function addHelps(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const schema = {
                roleid: { type: Number },
                wishId: { type: String },
                targetid: { type: Number },
                process: { type: Number, required: false },
                text: { type: String, required: false },
            };
            const params = req.params;
            yield (0, helper_1.checkParams)(params, schema);
            const ret = yield WishlistService.addHelps(req.params);
            const payload = {
                roleId: params.roleid,
                wishId: params.wishId,
                targetId: params.targetid,
            };
            eventBus2_1.EventBus.emit(constants_1.EventNames.WISH_HELP_FINISH, payload);
            res.send({ code: 0, data: ret });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.addHelps = addHelps;
function updateStatus(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const schema = {
                roleid: { type: Number },
                wishId: { type: String },
                status: { type: Number, required: false },
                visibility: { type: Number, required: false },
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            const ret = yield WishlistService.updateStatus(req.params);
            res.send({ code: 0, data: ret });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.updateStatus = updateStatus;
function syncWishlist(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const schema = {
                roleid: { type: Number },
                wishlist: { type: Array },
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            const ret = yield WishlistService.syncWishList(req.params);
            res.send({ code: 0, data: ret });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.syncWishlist = syncWishlist;
function listByRole(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        //查看某人的心愿单
        try {
            const schema = {
                roleid: { type: Number },
                targetid: { type: Number },
                page: { type: Number, default: 1, min: 1 },
                pageSize: { type: Number, default: 1, min: 1, max: 20 },
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            const ret = yield WishlistService.listByRole(req.params);
            res.send({ code: 0, data: ret });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.listByRole = listByRole;
function list(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        //查看朋友的心愿单
        try {
            const schema = {
                roleid: { type: Number },
                page: { type: Number, default: 1, min: 1 },
                pageSize: { type: Number, default: 1, min: 1, max: 20 },
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            const ret = yield WishlistService.list(req.params);
            res.send({ code: 0, data: ret });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.list = list;
function detail(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        //查看某个心愿单(包括助力)
        try {
            const schema = {
                roleid: { type: Number },
                wishId: { type: String },
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            const ret = yield WishlistService.detail(req.params);
            res.send({ code: 0, data: ret });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.detail = detail;
function getHelps(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const schema = {
                roleid: { type: Number },
                wishId: { type: String },
                page: { type: Number, default: 1, min: 1 },
                pageSize: { type: Number, default: 1, min: 1, max: 20 },
            };
            yield (0, helper_1.checkParams)(req.params, schema);
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.getHelps = getHelps;
function addHelpText(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const schema = {
                roleid: { type: Number },
                wishId: { type: String },
                text: { type: String, maxlen: 100 },
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            const data = yield WishlistService.addHelpText(req.params);
            res.send({ code: 0, data: data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.addHelpText = addHelpText;
function delHelpText(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const schema = {
                roleid: { type: Number },
                helpId: { type: Number },
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            const data = yield WishlistService.delHelpTextForWish(req.params);
            res.send({ code: 0, data: data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.delHelpText = delHelpText;
//# sourceMappingURL=wishlist.js.map