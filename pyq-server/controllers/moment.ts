/* eslint-disable @typescript-eslint/no-var-requires */
/**
 * Created by zhenhua on 2017/4/13.
 */

import * as MomentForwardModel from "../../models/PyqMomentForward";
const EventBus = require("../eventBus");
const Events = EventBus.Events;
const getMomentTrimText = require("../../service/qnm/pyq/PyqMomentUtil").getMomentTrimText;
const ModelManager = require("../../models/ModelManager");
const PyqMoments = ModelManager.getModelByTableName("pyq_moment", "SLAVE");
const QnmRoleInfo = ModelManager.getModelByTableName("qnm_roleinfo", "SLAVE");
import * as _ from "lodash";
import { Next, Request, Response } from "restify";
import { BasicParams } from "../../common/type";
import { convertBoolVal, getLangFromRequest } from "../../common/util2";
import { Permission, checkPermission } from "../../models/UserPermission";
import { getFollowIds } from "../../service/qnm/pyq/follow";
import * as LegacyMomentService from "../../service/qnm/pyq/moment";
import {
  cancelTopPlayerMoment,
  getPlayerPersonalMoments,
  setTopPlayerMoment,
} from "../../service/qnm/pyq/personalMoment";
import { checkParamsByAjv } from "../common/ajvCheck";
import { errorCodes } from "../errorCodes";
import { checkParams, errorHandler } from "../helper";
import { getLangTrans } from "../i18n/i18n";
import { MomentCols, MomentRecord, RoleMinorModel } from "../models";
import { FollowModel } from "../services/modelProxy";
import * as MomentService from "../services/moment";
import { MomentAtService } from "../services/momentAt";
import { getFriendIds } from "../services/profile";
import { checkSensitiveWordByEnvSdk } from "../services/util";
import { MomentReq } from "../type/req";
import { FORWARD_MOMENT_PAYLOAD } from "../types";
import { components, operations } from "../types/swagger";
import { Lang } from "../types/type";
import { getLoginSession } from "./auth";
import { getLangFromReq } from "./base2";
import { MomentTagService } from "../../service/qnm/pyq/momentTagService";
import momentTagCfgModel from "../models/momentTagCfgModel";

interface IForwardParams extends BasicParams {
  id?: number;
  moment_id?: number;
  text?: string;
}

export async function forward(req, res, next) {
  // 兼容参数
  req.params.moment_id = req.params.id || req.params.moment_id;
  const lang = getLangFromReq(req);
  const t = getLangTrans(lang);
  try {
    const schema = {
      roleid: { type: Number },
      moment_id: { type: Number, required: false },
      id: { type: Number, required: false },
      text: { required: true, type: String, maxlen: 800 },
    };
    await checkParams(req.params, schema);
    const params = req.params as IForwardParams;
    const roleId = params.roleid;
    const momentId = params.moment_id || params.id;
    if (!momentId) {
      throw errorCodes.InvalidParams;
    }
    await checkPermission(roleId, Permission.Moment, "您已被管理员禁止转发朋友圈状态");
    const text = getMomentTrimText(req.params.text, { pureTextLimit: 32 });
    const isSensitive = await checkSensitiveWordByEnvSdk(text);
    if (!isSensitive) {
      throw errorCodes.ContainSensitive;
    } else {
      const data = await MomentForwardModel.forwardMoment(roleId, momentId, text);
      if (data.momentId) {
        const session = getLoginSession(req);
        const payload: FORWARD_MOMENT_PAYLOAD = {
          session: session,
          forwardId: momentId,
          originId: data.originId,
          momentId: data.momentId,
        };
        EventBus.emit(Events.FORWARD_MOMENT, payload);
      }
      res.send({ code: 0, data: { id: data.momentId }, msg: t("forwardSuccess") });
    }
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

async function getMomentByGuildRoleIds(
  queryRoleIds: number[],
  params: IGetGuildMomentsSteamParams
): Promise<MomentRecord[]> {
  const limit = params.pagesize || 10;
  const maxId = params.lastid;
  let query = PyqMoments.normalScope()
    .whereIn("RoleId", queryRoleIds)
    .select(MomentCols)
    .orderBy("ID", "desc")
    .limit(limit);

  if (maxId) {
    query = query.where("ID", "<", maxId);
  }

  const moments = await PyqMoments.executeByQuery(query);
  return moments;
}

type TagFilterSchema = components["schemas"]["TagFilterSchema"];

interface IGetGuildMomentsSteamParams extends TagFilterSchema {
  guildid: number;
  roleid: number;
  pagesize?: number;
  lastid?: number;
  selectAllTags: boolean;
}

async function getGuildMomentsSteamHandler(req, params: IGetGuildMomentsSteamParams, lang: Lang) {
  const guildId = params.guildid;
  if (!guildId) {
    return [];
  }
  const guildRoleIds = await QnmRoleInfo.getGuildRoleIds(guildId);
  if (guildRoleIds.length === 0) {
    return [];
  }
  const queryRoleIds = await filterRoleIdsWithProtectMode(params.roleid, guildRoleIds);
  if (queryRoleIds.length === 0) {
    return [];
  }

  const selectAllTags = await momentTagCfgModel.isSelectAllTags(params.selectAllTags, params.tagIds);
  let moments: MomentRecord[];
  const hasTagFilter = !selectAllTags && params.tagIds && params.tagIds.length > 0;
  if (hasTagFilter) {
    moments = await MomentTagService.getHomeMomentByTagWithParams(req, {
      tagIds: params.tagIds,
      maxMomentId: params.lastid,
      limit: params.pagesize,
      curRoleId: params.roleid,
      viewRoleIds: queryRoleIds,
    });
  } else {
    moments = await getMomentByGuildRoleIds(queryRoleIds, params);
  }

  const showMoments = await LegacyMomentService.fillMomentsInfo(params, moments, lang);
  return showMoments;
}

async function filterRoleIdsWithProtectMode(roleId: number, roleIds: number[]) {
  const roleMinors = await RoleMinorModel.find({ roleId: roleIds, protectMode: 1 });
  if (roleMinors && roleMinors.length > 0) {
    const openProtectModeIds = roleMinors.map((item) => item.roleId);

    let friends = await getFriendIds(roleId);
    friends.push(Number(roleId));
    const followIds = await getFollowIds(roleId);
    const followOpenProtectModeIds = _.intersection(followIds, openProtectModeIds);
    if (followOpenProtectModeIds.length) {
      const followMeIds = await FollowModel.filterFollowingMe(followOpenProtectModeIds, roleId);
      friends = friends.concat(followMeIds);
    }
    const needHideIds = _.difference(openProtectModeIds, friends);
    return _.difference(roleIds, needHideIds);
  } else {
    return roleIds;
  }
}

export function listByGuild(req, res, next) {
  const minGuildLimit = 10000; // 帮会id至少是个5位数
  const lang = getLangFromRequest(req);
  return req.paramsValidator
    .param("guildid", { type: Number })
    .param("lastid", { type: Number, required: false })
    .param("pagesize", { type: Number, default: 10, max: 20 })
    .validate()
    .then(() => {
      const guildId = parseInt(req.params.guildid, 10);
      if (guildId < minGuildLimit) {
        return [];
      } else {
        return getGuildMomentsSteamHandler(req, req.params, lang);
      }
    })
    .then((data) => {
      res.send({ code: 0, data: { list: data } });
    })
    .catch((err) => {
      errorHandler(err, req, res, next);
    });
}

function fixPageParam(page: string) {
  page = page || "0";
  if (page) {
    return _.toNumber(page) + 1;
  } else {
    return 1;
  }
}

export async function getMoments(req, res, next) {
  try {
    req.params.pageSize = req.params.pagesize; //兼容以前参数
    req.params.page = fixPageParam(req.params.page); // 老的page参数从0计数
    const schema = {
      roleid: { type: Number },
      selectAllTags: { type: "boolean", required: false },
      tagIds: { type: Array, required: false },
    };
    await checkParams(req.params, schema);

    if (req.params.targetid && !req.params.lastid) {
      // 新的个人心情置顶不支持lastid分页
      return getPlayerPersonalMoment(req, res, next);
    } else {
      return getPlayerHomeMoment(req, res, next);
    }
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

async function getPlayerHomeMoment(req, res, next) {
  try {
    const schema = {
      roleid: { type: Number },
      page: { type: Number, default: 1, min: 1 },
      pageSize: { type: Number, default: 10, max: 20 },
    };
    await checkParams(req.params, schema);
    const params = req.params;
    const lang = getLangFromRequest(req);
    const data = await LegacyMomentService.get(req, params, lang);
    res.send({ code: 0, data: data });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

async function getPlayerPersonalMoment(req, res, next) {
  try {
    const schema = {
      roleid: { type: Number },
      targetid: { type: Number },
      page: { type: Number, default: 1, min: 1 },
      pageSize: { type: Number, default: 10, max: 20 },
    };
    await checkParams(req.params, schema);
    const data = await getPlayerPersonalMoments(req, req.params);
    res.send({ code: 0, data: data });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function setTopMoment(req, res, next) {
  try {
    const schema = {
      roleid: { type: Number },
      momentId: { type: Number },
    };
    await checkParams(req.params, schema);
    const params = req.params;
    const data = await setTopPlayerMoment(params.roleid, params.momentId);
    res.send({ code: 0, data: data });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function cancelTopMoment(req, res, next) {
  try {
    const schema = {
      roleid: { type: Number },
      momentId: { type: Number },
    };
    await checkParams(req.params, schema);
    const params = req.params;
    const data = await cancelTopPlayerMoment(params.roleid, params.momentId);
    res.send({ code: 0, data: data });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function likeMoment(req, res, next) {
  try {
    const schema = {
      type: "object",
      properties: {
        roleid: { type: "integer" },
        id: { type: "integer" },
        action: { type: "string" },
      },
      required: ["roleid", "id"],
    };
    await checkParamsByAjv(req.params, schema);
    const data = await LegacyMomentService.likeMoment(req);
    res.send({ code: 0, data: data });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function addArticle(req, res, next) {
  const sensitiveRule = { check: true, error: errorCodes.ContainSensitive };
  try {
    const schema = {
      roleid: { type: Number },
      title: { type: String, sensitive: sensitiveRule },
      content: { type: String, sensitive: sensitiveRule },
      imglist: { type: "JSON_ARRAY", maxSize: 3 },
      videolist: { type: "JSON_ARRAY", maxSize: 3 },
    };
    await checkParams(req.params, schema);
    const params: MomentReq.AddArticle = req.params;
    const data = await MomentService.addArticle(params, req);
    res.send({ code: 0, data: data });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function addMomentHandler(req: Request, res: Response, next: Next) {
  try {
    const data = await MomentService.addMoment(req);
    res.send({ code: 0, data });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

type ListAtPlayersReq = operations["atPlayersList"]["parameters"]["query"];

export async function listAtPlayers(req: Request, res: Response, next: Next) {
  try {
    const schema = {
      type: "object",
      properties: {
        roleid: { type: "integer" },
        page: { type: "integer", default: 1, minimum: 1 },
        pageSize: { type: "integer", minimum: 1, maximum: 20, default: 10 },
      },
      required: ["roleid"],
    };
    checkParamsByAjv(req.params, schema);
    const params: ListAtPlayersReq = req.params;
    const data = await MomentAtService.listAtPlayers(params.roleid, { page: params.page, pageSize: params.pageSize });
    res.send({ code: 0, data });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

type SearchAtPlayersReq = operations["atPlayersSearch"]["parameters"]["query"];
export async function searchAtPlayers(req: Request, res: Response, next: Next) {
  try {
    const schema = {
      type: "object",
      properties: { roleid: { type: "number" }, kw: { type: "string" } },
      required: ["roleid", "kw"],
    };
    checkParamsByAjv(req.params, schema);
    const params: SearchAtPlayersReq = req.params;
    const data = await MomentAtService.searchAtPlayers(params.roleid, params.kw);
    res.send({ code: 0, data });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

type GetMomentTagsRes = operations["momentTags"]["responses"]["200"]["content"]["application/json"]["data"];

export async function getMomentTags(req: Request, res: Response, next: Next) {
  try {
    const list = await momentTagCfgModel.getTagList();
    const data: GetMomentTagsRes = { list };
    res.send({ code: 0, data });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

type GetMomentByIdReq = operations["getMomentById"]["parameters"]["query"] & { lang: string };

export async function getMomentByIdHandler(req: Request, res: Response, next: Next) {
  try {
    const params = req.params as GetMomentByIdReq;
    const lang = getLangFromRequest(req);
    params.lang = lang;

    const schema = {
      type: "object",
      required: ["id", "roleid"],
      properties: {
        id: { type: "integer", minimum: 1 },
        roleid: { type: "integer", minimum: 1 },
        textStyle: { type: "string", enum: ["plain", "html"] },
      },
    };

    await checkParamsByAjv(req.params, schema);

    const data = await LegacyMomentService.getMomentById(req, req.params);
    res.send({ code: 0, data });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function delMomentHandler(req: Request, res: Response, next: Next) {
  try {
    const schema = {
      type: "object",
      required: ["id", "roleid"],
      properties: {
        roleid: { type: "integer", minimum: 1 },
        id: { type: "integer", minimum: 1 },
      },
    };
    await checkParamsByAjv(req.params, schema);
    const delResp = await LegacyMomentService.delMoment(req, req.params);
    res.send({ code: delResp.code, data: delResp.data, msg: delResp.msg });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}
