"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.removeAllowDevice = exports.addAllowDevice = exports.listAllowDevice = exports.listFiles = exports.addFile = exports.getNosToken = exports.deviceAllowChecker = void 0;
const helper_1 = require("../helper");
const ReportLogService = require("../services/reportLog");
function deviceAllowChecker(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const deviceId = req.params.device_id;
            yield ReportLogService.checkDeviceAllow(deviceId);
            next();
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.deviceAllowChecker = deviceAllowChecker;
function getNosToken(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        let schema = {
            objectname: { type: String },
        };
        try {
            yield (0, helper_1.checkParams)(req.params, schema);
            let data = yield ReportLogService.getNosToken(req.params);
            res.send({ code: 0, data: data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.getNosToken = getNosToken;
function addFile(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        let schema = {
            device_id: { type: String },
            url: { type: String },
        };
        try {
            yield (0, helper_1.checkParams)(req.params, schema);
            let data = yield ReportLogService.addFile(req.params);
            res.send({ code: 0, data: data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.addFile = addFile;
function listFiles(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        let schema = {
            start_time: { type: String, required: false },
            end_time: { type: String, required: false },
            device_id: { type: String, max: 100, default: 100 },
        };
        try {
            yield (0, helper_1.checkPageParams)(req.params);
            yield (0, helper_1.checkParams)(req.params, schema);
            let data = yield ReportLogService.listFiles(req.params);
            res.send({ code: 0, data: data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.listFiles = listFiles;
function listAllowDevice(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            let data = yield ReportLogService.listAllowDevice(req.params);
            res.send({ code: 0, data: data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.listAllowDevice = listAllowDevice;
function addAllowDevice(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        let schema = {
            device_id: { type: String },
        };
        try {
            yield (0, helper_1.checkParams)(req.params, schema);
            let data = yield ReportLogService.addAllowDevice(req.params);
            res.send({ code: 0, data: data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.addAllowDevice = addAllowDevice;
function removeAllowDevice(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        let schema = {
            device_id: { type: String },
        };
        try {
            yield (0, helper_1.checkParams)(req.params, schema);
            let data = yield ReportLogService.removeAllowDevice(req.params);
            res.send({ code: 0, data: data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.removeAllowDevice = removeAllowDevice;
//# sourceMappingURL=reportLog.js.map