import * as httpLib from '../../common/request';
import * as Url from 'url';
import { checkParams, queryServerIdByRoleId } from '../helper';
import { outApi, testCfg } from '../../common/config';
import { surveyLogger } from '../logger';

export interface ISubmitParams {
  question: number
  url: string
  serverId: number
}

export enum SubmitErrorCode {
  NOT_FOUND_ROLE_ID = 404,
}


export async function submit(req, res, next) {
  surveyLogger.info({ req: req, params: req.params }, 'surveySubmit')
  try {
    const schema = {
      question: { type: Number },
      url: { type: String },
      serverId: { type: Number, required: false }
    }
    await checkParams(req.params, schema)
    const params: ISubmitParams = req.params
    const roleId = getSubmitRoleId(req)
    if (roleId) {
      const rootServer = await queryServerIdByRoleId(roleId)
      const serverId = params.serverId || rootServer
      const postData = { playerId: roleId, serverId: serverId, surveyId: params.question }
      const ret = await notifyGameSurveyCompleted(postData)
      const retData = { resultCode: 200, resultDesc: "成功", apiRet: '' }
      if (testCfg.test_env) {
        retData.apiRet = ret
      }
      res.send(retData)
    } else {
      res.send({ resultCode: SubmitErrorCode.NOT_FOUND_ROLE_ID, resultDesc: 'playerId not found' })
    }
  } catch (err) {
    surveyLogger.error(err)
    // 返回正常让问卷能正常收集
    const resultCode = err.retCode || -1
    const resultDesc = err.message || '系统错误'
    res.send({ resultCode: resultCode, resultDesc: resultDesc })
  }
}


function getSubmitRoleId(req): number {
  const urlObj = Url.parse(req.params.url, true)
  const qs = urlObj.query
  const mdInfo = qs.qnm_md_info as string
  if (mdInfo) {
    const tokens = mdInfo.split('.')
    const roleIdStr = tokens[0]
    const roleId = parseInt(roleIdStr, 10)
    if (Number.isNaN(roleId)) {
      return null
    } else {
      return roleId
    }
  } else {
    return null
  }
}

function getGameServiceApi(): string {
  return outApi.gms
}

export interface INotifySurvey {
  serverId: number
  playerId: number
  surveyId: number
}

export interface GameApiParams extends INotifySurvey {
  cmd: string
  operatorInfo: string
}

export async function notifyGameSurveyCompleted(option: INotifySurvey) {
  const apiUrl = getGameServiceApi()
  const data: GameApiParams = {
    cmd: 'playerfinishsurvey',
    operatorInfo: "l10_md_server",
    playerId: option.playerId,
    serverId: option.serverId,
    surveyId: option.surveyId
  }
  try {
    surveyLogger.info('PrepareNotifyGame', { data: data })
    const ret = await httpLib.request({
      method: 'POST',
      url: apiUrl,
      body: data
    })
    surveyLogger.info('FinishNotifyGame', { data: data, ret: ret })
    return ret
  } catch (err) {
    surveyLogger.info('NotifyGameError', { data: data, err: err })
  }
}