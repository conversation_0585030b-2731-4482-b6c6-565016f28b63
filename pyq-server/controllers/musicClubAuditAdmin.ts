import { Request, Response, Next } from "restify";
import { musicClubAuditAdminCfg } from "../common/config.all";
import { MusicClubRecordingAuditTaskService } from "../services/music-club/musicClubRecordingAuditTaskService";
import { MusicClubRecordingModel } from "../models/musicClub/musicClubRecordingModel";
import { MusicClubRecordingAuditTaskModel } from "../models/musicClub/musicClubRecordingAuditTaskModel";
import * as knex from "knex";

/**
 * 音乐社团审核管理后台控制器
 */
export class MusicClubAuditAdminController {

  /**
   * 检查管理后台是否启用
   */
  private static checkEnabled(req: Request, res: Response, next: Next) {
    if (!musicClubAuditAdminCfg.enabled) {
      res.send(404, { error: "Admin panel is disabled" });
      return false;
    }
    return true;
  }

  /**
   * 获取审核任务列表
   */
  static async getAuditList(req: Request, res: Response, next: Next) {
    if (!MusicClubAuditAdminController.checkEnabled(req, res, next)) return;

    try {
      const {
        recordingId,
        userId,
        serverId,
        status,
        startTime,
        endTime,
        page = 1,
        pageSize = 20
      } = req.query;

      const pageNum = parseInt(page as string) || 1;
      const pageSizeNum = Math.min(parseInt(pageSize as string) || 20, 100); // 限制最大页面大小
      const offset = (pageNum - 1) * pageSizeNum;

      const auditTaskModel = MusicClubRecordingAuditTaskModel.getInstance();

      // 构建查询条件
      let countQuery = auditTaskModel.scope()
        .leftJoin('qnm_music_club_recording as r', 't.RecordingId', 'r.ID')
        .from('qnm_music_club_recording_audit_task as t');

      let dataQuery = auditTaskModel.scope()
        .leftJoin('qnm_music_club_recording as r', 't.RecordingId', 'r.ID')
        .from('qnm_music_club_recording_audit_task as t')
        .select([
          't.*',
          'r.Name as recording_name',
          'r.ServerId as recording_server_id',
          'r.MusicClubId as recording_music_club_id',
          'r.Duration as recording_duration',
          'r.DataUrl as recording_data_url',
          'r.AuditStatus as recording_audit_status'
        ]);

      // 应用筛选条件
      if (recordingId) {
        countQuery = countQuery.where('t.RecordingId', recordingId);
        dataQuery = dataQuery.where('t.RecordingId', recordingId);
      }

      if (userId) {
        countQuery = countQuery.where('t.RoleId', userId);
        dataQuery = dataQuery.where('t.RoleId', userId);
      }

      if (serverId) {
        countQuery = countQuery.where('r.ServerId', serverId);
        dataQuery = dataQuery.where('r.ServerId', serverId);
      }

      if (status !== undefined && status !== '') {
        countQuery = countQuery.where('t.Status', parseInt(status as string));
        dataQuery = dataQuery.where('t.Status', parseInt(status as string));
      }

      if (startTime) {
        // 将datetime-local格式转换为时间戳（毫秒）
        const startTimestamp = new Date(startTime as string).getTime();
        countQuery = countQuery.where('t.CreateTime', '>=', startTimestamp);
        dataQuery = dataQuery.where('t.CreateTime', '>=', startTimestamp);
      }

      if (endTime) {
        // 将datetime-local格式转换为时间戳（毫秒）
        const endTimestamp = new Date(endTime as string).getTime();
        countQuery = countQuery.where('t.CreateTime', '<=', endTimestamp);
        dataQuery = dataQuery.where('t.CreateTime', '<=', endTimestamp);
      }

      // 查询总数
      const countResult = await auditTaskModel.executeByQuery(countQuery.count('* as total'));
      const total = countResult[0]?.total || 0;
      const totalPages = Math.ceil(total / pageSizeNum);

      // 查询数据
      dataQuery = dataQuery.orderBy('t.CreateTime', 'desc').limit(pageSizeNum).offset(offset);
      const dataResult = await auditTaskModel.executeByQuery(dataQuery);

      // 格式化数据
      const data = dataResult.map((row: any) => ({
        ID: row.ID,
        RecordingId: row.RecordingId,
        VocalUrl: row.VocalUrl,
        Status: row.Status,
        RetryCount: row.RetryCount,
        CcTaskId: row.CcTaskId,
        RejectReason: row.RejectReason,
        CreateTime: row.CreateTime,
        UpdateTime: row.UpdateTime,
        recording: {
          Name: row.recording_name,
          RoleId: row.recording_role_id,
          ServerId: row.recording_server_id,
          MusicClubId: row.recording_music_club_id,
          Duration: row.recording_duration,
          DataUrl: row.recording_data_url,
          AuditStatus: row.recording_audit_status
        }
      }));

      res.send({
        success: true,
        data,
        total,
        page: pageNum,
        pageSize: pageSizeNum,
        totalPages
      });

    } catch (error) {
      console.error("获取审核任务列表失败:", error);
      res.send(500, { success: false, message: "获取数据失败" });
    }

    return next();
  }

  /**
   * 获取审核任务详情
   */
  static async getAuditDetail(req: Request, res: Response, next: Next) {
    if (!MusicClubAuditAdminController.checkEnabled(req, res, next)) return;

    try {
      const taskId = parseInt(req.params.id);
      if (!taskId) {
        res.send(400, { success: false, message: "无效的任务ID" });
        return next();
      }

      const auditTaskModel = MusicClubRecordingAuditTaskModel.getInstance();

      const query = auditTaskModel.scope()
        .leftJoin('qnm_music_club_recording as r', 't.RecordingId', 'r.ID')
        .from('qnm_music_club_recording_audit_task as t')
        .where('t.ID', taskId)
        .select([
          't.*',
          'r.Name as recording_name',
          'r.ServerId as recording_server_id',
          'r.MusicClubId as recording_music_club_id',
          'r.Duration as recording_duration',
          'r.DataUrl as recording_data_url',
          'r.AuditStatus as recording_audit_status',
          'r.TrackId as recording_track_id'
        ]);

      const result = await auditTaskModel.executeByQuery(query);

      if (!result || result.length === 0) {
        res.send(404, { success: false, message: "任务不存在" });
        return next();
      }

      const row = result[0];
      const data = {
        ID: row.ID,
        RecordingId: row.RecordingId,
        VocalUrl: row.VocalUrl,
        Status: row.Status,
        RetryCount: row.RetryCount,
        CcTaskId: row.CcTaskId,
        RejectReason: row.RejectReason,
        CreateTime: row.CreateTime,
        UpdateTime: row.UpdateTime,
        recording: {
          Name: row.recording_name,
          ServerId: row.recording_server_id,
          MusicClubId: row.recording_music_club_id,
          Duration: row.recording_duration,
          DataUrl: row.recording_data_url,
          AuditStatus: row.recording_audit_status,
          TrackId: row.recording_track_id
        }
      };

      res.send({ success: true, data });

    } catch (error) {
      console.error("获取审核任务详情失败:", error);
      res.send(500, { success: false, message: "获取详情失败" });
    }

    return next();
  }

  /**
   * 获取统计信息
   */
  static async getStatistics(req: Request, res: Response, next: Next) {
    if (!MusicClubAuditAdminController.checkEnabled(req, res, next)) return;

    try {
      const auditTaskModel = MusicClubRecordingAuditTaskModel.getInstance();

      // 分别查询各种状态的数量
      const totalQuery = auditTaskModel.scope().count('* as count');
      const pendingQuery = auditTaskModel.scope().where('Status', 0).count('* as count');
      const submittedQuery = auditTaskModel.scope().where('Status', 1).count('* as count');
      const passedQuery = auditTaskModel.scope().where('Status', 2).count('* as count');
      const rejectedQuery = auditTaskModel.scope().where('Status', 3).count('* as count');

      const [totalResult, pendingResult, submittedResult, passedResult, rejectedResult] = await Promise.all([
        auditTaskModel.executeByQuery(totalQuery),
        auditTaskModel.executeByQuery(pendingQuery),
        auditTaskModel.executeByQuery(submittedQuery),
        auditTaskModel.executeByQuery(passedQuery),
        auditTaskModel.executeByQuery(rejectedQuery)
      ]);
      res.send({
        success: true,
        data: {
          total: totalResult[0]?.count || 0,
          pending: pendingResult[0]?.count || 0,
          submitted: submittedResult[0]?.count || 0,
          passed: passedResult[0]?.count || 0,
          rejected: rejectedResult[0]?.count || 0
        }
      });

    } catch (error) {
      console.error("获取统计信息失败:", error);
      res.send(500, { success: false, message: "获取统计信息失败" });
    }

    return next();
  }

  /**
   * 重新提交审核任务
   */
  static async retryAuditTask(req: Request, res: Response, next: Next) {
    if (!MusicClubAuditAdminController.checkEnabled(req, res, next)) return;

    try {
      const taskId = parseInt(req.params.id);
      if (!taskId) {
        res.send(400, { success: false, message: "无效的任务ID" });
        return next();
      }

      const auditTaskService = new MusicClubRecordingAuditTaskService();
      const success = await auditTaskService.retryTask(taskId);

      if (success) {
        res.send({ success: true, message: "重新提交成功" });
      } else {
        res.send(400, { success: false, message: "重新提交失败，请检查任务状态" });
      }

    } catch (error) {
      console.error("重新提交审核任务失败:", error);
      res.send(500, { success: false, message: "重新提交失败" });
    }

    return next();
  }
}
