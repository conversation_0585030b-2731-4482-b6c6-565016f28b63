import * as EventService from '../../service/qnm/pyq/event'
import * as util from '../../common/util'
import { getFollowing } from './follow';
import { errorHandler } from '../helper';
import { EventTypes } from '../../service/qnm/pyq/events/common/base';
import { errorCodes } from '../errorCodes';

export async function addEvent(req, res, next) {
    try {
        const data = await EventService.dispatchEvent(req.params);
        res.send({ code: 0, data });
    } catch (err) {
        errorHandler(err, req, res, next)
    }
}

export async function getEvents(req, res, next) {
    try {
        // do some basic type cast
        const eventType = parseInt(req.params.type, 10);
        const targetId = parseInt(req.params.targetid, 10);
        req.params.type = eventType;
        req.params.targetid = targetId;

        if (req.params.type == EventTypes.Following) {
            return getFollowing(req, res, next)
        } else {
            const data = await EventService.get(req.params)
            res.send({ code: 0, data: data })
        }
    } catch (err) {
        errorHandler(err, req, res, next)
    }
}

export function queryEvents(req, res, next) {
    EventService.query(req.params, function () {
        res.send(util.response.apply(null, arguments));
    });
}
