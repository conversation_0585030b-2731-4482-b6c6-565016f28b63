"use strict";
/**
 * Created by zhenhua on 2017/6/8.
 */
Object.defineProperty(exports, "__esModule", { value: true });
const BaseController = require('./base');
const PanoramaController = new BaseController();
const errorHandler = require('../errorHandler');
const config = require('../../common/config');
const util = require('../../common/util');
const _ = require('lodash');
const PyqPanorama = require('../../models/PyqPanorama');
const QnmRoleInfos = require('../../models/QNMRoleInfos');
const Promise = require('bluebird');
const jwt = require('jwt-simple');
const ShareTokenUtil = {};
ShareTokenUtil._secret = 'MII$BOQ%IBAAJA&';
ShareTokenUtil._fixedHeader = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.';
ShareTokenUtil.sigin = function sign(id) {
    return jwt.encode({ id: id }, ShareTokenUtil._secret).replace(ShareTokenUtil._fixedHeader, '');
};
ShareTokenUtil.verify = function (token) {
    return jwt.decode(ShareTokenUtil._fixedHeader + token, ShareTokenUtil._secret);
};
function getPanoramaViewUrl(id) {
    const previewUrl = config.QnmPanoramaViewUrl || 'http://qnm.163.com/m/qjjy/';
    const shareToken = ShareTokenUtil.sigin(id);
    return previewUrl + `?id=${id}&share_token=${shareToken}`;
}
function formatHouseInfo(params) {
    const util = require('../../common/util');
    const info = _.pick(params, ['house_level', 'house_addr']);
    info.owner_id = parseInt(params.roleid, 10);
    info.guest_ids = util.csvStrToIntArray(params.guest_ids);
    // 传家宝会是罐，瓦，瓶这三个字中的一个，可以大于一个中文字符长度才认为传家宝有名字
    info.cjb_list = _.filter(util.csvStrToArray(params.cjb_list), s => s.length > 1);
    const defaultInfo = { owner_id: null, house_level: null, guest_ids: [], house_addr: '', cjb_list: [] };
    return _.defaults(info, defaultInfo);
}
/**
 * @api {POST} /qnm/panorama/create 上传全景图
 * @apiGroup  PanoramaHouse
 * @apiParam {String} roleid 角色id
 * @apiParam {String} top top
 * @apiParam {String} bottom bottom
 * @apiParam {String} left left
 * @apiParam {String} right right
 * @apiParam {String} front front
 * @apiParam {String} back back
 *
 * @apiSuccessExample {json} Success-Response:
 *  HTTP/1.1 200 OK
 *  {
 *     "code": 0,
 *     "data": {
 *      "id": 8,
 *      "preview_url": "http://test.nie.163.com/test_html/qnm/2017/jiayuan/?id=8"
 *      },
 *     "msg": "创建成功!"
 *  }
 */
PanoramaController.create = function (req, res, next) {
    let params;
    return req.paramsValidator
        .param('roleid')
        .param('top', { textType: 'url' })
        .param('bottom', { textType: 'url' })
        .param('left', { textType: 'url' })
        .param('right', { textType: 'url' })
        .param('front', { textType: 'url' })
        .param('back', { textType: 'url' })
        .param('house_level', { type: Number, required: false })
        .param('guest_ids', { type: String, required: false })
        .param('house_addr', { type: String, required: false })
        .param('cjb_list', { type: String, required: false })
        .validate()
        .then(() => {
        params = req.params;
        const houseInfo = JSON.stringify(formatHouseInfo(params));
        return PyqPanorama.insert({
            RoleId: params.roleid,
            Top: params.top,
            Bottom: params.bottom,
            Left: params.left,
            Right: params.right,
            Front: params.front,
            Back: params.back,
            ExtraInfo: houseInfo,
            CreateTime: Date.now()
        });
    })
        .then(insertInfo => {
        const id = insertInfo.insertId;
        res.send({ code: 0, data: { id: id, url: getPanoramaViewUrl(id) }, msg: '创建成功!' });
    }).catch(err => {
        errorHandler(err, req, res, next);
    });
};
function removeCDNPath(nosPath) {
    return nosPath.replace(/(https?):\/\/(.*).nosdn\.127\.net(.*)/, '$1://nos.netease.com/$2$3');
}
function getRoleInfos(roleIds) {
    return QnmRoleInfos.find({ RoleId: roleIds }, { cols: ['RoleId', 'RoleName', 'Level', 'JobId', 'ServerId', 'Gender'] });
}
function getHouseInfo(id) {
    const Servers = require('../../service/qnm/server/list');
    return PyqPanorama.findNormalById(id)
        .then(row => {
        if (row && row.ExtraInfo) {
            const panorama = _.reduce(['Top', 'Bottom', 'Left', 'Right', 'Front', 'Back'], (hash, prop) => {
                hash[prop] = removeCDNPath(row[prop]);
                return hash;
            }, {});
            const roleId = row.RoleId;
            const houseInfo = util.getJsonInfo(row.ExtraInfo);
            const guestIds = houseInfo.guest_ids;
            const ownerId = houseInfo.owner_id;
            const roleIds = _.uniq(_.compact(_.concat(roleId, guestIds, ownerId)));
            return Promise.all([
                getRoleInfos(roleIds),
                Servers.getHash()
            ]).spread((roleInfos, serverHash) => {
                const roleIdToInfos = util.keyToRecordHash(roleInfos, 'RoleId');
                const shareRoleInfo = roleIdToInfos[roleId];
                const server = _.defaults(serverHash[shareRoleInfo.ServerId] || { group: '测试组', name: '测试服' });
                const jobAvatar = QnmRoleInfos.getJobAvatar(shareRoleInfo);
                shareRoleInfo.Avatar = jobAvatar;
                shareRoleInfo.Server = server.group + '-' + server.name;
                const showRoleInfo = _.pick(shareRoleInfo, ['RoleName', 'Avatar', 'Level', 'Server']);
                houseInfo.guest_names = _.map(houseInfo.guest_ids, roleId => {
                    return _.get(roleIdToInfos[roleId], 'RoleName') || '';
                });
                houseInfo.owner_name = _.get(roleIdToInfos[houseInfo.owner_id], 'RoleName') || '';
                return {
                    roleInfo: showRoleInfo,
                    panorama: panorama,
                    houseInfo: _.pick(houseInfo, 'house_level', 'house_addr', 'owner_name', 'guest_names', 'cjb_list')
                };
            });
        }
        else {
            return null;
        }
    });
}
PanoramaController.checkShareToken = function (req, res, next) {
    if (config.testCfg.skip_share_token_auth) {
        next();
        return;
    }
    if (!req.params.share_token) {
        res.send({ code: -1, msg: '未携带分享口令' });
    }
    try {
        const info = ShareTokenUtil.verify(req.params.share_token);
        if (parseInt(req.params.id, 10) !== info.id) {
            res.send({ code: -1, msg: '分享口令与ID不一致' });
        }
        else {
            next();
        }
    }
    catch (err) {
        res.send({ code: -1, msg: '分享口令非法' });
    }
};
PanoramaController.getHouseInfo = function (req, res, next) {
    return req.paramsValidator
        .param('id', { type: Number })
        .validate()
        .then(() => {
        const id = req.params.id;
        return getHouseInfo(id);
    }).then(data => {
        PanoramaController.succSend(res, data);
    }).catch(err => {
        errorHandler(err, req, res, next);
    });
};
module.exports = PanoramaController;
//# sourceMappingURL=panorama.js.map