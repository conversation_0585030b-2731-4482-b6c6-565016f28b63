"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateSecureApi = exports.validateAuthForFuxi = exports.loginByDSDispatch = exports.loginByDSMock = exports.loginByDS = exports.markRoleStatus = exports.checkAuthMiddleware = exports.getLoginSession = exports.isLoginByDsForRoleId = exports.isLoginByDs = exports.validateLogin = exports.assistLogin = exports.logout = exports.login = void 0;
/* eslint-disable @typescript-eslint/no-var-requires */
const BaseController = require("./base");
const AuthController = new BaseController();
const util = require("../../common/util");
const config = require("../../common/config");
const SKIP_AUTH_LIST = require("../data/routerAuthList").SKIP_AUTH_LIST;
const jwt = require("jwt-simple");
const _ = require("lodash");
const bluebird = require("bluebird");
const authUtil = require("../../common/auth");
const constants_1 = require("../../common/constants");
const sessionUtil = require("../../common/session");
const util2_1 = require("../../common/util2");
const profile_1 = require("../../service/qnm/pyq/profile");
const config_1 = require("../common/config");
const constants_2 = require("../constants");
const errorCodes_1 = require("../errorCodes");
const helper_1 = require("../helper");
const logger_1 = require("../logger");
const DashenService = require("../services/daShen");
const modelProxy_1 = require("../services/modelProxy");
const PyqAuthService = require("../../service/qnm/pyq/auth");
const logger = (0, logger_1.clazzLogger)("controllers/auth");
const secret = config.JWT_TOKEN_SECRET;
const skeyHeader = jwt.encode("", secret).split(".")[0] + ".";
function login(req, res, next) {
    return (0, helper_1.handleOperation)(PyqAuthService.login)(req, res, next);
}
exports.login = login;
function logout(req, res, next) {
    return (0, helper_1.handleOperation)(PyqAuthService.logOut)(req, res, next);
}
exports.logout = logout;
function assistLoginProcess(roleId, serverId) {
    return __awaiter(this, void 0, void 0, function* () {
        const idType = "assist_role";
        const idVal = roleId;
        const sessKey = idType + ":" + idVal;
        const loginTime = Date.now();
        yield sessionUtil.set(sessKey, { roleid: roleId, server: serverId, time: loginTime });
        yield sessionUtil.setExpire(sessKey, config.ONE_DAY_SECONDS * 60);
        const skey = authUtil.getSkey(loginTime, idType, idVal);
        return { skey: skey };
    });
}
function assistLogin(req, res, next) {
    return req.paramsValidator
        .param("roleid", { type: Number })
        .param("serverid", { type: Number })
        .validate()
        .then(() => {
        return assistLoginProcess(req.params.roleid, req.params.serverid);
    })
        .then((skey) => {
        AuthController.succSend(res, skey);
    })
        .catch((err) => {
        (0, helper_1.errorHandler)(err, req, res, next);
    });
}
exports.assistLogin = assistLogin;
/**
 * 检查skey中的颁发时间是否过期， 问题来源可见 https://cloud.pm.netease.com/v6/issues/86970
 * @param timeInSession
 * @param timeInSkey
 * @returns
 */
function isTimeInSkeyValid(timeInSession, timeInSkey) {
    if (timeInSkey === constants_1.LOGIN_TIME_LONG_TERM_MARK) {
        return true;
    }
    else {
        const timeInSkeyInt = parseInt(timeInSkey, 10);
        const timeInSessionInt = parseInt(timeInSession, 10);
        return Math.abs(timeInSessionInt - timeInSkeyInt) <= constants_1.SKEY_CHECK_VALID_TIME_OFFSET;
    }
}
function decodeSkey(skey) {
    const decoded = jwt.decode(skey, secret, false, "HS256");
    const loginInfo = authUtil.LoginPayload.toObj(decoded);
    return loginInfo;
}
/**
 * validate login by roleid and skey
 * @param {Object} req.
 * @param {Object} req.params
 * @param {Number} req.params.roleid
 * @param {String} req.params.skey
 */
function validateLogin(req) {
    return __awaiter(this, void 0, void 0, function* () {
        const userSkey = req.params.skey || req.cookies[constants_2.L10_SESS_COOKIE.name];
        const skey = skeyHeader + userSkey;
        const roleId = "" + req.params.roleid;
        const routePath = req.route.path;
        let loginInfo = { time: "", idType: "", idVal: "", account: "" };
        let cheatSkeyEnable = false;
        try {
            loginInfo = decodeSkey(skey);
        }
        catch (err) {
            if (userSkey !== constants_2.CHEAT_SKEY) {
                logger.warn({ req, err, skey }, "ParseSkeyInValid");
            }
            if ((config_1.testCfg.skip_skey_check && userSkey === constants_2.CHEAT_SKEY) || config_1.testCfg.skip_skey_check_full) {
                cheatSkeyEnable = true;
                const account = req.params.account || "<EMAIL>";
                loginInfo = { idType: "role", idVal: roleId, time: `${Date.now()}`, account };
            }
            else {
                return bluebird.reject("Invalid_key"); // 1、非法skey
            }
        }
        if (loginInfo.idType === "face_pinch_cloud_account" /* FacePinchCloud */) {
            if (!routePath.startsWith(constants_2.apiPrefix + "/face_pinch/")) {
                throw errorCodes_1.errorCodes.AuthScopeNotMatch;
            }
        }
        if (roleId != loginInfo.idVal) {
            if (!req.skipIdMatchCheck) {
                return bluebird.reject("Id_mismatch"); // 2、id不匹配
            }
        }
        const sessionId = loginInfo.idType + ":" + loginInfo.idVal;
        const sessionData = yield sessionUtil.get(sessionId);
        if (cheatSkeyEnable || req.skipSkeySessionCheck) {
            // do nothing
        }
        else {
            if (!sessionData) {
                return bluebird.reject("No_session"); // 3、session已过期
            }
            const loginTimeInSession = _.toString(sessionData.time); // redis取出来的数据不会保持Int类型， 需要手动转换下
            if (!isTimeInSkeyValid(loginTimeInSession, loginInfo.time)) {
                return bluebird.reject("Key_outdated"); // 4、skey已过期
            }
        }
        // 和老的实现保持兼容
        req._SESS_INFO_ = Object.assign({ id: sessionId }, loginInfo);
        return sessionData;
    });
}
exports.validateLogin = validateLogin;
function isLoginByDs(session) {
    return session.idType === constants_2.DS_WEB_CONFIG.sessionIdType;
}
exports.isLoginByDs = isLoginByDs;
function isLoginByDsForRoleId(roleId) {
    return __awaiter(this, void 0, void 0, function* () {
        const sessionId = constants_2.DS_WEB_CONFIG.sessionIdType + ":" + roleId;
        const sessionData = yield sessionUtil.get(sessionId);
        return !!sessionData;
    });
}
exports.isLoginByDsForRoleId = isLoginByDsForRoleId;
function getLoginSession(req) {
    return req._SESS_INFO_ || { id: 0, idType: "", idVal: "" };
}
exports.getLoginSession = getLoginSession;
function checkAuthMiddleware(req, res, next) {
    const path = req.route.path + "";
    const isRouteSkipAuth = req.route.skipAuth || req.skipSkey;
    if (isRouteSkipAuth || SKIP_AUTH_LIST[path]) {
        next();
    }
    else {
        validateLogin(req)
            .then(function (info) {
            req.params.server = info ? info.server : 0; // 从session中取出服务器ID
            req.session = {
                language: info ? info.language : "cn",
                country: info ? Number(info.country) : 0,
            };
            next();
        })
            .catch(function (err) {
            errorHandlerForLoginCheck(err, req, res);
        });
    }
}
exports.checkAuthMiddleware = checkAuthMiddleware;
/**
 * legacy code not follow error process pattern, use standalone error handler
 * @param err
 * @param req
 * @param res
 */
function errorHandlerForLoginCheck(err, req, res) {
    let failMsg = "auth failed";
    if (err && err.message) {
        failMsg = err.message;
    }
    if (err && err.msg) {
        failMsg = err.msg;
    }
    if (typeof err === "string") {
        failMsg = err;
    }
    const code = err.code || -2;
    const errorResult = { code, msg: "非法请求：" + failMsg };
    logger.warn({ req, res, err, params: req.params }, "CheckLoginAuthFailed");
    res.send(util.response(errorResult));
}
function dashenWebLoginProcess(roleId, serverId) {
    return __awaiter(this, void 0, void 0, function* () {
        const idType = constants_2.DS_WEB_CONFIG.sessionIdType;
        const idVal = roleId;
        const sessKey = idType + ":" + idVal;
        const loginTime = Date.now();
        yield sessionUtil.set(sessKey, { roleid: roleId, server: serverId, time: loginTime });
        yield sessionUtil.setExpire(sessKey, config.ONE_DAY_SECONDS * 60);
        const skey = authUtil.getSkey(loginTime, idType, idVal);
        return { skey: skey };
    });
}
function markRoleStatus(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const params = req.params;
            const schema = {
                roleid: { type: Number },
                status: { type: Number, values: [constants_1.Statues.Normal, constants_1.Statues.Deleted] },
            };
            yield (0, helper_1.checkParams)(params, schema);
            const record = yield modelProxy_1.RoleInfoModel.findOne({ RoleId: params.roleid }, ["RoleId"]);
            let isOk = false;
            let isChanged = false;
            if (record && record.RoleId) {
                const ret = yield modelProxy_1.RoleInfoModel.updateByCondition({ RoleId: params.roleid }, { State: params.status });
                isOk = ret.affectedRows > 0;
                isChanged = ret.changedRows > 0;
            }
            else {
                yield bluebird.reject(errorCodes_1.errorCodes.RoleInfoNotFound);
            }
            res.send({ code: 0, data: { isOk: isOk, isChanged: isChanged } });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.markRoleStatus = markRoleStatus;
function loginByDS(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const schema = {
                accessToken: { type: String },
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            const params = req.params;
            let role = { roleId: 0, server: 0 };
            let mdRoleList = [];
            if (config.testCfg.test_env && params.mockRoleId) {
                role.roleId = params.mockRoleId;
                role.server = (0, util2_1.getServerIdFromRoleId)(role.roleId);
            }
            else {
                const roleList = yield DashenService.getBindRoleList(params.accessToken);
                role = yield DashenService.getActiveBindRole(roleList);
                mdRoleList = DashenService.filterMdRoles(roleList);
            }
            if (role) {
                const data = yield dashenWebLoginProcess(role.roleId, role.server);
                const profile = yield (0, profile_1.getProfileRecord)(role.roleId, {});
                const cookieCfg = {
                    path: constants_2.L10_SESS_COOKIE.path,
                    domain: constants_2.L10_SESS_COOKIE.domain,
                    httpOnly: constants_2.L10_SESS_COOKIE.httpOnly,
                };
                res.setCookie(constants_2.L10_SESS_COOKIE.name, data.skey, cookieCfg);
                res.send({ code: 0, data: profile });
            }
            else {
                if (mdRoleList.length > 0) {
                    yield (0, helper_1.BusinessError)(constants_2.ErrorTypes.DSRoleNotActive, "绑定角色尚未被选中");
                }
                else {
                    yield (0, helper_1.BusinessError)(constants_2.ErrorTypes.DSRoleNotBind, "大神尚未绑定手游角色");
                }
            }
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.loginByDS = loginByDS;
function loginByDSMock(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const schema = {
                accessToken: { type: String },
                roleid: { type: Number, required: false, default: 100100001 },
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            const roleId = req.params.roleid;
            const profile = yield (0, profile_1.getProfileRecord)(roleId, {});
            res.send({ code: 0, data: profile });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.loginByDSMock = loginByDSMock;
function loginByDSDispatch(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        const params = req.params;
        if (config.testCfg.test_env && params.mockRoleId) {
            return loginByDSMock(req, res, next);
        }
        else {
            return loginByDS(req, res, next);
        }
    });
}
exports.loginByDSDispatch = loginByDSDispatch;
function validateAuthForFuxi(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        next();
    });
}
exports.validateAuthForFuxi = validateAuthForFuxi;
function validateSecureApi(req, res, next) {
    const MAX_TIME_DIFF_ALLOW = 300;
    if (config.testCfg.skip_token_check) {
        next();
    }
    else {
        const params = req.params;
        const time = params.time, urs = params.urs, account = params.account, roleId = params.roleid, token = util.hexMd5(time + account + urs + roleId + config.AUTH_TOKEN_SALT);
        if (config_1.testCfg.token_time_diff_check) {
            const serverTs = (0, util2_1.getUnixTimeStamp)();
            if (Math.abs(serverTs - time) > MAX_TIME_DIFF_ALLOW) {
                logger.warn({ time, serverTs, req }, "LoginTokenTimeMisMatch");
                res.send(errorCodes_1.errorCodes.AuthTimeDeltaTooLarge);
            }
        }
        if (params.token != token) {
            logger.warn({ actual: params.token, expect: token, req }, "LoginTokenMismatch");
            res.send({ code: -2, msg: "Token misMatch" });
        }
        else {
            next();
        }
    }
}
exports.validateSecureApi = validateSecureApi;
//# sourceMappingURL=auth.js.map