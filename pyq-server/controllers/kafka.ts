import { Request, Response, Next } from "restify";
import { errorHand<PERSON> } from "../helper";
import { onCreditScoreLog } from "../consumers/onCreditScoreLog";
import { onLoginRoleAdditionalLog } from "../consumers/onLoginRoleAdditionalLog";
import { onPlayerLevelUpLog } from "../consumers/onPlayerLevelUpLog";
import { onBandHeatLog } from "../consumers/onBandHeatLog";

// 信用分日志
export async function creditScore(req: Request, res: Response, next: Next) {
  const creditScoreLog = req.body;
  try {
    const data = await onCreditScoreLog({
      eventName: "CreditScore",
      message: JSON.stringify(creditScoreLog),
      date: new Date().toISOString(),
    });
    res.send({ code: 0, data });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

// 角色登录额外信息日志
export async function loginRoleAdditional(req: Request, res: Response, next: Next) {
  const log = req.body;
  try {
    const data = await onLoginRoleAdditionalLog({
      eventName: "LoginRole_Additional",
      message: JSON.stringify(log),
      date: new Date().toISOString(),
    });
    res.send({ code: 0, data });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

// 玩家升级日志
export async function playerLevelUp(req: Request, res: Response, next: Next) {
  const log = req.body;
  try {
    const data = await onPlayerLevelUpLog({
      eventName: "PlayerLevelUp",
      message: JSON.stringify(log),
      date: new Date().toISOString(),
    });
    res.send({ code: 0, data });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

// 乐团热度日志
export async function musicClubBandHeat(req: Request, res: Response, next: Next) {
  const log = req.body;
  try {
    const data = await onBandHeatLog({
      eventName: "BandHeat",
      message: JSON.stringify(log),
      date: new Date().toISOString(),
    });
    res.send({ code: 0, data });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}
