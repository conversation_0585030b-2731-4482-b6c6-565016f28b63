"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.listEquipComments = exports.addEquipComment = exports.getTreasureRank = exports.getWeaponRank = exports.getFilters = void 0;
/* eslint-disable prefer-promise-reject-errors */
const errorHandler = require('../errorHandler');
const QnmEquipComment = require("../../models/QnmEquipComment");
const hasSensitiveWord = require('../../common/data').qnm.textFilter;
const equipRank_1 = require("../services/equipRank");
const bluebird = require("bluebird");
function getFilters(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        const servers = yield (0, equipRank_1.getServerFilters)();
        res.send({ code: 0, data: { servers: servers, equipPositions: equipRank_1.EquipPositions } });
    });
}
exports.getFilters = getFilters;
function getWeaponRank(req, res, next) {
    return req.paramsValidator
        .param('server_id', { type: Number })
        .param('equip_position', { type: Number })
        .param('page', { type: Number, required: false, default: 1, min: 1 })
        .param('page_size', { type: Number, required: false, default: 10, min: 1, max: 20 })
        .validate()
        .then(() => {
        return (0, equipRank_1.getWeaponRankList)(req.params);
    }).then(result => {
        res.send({ code: 0, data: result });
    }).catch(err => errorHandler(err, req, res, next));
}
exports.getWeaponRank = getWeaponRank;
function getTreasureRank(req, res, next) {
    return req.paramsValidator
        .param('date', { type: String, required: false })
        .param('period', { type: Number, required: false, min: 1, max: 7 })
        .validate()
        .then(() => {
        return (0, equipRank_1.getTreasureRankList)(req.params);
    }).then(result => {
        res.send({ code: 0, data: result });
    }).catch(err => errorHandler(err, req, res, next));
}
exports.getTreasureRank = getTreasureRank;
function checkTextByyFilter(text) {
    return new Promise((resolve, reject) => {
        if (hasSensitiveWord(text)) {
            reject({ errorType: 'SensitiveWord', msg: '包含敏感词汇' });
        }
        else {
            resolve(true);
        }
    });
}
// 检查是否禁止发表兵器谱评论
function checkBanEquipComment(roleid) {
    let profile = require('../../service/qnm/pyq/profile');
    return profile.getBanState(roleid, 'EquipComment')
        .catch(() => {
        return bluebird.reject({ errorType: 'BeBaned', msg: '您已被管理员禁止添加兵器谱评论' });
    });
}
function addEquipComment(req, res, next) {
    return req.paramsValidator
        .param('text', { type: String, min: 2 })
        .param('equipId', { type: String })
        .validate()
        .then(() => {
        let roleId = req.params.roleid;
        return checkBanEquipComment(roleId);
    }).then(() => {
        const text = req.params.text;
        return checkTextByyFilter(text);
    }).then(() => {
        let roleid = req.params.roleid;
        return QnmEquipComment.addComment(roleid, req.params.equipId, req.params.text);
    }).then(() => {
        res.send({ code: 0, data: null });
    }).catch(err => errorHandler(err, req, res, next));
}
exports.addEquipComment = addEquipComment;
function listEquipComments(req, res, next) {
    return req.paramsValidator
        .param('equipId', { type: String })
        .validate()
        .then(() => {
        return QnmEquipComment.listCommentsText(req.params.equipId);
    }).then(texts => {
        res.send({ code: 0, data: { list: texts } });
    })
        .catch(err => errorHandler(err, req, res, next));
}
exports.listEquipComments = listEquipComments;
//# sourceMappingURL=equip.js.map