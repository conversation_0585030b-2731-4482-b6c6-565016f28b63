import * as WishlistService from "../services/wishlist";
import { checkParams, errorHandler } from "../helper";
import { EventBus } from "../eventBus2";
import { EventNames } from "../constants";
import { IWishHelpFinishPayload } from "../types";
import { wishlistReq } from "../type/req";

export async function add(req, res, next) {
  try {
    const schema = {
      roleid: { type: Number },
      wishId: { type: String },
      type: { type: Number },
      text: { type: String, required: false, maxlen: 1000 },
      templateId: { type: Number, required: false },
      num: { type: Number, required: false },
      totalProgress: { type: Number, required: false },
      startTime: { type: Number },
      endTime: { type: Number },
      shareType: { type: Number, required: false },
      shareImgs: { type: Array, required: false, each: { type: String } },
    };
    await checkParams(req.params, schema);
    const ret = await WishlistService.add(req.params, req);
    res.send({ code: 0, data: ret });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function addHelps(req, res, next) {
  try {
    const schema = {
      roleid: { type: Number },
      wishId: { type: String },
      targetid: { type: Number },
      process: { type: Number, required: false },
      text: { type: String, required: false },
    };
    const params: wishlistReq.addHelps = req.params;
    await checkParams(params, schema);
    const ret = await WishlistService.addHelps(req.params);
    const payload: IWishHelpFinishPayload = {
      roleId: params.roleid,
      wishId: params.wishId,
      targetId: params.targetid,
    };
    EventBus.emit(EventNames.WISH_HELP_FINISH, payload);
    res.send({ code: 0, data: ret });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function updateStatus(req, res, next) {
  try {
    const schema = {
      roleid: { type: Number },
      wishId: { type: String },
      status: { type: Number, required: false },
      visibility: { type: Number, required: false },
    };
    await checkParams(req.params, schema);
    const ret = await WishlistService.updateStatus(req.params);
    res.send({ code: 0, data: ret });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function syncWishlist(req, res, next) {
  try {
    const schema = {
      roleid: { type: Number },
      wishlist: { type: Array },
    };
    await checkParams(req.params, schema);
    const ret = await WishlistService.syncWishList(req.params);
    res.send({ code: 0, data: ret });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function listByRole(req, res, next) {
  //查看某人的心愿单
  try {
    const schema = {
      roleid: { type: Number },
      targetid: { type: Number },
      page: { type: Number, default: 1, min: 1 },
      pageSize: { type: Number, default: 1, min: 1, max: 20 },
    };
    await checkParams(req.params, schema);
    const ret = await WishlistService.listByRole(req.params);

    res.send({ code: 0, data: ret });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function list(req, res, next) {
  //查看朋友的心愿单
  try {
    const schema = {
      roleid: { type: Number },
      page: { type: Number, default: 1, min: 1 },
      pageSize: { type: Number, default: 1, min: 1, max: 20 },
    };
    await checkParams(req.params, schema);
    const ret = await WishlistService.list(req.params);
    res.send({ code: 0, data: ret });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function detail(req, res, next) {
  //查看某个心愿单(包括助力)
  try {
    const schema = {
      roleid: { type: Number },
      wishId: { type: String },
    };
    await checkParams(req.params, schema);
    const ret = await WishlistService.detail(req.params);
    res.send({ code: 0, data: ret });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function getHelps(req, res, next) {
  try {
    const schema = {
      roleid: { type: Number },
      wishId: { type: String },
      page: { type: Number, default: 1, min: 1 },
      pageSize: { type: Number, default: 1, min: 1, max: 20 },
    };
    await checkParams(req.params, schema);
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function addHelpText(req, res, next) {
  try {
    const schema = {
      roleid: { type: Number },
      wishId: { type: String },
      text: { type: String, maxlen: 100 },
    };
    await checkParams(req.params, schema);
    const data = await WishlistService.addHelpText(req.params);
    res.send({ code: 0, data: data });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function delHelpText(req, res, next) {
  try {
    const schema = {
      roleid: { type: Number },
      helpId: { type: Number },
    };
    await checkParams(req.params, schema);
    const data = await WishlistService.delHelpTextForWish(req.params);
    res.send({ code: 0, data: data });
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}
