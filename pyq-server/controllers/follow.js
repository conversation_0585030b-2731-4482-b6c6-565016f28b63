"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getFollowList = exports.getFollowers = exports.getFollowing = exports.cancelFollowBatch = exports.cancelFollow = exports.follow = void 0;
const PyqFollow = require("../../models/PyqFollow");
const ProfileService = require("../../service/qnm/pyq/profile");
const _ = require("lodash");
const base_1 = require("../../service/qnm/pyq/events/common/base");
const helper_1 = require("../helper");
const follow_1 = require("../../service/qnm/pyq/follow");
const EventBus = require("../eventBus");
const auth_1 = require("./auth");
const FollowService = require("../services/follow");
const paramsValidator_1 = require("../common/paramsValidator");
const momentLotteryAttendService_1 = require("../services/momentLotteryAttendService");
function follow(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const schema = {
                roleid: { type: Number },
                targetid: { type: Number },
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            const params = req.params;
            const data = yield FollowService.addFollow(params);
            res.send({ code: 0, data: data });
            const session = (0, auth_1.getLoginSession)(req);
            const payload = { roleId: params.roleid, targetId: params.targetid, session: session };
            EventBus.emit(EventBus.Events.ADD_FOLLOW, payload);
            (0, momentLotteryAttendService_1.handlePlayerAttendMomentLotteryForFollow)(params.roleid, params.targetid, 8 /* Follow */, new Date());
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.follow = follow;
function cancelFollow(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const schema = {
                roleid: { type: Number },
                targetid: { type: Number },
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            const params = req.params;
            const data = yield FollowService.cancelFollow(params);
            res.send({ code: 0, data: data });
            const session = (0, auth_1.getLoginSession)(req);
            const payload = { roleId: params.roleid, targetId: params.targetid, session: session };
            EventBus.emit(EventBus.Events.CANCEL_FOLLOW, payload);
            (0, momentLotteryAttendService_1.handlePlayerAttendMomentLotteryForFollow)(params.roleid, params.targetid, -8 /* FollowCancel */, new Date());
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.cancelFollow = cancelFollow;
function cancelFollowBatch(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const schema = {
                roleid: { type: Number },
                targetids: { type: paramsValidator_1.PARAM_TYPES.CSV_ARRAY, maxSize: 20, each: { type: Number } },
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            const params = req.params;
            const data = yield FollowService.cancelFollowBatch(params);
            res.send({ code: 0, data: data });
            const session = (0, auth_1.getLoginSession)(req);
            const payload = { roleId: params.roleid, targetIds: params.targetids, session: session };
            EventBus.emit(EventBus.Events.CANCEL_FOLLOW_BATCH, payload);
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.cancelFollowBatch = cancelFollowBatch;
function getFollowing(req, res, next) {
    let curRoleId;
    return paramsValidator_1.ParamsValidator.from(req.params)
        .param("roleid")
        .validate()
        .then(function () {
        curRoleId = req.params.roleid;
        return (0, follow_1.getFollowIds)(curRoleId);
    })
        .then(function (followingIds) {
        return ProfileService.getRoleInfo(followingIds, curRoleId);
    })
        .then(function (following) {
        const followingIds = _.map(following, "RoleId");
        return [following, PyqFollow.filterFollowingMe(followingIds, curRoleId)];
    })
        .then(function (info) {
        const followingMeIds = info[0];
        const following = info[1];
        return _.map(following, (r) => {
            const followEach = _.includes(followingMeIds, r.RoleId);
            //和 "/qnm/get_events" 接口输出保持兼容
            return {
                RoleId: r.RoleId,
                RoleName: r.RoleName,
                FollowingEach: followEach,
                type: base_1.EventTypes.Following,
                roleInfo: r,
            };
        });
    })
        .then(function (players) {
        players = (0, helper_1.formatResStyle)(players);
        res.send({ code: 0, data: players });
    })
        .catch(function (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    });
}
exports.getFollowing = getFollowing;
function getFollowers(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const curRoleId = req.params.roleid;
            const followingIds = yield PyqFollow.getFollowerIds(curRoleId);
            const following = yield ProfileService.getRoleInfo(followingIds, curRoleId);
            const meFollowIds = yield PyqFollow.filterMeFollow(followingIds, curRoleId);
            let players = _.map(following, (r) => {
                const followEach = _.includes(meFollowIds, r.RoleId);
                //和 "/qnm/get_events" 接口输出保持兼容
                return {
                    RoleId: r.RoleId,
                    RoleName: r.RoleName,
                    FollowingEach: followEach,
                    type: base_1.EventTypes.Following,
                    roleInfo: r,
                };
            });
            players = (0, helper_1.formatResStyle)(players);
            res.send({ code: 0, data: players });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.getFollowers = getFollowers;
function getFollowList(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        const schema = {
            roleid: { type: Number },
            page: { type: Number, default: 1, min: 1 },
            pageSize: { type: Number, default: 10, min: 1, max: 20 },
        };
        try {
            yield (0, helper_1.checkParams)(req.params, schema);
            const params = req.params;
            const data = yield FollowService.getFollowListPage(params);
            res.send({ code: 0, data: (0, helper_1.formatResStyle)(data) });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.getFollowList = getFollowList;
//# sourceMappingURL=follow.js.map