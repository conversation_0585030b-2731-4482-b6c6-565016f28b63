"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.remove = exports.update = exports.getByIds = exports.list = void 0;
const util_1 = require("../../common/util");
const paramsValidator_1 = require("../common/paramsValidator");
const helper_1 = require("../helper");
const FireworkPhotoService = require("../services/fireworkPhoto");
function list(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            let schema = {
                roleid: { type: Number },
                targetid: { type: Number },
            };
            let params = req.params;
            yield (0, helper_1.checkParams)(params, schema);
            let ret = yield FireworkPhotoService.list(params);
            res.send({ code: 0, data: ret });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.list = list;
function getByIds(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            let schema = {
                roleid: { type: Number },
                ids: { type: paramsValidator_1.PARAM_TYPES.CSV_ARRAY, maxSize: 4, each: { type: Number } },
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            let params = req.params;
            let ret = yield FireworkPhotoService.getByIds(params);
            res.send({ code: 0, data: ret });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.getByIds = getByIds;
function update(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            let schema = {
                roleid: { type: Number },
                url: { type: String },
                index: { type: Number, min: 0, max: 9 },
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            let params = req.params;
            let ret = yield FireworkPhotoService.update(params);
            let ip = (0, util_1.getIp)(req);
            FireworkPhotoService.auditFireWorkPhoto(params.roleid, [params.url], ret.id, ip);
            res.send({ code: 0, data: ret });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.update = update;
function remove(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            let schema = {
                roleid: { type: Number },
                index: { type: Number, min: 0, max: 9 },
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            let params = req.params;
            let ret = yield FireworkPhotoService.remove(params);
            res.send({ code: 0, data: ret });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.remove = remove;
//# sourceMappingURL=fireworkPhoto.js.map