"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.musicClubBandHeat = exports.playerLevelUp = exports.loginRoleAdditional = exports.creditScore = void 0;
const helper_1 = require("../helper");
const onCreditScoreLog_1 = require("../consumers/onCreditScoreLog");
const onLoginRoleAdditionalLog_1 = require("../consumers/onLoginRoleAdditionalLog");
const onPlayerLevelUpLog_1 = require("../consumers/onPlayerLevelUpLog");
const onBandHeatLog_1 = require("../consumers/onBandHeatLog");
// 信用分日志
function creditScore(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        const creditScoreLog = req.body;
        try {
            const data = yield (0, onCreditScoreLog_1.onCreditScoreLog)({
                eventName: "CreditScore",
                message: JSON.stringify(creditScoreLog),
                date: new Date().toISOString(),
            });
            res.send({ code: 0, data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.creditScore = creditScore;
// 角色登录额外信息日志
function loginRoleAdditional(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        const log = req.body;
        try {
            const data = yield (0, onLoginRoleAdditionalLog_1.onLoginRoleAdditionalLog)({
                eventName: "LoginRole_Additional",
                message: JSON.stringify(log),
                date: new Date().toISOString(),
            });
            res.send({ code: 0, data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.loginRoleAdditional = loginRoleAdditional;
// 玩家升级日志
function playerLevelUp(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        const log = req.body;
        try {
            const data = yield (0, onPlayerLevelUpLog_1.onPlayerLevelUpLog)({
                eventName: "PlayerLevelUp",
                message: JSON.stringify(log),
                date: new Date().toISOString(),
            });
            res.send({ code: 0, data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.playerLevelUp = playerLevelUp;
// 乐团热度日志
function musicClubBandHeat(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        const log = req.body;
        try {
            const data = yield (0, onBandHeatLog_1.onBandHeatLog)({
                eventName: "BandHeat",
                message: JSON.stringify(log),
                date: new Date().toISOString(),
            });
            res.send({ code: 0, data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.musicClubBandHeat = musicClubBandHeat;
//# sourceMappingURL=kafka.js.map