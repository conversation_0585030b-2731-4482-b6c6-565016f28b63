"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.markAllInformUnread = exports.momentSearchHandler = exports.postReceiveLimit = exports.drawMomentLottery = exports.attendMomentLottery = exports.updateHotMomentsCache = exports.uploadJobAvatar = exports.quanMinPKSetRank = void 0;
const helper_1 = require("../helper");
const bluebird = require("bluebird");
const nosSdkClient_1 = require("../../common/nosSdkClient");
const qmpk_1 = require("../components/activity/qmpk");
const HotMomentsCache_1 = require("../../service/qnm/pyq/HotMomentsCache");
const ajvCheck_1 = require("../common/ajvCheck");
const momentLotteryType_1 = require("../types/momentLotteryType");
const models_1 = require("../models");
const errorCodes_1 = require("../errorCodes");
const momentLotteryAttendService_1 = require("../services/momentLotteryAttendService");
const config_1 = require("../common/config");
const drawMomentLotteryService_1 = require("../services/gms_services/drawMomentLotteryService");
const momentLotteryService = require("../services/momentLotteryService");
const util_1 = require("../../common/util");
const inform_1 = require("../services/inform");
const logger_1 = require("../logger");
const logger = (0, logger_1.clazzLogger)("controllers/gmCmd");
exports.quanMinPKSetRank = qmpk_1.setRank;
function uploadJobAvatar(req, res, next) {
    var _a, _b;
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const schema = {
                gender: { type: String, values: ["male", "female"] },
                clazz: { type: Number },
            };
            yield (0, helper_1.checkParams)(req.params, schema);
            const params = req.params;
            logger.debug({ files: req.files }, "uploadJobAvatar");
            const avatarPath = ((_b = (_a = req.files) === null || _a === void 0 ? void 0 : _a.avatar) === null || _b === void 0 ? void 0 : _b.path) || "";
            if (!avatarPath) {
                yield bluebird.reject({ message: "图片未上传" });
            }
            const key = `avatars/job_${params.clazz}_${params.gender}.jpg`;
            const bucketName = "hi-163-qnm";
            yield nosSdkClient_1.nosClient.put_file_async({
                bucket: bucketName,
                key: key,
                filepath: avatarPath,
            });
            const url = `https://${bucketName}.nosdn.127.net/` + key + "?imageView";
            res.send({ code: 0, data: { url } });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.uploadJobAvatar = uploadJobAvatar;
function updateHotMomentsCache(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const reqBodySchema = {
                properties: {
                    serverIds: { type: "array", items: { type: "string" } },
                    tagIds: { type: "array", items: { type: "integer" } },
                },
                required: ["serverIds", "tagIds"],
            };
            yield (0, ajvCheck_1.checkParamsByAjv)(req.body, reqBodySchema);
            const ret = yield (0, HotMomentsCache_1.refreshByServerIdsAndTagIds)(req.body.serverIds, req.body.tagIds);
            return res.send({ code: 0, data: ret });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.updateHotMomentsCache = updateHotMomentsCache;
function attendMomentLottery(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const reqBodySchema = {
                type: "object",
                properties: {
                    momentId: { type: "integer" },
                    roleid: { type: "integer" },
                    action: { type: "string", enum: ["like", "comment", "forward", "follow"] },
                },
                required: ["momentId", "roleid", "action"],
            };
            const params = req.params;
            yield (0, ajvCheck_1.checkParamsByAjv)(req.params, reqBodySchema);
            const moment = yield models_1.MomentModel.findOne({ ID: params.momentId }, ["ID", "RoleId"]);
            if (!moment) {
                yield (0, errorCodes_1.BussError)(errorCodes_1.errorCodes.MomentNotFound);
            }
            const notes = `gm_fake_${params.action}`;
            const actionScore = momentLotteryType_1.ActionScoreMap.get(params.action);
            const data = yield (0, momentLotteryAttendService_1.handlePlayerAttendMomentLottery)(params.roleid, params.momentId, moment.RoleId, actionScore, notes, new Date());
            return res.send({ code: 0, data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.attendMomentLottery = attendMomentLottery;
function drawMomentLottery(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const reqBodySchema = {
                type: "object",
                properties: {
                    momentId: { type: "integer" },
                },
                required: ["momentId"],
            };
            const params = req.params;
            yield (0, ajvCheck_1.checkParamsByAjv)(req.params, reqBodySchema);
            const data = yield momentLotteryService.drawMomentLottery(params.momentId);
            res.send({ code: 0, data });
        }
        catch (err) {
            logger.warn({ err, req }, "DrawMomentLotteryApiFail");
            res.send({ status: "Fail", message: err.message });
        }
    });
}
exports.drawMomentLottery = drawMomentLottery;
function postReceiveLimit(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const cmdRes = yield drawMomentLotteryService_1.gmsDrawMomentLotteryService.drawMomentLotteryRaw(config_1.momentLotteryCfg.gmsPostReceiveLimitApiUrl, 0, req.body);
            res.send(cmdRes);
        }
        catch (err) {
            if (err instanceof Error) {
                logger.error({ err, req }, "RequestPostReceiveLimitApiFail");
            }
            else {
                logger.warn({ err, req }, "RequestPostReceiveLimitApiBizErr");
            }
            res.send({ status: "Fail", message: err.message });
        }
    });
}
exports.postReceiveLimit = postReceiveLimit;
function momentSearchHandler(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        const params = req.params;
        try {
            const schemas = {
                type: "object",
                properties: {
                    roleId: { type: "number" },
                    kw: { type: "string" },
                    size: { type: "number", minimum: 1, maximum: 100, default: 10 },
                },
                required: ["roleId"],
            };
            (0, ajvCheck_1.checkParamsByAjv)(params, schemas);
            let query = models_1.MomentModel.scope().where({ RoleId: params.roleId }).orderBy("createTime", "desc").limit(params.size);
            if (params.kw) {
                query = query.where("Text", "like", `%${params.kw}%`);
            }
            const moments = (yield models_1.MomentModel.executeByQuery(query));
            const list = moments.map((m) => ({
                id: m.ID,
                text: m.Text,
                imgList: (0, util_1.csvStrToArray)(m.ImgList),
                videoList: (0, util_1.csvStrToArray)(m.VideoList),
                createTime: (0, util_1.formatDate)(m.CreateTime),
            }));
            res.send({ code: 0, data: { list } });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.momentSearchHandler = momentSearchHandler;
function markAllInformUnread(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const params = req.params;
            const reqBodySchema = {
                type: "object",
                properties: {
                    roleid: { type: "number" },
                },
                required: ["roleid"],
            };
            yield (0, ajvCheck_1.checkParamsByAjv)(req.params, reqBodySchema);
            const ret = yield inform_1.InformService.markAllInformUnread(params.roleid);
            logger.info({ roleId: params.roleid, markedCount: ret.affectedRows }, "MarkAllInformUnreadOK");
            const data = { markedCount: ret.affectedRows };
            res.send({ code: 0, data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.markAllInformUnread = markAllInformUnread;
//# sourceMappingURL=gmCmd.js.map