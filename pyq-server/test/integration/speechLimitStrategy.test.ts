import * as assert from "power-assert";
import { speechLimitChecker } from "../../middlewares/speechLimit";
import { LoginSession } from "../../../common/auth";

describe("Speech Limit Strategy Integration Tests", () => {
  let mockReq: any;
  let mockRes: any;
  let nextCalled: boolean;
  let mockNext: Function;
  let responseSent: any;

  beforeEach(() => {
    nextCalled = false;
    responseSent = null;
    
    mockNext = () => {
      nextCalled = true;
    };
    
    mockReq = {
      route: { path: "" },
      params: { roleid: 12345 }
    };
    
    mockRes = {
      send: (response: any) => {
        responseSent = response;
      }
    };
  });

  afterEach(async () => {
    // Clean up session
    try {
      await LoginSession.del(12345);
    } catch (err) {
      // Ignore cleanup errors
    }
    nextCalled = false;
    responseSent = null;
  });

  describe("Special period integration", () => {
    it("should integrate middleware and plugin for strategy-based blocking", async () => {
      // Set up session with BLOCK_SPEECH strategy
      await LoginSession.set(12345, {
        time: Date.now().toString(),
        account: "testaccount",
        isOverSea: "0",
        server: 1,
        urs: "testurs",
        speechLimitStrategy: "1" // BLOCK_SPEECH
      });

      // Create middleware with a special period date (you may need to adjust this based on your config)
      const specialPeriodDate = new Date("2025-06-01"); // This should be within your special period
      const checker = speechLimitChecker(specialPeriodDate);
      
      // Test speech URL (should be blocked)
      mockReq.route.path = "/qnm/addcomment";
      await checker(mockReq, mockRes, mockNext);
      
      // For speech URLs with BLOCK_SPEECH strategy, should be blocked
      // Note: This test depends on the isIn64SpecialPeriod function and your config
      // If the date is not in special period, it will call next regardless of strategy
    });

    it("should allow operations when not in special period regardless of strategy", async () => {
      // Set up session with BLOCK_ALL strategy
      await LoginSession.set(12345, {
        time: Date.now().toString(),
        account: "testaccount",
        isOverSea: "0",
        server: 1,
        urs: "testurs",
        speechLimitStrategy: "3" // BLOCK_ALL
      });

      // Create middleware with a non-special period date
      const normalDate = new Date("2023-01-01"); // This should be outside special period
      const checker = speechLimitChecker(normalDate);
      
      // Test speech URL (should be allowed despite BLOCK_ALL strategy)
      mockReq.route.path = "/qnm/addcomment";
      await checker(mockReq, mockRes, mockNext);
      
      // Should call next() regardless of strategy when not in special period
      assert(nextCalled, "Should call next() when not in special period");
      assert(!responseSent, "Should not send response when not in special period");
    });
  });

  describe("Strategy combinations", () => {
    it("should handle complex URL and strategy interactions", async () => {
      await LoginSession.set(12345, {
        time: Date.now().toString(),
        account: "testaccount",
        isOverSea: "0",
        server: 1,
        urs: "testurs",
        speechLimitStrategy: "2" // BLOCK_IMAGE only
      });

      // Test addmoment URL which is both a speech URL and can contain images
      const specialPeriodDate = new Date("2025-06-01");
      const checker = speechLimitChecker(specialPeriodDate);
      
      // First test: addmoment without images (speech should be allowed with BLOCK_IMAGE strategy)
      mockReq.route.path = "/qnm/addmoment";
      mockReq.params.imglist = JSON.stringify([]);
      mockReq.params.videolist = JSON.stringify([]);
      
      await checker(mockReq, mockRes, mockNext);
      
      // Reset for second test
      nextCalled = false;
      responseSent = null;
      
      // Second test: addmoment with images (should be blocked)
      mockReq.params.imglist = JSON.stringify(["image1.jpg"]);
      
      await checker(mockReq, mockRes, mockNext);
    });
  });

  describe("Default behavior testing", () => {
    it("should use default strategy when session has no speechLimitStrategy", async () => {
      await LoginSession.set(12345, {
        time: Date.now().toString(),
        account: "testaccount",
        isOverSea: "0",
        server: 1,
        urs: "testurs"
        // No speechLimitStrategy field
      });

      const specialPeriodDate = new Date("2025-06-01");
      const checker = speechLimitChecker(specialPeriodDate);
      
      mockReq.route.path = "/qnm/addcomment";
      await checker(mockReq, mockRes, mockNext);
      
      // Default strategy should be BLOCK_ALL (3), so speech should be blocked
      // Note: This behavior depends on the isIn64SpecialPeriod configuration
    });

    it("should handle session lookup failures gracefully", async () => {
      // Don't set up any session for this roleId
      const specialPeriodDate = new Date("2025-06-01");
      const checker = speechLimitChecker(specialPeriodDate);
      
      mockReq.route.path = "/qnm/addcomment";
      await checker(mockReq, mockRes, mockNext);
      
      // Should handle gracefully - either call next or send relogin error
      // This depends on how the error is handled in your implementation
    });
  });

  describe("URL pattern testing", () => {
    beforeEach(async () => {
      await LoginSession.set(12345, {
        time: Date.now().toString(),
        account: "testaccount",
        isOverSea: "0",
        server: 1,
        urs: "testurs",
        speechLimitStrategy: "0" // No restrictions
      });
    });

    it("should handle various URL patterns correctly", async () => {
      const specialPeriodDate = new Date("2025-06-01");
      const checker = speechLimitChecker(specialPeriodDate);
      
      // Test non-restricted URL (should always pass)
      mockReq.route.path = "/qnm/getmoment";
      await checker(mockReq, mockRes, mockNext);
      
      assert(nextCalled, "Should call next() for non-restricted URLs");
      assert(!responseSent, "Should not send response for non-restricted URLs");
      
      // Reset
      nextCalled = false;
      responseSent = null;
      
      // Test always-blocked URL (should always be blocked regardless of strategy)
      mockReq.route.path = "/qnm/setphoto";
      await checker(mockReq, mockRes, mockNext);
      
      assert(!nextCalled, "Should not call next() for always-blocked URLs");
      assert(responseSent, "Should send response for always-blocked URLs");
      assert.equal(responseSent.code, -1);
    });
  });
});