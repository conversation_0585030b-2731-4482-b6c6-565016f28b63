import * as assert from "power-assert";
import { speechLimitPlugin } from "../../services/speechLimitPlugin";
import { LoginSession } from "../../../common/auth";

describe("speechLimitPlugin", () => {
  let mockReq: any;
  let mockRes: any;
  let nextCalled: boolean;
  let mockNext: Function;
  let responseSent: any;

  beforeEach(async () => {
    nextCalled = false;
    responseSent = null;
    
    mockNext = () => {
      nextCalled = true;
    };
    
    mockReq = {
      route: { path: "" },
      params: { roleid: 12345 }
    };
    
    mockRes = {
      send: (response: any) => {
        responseSent = response;
      }
    };
  });

  afterEach(async () => {
    // Clean up session
    try {
      await LoginSession.del(12345);
    } catch (err) {
      // Ignore cleanup errors
    }
    nextCalled = false;
    responseSent = null;
  });

  describe("Speech Strategy Tests", () => {
    it("should allow all operations when strategy is NONE (0)", async () => {
      await LoginSession.set(12345, {
        time: Date.now().toString(),
        account: "testaccount",
        isOverSea: "0",
        server: 1,
        urs: "testurs",
        speechLimitStrategy: "0"
      });

      mockReq.route.path = "/qnm/addcomment";
      await speechLimitPlugin(mockReq, mockRes, mockNext);
      
      assert(nextCalled, "Should call next() for strategy 0");
      assert(!responseSent, "Should not send response for strategy 0");
    });

    it("should block speech when strategy is BLOCK_SPEECH (1)", async () => {
      await LoginSession.set(12345, {
        time: Date.now().toString(),
        account: "testaccount",
        isOverSea: "0",
        server: 1,
        urs: "testurs",
        speechLimitStrategy: "1"
      });

      mockReq.route.path = "/qnm/addcomment";
      await speechLimitPlugin(mockReq, mockRes, mockNext);
      
      assert(!nextCalled, "Should not call next() for speech block");
      assert(responseSent, "Should send response for speech block");
      assert.equal(responseSent.code, -1);
      assert.equal(responseSent.msg, "因技术升级，该功能暂不可用，对您造成不便深感歉意。");
    });

    it("should allow speech but block images when strategy is BLOCK_IMAGE (2)", async () => {
      await LoginSession.set(12345, {
        time: Date.now().toString(),
        account: "testaccount",
        isOverSea: "0",
        server: 1,
        urs: "testurs",
        speechLimitStrategy: "2"
      });

      // Test speech (should pass)
      mockReq.route.path = "/qnm/addcomment";
      await speechLimitPlugin(mockReq, mockRes, mockNext);
      
      assert(nextCalled, "Should call next() for speech with image-only block");
      assert(!responseSent, "Should not send response for speech with image-only block");

      // Reset for image test
      nextCalled = false;
      responseSent = null;

      // Test image (should be blocked)
      mockReq.route.path = "/qnm/addmoment";
      mockReq.params.imglist = JSON.stringify(["image1.jpg"]);
      await speechLimitPlugin(mockReq, mockRes, mockNext);
      
      assert(!nextCalled, "Should not call next() for image block");
      assert(responseSent, "Should send response for image block");
      assert.equal(responseSent.code, -1);
      assert.equal(responseSent.msg, "图片服务器升级，暂不支持发表图片动态");
    });

    it("should block all operations when strategy is BLOCK_ALL (3)", async () => {
      await LoginSession.set(12345, {
        time: Date.now().toString(),
        account: "testaccount",
        isOverSea: "0",
        server: 1,
        urs: "testurs",
        speechLimitStrategy: "3"
      });

      // Test speech (should be blocked)
      mockReq.route.path = "/qnm/addcomment";
      await speechLimitPlugin(mockReq, mockRes, mockNext);
      
      assert(!nextCalled, "Should not call next() for speech with block all");
      assert(responseSent, "Should send response for speech with block all");
      assert.equal(responseSent.code, -1);

      // Reset for image test
      nextCalled = false;
      responseSent = null;

      // Test image (should be blocked - but addmoment is also a speech URL so it gets blocked for speech first)
      mockReq.route.path = "/qnm/addmoment";
      mockReq.params.imglist = JSON.stringify(["image1.jpg"]);
      await speechLimitPlugin(mockReq, mockRes, mockNext);
      
      assert(!nextCalled, "Should not call next() for image with block all");
      assert(responseSent, "Should send response for image with block all");
      assert.equal(responseSent.code, -1);
      // Note: addmoment is in SPEECH_URLS, so it gets blocked for speech first
      assert.equal(responseSent.msg, "因技术升级，该功能暂不可用，对您造成不便深感歉意。");
    });

    it("should use default strategy (BLOCK_ALL) when no strategy is set", async () => {
      await LoginSession.set(12345, {
        time: Date.now().toString(),
        account: "testaccount",
        isOverSea: "0",
        server: 1,
        urs: "testurs"
        // No speechLimitStrategy
      });

      mockReq.route.path = "/qnm/addcomment";
      await speechLimitPlugin(mockReq, mockRes, mockNext);
      
      assert(!nextCalled, "Should not call next() for default strategy");
      assert(responseSent, "Should send response for default strategy");
      assert.equal(responseSent.code, -1);
    });

    it("should handle video blocking correctly", async () => {
      await LoginSession.set(12345, {
        time: Date.now().toString(),
        account: "testaccount",
        isOverSea: "0",
        server: 1,
        urs: "testurs",
        speechLimitStrategy: "2" // BLOCK_IMAGE
      });

      mockReq.route.path = "/qnm/addmoment";
      mockReq.params.videolist = JSON.stringify(["video1.mp4"]);
      await speechLimitPlugin(mockReq, mockRes, mockNext);
      
      assert(!nextCalled, "Should not call next() for video block");
      assert(responseSent, "Should send response for video block");
      assert.equal(responseSent.code, -1);
      assert.equal(responseSent.msg, "视频服务器升级，暂不支持发表视频动态");
    });
  });

  describe("URL Handling", () => {
    beforeEach(async () => {
      await LoginSession.set(12345, {
        time: Date.now().toString(),
        account: "testaccount",
        isOverSea: "0",
        server: 1,
        urs: "testurs",
        speechLimitStrategy: "0" // No restrictions
      });
    });

    it("should always block CHECK_URLS regardless of strategy", async () => {
      mockReq.route.path = "/qnm/setphoto";
      await speechLimitPlugin(mockReq, mockRes, mockNext);
      
      assert(!nextCalled, "Should not call next() for always blocked URLs");
      assert(responseSent, "Should send response for always blocked URLs");
      assert.equal(responseSent.code, -1);
      assert.equal(responseSent.msg, "因技术升级，该功能暂不可用，对您造成不便深感歉意。");
    });

    it("should allow non-restricted URLs", async () => {
      mockReq.route.path = "/qnm/getmoment";
      await speechLimitPlugin(mockReq, mockRes, mockNext);
      
      assert(nextCalled, "Should call next() for non-restricted URLs");
      assert(!responseSent, "Should not send response for non-restricted URLs");
    });

    it("should handle missing roleid gracefully", async () => {
      mockReq.params = {}; // No roleid
      mockReq.route.path = "/qnm/addcomment";
      await speechLimitPlugin(mockReq, mockRes, mockNext);
      
      assert(nextCalled, "Should call next() when no roleid");
      assert(!responseSent, "Should not send response when no roleid");
    });
  });

  describe("Error Handling", () => {
    it("should handle session lookup failure", async () => {
      // Don't set up session, so lookup will fail
      mockReq.route.path = "/qnm/addcomment";
      await speechLimitPlugin(mockReq, mockRes, mockNext);
      
      // The getSpeechLimitStrategy function catches errors and returns BLOCK_ALL (3)
      // So it should block with the default strategy
      assert(!nextCalled, "Should not call next() on session failure");
      assert(responseSent, "Should send response on session failure");
      assert.equal(responseSent.code, -1);
      assert.equal(responseSent.msg, "因技术升级，该功能暂不可用，对您造成不便深感歉意。");
    });
  });
});