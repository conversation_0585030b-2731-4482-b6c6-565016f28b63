import type { Request, RequestHandler } from "restify";
import { components, operations } from "./swagger";
import { EventNames } from "../constants";
import { MomentRecord } from "../models";
import { Context } from "../context";

export interface MountOption {
  skipAuth?: boolean;
  skipOpenIdAuth?: boolean;
  /** 只开启skey校验, 不使用session相关检查 */
  noSessionCheck?: boolean;
  useAjvCheck?: boolean;
}


export type Operation<T = any, F = any> = (params: T, request?: Request) => Promise<F>;


export type OperationV2<T = any, F = any> = (ctx: Context, params: T) => Promise<F>;

export type ComponentVersion = "v1" | "v2" | string

export interface Path {
  method: "get" | "post";
  url: string;
  paramsSchema?: object;
  /** request handlers before operation after paramsValidator*/
  before?: RequestHandler | RequestHandler[];
  operation?: Operation | OperationV2;
  /** request handlers after operation*/
  after?: RequestHandler | RequestHandler[];
  pre?: RequestHandler;
  /** when use handlers it will override all other handler*/
  handlers?: RequestHandler[];
  handler?: RequestHandler;
  /** 每个路由单独的配置 */
  option?: MountOption;
}

export interface Component {
  paths: Path[];
  prefix: string;
  option?: MountOption;
  handlers?: Record<string, RequestHandler>;
  operation?: Operation | OperationV2;
  // v2 版本加入context支持
  version?: "v1" | "v2"
}

export type CardInfo = components["schemas"]["CardInfo"];

export const enum ECommentType {
  Comment = 0,
  Reply = 1,
}

export const enum ENotificationStatus {
  UnRead = 0,
  Read = 1,

  Delete = -1,
}

export const enum ENotificationType {
  NotionBeLiked = 1,
  NotionBeComment = 2,
  CommentBeReplied = 3,
}

export const enum EHasNewStatus {
  NoNew = 0,
  HasNew = 1,
}

export type ActivityLoginValidateTokenReqParams = operations["activityLoginValidateAccessToken"]["parameters"]["query"];

export const enum EMessageStatus {
  DELETED = -1,
  NORMAL = 0,
  READ = 1,
}

export type ExpressionBase = components["schemas"]["ExpressionBase"];

export type ExpressionBaseCol = keyof ExpressionBase;

export interface EventPayload {
  /** 当前用户id */
  roleId: number;
  /** 事件发生的时间戳 */
  ts?: number;
}

export type EventHandler = (payload: EventPayload) => Promise<unknown>;

export type EventHandleGroup = { name: EventNames; handler: EventHandler }[];

export interface AuthLoginParams {
  account: string;
  urs: string;
  roleid: number;
  time: string;
  token: string;
  server: number;
  level: number;
  isOverSea?: string;
  vip?: number;
  accountId?: string;
  language?: string;
  country?: number;
  speechLimitStrategy?: string;
}

export type AuthLogoutParams = AuthLoginParams;

export interface LoginSessionData {
  time: string;
  account: string;
  isOverSea: string;
  server: number;
  urs: string;
  language?: string;
  country?: number;
  speechLimitStrategy?: string;
}

export const enum AuthLoginIdType {
  Role = "role",
  FacePinchCloud = "face_pinch_cloud_account",
}

export interface LoginJwtPayload {
  time: string;
  idType: string;
  idVal: string;
  account: string;
}

export type LoginJwtPayloadTuple = [
  LoginJwtPayload["time"],
  LoginJwtPayload["idType"],
  LoginJwtPayload["idVal"],
  LoginJwtPayload["account"]
];

export type FacePinchWorkUploadUsage = components["schemas"]["FacePinchWorkUploadUsage"];

export type FacePinchWorkCollectUsage = components["schemas"]["FacePinchWorkCollectUsage"];

export interface GdcSyncResp {
  isOk: boolean;
}

export type Lang = "cn" | "tw" | "en" | "vn" | "th" | "ina";

export type PreviousForwardShowMoment = Pick<MomentRecord, "ID" | "Text" | "RoleId">;

export type ApiResponse<T> = {
  code: number;
  data: T;
  msg: string;
};

/**
 * 通知状态
 */
export const enum EInformStatus {
  UnRead = 0,
  Read = 1,
  Delete = -1,
}

export type schemas = components["schemas"];