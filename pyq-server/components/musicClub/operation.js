"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.musicClubRecordingAuditCallback = exports.musicClubRecordingShow = exports.musicClubServerRecordingShow = exports.musicClubRecordingRate = exports.musicClubRecordingRemove = exports.musicClubRecordingRelease = exports.musicClubRadioRequestPlay = exports.getMusicClubRecordingList = exports.getMusicClubRankWeekHotListForServer = exports.getMusicClubRankWeekHotList = exports.getMusicClubRankHotListForServer = exports.getMusicClubRankHotList = exports.musicClubShow = exports.musicClubUpdate = exports.musicClubDisband = exports.musicClubCreate = void 0;
const config_1 = require("../../common/config");
const errorCodes_1 = require("../../errorCodes");
const logger_1 = require("../../logger");
const musicClubRecordingService_1 = require("../../services/music-club/musicClubRecordingService");
const musicClubService_1 = require("../../services/music-club/musicClubService");
const musicClubRankingCache_1 = require("../../services/music-club/musicClubRankingCache");
const musicClubRecordingAuditTaskService_1 = require("../../services/music-club/musicClubRecordingAuditTaskService");
const dateUtil_1 = require("../../../common/dateUtil");
const logger = (0, logger_1.clazzLogger)("musicClubOperation");
function musicClubCreate(ctx, params) {
    return __awaiter(this, void 0, void 0, function* () {
        const data = yield musicClubService_1.MusicClubService.getInstance().createMusicClub(ctx, params);
        return data;
    });
}
exports.musicClubCreate = musicClubCreate;
function musicClubDisband(ctx, params) {
    return __awaiter(this, void 0, void 0, function* () {
        const data = yield musicClubService_1.MusicClubService.getInstance().disbandMusicClub(ctx, params);
        return data;
    });
}
exports.musicClubDisband = musicClubDisband;
function musicClubUpdate(ctx, params) {
    return __awaiter(this, void 0, void 0, function* () {
        const data = yield musicClubService_1.MusicClubService.getInstance().updateMusicClub(ctx, params);
        return data;
    });
}
exports.musicClubUpdate = musicClubUpdate;
function musicClubShow(ctx, params) {
    return __awaiter(this, void 0, void 0, function* () {
        const data = yield musicClubService_1.MusicClubService.getInstance().getMusicClubInfo(ctx, params);
        return data;
    });
}
exports.musicClubShow = musicClubShow;
/** 总热度榜：展示50名。总热度从高到低排序。 */
function getMusicClubRankHotList(ctx, params) {
    return __awaiter(this, void 0, void 0, function* () {
        const musicClubService = musicClubService_1.MusicClubService.getInstance();
        const rankingCache = musicClubRankingCache_1.MusicClubRankingCache.getInstance();
        // If refresh=true, invalidate cache first to get real-time data
        if (params._refresh) {
            yield rankingCache.invalidateHotRankCache(ctx);
        }
        const rankList = yield rankingCache.get(ctx, {
            type: 'hot',
            limit: config_1.musicClubCfg.allHotRankListSize,
        });
        const showRankList = musicClubService.convertToShowRankItemTiny(rankList);
        const data = {
            list: showRankList,
            refreshTime: Date.now(),
        };
        return data;
    });
}
exports.getMusicClubRankHotList = getMusicClubRankHotList;
/** 总热度榜：展示10名。总热度从高到低排序。 */
function getMusicClubRankHotListForServer(ctx, params) {
    return __awaiter(this, void 0, void 0, function* () {
        const musicClubService = musicClubService_1.MusicClubService.getInstance();
        const rankingCache = musicClubRankingCache_1.MusicClubRankingCache.getInstance();
        // If _refresh=true, invalidate cache first to get real-time data
        if (params._refresh) {
            yield rankingCache.invalidateHotRankCache(ctx);
        }
        let rankList = yield rankingCache.get(ctx, {
            type: 'hot',
            limit: config_1.musicClubCfg.allHotRankListSize,
        });
        rankList = rankList.slice(0, config_1.musicClubCfg.allHotRankListSizeForServer);
        const showRankList = musicClubService.convertToShowRankItemTiny(rankList);
        const data = {
            list: showRankList,
            refreshTime: Date.now(),
        };
        return data;
    });
}
exports.getMusicClubRankHotListForServer = getMusicClubRankHotListForServer;
/** 新晋榜：展示20名。本周（从周一零点开始算）获取的总热度值最高的队伍。 */
function getMusicClubRankWeekHotList(ctx, params) {
    return __awaiter(this, void 0, void 0, function* () {
        const musicClubService = musicClubService_1.MusicClubService.getInstance();
        const rankingCache = musicClubRankingCache_1.MusicClubRankingCache.getInstance();
        const curTime = params.timestamp || Date.now();
        const weekDs = (0, dateUtil_1.getWeekDs)(curTime);
        // If _refresh=true, invalidate cache first to get real-time data
        if (params._refresh) {
            yield rankingCache.invalidateWeekRankCache(ctx, weekDs);
        }
        const rankList = yield rankingCache.get(ctx, {
            type: 'week',
            weekDs,
            limit: config_1.musicClubCfg.newHotRankListSize,
        });
        const showRankList = musicClubService.convertToShowRankItemTiny(rankList);
        const data = {
            list: showRankList,
            refreshTime: Date.now(),
            weekDs,
        };
        return data;
    });
}
exports.getMusicClubRankWeekHotList = getMusicClubRankWeekHotList;
/** 新晋榜：展示10名。当前周weekDs（从周一零点开始算）获取的总热度值最高的队伍。 */
function getMusicClubRankWeekHotListForServer(ctx, params) {
    return __awaiter(this, void 0, void 0, function* () {
        const weekDs = params.weekDs;
        const musicClubService = musicClubService_1.MusicClubService.getInstance();
        const rankingCache = musicClubRankingCache_1.MusicClubRankingCache.getInstance();
        // If _refresh=true, invalidate cache first to get real-time data
        if (params._refresh) {
            yield rankingCache.invalidateWeekRankCache(ctx, weekDs);
        }
        let rankList = yield rankingCache.get(ctx, {
            type: 'week',
            weekDs,
            limit: config_1.musicClubCfg.newHotRankListSize,
        });
        rankList = rankList.slice(0, config_1.musicClubCfg.newHotRankListSizeForServer);
        const showRankList = musicClubService.convertToShowRankItemTiny(rankList);
        const data = {
            list: showRankList,
            refreshTime: Date.now(),
            weekDs,
        };
        return data;
    });
}
exports.getMusicClubRankWeekHotListForServer = getMusicClubRankWeekHotListForServer;
/** 展示唱片列表，用于游戏内点播节目的信息展示 */
function getMusicClubRecordingList(ctx, params) {
    return __awaiter(this, void 0, void 0, function* () {
        const data = yield musicClubRecordingService_1.MusicClubRecordingService.getInstance().getMusicClubRecordingList(ctx, params);
        return data;
    });
}
exports.getMusicClubRecordingList = getMusicClubRecordingList;
/** 游戏同步点播操作行为，用于维护增加唱片点播计数 */
function musicClubRadioRequestPlay(ctx, params) {
    return __awaiter(this, void 0, void 0, function* () {
        const data = yield musicClubRecordingService_1.MusicClubRecordingService.getInstance().onRequestPlayLogEvent(ctx, params);
        return data;
    });
}
exports.musicClubRadioRequestPlay = musicClubRadioRequestPlay;
/** 上架唱片，从本地唱片上架到乐团 */
function musicClubRecordingRelease(ctx, params) {
    return __awaiter(this, void 0, void 0, function* () {
        // 先创建录音记录
        const data = yield musicClubRecordingService_1.MusicClubRecordingService.getInstance().addRecoding(ctx, params);
        try {
            // 创建审核任务并提交审核
            const auditTaskService = musicClubRecordingAuditTaskService_1.MusicClubRecordingAuditTaskService.getInstance();
            yield auditTaskService.createAuditTask(ctx, data.id, params.vocalUrl, params.roleid, params.serverId);
            logger.info({
                ctx,
                recordingId: data.id,
                vocalUrl: params.vocalUrl,
                roleId: params.roleid,
                serverId: params.serverId
            }, "CreateAuditTaskForRecordingSuccess");
        }
        catch (error) {
            // 即使创建审核任务失败，也不影响录音发布的结果
            logger.error({
                ctx,
                error,
                recordingId: data.id,
                vocalUrl: params.vocalUrl,
                roleId: params.roleid,
                serverId: params.serverId
            }, "CreateAuditTaskForRecordingError");
        }
        return data;
    });
}
exports.musicClubRecordingRelease = musicClubRecordingRelease;
/** 下架唱片，从乐团中移除该唱片, 只有乐团经理可以下架唱片, 需要*游戏服务端调用*, 完成乐团经理的权限校验 */
function musicClubRecordingRemove(ctx, params) {
    return __awaiter(this, void 0, void 0, function* () {
        const musicRecordingService = musicClubRecordingService_1.MusicClubRecordingService.getInstance();
        const musicClubService = musicClubService_1.MusicClubService.getInstance();
        const managerRoleId = yield musicClubService.getMusicClubManagerRoleId(ctx, params.musicClubId);
        if (managerRoleId !== params.roleid) {
            throw errorCodes_1.errorCodes.RecordingRemovePermissionDenied;
        }
        const recording = yield musicRecordingService.verifyRecordingExist(ctx, params.recordingId);
        if (recording.MusicClubId !== params.musicClubId) {
            logger.warn({ ctx, params, recording }, "TryToRemoveOtherMusicClubRecording");
            throw errorCodes_1.errorCodes.DataNotFound;
        }
        yield musicRecordingService.removeRecording(ctx, params.musicClubId, recording.ID);
        const resp = {
            id: recording.ID,
            removeTime: Date.now(),
        };
        return resp;
    });
}
exports.musicClubRecordingRemove = musicClubRecordingRemove;
/** 给唱片打分，打分范围0-10分 */
function musicClubRecordingRate(ctx, params) {
    return __awaiter(this, void 0, void 0, function* () {
        const recordingService = musicClubRecordingService_1.MusicClubRecordingService.getInstance();
        const resp = yield recordingService.addRecordingRating(ctx, params);
        yield recordingService.updateRecordingRatingStat(ctx, params.recordingId, params.rating);
        return resp;
    });
}
exports.musicClubRecordingRate = musicClubRecordingRate;
/** 获取唱片详细信息 */
function musicClubServerRecordingShow(ctx, params) {
    return __awaiter(this, void 0, void 0, function* () {
        const recordingService = musicClubRecordingService_1.MusicClubRecordingService.getInstance();
        const data = yield recordingService.getServerRecordingDetail(ctx, params);
        return data;
    });
}
exports.musicClubServerRecordingShow = musicClubServerRecordingShow;
function musicClubRecordingShow(ctx, params) {
    return __awaiter(this, void 0, void 0, function* () {
        const recordingService = musicClubRecordingService_1.MusicClubRecordingService.getInstance();
        const data = yield recordingService.getRecordingDetail(ctx, params);
        return data;
    });
}
exports.musicClubRecordingShow = musicClubRecordingShow;
/** 审核回调接口 */
function musicClubRecordingAuditCallback(ctx, params) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            // 使用新的审核任务服务处理回调
            const auditTaskService = musicClubRecordingAuditTaskService_1.MusicClubRecordingAuditTaskService.getInstance();
            const success = yield auditTaskService.handleAuditCallback(ctx, params.vocalUrl, params.auditStatus, params.rejectReason);
            if (success) {
                logger.info({ ctx, vocalUrl: params.vocalUrl, auditStatus: params.auditStatus }, "AuditCallbackHandledSuccessfully");
            }
            else {
                logger.warn({ ctx, vocalUrl: params.vocalUrl, auditStatus: params.auditStatus }, "AuditCallbackHandledFailed");
            }
            return { success };
        }
        catch (error) {
            logger.error({ ctx, error, params }, "AuditCallbackHandleError");
            return { success: false };
        }
    });
}
exports.musicClubRecordingAuditCallback = musicClubRecordingAuditCallback;
//# sourceMappingURL=operation.js.map