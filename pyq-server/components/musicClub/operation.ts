import { musicClubCfg } from "../../common/config";
import { Context } from "../../context";
import { errorCodes } from "../../errorCodes";
import { clazzLogger } from "../../logger";
import { MusicClubRecordingService } from "../../services/music-club/musicClubRecordingService";
import { MusicClubService } from "../../services/music-club/musicClubService";
import { MusicClubRankingCache } from "../../services/music-club/musicClubRankingCache";
import { MusicClubRecordingAuditTaskService } from "../../services/music-club/musicClubRecordingAuditTaskService";
import { MusicClubReq, MusicClubRes } from "./type";
import { getWeekDs } from "../../../common/dateUtil";
const logger = clazzLogger("musicClubOperation")

export async function musicClubCreate(
  ctx: Context,
  params: MusicClubReq.Create
): Promise<MusicClubRes.Create> {
  const data = await MusicClubService.getInstance().createMusicClub(ctx, params);
  return data;
}

export async function musicClubDisband(
  ctx: Context,
  params: MusicClubReq.Disband
): Promise<MusicClubRes.Disband> {
  const data = await MusicClubService.getInstance().disbandMusicClub(ctx, params);
  return data;
}

export async function musicClubUpdate(
  ctx: Context,
  params: MusicClubReq.Update
): Promise<MusicClubRes.Update> {
  const data = await MusicClubService.getInstance().updateMusicClub(ctx, params);
  return data;
}

export async function musicClubShow(
  ctx: Context,
  params: MusicClubReq.Show
): Promise<MusicClubRes.Show> {
  const data = await MusicClubService.getInstance().getMusicClubInfo(ctx, params);
  return data;
}


/** 总热度榜：展示50名。总热度从高到低排序。 */
export async function getMusicClubRankHotList(
  ctx: Context,
  params: MusicClubReq.GetRankHotList & { _refresh: true }
): Promise<MusicClubRes.GetRankHotList> {
  const musicClubService = MusicClubService.getInstance()
  const rankingCache = MusicClubRankingCache.getInstance()

  // If refresh=true, invalidate cache first to get real-time data
  if (params._refresh) {
    await rankingCache.invalidateHotRankCache(ctx)
  }

  const rankList = await rankingCache.get(ctx, {
    type: 'hot',
    limit: musicClubCfg.allHotRankListSize,
  })
  const showRankList = musicClubService.convertToShowRankItemTiny(rankList)
  const data: MusicClubRes.GetRankHotList = {
    list: showRankList,
    refreshTime: Date.now(),
  }
  return data;
}


/** 总热度榜：展示10名。总热度从高到低排序。 */
export async function getMusicClubRankHotListForServer(
  ctx: Context,
  params: MusicClubReq.GetRankHotListForServer & { _refresh: true }
): Promise<MusicClubRes.GetRankHotList> {
  const musicClubService = MusicClubService.getInstance()
  const rankingCache = MusicClubRankingCache.getInstance()

  // If _refresh=true, invalidate cache first to get real-time data
  if (params._refresh) {
    await rankingCache.invalidateHotRankCache(ctx)
  }

  let rankList = await rankingCache.get(ctx, {
    type: 'hot',
    limit: musicClubCfg.allHotRankListSize,
  })
  rankList = rankList.slice(0, musicClubCfg.allHotRankListSizeForServer)
  const showRankList = musicClubService.convertToShowRankItemTiny(rankList)
  const data: MusicClubRes.GetRankHotList = {
    list: showRankList,
    refreshTime: Date.now(),
  }
  return data;
}

/** 新晋榜：展示20名。本周（从周一零点开始算）获取的总热度值最高的队伍。 */
export async function getMusicClubRankWeekHotList(
  ctx: Context,
  params: MusicClubReq.GetRankWeekHotList & { _refresh: true }
): Promise<MusicClubRes.GetRankWeekHotList> {
  const musicClubService = MusicClubService.getInstance()
  const rankingCache = MusicClubRankingCache.getInstance()
  const curTime = params.timestamp || Date.now()
  const weekDs = getWeekDs(curTime)

  // If _refresh=true, invalidate cache first to get real-time data
  if (params._refresh) {
    await rankingCache.invalidateWeekRankCache(ctx, weekDs)
  }

  const rankList = await rankingCache.get(ctx, {
    type: 'week',
    weekDs,
    limit: musicClubCfg.newHotRankListSize,
  })

  const showRankList = musicClubService.convertToShowRankItemTiny(rankList)
  const data: MusicClubRes.GetRankWeekHotList = {
    list: showRankList,
    refreshTime: Date.now(),
    weekDs,
  }
  return data;
}


/** 新晋榜：展示10名。当前周weekDs（从周一零点开始算）获取的总热度值最高的队伍。 */
export async function getMusicClubRankWeekHotListForServer(
  ctx: Context,
  params: MusicClubReq.GetRankWeekHotListForServer & { _refresh: true }
): Promise<MusicClubRes.GetRankWeekHotList> {
  const weekDs = params.weekDs
  const musicClubService = MusicClubService.getInstance()
  const rankingCache = MusicClubRankingCache.getInstance()

  // If _refresh=true, invalidate cache first to get real-time data
  if (params._refresh) {
    await rankingCache.invalidateWeekRankCache(ctx, weekDs)
  }

  let rankList = await rankingCache.get(ctx, {
    type: 'week',
    weekDs,
    limit: musicClubCfg.newHotRankListSize,
  })

  rankList = rankList.slice(0, musicClubCfg.newHotRankListSizeForServer)

  const showRankList = musicClubService.convertToShowRankItemTiny(rankList)
  const data: MusicClubRes.GetRankWeekHotList = {
    list: showRankList,
    refreshTime: Date.now(),
    weekDs,
  }
  return data;
}

/** 展示唱片列表，用于游戏内点播节目的信息展示 */
export async function getMusicClubRecordingList(
  ctx: Context,
  params: MusicClubReq.GetRecordingList
): Promise<MusicClubRes.GetRecordingList> {
  const data = await MusicClubRecordingService.getInstance().getMusicClubRecordingList(ctx, params);
  return data;
}

/** 游戏同步点播操作行为，用于维护增加唱片点播计数 */
export async function musicClubRadioRequestPlay(
  ctx: Context,
  params: MusicClubReq.RadioRequestPlay
): Promise<MusicClubRes.RadioRequestPlay> {
  const data = await MusicClubRecordingService.getInstance().onRequestPlayLogEvent(ctx, params);
  return data;
}

/** 上架唱片，从本地唱片上架到乐团 */
export async function musicClubRecordingRelease(
  ctx: Context,
  params: MusicClubReq.RecordingRelease
): Promise<MusicClubRes.RecordingRelease> {
  // 先创建录音记录
  const data = await MusicClubRecordingService.getInstance().addRecoding(ctx, params);

  try {
    // 创建审核任务并提交审核
    const auditTaskService = MusicClubRecordingAuditTaskService.getInstance();
    await auditTaskService.createAuditTask(
      ctx,
      data.id,
      params.vocalUrl,
      params.roleid,
      params.serverId
    );

    logger.info({
      ctx,
      recordingId: data.id,
      vocalUrl: params.vocalUrl,
      roleId: params.roleid,
      serverId: params.serverId
    }, "CreateAuditTaskForRecordingSuccess");
  } catch (error) {
    // 即使创建审核任务失败，也不影响录音发布的结果
    logger.error({
      ctx,
      error,
      recordingId: data.id,
      vocalUrl: params.vocalUrl,
      roleId: params.roleid,
      serverId: params.serverId
    }, "CreateAuditTaskForRecordingError");
  }

  return data;
}

/** 下架唱片，从乐团中移除该唱片, 只有乐团经理可以下架唱片, 需要*游戏服务端调用*, 完成乐团经理的权限校验 */
export async function musicClubRecordingRemove(
  ctx: Context,
  params: MusicClubReq.RecordingRemove
): Promise<MusicClubRes.RecordingRemove> {
  const musicRecordingService = MusicClubRecordingService.getInstance()
  const musicClubService = MusicClubService.getInstance()
  const managerRoleId = await musicClubService.getMusicClubManagerRoleId(ctx, params.musicClubId)

  if (managerRoleId !== params.roleid) {
    throw errorCodes.RecordingRemovePermissionDenied
  }

  const recording = await musicRecordingService.verifyRecordingExist(ctx, params.recordingId)

  if (recording.MusicClubId !== params.musicClubId) {
    logger.warn({ ctx, params, recording }, "TryToRemoveOtherMusicClubRecording")
    throw errorCodes.DataNotFound
  }

  await musicRecordingService.removeRecording(ctx, params.musicClubId, recording.ID)

  const resp: MusicClubRes.RecordingRemove = {
    id: recording.ID,
    removeTime: Date.now(),
  };

  return resp;
}

/** 给唱片打分，打分范围0-10分 */
export async function musicClubRecordingRate(
  ctx: Context,
  params: MusicClubReq.RecordingRate
): Promise<MusicClubRes.RecordingRate> {
  const recordingService = MusicClubRecordingService.getInstance()
  const resp = await recordingService.addRecordingRating(ctx, params);
  await recordingService.updateRecordingRatingStat(ctx, params.recordingId, params.rating);
  return resp;
}

/** 获取唱片详细信息 */
export async function musicClubServerRecordingShow(
  ctx: Context,
  params: MusicClubReq.ServerRecordingShow
): Promise<MusicClubRes.RecordingShow> {
  const recordingService = MusicClubRecordingService.getInstance()
  const data = await recordingService.getServerRecordingDetail(ctx, params);
  return data;
}


export async function musicClubRecordingShow(
  ctx: Context,
  params: MusicClubReq.RecordingShow
): Promise<MusicClubRes.RecordingShow> {
  const recordingService = MusicClubRecordingService.getInstance()
  const data = await recordingService.getRecordingDetail(ctx, params);
  return data;
}

/** 审核回调接口 */
export async function musicClubRecordingAuditCallback(
  ctx: Context,
  params: { vocalUrl: string; auditStatus: number; rejectReason?: string }
): Promise<{ success: boolean }> {
  try {
    // 使用新的审核任务服务处理回调
    const auditTaskService = MusicClubRecordingAuditTaskService.getInstance();
    const success = await auditTaskService.handleAuditCallback(
      ctx,
      params.vocalUrl,
      params.auditStatus,
      params.rejectReason
    );

    if (success) {
      logger.info({ ctx, vocalUrl: params.vocalUrl, auditStatus: params.auditStatus }, "AuditCallbackHandledSuccessfully");
    } else {
      logger.warn({ ctx, vocalUrl: params.vocalUrl, auditStatus: params.auditStatus }, "AuditCallbackHandledFailed");
    }

    return { success };
  } catch (error) {
    logger.error({ ctx, error, params }, "AuditCallbackHandleError");
    return { success: false };
  }
}
