"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MusicClubComponent = exports.paths = void 0;
const helper_1 = require("../../../common/helper");
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "post",
        url: "/music_club/create",
        paramsSchema: type_1.ReqSchemas.MusicClubCreate,
        before: helper_1.gameIpLimit,
        operation: operation_1.musicClubCreate,
        option: {
            skipAuth: true,
        }
    },
    {
        method: "post",
        url: "/music_club/disband",
        paramsSchema: type_1.ReqSchemas.MusicClubDisband,
        before: helper_1.gameIpLimit,
        operation: operation_1.musicClubDisband,
        option: {
            skipAuth: true,
        }
    },
    {
        method: "post",
        url: "/music_club/update",
        paramsSchema: type_1.ReqSchemas.MusicClubUpdate,
        before: helper_1.gameIpLimit,
        operation: operation_1.musicClubUpdate,
        option: {
            skipAuth: true,
        }
    },
    {
        method: "get",
        url: "/music_club/show",
        before: helper_1.gameIpLimit,
        paramsSchema: type_1.ReqSchemas.MusicClubShow,
        operation: operation_1.musicClubShow,
        option: {
            skipAuth: true,
        }
    },
    {
        method: "get",
        url: "/music_club/rank/hot_list",
        paramsSchema: type_1.ReqSchemas.GetMusicClubRankHotList,
        operation: operation_1.getMusicClubRankHotList,
    },
    {
        method: "get",
        url: "/music_club/server/rank/hot_list",
        paramsSchema: type_1.ReqSchemas.GetMusicClubRankHotListForServer,
        before: helper_1.gameIpLimit,
        operation: operation_1.getMusicClubRankHotListForServer,
        option: {
            skipAuth: true,
        }
    },
    {
        method: "get",
        url: "/music_club/rank/week_hot_list",
        paramsSchema: type_1.ReqSchemas.GetMusicClubRankWeekHotList,
        before: helper_1.gameIpLimit,
        operation: operation_1.getMusicClubRankWeekHotList,
        option: {
            skipAuth: true,
        }
    },
    {
        method: "get",
        url: "/music_club/server/rank/week_hot_list",
        paramsSchema: type_1.ReqSchemas.GetMusicClubRankWeekHotListForServer,
        before: helper_1.gameIpLimit,
        operation: operation_1.getMusicClubRankWeekHotListForServer,
        option: {
            skipAuth: true,
        }
    },
    {
        method: "get",
        url: "/music_club/radio/recording_list",
        paramsSchema: type_1.ReqSchemas.GetMusicClubRecordingList,
        operation: operation_1.getMusicClubRecordingList,
    },
    {
        method: "post",
        url: "/music_club/radio/request_play",
        paramsSchema: type_1.ReqSchemas.MusicClubRadioRequestPlay,
        before: helper_1.gameIpLimit,
        operation: operation_1.musicClubRadioRequestPlay,
        option: {
            skipAuth: true,
        },
    },
    {
        method: "post",
        url: "/music_club/recording/release",
        paramsSchema: type_1.ReqSchemas.MusicClubRecordingRelease,
        operation: operation_1.musicClubRecordingRelease,
    },
    {
        method: "post",
        url: "/music_club/recording/remove",
        paramsSchema: type_1.ReqSchemas.MusicClubRecordingRemove,
        operation: operation_1.musicClubRecordingRemove,
    },
    {
        method: "post",
        url: "/music_club/recording/rate",
        paramsSchema: type_1.ReqSchemas.MusicClubRecordingRate,
        before: helper_1.gameIpLimit,
        operation: operation_1.musicClubRecordingRate,
        option: {
            skipAuth: true,
        }
    },
    {
        method: "get",
        url: "/music_club/recording/show",
        paramsSchema: type_1.ReqSchemas.MusicClubRecordingShow,
        operation: operation_1.musicClubServerRecordingShow,
    },
    {
        method: "get",
        url: "/music_club/server/recording/show",
        paramsSchema: type_1.ReqSchemas.MusicClubRecordingShow,
        before: helper_1.gameIpLimit,
        operation: operation_1.musicClubServerRecordingShow,
        option: {
            skipAuth: true,
        }
    },
    {
        method: "post",
        url: "/music_club/recording/audit_callback",
        paramsSchema: type_1.ReqSchemas.MusicClubRecordingAuditCallback,
        operation: operation_1.musicClubRecordingAuditCallback,
        option: {
            skipAuth: true,
        }
    },
];
exports.MusicClubComponent = {
    paths: exports.paths,
    prefix: "/music_club/",
    version: "v2",
};
//# sourceMappingURL=index.js.map