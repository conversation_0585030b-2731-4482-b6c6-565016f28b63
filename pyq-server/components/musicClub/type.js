"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = void 0;
const constants_1 = require("../../constants");
exports.ReqSchemas = {
    MusicClubCreate: {
        type: "object",
        properties: {
            musicClubId: { type: "integer" },
            name: { type: "string", minLength: 1, maxLength: constants_1.MusicClubConstants.MusicClubNameMaxLength },
            serverId: { type: "integer" },
            managerRoleId: { type: "integer" }
        },
        required: ["musicClubId", "name", "serverId", "managerRoleId"]
    },
    MusicClubDisband: {
        type: "object",
        properties: {
            musicClubId: { type: "integer" }
        },
        required: ["musicClubId"]
    },
    MusicClubUpdate: {
        type: "object",
        properties: {
            musicClubId: { type: "integer", minimum: 1 },
            level: { type: "integer", minimum: 1 },
            managerRoleId: { type: "integer", minimum: 1 },
            name: { type: "string", minLength: 1, maxLength: constants_1.MusicClubConstants.MusicClubNameMaxLength }
        },
        required: ["musicClubId"],
        additionalProperties: false
    },
    MusicClubShow: { type: "object", properties: { musicClubId: { type: "integer" } }, required: ["musicClubId"] },
    GetMusicClubRankHotList: { type: "object", properties: { roleid: { type: "number" } }, required: ["roleid"] },
    GetMusicClubRankWeekHotList: { type: "object", properties: { roleid: { type: "number" }, timestamp: { type: "number" } }, required: ["roleid"] },
    GetMusicClubRankHotListForServer: { type: "object", properties: { weekDs: { type: "string" } }, required: ["weekDs"] },
    GetMusicClubRankWeekHotListForServer: { type: "object", properties: { weekDs: { type: "string" } }, required: ["weekDs"] },
    GetMusicClubRecordingList: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            sortBy: { type: "string", default: "requestPlayCount", enum: ["requestPlayCount", "rating", "recentHot"] },
            musicClubId: { type: "integer" },
            page: { type: "number", minimum: 1, default: 1 },
            pageSize: { type: "number", minimum: 1, maximum: 20, default: 10 },
            kw: { type: "string" },
        },
        required: ["roleid"],
    },
    MusicClubRadioRequestPlay: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            fromRoleId: { type: "integer" },
            toRoleId: { type: "integer" },
            recordingId: { type: "integer" },
            eventTime: { type: "integer" },
        },
        required: ["roleid", "fromRoleId", "toRoleId", "recordingId", "eventTime"],
    },
    MusicClubRecordingRelease: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            trackId: { type: "string", maxLength: 16 },
            name: { type: "string", minLength: 1, maxLength: constants_1.MusicClubConstants.RecordingNameMaxLength },
            chorusStart: { type: "integer", minimum: 0 },
            vocalOffset: { type: "integer" },
            vocalVolume: { type: "number", minimum: 0, maximum: 100 },
            instrumentVolume: { type: "number", minimum: 0, maximum: 100 },
            dataUrl: { type: "string", minLength: 1 },
            musicClubId: { type: "integer" },
            duration: { type: "integer", minimum: 0 },
        },
        required: [
            "roleid",
            "trackId",
            "name",
            "chorusStart",
            "vocalOffset",
            "vocalVolume",
            "instrumentVolume",
            "dataUrl",
            "musicClubId",
            "duration",
        ],
    },
    MusicClubRecordingRemove: {
        type: "object",
        properties: { roleid: { type: "number" }, musicClubId: { type: "integer" }, recordingId: { type: "integer" } },
        required: ["roleid", "musicClubId", "recordingId"],
    },
    MusicClubRecordingRate: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            rating: { type: "number", minimum: 1, maximum: 10 },
            recordingId: { type: "integer" },
        },
        required: ["roleid", "rating", "recordingId"],
    },
    MusicClubRecordingShow: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            recordingId: { type: "integer", minimum: 1 },
        },
        required: ["roleid", "recordingId"],
    },
    MusicClubServerRecordingShow: {
        type: "object",
        properties: {
            recordingId: { type: "integer", minimum: 1 },
        },
        required: ["recordingId"],
    },
    MusicClubRecordingAuditCallback: {
        type: "object",
        properties: {
            vocalUrl: { type: "string", minLength: 1 },
            auditStatus: { type: "integer", enum: [1, -1] },
            rejectReason: { type: "string" },
        },
        required: ["vocalUrl", "auditStatus"],
    },
};
//# sourceMappingURL=type.js.map