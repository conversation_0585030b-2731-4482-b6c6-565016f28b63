import { gameIpLimit } from "../../../common/helper";
import { Component, Path } from "../../types/type";
import {
  getMusicClubRankHotList,
  getMusicClubRankWeekHotList,
  getMusicClubRecordingList,
  musicClubRadioRequestPlay,
  musicClubRecordingRelease,
  musicClubRecordingRemove,
  musicClubRecordingRate,
  musicClubServerRecordingShow,
  musicClubCreate,
  musicClubDisband,
  musicClubUpdate,
  musicClubShow,
  getMusicClubRankHotListForServer,
  getMusicClubRankWeekHotListForServer,
  musicClubRecordingAuditCallback,
} from "./operation";
import { ReqSchemas } from "./type";

export const paths: Path[] = [
  {
    method: "post",
    url: "/music_club/create",
    paramsSchema: ReqSchemas.MusicClubCreate,
    before: gameIpLimit,
    operation: musicClubCreate,
    option: {
      skipAuth: true,
    }
  },
  {
    method: "post",
    url: "/music_club/disband",
    paramsSchema: ReqSchemas.MusicClubDisband,
    before: gameIpLimit,
    operation: musicClubDisband,
    option: {
      skipAuth: true,
    }
  },
  {
    method: "post",
    url: "/music_club/update",
    paramsSchema: ReqSchemas.MusicClubUpdate,
    before: gameIpLimit,
    operation: musicClubUpdate,
    option: {
      skipAuth: true,
    }
  },
  {
    method: "get",
    url: "/music_club/show",
    before: gameIpLimit,
    paramsSchema: ReqSchemas.MusicClubShow,
    operation: musicClubShow,
    option: {
      skipAuth: true,
    }
  },
  {
    method: "get",
    url: "/music_club/rank/hot_list",
    paramsSchema: ReqSchemas.GetMusicClubRankHotList,
    operation: getMusicClubRankHotList,
  },
  {
    method: "get",
    url: "/music_club/server/rank/hot_list",
    paramsSchema: ReqSchemas.GetMusicClubRankHotListForServer,
    before: gameIpLimit,
    operation: getMusicClubRankHotListForServer,
    option: {
      skipAuth: true,
    }
  },
  {
    method: "get",
    url: "/music_club/rank/week_hot_list",
    paramsSchema: ReqSchemas.GetMusicClubRankWeekHotList,
    before: gameIpLimit,
    operation: getMusicClubRankWeekHotList,
    option: {
      skipAuth: true,
    }
  },
  {
    method: "get",
    url: "/music_club/server/rank/week_hot_list",
    paramsSchema: ReqSchemas.GetMusicClubRankWeekHotListForServer,
    before: gameIpLimit,
    operation: getMusicClubRankWeekHotListForServer,
    option: {
      skipAuth: true,
    }
  },
  {
    method: "get",
    url: "/music_club/radio/recording_list",
    paramsSchema: ReqSchemas.GetMusicClubRecordingList,
    operation: getMusicClubRecordingList,
  },
  {
    method: "post",
    url: "/music_club/radio/request_play",
    paramsSchema: ReqSchemas.MusicClubRadioRequestPlay,
    before: gameIpLimit,
    operation: musicClubRadioRequestPlay,
    option: {
      skipAuth: true,
    },
  },
  {
    method: "post",
    url: "/music_club/recording/release",
    paramsSchema: ReqSchemas.MusicClubRecordingRelease,
    operation: musicClubRecordingRelease,
  },
  {
    method: "post",
    url: "/music_club/recording/remove",
    paramsSchema: ReqSchemas.MusicClubRecordingRemove,
    operation: musicClubRecordingRemove,
  },
  {
    method: "post",
    url: "/music_club/recording/rate",
    paramsSchema: ReqSchemas.MusicClubRecordingRate,
    before: gameIpLimit,
    operation: musicClubRecordingRate,
    option: {
      skipAuth: true,
    }
  },
  {
    method: "get",
    url: "/music_club/recording/show",
    paramsSchema: ReqSchemas.MusicClubRecordingShow,
    operation: musicClubServerRecordingShow,
  },
  {
    method: "get",
    url: "/music_club/server/recording/show",
    paramsSchema: ReqSchemas.MusicClubRecordingShow,
    before: gameIpLimit,
    operation: musicClubServerRecordingShow,
    option: {
      skipAuth: true,
    }
  },
  {
    method: "post",
    url: "/music_club/recording/audit_callback",
    paramsSchema: ReqSchemas.MusicClubRecordingAuditCallback,
    operation: musicClubRecordingAuditCallback,
    option: {
      skipAuth: true,
    }
  },
];

export const MusicClubComponent: Component = {
  paths: paths,
  prefix: "/music_club/",
  version: "v2",
};
