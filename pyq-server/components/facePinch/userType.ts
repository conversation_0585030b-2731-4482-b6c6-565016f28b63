import { components } from "../../types/swagger"
import { UserRecord } from "./models/user"
import { FacePinchReq } from "./type"

export type UserActionFlag = components["schemas"]["FacePinchUserActionFlag"]

export interface IWorkSearchApi {
  search(params: FacePinchReq.WorkSearch)
}

export interface FacePinchCurUser {
  curUser: UserRecord
}


export type AccusedRole = components["schemas"]["FacePinchWorkExtraForAccuse"]["accusedRole"]