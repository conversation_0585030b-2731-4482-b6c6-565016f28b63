import { WorkRecord, WorkVisibility } from "../models/work"

export interface EventPayload {
  /** 当前用户id */
  userId: number
  /** 当前玩家id */
  roleId: number
  /** 事件发生的时间戳 */
  ts: number
}

export namespace WorkEvent {
  export interface Add extends EventPayload {
    work: WorkRecord
  }

  export interface WorkAction extends Add {
    isFirstTime: boolean
  }

  export interface WorkUpdateVisibility extends Add {
    visibility: WorkVisibility
  }


  export type LikeAdd = WorkAction
  export type LikeDel = Add
  export type CollectAdd = WorkAction
  export type CollectDel = Add
  export type Del = Add
  export type ApplyAdd = WorkAction
}


export interface IWorkEventHandler {
  onAdd?(payload: WorkEvent.Add)

  onLikeAdd?(payload: WorkEvent.LikeAdd)

  onCollectAdd?(payload: WorkEvent.CollectAdd)

  onApplyAdd?(payload: WorkEvent.ApplyAdd)

  onDel?(payload: WorkEvent.Del)
}