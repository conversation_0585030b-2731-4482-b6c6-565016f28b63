import { facePinchCfg } from "../../common/config";
import { injectIpHelper, ipLimitMiddleware } from "../../middlewares/ipAuth";
import { rateLimiter } from "../../middlewares/rateLimiter";
import { checkContentSensitive } from "../../middlewares/speechLimit";
import { Path } from "../../types/type";
import {
  facePinchWorkListPublic,
  facePinchWorkListCollect,
  facePinchWorkListSelf,
  facePinchWorkGetByShareId,
  facePinchWorkDetail,
  facePinchWorkSearch,
  facePinchWorkApply,
  facePinchWorkLike,
  facePinchWorkCancelLike,
  facePinchWorkCollect,
  facePinchWorkCancelCollect,
  facePinchWorkAdd,
  facePinchWorkDel,
  facePinchGetFpToken,
  facePinchWorkCloudGameAuthLogin,
  facePinchWorkUpdateVisibility,
  facePinchWebPublicShareInfo,
  facePinchWebPublicShareVideoInfo,
  facePinchShareVideoAdd,
  facePinchDashenShareInfoImport,
} from "./operation";
import { ReqSchemas } from "./type";
import { FacePinchCurUser } from "./userType";

export const paths: Path[] = [
  {
    method: "get",
    url: "/face_pinch/web/public/share_info",
    paramsSchema: ReqSchemas.FacePinchWebPublicShareInfo,
    operation: facePinchWebPublicShareInfo,
    option: { skipAuth: true },
  },
  {
    method: "post",
    url: "/face_pinch/dashen/share_info/import",
    paramsSchema: ReqSchemas.FacePinchDashenShareInfoImport,
    operation: facePinchDashenShareInfoImport,
    before: ipLimitMiddleware(facePinchCfg.dashenIpAllowList),
    option: { skipAuth: true },
  },
  {
    method: "get",
    url: "/face_pinch/web/public/share_video/info",
    paramsSchema: ReqSchemas.FacePinchWebPublicShareVideoInfo,
    operation: facePinchWebPublicShareVideoInfo,
    option: { skipAuth: true },
  },
  {
    method: "post",
    url: "/face_pinch/share_video/add",
    paramsSchema: ReqSchemas.FacePinchShareVideoAdd,
    operation: facePinchShareVideoAdd,
  },
  {
    method: "get",
    url: "/face_pinch/work/list_public",
    paramsSchema: ReqSchemas.FacePinchWorkListPublic,
    operation: facePinchWorkListPublic,
  },
  {
    method: "post",
    url: "/face_pinch/cloud_game/auth/login",
    paramsSchema: ReqSchemas.FacePinchCloudGameAuthLogin,
    before: injectIpHelper(),
    operation: facePinchWorkCloudGameAuthLogin,
    option: { skipAuth: true },
  },
  {
    method: "get",
    url: "/face_pinch/work/list_collect",
    paramsSchema: ReqSchemas.FacePinchWorkListCollect,
    operation: facePinchWorkListCollect,
  },
  {
    method: "get",
    url: "/face_pinch/work/list_self",
    paramsSchema: ReqSchemas.FacePinchWorkListSelf,
    operation: facePinchWorkListSelf,
  },
  {
    method: "get",
    url: "/face_pinch/work/get_by_share_id",
    paramsSchema: ReqSchemas.FacePinchWorkGetByShareId,
    operation: facePinchWorkGetByShareId,
  },
  {
    method: "get",
    url: "/face_pinch/work/detail",
    paramsSchema: ReqSchemas.FacePinchWorkDetail,
    operation: facePinchWorkDetail,
  },
  {
    method: "get",
    url: "/face_pinch/work/search",
    paramsSchema: ReqSchemas.FacePinchWorkSearch,
    operation: facePinchWorkSearch,
  },
  {
    method: "post",
    url: "/face_pinch/work/add",
    paramsSchema: ReqSchemas.FacePinchWorkAdd,
    before: checkContentSensitive(["roleName", "title", "desc"]),
    operation: facePinchWorkAdd,
  },
  {
    method: "post",
    url: "/face_pinch/work/update_visibility",
    paramsSchema: ReqSchemas.FacePinchWorkUpdateVisibility,
    operation: facePinchWorkUpdateVisibility,
  },
  {
    method: "get",
    url: "/face_pinch/get_fp_token",
    paramsSchema: ReqSchemas.FacePinchGetFpToken,
    operation: facePinchGetFpToken,
    before: [
      injectIpHelper(),
      rateLimiter<FacePinchCurUser>(
        {
          endPoint: "face_pinch_get_fp_token",
          rate_limit: {
            seconds: facePinchCfg.fpLimitRate.seconds,
            max: facePinchCfg.fpLimitRate.max,
          },
        },
        (params) => {
          if (params.curUser && params.curUser.account) {
            return params.curUser.account;
          } else {
            return "";
          }
        }
      ),
    ],
  },
  {
    method: "post",
    url: "/face_pinch/work/del",
    paramsSchema: ReqSchemas.FacePinchWorkDel,
    operation: facePinchWorkDel,
  },
  {
    method: "post",
    url: "/face_pinch/work/apply",
    paramsSchema: ReqSchemas.FacePinchWorkApply,
    operation: facePinchWorkApply,
  },
  {
    method: "post",
    url: "/face_pinch/work/like",
    paramsSchema: ReqSchemas.FacePinchWorkLike,
    operation: facePinchWorkLike,
  },
  {
    method: "post",
    url: "/face_pinch/work/cancel_like",
    paramsSchema: ReqSchemas.FacePinchWorkCancelLike,
    operation: facePinchWorkCancelLike,
  },
  {
    method: "post",
    url: "/face_pinch/work/collect",
    paramsSchema: ReqSchemas.FacePinchWorkCollect,
    operation: facePinchWorkCollect,
  },
  {
    method: "post",
    url: "/face_pinch/work/cancel_collect",
    paramsSchema: ReqSchemas.FacePinchWorkCancelCollect,
    operation: facePinchWorkCancelCollect,
  },
];

export const FacePinchComponent = {
  paths: paths,
  prefix: "/face_pinch/",
};
