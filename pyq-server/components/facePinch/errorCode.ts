export const ApiErrors = {
  WorkNotFound: { code: -1001, message: "该作品未找到", transKey: "workNotFound" },
  WorkAlreadyLiked: { code: -1002, message: "该作品已点赞", transKey: "workAlreadyLiked" },
  WorkAlreadyApplied: { code: -1003, message: "该作品已经应用过了", transKey: "workAlreadyApplied" },
  WorkNotLiked: { code: -1004, message: "该作品还未点赞", transKey: "workNotLiked" },
  WorkAlreadyCollected: { code: -1009, message: "该作品已收藏", transKey: "workAlreadyCollected" },
  WorkNotCollected: { code: -1010, message: "该作品还未收藏", transKey: "workNotCollected" },
  ContainSensitive: { code: -1011, message: "提交内容包含敏感文本", transKey: "containSensitive" },
  WorkNotPublic: { code: -1012, message: "该作品还未公开, 无法进行此操作", transKey: "workNotPublic" },
  WorkUploadReachLimit: { code: -1013, message: "作品保存到达上限", transKey: "workUploadReachLimit" },
  WorkCollectReachLimit: { code: -1014, message: "作品收藏到达上限", transKey: "workCollectReachLimit" },
  UserNotLogin: { code: -1015, message: "当前用户未登录", transKey: "userNotLogin" },
  UserLoginFrequency: { code: -1016, message: "当前用户登录频率过高", transKey: "userLoginFrequency" },
  CloudGameUserAccessDenied: { code: -1017, message: "当前用户未登录", transKey: "cloudGameUserAccessDenied" },
  WorkAlreadyPublic: { code: -1018, message: "该作品已公开", transKey: "workAlreadyPublic" },
  WorkAlreadyPrivate: { code: -1019, message: "该作品已私有", transKey: "workAlreadyPrivate" },
  ShareInfoNotFound: { code: -1020, message: "该分享作品未找到", transKey: "shareInfoNotFound" },
  VideoLinkInvalid: { code: -1021, message: "视频链接格式非法", transKey: "videoLinkInvalid" },
  RoleInfoNotFound: { code: -1022, message: "角色信息未找到", transKey: "roleInfoNotFound" },
  RoleInfoAccountMiss: { code: -1023, message: "角色信息账号未找到", transKey: "roleInfoAccountMiss" },
  
  // 部位功能相关错误
  InvalidPartsSelection: { code: -1024, message: "无效的部位选择", transKey: "invalidPartsSelection" },
  PartsNotAllowed: { code: -1025, message: "请求的部位超出作品允许范围", transKey: "partsNotAllowed" },
  LegacyDataRestriction: { code: -1026, message: "历史数据不支持单独使用塑形部位", transKey: "legacyDataRestriction" },
  StorageLimitExceeded: { code: -1027, message: "存储容量已达上限(300)，请删除部分内容后重试", transKey: "storageLimitExceeded" },
  AtLeastOnePartRequired: { code: -1028, message: "至少需要选择一个部位", transKey: "atLeastOnePartRequired" },
};
