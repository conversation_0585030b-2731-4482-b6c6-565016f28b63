import * as _ from "lodash"
import { EAuditStatues, EVisibility } from "../../../../common/constants"
import { ApiErrors } from "../errorCode"
import { ApplyModel } from "../models/apply"
import { CollectModel } from "../models/collect"
import { LikeModel } from "../models/like"
import { WorkModel, WorkRecord } from "../models/work"
import { FacePinchReq, FacePinchRes } from "../type"
import { FacePinchCurUser, UserActionFlag } from "../userType"
import { FacePartFilterService } from "../services/facePartFilterService"
import { getAvailableApplyParts } from "../utils/partsUtils"

export class WorkService {
  static async getPublicListByNew(
    params: FacePinchReq.WorkListPublic & FacePinchCurUser
  ): Promise<FacePinchRes.WorkListPublic> {
    const userId = params.curUser.id

    // 验证部位筛选参数
    const validation = FacePartFilterService.validateAllowedParts(params.allowedParts);
    if (!validation.valid) {
      throw ApiErrors.InvalidPartsSelection;
    }

    let query = WorkModel.publicScope()
    query = WorkModel.bodyShapeScope(query, params)

    // 应用部位筛选
    query = FacePartFilterService.buildFilterQuery(query, params.allowedParts);

    const rows = await WorkModel.powerQuery({
      initQuery: query,
      orderBy: [["id"], ["desc"]],
      pagination: { page: params.page, pageSize: params.pageSize },
    })
    const list = await WorkService.formatListRes(rows, userId)
    const data: FacePinchRes.WorkListPublic = { list }
    return data
  }

  static async checkExist(workId: number, userId: number): Promise<WorkRecord> {
    const r = await WorkModel.findOne({ id: workId, ...WorkModel.normalCondition() })
    if (!r) {
      throw ApiErrors.WorkNotFound
    } else {
      const isPublicAndAuditPass = r.auditStatus === EAuditStatues.PASS && r.visibility === EVisibility.Public
      const isSelfView = r.userId === userId
      if (isPublicAndAuditPass || isSelfView) {
        return r
      } else {
        throw ApiErrors.WorkNotFound
      }
    }
  }

  static async checkForImport(id: number): Promise<WorkRecord> {
    const r = await WorkModel.findOne(WorkModel.importCondition(id))
    if (!r) {
      throw ApiErrors.WorkNotFound
    } else {
      return r
    }
  }

  static async checkPublic(moment: WorkRecord) {
    if (moment.visibility !== EVisibility.Public) {
      throw ApiErrors.WorkNotPublic
    }
  }

  static async formatListRes(rows: WorkRecord[], userId: number) {
    return WorkService.formatWorkRecordList(rows, userId)
  }

  static async formatWorkRecordList(rows: WorkRecord[], userId: number): Promise<FacePinchRes.WorkDetail[]> {
    if(_.isEmpty(rows)) return []
    const workIds = rows.map((r) => r.id)
    const likeWorkIds = await LikeModel.filterUserLiked(userId, workIds)
    const collectWorkIds = await CollectModel.filterUserCollected(userId, workIds)
    const applyWorkIds = await ApplyModel.filterUserApplied(userId, workIds)
    const list = rows.map((r) => {
      const isLiked = _.includes(likeWorkIds, r.id)
      const isCollected = _.includes(collectWorkIds, r.id)
      const onceApplied = _.includes(applyWorkIds, r.id)
      const item = WorkService.formatWorkRecord(r, { isLiked, isCollected, onceApplied })
      return item
    })
    return list
  }

  private static formatWorkRecord(r: WorkRecord, actionFlag: UserActionFlag): FacePinchRes.WorkDetail {
    // 计算可用的应用部位（考虑历史数据限制）
    const availableApplyParts = getAvailableApplyParts(r.allowedParts, r.createTime);

    const data: FacePinchRes.WorkDetail = {
      id: r.id,
      userId: r.userId,
      roleId: r.roleId,
      roleName: r.roleName,
      shareId: r.shareId,
      jobId: r.jobId,
      gender: r.gender,
      createTime: r.createTime,
      likeCount: r.likeCount,
      collectCount: r.collectCount,
      applyCount: r.applyCount,
      hot: r.hot,
      title: r.title,
      desc: r.desc,
      dataUrl: r.dataUrl,
      image: r.image,
      visibility: r.visibility,
      allowedParts: r.allowedParts,
      availableApplyParts: availableApplyParts,
      ...actionFlag,
    }
    return data
  }
}