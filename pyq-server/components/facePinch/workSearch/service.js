"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.searchWork = void 0;
const work_1 = require("../models/work");
const config_1 = require("../../../common/config");
class WorkSearchApiByMysql {
    search(params) {
        return __awaiter(this, void 0, void 0, function* () {
            const pagination = { page: params.page, pageSize: params.pageSize };
            const kw = params.kw;
            const idRange = yield this.getSearchIdRange();
            let query = work_1.WorkModel.publicScope().where((query) => {
                query
                    .orWhere((query) => {
                    query
                        .where((query) => {
                        query.orWhereBetween("id", idRange).orWhere("roleId", params.roleid);
                    })
                        .where((query) => {
                        query.orWhere("title", "like", `%${work_1.WorkModel.escapeLikeStr(kw)}%`);
                        query.orWhere("desc", "like", `%${work_1.WorkModel.escapeLikeStr(kw)}%`);
                    });
                })
                    .orWhere((query) => {
                    query.where("title", "=", kw);
                });
            });
            query = work_1.WorkModel.bodyShapeScope(query, params);
            const rows = yield work_1.WorkModel.powerQuery({
                initQuery: query,
                pagination,
                orderBy: [
                    ["hot", "id"],
                    ["desc", "desc"],
                ],
            });
            return rows;
        });
    }
    getSearchIdRange() {
        return __awaiter(this, void 0, void 0, function* () {
            const curMaxId = yield this.getCurrentMaxMomentId();
            const minId = Math.max(0, curMaxId - config_1.searchCfg.maxScanLimit);
            return [minId, curMaxId];
        });
    }
    getCurrentMaxMomentId() {
        return __awaiter(this, void 0, void 0, function* () {
            const query = work_1.WorkModel.scope().orderBy("id", "desc").limit(1);
            const rows = yield work_1.WorkModel.smartQuery(query);
            if (rows) {
                return rows[0].id || 0;
            }
            else {
                return 0;
            }
        });
    }
    static getInstance() {
        if (!this.instance) {
            this.instance = new this();
        }
        return this.instance;
    }
}
function searchWork(params) {
    return __awaiter(this, void 0, void 0, function* () {
        //OPTIMIZE:try other search engine, cause mysql not support full text search well
        return WorkSearchApiByMysql.getInstance().search(params);
    });
}
exports.searchWork = searchWork;
//# sourceMappingURL=service.js.map