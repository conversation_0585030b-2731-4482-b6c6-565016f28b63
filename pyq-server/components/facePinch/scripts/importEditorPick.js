#!/usr/bin/env ts-node
"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const path = require("path");
const models_1 = require("../../../models");
const work_1 = require("../models/work");
const modelProxy_1 = require("../../../models/modelProxy");
const logger_1 = require("../../../logger");
const auth_1 = require("../user/auth");
const constants_1 = require("../../../constants");
const operation_1 = require("../operation");
const _ = require("lodash");
const logger = (0, logger_1.clazzLogger)("scripts/importEditorPick");
const importUser = require(path.join(__dirname, "../../../../asserts/json/face_pinch/editor_user.json"));
const pickWorkList = require(path.join(__dirname, "../../../../asserts/json/face_pinch/editor_pick_work.json"));
const importProfile = importUser["pyq_profile"];
const importRoleInfo = importUser["qnm_roleinfo"];
function main() {
    return __awaiter(this, void 0, void 0, function* () {
        logger.info({ importProfile, importRoleInfo, workLen: pickWorkList.length }, "CheckImportUser");
        // process roleinfo
        const roleinfo = yield modelProxy_1.RoleInfoModel.findOne({ RoleId: importRoleInfo.RoleId });
        if (roleinfo && roleinfo.RoleId) {
            logger.info({ importRoleInfo, roleinfo }, "ImportRoleInfoExist");
        }
        else {
            const ret = yield modelProxy_1.RoleInfoModel.createOrUpdate(importRoleInfo);
            logger.info({ roleinfo, ret }, "ImportRoleInfoCreateOk");
        }
        // process profile
        const profile = yield models_1.ProfileModel.findOne({ RoleId: importProfile.RoleId });
        if (profile && profile.RoleId) {
            logger.info({ importProfile, profile }, "ImportProfileExist");
        }
        else {
            const ret = yield models_1.ProfileModel.createOrUpdate(importProfile);
            logger.info({ importProfile, ret }, "ImportRoleInfoCreateOk");
        }
        const account = roleinfo[constants_1.RoleInfoAccountCol];
        const curUser = yield auth_1.userInfoCache.get({ account, roleId: roleinfo.RoleId });
        logger.info({ user: curUser, account }, "importEditorFacePinchUser");
        if (!curUser) {
            throw new Error("face pinch use not exist");
        }
        for (const w of pickWorkList) {
            const addProp = {
                roleid: curUser.roleId,
                dataUrl: w.dataUrl,
                image: w.image,
                title: w.title,
                roleName: w.roleName,
                gender: w.gender,
                jobId: w.jobId,
                allowedParts: 7, // 默认允许所有部位
            };
            const ret = yield (0, operation_1.addFacePinchWork)(addProp, curUser);
            if (ret && ret.id) {
                yield work_1.WorkModel.updateById(ret.id, { hot: _.random(800, 1000) });
            }
            logger.info({ addProp, ret }, "importEditorFacePinchWorkOK");
        }
    });
}
main()
    .then(() => {
    logger.info("Done");
    process.exit(0);
})
    .catch((err) => {
    logger.error({ err }, "MainError");
    process.exit(-1);
});
//# sourceMappingURL=importEditorPick.js.map