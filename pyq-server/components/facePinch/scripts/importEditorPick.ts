#!/usr/bin/env ts-node

import path = require("path")
import { ProfileModel, ProfileRecord } from "../../../models"
import { QnmRoleInfoRecord } from "../../../../models/modelInterface"
import { WorkModel, WorkRecord } from "../models/work"
import { RoleInfoModel } from "../../../models/modelProxy"
import { clazzLogger } from "../../../logger"
import { userInfoCache } from "../user/auth"
import { RoleInfoAccountCol } from "../../../constants"
import { addFacePinchWork } from "../operation"
import _ = require("lodash")
const logger = clazzLogger("scripts/importEditorPick")

const importUser = require(path.join(__dirname, "../../../../asserts/json/face_pinch/editor_user.json"))
const pickWorkList: WorkRecord[] = require(path.join(
  __dirname,
  "../../../../asserts/json/face_pinch/editor_pick_work.json"
))

const importProfile: ProfileRecord = importUser["pyq_profile"]
const importRoleInfo: QnmRoleInfoRecord = importUser["qnm_roleinfo"]


async function main() {
  logger.info({ importProfile, importRoleInfo, workLen: pickWorkList.length }, "CheckImportUser")
  // process roleinfo
  const roleinfo = await RoleInfoModel.findOne({ RoleId: importRoleInfo.RoleId })
  if (roleinfo && roleinfo.RoleId) {
    logger.info({ importRoleInfo, roleinfo }, "ImportRoleInfoExist")
  } else {
    const ret = await RoleInfoModel.createOrUpdate(importRoleInfo)
    logger.info({ roleinfo, ret }, "ImportRoleInfoCreateOk")
  }

  // process profile
  const profile = await ProfileModel.findOne({ RoleId: importProfile.RoleId })
  if (profile && profile.RoleId) {
    logger.info({ importProfile, profile }, "ImportProfileExist")
  } else {
    const ret = await ProfileModel.createOrUpdate(importProfile)
    logger.info({ importProfile, ret }, "ImportRoleInfoCreateOk")
  }

  const account = roleinfo[RoleInfoAccountCol]
  const curUser = await userInfoCache.get({ account, roleId: roleinfo.RoleId })
  logger.info({ user: curUser, account }, "importEditorFacePinchUser")
  if (!curUser) {
    throw new Error("face pinch use not exist")
  }

  for (const w of pickWorkList) {
    const addProp = {
      roleid: curUser.roleId,
      dataUrl: w.dataUrl,
      image: w.image,
      title: w.title,
      roleName: w.roleName,
      gender: w.gender,
      jobId: w.jobId,
      allowedParts: 7, // 默认允许所有部位
    }
    const ret = await addFacePinchWork(addProp, curUser)
    if (ret && ret.id) {
      await WorkModel.updateById(ret.id, { hot: _.random(800, 1000) })
    }
    logger.info({ addProp, ret }, "importEditorFacePinchWorkOK")
  }
}

main()
  .then(() => {
    logger.info("Done")
    process.exit(0)
  })
  .catch((err) => {
    logger.error({ err }, "MainError")
    process.exit(-1)
  })
