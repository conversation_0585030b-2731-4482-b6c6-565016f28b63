import { error<PERSON><PERSON><PERSON> } from "../../../helper";
import { SESS_INFO } from "../../../types";
import { UserModel, UserRecord } from "../models/user";
import { <PERSON>ricCache, RedisLock } from "../../../../common/cacheUtil";
import { cacheKeyGen } from "../../../../common/util2";
import { facePinchCfg, redisCfg, testCfg } from "../../../common/config";
import { ApiErrors } from "../errorCode";
import { clazzLogger } from "../../../logger";
import { autoFixRoleInfo, getRoleName, inheritPreCreateUser } from "./service";
import * as sessionUtil from "../../../../common/session";
import * as authUtil from "../../../../common/auth";
import {
  FACE_PINCH_CLOUD_AUTH_LOGIN_TYPE as FACE_PINCH_CLOUD_ACCOUNT_ID_TYPE,
  MD_LOGIN_ROLE_ID_TYPE,
  ONE_DAY_SECONDS,
} from "../constant";
import { isAllowCloudGameGuestAccess } from "../operation";
import { FacePinchReq } from "../type";
import { errorCodes } from "../../../errorCodes";
import { hexMd5 } from "../../../../common/util";
import { AuthLoginIdType } from "../../../types/type";
import { nanoid } from "nanoid";
const logger = clazzLogger("facePinch/user/auth");

export async function getUserInfo(account: string, roleId: number): Promise<UserRecord> {
  const userInfo = await UserModel.getUserInfo(account, roleId);
  if (userInfo && userInfo.id) return userInfo;
  const lockKey = cacheKeyGen(`${redisCfg.prefix}face_pinch_user_info`, { account });
  // need add a distribute lock to prevent inherit not work
  const lockOwnerId = nanoid();
  try {
    const hasLock = await RedisLock.optimistic(lockKey, lockOwnerId, 2000, 5, 300);
    if (hasLock) {
      if (roleId === 0) return createNewUser(account, 0);
      const preCreateUser = await UserModel.getUserInfo(account, 0);
      if (!preCreateUser) return createNewUser(account, roleId);
      return inheritPreCreateUser(preCreateUser, roleId);
    } else {
      throw ApiErrors.UserLoginFrequency;
    }
  } finally {
    await RedisLock.unLock(lockKey, lockOwnerId);
  }
}

class UserInfoCacheClass extends GenericCache<{ account: string; roleId: number }, UserRecord> {
  getExpireTime() {
    return facePinchCfg.userCacheTime;
  }

  getKey(params: { account: string; roleId: number }): string {
    return cacheKeyGen(`${redisCfg.prefix}face_pinch_user`, { account: params.account, roleId: params.roleId });
  }

  fetchDataSource(params: { account: string; roleId: number }): Promise<UserRecord> {
    return getUserInfo(params.account, params.roleId);
  }
}

export const userInfoCache = new UserInfoCacheClass();

async function createNewUser(account: string, roleId: number) {
  const roleName = await getRoleName(roleId);
  const prop: Omit<UserRecord, "id"> = { account, roleId, roleName, createTime: Date.now() };
  const updateProp = { account, roleId };
  const ret = await UserModel.createOrUpdate(prop, updateProp);
  let userId = 0;
  if (ret.insertId > 0) {
    userId = ret.insertId;
  } else {
    const info = await UserModel.findOne({ account, roleId }, ["id"], { dbNode: "MASTER", maxScaleForceMaster: true });
    if (info && info.id) {
      userId = info.id;
    } else {
      logger.warn("FindUserAfterCreateUserError", { account, roleId });
      throw ApiErrors.UserLoginFrequency;
    }
  }
  logger.debug({ userId, prop }, "createNewUser");
  return { id: userId, ...prop };
}

export async function onLoginInfoInjector(req, res, next) {
  try {
    const isRouteSkipAuth = req.route.skipAuth || req.skipSkey;
    if (isRouteSkipAuth) {
      next();
      return;
    }
    const sessionInfo: SESS_INFO = req._SESS_INFO_ || {};
    if (sessionInfo.idType === FACE_PINCH_CLOUD_ACCOUNT_ID_TYPE) {
      const routePath = req.route.path;
      if (!isAllowCloudGameGuestAccess(routePath)) {
        throw ApiErrors.CloudGameUserAccessDenied;
      }
    }
    const roleId = getRoleIdFromSession(sessionInfo);
    let account = sessionInfo.account;
    // 梦岛未更新前捏脸签发skey依旧用的线上版本， account未被编码到skey中，需要额外的逻辑处理
    if (roleId > 0 && !account) {
      const redisSession = await authUtil.LoginSession.get(roleId);
      logger.warn({ roleId, account, redisSession }, "onLoginInfoInjectorNoAccountTryToFix");
      if (redisSession) {
        account = redisSession.account;
      }
    }
    if (!account) {
      logger.debug({ sessionInfo }, "onLoginInfoInjectorNoAccount");
      throw ApiErrors.UserNotLogin;
    } else {
      const userInfo = await userInfoCache.get({ account, roleId });
      autoFixRoleInfo(userInfo);
      req.params.curUser = userInfo;
      next();
    }
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

function getRoleIdFromSession(sessionInfo: SESS_INFO) {
  let roleId = 0;
  if (sessionInfo.idType === MD_LOGIN_ROLE_ID_TYPE) {
    if (sessionInfo.idVal) {
      const number = parseInt(sessionInfo.idVal, 10);
      if (Number.isInteger(number)) {
        roleId = number;
      }
    }
  }
  return roleId;
}

export async function facePinchAuthLogin(params: FacePinchReq.CloudGameAuthLogin): Promise<{ skey: string }> {
  checkAuthActionToken(params);
  const idType = AuthLoginIdType.FacePinchCloud;
  const idVal = params.account;
  const sessKey = authUtil.sessionId(idType, idVal);
  const time = params.time + "";
  const account = "cloud:" + params.account;
  await sessionUtil.set(sessKey, { time, account, roleId: params.roleid });
  await sessionUtil.setExpire(sessKey, ONE_DAY_SECONDS);
  const skey = authUtil.generateSkey({ idType, idVal, account, time });
  return { skey: skey };
}

function checkAuthActionToken(params: FacePinchReq.CloudGameAuthLogin) {
  const isAuthValid = isAuthActionTokenValid(params);
  if (!isAuthValid) {
    throw errorCodes.CheckTokenFailed;
  }
}

function isAuthActionTokenValid(params: FacePinchReq.CloudGameAuthLogin): boolean {
  const signPayload = [params.time, params.account, params.roleid].join("");
  const token = hexMd5(signPayload + facePinchCfg.cloudAuthLoginSalt);
  if (params.token != token) {
    logger.warn({ actual: params.token, expect: token, params, signPayload }, "AuthLoginWithInvalidToken");
    if (testCfg.skip_token_check) {
      return true;
    } else {
      return false;
    }
  }
  return true;
}
