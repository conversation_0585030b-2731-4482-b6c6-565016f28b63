"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FacePinchDataInherentOnEvent = exports.listenOnAuthLoginOk = void 0;
const constants_1 = require("../../../constants");
const auth_1 = require("./auth");
function listenOnAuthLoginOk(payload) {
    return __awaiter(this, void 0, void 0, function* () {
        if (payload.roleId && payload.loginParams.account) {
            // this will trigger data inherent for face pinch user
            yield auth_1.userInfoCache.get({ account: payload.loginParams.account, roleId: payload.roleId });
        }
    });
}
exports.listenOnAuthLoginOk = listenOnAuthLoginOk;
exports.FacePinchDataInherentOnEvent = [
    { name: constants_1.EventNames.AUTH_LOGIN$LOGIN_OK, handler: listenOnAuthLoginOk },
];
//# sourceMappingURL=onEvents.js.map