import { clazz<PERSON>ogger } from "../../../logger"
import { RoleInfoModel } from "../../../models/modelProxy"
import { UserModel, UserRecord } from "../models/user"
import { WorkModel, WorkRecord, getBodyShapeFromGenderAndJobId } from "../models/work"
import { AccusedRole } from "../userType"
const logger = clazzLogger("facePinch/user/service")

export async function getAccusedRole(userId: number): Promise<AccusedRole> {
  let ar: AccusedRole = {
    userId,
    roleId: 0,
    roleName: "",
    roleLevel: 0,
    server: 0,
    vip: 0,
  }
  const u = await UserModel.findOne({ id: userId })
  if (!u) return ar
  if (u.roleId === 0) return ar
  const r = await RoleInfoModel.findOne({ RoleId: u.roleId })
  if (!r) return ar
  ar = { ...ar, roleId: r.RoleId, roleName: r.RoleName, roleLevel: r.Level, server: r.ServerId, vip: r.VIP }
  return ar
}


export async function getRoleName(roleId: number) {
  if (roleId > 0) {
    const roleInfo = await RoleInfoModel.findOne({ RoleId: roleId })
    if (roleInfo) {
      return roleInfo.RoleName || ""
    }
  }
  return ""
}


export async function getInhereRoleInfo(
  roleId: number
): Promise<Pick<WorkRecord, "roleId" | "roleName">> {
  const prop = {
    roleId,
    roleName: "",
  }
  if (roleId > 0) {
    const roleInfo = await RoleInfoModel.findOne({ RoleId: roleId })
    if (roleInfo && roleInfo.RoleId) {
      prop.roleName = roleInfo.RoleName
    }
  }
  return prop
}



/**
 * this account and roleId login exist preCreateUser, login to this this and update roleId
 **/
export async function inheritPreCreateUser(preCreateUser: UserRecord, roleId: number) {
  const userId = preCreateUser.id
  const inherentInfo = await getInhereRoleInfo(roleId)
  const upProps = { roleId, roleName: inherentInfo.roleName }
  logger.info({ roleId, preCreateUser }, "InheritPreCreateUserData")
  await UserModel.updateByCondition({ id: userId }, upProps)
  await WorkModel.updateByCondition({ userId }, inherentInfo)
  return { ...preCreateUser, ...upProps }
}


export async function autoFixRoleInfo(userInfo: UserRecord) {
  try {
    if (!userInfo) return
    const userId = userInfo.id
    if (userInfo && userInfo.roleId > 0 && !userInfo.roleName) {
      const inherentInfo = await getInhereRoleInfo(userInfo.roleId)
      if (inherentInfo && inherentInfo.roleName) {
        await UserModel.updateByCondition({ id: userId }, { roleName: inherentInfo.roleName })
        await WorkModel.updateByCondition({ userId }, inherentInfo)
      }
    }
  } catch (err) {
    logger.error({ err, userInfo }, "AutoFixInheritRoleInfoFail")
  }
}
