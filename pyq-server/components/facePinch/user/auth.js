"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.facePinchAuthLogin = exports.onLoginInfoInjector = exports.userInfoCache = exports.getUserInfo = void 0;
const helper_1 = require("../../../helper");
const user_1 = require("../models/user");
const cacheUtil_1 = require("../../../../common/cacheUtil");
const util2_1 = require("../../../../common/util2");
const config_1 = require("../../../common/config");
const errorCode_1 = require("../errorCode");
const logger_1 = require("../../../logger");
const service_1 = require("./service");
const sessionUtil = require("../../../../common/session");
const authUtil = require("../../../../common/auth");
const constant_1 = require("../constant");
const operation_1 = require("../operation");
const errorCodes_1 = require("../../../errorCodes");
const util_1 = require("../../../../common/util");
const nanoid_1 = require("nanoid");
const logger = (0, logger_1.clazzLogger)("facePinch/user/auth");
function getUserInfo(account, roleId) {
    return __awaiter(this, void 0, void 0, function* () {
        const userInfo = yield user_1.UserModel.getUserInfo(account, roleId);
        if (userInfo && userInfo.id)
            return userInfo;
        const lockKey = (0, util2_1.cacheKeyGen)(`${config_1.redisCfg.prefix}face_pinch_user_info`, { account });
        // need add a distribute lock to prevent inherit not work
        const lockOwnerId = (0, nanoid_1.nanoid)();
        try {
            const hasLock = yield cacheUtil_1.RedisLock.optimistic(lockKey, lockOwnerId, 2000, 5, 300);
            if (hasLock) {
                if (roleId === 0)
                    return createNewUser(account, 0);
                const preCreateUser = yield user_1.UserModel.getUserInfo(account, 0);
                if (!preCreateUser)
                    return createNewUser(account, roleId);
                return (0, service_1.inheritPreCreateUser)(preCreateUser, roleId);
            }
            else {
                throw errorCode_1.ApiErrors.UserLoginFrequency;
            }
        }
        finally {
            yield cacheUtil_1.RedisLock.unLock(lockKey, lockOwnerId);
        }
    });
}
exports.getUserInfo = getUserInfo;
class UserInfoCacheClass extends cacheUtil_1.GenericCache {
    getExpireTime() {
        return config_1.facePinchCfg.userCacheTime;
    }
    getKey(params) {
        return (0, util2_1.cacheKeyGen)(`${config_1.redisCfg.prefix}face_pinch_user`, { account: params.account, roleId: params.roleId });
    }
    fetchDataSource(params) {
        return getUserInfo(params.account, params.roleId);
    }
}
exports.userInfoCache = new UserInfoCacheClass();
function createNewUser(account, roleId) {
    return __awaiter(this, void 0, void 0, function* () {
        const roleName = yield (0, service_1.getRoleName)(roleId);
        const prop = { account, roleId, roleName, createTime: Date.now() };
        const updateProp = { account, roleId };
        const ret = yield user_1.UserModel.createOrUpdate(prop, updateProp);
        let userId = 0;
        if (ret.insertId > 0) {
            userId = ret.insertId;
        }
        else {
            const info = yield user_1.UserModel.findOne({ account, roleId }, ["id"], { dbNode: "MASTER", maxScaleForceMaster: true });
            if (info && info.id) {
                userId = info.id;
            }
            else {
                logger.warn("FindUserAfterCreateUserError", { account, roleId });
                throw errorCode_1.ApiErrors.UserLoginFrequency;
            }
        }
        logger.debug({ userId, prop }, "createNewUser");
        return Object.assign({ id: userId }, prop);
    });
}
function onLoginInfoInjector(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const isRouteSkipAuth = req.route.skipAuth || req.skipSkey;
            if (isRouteSkipAuth) {
                next();
                return;
            }
            const sessionInfo = req._SESS_INFO_ || {};
            if (sessionInfo.idType === constant_1.FACE_PINCH_CLOUD_AUTH_LOGIN_TYPE) {
                const routePath = req.route.path;
                if (!(0, operation_1.isAllowCloudGameGuestAccess)(routePath)) {
                    throw errorCode_1.ApiErrors.CloudGameUserAccessDenied;
                }
            }
            const roleId = getRoleIdFromSession(sessionInfo);
            let account = sessionInfo.account;
            // 梦岛未更新前捏脸签发skey依旧用的线上版本， account未被编码到skey中，需要额外的逻辑处理
            if (roleId > 0 && !account) {
                const redisSession = yield authUtil.LoginSession.get(roleId);
                logger.warn({ roleId, account, redisSession }, "onLoginInfoInjectorNoAccountTryToFix");
                if (redisSession) {
                    account = redisSession.account;
                }
            }
            if (!account) {
                logger.debug({ sessionInfo }, "onLoginInfoInjectorNoAccount");
                throw errorCode_1.ApiErrors.UserNotLogin;
            }
            else {
                const userInfo = yield exports.userInfoCache.get({ account, roleId });
                (0, service_1.autoFixRoleInfo)(userInfo);
                req.params.curUser = userInfo;
                next();
            }
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.onLoginInfoInjector = onLoginInfoInjector;
function getRoleIdFromSession(sessionInfo) {
    let roleId = 0;
    if (sessionInfo.idType === constant_1.MD_LOGIN_ROLE_ID_TYPE) {
        if (sessionInfo.idVal) {
            const number = parseInt(sessionInfo.idVal, 10);
            if (Number.isInteger(number)) {
                roleId = number;
            }
        }
    }
    return roleId;
}
function facePinchAuthLogin(params) {
    return __awaiter(this, void 0, void 0, function* () {
        checkAuthActionToken(params);
        const idType = "face_pinch_cloud_account" /* FacePinchCloud */;
        const idVal = params.account;
        const sessKey = authUtil.sessionId(idType, idVal);
        const time = params.time + "";
        const account = "cloud:" + params.account;
        yield sessionUtil.set(sessKey, { time, account, roleId: params.roleid });
        yield sessionUtil.setExpire(sessKey, constant_1.ONE_DAY_SECONDS);
        const skey = authUtil.generateSkey({ idType, idVal, account, time });
        return { skey: skey };
    });
}
exports.facePinchAuthLogin = facePinchAuthLogin;
function checkAuthActionToken(params) {
    const isAuthValid = isAuthActionTokenValid(params);
    if (!isAuthValid) {
        throw errorCodes_1.errorCodes.CheckTokenFailed;
    }
}
function isAuthActionTokenValid(params) {
    const signPayload = [params.time, params.account, params.roleid].join("");
    const token = (0, util_1.hexMd5)(signPayload + config_1.facePinchCfg.cloudAuthLoginSalt);
    if (params.token != token) {
        logger.warn({ actual: params.token, expect: token, params, signPayload }, "AuthLoginWithInvalidToken");
        if (config_1.testCfg.skip_token_check) {
            return true;
        }
        else {
            return false;
        }
    }
    return true;
}
//# sourceMappingURL=auth.js.map