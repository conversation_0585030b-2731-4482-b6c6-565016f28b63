import { AuthEvent } from "../../../../service/qnm/pyq/auth"
import { EventNames } from "../../../constants"
import { EventHandleGroup } from "../../../types/type"
import { userInfoCache } from "./auth"

export async function listenOnAuthLoginOk(payload: AuthEvent.LoginOK) {
  if (payload.roleId && payload.loginParams.account) {
    // this will trigger data inherent for face pinch user
    await userInfoCache.get({ account: payload.loginParams.account, roleId: payload.roleId })
  }
}

export const FacePinchDataInherentOnEvent: EventHandleGroup = [
  { name: EventNames.AUTH_LOGIN$LOGIN_OK, handler: listenOnAuthLoginOk },
]