"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.autoFixRoleInfo = exports.inheritPreCreateUser = exports.getInhereRoleInfo = exports.getRoleName = exports.getAccusedRole = void 0;
const logger_1 = require("../../../logger");
const modelProxy_1 = require("../../../models/modelProxy");
const user_1 = require("../models/user");
const work_1 = require("../models/work");
const logger = (0, logger_1.clazzLogger)("facePinch/user/service");
function getAccusedRole(userId) {
    return __awaiter(this, void 0, void 0, function* () {
        let ar = {
            userId,
            roleId: 0,
            roleName: "",
            roleLevel: 0,
            server: 0,
            vip: 0,
        };
        const u = yield user_1.UserModel.findOne({ id: userId });
        if (!u)
            return ar;
        if (u.roleId === 0)
            return ar;
        const r = yield modelProxy_1.RoleInfoModel.findOne({ RoleId: u.roleId });
        if (!r)
            return ar;
        ar = Object.assign(Object.assign({}, ar), { roleId: r.RoleId, roleName: r.RoleName, roleLevel: r.Level, server: r.ServerId, vip: r.VIP });
        return ar;
    });
}
exports.getAccusedRole = getAccusedRole;
function getRoleName(roleId) {
    return __awaiter(this, void 0, void 0, function* () {
        if (roleId > 0) {
            const roleInfo = yield modelProxy_1.RoleInfoModel.findOne({ RoleId: roleId });
            if (roleInfo) {
                return roleInfo.RoleName || "";
            }
        }
        return "";
    });
}
exports.getRoleName = getRoleName;
function getInhereRoleInfo(roleId) {
    return __awaiter(this, void 0, void 0, function* () {
        const prop = {
            roleId,
            roleName: "",
        };
        if (roleId > 0) {
            const roleInfo = yield modelProxy_1.RoleInfoModel.findOne({ RoleId: roleId });
            if (roleInfo && roleInfo.RoleId) {
                prop.roleName = roleInfo.RoleName;
            }
        }
        return prop;
    });
}
exports.getInhereRoleInfo = getInhereRoleInfo;
/**
 * this account and roleId login exist preCreateUser, login to this this and update roleId
 **/
function inheritPreCreateUser(preCreateUser, roleId) {
    return __awaiter(this, void 0, void 0, function* () {
        const userId = preCreateUser.id;
        const inherentInfo = yield getInhereRoleInfo(roleId);
        const upProps = { roleId, roleName: inherentInfo.roleName };
        logger.info({ roleId, preCreateUser }, "InheritPreCreateUserData");
        yield user_1.UserModel.updateByCondition({ id: userId }, upProps);
        yield work_1.WorkModel.updateByCondition({ userId }, inherentInfo);
        return Object.assign(Object.assign({}, preCreateUser), upProps);
    });
}
exports.inheritPreCreateUser = inheritPreCreateUser;
function autoFixRoleInfo(userInfo) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            if (!userInfo)
                return;
            const userId = userInfo.id;
            if (userInfo && userInfo.roleId > 0 && !userInfo.roleName) {
                const inherentInfo = yield getInhereRoleInfo(userInfo.roleId);
                if (inherentInfo && inherentInfo.roleName) {
                    yield user_1.UserModel.updateByCondition({ id: userId }, { roleName: inherentInfo.roleName });
                    yield work_1.WorkModel.updateByCondition({ userId }, inherentInfo);
                }
            }
        }
        catch (err) {
            logger.error({ err, userInfo }, "AutoFixInheritRoleInfoFail");
        }
    });
}
exports.autoFixRoleInfo = autoFixRoleInfo;
//# sourceMappingURL=service.js.map