"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FacePinchWeekHotOnEvents = exports.WeekHotEventHandlerClass = void 0;
const constants_1 = require("../../../constants");
const weekHot_1 = require("../models/weekHot");
const service_1 = require("./service");
class WeekHotEventHandlerClass {
    onIncrWeekHotAction(payload) {
        return __awaiter(this, void 0, void 0, function* () {
            const { work, isFirstTime, ts } = payload;
            if (!isFirstTime)
                return;
            const week = service_1.WeekHotService.weekDateStr(ts);
            const id = yield weekHot_1.WeekHotModel.createIfNotExist(work, week);
            if (id && id > 0) {
                return weekHot_1.WeekHotModel.increment({ id }, "hot");
            }
        });
    }
    onLikeAdd(payload) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.onIncrWeekHotAction(payload);
        });
    }
    onCollectAdd(payload) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.onIncrWeekHotAction(payload);
        });
    }
    onApplyAdd(payload) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.onIncrWeekHotAction(payload);
        });
    }
    onDel(payload) {
        return __awaiter(this, void 0, void 0, function* () {
            const { work } = payload;
            if (work) {
                return weekHot_1.WeekHotModel.delete({ workId: work.id });
            }
            else {
                return false;
            }
        });
    }
}
exports.WeekHotEventHandlerClass = WeekHotEventHandlerClass;
const h = new WeekHotEventHandlerClass();
exports.FacePinchWeekHotOnEvents = [
    { name: constants_1.EventNames.FACE_PINCH_WORK$LIKE_ADD, handler: h.onLikeAdd.bind(h) },
    { name: constants_1.EventNames.FACE_PINCH_WORK$COLLECT_ADD, handler: h.onCollectAdd.bind(h) },
    { name: constants_1.EventNames.FACE_PINCH_WORK$APPLY_ADD, handler: h.onApplyAdd.bind(h) },
    { name: constants_1.EventNames.FACE_PINCH_WORK$DEL, handler: h.onDel.bind(h) },
];
//# sourceMappingURL=onEvents.js.map