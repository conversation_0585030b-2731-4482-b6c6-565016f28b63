import { EventNames } from "../../../constants"
import { EventHandleGroup } from "../../../types/type"
import { IWorkEventHandler, WorkEvent } from "../events/events"
import { WeekHotModel } from "../models/weekHot"
import { WeekHotService } from "./service"

export class WeekHotEventHandlerClass implements IWorkEventHandler {
  private async onIncrWeekHotAction(payload: WorkEvent.WorkAction) {
    const { work, isFirstTime, ts } = payload;
    if (!isFirstTime) return;
    const week = WeekHotService.weekDateStr(ts);
    const id = await WeekHotModel.createIfNotExist(work, week);
    if (id && id > 0) {
      return WeekHotModel.increment({ id }, "hot");
    }
  }

  async onLikeAdd(payload: WorkEvent.LikeAdd) {
    return this.onIncrWeekHotAction(payload);
  }

  async onCollectAdd(payload: WorkEvent.CollectAdd) {
    return this.onIncrWeekHotAction(payload);
  }

  async onApplyAdd(payload: WorkEvent.ApplyAdd) {
    return this.onIncrWeekHotAction(payload);
  }

  async onDel(payload: WorkEvent.Del) {
    const { work } = payload;
    if (work) {
      return WeekHotModel.delete({ workId: work.id });
    } else {
      return false;
    }
  }
}

const h = new WeekHotEventHandlerClass()

export const FacePinchWeekHotOnEvents: EventHandleGroup = [
  { name: EventNames.FACE_PINCH_WORK$LIKE_ADD, handler: h.onLikeAdd.bind(h) },
  { name: EventNames.FACE_PINCH_WORK$COLLECT_ADD, handler: h.onCollectAdd.bind(h) },
  { name: EventNames.FACE_PINCH_WORK$APPLY_ADD, handler: h.onApplyAdd.bind(h) },
  { name: EventNames.FACE_PINCH_WORK$DEL, handler: h.onDel.bind(h) },
]