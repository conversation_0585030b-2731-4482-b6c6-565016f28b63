"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeekHotService = void 0;
const date_fns_1 = require("date-fns");
const weekHot_1 = require("../models/weekHot");
const work_1 = require("../models/work");
const service_1 = require("../work/service");
const facePartFilterService_1 = require("../services/facePartFilterService");
const errorCode_1 = require("../errorCode");
class WeekHotService {
    static getPublicList(params) {
        return __awaiter(this, void 0, void 0, function* () {
            const userId = params.curUser.id;
            // 验证部位筛选参数
            const validation = facePartFilterService_1.FacePartFilterService.validateAllowedParts(params.allowedParts);
            if (!validation.valid) {
                throw errorCode_1.ApiErrors.InvalidPartsSelection;
            }
            const curDate = params.date ? new Date(params.date) : new Date();
            const lastWeekToday = (0, date_fns_1.subDays)(curDate, 7);
            const ids = yield this.getHotIds(lastWeekToday, params);
            const rows = yield work_1.WorkModel.getPublicByIds(ids);
            const list = yield service_1.WorkService.formatListRes(rows, userId);
            const data = { list };
            return data;
        });
    }
    static weekDateStr(date) {
        const week = (0, date_fns_1.format)((0, date_fns_1.startOfWeek)(date, { weekStartsOn: 1 }), "yyyy-MM-dd");
        return week;
    }
    static getHotIds(date, params) {
        return __awaiter(this, void 0, void 0, function* () {
            let query = weekHot_1.WeekHotModel.scope().where("week", this.weekDateStr(date));
            query = work_1.WorkModel.bodyShapeScope(query, params);
            // 应用部位筛选 - 需要通过JOIN到work表来筛选allowedParts字段
            const hasPartsFilter = params.allowedParts && facePartFilterService_1.FacePartFilterService.validateAllowedParts(params.allowedParts).valid;
            if (hasPartsFilter) {
                const validAllowedParts = facePartFilterService_1.FacePartFilterService.getValidAllowedPartsValues(params.allowedParts);
                const limit = params.pageSize || 10;
                const offset = ((params.page || 1) - 1) * limit;
                query = query
                    .join('pyq_face_pinch_work as work', 'pyq_face_pinch_week_hot.workId', 'work.id')
                    .whereIn('work.allowedParts', validAllowedParts)
                    .orderBy('pyq_face_pinch_week_hot.hot', 'desc')
                    .orderBy('pyq_face_pinch_week_hot.id', 'desc')
                    .select('pyq_face_pinch_week_hot.workId')
                    .limit(limit)
                    .offset(offset);
                const rows = yield weekHot_1.WeekHotModel.executeByQuery(query);
                return rows.map((r) => r.workId);
            }
            // 无部位筛选时使用原有的powerQuery方法
            const rows = yield weekHot_1.WeekHotModel.powerQuery({
                initQuery: query,
                select: ["workId"],
                orderBy: [
                    ["hot", "id"],
                    ["desc", "desc"],
                ],
                pagination: { page: params.page, pageSize: params.pageSize },
            });
            return rows.map((r) => r.workId);
        });
    }
}
exports.WeekHotService = WeekHotService;
//# sourceMappingURL=service.js.map