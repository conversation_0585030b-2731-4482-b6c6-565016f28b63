"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FacePinchUserCollectCntOnEvents = exports.UserCollectCountEventHandlerClass = void 0;
const constants_1 = require("../../../constants");
const service_1 = require("./service");
class UserCollectCountEventHandlerClass {
    onCollectAdd(payload) {
        return __awaiter(this, void 0, void 0, function* () {
            return service_1.UserCollectWorkCountCache.del(payload.roleId);
        });
    }
    onCollectDel(payload) {
        return __awaiter(this, void 0, void 0, function* () {
            return service_1.UserCollectWorkCountCache.del(payload.roleId);
        });
    }
}
exports.UserCollectCountEventHandlerClass = UserCollectCountEventHandlerClass;
const h = new UserCollectCountEventHandlerClass();
exports.FacePinchUserCollectCntOnEvents = [
    { name: constants_1.EventNames.FACE_PINCH_WORK$COLLECT_ADD, handler: h.onCollectAdd.bind(h) },
    { name: constants_1.EventNames.FACE_PINCH_WORK$COLLECT_DEL, handler: h.onCollectDel.bind(h) },
];
//# sourceMappingURL=onEvent.js.map