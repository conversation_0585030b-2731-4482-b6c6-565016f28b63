import { cacheKeyGen } from "../../../../common/util2";
import { GenericCache } from "../../../common/cacheUtil";
import { facePinchCfg } from "../../../common/config";
import { CollectModel } from "../models/collect";

export class UserCollectWorkCountCacheClass extends GenericCache<number, number> {
  getExpireTime() {
    return 3
  }

  getKey(userId: number): string {
    return cacheKeyGen("face_pinch:user_collect_work_count", { userId });
  }

  async fetchDataSource(userId: number): Promise<number> {
    const cnt = await CollectModel.getCollectCount(userId);
    return cnt;
  }
}

export const UserCollectWorkCountCache = new UserCollectWorkCountCacheClass()

export class UserCollectService {
  static async getCollectCount(userId: number) {
    return UserCollectWorkCountCache.get(userId);
  }

  static async hasCollectQuota(userId: number) {
    const count = await this.getCollectCount(userId);
    return count < facePinchCfg.maxCollectPerRoleId;
  }

  static async getUsage(userId: number) {
    const count = await this.getCollectCount(userId);
    const max = facePinchCfg.maxCollectPerRoleId;
    const current = Math.min(count, max);
    return { current, max };
  }
}