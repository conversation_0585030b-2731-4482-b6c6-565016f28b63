import { EventNames } from "../../../constants";
import { EventHandleGroup } from "../../../types/type";
import { IWorkEventHandler, WorkEvent } from "../events/events";
import { UserCollectWorkCountCache } from "./service";

export class UserCollectCountEventHandlerClass implements IWorkEventHandler {
  async onCollectAdd(payload: WorkEvent.CollectAdd) {
    return UserCollectWorkCountCache.del(payload.roleId);
  }

  async onCollectDel(payload: WorkEvent.CollectDel) {
    return UserCollectWorkCountCache.del(payload.roleId);
  }
}

const h = new UserCollectCountEventHandlerClass();

export const FacePinchUserCollectCntOnEvents: EventHandleGroup = [
  { name: EventNames.FACE_PINCH_WORK$COLLECT_ADD, handler: h.onCollectAdd.bind(h) },
  { name: EventNames.FACE_PINCH_WORK$COLLECT_DEL, handler: h.onCollectDel.bind(h) },
];
