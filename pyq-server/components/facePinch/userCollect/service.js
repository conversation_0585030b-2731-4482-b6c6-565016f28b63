"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserCollectService = exports.UserCollectWorkCountCache = exports.UserCollectWorkCountCacheClass = void 0;
const util2_1 = require("../../../../common/util2");
const cacheUtil_1 = require("../../../common/cacheUtil");
const config_1 = require("../../../common/config");
const collect_1 = require("../models/collect");
class UserCollectWorkCountCacheClass extends cacheUtil_1.GenericCache {
    getExpireTime() {
        return 3;
    }
    getKey(userId) {
        return (0, util2_1.cacheKeyGen)("face_pinch:user_collect_work_count", { userId });
    }
    fetchDataSource(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            const cnt = yield collect_1.CollectModel.getCollectCount(userId);
            return cnt;
        });
    }
}
exports.UserCollectWorkCountCacheClass = UserCollectWorkCountCacheClass;
exports.UserCollectWorkCountCache = new UserCollectWorkCountCacheClass();
class UserCollectService {
    static getCollectCount(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            return exports.UserCollectWorkCountCache.get(userId);
        });
    }
    static hasCollectQuota(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            const count = yield this.getCollectCount(userId);
            return count < config_1.facePinchCfg.maxCollectPerRoleId;
        });
    }
    static getUsage(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            const count = yield this.getCollectCount(userId);
            const max = config_1.facePinchCfg.maxCollectPerRoleId;
            const current = Math.min(count, max);
            return { current, max };
        });
    }
}
exports.UserCollectService = UserCollectService;
//# sourceMappingURL=service.js.map