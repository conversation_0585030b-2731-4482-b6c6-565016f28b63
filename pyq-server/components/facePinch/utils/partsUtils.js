"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.violatesLegacyDataRestriction = exports.isPartsAllowed = exports.getAvailableApplyParts = exports.isLegacyData = exports.getPartsDisplayNames = exports.validatePartsSelection = void 0;
const constant_1 = require("../constant");
const config_1 = require("../../../common/config");
/**
 * 验证部位选择的有效性
 * @param allowedParts 允许的部位值
 * @returns 是否有效
 */
function validatePartsSelection(allowedParts) {
    // 必须大于0且不能超过所有部位的组合值(7)
    return allowedParts > 0 && allowedParts <= (constant_1.FACE_PARTS.SHAPE | constant_1.FACE_PARTS.MAKEUP | constant_1.FACE_PARTS.FULL_FACE);
}
exports.validatePartsSelection = validatePartsSelection;
/**
 * 获取部位的显示名称
 * @param parts 部位值
 * @returns 部位名称数组
 */
function getPartsDisplayNames(parts) {
    const names = [];
    if (parts & constant_1.FACE_PARTS.SHAPE)
        names.push(constant_1.FACE_PARTS_NAMES[constant_1.FACE_PARTS.SHAPE]);
    if (parts & constant_1.FACE_PARTS.MAKEUP)
        names.push(constant_1.FACE_PARTS_NAMES[constant_1.FACE_PARTS.MAKEUP]);
    if (parts & constant_1.FACE_PARTS.FULL_FACE)
        names.push(constant_1.FACE_PARTS_NAMES[constant_1.FACE_PARTS.FULL_FACE]);
    return names;
}
exports.getPartsDisplayNames = getPartsDisplayNames;
/**
 * 判断是否为历史数据（在部位功能上线前创建）
 * @param createTime 创建时间戳（毫秒）
 * @returns 是否为历史数据
 */
function isLegacyData(createTime) {
    const launchTime = new Date(config_1.facePinchCfg.partsFeatureLaunchTime).getTime();
    return createTime < launchTime;
}
exports.isLegacyData = isLegacyData;
/**
 * 计算作品的可用应用部位（考虑历史数据限制）
 * @param allowedParts 作品允许的部位
 * @param createTime 作品创建时间
 * @returns 可用部位名称数组
 */
function getAvailableApplyParts(allowedParts, createTime) {
    if (isLegacyData(createTime)) {
        // 历史数据不能单独应用塑形，移除塑形部位
        const availableParts = allowedParts & ~constant_1.FACE_PARTS.SHAPE;
        return getPartsDisplayNames(availableParts);
    }
    return getPartsDisplayNames(allowedParts);
}
exports.getAvailableApplyParts = getAvailableApplyParts;
/**
 * 检查请求的部位是否被作品允许
 * @param allowedParts 作品允许的部位
 * @param requestedParts 请求的部位
 * @returns 是否被允许
 */
function isPartsAllowed(allowedParts, requestedParts) {
    return (allowedParts & requestedParts) === requestedParts;
}
exports.isPartsAllowed = isPartsAllowed;
/**
 * 检查历史数据的部位使用限制
 * @param createTime 作品创建时间
 * @param requestedParts 请求的部位
 * @returns 是否违反历史数据限制
 */
function violatesLegacyDataRestriction(createTime, requestedParts) {
    return isLegacyData(createTime) && (requestedParts & constant_1.FACE_PARTS.SHAPE) > 0;
}
exports.violatesLegacyDataRestriction = violatesLegacyDataRestriction;
//# sourceMappingURL=partsUtils.js.map