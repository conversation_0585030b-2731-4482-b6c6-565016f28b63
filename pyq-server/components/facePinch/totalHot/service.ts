import { WorkModel } from "../models/work"
import { FacePinchReq, FacePinchRes } from "../type"
import { FacePinchCurUser } from "../userType"
import { WorkService } from "../work/service"
import { FacePartFilterService } from "../services/facePartFilterService"
import { ApiErrors } from "../errorCode"

export class TotalHotService {
  static async getPublicList(
    params: FacePinchReq.WorkListPublic & FacePinchCurUser
  ): Promise<FacePinchRes.WorkListPublic> {
    const userId = params.curUser.id

    // 验证部位筛选参数
    const validation = FacePartFilterService.validateAllowedParts(params.allowedParts);
    if (!validation.valid) {
      throw ApiErrors.InvalidPartsSelection;
    }

    const ids = await this.getHotIds(params)
    const rows = await WorkModel.getPublicByIds(ids)
    const list = await WorkService.formatListRes(rows, userId)
    const data: FacePinchRes.WorkListPublic = { list }
    return data
  }

  static async getHotIds(params: FacePinchReq.WorkListPublic) {
    let query = WorkModel.publicScope()
    query = WorkModel.bodyShapeScope(query, params)

    // 应用部位筛选
    query = FacePartFilterService.buildFilterQuery(query, params.allowedParts);

    const rows = await WorkModel.powerQuery({
      initQuery: query,
      select: ["id"],
      orderBy: [
        ["hot", "id"],
        ["desc", "desc"],
      ],
      pagination: { page: params.page, pageSize: params.pageSize },
    })
    return rows.map((r) => r.id)
  }
}