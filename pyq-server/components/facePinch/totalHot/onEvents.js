"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FacePinchTotalHotOnEvents = exports.TotalHotEventHandlerClass = void 0;
const constants_1 = require("../../../constants");
const work_1 = require("../models/work");
class TotalHotEventHandlerClass {
    onLikeAdd(payload) {
        const { work, isFirstTime } = payload;
        if (!isFirstTime)
            return;
        return work_1.WorkModel.incrManyFields({ id: work.id }, ["likeCount", "hot"]);
    }
    onCollectAdd(payload) {
        const { work, isFirstTime } = payload;
        if (!isFirstTime)
            return;
        return work_1.WorkModel.incrManyFields({ id: work.id }, ["collectCount", "hot"]);
    }
    onApplyAdd(payload) {
        const { work, isFirstTime } = payload;
        if (!isFirstTime)
            return;
        return work_1.WorkModel.incrManyFields({ id: work.id }, ["applyCount", "hot"]);
    }
}
exports.TotalHotEventHandlerClass = TotalHotEventHandlerClass;
const TotalHotEventHandler = new TotalHotEventHandlerClass();
exports.FacePinchTotalHotOnEvents = [
    { name: constants_1.EventNames.FACE_PINCH_WORK$LIKE_ADD, handler: TotalHotEventHandler.onLikeAdd },
    { name: constants_1.EventNames.FACE_PINCH_WORK$COLLECT_ADD, handler: TotalHotEventHandler.onCollectAdd },
    { name: constants_1.EventNames.FACE_PINCH_WORK$APPLY_ADD, handler: TotalHotEventHandler.onApplyAdd },
];
//# sourceMappingURL=onEvents.js.map