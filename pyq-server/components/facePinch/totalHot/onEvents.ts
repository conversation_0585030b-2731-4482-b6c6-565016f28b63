import { EventNames } from "../../../constants"
import { EventHandleGroup } from "../../../types/type"
import { WorkModel } from "../models/work"
import { IWorkEventHandler, WorkEvent } from "../events/events"

export class TotalHotEventHandlerClass implements IWorkEventHandler {
  onLikeAdd(payload: WorkEvent.LikeAdd) {
    const { work, isFirstTime } = payload
    if (!isFirstTime) return
    return WorkModel.incrManyFields({ id: work.id }, ["likeCount", "hot"])
  }
  onCollectAdd(payload: WorkEvent.CollectAdd) {
    const { work, isFirstTime } = payload
    if (!isFirstTime) return
    return WorkModel.incrManyFields({ id: work.id }, ["collectCount", "hot"])
  }
  onApplyAdd(payload: WorkEvent.ApplyAdd) {
    const { work, isFirstTime } = payload
    if (!isFirstTime) return
    return WorkModel.incrManyFields({ id: work.id }, ["applyCount", "hot"])
  }
}

const TotalHotEventHandler = new TotalHotEventHandlerClass()

export const FacePinchTotalHotOnEvents: EventHandleGroup = [
  { name: EventNames.FACE_PINCH_WORK$LIKE_ADD, handler: TotalHotEventHandler.onLikeAdd },
  { name: EventNames.FACE_PINCH_WORK$COLLECT_ADD, handler: TotalHotEventHandler.onCollectAdd },
  { name: EventNames.FACE_PINCH_WORK$APPLY_ADD, handler: TotalHotEventHandler.onApplyAdd },
]