"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TotalHotService = void 0;
const work_1 = require("../models/work");
const service_1 = require("../work/service");
const facePartFilterService_1 = require("../services/facePartFilterService");
const errorCode_1 = require("../errorCode");
class TotalHotService {
    static getPublicList(params) {
        return __awaiter(this, void 0, void 0, function* () {
            const userId = params.curUser.id;
            // 验证部位筛选参数
            const validation = facePartFilterService_1.FacePartFilterService.validateAllowedParts(params.allowedParts);
            if (!validation.valid) {
                throw errorCode_1.ApiErrors.InvalidPartsSelection;
            }
            const ids = yield this.getHotIds(params);
            const rows = yield work_1.WorkModel.getPublicByIds(ids);
            const list = yield service_1.WorkService.formatListRes(rows, userId);
            const data = { list };
            return data;
        });
    }
    static getHotIds(params) {
        return __awaiter(this, void 0, void 0, function* () {
            let query = work_1.WorkModel.publicScope();
            query = work_1.WorkModel.bodyShapeScope(query, params);
            // 应用部位筛选
            query = facePartFilterService_1.FacePartFilterService.buildFilterQuery(query, params.allowedParts);
            const rows = yield work_1.WorkModel.powerQuery({
                initQuery: query,
                select: ["id"],
                orderBy: [
                    ["hot", "id"],
                    ["desc", "desc"],
                ],
                pagination: { page: params.page, pageSize: params.pageSize },
            });
            return rows.map((r) => r.id);
        });
    }
}
exports.TotalHotService = TotalHotService;
//# sourceMappingURL=service.js.map