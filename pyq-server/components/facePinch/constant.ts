import { apiPrefix } from "../../constants";
import { Lang } from "../../types/type";

export const enum EVisibility {
  Public = 0,
  Private = 1,
}

export const enum EWorkType {
  Normal = 0,
  /** 纯分享生成二维码，内容不可检索*/
  PURE_SHARE = 1,
}

// 游客模式登录
export const FACE_PINCH_CLOUD_AUTH_LOGIN_TYPE = "face_pinch_cloud_account";

// 梦岛登录使用的idType
export const MD_LOGIN_ROLE_ID_TYPE = "role";

export const GUEST_MODE_ALLOW_API_SET = new Set([
  apiPrefix + "/face_pinch/get_fp_token",
  apiPrefix + "/face_pinch/work/add",
  apiPrefix + "/face_pinch/work/get_by_share_id",
]);

export const ONE_DAY_SECONDS = 86400; // 1天，单位秒

export const enum EGender {
  MALE = 0,
  FEMALE = 1,
}

export const enum EJobId {
  /** 甲士 */
  JIA_SHI = 2,
  /** 侠客*/
  XIA_KE = 4,
  /**异人 */
  YI_REN = 8,
  /** 画魂 */
  HUA_HUN = 10,
}

/** 脸部部位定义，使用位运算进行组合 */
export const FACE_PARTS = {
  /** 塑形 */
  SHAPE: 1,
  /** 妆容 */
  MAKEUP: 2,
  /** 全脸 */
  FULL_FACE: 4,
} as const;

/** 脸部部位名称映射 */
export const FACE_PARTS_NAMES = {
  [FACE_PARTS.SHAPE]: 'Shape',
  [FACE_PARTS.MAKEUP]: 'Makeup',
  [FACE_PARTS.FULL_FACE]: 'FullFace',
} as const;


export type AvailableApplyPart = "Shape" | "Makeup" | "FullFace"