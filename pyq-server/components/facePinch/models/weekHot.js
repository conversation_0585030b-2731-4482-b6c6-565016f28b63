"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeekHotModel = exports.WorkHotWeekCols = void 0;
const nanoid_1 = require("nanoid");
const cacheUtil_1 = require("../../../../common/cacheUtil");
const util2_1 = require("../../../../common/util2");
const cacheUtil_2 = require("../../../common/cacheUtil");
const baseModel_1 = require("../../../models/baseModel");
const logger_1 = require("../../../logger");
exports.WorkHotWeekCols = ["id", "week", "workId", "gender", "jobId", "hot"];
class WorkWeekHotIdCacheClass extends cacheUtil_2.GenericCache {
    getKey(params, opt) {
        return (0, util2_1.cacheKeyGen)("face_pinch_week_hot_work", { workId: params.workId, week: params.week });
    }
    getExpireTime(params) {
        return 5 * 60;
    }
    fetchDataSource(params, opt) {
        return __awaiter(this, void 0, void 0, function* () {
            const record = yield exports.WeekHotModel.findOne({ workId: params.workId, week: params.week });
            if (record && record.id) {
                return record.id;
            }
            else {
                return 0;
            }
        });
    }
    updateWeekHotId(params, content) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.writeToCache(params, content);
        });
    }
}
const WorkWeekHotIdCache = new WorkWeekHotIdCacheClass();
class WeekHotModelClass extends baseModel_1.BaseModelClass {
    constructor() {
        super("pyq_face_pinch_week_hot");
    }
    createIfNotExist(work, week) {
        return __awaiter(this, void 0, void 0, function* () {
            const workWeekHotId = yield WorkWeekHotIdCache.get({ workId: work.id, week });
            if (workWeekHotId > 0)
                return workWeekHotId;
            const workHotProp = {
                workId: work.id,
                userId: work.userId,
                roleId: work.roleId,
                gender: work.gender,
                jobId: work.jobId,
                week,
                hot: 0,
            };
            const recordWeekHotLockKey = `pyq_face_pinch_week_hot_work:${work.id}:week:${week}`;
            const lockOwnerId = (0, nanoid_1.nanoid)();
            try {
                const hasLock = yield cacheUtil_1.RedisLock.optimistic(recordWeekHotLockKey, lockOwnerId, 1000, 3, 50);
                if (hasLock) {
                    const id = yield exports.WeekHotModel.insert(workHotProp);
                    yield WorkWeekHotIdCache.updateWeekHotId({ workId: work.id, week }, id);
                    return id;
                }
                else {
                    logger_1.logger.warn({ work, week, lockOwnerId }, "WeekHotModelGetLockFailed");
                    return 0;
                }
            }
            catch (err) {
                logger_1.logger.info({ err, work, week, lockOwnerId }, "WeekHotModelCreateFailed");
            }
            finally {
                yield cacheUtil_1.RedisLock.unLock(recordWeekHotLockKey, lockOwnerId);
            }
        });
    }
}
exports.WeekHotModel = new WeekHotModelClass();
//# sourceMappingURL=weekHot.js.map