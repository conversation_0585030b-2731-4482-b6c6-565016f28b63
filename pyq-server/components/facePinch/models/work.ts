import type { QueryBuilder } from "knex"
import { EAuditStatues, EStatuses, EVisibility } from "../../../../common/constants"
import { Condition } from "../../../../models/baseModel2"
import { facePinchCfg } from "../../../common/config"
import { BaseModelClass } from "../../../models/baseModel"
import { EGender, EJobId, EWorkType } from "../constant"
import { ApiErrors } from "../errorCode"

export const enum WorkVisibility {
  Public = 0,
  Private = 1,
}

/** 倩女手游捏脸作品 */
export interface WorkRecord {
  id: number
  /** 用户id */
  userId: number
  /** 角色id */
  roleId: number
  /** 角色名字 */
  roleName: string
  /** 账号 */
  account: string
  /** 捏脸数据对应的url */
  dataUrl: string
  /** 作品大图 游戏内直接传来的一张 */
  image: string
  /** 标题 */
  title: string
  /** 描述 */
  desc: string
  /** 分享后的分享Id */
  shareId: string
  /** 作品所能应用体型 */
  bodyShape: EBodyShape
  /** 允许使用的部位：1=塑形，2=妆容，4=全脸，可组合使用位运算 */
  allowedParts: number
  /** 作品所能应用性别 */
  gender: number
  /** 作品所能应用职业id */
  jobId: number
  /** 作品类型 0 => 标准 1 => (不出现在作品列表中, 只是为了生成二维码数据) */
  workType: 0 | 1
  /** 公开级别， 0 => 公开 1 => 私有 */
  visibility: 0 | 1
  /** 热度 */
  hot: number
  /** 点赞数 */
  likeCount: number
  /** 收藏数 */
  collectCount: number
  /** 被使用数 */
  applyCount: number
  /** 审核状态 0 => 审核中, -1 => 未通过, 1 => 通过 */
  auditStatus: 0 | -1 | 1
  /** 作品状态：0 正常 -1 已删除 */
  status: 0 | -1
  /** 创建时间 */
  createTime: number
}

export type WorkCol = keyof WorkRecord

export const WorkCols: WorkCol[] = [
  "id",
  "roleId",
  "roleName",
  "account",
  "dataUrl",
  "image",
  "title",
  "desc",
  "shareId",
  "bodyShape",
  "allowedParts",
  "gender",
  "jobId",
  "workType",
  "visibility",
  "hot",
  "likeCount",
  "collectCount",
  "applyCount",
  "status",
  "createTime",
]

export const enum EBodyShape {
  /** 未知 */
  UNKNOWN = -1,
  /** 成年男性 */
  ADULT_MALE = 0,
  /** 成年女性 */
  ADULT_FEMALE = 1,
  /** 壮男 */
  STRONG_MALE = 2,
  /** 萝莉 */
  LOLITA = 3,
}

export function getBodyShapeFromGenderAndJobId(gender: number, jobId: number): EBodyShape {
  if (jobId <=0 ) return EBodyShape.UNKNOWN
  if (gender === EGender.MALE) {
    if (jobId === EJobId.JIA_SHI || jobId === EJobId.XIA_KE) {
      return EBodyShape.STRONG_MALE
    } else {
      return EBodyShape.ADULT_MALE
    }
  } else {
    if (jobId === EJobId.YI_REN || jobId === EJobId.HUA_HUN) {
      return EBodyShape.LOLITA
    } else {
      return EBodyShape.ADULT_FEMALE
    }
  }
}

class WorkModelClass extends BaseModelClass<WorkRecord> {
  constructor() {
    super("pyq_face_pinch_work")
  }

  normalCondition(): Condition<WorkRecord> {
    return {
      status: EStatuses.Normal,
      workType: EWorkType.Normal,
    }
  }

  shareViewCondition(shareId: string): Condition<WorkRecord> {
    return {
      shareId,
      status: EStatuses.Normal,
      auditStatus: EAuditStatues.PASS,
      visibility: EVisibility.Public,
    }
  }

  importCondition(id: number): Condition<WorkRecord> {
    return {
      id,
      status: EStatuses.Normal,
      auditStatus: EAuditStatues.PASS,
      visibility: EVisibility.Public,
    }
  }

  selfViewCondition(userId: number): Condition<WorkRecord> {
    return {
      userId,
      status: EStatuses.Normal,
      auditStatus: EAuditStatues.PASS,
      workType: EWorkType.Normal,
    }
  }

  publicCondition(): Condition<WorkRecord> {
    return {
      ...this.normalCondition(),
      workType: EWorkType.Normal,
      visibility: EVisibility.Public,
    }
  }

  collectCondition(): Condition<WorkRecord> {
    return {
      status: EStatuses.Normal,
      visibility: EVisibility.Public,
    }
  }

  normalScope() {
    return this.scope().where(this.normalCondition())
  }

  publicScope() {
    return this.scope().where(this.publicCondition())
  }

  collectScope() {
    return this.scope().where(this.collectCondition())
  }

  selfScope(curUserId: number) {
    return this.scope().where(this.selfViewCondition(curUserId))
  }

  async getPublicByIds(ids: number[]) {
    const rows = await this.find({
      id: ids,
      ...this.publicCondition(),
    })
    rows.sort((a, b) => ids.indexOf(a.id) - ids.indexOf(b.id))
    return rows
  }

  async findByShareId(shareId: string) {
    return this.findOne(this.shareViewCondition(shareId))
  }
  async getByWorkId(id: number) {
    return this.findOne({
      id,
      ...this.publicCondition(),
    })
  }

  async getUploadCount(userId: number) {
    return this.count({
      userId,
      ...this.normalCondition(),
    })
  }
  async getUsage(userId: number) {
    const count = await this.getUploadCount(userId)
    const max = facePinchCfg.maxUploadPerRoleId
    const current = Math.min(count, max)
    return { current, max }
  }

  async hasUploadQuota(userId: number) {
    const count = await this.getUploadCount(userId)
    return count < facePinchCfg.maxUploadPerRoleId
  }

  async checkIsOwnWork(workId: number, userId: number) {
    const r = await WorkModel.findOne({ id: workId, status: EStatuses.Normal, userId })
    if (r && r.id) {
      return r
    } else {
      throw ApiErrors.WorkNotFound
    }
  }

  bodyShapeScope(query: QueryBuilder, params: { gender?: number; jobId?: number, bodyShape?: EBodyShape }) {
    if(params.bodyShape >= 0 ) {
      query = query.where("bodyShape", params.bodyShape)
    } else if (params.jobId > 0) {
      const bodyShape = getBodyShapeFromGenderAndJobId(params.gender || 0, params.jobId || 0)
      query = query.where("bodyShape", bodyShape).where("jobId", ">", 0);
    }
    return query
  }

  getByShareId(shareId: string) {
    return this.findOne(this.shareViewCondition(shareId))
  }
}

export const WorkModel = new WorkModelClass()