import { EStatuses } from "../../../../common/constants"
import { BaseModelClass } from "../../../models/baseModel"

/** 倩女手游捏脸使用记录表 */
export interface ShareVideoRecord {
  id: number
  /** 用户id */
  userId: number
  /** 角色id */
  roleId: number
  /** 作品视频分享url */
  video: string
  /** 视频分享ID */
  shareId: string
  status: EStatuses
  createTime: number
}

export type ShareVideoCol = keyof ShareVideoRecord

export const ShareVideoCols: ShareVideoCol[] = ["id", "userId", "roleId", "video", "status", "shareId", "createTime"]

class ShareVideoModelClass extends BaseModelClass<ShareVideoRecord> {
  constructor() {
    super("pyq_face_pinch_share_video")
  }
}

export const ShareVideoModel = new ShareVideoModelClass()