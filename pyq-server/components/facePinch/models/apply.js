"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApplyModel = exports.ApplyCols = void 0;
const baseModel_1 = require("../../../models/baseModel");
exports.ApplyCols = ["id", "userId", "roleId", "workId", "targetId", "status", "createTime"];
class ApplyModelClass extends baseModel_1.BaseModelClass {
    constructor() {
        super("pyq_face_pinch_apply");
    }
    filterUserApplied(userId, workIds) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!userId)
                return [];
            const rows = yield this.find({ workId: workIds, userId, status: 0 /* Normal */ });
            return rows.map((r) => r.workId);
        });
    }
}
exports.ApplyModel = new ApplyModelClass();
//# sourceMappingURL=apply.js.map