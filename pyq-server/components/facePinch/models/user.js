"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserModel = exports.UserCols = void 0;
const baseModel_1 = require("../../../models/baseModel");
exports.UserCols = ["id", "roleId", "roleName", "account", "createTime"];
class UserModelClass extends baseModel_1.BaseModelClass {
    constructor() {
        super("pyq_face_pinch_user");
    }
    getUserInfo(account, roleId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.findOne({ account, roleId });
        });
    }
    createUser(props) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.insert(props);
        });
    }
}
exports.UserModel = new UserModelClass();
//# sourceMappingURL=user.js.map