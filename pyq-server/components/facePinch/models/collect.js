"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CollectModel = exports.CollectCols = void 0;
const config_1 = require("../../../common/config");
const baseModel_1 = require("../../../models/baseModel");
exports.CollectCols = ["id", "roleId", "workId", "targetId", "status", "createTime"];
class CollectModelClass extends baseModel_1.BaseModelClass {
    constructor() {
        super("pyq_face_pinch_collect");
    }
    getCollectWorkIds(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            const rows = yield this.find({ userId, status: 0 /* Normal */ }, { limit: config_1.facePinchCfg.maxCollectPerRoleId });
            return rows.map((row) => row.workId);
        });
    }
    filterUserCollected(userId, workIds) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!userId)
                return [];
            const rows = yield this.find({ workId: workIds, userId, status: 0 /* Normal */ });
            return rows.map((r) => r.workId);
        });
    }
    getCollectCount(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            const query = this.scope()
                .from(this.tableName + " as c")
                .innerJoin("pyq_face_pinch_work as w", "c.workId", "w.id")
                .where("c.userId", userId)
                .where("c.status", 0 /* Normal */)
                .where("w.status", 0 /* Normal */)
                .where("w.visibility", 0 /* Public */);
            return this.countByQuery(query);
        });
    }
}
exports.CollectModel = new CollectModelClass();
//# sourceMappingURL=collect.js.map