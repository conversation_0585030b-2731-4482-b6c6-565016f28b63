import { BaseModelClass } from "../../../models/baseModel"

/** 倩女手游捏脸点赞表 */
export interface UserRecord {
  id: number
  /** 角色id */
  roleId: number
  /** 角色名字 */
  roleName: string
  /** 角色id */
  account: string
  createTime: number
}

export type UserCol = keyof UserRecord

export const UserCols: UserCol[] = ["id", "roleId", "roleName", "account", "createTime"]

class UserModelClass extends BaseModelClass<UserRecord> {
  constructor() {
    super("pyq_face_pinch_user")
  }

  async getUserInfo(account: string, roleId: number) {
    return this.findOne({ account, roleId })
  }

  async createUser(props: Omit<UserRecord, "id">) {
    return this.insert(props)
  }
}

export const UserModel = new UserModelClass()