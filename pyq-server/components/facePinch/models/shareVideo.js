"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShareVideoModel = exports.ShareVideoCols = void 0;
const baseModel_1 = require("../../../models/baseModel");
exports.ShareVideoCols = ["id", "userId", "roleId", "video", "status", "shareId", "createTime"];
class ShareVideoModelClass extends baseModel_1.BaseModelClass {
    constructor() {
        super("pyq_face_pinch_share_video");
    }
}
exports.ShareVideoModel = new ShareVideoModelClass();
//# sourceMappingURL=shareVideo.js.map