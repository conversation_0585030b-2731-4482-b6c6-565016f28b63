import { EStatuses } from "../../../../common/constants"
import { facePinchCfg } from "../../../common/config"
import { BaseModelClass } from "../../../models/baseModel"
import { EVisibility } from "../constant"

/** 倩女手游捏脸收藏表 */
export interface CollectRecord {
  id: number

  /** 用户id */
  userId: number
  /** 角色id */
  roleId: number
  /** 捏脸站作品ID */
  workId: number
  /** 心情对应的角色ID */
  targetId: number
  /** -1 取消收藏， 0收藏 */
  status: -1 | 0
  createTime: number
}

export type CollectCol = keyof CollectRecord

export const CollectCols: CollectCol[] =
["id", "roleId", "workId", "targetId", "status", "createTime"]

class CollectModelClass extends BaseModelClass<CollectRecord> {
  constructor() {
    super("pyq_face_pinch_collect")
  }

  async getCollectWorkIds(userId: number) {
    const rows = await this.find({ userId, status: EStatuses.Normal }, { limit: facePinchCfg.maxCollectPerRoleId });
    return rows.map((row) => row.workId)
  }

  async filterUserCollected(userId: number, workIds: number[]) {
    if (!userId) return []
    const rows = await this.find({ workId: workIds, userId, status: EStatuses.Normal })
    return rows.map((r) => r.workId)
  }

  async getCollectCount(userId: number) {
    const query = this.scope()
      .from(this.tableName + " as c")
      .innerJoin("pyq_face_pinch_work as w", "c.workId", "w.id")
      .where("c.userId", userId)
      .where("c.status", EStatuses.Normal)
      .where("w.status", EStatuses.Normal)
      .where("w.visibility", EVisibility.Public)
    return this.countByQuery(query)
  }
}

export const CollectModel = new CollectModelClass()