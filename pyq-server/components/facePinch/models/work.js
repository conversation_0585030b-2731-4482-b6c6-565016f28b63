"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkModel = exports.getBodyShapeFromGenderAndJobId = exports.WorkCols = void 0;
const config_1 = require("../../../common/config");
const baseModel_1 = require("../../../models/baseModel");
const errorCode_1 = require("../errorCode");
exports.WorkCols = [
    "id",
    "roleId",
    "roleName",
    "account",
    "dataUrl",
    "image",
    "title",
    "desc",
    "shareId",
    "bodyShape",
    "allowedParts",
    "gender",
    "jobId",
    "workType",
    "visibility",
    "hot",
    "likeCount",
    "collectCount",
    "applyCount",
    "status",
    "createTime",
];
function getBodyShapeFromGenderAndJobId(gender, jobId) {
    if (jobId <= 0)
        return -1 /* UNKNOWN */;
    if (gender === 0 /* MALE */) {
        if (jobId === 2 /* JIA_SHI */ || jobId === 4 /* XIA_KE */) {
            return 2 /* STRONG_MALE */;
        }
        else {
            return 0 /* ADULT_MALE */;
        }
    }
    else {
        if (jobId === 8 /* YI_REN */ || jobId === 10 /* HUA_HUN */) {
            return 3 /* LOLITA */;
        }
        else {
            return 1 /* ADULT_FEMALE */;
        }
    }
}
exports.getBodyShapeFromGenderAndJobId = getBodyShapeFromGenderAndJobId;
class WorkModelClass extends baseModel_1.BaseModelClass {
    constructor() {
        super("pyq_face_pinch_work");
    }
    normalCondition() {
        return {
            status: 0 /* Normal */,
            workType: 0 /* Normal */,
        };
    }
    shareViewCondition(shareId) {
        return {
            shareId,
            status: 0 /* Normal */,
            auditStatus: 1 /* PASS */,
            visibility: 0 /* Public */,
        };
    }
    importCondition(id) {
        return {
            id,
            status: 0 /* Normal */,
            auditStatus: 1 /* PASS */,
            visibility: 0 /* Public */,
        };
    }
    selfViewCondition(userId) {
        return {
            userId,
            status: 0 /* Normal */,
            auditStatus: 1 /* PASS */,
            workType: 0 /* Normal */,
        };
    }
    publicCondition() {
        return Object.assign(Object.assign({}, this.normalCondition()), { workType: 0 /* Normal */, visibility: 0 /* Public */ });
    }
    collectCondition() {
        return {
            status: 0 /* Normal */,
            visibility: 0 /* Public */,
        };
    }
    normalScope() {
        return this.scope().where(this.normalCondition());
    }
    publicScope() {
        return this.scope().where(this.publicCondition());
    }
    collectScope() {
        return this.scope().where(this.collectCondition());
    }
    selfScope(curUserId) {
        return this.scope().where(this.selfViewCondition(curUserId));
    }
    getPublicByIds(ids) {
        return __awaiter(this, void 0, void 0, function* () {
            const rows = yield this.find(Object.assign({ id: ids }, this.publicCondition()));
            rows.sort((a, b) => ids.indexOf(a.id) - ids.indexOf(b.id));
            return rows;
        });
    }
    findByShareId(shareId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.findOne(this.shareViewCondition(shareId));
        });
    }
    getByWorkId(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.findOne(Object.assign({ id }, this.publicCondition()));
        });
    }
    getUploadCount(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.count(Object.assign({ userId }, this.normalCondition()));
        });
    }
    getUsage(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            const count = yield this.getUploadCount(userId);
            const max = config_1.facePinchCfg.maxUploadPerRoleId;
            const current = Math.min(count, max);
            return { current, max };
        });
    }
    hasUploadQuota(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            const count = yield this.getUploadCount(userId);
            return count < config_1.facePinchCfg.maxUploadPerRoleId;
        });
    }
    checkIsOwnWork(workId, userId) {
        return __awaiter(this, void 0, void 0, function* () {
            const r = yield exports.WorkModel.findOne({ id: workId, status: 0 /* Normal */, userId });
            if (r && r.id) {
                return r;
            }
            else {
                throw errorCode_1.ApiErrors.WorkNotFound;
            }
        });
    }
    bodyShapeScope(query, params) {
        if (params.bodyShape >= 0) {
            query = query.where("bodyShape", params.bodyShape);
        }
        else if (params.jobId > 0) {
            const bodyShape = getBodyShapeFromGenderAndJobId(params.gender || 0, params.jobId || 0);
            query = query.where("bodyShape", bodyShape).where("jobId", ">", 0);
        }
        return query;
    }
    getByShareId(shareId) {
        return this.findOne(this.shareViewCondition(shareId));
    }
}
exports.WorkModel = new WorkModelClass();
//# sourceMappingURL=work.js.map