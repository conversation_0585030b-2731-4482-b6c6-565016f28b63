import { EStatuses } from "../../../../common/constants";
import { BaseModelClass } from "../../../models/baseModel"

/** 倩女手游捏脸点赞表 */
export interface LikeRecord {
  id: number;

  /** 用户id */
  userId: number;
  /** 角色id */
  roleId: number;
  /** 捏脸站作品ID */
  workId: number;
  /** 心情对应的角色ID */
  targetId: number;
  /** -1 取消点赞， 0点赞 */
  status: -1 | 0;
  createTime: number;
}

export type LikeCol = keyof LikeRecord

export const LikeCols: LikeCol[] = ['id', 'userId', 'roleId', 'workId', 'targetId', 'status', 'createTime'];

class LikeModelClass extends BaseModelClass<LikeRecord> {
  constructor() {
    super("pyq_face_pinch_like")
  }

  async filterUserLiked(userId: number, workIds: number[]) {
    if (!userId) return []
    const rows = await this.find({ workId: workIds, userId, status: EStatuses.Normal })
    return rows.map((r) => r.workId)
  }
}

export const LikeModel = new LikeModelClass()