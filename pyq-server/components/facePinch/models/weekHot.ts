import { nanoid } from "nanoid"
import { RedisLock } from "../../../../common/cacheUtil"
import { cacheKeyGen } from "../../../../common/util2"
import { GenericCache } from "../../../common/cacheUtil"
import { BaseModelClass } from "../../../models/baseModel"
import type { WorkRecord } from "./work"
import { logger } from "../../../logger"

/** 玩家瞬间周人气排行 */
export interface WorkHotWeekRecord {
  id: number
  /** 一周开始，记录当周周一 */
  week: string
  /** 心情ID */
  workId: number
  /** 用户id */
  userId: number
  /** 角色id */
  roleId: number
  /** 作品所能应用性别 */
  gender: number
  /** 作品所能应用职业id */
  jobId: number
  /** 周热度 */
  hot: number
}

export type WorkHotWeekCol = keyof WorkHotWeekRecord

export const WorkHotWeekCols: WorkHotWeekCol[] = ["id", "week", "workId", "gender", "jobId", "hot"]


class WorkWeekHotIdCacheClass extends GenericCache<{ workId: number; week: string }, number> {
  getKey(params: Partial<{ workId: number; week: string }>, opt?: any): string {
    return cacheKeyGen("face_pinch_week_hot_work", { workId: params.workId, week: params.week });
  }

  getExpireTime(params: { workId: number; week: string }) {
    return 5 * 60;
  }

  async fetchDataSource(params: { workId: number; week: string }, opt?: any): Promise<number> {
    const record = await WeekHotModel.findOne({ workId: params.workId, week: params.week });
    if (record && record.id) {
      return record.id;
    } else {
      return 0;
    }
  }

  async updateWeekHotId(params: { workId: number; week: string }, content: number) {
    return this.writeToCache(params, content);
  }
}

const WorkWeekHotIdCache = new WorkWeekHotIdCacheClass();

class WeekHotModelClass extends BaseModelClass<WorkHotWeekRecord> {
  constructor() {
    super("pyq_face_pinch_week_hot");
  }

  async createIfNotExist(work: WorkRecord, week: string) {
    const workWeekHotId = await WorkWeekHotIdCache.get({ workId: work.id, week });
    if (workWeekHotId > 0) return workWeekHotId;
    const workHotProp: Omit<WorkHotWeekRecord, "id"> = {
      workId: work.id,
      userId: work.userId,
      roleId: work.roleId,
      gender: work.gender,
      jobId: work.jobId,
      week,
      hot: 0,
    };
    const recordWeekHotLockKey = `pyq_face_pinch_week_hot_work:${work.id}:week:${week}`;
    const lockOwnerId = nanoid();
    try {
      const hasLock = await RedisLock.optimistic(recordWeekHotLockKey, lockOwnerId, 1000, 3, 50);
      if (hasLock) {
        const id = await WeekHotModel.insert(workHotProp);
        await WorkWeekHotIdCache.updateWeekHotId({ workId: work.id, week }, id);
        return id;
      } else {
        logger.warn({ work, week, lockOwnerId }, "WeekHotModelGetLockFailed");
        return 0;
      }
    } catch (err) {
      logger.info({ err, work, week, lockOwnerId }, "WeekHotModelCreateFailed");
    } finally {
      await RedisLock.unLock(recordWeekHotLockKey, lockOwnerId);
    }
  }
}

export const WeekHotModel = new WeekHotModelClass()
