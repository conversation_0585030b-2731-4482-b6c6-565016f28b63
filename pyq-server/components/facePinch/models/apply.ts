import { EStatuses } from "../../../../common/constants"
import { BaseModelClass } from "../../../models/baseModel"

/** 倩女手游捏脸使用记录表 */
export interface ApplyRecord {
  id: number
  /** 用户id */
  userId: number
  /** 角色id */
  roleId: number
  /** 捏脸站作品ID */
  workId: number
  /** 应用对应的用户ID */
  targetId: number
  /** -1 取消收藏， 0收藏 */
  status: -1 | 0
  createTime: number
}

export type ApplyCol = keyof ApplyRecord;

export const ApplyCols: ApplyCol[] = ["id", "userId", "roleId", "workId", "targetId", "status", "createTime"]

class ApplyModelClass extends BaseModelClass<ApplyRecord> {
  constructor() {
    super("pyq_face_pinch_apply")
  }

  async filterUserApplied(userId: number, workIds: number[]) {
    if (!userId) return []
    const rows = await this.find({ workId: workIds, userId, status: EStatuses.Normal })
    return rows.map((r) => r.workId)
  }
}

export const ApplyModel = new ApplyModelClass();
