"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FacePinchComponent = exports.paths = void 0;
const config_1 = require("../../common/config");
const ipAuth_1 = require("../../middlewares/ipAuth");
const rateLimiter_1 = require("../../middlewares/rateLimiter");
const speechLimit_1 = require("../../middlewares/speechLimit");
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "get",
        url: "/face_pinch/web/public/share_info",
        paramsSchema: type_1.ReqSchemas.FacePinchWebPublicShareInfo,
        operation: operation_1.facePinchWebPublicShareInfo,
        option: { skipAuth: true },
    },
    {
        method: "post",
        url: "/face_pinch/dashen/share_info/import",
        paramsSchema: type_1.ReqSchemas.FacePinchDashenShareInfoImport,
        operation: operation_1.facePinchDashenShareInfoImport,
        before: (0, ipAuth_1.ipLimitMiddleware)(config_1.facePinchCfg.dashenIpAllowList),
        option: { skipAuth: true },
    },
    {
        method: "get",
        url: "/face_pinch/web/public/share_video/info",
        paramsSchema: type_1.ReqSchemas.FacePinchWebPublicShareVideoInfo,
        operation: operation_1.facePinchWebPublicShareVideoInfo,
        option: { skipAuth: true },
    },
    {
        method: "post",
        url: "/face_pinch/share_video/add",
        paramsSchema: type_1.ReqSchemas.FacePinchShareVideoAdd,
        operation: operation_1.facePinchShareVideoAdd,
    },
    {
        method: "get",
        url: "/face_pinch/work/list_public",
        paramsSchema: type_1.ReqSchemas.FacePinchWorkListPublic,
        operation: operation_1.facePinchWorkListPublic,
    },
    {
        method: "post",
        url: "/face_pinch/cloud_game/auth/login",
        paramsSchema: type_1.ReqSchemas.FacePinchCloudGameAuthLogin,
        before: (0, ipAuth_1.injectIpHelper)(),
        operation: operation_1.facePinchWorkCloudGameAuthLogin,
        option: { skipAuth: true },
    },
    {
        method: "get",
        url: "/face_pinch/work/list_collect",
        paramsSchema: type_1.ReqSchemas.FacePinchWorkListCollect,
        operation: operation_1.facePinchWorkListCollect,
    },
    {
        method: "get",
        url: "/face_pinch/work/list_self",
        paramsSchema: type_1.ReqSchemas.FacePinchWorkListSelf,
        operation: operation_1.facePinchWorkListSelf,
    },
    {
        method: "get",
        url: "/face_pinch/work/get_by_share_id",
        paramsSchema: type_1.ReqSchemas.FacePinchWorkGetByShareId,
        operation: operation_1.facePinchWorkGetByShareId,
    },
    {
        method: "get",
        url: "/face_pinch/work/detail",
        paramsSchema: type_1.ReqSchemas.FacePinchWorkDetail,
        operation: operation_1.facePinchWorkDetail,
    },
    {
        method: "get",
        url: "/face_pinch/work/search",
        paramsSchema: type_1.ReqSchemas.FacePinchWorkSearch,
        operation: operation_1.facePinchWorkSearch,
    },
    {
        method: "post",
        url: "/face_pinch/work/add",
        paramsSchema: type_1.ReqSchemas.FacePinchWorkAdd,
        before: (0, speechLimit_1.checkContentSensitive)(["roleName", "title", "desc"]),
        operation: operation_1.facePinchWorkAdd,
    },
    {
        method: "post",
        url: "/face_pinch/work/update_visibility",
        paramsSchema: type_1.ReqSchemas.FacePinchWorkUpdateVisibility,
        operation: operation_1.facePinchWorkUpdateVisibility,
    },
    {
        method: "get",
        url: "/face_pinch/get_fp_token",
        paramsSchema: type_1.ReqSchemas.FacePinchGetFpToken,
        operation: operation_1.facePinchGetFpToken,
        before: [
            (0, ipAuth_1.injectIpHelper)(),
            (0, rateLimiter_1.rateLimiter)({
                endPoint: "face_pinch_get_fp_token",
                rate_limit: {
                    seconds: config_1.facePinchCfg.fpLimitRate.seconds,
                    max: config_1.facePinchCfg.fpLimitRate.max,
                },
            }, (params) => {
                if (params.curUser && params.curUser.account) {
                    return params.curUser.account;
                }
                else {
                    return "";
                }
            }),
        ],
    },
    {
        method: "post",
        url: "/face_pinch/work/del",
        paramsSchema: type_1.ReqSchemas.FacePinchWorkDel,
        operation: operation_1.facePinchWorkDel,
    },
    {
        method: "post",
        url: "/face_pinch/work/apply",
        paramsSchema: type_1.ReqSchemas.FacePinchWorkApply,
        operation: operation_1.facePinchWorkApply,
    },
    {
        method: "post",
        url: "/face_pinch/work/like",
        paramsSchema: type_1.ReqSchemas.FacePinchWorkLike,
        operation: operation_1.facePinchWorkLike,
    },
    {
        method: "post",
        url: "/face_pinch/work/cancel_like",
        paramsSchema: type_1.ReqSchemas.FacePinchWorkCancelLike,
        operation: operation_1.facePinchWorkCancelLike,
    },
    {
        method: "post",
        url: "/face_pinch/work/collect",
        paramsSchema: type_1.ReqSchemas.FacePinchWorkCollect,
        operation: operation_1.facePinchWorkCollect,
    },
    {
        method: "post",
        url: "/face_pinch/work/cancel_collect",
        paramsSchema: type_1.ReqSchemas.FacePinchWorkCancelCollect,
        operation: operation_1.facePinchWorkCancelCollect,
    },
];
exports.FacePinchComponent = {
    paths: exports.paths,
    prefix: "/face_pinch/",
};
//# sourceMappingURL=index.js.map