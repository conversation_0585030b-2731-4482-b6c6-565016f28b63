"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getRankPlayers = exports.PkRenQiDailyRank = void 0;
const util_1 = require("@leihuo/openid-cookie-auth/lib/util");
const util_2 = require("../../../../common/util");
const redis_1 = require("../../../common/redis");
const pkRoleIdMap_1 = require("./pkRoleIdMap");
const player_1 = require("./player");
const util_3 = require("./util");
class PkRenQiDailyRank {
    constructor(date) {
        this.rankSize = 100;
        this.keyPrefix = `${(0, util_3.getRedisPrefix)(date)}:daily_rank:`;
    }
    key(date) {
        const dateStr = (0, util_2.formatDate)(date.getTime(), "yyyy-MM-dd");
        return this.keyPrefix + dateStr;
    }
    getTopRank(date) {
        return __awaiter(this, void 0, void 0, function* () {
            const items = yield (0, redis_1.getRedis)().zrevrangeAsync(this.key(date), 0, this.rankSize - 1, "WITHSCORES");
            const list = [];
            for (let i = 0; i < items.length; i = i + 2) {
                const ele = { RoleId: parseInt(items[i], 10), RenQi: parseInt(items[i + 1], 10) };
                list.push(ele);
            }
            return list;
        });
    }
    incrRenQi(roleId, incr, date) {
        return __awaiter(this, void 0, void 0, function* () {
            const rankKey = this.key(date);
            const result = yield (0, redis_1.getRedis)().zincrbyAsync(rankKey, incr, roleId);
            yield this.autoExpireRank(rankKey);
            return result;
        });
    }
    setRenqi(roleId, renqi, date) {
        return __awaiter(this, void 0, void 0, function* () {
            const rankKey = this.key(date);
            const result = yield (0, redis_1.getRedis)().zaddAsync(rankKey, renqi, roleId);
            yield this.autoExpireRank(rankKey);
            return result;
        });
    }
    autoExpireRank(rankKey) {
        return __awaiter(this, void 0, void 0, function* () {
            yield (0, redis_1.getRedis)().expireAsync(rankKey, 14 * util_1.ONE_DAY_SECONDS);
        });
    }
    static from(date) {
        return new PkRenQiDailyRank(date);
    }
}
exports.PkRenQiDailyRank = PkRenQiDailyRank;
function getRankPlayers(date) {
    return __awaiter(this, void 0, void 0, function* () {
        const result = [];
        const rankInfo = yield PkRenQiDailyRank.from(date).getTopRank(date);
        if (rankInfo.length === 0) {
            return [];
        }
        let queryRoleIds = null;
        const roleIds = rankInfo.map(x => x.RoleId);
        const pkRoleIdMap = yield pkRoleIdMap_1.PkRoleIdRelation.create(date).getPkRoleIdMapping(roleIds);
        const pkRoleIds = Array.from(pkRoleIdMap.values());
        queryRoleIds = [].concat(roleIds, pkRoleIds);
        const roleInfoMap = yield (0, player_1.getRoleInfoMap)(queryRoleIds);
        for (let i = 0; i < rankInfo.length; i++) {
            const ele = rankInfo[i];
            const pkRoleId = pkRoleIdMap.get(ele.RoleId);
            const player = {
                rank: i + 1,
                renqi: ele.RenQi,
                current: roleInfoMap.get(pkRoleId) || null,
                origin: roleInfoMap.get(ele.RoleId) || null
            };
            result.push(player);
        }
        return result;
    });
}
exports.getRankPlayers = getRankPlayers;
//# sourceMappingURL=pkRenQiDailyRank.js.map