import { testCfg } from "../../../../common/config";
import * as PyqProfile from "../../../../models/PyqProfile";
import * as QnmRoleInfo from "../../../../models/QNMRoleInfos";
import * as ServerList from "../../../../service/qnm/server/list";

export async function getRoleInfoMap(roleIds: number[]): Promise<Map<number, IPlayer>> {
  const map: Map<number, IPlayer> = new Map();
  const roleInfos = (await QnmRoleInfo.find(
    { RoleId: roleIds },
    { cols: ["RoleId", "RoleName", "JobId", "Gender", "ServerId"] }
  )) as { RoleId: number; RoleName: string; JobId: number; Gender: number; ServerId: number }[];
  const photoMap = await getPhotoMap(roleIds);
  for (const r of roleInfos) {
    const serverName = await getServerName(r.ServerId);
    const jobAvatar = QnmRoleInfo.getJobAvatar({ Gender: r.Gender, JobId: r.JobId });
    const profileAvatar = photoMap.get(r.RoleId);
    const avatar = profileAvatar || jobAvatar;
    const player: IPlayer = {
      roleid: r.RoleId,
      rolename: r.RoleName,
      careerid: r.JobId,
      gender: r.Gender,
      servername: serverName,
      avatar: avatar,
    };
    map.set(r.RoleId, player);
  }
  return map;
}

async function getServerName(serverId: number) {
  const servers = await ServerList.getHash2();
  if (servers[serverId]) {
    return servers[serverId].name;
  } else {
    if (testCfg.test_env) {
      return "内网测试:" + serverId;
    } else {
      return "";
    }
  }
}

async function getPhotoMap(roleIds: number[]): Promise<Map<number, string>> {
  const profiles = (await PyqProfile.find({ RoleId: roleIds, PhotoAudit: 1 }, { cols: ["RoleId", "Photo"] })) as {
    RoleId: number;
    Photo: string;
  }[];
  const map: Map<number, string> = new Map();
  for (const r of profiles) {
    map.set(r.RoleId, r.Photo);
  }
  return map;
}
