"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getPkQueryDate = exports.getRedisPrefix = void 0;
const moment = require("moment");
const constants_1 = require("../../../constants");
function getDateYearStr(date) {
    return date.getFullYear();
}
function getRedisPrefix(date) {
    return `qmpk_${getDateYearStr(date)}`;
}
exports.getRedisPrefix = getRedisPrefix;
/**
 *
 * @param ds date string like 'yyyy-mm-dd'
 * @returns
 */
function getPkQueryDate(ds) {
    // 策划要求 默认查前一日榜单
    if (ds && constants_1.DS_REGEX.test(ds)) {
        const queryDate = moment(ds, "YYYY-MM-DD").toDate();
        return queryDate;
    }
    else {
        const yesterday = moment().subtract(1, "days").toDate();
        return yesterday;
    }
}
exports.getPkQueryDate = getPkQueryDate;
//# sourceMappingURL=util.js.map