"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.addShareMoment = exports.ShareMomentSet = void 0;
const redis_1 = require("../../../common/redis");
const logger_1 = require("../../../logger");
const models_1 = require("../../../models");
const pkRoleIdMap_1 = require("./pkRoleIdMap");
const util_1 = require("./util");
const logger = (0, logger_1.clazzLogger)("qmpk/shareMoment");
class ShareMomentSet {
    constructor(date) {
        this.key = `${(0, util_1.getRedisPrefix)(date)}:share_moment_ids_set`;
    }
    has(momentId) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield (0, redis_1.getRedis)().sismemberAsync(this.key, momentId);
            return result === 1;
        });
    }
    add(momentId) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield (0, redis_1.getRedis)().saddAsync(this.key, momentId);
            return result;
        });
    }
    isPkShareMoment(momentId) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.has(momentId);
            return result;
        });
    }
    static create(date) {
        return new ShareMomentSet(date);
    }
}
exports.ShareMomentSet = ShareMomentSet;
function addShareMoment(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const date = params.ts ? new Date(params.ts) : new Date();
        const props = {
            RoleId: params.originRoleId,
            Text: params.text,
            ImgList: params.url,
            ImgAudit: "1",
            CreateTime: date.getTime(),
        };
        const momentId = yield models_1.MomentModel.insert(props);
        yield pkRoleIdMap_1.PkRoleIdRelation.create(date).add(params.originRoleId, params.roleId);
        yield ShareMomentSet.create(date).add(momentId);
        logger.info({ momentId, date }, "AddShareMomentIdSetOK");
        return momentId;
    });
}
exports.addShareMoment = addShareMoment;
//# sourceMappingURL=shareMoment.js.map