"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PkRoleIdRelation = void 0;
const redis_1 = require("../../../common/redis");
const util_1 = require("./util");
class PkRoleIdRelation {
    constructor(date) {
        this.key = `${(0, util_1.getRedisPrefix)(date)}:role_id_mapping`;
    }
    getPkRoleIdMapping(roleIds) {
        return __awaiter(this, void 0, void 0, function* () {
            const values = yield (0, redis_1.getRedis)().hmgetAsync(this.key, ...roleIds);
            const map = new Map();
            for (let i = 0; i < roleIds.length; i++) {
                const pkRoleId = values[i] ? parseInt(values[i], 10) : 0;
                map.set(roleIds[i], pkRoleId);
            }
            return map;
        });
    }
    add(roleId, pkRoleId) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield (0, redis_1.getRedis)().hsetAsync(this.key, '' + roleId, pkRoleId);
            return result;
        });
    }
    static create(date) {
        return new PkRoleIdRelation(date || new Date());
    }
}
exports.PkRoleIdRelation = PkRoleIdRelation;
//# sourceMappingURL=pkRoleIdMap.js.map