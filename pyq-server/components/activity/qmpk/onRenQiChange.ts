import { Statues } from "../../../../common/constants"
import { EventNames } from "../../../constants"
import { clazzLogger } from "../../../logger"
import { MomentModel } from "../../../models"
import { LIKE_MOMENT_PAYLOAD } from "../../../types"
import { EventHandleGroup } from "../../../types/type"
import { PkRenQiDailyRank } from "./pkRenQiDailyRank"
import { ShareMomentSet } from "./shareMoment"
const logger = clazzLogger("qmpk/onRenQiChange")

enum LikeTypes {
  LIKE = 1,
  CANCEL_LIKE = 2,
}

function tsToDate(ts: number): Date {
  return ts ? new Date(ts) : new Date()
}

async function updatePkRenQi(payload: LIKE_MOMENT_PAYLOAD, type: LikeTypes) {
  const momentId = payload.momentId
  const moment = await MomentModel.findOne({ ID: momentId, Status: Statues.Normal }, ["RoleId", "CreateTime"])
  if (!moment) {
    logger.warn({ momentId }, "UpdatePkRenQiMomentNotFound")
    return
  }
  const isAttendPk = await ShareMomentSet.create(new Date(moment.CreateTime)).isPkShareMoment(momentId)
  if (isAttendPk) {
    const rankList = PkRenQiDailyRank.from(new Date(moment.CreateTime))
    if (type === LikeTypes.LIKE) {
      await rankList.incrRenQi(moment.RoleId, 1, tsToDate(payload.ts))
    } else {
      await rankList.incrRenQi(moment.RoleId, -1, tsToDate(payload.ts))
    }
  } else {
    logger.debug({ momentId }, "UpdatePkRenQiMomentNotPk")
  }
}

export class QmPkRenQiEventHandler {
  static async onLikeMoment(payload: LIKE_MOMENT_PAYLOAD) {
    updatePkRenQi(payload, LikeTypes.LIKE)
  }

  static async onCancelLikeMoment(payload: LIKE_MOMENT_PAYLOAD) {
    updatePkRenQi(payload, LikeTypes.CANCEL_LIKE)
  }
}

export const QmPkRenQiOnEvents: EventHandleGroup = [
  { name: EventNames.LIKE_MOMENT, handler: QmPkRenQiEventHandler.onLikeMoment },
  { name: EventNames.CANCEL_LIKE_MOMENT, handler: QmPkRenQiEventHandler.onCancelLikeMoment },
]
