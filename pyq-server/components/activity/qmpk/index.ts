import { checkParams, errorHandler } from "../../../helper"
import { clazzLogger } from "../../../logger"
import { PkRenQiDailyRank, getRankPlayers } from "./pkRenQiDailyRank"
import { PkRoleIdRelation } from "./pkRoleIdMap"
import { addShareMoment } from "./shareMoment"
import { getPkQueryDate } from "./util"
const logger = clazzLogger("activity/qmpk")

export async function share(req, res, next) {
  const validator = req.paramsValidator
    .param('roleid', { type: Number }) // 参赛服角色id
    .param('origin_roleid', { type: Number }) //原始角色id
    .param('text', { type: String, required: false })
    .param('url', { type: String })
    .param('ts', { type: Number, required: false})
  try {
    await validator.validate()
    const params = req.params
    const text = params.text || ''
    const option = {
      roleId: params.roleid,
      originRoleId: params.origin_roleid,
      text: text,
      url: params.url,
      ts: params.ts || Date.now()
    }
    const id = await addShareMoment(option)
    res.send({ code: 0, data: { id: id } })
  } catch (err) {
    errorHandler(err, req, res, next)
  }
}


export async function getRankList(req, res, next) {
  try {
    const queryDate = getPkQueryDate(req.params.ds)
    const data = await getRankPlayers(queryDate)
    res.send({ code: 0, data: data })
  } catch (err) {
    errorHandler(err, req, res, next)
  }
}


interface ISetRank {
  roleid: number
  originRoleId: number
  ds?: string
  rank: number
}

export async function setRank(req, res, next) {
  try {
    const schema = {
      roleid: { type: Number },
      originRoleId: { type: Number },
      ds: { type: String, required: false},
      rank: { type: Number, min: 1 },
    }
    const params = req.params as ISetRank
    await checkParams(params, schema)
    const { roleid, rank, originRoleId } = params
    const queryDate = getPkQueryDate(params.ds)
    const renqiRank = PkRenQiDailyRank.from(queryDate)
    const rankInfo: TopRankItem[] = await renqiRank.getTopRank(queryDate)
    await PkRoleIdRelation.create(queryDate).add(originRoleId, roleid)
    if (rankInfo && rankInfo.length > 0 && rankInfo[rank - 1]) {
      logger.debug({queryDate}, "setRenQi")
      await renqiRank.setRenqi(originRoleId, rankInfo[rank - 1].RenQi + 1, queryDate)
    } else {
      await renqiRank.incrRenQi(originRoleId, 1, queryDate)
    }
    const data = await getRankPlayers(queryDate)
    res.send({ code: 0, data: data })
  } catch (err) {
    errorHandler(err, req, res, next)
  }
}