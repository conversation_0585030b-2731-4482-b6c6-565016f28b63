import { getRedis } from "../../../common/redis"
import { getRedisPrefix } from "./util"

export class PkRoleIdRelation {
  private key: string

  constructor(date: Date) {
    this.key = `${getRedisPrefix(date)}:role_id_mapping`
  }

  async getPkRoleIdMapping(roleIds: number[]): Promise<Map<number, number>> {
    const values = await getRedis().hmgetAsync(this.key, ...roleIds)
    const map: Map<number, number> = new Map()
    for (let i = 0; i < roleIds.length; i++) {
      const pkRoleId = values[i] ? parseInt(values[i], 10) : 0
      map.set(roleIds[i], pkRoleId)
    }
    return map
  }

  async add(roleId: number, pkRoleId: number) {
    const result = await getRedis().hsetAsync(this.key, '' + roleId, pkRoleId)
    return result
  }

  static create(date: Date) {
    return new PkRoleIdRelation(date || new Date())
  }
}
