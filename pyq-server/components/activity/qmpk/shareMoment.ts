import { getRedis } from "../../../common/redis"
import { clazz<PERSON>ogger } from "../../../logger"
import { MomentModel } from "../../../models"
import { PkRoleIdRelation } from "./pkRoleIdMap"
import { getRedisPrefix } from "./util"
const logger = clazzLogger("qmpk/shareMoment")

export class ShareMomentSet {
  private key: string
  constructor(date: Date) {
    this.key = `${getRedisPrefix(date)}:share_moment_ids_set`
  }

  async has(momentId: number): Promise<boolean> {
    const result = await getRedis().sismemberAsync(this.key, momentId)
    return result === 1
  }

  async add(momentId: number) {
    const result = await getRedis().saddAsync(this.key, momentId)
    return result
  }
  async isPkShareMoment(momentId: number): Promise<boolean> {
    const result = await this.has(momentId)
    return result
  }
  static create(date: Date) {
    return new ShareMomentSet(date)
  }
}

export async function addShareMoment(params: IShare): Promise<number> {
  const date = params.ts ? new Date(params.ts) : new Date()
  const props = {
    RoleId: params.originRoleId,
    Text: params.text,
    ImgList: params.url,
    ImgAudit: "1",
    CreateTime: date.getTime(),
  }
  const momentId = await MomentModel.insert(props)
  await PkRoleIdRelation.create(date).add(params.originRoleId, params.roleId)
  await ShareMomentSet.create(date).add(momentId)
  logger.info({ momentId, date }, "AddShareMomentIdSetOK")
  return momentId
}