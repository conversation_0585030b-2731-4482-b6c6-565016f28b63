import moment = require("moment")
import { DS_REGEX } from "../../../constants"

function getDateYearStr(date: Date) {
  return date.getFullYear()
}

export function getRedisPrefix(date: Date) {
  return `qmpk_${getDateYearStr(date)}`
}

/**
 *
 * @param ds date string like 'yyyy-mm-dd'
 * @returns
 */
export function getPkQueryDate(ds: string) {
  // 策划要求 默认查前一日榜单
  if (ds && DS_REGEX.test(ds))  {
    const queryDate = moment(ds, "YYYY-MM-DD").toDate()
    return queryDate
  } else {
    const yesterday = moment().subtract(1, "days").toDate()
    return yesterday
  }
}