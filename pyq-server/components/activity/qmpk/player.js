"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getRoleInfoMap = void 0;
const config_1 = require("../../../../common/config");
const PyqProfile = require("../../../../models/PyqProfile");
const QnmRoleInfo = require("../../../../models/QNMRoleInfos");
const ServerList = require("../../../../service/qnm/server/list");
function getRoleInfoMap(roleIds) {
    return __awaiter(this, void 0, void 0, function* () {
        const map = new Map();
        const roleInfos = (yield QnmRoleInfo.find({ RoleId: roleIds }, { cols: ["RoleId", "RoleName", "JobId", "Gender", "ServerId"] }));
        const photoMap = yield getPhotoMap(roleIds);
        for (const r of roleInfos) {
            const serverName = yield getServerName(r.ServerId);
            const jobAvatar = QnmRoleInfo.getJobAvatar({ Gender: r.Gender, JobId: r.JobId });
            const profileAvatar = photoMap.get(r.RoleId);
            const avatar = profileAvatar || jobAvatar;
            const player = {
                roleid: r.RoleId,
                rolename: r.RoleName,
                careerid: r.JobId,
                gender: r.Gender,
                servername: serverName,
                avatar: avatar,
            };
            map.set(r.RoleId, player);
        }
        return map;
    });
}
exports.getRoleInfoMap = getRoleInfoMap;
function getServerName(serverId) {
    return __awaiter(this, void 0, void 0, function* () {
        const servers = yield ServerList.getHash2();
        if (servers[serverId]) {
            return servers[serverId].name;
        }
        else {
            if (config_1.testCfg.test_env) {
                return "内网测试:" + serverId;
            }
            else {
                return "";
            }
        }
    });
}
function getPhotoMap(roleIds) {
    return __awaiter(this, void 0, void 0, function* () {
        const profiles = (yield PyqProfile.find({ RoleId: roleIds, PhotoAudit: 1 }, { cols: ["RoleId", "Photo"] }));
        const map = new Map();
        for (const r of profiles) {
            map.set(r.RoleId, r.Photo);
        }
        return map;
    });
}
//# sourceMappingURL=player.js.map