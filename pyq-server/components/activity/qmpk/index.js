"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.setRank = exports.getRankList = exports.share = void 0;
const helper_1 = require("../../../helper");
const logger_1 = require("../../../logger");
const pkRenQiDailyRank_1 = require("./pkRenQiDailyRank");
const pkRoleIdMap_1 = require("./pkRoleIdMap");
const shareMoment_1 = require("./shareMoment");
const util_1 = require("./util");
const logger = (0, logger_1.clazzLogger)("activity/qmpk");
function share(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        const validator = req.paramsValidator
            .param('roleid', { type: Number }) // 参赛服角色id
            .param('origin_roleid', { type: Number }) //原始角色id
            .param('text', { type: String, required: false })
            .param('url', { type: String })
            .param('ts', { type: Number, required: false });
        try {
            yield validator.validate();
            const params = req.params;
            const text = params.text || '';
            const option = {
                roleId: params.roleid,
                originRoleId: params.origin_roleid,
                text: text,
                url: params.url,
                ts: params.ts || Date.now()
            };
            const id = yield (0, shareMoment_1.addShareMoment)(option);
            res.send({ code: 0, data: { id: id } });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.share = share;
function getRankList(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const queryDate = (0, util_1.getPkQueryDate)(req.params.ds);
            const data = yield (0, pkRenQiDailyRank_1.getRankPlayers)(queryDate);
            res.send({ code: 0, data: data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.getRankList = getRankList;
function setRank(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const schema = {
                roleid: { type: Number },
                originRoleId: { type: Number },
                ds: { type: String, required: false },
                rank: { type: Number, min: 1 },
            };
            const params = req.params;
            yield (0, helper_1.checkParams)(params, schema);
            const { roleid, rank, originRoleId } = params;
            const queryDate = (0, util_1.getPkQueryDate)(params.ds);
            const renqiRank = pkRenQiDailyRank_1.PkRenQiDailyRank.from(queryDate);
            const rankInfo = yield renqiRank.getTopRank(queryDate);
            yield pkRoleIdMap_1.PkRoleIdRelation.create(queryDate).add(originRoleId, roleid);
            if (rankInfo && rankInfo.length > 0 && rankInfo[rank - 1]) {
                logger.debug({ queryDate }, "setRenQi");
                yield renqiRank.setRenqi(originRoleId, rankInfo[rank - 1].RenQi + 1, queryDate);
            }
            else {
                yield renqiRank.incrRenQi(originRoleId, 1, queryDate);
            }
            const data = yield (0, pkRenQiDailyRank_1.getRankPlayers)(queryDate);
            res.send({ code: 0, data: data });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.setRank = setRank;
//# sourceMappingURL=index.js.map