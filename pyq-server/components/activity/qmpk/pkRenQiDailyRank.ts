import { ONE_DAY_SECONDS } from "@leihuo/openid-cookie-auth/lib/util"
import { formatDate } from "../../../../common/util"
import { getRedis } from "../../../common/redis"
import { PkRoleIdRelation } from "./pkRoleIdMap"
import { getRoleInfoMap } from "./player"
import { getRedisPrefix } from './util'

export class PkRenQiDailyRank {
  private keyPrefix: string
  private rankSize = 100

  constructor(date?: Date) {
    this.keyPrefix = `${getRedisPrefix(date)}:daily_rank:`
  }

  key(date: Date) {
    const dateStr = formatDate(date.getTime(), "yyyy-MM-dd")
    return this.keyPrefix + dateStr
  }

  async getTopRank(date: Date): Promise<TopRankItem[]> {
    const items = await getRedis().zrevrangeAsync(this.key(date), 0, this.rankSize - 1, "WITHSCORES")
    const list: TopRankItem[] = []
    for (let i = 0; i < items.length; i = i + 2) {
      const ele = { RoleId: parseInt(items[i], 10), RenQi: parseInt(items[i + 1], 10) }
      list.push(ele)
    }
    return list
  }

  async incrRenQi(roleId: number, incr: number, date: Date): Promise<string> {
    const rankKey = this.key(date)
    const result = await getRedis().zincrbyAsync(rankKey, incr, roleId)
    await this.autoExpireRank(rankKey)
    return result
  }

  async setRenqi(roleId: number, renqi: number, date: Date): Promise<string> {
    const rankKey = this.key(date)
    const result = await getRedis().zaddAsync(rankKey, renqi, roleId)
    await this.autoExpireRank(rankKey)
    return result
  }

  async autoExpireRank(rankKey: string) {
    await getRedis().expireAsync(rankKey, 14 * ONE_DAY_SECONDS)
  }

  static from(date: Date) {
    return new PkRenQiDailyRank(date)
  }
}



export async function getRankPlayers(date: Date): Promise<IRankPlayer[]> {
  const result: IRankPlayer[] = []
  const rankInfo: TopRankItem[] = await PkRenQiDailyRank.from(date).getTopRank(date)
  if (rankInfo.length === 0) {
    return []
  }
  let queryRoleIds: number[] = null
  const roleIds = rankInfo.map(x => x.RoleId)
  const pkRoleIdMap = await PkRoleIdRelation.create(date).getPkRoleIdMapping(roleIds)
  const pkRoleIds = Array.from(pkRoleIdMap.values())
  queryRoleIds = [].concat(roleIds, pkRoleIds)
  const roleInfoMap = await getRoleInfoMap(queryRoleIds)

  for (let i = 0; i < rankInfo.length; i++) {
    const ele = rankInfo[i]
    const pkRoleId = pkRoleIdMap.get(ele.RoleId)
    const player: IRankPlayer = {
      rank: i + 1,
      renqi: ele.RenQi,
      current: roleInfoMap.get(pkRoleId) || null,
      origin: roleInfoMap.get(ele.RoleId) || null
    }
    result.push(player)
  }

  return result
}