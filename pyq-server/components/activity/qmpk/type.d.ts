interface IShare {
  roleId: number
  originRoleId: number
  text: string
  url: string
  /** 时间戳(ms) */
  ts?: number
}

interface TopRankItem {
  RoleId: number,
  RenQi: number
}

interface ILikeEvent {
  momentId: number,
  roleId: number
}


interface IRankPlayer {
  rank: number
  renqi: number
  origin: IPlayer
  current: IPlayer
}

interface IPlayer {
  roleid: number,
  rolename: string
  careerid: number,
  gender: number,
  servername: string,
  avatar: string
}