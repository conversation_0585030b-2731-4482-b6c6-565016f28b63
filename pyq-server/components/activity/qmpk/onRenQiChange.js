"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QmPkRenQiOnEvents = exports.QmPkRenQiEventHandler = void 0;
const constants_1 = require("../../../../common/constants");
const constants_2 = require("../../../constants");
const logger_1 = require("../../../logger");
const models_1 = require("../../../models");
const pkRenQiDailyRank_1 = require("./pkRenQiDailyRank");
const shareMoment_1 = require("./shareMoment");
const logger = (0, logger_1.clazzLogger)("qmpk/onRenQiChange");
var LikeTypes;
(function (LikeTypes) {
    LikeTypes[LikeTypes["LIKE"] = 1] = "LIKE";
    LikeTypes[LikeTypes["CANCEL_LIKE"] = 2] = "CANCEL_LIKE";
})(LikeTypes || (LikeTypes = {}));
function tsToDate(ts) {
    return ts ? new Date(ts) : new Date();
}
function updatePkRenQi(payload, type) {
    return __awaiter(this, void 0, void 0, function* () {
        const momentId = payload.momentId;
        const moment = yield models_1.MomentModel.findOne({ ID: momentId, Status: constants_1.Statues.Normal }, ["RoleId", "CreateTime"]);
        if (!moment) {
            logger.warn({ momentId }, "UpdatePkRenQiMomentNotFound");
            return;
        }
        const isAttendPk = yield shareMoment_1.ShareMomentSet.create(new Date(moment.CreateTime)).isPkShareMoment(momentId);
        if (isAttendPk) {
            const rankList = pkRenQiDailyRank_1.PkRenQiDailyRank.from(new Date(moment.CreateTime));
            if (type === LikeTypes.LIKE) {
                yield rankList.incrRenQi(moment.RoleId, 1, tsToDate(payload.ts));
            }
            else {
                yield rankList.incrRenQi(moment.RoleId, -1, tsToDate(payload.ts));
            }
        }
        else {
            logger.debug({ momentId }, "UpdatePkRenQiMomentNotPk");
        }
    });
}
class QmPkRenQiEventHandler {
    static onLikeMoment(payload) {
        return __awaiter(this, void 0, void 0, function* () {
            updatePkRenQi(payload, LikeTypes.LIKE);
        });
    }
    static onCancelLikeMoment(payload) {
        return __awaiter(this, void 0, void 0, function* () {
            updatePkRenQi(payload, LikeTypes.CANCEL_LIKE);
        });
    }
}
exports.QmPkRenQiEventHandler = QmPkRenQiEventHandler;
exports.QmPkRenQiOnEvents = [
    { name: constants_2.EventNames.LIKE_MOMENT, handler: QmPkRenQiEventHandler.onLikeMoment },
    { name: constants_2.EventNames.CANCEL_LIKE_MOMENT, handler: QmPkRenQiEventHandler.onCancelLikeMoment },
];
//# sourceMappingURL=onRenQiChange.js.map