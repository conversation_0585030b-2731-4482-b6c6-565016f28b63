"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.gmWebImportSendFlowerHandler = exports.gmWebUiHandler = void 0;
const fs = require("fs");
const path = require("path");
const node_xlsx_1 = require("node-xlsx");
const logger_1 = require("../../logger");
const eventService_1 = require("../../../service/qnm/pyq/eventService");
const logger = (0, logger_1.clazzLogger)("gmWebController");
function gmWebUiHandler(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        const html = fs.readFileSync(path.join(__dirname, "../../../public/gm-web/web-ui.html"));
        res.end(html);
        return next();
    });
}
exports.gmWebUiHandler = gmWebUiHandler;
function gmWebImportSendFlowerHandler(req, res, next) {
    var _a;
    return __awaiter(this, void 0, void 0, function* () {
        const file = (_a = req.files) === null || _a === void 0 ? void 0 : _a.excelFile;
        if (!file) {
            res.send(400, { error: "No file uploaded or file buffer is missing." });
            return next();
        }
        let workSheets;
        try {
            workSheets = node_xlsx_1.default.parse(file.path);
            if (!workSheets || workSheets.length === 0 || !workSheets[0].data || workSheets[0].data.length === 0) {
                res.send(400, { error: "Could not parse any worksheet, or the worksheet is empty." });
                return next();
            }
        }
        catch (parseError) {
            logger.error("Initial Excel parsing error:", parseError);
            res.send(500, { error: "Error parsing the Excel file.", details: parseError.message });
            return next();
        }
        res.setHeader('Content-Type', 'text/event-stream');
        res.setHeader('Cache-Control', 'no-cache');
        res.setHeader('Connection', 'keep-alive');
        res.setHeader('X-Accel-Buffering', 'no');
        res.flushHeaders();
        const sendSseEvent = (eventName, data) => {
            if (res.writableEnded)
                return;
            res.write(`event: ${eventName}\n`);
            res.write(`data: ${JSON.stringify(data)}\n\n`);
        };
        const sheetData = workSheets[0].data;
        const sheetName = workSheets[0].name;
        const originalFileName = file.name;
        let successfulRows = 0;
        let failedRows = 0;
        let totalRowsProcessed = 0;
        let processingStoppedEarly = false;
        let interruptErrorRowIndex = -1;
        let interruptErrorMessage = "";
        let interruptErrorRowData = null;
        try {
            const headerRow = sheetData[0] || [];
            const actualDataRows = sheetData.slice(1);
            const totalDataRowsCount = actualDataRows.length;
            sendSseEvent('StreamStart', {
                message: `开始处理文件: ${originalFileName}, 工作表: ${sheetName}`,
                fileName: originalFileName,
                sheetName: sheetName,
                totalRows: totalDataRowsCount
            });
            const getIndex = (colName) => headerRow.indexOf(colName);
            const timeIndex = getIndex("time");
            const serverIndex = getIndex("server");
            const roleIdIndex = getIndex("role_id");
            const targetIdIndex = getIndex("targetId");
            const flowerTemplateIdIndex = getIndex("flowerTemplateId");
            const flowerNameIndex = getIndex("flowerName");
            const popularityIndex = getIndex("popularity");
            const numIndex = getIndex("num");
            if ([timeIndex, serverIndex, roleIdIndex, targetIdIndex, flowerTemplateIdIndex, flowerNameIndex, numIndex, popularityIndex].some(index => index === -1)) {
                throw new Error("Excel文件中缺少必要的列头 (time, server, role_id, targetId, flowerTemplateId, flowerName, num, popularity)");
            }
            for (let i = 0; i < actualDataRows.length; i++) {
                totalRowsProcessed++;
                const row = actualDataRows[i];
                let success = true;
                let procMessage = '处理成功';
                const flowerEventPayload = {
                    type: eventService_1.EventType.SEND_FLOWER + "",
                    server: row[serverIndex],
                    roleid: row[roleIdIndex],
                    targetid: row[targetIdIndex],
                    parameter: (0, eventService_1.toFlowerParams)(row[flowerTemplateIdIndex], row[flowerNameIndex], row[numIndex], row[popularityIndex]),
                    timestamp: new Date(row[timeIndex]).getTime()
                };
                try {
                    if (flowerEventPayload.timestamp === null || isNaN(flowerEventPayload.timestamp)) {
                        throw new Error('无效的时间戳格式');
                    }
                    if (!flowerEventPayload.server || !flowerEventPayload.roleid || !flowerEventPayload.targetid) {
                        throw new Error('服务器、角色ID或目标ID不能为空');
                    }
                    yield eventService_1.EventService.addFlowerEvent(flowerEventPayload);
                    successfulRows++;
                }
                catch (error) {
                    failedRows++;
                    success = false;
                    procMessage = error.message || error.msg || "行处理时发生未知错误";
                    logger.error(`Error processing row ${i + 1} (Excel row ${i + 2}) for file ${originalFileName}:`, procMessage);
                    processingStoppedEarly = true;
                    interruptErrorRowIndex = i;
                    interruptErrorMessage = procMessage;
                    interruptErrorRowData = row;
                }
                sendSseEvent('FlowerEvent', {
                    rowIndex: i,
                    totalRowsInSheet: totalDataRowsCount,
                    rowData: flowerEventPayload,
                    success: success,
                    message: procMessage,
                });
                if (processingStoppedEarly) {
                    break;
                }
            }
        }
        catch (streamError) {
            logger.error(`Critical error during SSE streaming for ${originalFileName}:`, streamError);
            if (!res.writableEnded) {
                try {
                    sendSseEvent('StreamError', {
                        error: "处理过程中发生严重错误，流已中断。",
                        details: streamError.message
                    });
                }
                catch (finalErr) {
                    logger.error("Failed to send StreamError event:", finalErr);
                }
            }
            processingStoppedEarly = true;
            interruptErrorMessage = streamError.message || "未知流错误";
        }
        finally {
            let finalMessage = `${originalFileName} 处理完毕。`;
            if (processingStoppedEarly && failedRows > 0) {
                const displayRowNumber = interruptErrorRowIndex !== -1 ? interruptErrorRowIndex + 2 : "未知";
                const dataString = interruptErrorRowData ? JSON.stringify(interruptErrorRowData) : "无";
                finalMessage = `${originalFileName} 处理在Excel第 ${displayRowNumber} 行 (数据行 ${interruptErrorRowIndex !== -1 ? interruptErrorRowIndex + 1 : "未知"}) 因错误中断: ${interruptErrorMessage}。 错误行数据: ${dataString}`;
            }
            else if (processingStoppedEarly) {
                finalMessage = `${originalFileName} 处理因流错误中断: ${interruptErrorMessage}`;
            }
            sendSseEvent('ProcessingComplete', {
                message: finalMessage,
                fileName: originalFileName,
                successfulRows: successfulRows,
                failedRows: failedRows,
                totalRowsProcessed: totalRowsProcessed
            });
            if (!res.writableEnded) {
                res.end();
            }
        }
    });
}
exports.gmWebImportSendFlowerHandler = gmWebImportSendFlowerHandler;
//# sourceMappingURL=gmWebController.js.map