"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.findComment = void 0;
const constants_1 = require("../../../common/constants");
const errorCodes_1 = require("../../errorCodes");
const models_1 = require("../../models");
function findComment(id) {
    return __awaiter(this, void 0, void 0, function* () {
        const r = yield models_1.CardNotionCommentModel.findOne({ id, status: constants_1.Statues.Normal });
        if (!r) {
            return (0, errorCodes_1.BussError)(errorCodes_1.CardErrors.CommentNotFound);
        }
        return r;
    });
}
exports.findComment = findComment;
//# sourceMappingURL=service.js.map