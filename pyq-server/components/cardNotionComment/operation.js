"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.cardNotionCommentDel = exports.cardNotionCommentAdd = exports.cardNotionCommentList = void 0;
const _ = require("lodash");
const constants_1 = require("../../../common/constants");
const models_1 = require("../../models");
const roleInfo_1 = require("../../services/roleInfo");
const service_1 = require("../card/service");
const service_2 = require("../cardNotification/service");
const service_3 = require("../cardNotion/service");
const service_4 = require("./service");
/** 列出卡片想法评论列表 */
function cardNotionCommentList(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const roleId = params.roleid;
        const notionId = params.notionId;
        const notion = yield (0, service_3.findNotion)(notionId);
        const cardId = notion.cardId;
        const card = yield (0, service_1.getUserCardInfoView)(roleId, cardId);
        const result = yield models_1.CardNotionCommentModel.powerListQuery({
            select: ['id', 'roleId', 'createTime', 'text', 'replyId', 'replyRoleId'],
            where: { status: constants_1.Statues.Normal, notionId: notionId },
            orderBy: [['id'], ['desc']],
            pagination: { page: params.page, pageSize: params.pageSize }
        });
        const rawList = result.list;
        const queryRoleIds = _.flatMap(rawList, r => [r.roleId, r.replyRoleId]);
        const roleInfoMap = yield (0, roleInfo_1.getRoleInfoMap)(queryRoleIds);
        const count = result.meta.totalCount;
        const list = rawList.map(r => {
            const roleInfo = roleInfoMap.get(r.roleId) || { RoleName: "", Clazz: 0, Grade: 0, Gender: 0, XianFanStatus: 0 };
            const replyInfo = roleInfoMap.get(r.replyRoleId) || { RoleName: "" };
            return {
                id: r.id,
                cardId: cardId,
                notionId: notionId,
                text: r.text,
                createTime: r.createTime,
                roleId: r.roleId,
                roleName: roleInfo.RoleName,
                clazz: roleInfo.Clazz,
                grade: roleInfo.Grade,
                gender: roleInfo.Gender,
                xianfanstatus: roleInfo.XianFanStatus,
                replyId: r.replyId,
                replyRoleId: r.replyRoleId,
                replyRoleName: replyInfo.RoleName,
            };
        });
        const data = { list, card, count };
        return data;
    });
}
exports.cardNotionCommentList = cardNotionCommentList;
/** 添加卡片想法评论 */
function cardNotionCommentAdd(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const roleId = params.roleid;
        const notionId = params.notionId;
        const notion = yield (0, service_3.findNotion)(notionId);
        const commentText = params.text || "";
        let replyComment = null;
        const insertProps = {
            roleId: roleId,
            notionId: params.notionId,
            text: commentText,
            status: constants_1.Statues.Normal,
            replyId: 0,
            replyRoleId: 0,
            type: 0 /* Comment */,
            createTime: Date.now(),
        };
        if (params.replyCommentId) {
            insertProps.type = 1 /* Reply */;
            replyComment = yield (0, service_4.findComment)(params.replyCommentId);
            insertProps.replyId = params.replyCommentId;
            insertProps.replyRoleId = replyComment.roleId;
        }
        const newCommentId = yield models_1.CardNotionCommentModel.insert(insertProps);
        const data = {
            id: newCommentId,
        };
        (0, service_3.updateCardNotionHotState)(notion, 2 /* Comment */, roleId);
        (0, service_2.addNotificationByAction)(2 /* NotionBeComment */, {
            roleId,
            notionId,
            text: commentText,
            relateId: notion.id,
            targetId: notion.roleId,
            cardId: notion.cardId,
            actionId: (0, service_2.actionNotificationId)(2 /* NotionBeComment */, newCommentId)
        });
        if (insertProps.type === 1 /* Reply */) {
            (0, service_2.addNotificationByAction)(3 /* CommentBeReplied */, {
                roleId,
                notionId,
                text: commentText,
                relateId: replyComment.id,
                targetId: replyComment.roleId,
                cardId: notion.cardId,
                actionId: (0, service_2.actionNotificationId)(3 /* CommentBeReplied */, newCommentId)
            });
        }
        return data;
    });
}
exports.cardNotionCommentAdd = cardNotionCommentAdd;
/** 删除卡片想法评论 */
function cardNotionCommentDel(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const comment = yield (0, service_4.findComment)(params.commentId);
        const notion = yield (0, service_3.findNotion)(comment.notionId);
        const ret = yield models_1.CardNotionCommentModel.softDeleteByCondition({ id: params.commentId, roleId: params.roleid });
        const isOk = ret.affectedRows > 0;
        const data = { isOk };
        yield (0, service_2.delNotificationByActionId)(2 /* NotionBeComment */, comment.id);
        (0, service_3.updateCardNotionHotState)(notion, 3 /* DelComment */, params.roleid);
        return data;
    });
}
exports.cardNotionCommentDel = cardNotionCommentDel;
//# sourceMappingURL=operation.js.map