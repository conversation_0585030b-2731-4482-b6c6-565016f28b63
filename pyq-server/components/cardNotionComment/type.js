"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = void 0;
exports.ReqSchemas = {
    CardNotionCommentList: {
        roleid: { type: Number },
        notionId: { type: Number },
        page: { type: Number, default: 1 },
        pageSize: { type: Number, default: 10 },
    },
    CardNotionCommentAdd: {
        roleid: { type: Number },
        notionId: { type: Number },
        text: { type: String },
        replyCommentId: { type: Number, required: false },
    },
    CardNotionCommentDel: {
        roleid: { type: Number },
        commentId: { type: Number },
    },
};
//# sourceMappingURL=type.js.map