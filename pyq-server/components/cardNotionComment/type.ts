import { operations } from "../../types/swagger";

export namespace CardNotionCommentReq {
  export type List = operations["cardNotionCommentList"]["parameters"]["query"];
  export type Add = operations["cardNotionCommentAdd"]["parameters"]["query"];
  export type Del = operations["cardNotionCommentDel"]["parameters"]["query"];
}

export namespace CardNotionCommentRes {
  export type List = operations["cardNotionCommentList"]["responses"]["200"]["content"]["application/json"]["data"];
  export type Add = operations["cardNotionCommentAdd"]["responses"]["200"]["content"]["application/json"]["data"];
  export type Del = operations["cardNotionCommentDel"]["responses"]["200"]["content"]["application/json"]["data"];
}

export const ReqSchemas = {
  CardNotionCommentList: {
    roleid: { type: Number },
    notionId: { type: Number },
    page: { type: Number, default: 1 },
    pageSize: { type: Number, default: 10 },
  },

  CardNotionCommentAdd: {
    roleid: { type: Number },
    notionId: { type: Number },
    text: { type: String },
    replyCommentId: { type: Number, required: false },
  },

  CardNotionCommentDel: {
    roleid: { type: Number },
    commentId: { type: Number },
  },
};
