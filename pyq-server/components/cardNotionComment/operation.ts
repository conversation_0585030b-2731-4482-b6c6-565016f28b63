import * as _ from 'lodash'
import { Statues } from "../../../common/constants";
import { CardNotionCommentModel, CardNotionCommentRecord } from "../../models";
import { getRoleInfoMap } from "../../services/roleInfo";
import { ECommentType, ENotificationType } from '../../types/type';
import { getUserCardInfoView } from "../card/service";
import { actionNotificationId, addNotificationByAction, delNotificationByActionId } from '../cardNotification/service';
import { ENotionAction, findNotion, updateCardNotionHotState } from "../cardNotion/service";
import { findComment } from './service';
import { CardNotionCommentReq, CardNotionCommentRes } from "./type";

/** 列出卡片想法评论列表 */
export async function cardNotionCommentList(params: CardNotionCommentReq.List): Promise<CardNotionCommentRes.List> {
  const roleId = params.roleid
  const notionId = params.notionId
  const notion = await findNotion(notionId)
  const cardId = notion.cardId
  const card = await getUserCardInfoView(roleId, cardId)
  const result = await CardNotionCommentModel.powerListQuery({
    select: ['id', 'roleId', 'createTime', 'text', 'replyId', 'replyRoleId'],
    where: { status: Statues.Normal, notionId: notionId },
    orderBy: [['id'], ['desc']],
    pagination: { page: params.page, pageSize: params.pageSize }
  })

  const rawList = result.list
  const queryRoleIds = _.flatMap(rawList, r => [r.roleId, r.replyRoleId])
  const roleInfoMap = await getRoleInfoMap(queryRoleIds)
  const count = result.meta.totalCount
  const list: CardNotionCommentRes.List["list"] = rawList.map(r => {
    const roleInfo = roleInfoMap.get(r.roleId) || { RoleName: "", Clazz: 0, Grade: 0, Gender: 0, XianFanStatus: 0 }
    const replyInfo = roleInfoMap.get(r.replyRoleId) || { RoleName: "" }
    return {
      id: r.id,
      cardId: cardId,
      notionId: notionId,
      text: r.text,
      createTime: r.createTime,
      roleId: r.roleId,
      roleName: roleInfo.RoleName,
      clazz: roleInfo.Clazz,
      grade: roleInfo.Grade,
      gender: roleInfo.Gender,
      xianfanstatus: roleInfo.XianFanStatus,
      replyId: r.replyId,
      replyRoleId: r.replyRoleId,
      replyRoleName: replyInfo.RoleName,
    }
  })
  const data: CardNotionCommentRes.List = { list, card, count };
  return data;
}


/** 添加卡片想法评论 */
export async function cardNotionCommentAdd(params: CardNotionCommentReq.Add): Promise<CardNotionCommentRes.Add> {
  const roleId = params.roleid
  const notionId = params.notionId
  const notion = await findNotion(notionId)
  const commentText = params.text || ""
  let replyComment: CardNotionCommentRecord = null
  const insertProps: Omit<CardNotionCommentRecord, "id"> = {
    roleId: roleId,
    notionId: params.notionId,
    text: commentText,
    status: Statues.Normal,
    replyId: 0,
    replyRoleId: 0,
    type: ECommentType.Comment,
    createTime: Date.now(),
  };
  if (params.replyCommentId) {
    insertProps.type = ECommentType.Reply
    replyComment = await findComment(params.replyCommentId)
    insertProps.replyId = params.replyCommentId
    insertProps.replyRoleId = replyComment.roleId
  }
  const newCommentId = await CardNotionCommentModel.insert(insertProps);

  const data: CardNotionCommentRes.Add = {
    id: newCommentId,
  };
  updateCardNotionHotState(notion, ENotionAction.Comment, roleId)

  addNotificationByAction(ENotificationType.NotionBeComment, {
    roleId,
    notionId,
    text: commentText,
    relateId: notion.id,
    targetId: notion.roleId,
    cardId: notion.cardId,
    actionId: actionNotificationId(ENotificationType.NotionBeComment, newCommentId)
  })

  if (insertProps.type === ECommentType.Reply) {
    addNotificationByAction(ENotificationType.CommentBeReplied, {
      roleId,
      notionId,
      text: commentText,
      relateId: replyComment.id,
      targetId: replyComment.roleId,
      cardId: notion.cardId,
      actionId: actionNotificationId(ENotificationType.CommentBeReplied, newCommentId)
    })
  }

  return data;
}

/** 删除卡片想法评论 */
export async function cardNotionCommentDel(params: CardNotionCommentReq.Del): Promise<CardNotionCommentRes.Del> {
  const comment = await findComment(params.commentId)
  const notion = await findNotion(comment.notionId)
  const ret = await CardNotionCommentModel.softDeleteByCondition({ id: params.commentId, roleId: params.roleid })
  const isOk = ret.affectedRows > 0
  const data: CardNotionCommentRes.Del = { isOk };
  await delNotificationByActionId(ENotificationType.NotionBeComment, comment.id)
  updateCardNotionHotState(notion, ENotionAction.DelComment, params.roleid)
  return data;
}