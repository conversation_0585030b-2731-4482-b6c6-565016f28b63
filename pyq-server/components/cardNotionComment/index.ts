import { Path } from "../../types/type";
import { card<PERSON><PERSON>CommentList, cardNotionCommentAdd, cardNotionCommentDel } from "./operation";
import { ReqSchemas } from "./type";

export const paths: Path[] = [
  {
    method: "post",
    url: "/card/notion/comment/list",
    paramsSchema: ReqSchemas.CardNotionCommentList,
    operation: cardNotionCommentList,
  },
  {
    method: "post",
    url: "/card/notion/comment/add",
    paramsSchema: ReqSchemas.CardNotionCommentAdd,
    operation: cardNotionCommentAdd,
  },
  {
    method: "post",
    url: "/card/notion/comment/del",
    paramsSchema: ReqSchemas.CardNotionCommentDel,
    operation: cardNotionCommentDel,
  },
];

export const CardNotionCommentComponent = {
  paths: paths,
  prefix: "/card/notion/comment/",
};
