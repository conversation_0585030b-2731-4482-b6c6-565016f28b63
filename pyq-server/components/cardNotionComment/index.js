"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CardNotionCommentComponent = exports.paths = void 0;
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "post",
        url: "/card/notion/comment/list",
        paramsSchema: type_1.ReqSchemas.CardNotionCommentList,
        operation: operation_1.cardNotionCommentList,
    },
    {
        method: "post",
        url: "/card/notion/comment/add",
        paramsSchema: type_1.ReqSchemas.CardNotionCommentAdd,
        operation: operation_1.cardNotionCommentAdd,
    },
    {
        method: "post",
        url: "/card/notion/comment/del",
        paramsSchema: type_1.ReqSchemas.CardNotionCommentDel,
        operation: operation_1.cardNotionCommentDel,
    },
];
exports.CardNotionCommentComponent = {
    paths: exports.paths,
    prefix: "/card/notion/comment/",
};
//# sourceMappingURL=index.js.map