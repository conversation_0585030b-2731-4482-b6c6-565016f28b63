import { Path } from "../../types/type";
import { slientLoveAdd, slientLoveCancel } from "./operation";
import { ReqSchemas } from "./type";

export const paths: Path[] = [
  {
    method: "post",
    url: "/slient_love/add",
    paramsSchema: ReqSchemas.SlientLoveAdd,
    operation: slientLoveAdd,
    option: { skipAuth: true },
  },
  {
    method: "post",
    url: "/slient_love/cancel",
    paramsSchema: ReqSchemas.SlientLoveCancel,
    operation: slientLoveCancel,
    option: { skipAuth: true },
  },
];

export const SlientLoveComponent = {
  paths: paths,
  prefix: "/slient_love/",
};
