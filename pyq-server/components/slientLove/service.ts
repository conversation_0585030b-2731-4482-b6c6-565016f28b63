import { cacheKeyGen } from "../../../common/util2";
import { smartMemorize } from "../../common/cacheUtil";
import { ESlientLoveStatus, ONE_HOUR_SECONDS } from "../../common/constants";
import { getRedis } from "../../common/redis";
import { SlientLoveModel } from "../../models";

export async function getRelationFromDB(roleId: number, targetId: number): Promise<ESlientLoveStatus> {
  if (roleId === targetId) return ESlientLoveStatus.NoRelation;
  const iLoveU = await isLoving(roleId, targetId);
  if (iLoveU) {
    const uLoveMe = await isLoving(targetId, roleId);
    if (uLoveMe) {
      return ESlientLoveStatus.LovingEach;
    } else {
      return ESlientLoveStatus.Loving;
    }
  } else {
    return ESlientLoveStatus.NoRelation;
  }
}

export async function isLoving(roleId: number, targetId: number): Promise<boolean> {
  const iLoveYou = await SlientLoveModel.findOne({ RoleId: roleId, TargetId: targetId }, ["ID"]);
  return !!iLoveYou;
}

function slientLoveRelationCacheKey(roleId: number, targetId: number) {
  return cacheKeyGen("l10_slient_love_relation", { roleId, targetId });
}

export const getRelation = smartMemorize(getRelationFromDB, {
  keyGen: function (roleId, targetId) {
    return slientLoveRelationCacheKey(roleId, targetId);
  },
  expireSeconds: ONE_HOUR_SECONDS,
});

export async function makeRelationCacheInvalid(roleId: number, targetId: number) {
  const key = slientLoveRelationCacheKey(roleId, targetId);
  return getRedis().delAsync(key);
}

export async function onRelationChange(roleId: number, targetId: number) {
  return Promise.all([makeRelationCacheInvalid(roleId, targetId), makeRelationCacheInvalid(targetId, roleId)]);
}
