"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.onRelationChange = exports.makeRelationCacheInvalid = exports.getRelation = exports.isLoving = exports.getRelationFromDB = void 0;
const util2_1 = require("../../../common/util2");
const cacheUtil_1 = require("../../common/cacheUtil");
const constants_1 = require("../../common/constants");
const redis_1 = require("../../common/redis");
const models_1 = require("../../models");
function getRelationFromDB(roleId, targetId) {
    return __awaiter(this, void 0, void 0, function* () {
        if (roleId === targetId)
            return constants_1.ESlientLoveStatus.NoRelation;
        const iLoveU = yield isLoving(roleId, targetId);
        if (iLoveU) {
            const uLoveMe = yield isLoving(targetId, roleId);
            if (uLoveMe) {
                return constants_1.ESlientLoveStatus.LovingEach;
            }
            else {
                return constants_1.ESlientLoveStatus.Loving;
            }
        }
        else {
            return constants_1.ESlientLoveStatus.NoRelation;
        }
    });
}
exports.getRelationFromDB = getRelationFromDB;
function isLoving(roleId, targetId) {
    return __awaiter(this, void 0, void 0, function* () {
        const iLoveYou = yield models_1.SlientLoveModel.findOne({ RoleId: roleId, TargetId: targetId }, ["ID"]);
        return !!iLoveYou;
    });
}
exports.isLoving = isLoving;
function slientLoveRelationCacheKey(roleId, targetId) {
    return (0, util2_1.cacheKeyGen)("l10_slient_love_relation", { roleId, targetId });
}
exports.getRelation = (0, cacheUtil_1.smartMemorize)(getRelationFromDB, {
    keyGen: function (roleId, targetId) {
        return slientLoveRelationCacheKey(roleId, targetId);
    },
    expireSeconds: constants_1.ONE_HOUR_SECONDS,
});
function makeRelationCacheInvalid(roleId, targetId) {
    return __awaiter(this, void 0, void 0, function* () {
        const key = slientLoveRelationCacheKey(roleId, targetId);
        return (0, redis_1.getRedis)().delAsync(key);
    });
}
exports.makeRelationCacheInvalid = makeRelationCacheInvalid;
function onRelationChange(roleId, targetId) {
    return __awaiter(this, void 0, void 0, function* () {
        return Promise.all([makeRelationCacheInvalid(roleId, targetId), makeRelationCacheInvalid(targetId, roleId)]);
    });
}
exports.onRelationChange = onRelationChange;
//# sourceMappingURL=service.js.map