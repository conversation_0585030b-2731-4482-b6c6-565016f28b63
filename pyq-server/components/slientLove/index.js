"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SlientLoveComponent = exports.paths = void 0;
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "post",
        url: "/slient_love/add",
        paramsSchema: type_1.ReqSchemas.SlientLoveAdd,
        operation: operation_1.slientLoveAdd,
        option: { skipAuth: true },
    },
    {
        method: "post",
        url: "/slient_love/cancel",
        paramsSchema: type_1.ReqSchemas.SlientLoveCancel,
        operation: operation_1.slientLoveCancel,
        option: { skipAuth: true },
    },
];
exports.SlientLoveComponent = {
    paths: exports.paths,
    prefix: "/slient_love/",
};
//# sourceMappingURL=index.js.map