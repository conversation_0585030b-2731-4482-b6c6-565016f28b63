import { operations } from "../../types/swagger";

export namespace SlientLoveReq {
  export type Add = operations["slientLoveAdd"]["parameters"]["query"];
  export type Cancel = operations["slientLoveCancel"]["parameters"]["query"];
}

export namespace SlientLoveRes {
  export type Add = operations["slientLoveAdd"]["responses"]["200"]["content"]["application/json"]["data"];
  export type Cancel = operations["slientLoveCancel"]["responses"]["200"]["content"]["application/json"]["data"];
}

export const ReqSchemas = {
  SlientLoveAdd: {
    roleid: { type: Number },
    targetid: { type: Number },
  },

  SlientLoveCancel: {
    roleid: { type: Number },
    targetid: { type: Number },
  },
};
