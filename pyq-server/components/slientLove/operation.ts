import { ESlientLoveStatus } from "../../common/constants";
import { BussError, SlientLoveErrors } from "../../errorCodes";
import { SlientLoveModel, SlientLoveRecord } from "../../models";
import { isFollowing } from "../../services/follow";
import { onRelationChange } from "./service";
import { SlientLoveReq, SlientLoveRes } from "./type";

/** 添加对方为暗恋 */
export async function slientLoveAdd(params: SlientLoveReq.Add): Promise<SlientLoveRes.Add> {
  const { roleid, targetid } = params;
  const isFollow = await isFollowing(roleid, targetid);
  if (!isFollow) {
    return BussError(SlientLoveErrors.NeedLoveFirst);
  }

  const data: SlientLoveRes.Add = {
    status: ESlientLoveStatus.Loving,
    id: 0,
  };
  const iLoveYou = await SlientLoveModel.findOne({ RoleId: roleid, TargetId: targetid }, ["ID"]);
  if (iLoveYou) {
    return BussError(SlientLoveErrors.AlreadLoved);
  } else {
    const insertProps: Omit<SlientLoveRecord, "ID"> = {
      RoleId: roleid,
      TargetId: targetid,
      CreateTime: Date.now(),
    };
    const insertId = await SlientLoveModel.insert(insertProps);
    onRelationChange(roleid, targetid);
    const uLoveMe = await SlientLoveModel.findOne({ RoleId: targetid, TargetId: roleid }, ["ID"]);
    if (uLoveMe) {
      data.status = ESlientLoveStatus.LovingEach;
      data.id = insertId;
    }
  }

  return data;
}

/** 取消暗恋 */
export async function slientLoveCancel(params: SlientLoveReq.Cancel): Promise<SlientLoveRes.Cancel> {
  const { roleid, targetid } = params;
  const iLoveYou = await SlientLoveModel.findOne({ RoleId: roleid, TargetId: targetid }, ["ID"]);
  if (iLoveYou) {
    const ret = await SlientLoveModel.deleteByCondition({ ID: iLoveYou.ID });
    const isOk = ret.affectedRows > 0;
    const data: SlientLoveRes.Cancel = {
      isOk,
    };
    onRelationChange(roleid, targetid);
    return data;
  } else {
    return BussError(SlientLoveErrors.NeedLoveFirst);
  }
}
