"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.slientLoveCancel = exports.slientLoveAdd = void 0;
const constants_1 = require("../../common/constants");
const errorCodes_1 = require("../../errorCodes");
const models_1 = require("../../models");
const follow_1 = require("../../services/follow");
const service_1 = require("./service");
/** 添加对方为暗恋 */
function slientLoveAdd(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const { roleid, targetid } = params;
        const isFollow = yield (0, follow_1.isFollowing)(roleid, targetid);
        if (!isFollow) {
            return (0, errorCodes_1.BussError)(errorCodes_1.SlientLoveErrors.NeedLoveFirst);
        }
        const data = {
            status: constants_1.ESlientLoveStatus.Loving,
            id: 0,
        };
        const iLoveYou = yield models_1.SlientLoveModel.findOne({ RoleId: roleid, TargetId: targetid }, ["ID"]);
        if (iLoveYou) {
            return (0, errorCodes_1.BussError)(errorCodes_1.SlientLoveErrors.AlreadLoved);
        }
        else {
            const insertProps = {
                RoleId: roleid,
                TargetId: targetid,
                CreateTime: Date.now(),
            };
            const insertId = yield models_1.SlientLoveModel.insert(insertProps);
            (0, service_1.onRelationChange)(roleid, targetid);
            const uLoveMe = yield models_1.SlientLoveModel.findOne({ RoleId: targetid, TargetId: roleid }, ["ID"]);
            if (uLoveMe) {
                data.status = constants_1.ESlientLoveStatus.LovingEach;
                data.id = insertId;
            }
        }
        return data;
    });
}
exports.slientLoveAdd = slientLoveAdd;
/** 取消暗恋 */
function slientLoveCancel(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const { roleid, targetid } = params;
        const iLoveYou = yield models_1.SlientLoveModel.findOne({ RoleId: roleid, TargetId: targetid }, ["ID"]);
        if (iLoveYou) {
            const ret = yield models_1.SlientLoveModel.deleteByCondition({ ID: iLoveYou.ID });
            const isOk = ret.affectedRows > 0;
            const data = {
                isOk,
            };
            (0, service_1.onRelationChange)(roleid, targetid);
            return data;
        }
        else {
            return (0, errorCodes_1.BussError)(errorCodes_1.SlientLoveErrors.NeedLoveFirst);
        }
    });
}
exports.slientLoveCancel = slientLoveCancel;
//# sourceMappingURL=operation.js.map