import { Path } from "../../types/type";
import { dailyLoginLoginTime } from "./operation";
import { ReqSchemas } from "./type";

export const paths: Path[] = [
  {
    method: "get",
    url: "/daily_login/login_time",
    paramsSchema: ReqSchemas.DailyLoginLoginTime,
    operation: dailyLoginLoginTime,
    option: { skipAuth: true },
  },
];

export const DailyLoginComponent = {
  paths: paths,
  prefix: "/daily_login/",
};
