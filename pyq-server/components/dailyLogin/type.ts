import { operations } from "../../types/swagger";

export namespace DailyLoginReq {
  export type LoginTime = operations["dailyLoginLoginTime"]["parameters"]["query"];
}

export namespace DailyLoginRes {
  export type LoginTime = operations["dailyLoginLoginTime"]["responses"]["200"]["content"]["application/json"]["data"];
}

export const ReqSchemas = {
  DailyLoginLoginTime: {
    type: "object",
    properties: {
      roleid: { type: "number" },
      ds: { type: "string", pattern: "^\\d{4}-\\d{2}-\\d{2}$" },
      nonce: { type: "string", minLength: 6, maxLength: 6 },
      ts: { type: "number" },
      token: { type: "string" },
    },
    required: ["roleid", "ds", "nonce", "ts", "token"],
  },
};
