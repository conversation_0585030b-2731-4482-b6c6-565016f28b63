"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DailyLoginComponent = exports.paths = void 0;
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "get",
        url: "/daily_login/login_time",
        paramsSchema: type_1.ReqSchemas.DailyLoginLoginTime,
        operation: operation_1.dailyLoginLoginTime,
        option: { skipAuth: true },
    },
];
exports.DailyLoginComponent = {
    paths: exports.paths,
    prefix: "/daily_login/",
};
//# sourceMappingURL=index.js.map