import moment = require("moment");
import { errorCodes } from "../../errorCodes";
import { DailyLoginService } from "../../services/dailyLoginService";
import { DailyLoginReq, DailyLoginRes } from "./type";
import { dailyLoginCfg } from "../../common/config";
import { clazzLogger } from "../../logger";
import { checkAuthToken } from "../../services/authUtil";
const logger = clazzLogger("dailyLogin.operation");

/** 获取每日登录时间(只支持7d之内) */
export async function dailyLoginLoginTime(params: DailyLoginReq.LoginTime): Promise<DailyLoginRes.LoginTime> {
  const date = new Date(params.ds);
  if (isNaN(date.getTime())) {
    throw errorCodes.InvalidParams;
  }
  const isPass = checkAuthToken(params, dailyLoginCfg.secretKey);
  if (!isPass) {
    throw errorCodes.CheckTokenFailed;
  }
  const minQueryDate = moment().startOf("day").subtract(dailyLoginCfg.keepDays, "days");
  const maxQueryDate = moment().endOf("day");
  const queryDs = moment(date);
  logger.debug({ date, minQueryDate, maxQueryDate }, "DailyLoginQueryDsCheck");
  if (queryDs.isBefore(minQueryDate) || queryDs.isAfter(maxQueryDate)) {
    throw errorCodes.QueryDateInvalid;
  }
  const loginTime = await DailyLoginService.getLoginTime(params.roleid, new Date(params.ds));
  const data: DailyLoginRes.LoginTime = {
    roleid: params.roleid,
    loginTime,
  };
  return data;
}
