"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = void 0;
exports.ReqSchemas = {
    DailyLoginLoginTime: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            ds: { type: "string", pattern: "^\\d{4}-\\d{2}-\\d{2}$" },
            nonce: { type: "string", minLength: 6, maxLength: 6 },
            ts: { type: "number" },
            token: { type: "string" },
        },
        required: ["roleid", "ds", "nonce", "ts", "token"],
    },
};
//# sourceMappingURL=type.js.map