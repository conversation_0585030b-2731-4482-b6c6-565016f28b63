"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.dailyLoginLoginTime = void 0;
const moment = require("moment");
const errorCodes_1 = require("../../errorCodes");
const dailyLoginService_1 = require("../../services/dailyLoginService");
const config_1 = require("../../common/config");
const logger_1 = require("../../logger");
const authUtil_1 = require("../../services/authUtil");
const logger = (0, logger_1.clazzLogger)("dailyLogin.operation");
/** 获取每日登录时间(只支持7d之内) */
function dailyLoginLoginTime(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const date = new Date(params.ds);
        if (isNaN(date.getTime())) {
            throw errorCodes_1.errorCodes.InvalidParams;
        }
        const isPass = (0, authUtil_1.checkAuthToken)(params, config_1.dailyLoginCfg.secretKey);
        if (!isPass) {
            throw errorCodes_1.errorCodes.CheckTokenFailed;
        }
        const minQueryDate = moment().startOf("day").subtract(config_1.dailyLoginCfg.keepDays, "days");
        const maxQueryDate = moment().endOf("day");
        const queryDs = moment(date);
        logger.debug({ date, minQueryDate, maxQueryDate }, "DailyLoginQueryDsCheck");
        if (queryDs.isBefore(minQueryDate) || queryDs.isAfter(maxQueryDate)) {
            throw errorCodes_1.errorCodes.QueryDateInvalid;
        }
        const loginTime = yield dailyLoginService_1.DailyLoginService.getLoginTime(params.roleid, new Date(params.ds));
        const data = {
            roleid: params.roleid,
            loginTime,
        };
        return data;
    });
}
exports.dailyLoginLoginTime = dailyLoginLoginTime;
//# sourceMappingURL=operation.js.map