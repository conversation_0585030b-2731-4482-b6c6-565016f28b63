"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CardNotionComponent = exports.paths = void 0;
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "post",
        url: "/card/notion/show",
        paramsSchema: type_1.ReqSchemas.CardNotionShow,
        operation: operation_1.cardNotionShow,
    },
    {
        method: "post",
        url: "/card/notion/list",
        paramsSchema: type_1.ReqSchemas.CardNotionList,
        operation: operation_1.cardNotionList,
    },
    {
        method: "post",
        url: "/card/notion/add",
        paramsSchema: type_1.ReqSchemas.CardNotionAdd,
        operation: operation_1.cardNotionAdd,
    },
    {
        method: "post",
        url: "/card/notion/del",
        paramsSchema: type_1.ReqSchemas.CardNotionDel,
        operation: operation_1.cardNotionDel,
    },
    {
        method: "post",
        url: "/card/notion/like",
        paramsSchema: type_1.ReqSchemas.CardNotionLike,
        operation: operation_1.cardNotionLike,
    },
    {
        method: "post",
        url: "/card/notion/cancel_like",
        paramsSchema: type_1.ReqSchemas.CardNotionCancelLike,
        operation: operation_1.cardNotionCancelLike,
    },
];
exports.CardNotionComponent = {
    paths: exports.paths,
    prefix: "/card/notion/",
};
//# sourceMappingURL=index.js.map