"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = void 0;
exports.ReqSchemas = {
    CardNotionShow: {
        roleid: { type: Number },
        notionId: { type: Number },
    },
    CardNotionList: {
        roleid: { type: Number },
        card_id: { type: "integer" },
        sort_by: { type: String, values: ["new", "hot"] },
        page: { type: Number, default: 1 },
        pageSize: { type: Number, default: 10 },
    },
    CardNotionAdd: {
        roleid: { type: Number },
        card_id: { type: "integer" },
        text: { type: String },
    },
    CardNotionDel: {
        roleid: { type: Number },
        notionId: { type: Number },
    },
    CardNotionLike: {
        roleid: { type: Number },
        notionId: { type: Number },
    },
    CardNotionCancelLike: {
        roleid: { type: Number },
        notionId: { type: Number },
    },
};
//# sourceMappingURL=type.js.map