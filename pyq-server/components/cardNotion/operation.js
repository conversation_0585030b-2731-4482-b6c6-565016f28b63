"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.cardNotionCancelLike = exports.cardNotionLike = exports.cardNotionDel = exports.cardNotionAdd = exports.cardNotionList = exports.cardNotionShow = void 0;
const _ = require("lodash");
const constants_1 = require("../../../common/constants");
const util_1 = require("../../../common/util");
const errorCodes_1 = require("../../errorCodes");
const models_1 = require("../../models");
const roleInfo_1 = require("../../services/roleInfo");
const service_1 = require("../card/service");
const service_2 = require("../cardNotification/service");
const service_3 = require("./service");
/** 列出单个卡片的想法详情 */
function cardNotionShow(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const roleId = params.roleid;
        const notionId = params.notionId;
        const notion = yield (0, service_3.findNotion)(notionId);
        const cardId = notion.cardId;
        const card = yield (0, service_1.getUserCardInfoView)(roleId, cardId);
        const hotState = (0, service_3.formatHotState)(notion);
        const likeCount = hotState.like;
        const commentCount = hotState.comment;
        const isLiked = yield (0, service_3.isUserLikeNotion)(roleId, notion.id);
        const likeUserIds = (0, util_1.csvStrToIntArray)(notion.likeUsers);
        const queryRoleIds = _.concat(likeUserIds, [notion.roleId]);
        const roleInfoMap = yield (0, roleInfo_1.getRoleInfoMap)(queryRoleIds);
        const likeUsers = likeUserIds.map(roleId => {
            const r = roleInfoMap.get(roleId) || { RoleName: "" };
            return {
                roleId,
                roleName: r.RoleName
            };
        });
        const cr = roleInfoMap.get(notion.roleId) || { RoleName: "", Clazz: 0, Grade: 0, Gender: 0, XianFanStatus: 0 };
        const data = {
            id: notion.id,
            cardId: cardId,
            text: notion.text,
            hot: notion.hot,
            createTime: notion.hot,
            likeCount,
            commentCount,
            isLiked,
            roleId: notion.roleId,
            roleName: cr.RoleName,
            clazz: cr.Clazz,
            grade: cr.Grade,
            gender: cr.Gender,
            xianfanstatus: cr.XianFanStatus,
            likeUsers,
            card,
        };
        return data;
    });
}
exports.cardNotionShow = cardNotionShow;
/** 列出单个卡片下想法列表 */
function cardNotionList(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const roleId = params.roleid;
        const cardId = params.card_id;
        const card = yield (0, service_1.getUserCardInfoView)(roleId, cardId);
        let query = models_1.CardNotionModel.normalScope().where('cardId', cardId);
        if (params.sort_by === "new") {
            query = query.orderBy("id", "desc");
        }
        else if (params.sort_by === "hot") {
            query = query.orderBy("hot", "desc");
        }
        const page = Math.max(params.page, 1);
        const pageSize = Math.min(params.pageSize, 10);
        const result = yield models_1.CardNotionModel.powerListQuery({
            initQuery: query,
            select: ['id', 'roleId', 'createTime', 'text', 'hot', 'hotState'],
            where: { status: constants_1.Statues.Normal, cardId: cardId },
            pagination: { page, pageSize }
        });
        const rawList = result.list;
        const count = result.meta.totalCount;
        const notionIds = rawList.map(r => r.id);
        const queryRoleIds = _.map(rawList, r => r.roleId);
        const roleInfoFetcher = yield (0, roleInfo_1.getRoleInfoSafeFetcher)(queryRoleIds);
        const likeNotionIdSet = yield (0, service_3.getLikedNotionIdSet)(params.roleid, notionIds);
        const list = rawList.map(r => {
            const hotState = (0, service_3.formatHotState)(r);
            const likeCount = hotState.like;
            const commentCount = hotState.comment;
            const roleInfo = roleInfoFetcher(r.roleId);
            const isLiked = likeNotionIdSet.has(r.id);
            return {
                id: r.id,
                cardId: cardId,
                text: r.text,
                hot: r.hot,
                createTime: r.createTime,
                likeCount,
                commentCount,
                isLiked,
                roleId: r.roleId,
                roleName: roleInfo.RoleName,
                clazz: roleInfo.Clazz,
                grade: roleInfo.Grade,
                gender: roleInfo.Gender,
                xianfanstatus: roleInfo.XianFanStatus,
            };
        });
        const data = { list, card, count };
        return data;
    });
}
exports.cardNotionList = cardNotionList;
/** 添加卡片的想法 */
function cardNotionAdd(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const insertProps = {
            roleId: params.roleid,
            cardId: params.card_id,
            text: params.text || "",
            likeUsers: "",
            hot: 0,
            hotState: '',
            status: constants_1.Statues.Normal,
            createTime: Date.now(),
        };
        const newId = yield models_1.CardNotionModel.insert(insertProps);
        const data = { id: newId };
        return data;
    });
}
exports.cardNotionAdd = cardNotionAdd;
/** 删除卡片的想法 */
function cardNotionDel(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const ret = yield models_1.CardNotionModel.softDeleteByCondition({ id: params.notionId, roleId: params.roleid });
        const isOk = ret.affectedRows > 0;
        const data = { isOk };
        return data;
    });
}
exports.cardNotionDel = cardNotionDel;
/** 给卡片想法点赞 */
function cardNotionLike(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const roleId = params.roleid;
        const notionId = params.notionId;
        const notion = yield (0, service_3.findNotion)(notionId);
        const likeRecord = yield models_1.CardNotionLikeModel.findOne({ roleId, notionId });
        let newId = 0;
        if (likeRecord) {
            if (likeRecord.status === constants_1.Statues.Deleted) {
                yield models_1.CardNotionLikeModel.updateById(likeRecord.id, { status: constants_1.Statues.Normal });
                newId = likeRecord.id;
            }
            else {
                return (0, errorCodes_1.BussError)(errorCodes_1.CardErrors.CardNotionAlreadyLiked);
            }
        }
        else {
            const insertProps = {
                roleId,
                notionId,
                status: constants_1.Statues.Normal,
                createTime: Date.now(),
            };
            newId = yield models_1.CardNotionLikeModel.insert(insertProps);
        }
        const data = {
            isOk: newId > 0,
        };
        (0, service_3.updateCardNotionHotState)(notion, 0 /* Like */, roleId);
        (0, service_2.addNotificationByAction)(1 /* NotionBeLiked */, {
            roleId,
            text: "",
            relateId: notionId,
            notionId,
            cardId: notion.cardId,
            targetId: notion.roleId,
            actionId: (0, service_2.actionNotificationId)(1 /* NotionBeLiked */, newId)
        });
        return data;
    });
}
exports.cardNotionLike = cardNotionLike;
/** 给卡片想法取消点赞 */
function cardNotionCancelLike(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const roleId = params.roleid;
        const notionId = params.notionId;
        const notion = yield (0, service_3.findNotion)(notionId);
        const likeRecord = yield models_1.CardNotionLikeModel.findOne({ roleId, notionId, status: constants_1.Statues.Normal });
        if (likeRecord) {
            yield models_1.CardNotionLikeModel.updateById(likeRecord.id, { status: constants_1.Statues.Deleted });
        }
        else {
            return (0, errorCodes_1.BussError)(errorCodes_1.CardErrors.CardNotionNotLiked);
        }
        const data = {
            isOk: true,
        };
        (0, service_2.delNotificationByActionId)(1 /* NotionBeLiked */, likeRecord.id);
        (0, service_3.updateCardNotionHotState)(notion, 1 /* CancelLike */, roleId);
        return data;
    });
}
exports.cardNotionCancelLike = cardNotionCancelLike;
//# sourceMappingURL=operation.js.map