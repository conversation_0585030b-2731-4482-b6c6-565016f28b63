"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateCardNotionHotState = exports.getHotFromState = exports.getNewHotState = exports.formatHotState = exports.findNotion = exports.isUserLikeNotion = exports.getLikedNotionIdSet = exports.INIT_HOT_STATE = void 0;
const constants_1 = require("../../../common/constants");
const util_1 = require("../../../common/util");
const errorCodes_1 = require("../../errorCodes");
const models_1 = require("../../models");
const _ = require("lodash");
exports.INIT_HOT_STATE = { like: 0, comment: 0 };
function getLikedNotionIdSet(roleId, notionIds) {
    return __awaiter(this, void 0, void 0, function* () {
        const rows = yield models_1.CardNotionLikeModel.find({ roleId, status: constants_1.Statues.Normal, notionId: notionIds }, { cols: ["notionId"] });
        const likeIds = rows.map((r) => r.notionId);
        return new Set(likeIds);
    });
}
exports.getLikedNotionIdSet = getLikedNotionIdSet;
function isUserLikeNotion(roleId, notionId) {
    return __awaiter(this, void 0, void 0, function* () {
        const likeSet = yield getLikedNotionIdSet(roleId, [notionId]);
        return likeSet.has(notionId);
    });
}
exports.isUserLikeNotion = isUserLikeNotion;
function findNotion(id) {
    return __awaiter(this, void 0, void 0, function* () {
        const r = yield models_1.CardNotionModel.findOne({ id, status: constants_1.Statues.Normal });
        if (!r) {
            return (0, errorCodes_1.BussError)(errorCodes_1.CardErrors.NotionNotFound);
        }
        return r;
    });
}
exports.findNotion = findNotion;
function formatHotState(r) {
    const hotStatObj = (0, util_1.getJsonInfo)(r.hotState, {});
    if (hotStatObj) {
        return { like: hotStatObj.like || 0, comment: hotStatObj.comment || 0 };
    }
    else {
        return { like: 0, comment: 0 };
    }
}
exports.formatHotState = formatHotState;
function getNewHotState(hotState, action) {
    const newState = _.clone(hotState) || { like: 0, comment: 0 };
    if (action === 0 /* Like */) {
        newState.like = hotState.like + 1;
    }
    else if (action === 1 /* CancelLike */) {
        newState.like = Math.max(0, hotState.like - 1);
    }
    else if (action === 2 /* Comment */) {
        newState.comment = hotState.comment + 1;
    }
    else if (action === 3 /* DelComment */) {
        newState.comment = Math.max(0, hotState.comment - 1);
    }
    else {
        // do nothing
    }
    return newState;
}
exports.getNewHotState = getNewHotState;
function getHotFromState(hotState) {
    return hotState.like + hotState.comment;
}
exports.getHotFromState = getHotFromState;
function updateCardNotionHotState(notion, action, actionRoleId) {
    return __awaiter(this, void 0, void 0, function* () {
        const hotState = formatHotState(notion);
        const newState = getNewHotState(hotState, action);
        const newHot = getHotFromState(newState);
        let updateProps = { hotState: JSON.stringify(newState), hot: newHot };
        if (action === 0 /* Like */ || action === 1 /* CancelLike */) {
            const likeUserIds = (0, util_1.csvStrToIntArray)(notion.likeUsers);
            const newLikeUserIds = action === 0 /* Like */ ? [...likeUserIds, actionRoleId].slice(0, constants_1.LIKE_USER_MAX_SIZE) : likeUserIds.filter(id => id !== actionRoleId);
            const newLikeUserIdCsv = newLikeUserIds.join(',');
            updateProps.likeUsers = newLikeUserIdCsv;
        }
        const ret = yield models_1.CardNotionModel.updateById(notion.id, updateProps);
        return ret;
    });
}
exports.updateCardNotionHotState = updateCardNotionHotState;
//# sourceMappingURL=service.js.map