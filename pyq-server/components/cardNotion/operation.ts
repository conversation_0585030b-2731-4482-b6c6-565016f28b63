import * as _ from "lodash"
import { Statues } from "../../../common/constants";
import { csvStrToIntArray } from "../../../common/util";
import { BussError, CardErrors } from "../../errorCodes";
import { CardNotificationModel, CardNotionLikeModel, CardNotionLikeRecord, CardNotionModel, CardNotionRecord } from "../../models";
import { getRoleInfoMap, getRoleInfoSafeFetcher } from "../../services/roleInfo";
import { ENotificationType } from "../../types/type";
import { getUserCardInfoView, isUserLikeCard } from "../card/service";
import { actionNotificationId, addNotificationByAction, delNotificationByActionId } from "../cardNotification/service";
import { ENotionAction, findNotion, formatHotState, getLikedNotionIdSet, isUserLikeNotion, updateCardNotionHotState } from "./service";
import { CardNotionReq, CardNotionRes } from "./type";

/** 列出单个卡片的想法详情 */
export async function cardNotionShow(params: CardNotionReq.Show): Promise<CardNotionRes.Show> {
  const roleId = params.roleid
  const notionId = params.notionId
  const notion = await findNotion(notionId)
  const cardId = notion.cardId
  const card = await getUserCardInfoView(roleId, cardId)

  const hotState = formatHotState(notion)
  const likeCount = hotState.like
  const commentCount = hotState.comment

  const isLiked = await isUserLikeNotion(roleId, notion.id)
  const likeUserIds = csvStrToIntArray(notion.likeUsers)
  const queryRoleIds = _.concat(likeUserIds, [notion.roleId])
  const roleInfoMap = await getRoleInfoMap(queryRoleIds)

  const likeUsers: CardNotionRes.Show["likeUsers"] = likeUserIds.map(roleId => {
    const r = roleInfoMap.get(roleId) || { RoleName: "" }
    return {
      roleId,
      roleName: r.RoleName
    }
  })

  const cr = roleInfoMap.get(notion.roleId) || { RoleName: "", Clazz: 0, Grade: 0, Gender: 0, XianFanStatus: 0 }
  const data: CardNotionRes.Show = {
    id: notion.id,
    cardId: cardId,
    text: notion.text,
    hot: notion.hot,
    createTime: notion.hot,
    likeCount,
    commentCount,
    isLiked,
    roleId: notion.roleId,
    roleName: cr.RoleName,
    clazz: cr.Clazz,
    grade: cr.Grade,
    gender: cr.Gender,
    xianfanstatus: cr.XianFanStatus,
    likeUsers,
    card,
  };

  return data;
}

/** 列出单个卡片下想法列表 */
export async function cardNotionList(params: CardNotionReq.List): Promise<CardNotionRes.List> {
  const roleId = params.roleid
  const cardId = params.card_id
  const card = await getUserCardInfoView(roleId, cardId)

  let query = CardNotionModel.normalScope().where('cardId', cardId)
  if (params.sort_by === "new") {
    query = query.orderBy("id", "desc")
  } else if (params.sort_by === "hot") {
    query = query.orderBy("hot", "desc")
  }

  const page = Math.max(params.page, 1)
  const pageSize = Math.min(params.pageSize, 10)

  const result = await CardNotionModel.powerListQuery({
    initQuery: query,
    select: ['id', 'roleId', 'createTime', 'text', 'hot', 'hotState'],
    where: { status: Statues.Normal, cardId: cardId },
    pagination: { page, pageSize }
  })

  const rawList = result.list
  const count = result.meta.totalCount
  const notionIds = rawList.map(r => r.id)
  const queryRoleIds = _.map(rawList, r => r.roleId)
  const roleInfoFetcher = await getRoleInfoSafeFetcher(queryRoleIds)
  const likeNotionIdSet = await getLikedNotionIdSet(params.roleid, notionIds)

  const list: CardNotionRes.List["list"] = rawList.map(r => {
    const hotState = formatHotState(r)
    const likeCount = hotState.like
    const commentCount = hotState.comment
    const roleInfo = roleInfoFetcher(r.roleId)
    const isLiked = likeNotionIdSet.has(r.id)
    return {
      id: r.id,
      cardId: cardId,
      text: r.text,
      hot: r.hot,
      createTime: r.createTime,
      likeCount,
      commentCount,
      isLiked,
      roleId: r.roleId,
      roleName: roleInfo.RoleName,
      clazz: roleInfo.Clazz,
      grade: roleInfo.Grade,
      gender: roleInfo.Gender,
      xianfanstatus: roleInfo.XianFanStatus,
    }
  })

  const data: CardNotionRes.List = { list, card, count };

  return data;
}

/** 添加卡片的想法 */
export async function cardNotionAdd(params: CardNotionReq.Add): Promise<CardNotionRes.Add> {
  const insertProps: Omit<CardNotionRecord, "id"> = {
    roleId: params.roleid,
    cardId: params.card_id,
    text: params.text || "",
    likeUsers: "",
    hot: 0,
    hotState: '',
    status: Statues.Normal,
    createTime: Date.now(),
  };
  const newId = await CardNotionModel.insert(insertProps);

  const data: CardNotionRes.Add = { id: newId };
  return data;
}

/** 删除卡片的想法 */
export async function cardNotionDel(params: CardNotionReq.Del): Promise<CardNotionRes.Del> {
  const ret = await CardNotionModel.softDeleteByCondition({ id: params.notionId, roleId: params.roleid })
  const isOk = ret.affectedRows > 0
  const data: CardNotionRes.Del = { isOk };
  return data;
}

/** 给卡片想法点赞 */
export async function cardNotionLike(params: CardNotionReq.Like): Promise<CardNotionRes.Like> {
  const roleId = params.roleid;
  const notionId = params.notionId;
  const notion = await findNotion(notionId)
  const likeRecord = await CardNotionLikeModel.findOne({ roleId, notionId });
  let newId = 0;

  if (likeRecord) {
    if (likeRecord.status === Statues.Deleted) {
      await CardNotionLikeModel.updateById(likeRecord.id, { status: Statues.Normal });
      newId = likeRecord.id;
    } else {
      return BussError(CardErrors.CardNotionAlreadyLiked);
    }
  } else {
    const insertProps: Omit<CardNotionLikeRecord, "id"> = {
      roleId,
      notionId,
      status: Statues.Normal,
      createTime: Date.now(),
    };
    newId = await CardNotionLikeModel.insert(insertProps);
  }

  const data: CardNotionRes.Like = {
    isOk: newId > 0,
  };

  updateCardNotionHotState(notion, ENotionAction.Like, roleId)

  addNotificationByAction(ENotificationType.NotionBeLiked, {
    roleId,
    text: "",
    relateId: notionId,
    notionId,
    cardId: notion.cardId,
    targetId: notion.roleId,
    actionId: actionNotificationId(ENotificationType.NotionBeLiked, newId)
  })

  return data;
}

/** 给卡片想法取消点赞 */
export async function cardNotionCancelLike(params: CardNotionReq.CancelLike): Promise<CardNotionRes.CancelLike> {
  const roleId = params.roleid;
  const notionId = params.notionId;
  const notion = await findNotion(notionId)
  const likeRecord = await CardNotionLikeModel.findOne({ roleId, notionId, status: Statues.Normal });
  if (likeRecord) {
    await CardNotionLikeModel.updateById(likeRecord.id, { status: Statues.Deleted });
  } else {
    return BussError(CardErrors.CardNotionNotLiked);
  }
  const data: CardNotionRes.CancelLike = {
    isOk: true,
  };
  delNotificationByActionId(ENotificationType.NotionBeLiked, likeRecord.id)
  updateCardNotionHotState(notion, ENotionAction.CancelLike, roleId)
  return data;
}