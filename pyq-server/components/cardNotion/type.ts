import { operations } from "../../types/swagger";

export namespace CardNotionReq {
  export type Show = operations["cardNotionShow"]["parameters"]["query"];
  export type List = operations["cardNotionList"]["parameters"]["query"];
  export type Add = operations["cardNotionAdd"]["parameters"]["query"];
  export type Del = operations["cardNotionDel"]["parameters"]["query"];
  export type Like = operations["cardNotionLike"]["parameters"]["query"];
  export type CancelLike = operations["cardNotionCancelLike"]["parameters"]["query"];
}

export namespace CardNotionRes {
  export type Show = operations["cardNotionShow"]["responses"]["200"]["content"]["application/json"]["data"];
  export type List = operations["cardNotionList"]["responses"]["200"]["content"]["application/json"]["data"];
  export type Add = operations["cardNotionAdd"]["responses"]["200"]["content"]["application/json"]["data"];
  export type Del = operations["cardNotionDel"]["responses"]["200"]["content"]["application/json"]["data"];
  export type Like = operations["cardNotionLike"]["responses"]["200"]["content"]["application/json"]["data"];
  export type CancelLike =
    operations["cardNotionCancelLike"]["responses"]["200"]["content"]["application/json"]["data"];
}

export const ReqSchemas = {
  CardNotionShow: {
    roleid: { type: Number },
    notionId: { type: Number },
  },

  CardNotionList: {
    roleid: { type: Number },
    card_id: { type: "integer" },
    sort_by: { type: String, values: ["new", "hot"] },
    page: { type: Number, default: 1 },
    pageSize: { type: Number, default: 10 },
  },

  CardNotionAdd: {
    roleid: { type: Number },
    card_id: { type: "integer" },
    text: { type: String },
  },

  CardNotionDel: {
    roleid: { type: Number },
    notionId: { type: Number },
  },

  CardNotionLike: {
    roleid: { type: Number },
    notionId: { type: Number },
  },

  CardNotionCancelLike: {
    roleid: { type: Number },
    notionId: { type: Number },
  },
};