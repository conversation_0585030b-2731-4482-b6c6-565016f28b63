import { LIKE_USER_MAX_SIZE, Statues } from "../../../common/constants";
import { csvStrToIntArray, getJsonInfo } from "../../../common/util";
import { BussError, CardErrors } from "../../errorCodes";
import { CardNotionLikeModel, CardNotionModel, CardNotionRecord } from "../../models";
import * as _ from 'lodash'

interface HotState {
    like: number;
    comment: number;
}

export const INIT_HOT_STATE: HotState = { like: 0, comment: 0 };

export const enum ENotionAction {
    Like,
    CancelLike,
    Comment,
    DelComment,
}


export async function getLikedNotionIdSet(roleId: number, notionIds: number[]) {
    const rows = await CardNotionLikeModel.find(
        { roleId, status: Statues.Normal, notionId: notionIds },
        { cols: ["notionId"] }
    );
    const likeIds = rows.map((r) => r.notionId);
    return new Set(likeIds);
}


export async function isUserLikeNotion(roleId: number, notionId: number) {
    const likeSet = await getLikedNotionIdSet(roleId, [notionId])
    return likeSet.has(notionId)
}


export async function findNotion(id: number) {
    const r = await CardNotionModel.findOne({ id, status: Statues.Normal });
    if (!r) {
        return BussError(CardErrors.NotionNotFound);
    }
    return r;
}

export function formatHotState(r: Pick<CardNotionRecord, "hotState">): HotState {
    const hotStatObj = getJsonInfo(r.hotState, {}) as HotState;
    if (hotStatObj) {
        return { like: hotStatObj.like || 0, comment: hotStatObj.comment || 0 };
    } else {
        return { like: 0, comment: 0 };
    }
}

export function getNewHotState(hotState: HotState, action: ENotionAction): HotState {
    const newState: HotState = _.clone(hotState) || { like: 0, comment: 0 };
    if (action === ENotionAction.Like) {
        newState.like = hotState.like + 1;
    } else if (action === ENotionAction.CancelLike) {
        newState.like = Math.max(0, hotState.like - 1);
    } else if (action === ENotionAction.Comment) {
        newState.comment = hotState.comment + 1;
    } else if (action === ENotionAction.DelComment) {
        newState.comment = Math.max(0, hotState.comment - 1);
    } else {
        // do nothing
    }
    return newState;
}

export function getHotFromState(hotState: HotState) {
    return hotState.like + hotState.comment;
}

export async function updateCardNotionHotState(
    notion: Pick<CardNotionRecord, "id" | "hotState" | "likeUsers">,
    action: ENotionAction,
    actionRoleId: number
) {
    const hotState = formatHotState(notion);
    const newState = getNewHotState(hotState, action);
    const newHot = getHotFromState(newState);
    let updateProps: Partial<Pick<CardNotionRecord, "hot" | "hotState" | "likeUsers">> = { hotState: JSON.stringify(newState), hot: newHot }
    if (action === ENotionAction.Like || action === ENotionAction.CancelLike) {
        const likeUserIds = csvStrToIntArray(notion.likeUsers)
        const newLikeUserIds = action === ENotionAction.Like ? [...likeUserIds, actionRoleId].slice(0, LIKE_USER_MAX_SIZE) : likeUserIds.filter(id => id !== actionRoleId)
        const newLikeUserIdCsv = newLikeUserIds.join(',')
        updateProps.likeUsers = newLikeUserIdCsv
    }
    const ret = await CardNotionModel.updateById(notion.id, updateProps);
    return ret;
}