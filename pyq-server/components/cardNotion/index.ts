import { Path } from "../../types/type";
import {
  cardNotionShow,
  cardNotionList,
  cardNotionAdd,
  cardNotionDel,
  cardNotionLike,
  cardNotionCancelLike,
} from "./operation";
import { ReqSchemas } from "./type";

export const paths: Path[] = [
  {
    method: "post",
    url: "/card/notion/show",
    paramsSchema: ReqSchemas.CardNotionShow,
    operation: cardNotionShow,
  },
  {
    method: "post",
    url: "/card/notion/list",
    paramsSchema: ReqSchemas.CardNotionList,
    operation: cardNotionList,
  },
  {
    method: "post",
    url: "/card/notion/add",
    paramsSchema: ReqSchemas.CardNotionAdd,
    operation: cardNotionAdd,
  },
  {
    method: "post",
    url: "/card/notion/del",
    paramsSchema: ReqSchemas.CardNotionDel,
    operation: cardNotionDel,
  },
  {
    method: "post",
    url: "/card/notion/like",
    paramsSchema: ReqSchemas.CardNotionLike,
    operation: cardNotionLike,
  },
  {
    method: "post",
    url: "/card/notion/cancel_like",
    paramsSchema: ReqSchemas.CardNotionCancelLike,
    operation: cardNotionCancelLike,
  },
];

export const CardNotionComponent = {
  paths: paths,
  prefix: "/card/notion/",
};
