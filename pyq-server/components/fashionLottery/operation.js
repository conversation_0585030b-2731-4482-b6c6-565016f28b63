"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.fashionLongTermLotteryList = exports.fashionLotteryList = void 0;
const FashionLongTermLotteryOnce_1 = require("./models/FashionLongTermLotteryOnce");
const FashionLotteryOnce_1 = require("./models/FashionLotteryOnce");
const config_1 = require("../../common/config");
const moment = require("moment");
const cacheUtil_1 = require("../../common/cacheUtil");
const util2_1 = require("../../../common/util2");
function getMinKeepReceiveTime(now) {
    return moment(now).subtract(config_1.fashionLotteryCfg.keepMonth, "month").valueOf();
}
class FashioLotteryCacheClass extends cacheUtil_1.GenericCache {
    getKey(params) {
        return (0, util2_1.cacheKeyGen)("fashion_lottery_list", {
            roleid: params.roleid,
            page: params.page,
            itemLevel: params.itemLevel,
            pageSize: params.pageSize,
        });
    }
    getExpireTime() {
        return 60;
    }
    fetchDataSource(params) {
        return __awaiter(this, void 0, void 0, function* () {
            let query = FashionLotteryOnce_1.FashionLotteryOnceModel.normalScope().where("receiveTime", ">=", getMinKeepReceiveTime(Date.now()));
            if (params.itemLevel >= 0) {
                query = query.where("itemLevel", params.itemLevel);
            }
            const result = yield FashionLotteryOnce_1.FashionLotteryOnceModel.powerListQuery({
                initQuery: query,
                where: { roleId: params.roleid },
                select: ["id", "roleId", "itemLevel", "itemIndex", "receiveTime"],
                pagination: { page: params.page, pageSize: params.pageSize, roleId: params.roleid },
                orderBy: [["totalCount"], ["desc"]],
            });
            const data = {
                list: result.list,
                count: result.meta.totalCount,
            };
            return data;
        });
    }
}
const FashioLotteryCache = new FashioLotteryCacheClass();
class FashionLongTermLotteryCacheClass extends cacheUtil_1.GenericCache {
    getExpireTime() {
        return 60;
    }
    getKey(params) {
        return (0, util2_1.cacheKeyGen)("fashion_long_term_lottery_list", {
            roleid: params.roleid,
            page: params.page,
            itemLevel: params.itemLevel,
            pageSize: params.pageSize,
        });
    }
    fetchDataSource(params) {
        return __awaiter(this, void 0, void 0, function* () {
            let query = FashionLongTermLotteryOnce_1.FashionLongTermLotteryOnceModel.normalScope().where("receiveTime", ">=", getMinKeepReceiveTime(Date.now()));
            if (params.itemLevel >= 0) {
                query = query.where("itemLevel", params.itemLevel);
            }
            const result = yield FashionLongTermLotteryOnce_1.FashionLongTermLotteryOnceModel.powerListQuery({
                initQuery: query,
                where: { roleId: params.roleid },
                select: ["id", "roleId", "itemLevel", "itemIndex", "receiveTime"],
                pagination: { page: params.page, pageSize: params.pageSize, roleId: params.roleid },
                orderBy: [["totalCount"], ["desc"]],
            });
            const data = {
                list: result.list,
                count: result.meta.totalCount,
            };
            return data;
        });
    }
}
const FashionLongTermLotteryCache = new FashionLongTermLotteryCacheClass();
/** 时装抽奖获取记录列表 */
function fashionLotteryList(params) {
    return __awaiter(this, void 0, void 0, function* () {
        return FashioLotteryCache.get(params);
    });
}
exports.fashionLotteryList = fashionLotteryList;
/** 时装抽奖(长期)获取记录列表 */
function fashionLongTermLotteryList(params) {
    return __awaiter(this, void 0, void 0, function* () {
        return FashionLongTermLotteryCache.get(params);
    });
}
exports.fashionLongTermLotteryList = fashionLongTermLotteryList;
//# sourceMappingURL=operation.js.map