"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = void 0;
exports.ReqSchemas = {
    FashionLotteryList: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            itemLevel: { type: "integer", minimum: 0, maximum: 2 },
            page: { type: "integer", default: 1, minimum: 1 },
            pageSize: { type: "integer", maximum: 20, default: 10, minimum: 1 },
        },
        required: ["roleid", "page", "pageSize"],
    },
    FashionLotteryLongTermList: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            itemLevel: { type: "integer", minimum: 0, maximum: 2 },
            page: { type: "integer", default: 1, minimum: 1 },
            pageSize: { type: "integer", maximum: 20, default: 10, minimum: 1 },
        },
        required: ["roleid", "page", "pageSize"],
    },
};
//# sourceMappingURL=type.js.map