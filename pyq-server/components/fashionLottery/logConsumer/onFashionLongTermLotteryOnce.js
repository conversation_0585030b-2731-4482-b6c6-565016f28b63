"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.onFashionLongTermLotteryOnceLog = void 0;
const util_1 = require("../../../../common/util");
const logger_1 = require("../../../logger");
const FashionLongTermLotteryOnce_1 = require("../models/FashionLongTermLotteryOnce");
const helper_1 = require("./helper");
const logger = (0, logger_1.clazzLogger)("logConsumer/onFashionLotteryOnce");
function onFashionLongTermLotteryOnceLog(log) {
    return __awaiter(this, void 0, void 0, function* () {
        const payload = (0, util_1.getJsonInfo)(log.message);
        if (payload && payload.role_id) {
            try {
                const addProps = {
                    server: parseInt(payload.server, 10),
                    accountId: payload.account_id,
                    roleId: parseInt(payload.role_id, 10),
                    roleName: payload.role_name,
                    roleLevel: payload.role_level,
                    vip: payload.vip,
                    itemIndex: payload.itemIndex,
                    itemLevel: payload.itemLevel || 0,
                    totalCount: payload.totalCount,
                    lucky: payload.lucky,
                    isDiscount: payload.isDiscount,
                    isGuarantee: payload.isGuarantee,
                    isGuarantee10: payload.isGuarantee10,
                    createTime: Date.now(),
                    receiveTime: (0, helper_1.parseLogTime)(log.date),
                };
                const id = yield FashionLongTermLotteryOnce_1.FashionLongTermLotteryOnceModel.insert(addProps);
                logger.debug({ log, id }, "SaveOnLongTermLotteryOnceLogOk");
                return id;
            }
            catch (err) {
                logger.error({ log, err }, "SaveOnLongTermLotteryOnceLogFail");
            }
        }
        else {
            logger.error({ log }, "ParseOnLongTermLotteryOneLogError");
        }
    });
}
exports.onFashionLongTermLotteryOnceLog = onFashionLongTermLotteryOnceLog;
//# sourceMappingURL=onFashionLongTermLotteryOnce.js.map