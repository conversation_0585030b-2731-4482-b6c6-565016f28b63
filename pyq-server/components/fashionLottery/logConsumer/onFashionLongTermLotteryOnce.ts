import { getJsonInfo } from "../../../../common/util";
import { clazzLogger } from "../../../logger";
import { FashionLongTermLotteryOnceModel } from "../models/FashionLongTermLotteryOnce";
import { FashionLotteryOnceRecord } from "../models/FashionLotteryOnce";
import { RawLogItem } from "../../../consumers/gameLog";
import { parseLogTime } from "./helper";
const logger = clazzLogger("logConsumer/onFashionLotteryOnce");

/**
 * **LotteryOnce log data sample**
 * ```json
  {
   "server":12,
   "account_id":"gm",
   "role_id":"***********",
   "role_name":"千年昭阳",
   "role_level":150,
   "vip":18,
   "itemIndex":3,
   "totalCount":3,
   "lucky":3,
   "isDiscount":0,
   "isGuarantee":0,
   "isGuarantee10":0
}
 * ```
 */
interface FashionLotteryOnce {
  /** 服务器ID */
  server: string;
  /** 玩家账号 */
  account_id: string;
  /** 玩家角色ID */
  role_id: string;
  /** 玩家名称 */
  role_name: string;
  /** 玩家等级 */
  role_level: number;
  /** VIP等级 */
  vip: number;
  /** 奖品编号 */
  itemIndex: number;
  /** 奖品级别 精致，豪华，至尊 */
  itemLevel: number;
  /** 总抽奖数量 */
  totalCount: number;
  /** 幸运值 */
  lucky: number;
  /** 是否是特惠券 */
  isDiscount: number;
  /** 是否是大保底 */
  isGuarantee: number;
  /** 是否是十连保底 */
  isGuarantee10: number;
}

export async function onFashionLongTermLotteryOnceLog(log: RawLogItem) {
  const payload = getJsonInfo<FashionLotteryOnce>(log.message);
  if (payload && payload.role_id) {
    try {
      const addProps: Omit<FashionLotteryOnceRecord, "id"> = {
        server: parseInt(payload.server, 10),
        accountId: payload.account_id,
        roleId: parseInt(payload.role_id, 10),
        roleName: payload.role_name,
        roleLevel: payload.role_level,
        vip: payload.vip,
        itemIndex: payload.itemIndex,
        itemLevel: payload.itemLevel || 0,
        totalCount: payload.totalCount,
        lucky: payload.lucky,
        isDiscount: payload.isDiscount,
        isGuarantee: payload.isGuarantee,
        isGuarantee10: payload.isGuarantee10,
        createTime: Date.now(),
        receiveTime: parseLogTime(log.date),
      };
      const id = await FashionLongTermLotteryOnceModel.insert(addProps);
      logger.debug({ log, id }, "SaveOnLongTermLotteryOnceLogOk");
      return id;
    } catch (err) {
      logger.error({ log, err }, "SaveOnLongTermLotteryOnceLogFail");
    }
  } else {
    logger.error({ log }, "ParseOnLongTermLotteryOneLogError");
  }
}
