import { getServerRegion } from "../../../serverRegion";
import { clazzLogger } from "../../../logger";
import { timezoneCfg } from "../../../common/config";
const logger = clazzLogger("logConsumer/helper");

/**
 * 获取服务器对应的时区
 * @param region 服务器区域
 * @returns 时区字符串
 */
function getTimezone(region: string): string {
  const timezone = timezoneCfg[region];
  if (!timezone) {
    logger.error({ region }, "InvalidServerRegion");
    return "+08:00"; // 默认使用东八区
  }
  return timezone;
}

/**
 * 根据服务器区域解析日志时间
 * @param dateStr 日志时间字符串
 * @returns 解析后的时间戳，如果解析失败则返回当前时间
 */
export function parseLogTime(dateStr: string): number {
  try {
    const region = getServerRegion();
    const timezone = getTimezone(region);
    const timestamp = new Date(`${dateStr}${timezone}`).getTime();

    // 检查是否是有效的时间戳
    if (isNaN(timestamp) || timestamp <= 0) {
      logger.error({ dateStr, timezone, region }, "InvalidLogTime");
      return Date.now();
    }

    return timestamp;
  } catch (err) {
    logger.error({ dateStr, err }, "ParseLogTimeError");
    return Date.now();
  }
}