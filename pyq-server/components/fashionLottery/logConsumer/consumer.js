"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.transformKafkaMessage = exports.processKafkaMessage = exports.listenKafkaTopic = void 0;
const kafkajs_1 = require("kafkajs");
// eslint-disable-next-line @typescript-eslint/no-var-requires
const LZ4 = require("kafkajs-lz4");
const gameLog_1 = require("../../../consumers/gameLog");
const logger_1 = require("../../../common/logger");
const config_1 = require("../../../common/config");
const onFashionLotteryOnce_1 = require("./onFashionLotteryOnce");
const onFashionLongTermLotteryOnce_1 = require("./onFashionLongTermLotteryOnce");
const gameEvent_1 = require("../../../consumers/gameEvent");
kafkajs_1.CompressionCodecs[kafkajs_1.CompressionTypes.LZ4] = new LZ4().codec;
let kafka = null;
let consumer = null;
const bunyanLoggerCreator = (level) => {
    const logger = (0, logger_1.getLogger)("kafka_consumer", (0, gameLog_1.toBunyanLogLevel)(level));
    logger.info("initKafkaLog");
    return ({ namespace, level, label, log }) => {
        const { message } = log, extra = __rest(log, ["message"]);
        if (level === kafkajs_1.logLevel.ERROR) {
            logger.error({ label, extra, namespace }, message);
        }
        else {
            logger.info({ label, extra, namespace }, message);
        }
    };
};
function createKafkaClient() {
    const clientOption = {
        clientId: config_1.kafkaCfg.clientId,
        brokers: config_1.kafkaCfg.brokers,
        logLevel: kafkajs_1.logLevel[config_1.kafkaCfg.logLevel],
        logCreator: bunyanLoggerCreator,
    };
    if (config_1.kafkaCfg.sasl && config_1.kafkaCfg.sasl.username && config_1.kafkaCfg.sasl.password) {
        clientOption.sasl = config_1.kafkaCfg.sasl;
    }
    const kafka = new kafkajs_1.Kafka(clientOption);
    return kafka;
}
function getKafkaClient() {
    if (kafka === null) {
        kafka = createKafkaClient();
    }
    return kafka;
}
function getConsumer() {
    if (consumer === null) {
        consumer = createConsumer();
    }
    return consumer;
}
function createConsumer() {
    const consumer = getKafkaClient().consumer({ groupId: config_1.fashionLotteryCfg.logConsumer.groupId });
    return consumer;
}
function listenKafkaTopic() {
    return __awaiter(this, void 0, void 0, function* () {
        const consumer = getConsumer();
        try {
            yield consumer.connect();
            yield consumer.subscribe({
                topic: config_1.kafkaCfg.topic,
                fromBeginning: config_1.fashionLotteryCfg.logConsumer.fromBeginning,
            });
            yield consumer.run({
                partitionsConsumedConcurrently: config_1.kafkaCfg.partitionsConsumedConcurrently,
                eachMessage: ({ topic, partition, message }) => __awaiter(this, void 0, void 0, function* () {
                    return processKafkaMessage(consumer, topic, partition, message);
                }),
            });
        }
        catch (err) {
            consumer.logger().error("ListenKafkaTopicFail", { err });
        }
    });
}
exports.listenKafkaTopic = listenKafkaTopic;
function processKafkaMessage(consumer, topic, partition, message) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const logMessage = message.value.toString();
            const logItem = yield transformKafkaMessage(consumer, logMessage);
            yield persistLogMessage(consumer, logItem);
        }
        catch (err) {
            consumer.logger().error("ProcessKafkaMessageFail", { err, topic, partition, message });
        }
    });
}
exports.processKafkaMessage = processKafkaMessage;
function persistLogMessage(consumer, logItem) {
    return __awaiter(this, void 0, void 0, function* () {
        if (!logItem)
            return;
        try {
            switch (logItem.eventName) {
                case gameEvent_1.GameLogEvents.FashionLottery_LotteryOnce:
                    yield (0, onFashionLotteryOnce_1.onFashionLotteryOnceLog)(logItem);
                    break;
                case gameEvent_1.GameLogEvents.FashionLotteryLongTerm_LotteryOnce:
                    yield (0, onFashionLongTermLotteryOnce_1.onFashionLongTermLotteryOnceLog)(logItem);
                    break;
                default:
                    consumer.logger().debug("PersistLogMessageSkipCauseNotMatch", { logItem });
                    break;
            }
        }
        catch (err) {
            consumer.logger().error("PersistLogMessageFail", { err, logItem });
        }
    });
}
function transformKafkaMessage(consumer, message) {
    return __awaiter(this, void 0, void 0, function* () {
        const logItem = (0, gameLog_1.parseLogRawMessage)(message);
        if (logItem && logItem.eventName && logItem.message) {
            consumer.logger().debug("TransformKafkaMessageOK", { logItem });
            return logItem;
        }
        else {
            consumer.logger().warn("TransformKafkaMessageFail", { logItem, message });
            return null;
        }
    });
}
exports.transformKafkaMessage = transformKafkaMessage;
//# sourceMappingURL=consumer.js.map