import { Kafka, KafkaConfig, KafkaMessage, Consumer, logLevel, CompressionTypes, CompressionCodecs } from "kafkajs";
// eslint-disable-next-line @typescript-eslint/no-var-requires
const LZ4 = require("kafkajs-lz4");
import { RawLogItem, parseLogRawMessage, toBunyanLogLevel } from "../../../consumers/gameLog";
import { getLogger } from "../../../common/logger";
import { fashionLotteryCfg, kafkaCfg } from "../../../common/config";
import { onFashionLotteryOnceLog } from "./onFashionLotteryOnce";
import { onFashionLongTermLotteryOnceLog } from "./onFashionLongTermLotteryOnce";
import { GameLogEvents } from "../../../consumers/gameEvent";
CompressionCodecs[CompressionTypes.LZ4] = new LZ4().codec;

let kafka: Kafka = null;
let consumer: Consumer = null;

const bunyanLoggerCreator = (level: logLevel) => {
  const logger = getLogger("kafka_consumer", toBunyanLogLevel(level));
  logger.info("initKafkaLog");
  return ({ namespace, level, label, log }) => {
    const { message, ...extra } = log;
    if (level === logLevel.ERROR) {
      logger.error({ label, extra, namespace }, message);
    } else {
      logger.info({ label, extra, namespace }, message);
    }
  };
};

function createKafkaClient() {
  const clientOption: KafkaConfig = {
    clientId: kafkaCfg.clientId,
    brokers: kafkaCfg.brokers,
    logLevel: logLevel[kafkaCfg.logLevel],
    logCreator: bunyanLoggerCreator,
  };
  if (kafkaCfg.sasl && kafkaCfg.sasl.username && kafkaCfg.sasl.password) {
    clientOption.sasl = kafkaCfg.sasl;
  }

  const kafka = new Kafka(clientOption);
  return kafka;
}

function getKafkaClient() {
  if (kafka === null) {
    kafka = createKafkaClient();
  }
  return kafka;
}

function getConsumer() {
  if (consumer === null) {
    consumer = createConsumer();
  }
  return consumer;
}

function createConsumer() {
  const consumer = getKafkaClient().consumer({ groupId: fashionLotteryCfg.logConsumer.groupId });
  return consumer;
}

export async function listenKafkaTopic() {
  const consumer = getConsumer();
  try {
    await consumer.connect();
    await consumer.subscribe({
      topic: kafkaCfg.topic,
      fromBeginning: fashionLotteryCfg.logConsumer.fromBeginning,
    });

    await consumer.run({
      partitionsConsumedConcurrently: kafkaCfg.partitionsConsumedConcurrently,
      eachMessage: async ({ topic, partition, message }) => {
        return processKafkaMessage(consumer, topic, partition, message);
      },
    });
  } catch (err) {
    consumer.logger().error("ListenKafkaTopicFail", { err });
  }
}

export async function processKafkaMessage(consumer: Consumer, topic: string, partition: number, message: KafkaMessage) {
  try {
    const logMessage = message.value.toString();
    const logItem = await transformKafkaMessage(consumer, logMessage);
    await persistLogMessage(consumer, logItem);
  } catch (err) {
    consumer.logger().error("ProcessKafkaMessageFail", { err, topic, partition, message });
  }
}

async function persistLogMessage(consumer: Consumer, logItem: RawLogItem) {
  if (!logItem) return;
  try {
    switch (logItem.eventName) {
      case GameLogEvents.FashionLottery_LotteryOnce:
        await onFashionLotteryOnceLog(logItem);
        break;
      case GameLogEvents.FashionLotteryLongTerm_LotteryOnce:
        await onFashionLongTermLotteryOnceLog(logItem);
        break;
      default:
        consumer.logger().debug("PersistLogMessageSkipCauseNotMatch", { logItem });
        break;
    }
  } catch (err) {
    consumer.logger().error("PersistLogMessageFail", { err, logItem });
  }
}

export async function transformKafkaMessage(consumer: Consumer, message: string) {
  const logItem = parseLogRawMessage(message);
  if (logItem && logItem.eventName && logItem.message) {
    consumer.logger().debug("TransformKafkaMessageOK", { logItem });
    return logItem;
  } else {
    consumer.logger().warn("TransformKafkaMessageFail", { logItem, message });
    return null;
  }
}
