import { operations } from "../../types/swagger";

export namespace FashionLotteryReq {
  export type List = operations["fashionLotteryList"]["parameters"]["query"];
}

export namespace FashionLotteryRes {
  export type List = operations["fashionLotteryList"]["responses"]["200"]["content"]["application/json"]["data"];
}

export const ReqSchemas = {
  FashionLotteryList: {
    type: "object",
    properties: {
      roleid: { type: "number" },
      itemLevel: { type: "integer", minimum: 0, maximum: 2 },
      page: { type: "integer", default: 1, minimum: 1 },
      pageSize: { type: "integer", maximum: 20, default: 10, minimum: 1 },
    },
    required: ["roleid", "page", "pageSize"],
  },
  FashionLotteryLongTermList: {
    type: "object",
    properties: {
      roleid: { type: "number" },
      itemLevel: { type: "integer", minimum: 0, maximum: 2 },
      page: { type: "integer", default: 1, minimum: 1 },
      pageSize: { type: "integer", maximum: 20, default: 10, minimum: 1 },
    },
    required: ["roleid", "page", "pageSize"],
  },
};
