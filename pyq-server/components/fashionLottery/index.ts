import { Path } from "../../types/type";
import { fashionLongTermLotteryList, fashionLotteryList } from "./operation";
import { ReqSchemas } from "./type";

export const paths: Path[] = [
  {
    method: "get",
    url: "/fashion_lottery/list",
    paramsSchema: ReqSchemas.FashionLotteryList,
    operation: fashionLotteryList,
  },
  {
    method: "get",
    url: "/fashion_lottery/long_term_list",
    paramsSchema: ReqSchemas.FashionLotteryLongTermList,
    operation: fashionLongTermLotteryList,
  },
];

export const FashionLotteryComponent = {
  paths: paths,
  prefix: "/fashion_lottery/",
};
