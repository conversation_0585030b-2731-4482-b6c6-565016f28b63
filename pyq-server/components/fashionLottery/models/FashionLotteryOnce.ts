import { BaseModelClass } from "../../../models/baseModel";

export interface FashionLotteryOnceRecord {
  id: number;
  server: number;
  accountId: string;
  roleId: number;
  roleName: string;
  roleLevel: number;
  vip: number;
  itemIndex: number;
  itemLevel: number;
  totalCount: number;
  lucky: number;
  isDiscount: number;
  isGuarantee: number;
  isGuarantee10: number;
  createTime: number;
  receiveTime: number;
}

export type FashionLotteryOnceCol = keyof FashionLotteryOnceRecord;

export const FashionLotteryOnceCols: FashionLotteryOnceCol[] = [
  "id",
  "server",
  "accountId",
  "roleId",
  "roleName",
  "roleLevel",
  "vip",
  "itemIndex",
  "itemLevel",
  "totalCount",
  "lucky",
  "isDiscount",
  "isGuarantee",
  "isGuarantee10",
  "createTime",
  "receiveTime",
];

export class FashionLotteryOnceModelClass extends BaseModelClass<FashionLotteryOnceRecord> {
  constructor(tableName?: string) {
    super(tableName || "pyq_fashion_lottery_once");
  }
}

export const FashionLotteryOnceModel = new FashionLotteryOnceModelClass();
