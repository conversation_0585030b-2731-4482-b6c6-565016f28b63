"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FashionLotteryOnceModel = exports.FashionLotteryOnceModelClass = exports.FashionLotteryOnceCols = void 0;
const baseModel_1 = require("../../../models/baseModel");
exports.FashionLotteryOnceCols = [
    "id",
    "server",
    "accountId",
    "roleId",
    "roleName",
    "roleLevel",
    "vip",
    "itemIndex",
    "itemLevel",
    "totalCount",
    "lucky",
    "isDiscount",
    "isGuarantee",
    "isGuarantee10",
    "createTime",
    "receiveTime",
];
class FashionLotteryOnceModelClass extends baseModel_1.BaseModelClass {
    constructor(tableName) {
        super(tableName || "pyq_fashion_lottery_once");
    }
}
exports.FashionLotteryOnceModelClass = FashionLotteryOnceModelClass;
exports.FashionLotteryOnceModel = new FashionLotteryOnceModelClass();
//# sourceMappingURL=FashionLotteryOnce.js.map