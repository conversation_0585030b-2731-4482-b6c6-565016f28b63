import { FashionLongTermLotteryOnceModel } from "./models/FashionLongTermLotteryOnce";
import { FashionLotteryOnceModel } from "./models/FashionLotteryOnce";
import { FashionLotteryReq, FashionLotteryRes } from "./type";
import { fashionLotteryCfg } from "../../common/config";
import * as moment from "moment";
import { GenericCache } from "../../common/cacheUtil";
import { cacheKeyGen } from "../../../common/util2";

function getMinKeepReceiveTime(now: number) {
  return moment(now).subtract(fashionLotteryCfg.keepMonth, "month").valueOf();
}

class FashioLotteryCacheClass extends GenericCache<FashionLotteryReq.List, FashionLotteryRes.List> {
  getKey(params: FashionLotteryReq.List): string {
    return cacheKeyGen("fashion_lottery_list", {
      roleid: params.roleid,
      page: params.page,
      itemLevel: params.itemLevel,
      pageSize: params.pageSize,
    });
  }

  getExpireTime() {
    return 60;
  }

  async fetchDataSource(params: FashionLotteryReq.List): Promise<FashionLotteryRes.List> {
    let query = FashionLotteryOnceModel.normalScope().where("receiveTime", ">=", getMinKeepReceiveTime(Date.now()));
    if (params.itemLevel >= 0) {
      query = query.where("itemLevel", params.itemLevel);
    }
    const result = await FashionLotteryOnceModel.powerListQuery({
      initQuery: query,
      where: { roleId: params.roleid },
      select: ["id", "roleId", "itemLevel", "itemIndex", "receiveTime"],
      pagination: { page: params.page, pageSize: params.pageSize, roleId: params.roleid },
      orderBy: [["totalCount"], ["desc"]],
    });
    const data: FashionLotteryRes.List = {
      list: result.list,
      count: result.meta.totalCount,
    };
    return data;
  }
}
const FashioLotteryCache = new FashioLotteryCacheClass();

class FashionLongTermLotteryCacheClass extends GenericCache<FashionLotteryReq.List, FashionLotteryRes.List> {
  getExpireTime() {
    return 60;
  }

  getKey(params: FashionLotteryReq.List): string {
    return cacheKeyGen("fashion_long_term_lottery_list", {
      roleid: params.roleid,
      page: params.page,
      itemLevel: params.itemLevel,
      pageSize: params.pageSize,
    });
  }
  async fetchDataSource(params: FashionLotteryReq.List): Promise<FashionLotteryRes.List> {
    let query = FashionLongTermLotteryOnceModel.normalScope().where(
      "receiveTime",
      ">=",
      getMinKeepReceiveTime(Date.now())
    );
    if (params.itemLevel >= 0) {
      query = query.where("itemLevel", params.itemLevel);
    }
    const result = await FashionLongTermLotteryOnceModel.powerListQuery({
      initQuery: query,
      where: { roleId: params.roleid },
      select: ["id", "roleId", "itemLevel", "itemIndex", "receiveTime"],
      pagination: { page: params.page, pageSize: params.pageSize, roleId: params.roleid },
      orderBy: [["totalCount"], ["desc"]],
    });
    const data: FashionLotteryRes.List = {
      list: result.list,
      count: result.meta.totalCount,
    };
    return data;
  }
}

const FashionLongTermLotteryCache = new FashionLongTermLotteryCacheClass();

/** 时装抽奖获取记录列表 */
export async function fashionLotteryList(params: FashionLotteryReq.List): Promise<FashionLotteryRes.List> {
  return FashioLotteryCache.get(params);
}

/** 时装抽奖(长期)获取记录列表 */
export async function fashionLongTermLotteryList(params: FashionLotteryReq.List): Promise<FashionLotteryRes.List> {
  return FashionLongTermLotteryCache.get(params);
}
