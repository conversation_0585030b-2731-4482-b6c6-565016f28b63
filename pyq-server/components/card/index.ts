import { Path } from "../../types/type";
import { cardIsEnable, cardList, cardRedDot, cardLike, cardCancelLike } from "./operation";
import { ReqSchemas } from "./type";

export const paths: Path[] = [
   {
    method: "post",
    url: "/card/is_enable",
    paramsSchema: ReqSchemas.CardIsEnable,
    operation: cardIsEnable,
  },
  {
    method: "post",
    url: "/card/list",
    paramsSchema: ReqSchemas.CardList,
    operation: cardList,
  },
  {
    method: "post",
    url: "/card/red_dot",
    paramsSchema: ReqSchemas.CardRedDot,
    operation: cardRedDot,
  },
  {
    method: "post",
    url: "/card/like",
    paramsSchema: ReqSchemas.CardLike,
    operation: cardLike,
  },
  {
    method: "post",
    url: "/card/cancel_like",
    paramsSchema: ReqSchemas.CardCancelLike,
    operation: cardCancelLike,
  },
];

export const CardComponent = {
  paths: paths,
  prefix: "/card/",
};
