"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserCardInfoView = exports.updateCardLikeCount = exports.isUserLikeCard = exports.getLikedCardIdSet = void 0;
const constants_1 = require("../../../common/constants");
const models_1 = require("../../models");
function getLikedCardIdSet(roleId, cardIds) {
    return __awaiter(this, void 0, void 0, function* () {
        const rows = yield models_1.CardLikeModel.find({ roleId, status: constants_1.Statues.Normal, cardId: cardIds }, { cols: ["cardId"] });
        const likeIds = rows.map((r) => r.cardId);
        return new Set(likeIds);
    });
}
exports.getLikedCardIdSet = getLikedCardIdSet;
function isUserLikeCard(roleId, cardId) {
    return __awaiter(this, void 0, void 0, function* () {
        const likeSet = yield getLikedCardIdSet(roleId, [cardId]);
        return likeSet.has(cardId);
    });
}
exports.isUserLikeCard = isUserLikeCard;
function updateCardLikeCount(cardId, action) {
    return __awaiter(this, void 0, void 0, function* () {
        const curCard = yield models_1.CardModel.findOne({ cardId, status: constants_1.Statues.Normal }, ['id', 'likeCount']);
        if (curCard) {
            const likeCount = action === 0 /* Like */ ? curCard.likeCount + 1 : Math.max(curCard.likeCount - 1, 0);
            yield models_1.CardModel.updateById(curCard.id, { likeCount });
        }
        else {
            const likeCount = action === 0 /* Like */ ? 1 : 0;
            const props = {
                cardId,
                createTime: Date.now(),
                likeCount,
                status: constants_1.Statues.Normal
            };
            yield models_1.CardModel.insert(props);
        }
    });
}
exports.updateCardLikeCount = updateCardLikeCount;
function getUserCardInfoView(roleId, cardId) {
    return __awaiter(this, void 0, void 0, function* () {
        const r = yield models_1.CardModel.findOne({ cardId, status: constants_1.Statues.Normal });
        if (r && r.id) {
            const isLiked = yield isUserLikeCard(roleId, cardId);
            return { cardId, likeCount: r.likeCount, isLiked };
        }
        else {
            return { cardId, likeCount: 0, isLiked: false };
        }
    });
}
exports.getUserCardInfoView = getUserCardInfoView;
//# sourceMappingURL=service.js.map