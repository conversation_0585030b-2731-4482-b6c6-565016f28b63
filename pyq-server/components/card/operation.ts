import { Statues } from "../../../common/constants";
import { csvStrToIntArray } from "../../../common/util";
import { cardNotionCfg } from "../../common/config";
import { BussError, CardErrors } from "../../errorCodes";
import { CardLikeModel, CardLikeRecord, CardModel, CardUserModel } from "../../models";
import { getPlayerLevel } from "../../services/player";
import { EHasNewStatus } from "../../types/type";
import { ECardAction, getLikedCardIdSet, updateCardLikeCount } from "./service";
import { CardReq, CardRes } from "./type";

/** 剧情卡功能是否对玩家开放 */
export async function cardIsEnable(params: CardReq.IsEnable): Promise<CardRes.IsEnable> {
  const { roleid } = params;
  const playerLevel = await getPlayerLevel(roleid);
  const isEnable = playerLevel >= cardNotionCfg.openMinLevel;
  const data: CardRes.IsEnable = { isEnable };
  return data;
}

/** 卡片列表统计详情 */
export async function cardList(params: CardReq.List): Promise<CardRes.List> {
  const cardIds = csvStrToIntArray(params.card_ids);
  if (cardIds && cardIds.length > 0) {
    const cards = await CardModel.find({ cardId: cardIds, status: Statues.Normal }, { cols: ["cardId", "likeCount"] });
    const likeCardIdSet = await getLikedCardIdSet(params.roleid, cardIds);

    const data: CardRes.List = cards.map((c) => {
      const isLiked = likeCardIdSet.has(c.cardId);
      return {
        cardId: c.cardId,
        likeCount: c.likeCount,
        isLiked,
      };
    });
    return data;
  } else {
    return [];
  }
}

/** 卡片红点接口(返回所有有新消息的卡片id) */
export async function cardRedDot(params: CardReq.RedDot): Promise<CardRes.RedDot> {
  const roleId = params.roleid;
  const rows = await CardUserModel.powerQuery({
    where: { hasNew: EHasNewStatus.HasNew, roleId },
    select: ["cardId"],
    pagination: { page: params.page, pageSize: params.pageSize },
  });
  const data: CardRes.RedDot = rows.map((r) => r.cardId);
  return data;
}

/** 点赞卡片 */
export async function cardLike(params: CardReq.Like): Promise<CardRes.Like> {
  const roleId = params.roleid;
  const cardId = params.card_id;
  const likeRecord = await CardLikeModel.findOne({ roleId, cardId });
  let newId = 0;

  if (likeRecord) {
    if (likeRecord.status === Statues.Deleted) {
      await CardLikeModel.updateById(likeRecord.id, { status: Statues.Normal });
      newId = likeRecord.id;
    } else {
      return BussError(CardErrors.CardAlreadyLiked);
    }
  } else {
    const insertProps: Omit<CardLikeRecord, "id"> = {
      roleId,
      cardId,
      status: Statues.Normal,
      createTime: Date.now(),
    };
    newId = await CardLikeModel.insert(insertProps);
  }
  const data: CardRes.Like = { isOk: newId > 0 };
  await updateCardLikeCount(cardId, ECardAction.Like);
  return data;
}

/** 取消点赞卡片 */
export async function cardCancelLike(params: CardReq.CancelLike): Promise<CardRes.CancelLike> {
  const roleId = params.roleid;
  const cardId = params.card_id;
  const likeRecord = await CardLikeModel.findOne({ roleId, cardId, status: Statues.Normal });
  if (likeRecord) {
    await CardLikeModel.updateById(likeRecord.id, { status: Statues.Deleted });
  } else {
    return BussError(CardErrors.CardNotLiked);
  }
  const data: CardRes.CancelLike = {
    isOk: true,
  };
  await updateCardLikeCount(cardId, ECardAction.CancelLike);
  return data;
}
