import { operations } from "../../types/swagger";

export namespace CardReq {
   export type IsEnable = operations["cardIsEnable"]["parameters"]["query"];
  export type List = operations["cardList"]["parameters"]["query"];
  export type RedDot = operations["cardRedDot"]["parameters"]["query"];
  export type Like = operations["cardLike"]["parameters"]["query"];
  export type CancelLike = operations["cardCancelLike"]["parameters"]["query"];
}

export namespace CardRes {
  export type IsEnable = operations["cardIsEnable"]["responses"]["200"]["content"]["application/json"]["data"];
  export type List = operations["cardList"]["responses"]["200"]["content"]["application/json"]["data"];
  export type RedDot = operations["cardRedDot"]["responses"]["200"]["content"]["application/json"]["data"];
  export type Like = operations["cardLike"]["responses"]["200"]["content"]["application/json"]["data"];
  export type CancelLike = operations["cardCancelLike"]["responses"]["200"]["content"]["application/json"]["data"];
}

export const ReqSchemas = {
  CardIsEnable: {
    roleid: { type: Number },
  },

  CardList: {
    roleid: { type: Number },
    card_ids: { type: String },
  },

  CardRedDot: {
    roleid: { type: Number },
    page: { type: Number, default: 1 },
    pageSize: { type: Number, default: 10 },
  },

  CardLike: {
    roleid: { type: Number },
    card_id: { type: "integer" },
  },

  CardCancelLike: {
    roleid: { type: Number },
    card_id: { type: "integer" },
  },
};
