"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.cardCancelLike = exports.cardLike = exports.cardRedDot = exports.cardList = exports.cardIsEnable = void 0;
const constants_1 = require("../../../common/constants");
const util_1 = require("../../../common/util");
const config_1 = require("../../common/config");
const errorCodes_1 = require("../../errorCodes");
const models_1 = require("../../models");
const player_1 = require("../../services/player");
const service_1 = require("./service");
/** 剧情卡功能是否对玩家开放 */
function cardIsEnable(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const { roleid } = params;
        const playerLevel = yield (0, player_1.getPlayerLevel)(roleid);
        const isEnable = playerLevel >= config_1.cardNotionCfg.openMinLevel;
        const data = { isEnable };
        return data;
    });
}
exports.cardIsEnable = cardIsEnable;
/** 卡片列表统计详情 */
function cardList(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const cardIds = (0, util_1.csvStrToIntArray)(params.card_ids);
        if (cardIds && cardIds.length > 0) {
            const cards = yield models_1.CardModel.find({ cardId: cardIds, status: constants_1.Statues.Normal }, { cols: ["cardId", "likeCount"] });
            const likeCardIdSet = yield (0, service_1.getLikedCardIdSet)(params.roleid, cardIds);
            const data = cards.map((c) => {
                const isLiked = likeCardIdSet.has(c.cardId);
                return {
                    cardId: c.cardId,
                    likeCount: c.likeCount,
                    isLiked,
                };
            });
            return data;
        }
        else {
            return [];
        }
    });
}
exports.cardList = cardList;
/** 卡片红点接口(返回所有有新消息的卡片id) */
function cardRedDot(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const roleId = params.roleid;
        const rows = yield models_1.CardUserModel.powerQuery({
            where: { hasNew: 1 /* HasNew */, roleId },
            select: ["cardId"],
            pagination: { page: params.page, pageSize: params.pageSize },
        });
        const data = rows.map((r) => r.cardId);
        return data;
    });
}
exports.cardRedDot = cardRedDot;
/** 点赞卡片 */
function cardLike(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const roleId = params.roleid;
        const cardId = params.card_id;
        const likeRecord = yield models_1.CardLikeModel.findOne({ roleId, cardId });
        let newId = 0;
        if (likeRecord) {
            if (likeRecord.status === constants_1.Statues.Deleted) {
                yield models_1.CardLikeModel.updateById(likeRecord.id, { status: constants_1.Statues.Normal });
                newId = likeRecord.id;
            }
            else {
                return (0, errorCodes_1.BussError)(errorCodes_1.CardErrors.CardAlreadyLiked);
            }
        }
        else {
            const insertProps = {
                roleId,
                cardId,
                status: constants_1.Statues.Normal,
                createTime: Date.now(),
            };
            newId = yield models_1.CardLikeModel.insert(insertProps);
        }
        const data = { isOk: newId > 0 };
        yield (0, service_1.updateCardLikeCount)(cardId, 0 /* Like */);
        return data;
    });
}
exports.cardLike = cardLike;
/** 取消点赞卡片 */
function cardCancelLike(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const roleId = params.roleid;
        const cardId = params.card_id;
        const likeRecord = yield models_1.CardLikeModel.findOne({ roleId, cardId, status: constants_1.Statues.Normal });
        if (likeRecord) {
            yield models_1.CardLikeModel.updateById(likeRecord.id, { status: constants_1.Statues.Deleted });
        }
        else {
            return (0, errorCodes_1.BussError)(errorCodes_1.CardErrors.CardNotLiked);
        }
        const data = {
            isOk: true,
        };
        yield (0, service_1.updateCardLikeCount)(cardId, 1 /* CancelLike */);
        return data;
    });
}
exports.cardCancelLike = cardCancelLike;
//# sourceMappingURL=operation.js.map