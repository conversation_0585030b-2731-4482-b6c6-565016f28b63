import { Statues } from "../../../common/constants";
import { CardLikeModel, CardModel, CardRecord } from "../../models";
import { CardInfo } from "../../types/type";

export const enum ECardAction {
    Like,
    CancelLike,
}


export async function getLikedCardIdSet(roleId: number, cardIds: number[]) {
    const rows = await CardLikeModel.find(
        { roleId, status: Statues.Normal, cardId: cardIds },
        { cols: ["cardId"] }
    );
    const likeIds = rows.map((r) => r.cardId);
    return new Set(likeIds);
}


export async function isUserLikeCard(roleId: number, cardId: number) {
    const likeSet = await getLikedCardIdSet(roleId, [cardId])
    return likeSet.has(cardId)
}


export async function updateCardLikeCount(cardId: number, action: ECardAction) {
    const curCard = await CardModel.findOne({ cardId, status: Statues.Normal }, ['id', 'likeCount'])
    if (curCard) {
        const likeCount = action === ECardAction.Like ? curCard.likeCount + 1 : Math.max(curCard.likeCount - 1, 0)
        await CardModel.updateById(curCard.id, { likeCount })
    } else {
        const likeCount = action === ECardAction.Like ? 1 : 0
        const props: Omit<CardRecord, 'id'> = {
            cardId,
            createTime: Date.now(),
            likeCount,
            status: Statues.Normal
        }
        await CardModel.insert(props)
    }
}

export async function getUserCardInfoView(roleId: number, cardId: number): Promise<CardInfo> {
    const r = await CardModel.findOne({ cardId, status: Statues.Normal })
    if (r && r.id) {
        const isLiked = await isUserLikeCard(roleId, cardId)
        return { cardId, likeCount: r.likeCount, isLiked }
    } else {
        return { cardId, likeCount: 0, isLiked: false }
    }
}