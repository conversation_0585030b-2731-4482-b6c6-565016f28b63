"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = void 0;
exports.ReqSchemas = {
    CardIsEnable: {
        roleid: { type: Number },
    },
    CardList: {
        roleid: { type: Number },
        card_ids: { type: String },
    },
    CardRedDot: {
        roleid: { type: Number },
        page: { type: Number, default: 1 },
        pageSize: { type: Number, default: 10 },
    },
    CardLike: {
        roleid: { type: Number },
        card_id: { type: "integer" },
    },
    CardCancelLike: {
        roleid: { type: Number },
        card_id: { type: "integer" },
    },
};
//# sourceMappingURL=type.js.map