"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CardComponent = exports.paths = void 0;
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "post",
        url: "/card/is_enable",
        paramsSchema: type_1.ReqSchemas.CardIsEnable,
        operation: operation_1.cardIsEnable,
    },
    {
        method: "post",
        url: "/card/list",
        paramsSchema: type_1.ReqSchemas.CardList,
        operation: operation_1.cardList,
    },
    {
        method: "post",
        url: "/card/red_dot",
        paramsSchema: type_1.ReqSchemas.CardRedDot,
        operation: operation_1.cardRedDot,
    },
    {
        method: "post",
        url: "/card/like",
        paramsSchema: type_1.ReqSchemas.CardLike,
        operation: operation_1.cardLike,
    },
    {
        method: "post",
        url: "/card/cancel_like",
        paramsSchema: type_1.ReqSchemas.CardCancelLike,
        operation: operation_1.cardCancelLike,
    },
];
exports.CardComponent = {
    paths: exports.paths,
    prefix: "/card/",
};
//# sourceMappingURL=index.js.map