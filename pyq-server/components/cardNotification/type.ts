import { operations } from "../../types/swagger";

export namespace CardNotificationReq {
  export type List = operations["cardNotificationList"]["parameters"]["query"];
  export type ReadAll = operations["cardNotificationReadAll"]["parameters"]["query"];
  export type DelAll = operations["cardNotificationDelAll"]["parameters"]["query"];

  export type NewNum = operations["cardNotificationNewNum"]["parameters"]["query"];
  export type Read = operations["cardNotificationRead"]["parameters"]["query"];
}

export namespace CardNotificationRes {
  export type List = operations["cardNotificationList"]["responses"]["200"]["content"]["application/json"]["data"];
  export type ReadAll =
    operations["cardNotificationReadAll"]["responses"]["200"]["content"]["application/json"]["data"];

  export type DelAll =
    operations["cardNotificationDelAll"]["responses"]["200"]["content"]["application/json"]["data"];

  export type NewNum =
    operations["cardNotificationNewNum"]["responses"]["200"]["content"]["application/json"]["data"];


  export type Read = operations["cardNotificationRead"]["responses"]["200"]["content"]["application/json"]["data"];
}

export const ReqSchemas = {
  CardNotificationList: {
    roleid: { type: Number },
    card_id: { type: "integer" },
    type: { type: Number, required: false, values: [0, 1] },
    page: { type: Number, default: 1 },
    pageSize: { type: Number, default: 10 },
  },

  CardNotificationReadAll: {
    card_id: { type: "integer" },
    roleid: { type: Number },
  },

  CardNotificationDelAll: {
    card_id: { type: "integer" },
    roleid: { type: Number },
  },

  CardNotificationNewNum: {
    card_id: { type: "integer" },
    roleid: { type: Number },
  },

  CardNotificationRead: {
    roleid: { type: Number },
    id: { type: Number },
  },
};