"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.delNotificationByActionId = exports.actionNotificationId = exports.getNewNotificationNum = exports.viewNotificationTimeGet = exports.viewNotificationTimeSet = exports.buildNotificationByAction = exports.addNotificationByAction = exports.unsetHasNewRedDot = exports.checkAndClearHasNewRedDot = exports.clearHasNewRedDot = exports.getRelateEntityFetcher = exports.getSplitRelatedIds = exports.isTypeForComment = exports.isTypeForNotion = exports.findUnReadNotification = void 0;
const config_1 = require("../../../common/config");
const constants_1 = require("../../../common/constants");
const redisNew_1 = require("../../../common/redisNew");
const redisTypes_1 = require("../../../common/redisTypes");
const util2_1 = require("../../../common/util2");
const errorCodes_1 = require("../../errorCodes");
const models_1 = require("../../models");
function findUnReadNotification(id) {
    return __awaiter(this, void 0, void 0, function* () {
        const r = yield models_1.CardNotificationModel.findOne({ id, status: 0 /* UnRead */ });
        if (!r) {
            return (0, errorCodes_1.BussError)(errorCodes_1.CardErrors.UnReadNotificationNotFound);
        }
        return r;
    });
}
exports.findUnReadNotification = findUnReadNotification;
function isTypeForNotion(type) {
    return type === 1 /* NotionBeLiked */ || type === 2 /* NotionBeComment */;
}
exports.isTypeForNotion = isTypeForNotion;
function isTypeForComment(type) {
    return type === 3 /* CommentBeReplied */;
}
exports.isTypeForComment = isTypeForComment;
function getSplitRelatedIds(list) {
    const commentIdsSet = new Set();
    for (let r of list) {
        if (isTypeForComment(r.type)) {
            commentIdsSet.add(r.relateId);
        }
    }
    const notionIds = list.map(r => r.notionId);
    const commentIds = Array.from(commentIdsSet);
    return { notionIds, commentIds };
}
exports.getSplitRelatedIds = getSplitRelatedIds;
function getRelateEntityFetcher(list) {
    return __awaiter(this, void 0, void 0, function* () {
        const { notionIds, commentIds } = getSplitRelatedIds(list);
        const notionList = yield models_1.CardNotionModel.find({ id: notionIds, status: constants_1.Statues.Normal }, { cols: ['id', 'text'] });
        const notionMap = (0, util2_1.keyToRecordMap)(notionList, 'id');
        const commentList = yield models_1.CardNotionCommentModel.find({ id: commentIds }, { cols: ['id', 'text'] });
        const commentMap = (0, util2_1.keyToRecordMap)(commentList, 'id');
        const notionFetcher = (notionId) => {
            const notion = notionMap.get(notionId) || { text: "" };
            return { id: notionId, text: notion.text };
        };
        const commentFetcher = (commentId) => {
            const notion = commentMap.get(commentId) || { text: "" };
            return { id: commentId, text: notion.text };
        };
        return { notionFetcher, commentFetcher };
    });
}
exports.getRelateEntityFetcher = getRelateEntityFetcher;
function updateRedNotNewStatus(roleId, cardId, hasNew) {
    return __awaiter(this, void 0, void 0, function* () {
        const props = {
            roleId,
            cardId,
            hasNew
        };
        const upProps = { hasNew };
        const oldRecord = yield models_1.CardUserModel.findOne({
            roleId,
            cardId,
        });
        if (oldRecord) {
            yield models_1.CardUserModel.updateById(oldRecord.id, upProps);
        }
        else {
            yield models_1.CardUserModel.insert(props);
        }
    });
}
function markHasNewRedDot(roleId, cardId) {
    return __awaiter(this, void 0, void 0, function* () {
        return updateRedNotNewStatus(roleId, cardId, 1 /* HasNew */);
    });
}
function clearHasNewRedDot(roleId, cardId) {
    return __awaiter(this, void 0, void 0, function* () {
        return updateRedNotNewStatus(roleId, cardId, 0 /* NoNew */);
    });
}
exports.clearHasNewRedDot = clearHasNewRedDot;
function checkAndClearHasNewRedDot(targetId, cardId) {
    return __awaiter(this, void 0, void 0, function* () {
        const query = models_1.CardNotificationModel.scope()
            .where('targetId', targetId)
            .where('cardId', cardId)
            .where('status', 0 /* UnRead */);
        const count = yield models_1.CardNotificationModel.countByQuery(query);
        if (!count) {
            return clearHasNewRedDot(targetId, cardId);
        }
    });
}
exports.checkAndClearHasNewRedDot = checkAndClearHasNewRedDot;
function unsetHasNewRedDot(roleId, cardId) {
    return __awaiter(this, void 0, void 0, function* () {
        const oldRecord = yield models_1.CardUserModel.findOne({
            roleId,
            cardId,
        });
        if (!oldRecord || !oldRecord.hasNew) {
            return;
        }
        yield models_1.CardUserModel.updateById(oldRecord.id, { hasNew: 0 /* NoNew */ });
    });
}
exports.unsetHasNewRedDot = unsetHasNewRedDot;
function addNotificationByAction(type, props) {
    return __awaiter(this, void 0, void 0, function* () {
        if (props.roleId !== props.targetId) {
            const insertProps = buildNotificationByAction(type, props);
            yield models_1.CardNotificationModel.insert(insertProps);
            yield markHasNewRedDot(props.targetId, props.cardId);
        }
    });
}
exports.addNotificationByAction = addNotificationByAction;
function buildNotificationByAction(type, props) {
    const insertProps = {
        roleId: props.roleId,
        type,
        cardId: props.cardId,
        notionId: props.notionId,
        targetId: props.targetId,
        text: props.text || "",
        relateId: props.relateId || 0,
        actionId: props.actionId,
        status: 0 /* UnRead */,
        createTime: Date.now(),
    };
    return insertProps;
}
exports.buildNotificationByAction = buildNotificationByAction;
const ViewNotificationTimeKey = "card_notification_view_time";
function viewNotificationTimeKey(roleId, cardId) {
    return (0, util2_1.cacheKeyGen)(ViewNotificationTimeKey, { roleId, cardId });
}
function viewNotificationTimeSet(roleId, cardId, ts) {
    return __awaiter(this, void 0, void 0, function* () {
        const key = viewNotificationTimeKey(roleId, cardId);
        const ret = yield (0, redisNew_1.getRedis)().setAsync(key, ts, redisTypes_1.ExpireType.EX, 90 * config_1.ONE_DAY_SECONDS);
        return ret;
    });
}
exports.viewNotificationTimeSet = viewNotificationTimeSet;
function viewNotificationTimeGet(roleId, cardId) {
    return __awaiter(this, void 0, void 0, function* () {
        const key = viewNotificationTimeKey(roleId, cardId);
        const ret = yield (0, redisNew_1.getRedis)().getAsync(key);
        const timeTs = parseInt(ret, 10);
        if (timeTs > 0) {
            return timeTs;
        }
        else {
            return 0;
        }
    });
}
exports.viewNotificationTimeGet = viewNotificationTimeGet;
function getNewNotificationNum(roleId, cardId) {
    return __awaiter(this, void 0, void 0, function* () {
        const viewTime = yield viewNotificationTimeGet(roleId, cardId);
        const query = models_1.CardNotificationModel.normalScope()
            .where('targetId', roleId)
            .where('cardId', cardId)
            .where('createTime', '>', viewTime);
        const cnt = yield models_1.CardNotificationModel.countByQuery(query);
        return cnt;
    });
}
exports.getNewNotificationNum = getNewNotificationNum;
const actionNameMap = {
    [1 /* NotionBeLiked */]: "like",
    [3 /* CommentBeReplied */]: "comment",
    [2 /* NotionBeComment */]: "comment",
};
function actionNotificationId(action, id) {
    const typeStr = actionNameMap[action];
    return [typeStr, id].join("-");
}
exports.actionNotificationId = actionNotificationId;
function delNotificationByActionId(action, id) {
    return __awaiter(this, void 0, void 0, function* () {
        const actionId = actionNotificationId(action, id);
        const notifications = yield models_1.CardNotificationModel.find({ actionId, status: [0 /* UnRead */, 1 /* Read */] });
        if (!notifications.length) {
            return;
        }
        const ret = yield models_1.CardNotificationModel.softDeleteByCondition({ actionId });
        // 清除掉用户的小红点
        const cardId = notifications[0].cardId;
        if (cardId) {
            const targetIds = notifications.map(item => item.targetId);
            yield Promise.all(targetIds.map(targetId => checkAndClearHasNewRedDot(targetId, cardId)));
        }
        return ret;
    });
}
exports.delNotificationByActionId = delNotificationByActionId;
//# sourceMappingURL=service.js.map