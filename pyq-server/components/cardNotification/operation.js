"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.cardNotificationRead = exports.cardNotificationNewNum = exports.cardNotificationDelAll = exports.cardNotificationReadAll = exports.cardNotificationList = void 0;
const models_1 = require("../../models");
const service_1 = require("../card/service");
const _ = require("lodash");
const roleInfo_1 = require("../../services/roleInfo");
const service_2 = require("./service");
/** 卡片通知列表 */
function cardNotificationList(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const roleId = params.roleid;
        const cardId = params.card_id;
        const card = yield (0, service_1.getUserCardInfoView)(roleId, cardId);
        const query = models_1.CardNotificationModel.normalScope()
            .where('cardId', cardId)
            .where('targetId', roleId);
        const result = yield models_1.CardNotificationModel.powerListQuery({
            initQuery: query,
            select: ['id', 'roleId', 'createTime', 'text', 'type', 'relateId', "notionId"],
            pagination: { page: params.page, pageSize: params.pageSize },
            orderBy: [['createTime'], ['desc']]
        });
        const rawList = result.list;
        const count = result.meta.totalCount;
        const queryRoleIds = _.map(rawList, r => r.roleId);
        const roleInfoFetcher = yield (0, roleInfo_1.getRoleInfoSafeFetcher)(queryRoleIds);
        const { notionFetcher, commentFetcher } = yield (0, service_2.getRelateEntityFetcher)(rawList);
        const list = rawList.map(r => {
            const roleInfo = roleInfoFetcher(r.roleId);
            const notion = notionFetcher(r.notionId);
            const notionComment = (0, service_2.isTypeForComment)(r.type) ? commentFetcher(r.relateId) : null;
            return {
                notifier: {
                    roleId: r.roleId,
                    roleName: roleInfo.RoleName,
                    clazz: roleInfo.Clazz,
                    grade: roleInfo.Grade,
                    gender: roleInfo.Gender,
                    xianfanstatus: roleInfo.XianFanStatus,
                },
                id: r.id,
                type: r.type,
                text: r.text || "",
                createTime: r.createTime,
                notion,
                notionComment,
            };
        });
        const data = { list, card, count };
        yield (0, service_2.viewNotificationTimeSet)(roleId, cardId, Date.now());
        yield (0, service_2.clearHasNewRedDot)(roleId, cardId);
        return data;
    });
}
exports.cardNotificationList = cardNotificationList;
/** 卡片通知列表全部设置为已读 */
function cardNotificationReadAll(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const ret = yield models_1.CardNotificationModel.updateByCondition({ targetId: params.roleid, cardId: params.card_id, status: 0 /* UnRead */ }, { status: 1 /* Read */ });
        const isOk = ret.affectedRows > 0;
        const data = { isOk };
        yield (0, service_2.clearHasNewRedDot)(params.roleid, params.card_id);
        return data;
    });
}
exports.cardNotificationReadAll = cardNotificationReadAll;
/** 卡片通知列表全部清除 */
function cardNotificationDelAll(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const ret = yield models_1.CardNotificationModel.updateByCondition({ targetId: params.roleid, cardId: params.card_id }, { status: -1 /* Delete */ });
        const isOk = ret.affectedRows > 0;
        const data = { isOk };
        yield (0, service_2.clearHasNewRedDot)(params.roleid, params.card_id);
        return data;
    });
}
exports.cardNotificationDelAll = cardNotificationDelAll;
/** 卡片通知新消息数量 */
function cardNotificationNewNum(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const { roleid, card_id } = params;
        const count = yield (0, service_2.getNewNotificationNum)(roleid, card_id);
        if (!count) {
            yield (0, service_2.unsetHasNewRedDot)(params.roleid, params.card_id);
        }
        return { count };
    });
}
exports.cardNotificationNewNum = cardNotificationNewNum;
/** 卡片通知列表单条设置为已读 */
function cardNotificationRead(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const ret = yield models_1.CardNotificationModel.updateByCondition({ targetId: params.roleid, id: params.id, status: 0 /* UnRead */ }, { status: 1 /* Read */ });
        const isOk = ret.affectedRows > 0;
        const data = { isOk };
        return data;
    });
}
exports.cardNotificationRead = cardNotificationRead;
//# sourceMappingURL=operation.js.map