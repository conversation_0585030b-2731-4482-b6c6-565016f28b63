"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = void 0;
exports.ReqSchemas = {
    CardNotificationList: {
        roleid: { type: Number },
        card_id: { type: "integer" },
        type: { type: Number, required: false, values: [0, 1] },
        page: { type: Number, default: 1 },
        pageSize: { type: Number, default: 10 },
    },
    CardNotificationReadAll: {
        card_id: { type: "integer" },
        roleid: { type: Number },
    },
    CardNotificationDelAll: {
        card_id: { type: "integer" },
        roleid: { type: Number },
    },
    CardNotificationNewNum: {
        card_id: { type: "integer" },
        roleid: { type: Number },
    },
    CardNotificationRead: {
        roleid: { type: Number },
        id: { type: Number },
    },
};
//# sourceMappingURL=type.js.map