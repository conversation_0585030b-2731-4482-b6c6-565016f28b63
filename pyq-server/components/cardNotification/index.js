"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CardNotificationComponent = exports.paths = void 0;
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "post",
        url: "/card/notification/list",
        paramsSchema: type_1.ReqSchemas.CardNotificationList,
        operation: operation_1.cardNotificationList,
    },
    {
        method: "post",
        url: "/card/notification/del_all",
        paramsSchema: type_1.ReqSchemas.CardNotificationDelAll,
        operation: operation_1.cardNotificationDelAll,
    },
    {
        method: "post",
        url: "/card/notification/new_num",
        paramsSchema: type_1.ReqSchemas.CardNotificationNewNum,
        operation: operation_1.cardNotificationNewNum,
    },
    {
        method: "post",
        url: "/card/notification/read_all",
        paramsSchema: type_1.ReqSchemas.CardNotificationReadAll,
        operation: operation_1.cardNotificationReadAll,
    },
    {
        method: "post",
        url: "/card/notification/read",
        paramsSchema: type_1.ReqSchemas.CardNotificationRead,
        operation: operation_1.cardNotificationRead,
    },
];
exports.CardNotificationComponent = {
    paths: exports.paths,
    prefix: "/card/notification/",
};
//# sourceMappingURL=index.js.map