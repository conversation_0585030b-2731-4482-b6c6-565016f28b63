import { Path } from "../../types/type";
import { cardNotificationList, cardNotificationReadAll, cardNotificationRead, cardNotificationDelAll, cardNotificationNewNum } from "./operation";
import { ReqSchemas } from "./type";

export const paths: Path[] = [
  {
    method: "post",
    url: "/card/notification/list",
    paramsSchema: ReqSchemas.CardNotificationList,
    operation: cardNotificationList,
  },
  {
    method: "post",
    url: "/card/notification/del_all",
    paramsSchema: ReqSchemas.CardNotificationDelAll,
    operation: cardNotificationDelAll,
  },
  {
    method: "post",
    url: "/card/notification/new_num",
    paramsSchema: ReqSchemas.CardNotificationNewNum,
    operation: cardNotificationNewNum,
  },
  {
    method: "post",
    url: "/card/notification/read_all",
    paramsSchema: ReqSchemas.CardNotificationReadAll,
    operation: cardNotificationReadAll,
  },
  {
    method: "post",
    url: "/card/notification/read",
    paramsSchema: ReqSchemas.CardNotificationRead,
    operation: cardNotificationRead,
  },
];

export const CardNotificationComponent = {
  paths: paths,
  prefix: "/card/notification/",
};
