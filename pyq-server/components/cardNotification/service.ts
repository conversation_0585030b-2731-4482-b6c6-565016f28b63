import _ = require("lodash")
import { ONE_DAY_SECONDS } from "../../../common/config";
import { Statues } from "../../../common/constants";
import { getRedis } from "../../../common/redisNew";
import { ExpireType } from "../../../common/redisTypes";
import { cache<PERSON>eyGen, keyToRecordMap } from "../../../common/util2"
import { BussError, CardErrors } from "../../errorCodes";
import { CardNotificationModel, CardNotificationRecord, CardNotionCommentModel, CardNotionModel, CardUserModel, CardUserRecord } from "../../models"
import { EHasNewStatus, ENotificationType, ENotificationStatus } from "../../types/type"


export async function findUnReadNotification(id: number) {
    const r = await CardNotificationModel.findOne({ id, status: ENotificationStatus.UnRead });
    if (!r) {
        return BussError(CardErrors.UnReadNotificationNotFound);
    }
    return r;
}

export function isTypeForNotion(type: ENotificationType) {
    return type === ENotificationType.NotionBeLiked || type === ENotificationType.NotionBeComment
}


export function isTypeForComment(type: ENotificationType) {
    return type === ENotificationType.CommentBeReplied
}

export function getSplitRelatedIds(list: Pick<CardNotificationRecord, "relateId" | "type" | "notionId">[]) {
    const commentIdsSet: Set<number> = new Set()

    for (let r of list) {
        if (isTypeForComment(r.type)) {
            commentIdsSet.add(r.relateId)
        }
    }

    const notionIds = list.map(r => r.notionId)
    const commentIds = Array.from(commentIdsSet)
    return { notionIds, commentIds }
}


export async function getRelateEntityFetcher(list: Pick<CardNotificationRecord, "relateId" | "type" | "notionId">[]) {
    const { notionIds, commentIds } = getSplitRelatedIds(list)
    const notionList = await CardNotionModel.find({ id: notionIds, status: Statues.Normal }, { cols: ['id', 'text'] })
    const notionMap = keyToRecordMap(notionList, 'id')
    const commentList = await CardNotionCommentModel.find({ id: commentIds }, { cols: ['id', 'text'] })
    const commentMap = keyToRecordMap(commentList, 'id')
    const notionFetcher = (notionId: number) => {
        const notion = notionMap.get(notionId) || { text: "" }
        return { id: notionId, text: notion.text }
    }
    const commentFetcher = (commentId: number) => {
        const notion = commentMap.get(commentId) || { text: "" }
        return { id: commentId, text: notion.text }
    }
    return { notionFetcher, commentFetcher }
}


async function updateRedNotNewStatus(roleId: number, cardId: number, hasNew: EHasNewStatus) {
    const props: Omit<CardUserRecord, "id"> = {
        roleId,
        cardId,
        hasNew
    }
    const upProps = { hasNew }
    const oldRecord = await CardUserModel.findOne({
        roleId,
        cardId,
    });
    if (oldRecord) {
        await CardUserModel.updateById(oldRecord.id, upProps);
    } else {
        await CardUserModel.insert(props);
    }
}

async function markHasNewRedDot(roleId: number, cardId: number) {
    return updateRedNotNewStatus(roleId, cardId, EHasNewStatus.HasNew)
}


export async function clearHasNewRedDot(roleId: number, cardId: number) {
    return updateRedNotNewStatus(roleId, cardId, EHasNewStatus.NoNew)
}

export async function checkAndClearHasNewRedDot(targetId: number, cardId: number) {
    const query = CardNotificationModel.scope()
        .where('targetId', targetId)
        .where('cardId', cardId)
        .where('status', ENotificationStatus.UnRead);
    const count = await CardNotificationModel.countByQuery(query);
    if (!count) {
        return clearHasNewRedDot(targetId, cardId);
    }
}

export async function unsetHasNewRedDot(roleId: number, cardId: number) {
    const oldRecord = await CardUserModel.findOne({
        roleId,
        cardId,
    });
    if (!oldRecord || !oldRecord.hasNew) {
        return;
    }
    await CardUserModel.updateById(oldRecord.id, { hasNew: EHasNewStatus.NoNew });
}


type AddNotificationProps = Pick<CardNotificationRecord, "text" | "relateId" | "roleId" | "targetId" | "cardId" | "notionId" | "actionId">

export async function addNotificationByAction(type: ENotificationType, props: AddNotificationProps) {
    if (props.roleId !== props.targetId) {
        const insertProps: Omit<CardNotificationRecord, "id"> = buildNotificationByAction(type, props);
        await CardNotificationModel.insert(insertProps);
        await markHasNewRedDot(props.targetId, props.cardId)
    }
}

export function buildNotificationByAction(type: ENotificationType, props: AddNotificationProps
) {
    const insertProps: Omit<CardNotificationRecord, "id"> = {
        roleId: props.roleId,
        type,
        cardId: props.cardId,
        notionId: props.notionId,
        targetId: props.targetId,
        text: props.text || "",
        relateId: props.relateId || 0,
        actionId: props.actionId,
        status: ENotificationStatus.UnRead,
        createTime: Date.now(),
    };
    return insertProps;
}

const ViewNotificationTimeKey = "card_notification_view_time"


function viewNotificationTimeKey(roleId: number, cardId: number) {
    return cacheKeyGen(ViewNotificationTimeKey, { roleId, cardId })
}

export async function viewNotificationTimeSet(roleId: number, cardId: number, ts: number) {
    const key = viewNotificationTimeKey(roleId, cardId)
    const ret = await getRedis().setAsync(key, ts, ExpireType.EX, 90 * ONE_DAY_SECONDS)
    return ret
}

export async function viewNotificationTimeGet(roleId: number, cardId: number): Promise<number> {
    const key = viewNotificationTimeKey(roleId, cardId)
    const ret = await getRedis().getAsync(key)
    const timeTs = parseInt(ret, 10)
    if (timeTs > 0) {
        return timeTs
    } else {
        return 0
    }
}

export async function getNewNotificationNum(roleId: number, cardId: number) {
    const viewTime = await viewNotificationTimeGet(roleId, cardId)
    const query = CardNotificationModel.normalScope()
        .where('targetId', roleId)
        .where('cardId', cardId)
        .where('createTime', '>', viewTime)
    const cnt = await CardNotificationModel.countByQuery(query)
    return cnt
}


const actionNameMap = {
    [ENotificationType.NotionBeLiked]: "like",
    [ENotificationType.CommentBeReplied]: "comment",
    [ENotificationType.NotionBeComment]: "comment",
}

export function actionNotificationId(action: ENotificationType, id: number) {
    const typeStr = actionNameMap[action]
    return [typeStr, id].join("-")
}


export async function delNotificationByActionId(action: ENotificationType, id: number) {
    const actionId = actionNotificationId(action, id)
    const notifications = await CardNotificationModel.find({ actionId, status: [ENotificationStatus.UnRead, ENotificationStatus.Read] });
    if (!notifications.length) {
        return;
    }
    const ret = await CardNotificationModel.softDeleteByCondition({ actionId })
    // 清除掉用户的小红点
    const cardId = notifications[0].cardId;
    if (cardId) {
        const targetIds = notifications.map(item => item.targetId);
        await Promise.all(targetIds.map(targetId => checkAndClearHasNewRedDot(targetId, cardId)));
    }
    return ret
}