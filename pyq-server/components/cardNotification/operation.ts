import { CardNotificationModel, CardNotionCommentModel } from "../../models";
import { getUserCardInfoView } from "../card/service";
import { CardNotificationReq, CardNotificationRes } from "./type";
import * as _ from 'lodash'
import { getRoleInfoSafeFetcher } from "../../services/roleInfo";
import { checkAndClearHasNewRedDot, clearHasNewRedDot, getNewNotificationNum, getRelateEntityFetcher, isTypeForComment, isTypeForNotion, unsetHasNewRedDot, viewNotificationTimeSet } from "./service";
import { ENotificationStatus } from "../../types/type";

/** 卡片通知列表 */
export async function cardNotificationList(params: CardNotificationReq.List): Promise<CardNotificationRes.List> {
  const roleId = params.roleid
  const cardId = params.card_id
  const card = await getUserCardInfoView(roleId, cardId)

  const query = CardNotificationModel.normalScope()
    .where('cardId', cardId)
    .where('targetId', roleId)

  const result = await CardNotificationModel.powerListQuery({
    initQuery: query,
    select: ['id', 'roleId', 'createTime', 'text', 'type', 'relateId', "notionId"],
    pagination: { page: params.page, pageSize: params.pageSize },
    orderBy: [['createTime'], ['desc']]
  })

  const rawList = result.list
  const count = result.meta.totalCount
  const queryRoleIds = _.map(rawList, r => r.roleId)
  const roleInfoFetcher = await getRoleInfoSafeFetcher(queryRoleIds)
  const { notionFetcher, commentFetcher } = await getRelateEntityFetcher(rawList)

  const list: CardNotificationRes.List["list"] = rawList.map(r => {
    const roleInfo = roleInfoFetcher(r.roleId)
    const notion = notionFetcher(r.notionId)
    const notionComment = isTypeForComment(r.type) ? commentFetcher(r.relateId) : null
    return {
      notifier: {
        roleId: r.roleId,
        roleName: roleInfo.RoleName,
        clazz: roleInfo.Clazz,
        grade: roleInfo.Grade,
        gender: roleInfo.Gender,
        xianfanstatus: roleInfo.XianFanStatus,
      },
      id: r.id,
      type: r.type,
      text: r.text || "",
      createTime: r.createTime,
      notion,
      notionComment,
    }
  })

  const data: CardNotificationRes.List = { list, card, count };
  await viewNotificationTimeSet(roleId, cardId, Date.now())
  await clearHasNewRedDot(roleId, cardId)
  return data;
}

/** 卡片通知列表全部设置为已读 */
export async function cardNotificationReadAll(
  params: CardNotificationReq.ReadAll
): Promise<CardNotificationRes.ReadAll> {
  const ret = await CardNotificationModel.updateByCondition({ targetId: params.roleid, cardId: params.card_id, status: ENotificationStatus.UnRead }, { status: ENotificationStatus.Read })
  const isOk = ret.affectedRows > 0
  const data: CardNotificationRes.ReadAll = { isOk };
  await clearHasNewRedDot(params.roleid, params.card_id)
  return data;
}


/** 卡片通知列表全部清除 */
export async function cardNotificationDelAll(
  params: CardNotificationReq.DelAll
): Promise<CardNotificationRes.DelAll> {
  const ret = await CardNotificationModel.updateByCondition({ targetId: params.roleid, cardId: params.card_id }, { status: ENotificationStatus.Delete })
  const isOk = ret.affectedRows > 0
  const data: CardNotificationRes.DelAll = { isOk };
  await clearHasNewRedDot(params.roleid, params.card_id)
  return data;
}


/** 卡片通知新消息数量 */
export async function cardNotificationNewNum(
  params: CardNotificationReq.NewNum
): Promise<CardNotificationRes.NewNum> {
  const { roleid, card_id } = params
  const count = await getNewNotificationNum(roleid, card_id)
  if (!count) {
    await unsetHasNewRedDot(params.roleid, params.card_id);
  }
  return { count }
}

/** 卡片通知列表单条设置为已读 */
export async function cardNotificationRead(params: CardNotificationReq.Read): Promise<CardNotificationRes.Read> {
  const ret = await CardNotificationModel.updateByCondition({ targetId: params.roleid, id: params.id, status: ENotificationStatus.UnRead }, { status: ENotificationStatus.Read })
  const isOk = ret.affectedRows > 0
  const data: CardNotificationRes.Read = { isOk };
  return data;
}