import { MomentModel } from "../../models";
import { ThisDayThatYearReq, ThisDayThatYearRes } from "./type";
import { formatDate } from "../../../common/util";
import { startOfDay } from "../../../common/dateUtil";
import { fillMomentsInfo } from "../../../service/qnm/pyq/moment";
import { getLangFromRequest } from "../../../common/util2";

function thisDayThatYearScope(roleId: number) {
  const now = new Date();
  const dateStr = formatDate(now, "MM-dd");
  const dayStartTs = startOfDay(now).getTime();
  const query = MomentModel.normalScope()
    .where("RoleId", roleId)
    .whereRaw(`from_unixtime(CreateTime/1000, '%m-%d') = ?`, [dateStr])
    .where("CreateTime", "<", dayStartTs);
  return query;
}

/** 玩家在往年的同一日期至少发布过一条动态的时候返回为true */
export async function thisDayThatYearMomentHas(
  params: ThisDayThatYearReq.MomentHas
): Promise<ThisDayThatYearRes.MomentHas> {
  const { roleid } = params;
  const query = thisDayThatYearScope(roleid).limit(1);
  const rows: any[] = await MomentModel.executeByQuery(query);
  const hasMoment = rows && rows.length > 0;
  const data: ThisDayThatYearRes.MomentHas = {
    hasMoment,
  };

  return data;
}

/** 返回那年同一日的动态列表，按照时间倒序 */
export async function thisDayThatYearMomentList(
  params: ThisDayThatYearReq.MomentList,
  req
): Promise<ThisDayThatYearRes.MomentList> {
  const { roleid, page, pagesize } = params;
  const lang = getLangFromRequest(req);
  const query = thisDayThatYearScope(roleid);
  const countQuery = query.clone();
  const count = await MomentModel.countByQuery(countQuery);
  const moments = await MomentModel.queryWithPagination(query, { page, pageSize: pagesize });
  const momentList = await fillMomentsInfo({ roleid }, moments, lang);

  const data: ThisDayThatYearRes.MomentList = {
    list: momentList,
    count,
  };

  return data;
}
