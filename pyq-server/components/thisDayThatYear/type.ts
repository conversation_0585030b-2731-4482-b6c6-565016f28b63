import { operations } from "../../types/swagger";

export namespace ThisDayThatYearReq {
  export type MomentHas = operations["thisDayThatYearMomentHas"]["parameters"]["query"];
  export type MomentList = operations["thisDayThatYearMomentList"]["parameters"]["query"];
}

export namespace ThisDayThatYearRes {
  export type MomentHas =
    operations["thisDayThatYearMomentHas"]["responses"]["200"]["content"]["application/json"]["data"];
  export type MomentList =
    operations["thisDayThatYearMomentList"]["responses"]["200"]["content"]["application/json"]["data"];
}

export const ReqSchemas = {
  ThisDayThatYearMomentHas: {
    roleid: { type: Number },
  },

  ThisDayThatYearMomentList: {
    roleid: { type: Number },
    page: { type: Number, default: 1 },
    pagesize: { type: Number, default: 10, max: 20 },
  },
};
