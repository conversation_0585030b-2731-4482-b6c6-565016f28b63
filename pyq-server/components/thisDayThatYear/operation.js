"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.thisDayThatYearMomentList = exports.thisDayThatYearMomentHas = void 0;
const models_1 = require("../../models");
const util_1 = require("../../../common/util");
const dateUtil_1 = require("../../../common/dateUtil");
const moment_1 = require("../../../service/qnm/pyq/moment");
const util2_1 = require("../../../common/util2");
function thisDayThatYearScope(roleId) {
    const now = new Date();
    const dateStr = (0, util_1.formatDate)(now, "MM-dd");
    const dayStartTs = (0, dateUtil_1.startOfDay)(now).getTime();
    const query = models_1.MomentModel.normalScope()
        .where("RoleId", roleId)
        .whereRaw(`from_unixtime(CreateTime/1000, '%m-%d') = ?`, [dateStr])
        .where("CreateTime", "<", dayStartTs);
    return query;
}
/** 玩家在往年的同一日期至少发布过一条动态的时候返回为true */
function thisDayThatYearMomentHas(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const { roleid } = params;
        const query = thisDayThatYearScope(roleid).limit(1);
        const rows = yield models_1.MomentModel.executeByQuery(query);
        const hasMoment = rows && rows.length > 0;
        const data = {
            hasMoment,
        };
        return data;
    });
}
exports.thisDayThatYearMomentHas = thisDayThatYearMomentHas;
/** 返回那年同一日的动态列表，按照时间倒序 */
function thisDayThatYearMomentList(params, req) {
    return __awaiter(this, void 0, void 0, function* () {
        const { roleid, page, pagesize } = params;
        const lang = (0, util2_1.getLangFromRequest)(req);
        const query = thisDayThatYearScope(roleid);
        const countQuery = query.clone();
        const count = yield models_1.MomentModel.countByQuery(countQuery);
        const moments = yield models_1.MomentModel.queryWithPagination(query, { page, pageSize: pagesize });
        const momentList = yield (0, moment_1.fillMomentsInfo)({ roleid }, moments, lang);
        const data = {
            list: momentList,
            count,
        };
        return data;
    });
}
exports.thisDayThatYearMomentList = thisDayThatYearMomentList;
//# sourceMappingURL=operation.js.map