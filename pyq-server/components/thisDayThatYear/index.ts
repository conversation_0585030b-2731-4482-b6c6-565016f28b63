import { Path } from "../../types/type";
import { thisDayThatYearMomentHas, thisDayThatYearMomentList } from "./operation";
import { ReqSchemas } from "./type";

export const paths: Path[] = [
  {
    method: "post",
    url: "/this_day_that_year/moment_has",
    paramsSchema: ReqSchemas.ThisDayThatYearMomentHas,
    operation: thisDayThatYearMomentHas,
  },
  {
    method: "post",
    url: "/this_day_that_year/moment_list",
    paramsSchema: ReqSchemas.ThisDayThatYearMomentList,
    operation: thisDayThatYearMomentList,
  },
];

export const ThisDayThatYearComponent = {
  paths: paths,
  prefix: "/this_day_that_year/",
};
