import { Path } from "../../types/type";
import {
  equipCommentsCreate,
  equipCommentsList,
  equipWeaponsFilters,
  equipWeaponsRank,
  equipTreasuresRank,
} from "./operation";
import { ReqSchemas } from "./type";
import { jsonp } from '../../../common/betterJsonp'
import { injectIpHelper } from "../../middlewares/ipAuth";

export const paths: Path[] = [
  {
    method: "post",
    url: "/equip/comments/create",
    before: [injectIpHelper()],
    paramsSchema: ReqSchemas.EquipCommentsCreate,
    operation: equipCommentsCreate,
    option: { skipAuth: true },
  },
  {
    method: "post",
    url: "/equip/comments/list",
    paramsSchema: ReqSchemas.EquipCommentsList,
    operation: equipCommentsList,
    option: { skipAuth: true },
  },
  {
    method: "get",
    url: "/equip/weapons/filters",
    before: [jsonp()],
    paramsSchema: ReqSchemas.EquipWeaponsFilters,
    operation: equipWeaponsFilters,
  },
  {
    method: "get",
    url: "/equip/weapons/rank",
    before: [jsonp()],
    paramsSchema: ReqSchemas.EquipWeaponsRank,
    operation: equipWeaponsRank,
  },
  {
    method: "get",
    url: "/equip/treasures/rank",
    paramsSchema: ReqSchemas.EquipTreasuresRank,
    before: [jsonp()],
    operation: equipTreasuresRank,
  },
];

export const EquipComponent = {
  paths: paths,
  prefix: "/equip/",
};