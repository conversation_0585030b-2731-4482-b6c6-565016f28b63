"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.equipTreasuresRank = exports.equipWeaponsRank = exports.getEquipRankList = exports.getEquipRankListFromDB = exports.EPreciousRank = exports.equipWeaponsFilters = exports.equipCommentsList = exports.equipCommentsCreate = void 0;
const util2_1 = require("../../../common/util2");
const QnmEquipRank_1 = require("../../../models/QnmEquipRank");
const cacheUtil_1 = require("../../common/cacheUtil");
const service_1 = require("./service");
const QnmEquipComment = require("../../../models/QnmEquipComment");
/** 发言限制: *每个玩家每个武器1分钟限制1条弹幕* */
function equipCommentsCreate(params) {
    return __awaiter(this, void 0, void 0, function* () {
        yield (0, service_1.checkTextByyFilter)(params.text);
        yield QnmEquipComment.addComment(params.ip, params.equipId, params.text);
        const data = {};
        return data;
    });
}
exports.equipCommentsCreate = equipCommentsCreate;
/** 兵器谱弹幕装备列表 */
function equipCommentsList(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const list = yield QnmEquipComment.listCommentsText(params.equipId);
        const data = {
            list,
        };
        return data;
    });
}
exports.equipCommentsList = equipCommentsList;
/** 获取兵器谱排行筛选数据 */
function equipWeaponsFilters() {
    return __awaiter(this, void 0, void 0, function* () {
        const servers = yield (0, service_1.getServerFilters)();
        const data = {
            servers,
            equipPositions: service_1.EquipPositions,
        };
        return data;
    });
}
exports.equipWeaponsFilters = equipWeaponsFilters;
const COLS_FOR_LIST = [
    "rank_pos as Rank",
    "ownner_id as RoleId",
    "equip_gk as EquipId",
    "equip_name as EquipName",
    "score as Score",
    "equip_type as PositionId",
];
var EPreciousRank;
(function (EPreciousRank) {
    /** 武器榜 */
    EPreciousRank[EPreciousRank["Weapon"] = 0] = "Weapon";
    /** 奇珍榜 */
    EPreciousRank[EPreciousRank["Precious"] = 1] = "Precious";
})(EPreciousRank = exports.EPreciousRank || (exports.EPreciousRank = {}));
function getLatestRankDs() {
    return __awaiter(this, void 0, void 0, function* () {
        const query = QnmEquipRank_1.default.scope().orderBy("ds", "desc").limit(1).select(["ds"]);
        const rows = yield QnmEquipRank_1.default.executeByQuery(query);
        if (rows && rows.length > 0) {
            return rows[0].ds;
        }
        else {
            return "";
        }
    });
}
function getEquipRankListFromDB(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const serverId = params.server_id;
        const equip_type = params.equip_type;
        const ds = yield getLatestRankDs();
        const condition = { server: serverId, precious_rank: params.precious_rank, ds };
        if (equip_type > 0) {
            condition["equip_type"] = equip_type;
        }
        const COLS_FOR_LIST = [
            "rank_pos as Rank",
            "ownner_id as RoleId",
            "equip_gk as EquipId",
            "equip_name as EquipName",
            "server as Server",
            "score as Score",
            "equip_type as PositionId",
            "tag_name as TagName",
        ];
        const query = QnmEquipRank_1.default.scope().where(condition).select(COLS_FOR_LIST).orderBy("rank_pos", "asc");
        const totalCount = yield QnmEquipRank_1.default.count(condition);
        const totalPage = Math.ceil(totalCount / params.pageSize);
        const ranks = yield QnmEquipRank_1.default.queryWithPagination(query, params);
        const list = yield (0, service_1.fillEquipRankInfo)(ranks);
        const updateStamp = Date.now();
        if (params.precious_rank === EPreciousRank.Precious) {
            for (const e of list) {
                e.RankName = (0, service_1.findTreasureRankName)(e.PositionId);
                e.RoleName = e.RoleName || "";
            }
        }
        const updateTime = yield (0, service_1.getUpdateTime)(updateStamp);
        return {
            list: list,
            updateTime: updateTime,
            pagination: {
                cur: params.page,
                total: totalPage,
            },
        };
    });
}
exports.getEquipRankListFromDB = getEquipRankListFromDB;
exports.getEquipRankList = (0, cacheUtil_1.smartMemorize)(getEquipRankListFromDB, {
    keyGen: function (params) {
        return (0, util2_1.cacheKeyGen)("qnm:equip_rank_list", {
            server_id: params.server_id,
            equip_type: params.equip_type,
            page: params.page,
            pageSize: params.pageSize,
            precious_rank: params.precious_rank,
        });
    },
    expireSeconds: 60,
});
/** 获取兵器谱排行 */
function equipWeaponsRank(params) {
    return __awaiter(this, void 0, void 0, function* () {
        return (0, exports.getEquipRankList)({
            server_id: params.server_id,
            precious_rank: EPreciousRank.Weapon,
            page: params.page,
            pageSize: params.page_size,
            equip_type: params.equip_position,
        });
    });
}
exports.equipWeaponsRank = equipWeaponsRank;
/** 获取奇珍榜排行 */
function equipTreasuresRank(params) {
    return __awaiter(this, void 0, void 0, function* () {
        return (0, exports.getEquipRankList)({
            server_id: params.server_id,
            precious_rank: EPreciousRank.Precious,
            page: params.page,
            pageSize: params.page_size,
        });
    });
}
exports.equipTreasuresRank = equipTreasuresRank;
//# sourceMappingURL=operation.js.map