/* eslint-disable @typescript-eslint/no-var-requires */
import _ = require("lodash");
import { getServerGroupMap } from "../../../service/qnm/server/serverList";
import * as config from "../../common/config";
import * as QnmRoleInfo from "../../../models/QNMRoleInfos";
import { formatDate } from "../../../common/util";
import BingQiPuEquip = require("../../../models/QnmBingQiPuEquip");
import bluebird = require("bluebird");
import { ProfileService } from "../../services/proxy";
const hasSensitiveWord = require("../../../common/data").qnm.textFilter;
const equipIconMeta: EquipMetaItem[] = require("../../asserts/json/equip_icon_meta.json");
import { parseEquipItem, qnm as qnmDataUtil } from "../../../common/data";
import { getHash2 } from "../../../service/qnm/server/list";

interface EquipMetaItem {
  equipType: number;
  id: number;
  name: string;
  icon: string;
  path: string;
  keyName: string;
  url: string;
}

export interface EquipRankItem {
  Rank: number;
  RoleId: number;
  EquipId: string;
  EquipName: string;
  EquipIcon: string;
  Score: number;
  PositionId: number;
  RoleName: string;
  /** 排行榜单名字 */
  RankName: string;

  Server: number;

  Avatar: string;
  EquipDetail: {
    small: string;
    desc: string;
    color: string;
  };

  TagName: string;
}

export const EquipPositions = [
  { id: 1, name: "武器" },
  { id: 16, name: "副手" },
  { id: 17, name: "头盔" },
  { id: 18, name: "甲胄" },
  { id: 19, name: "腰带" },
  { id: 20, name: "手套" },
  { id: 21, name: "鞋子" },
  { id: 22, name: "戒指" },
  { id: 23, name: "手镯" },
  { id: 24, name: "项链" },
];

export const TreasureRanks = [
  { id: 1, name: "根骨条数最多" },
  { id: 2, name: "精力条数最多" },
  { id: 3, name: "力量条数最多" },
  { id: 4, name: "智力条数最多" },
  { id: 5, name: "敏捷条数最多" },
  { id: 6, name: "洗炼积分最高" },
  { id: 7, name: "物理躲避条数最多" },
  { id: 8, name: "法术躲避条数最多" },
  { id: 9, name: "物理命中条数最多" },
  { id: 10, name: "法术命中条数最多" },
  { id: 11, name: "冰抗条数最多" },
  { id: 12, name: "火抗条数最多" },
  { id: 13, name: "电抗条数最多" },
  { id: 14, name: "毒抗条数最多" },
  { id: 15, name: "风抗条数最多" },
  { id: 16, name: "光抗条数最多" },
  { id: 17, name: "幻抗条数最多" },
  { id: 18, name: "水抗条数最多" },
  { id: 19, name: "物理攻速条数最多" },
  { id: 20, name: "法术攻速条数最多" },
  { id: 21, name: "气血上限条数最多" },
];

export function findTreasureRankName(id: number) {
  const item = TreasureRanks.find((r) => r.id === id) || { name: "" };
  return item.name;
}

export async function getServerFilters() {
  const serverGroupMap = await getServerGroupMap();
  const serverGroups = {};
  for (const [group, servers] of serverGroupMap.entries()) {
    if (group !== "跨服专区" && servers.length > 0) serverGroups[group] = servers;
  }
  if (config.testCfg.test_env) {
    serverGroups["内网测试"] = _.map(_.range(1, 20), (id) => {
      return { id: id, name: "测试服:" + id };
    });
  }
  return serverGroups;
}

async function getServerHash() {
  const hash = await getHash2();
  if (config.testCfg.test_env) {
    for (let i = 1; i < 20; i++) {
      hash[i] = { group: "内网测试", name: "测试服:" + i };
    }
  }
  return hash;
}

export namespace EquipDetailNs {
  export interface Equip {
    pos: number;
    templateid: number;
    color: number;
    equipname: string;
    type: string;
    words: string[];
    jewelids: any[];
    jewelwords: any[];
    holenum: number;
    duration: string;
    score: number;
    gemgroup: string;
    gemwords: any[];
    levellimit: number;
    props: string[];
    perfection: number;
    intensifylevel: number;
  }

  export interface EquipDetail {
    equipscore?: string;
    equips?: Equip[];
  }
}

const DefaultEquipIconUrl = "http://hi-163-qnm.nosdn.127.net/asserts/icons/128x128/equip/beiyong.jpg";

function getEquipIcon(templateId: number) {
  const item = equipIconMeta.find((r) => r.id === templateId) || { url: DefaultEquipIconUrl };
  return item.url;
}

export interface EquipHtmlShow {
  small: string;
  desc: string;
  color: string;
}

export function equipRecordToHtml(equip: { templateid: number }): EquipHtmlShow {
  const item = qnmDataUtil.getEquip(equip.templateid);
  const small = getEquipIcon(equip.templateid);
  let str = parseEquipItem(equip);
  str = (small ? '<img src="' + small + '" /><br/>' : "") + str;
  return { desc: str, small: small, color: item.color };
}

export async function fillEquipRankInfo(rankList: EquipRankItem[]) {
  const roleIds = _.map(rankList, "RoleId");
  const equipIds = _.map(rankList, "EquipId");
  const roleInfos = await QnmRoleInfo.find(
    { RoleId: roleIds },
    { cols: ["RoleId", "RoleName", "ServerId", "Gender", "JobId"] }
  );
  const equipDetails = await BingQiPuEquip.getEquipDetailsHash(equipIds);
  const serverHash = await getServerHash();
  return _.map(rankList, (row) => {
    const info = _.find(roleInfos, { RoleId: row.RoleId }) || { RoleName: "" };
    const roleName = info.RoleName || "";
    const server = serverHash[info.ServerId];
    const avatar = QnmRoleInfo.getJobAvatar(info);
    const equipDetail: EquipDetailNs.Equip = equipDetails[row.EquipId];
    let equipIcon = DefaultEquipIconUrl;
    let equipDetailHtml: Partial<EquipHtmlShow> = {};
    if (equipDetail) {
      equipDetailHtml = equipRecordToHtml(equipDetail);
      if (_.isNumber(equipDetail.templateid)) {
        const templateId = equipDetail.templateid;
        equipIcon = getEquipIcon(templateId);
      }
    }
    const item: EquipRankItem = _.assign(
      row,
      { RoleName: roleName },
      { Avatar: avatar },
      { Server: server },
      { EquipDetail: equipDetailHtml, EquipIcon: equipIcon }
    );
    return item;
  });
}

export async function getUpdateTime(date?) {
  return Promise.resolve(formatDate(date || Date.now(), "yyyy-MM-dd"));
}

export function checkBanEquipComment(roleid: number) {
  return ProfileService.getBanState(roleid, "EquipComment").catch(() => {
    return bluebird.reject({ errorType: "BeBaned", msg: "您已被管理员禁止添加兵器谱评论" });
  });
}

export function checkTextByyFilter(text) {
  return new Promise((resolve, reject) => {
    if (hasSensitiveWord(text)) {
      reject({ errorType: "SensitiveWord", msg: "包含敏感词汇" });
    } else {
      resolve(true);
    }
  });
}
