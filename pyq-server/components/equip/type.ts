import { operations } from "../../types/swagger";

export namespace EquipReq {
  export type CommentsCreate = operations["equipCommentsCreate"]["parameters"]["query"];
  export type CommentsList = operations["equipCommentsList"]["parameters"]["query"];
  export type WeaponsRank = operations["equipWeaponsRank"]["parameters"]["query"];
  export type TreasuresRank = operations["equipTreasuresRank"]["parameters"]["query"];
}

export namespace EquipRes {
  export type CommentsCreate = operations["equipCommentsCreate"]["responses"]["200"]["content"]["application/json"]["data"];
  export type CommentsList = operations["equipCommentsList"]["responses"]["200"]["content"]["application/json"]["data"];
  export type WeaponsFilters =
    operations["equipWeaponsFilters"]["responses"]["200"]["content"]["application/json"]["data"];
  export type WeaponsRank = operations["equipWeaponsRank"]["responses"]["200"]["content"]["application/json"]["data"];
  export type TreasuresRank =
    operations["equipTreasuresRank"]["responses"]["200"]["content"]["application/json"]["data"];
}

export const ReqSchemas = {
  EquipCommentsCreate: {
    equipId: { type: String },
    text: { type: String },
  },

  EquipCommentsList: {
    equipId: { type: String },
  },

  EquipWeaponsFilters: {},

  EquipWeaponsRank: {
    server_id: { type: Number },
    equip_position: { type: Number },
    page: { type: Number, default: 1 },
    page_size: { type: "integer", min: 1, max: 20 },
  },

  EquipTreasuresRank: {
    server_id: { type: Number },
    page: { type: Number, default: 1 },
    page_size: { type: "integer", min: 1, max: 20 },
  },
};
