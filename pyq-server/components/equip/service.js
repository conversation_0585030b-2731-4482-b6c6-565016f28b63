"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkTextByyFilter = exports.checkBanEquipComment = exports.getUpdateTime = exports.fillEquipRankInfo = exports.equipRecordToHtml = exports.getServerFilters = exports.findTreasureRankName = exports.TreasureRanks = exports.EquipPositions = void 0;
/* eslint-disable @typescript-eslint/no-var-requires */
const _ = require("lodash");
const serverList_1 = require("../../../service/qnm/server/serverList");
const config = require("../../common/config");
const QnmRoleInfo = require("../../../models/QNMRoleInfos");
const util_1 = require("../../../common/util");
const BingQiPuEquip = require("../../../models/QnmBingQiPuEquip");
const bluebird = require("bluebird");
const proxy_1 = require("../../services/proxy");
const hasSensitiveWord = require("../../../common/data").qnm.textFilter;
const equipIconMeta = require("../../asserts/json/equip_icon_meta.json");
const data_1 = require("../../../common/data");
const list_1 = require("../../../service/qnm/server/list");
exports.EquipPositions = [
    { id: 1, name: "武器" },
    { id: 16, name: "副手" },
    { id: 17, name: "头盔" },
    { id: 18, name: "甲胄" },
    { id: 19, name: "腰带" },
    { id: 20, name: "手套" },
    { id: 21, name: "鞋子" },
    { id: 22, name: "戒指" },
    { id: 23, name: "手镯" },
    { id: 24, name: "项链" },
];
exports.TreasureRanks = [
    { id: 1, name: "根骨条数最多" },
    { id: 2, name: "精力条数最多" },
    { id: 3, name: "力量条数最多" },
    { id: 4, name: "智力条数最多" },
    { id: 5, name: "敏捷条数最多" },
    { id: 6, name: "洗炼积分最高" },
    { id: 7, name: "物理躲避条数最多" },
    { id: 8, name: "法术躲避条数最多" },
    { id: 9, name: "物理命中条数最多" },
    { id: 10, name: "法术命中条数最多" },
    { id: 11, name: "冰抗条数最多" },
    { id: 12, name: "火抗条数最多" },
    { id: 13, name: "电抗条数最多" },
    { id: 14, name: "毒抗条数最多" },
    { id: 15, name: "风抗条数最多" },
    { id: 16, name: "光抗条数最多" },
    { id: 17, name: "幻抗条数最多" },
    { id: 18, name: "水抗条数最多" },
    { id: 19, name: "物理攻速条数最多" },
    { id: 20, name: "法术攻速条数最多" },
    { id: 21, name: "气血上限条数最多" },
];
function findTreasureRankName(id) {
    const item = exports.TreasureRanks.find((r) => r.id === id) || { name: "" };
    return item.name;
}
exports.findTreasureRankName = findTreasureRankName;
function getServerFilters() {
    return __awaiter(this, void 0, void 0, function* () {
        const serverGroupMap = yield (0, serverList_1.getServerGroupMap)();
        const serverGroups = {};
        for (const [group, servers] of serverGroupMap.entries()) {
            if (group !== "跨服专区" && servers.length > 0)
                serverGroups[group] = servers;
        }
        if (config.testCfg.test_env) {
            serverGroups["内网测试"] = _.map(_.range(1, 20), (id) => {
                return { id: id, name: "测试服:" + id };
            });
        }
        return serverGroups;
    });
}
exports.getServerFilters = getServerFilters;
function getServerHash() {
    return __awaiter(this, void 0, void 0, function* () {
        const hash = yield (0, list_1.getHash2)();
        if (config.testCfg.test_env) {
            for (let i = 1; i < 20; i++) {
                hash[i] = { group: "内网测试", name: "测试服:" + i };
            }
        }
        return hash;
    });
}
const DefaultEquipIconUrl = "http://hi-163-qnm.nosdn.127.net/asserts/icons/128x128/equip/beiyong.jpg";
function getEquipIcon(templateId) {
    const item = equipIconMeta.find((r) => r.id === templateId) || { url: DefaultEquipIconUrl };
    return item.url;
}
function equipRecordToHtml(equip) {
    const item = data_1.qnm.getEquip(equip.templateid);
    const small = getEquipIcon(equip.templateid);
    let str = (0, data_1.parseEquipItem)(equip);
    str = (small ? '<img src="' + small + '" /><br/>' : "") + str;
    return { desc: str, small: small, color: item.color };
}
exports.equipRecordToHtml = equipRecordToHtml;
function fillEquipRankInfo(rankList) {
    return __awaiter(this, void 0, void 0, function* () {
        const roleIds = _.map(rankList, "RoleId");
        const equipIds = _.map(rankList, "EquipId");
        const roleInfos = yield QnmRoleInfo.find({ RoleId: roleIds }, { cols: ["RoleId", "RoleName", "ServerId", "Gender", "JobId"] });
        const equipDetails = yield BingQiPuEquip.getEquipDetailsHash(equipIds);
        const serverHash = yield getServerHash();
        return _.map(rankList, (row) => {
            const info = _.find(roleInfos, { RoleId: row.RoleId }) || { RoleName: "" };
            const roleName = info.RoleName || "";
            const server = serverHash[info.ServerId];
            const avatar = QnmRoleInfo.getJobAvatar(info);
            const equipDetail = equipDetails[row.EquipId];
            let equipIcon = DefaultEquipIconUrl;
            let equipDetailHtml = {};
            if (equipDetail) {
                equipDetailHtml = equipRecordToHtml(equipDetail);
                if (_.isNumber(equipDetail.templateid)) {
                    const templateId = equipDetail.templateid;
                    equipIcon = getEquipIcon(templateId);
                }
            }
            const item = _.assign(row, { RoleName: roleName }, { Avatar: avatar }, { Server: server }, { EquipDetail: equipDetailHtml, EquipIcon: equipIcon });
            return item;
        });
    });
}
exports.fillEquipRankInfo = fillEquipRankInfo;
function getUpdateTime(date) {
    return __awaiter(this, void 0, void 0, function* () {
        return Promise.resolve((0, util_1.formatDate)(date || Date.now(), "yyyy-MM-dd"));
    });
}
exports.getUpdateTime = getUpdateTime;
function checkBanEquipComment(roleid) {
    return proxy_1.ProfileService.getBanState(roleid, "EquipComment").catch(() => {
        return bluebird.reject({ errorType: "BeBaned", msg: "您已被管理员禁止添加兵器谱评论" });
    });
}
exports.checkBanEquipComment = checkBanEquipComment;
function checkTextByyFilter(text) {
    return new Promise((resolve, reject) => {
        if (hasSensitiveWord(text)) {
            reject({ errorType: "SensitiveWord", msg: "包含敏感词汇" });
        }
        else {
            resolve(true);
        }
    });
}
exports.checkTextByyFilter = checkTextByyFilter;
//# sourceMappingURL=service.js.map