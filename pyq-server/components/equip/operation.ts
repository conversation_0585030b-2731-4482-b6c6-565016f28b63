import { Pagination } from "../../../common/type"
import { cacheKeyGen } from "../../../common/util2"
import QnmEquipRank from "../../../models/QnmEquipRank"
import { smartMemorize } from "../../common/cacheUtil"
import {
  checkTextByyFilter,
  EquipPositions,
  EquipRankItem,
  fillEquipRankInfo,
  findTreasureRankName,
  getServerFilters,
  getUpdateTime,
} from "./service"
import { EquipReq, EquipRes } from "./type"
import * as QnmEquipComment from "../../../models/QnmEquipComment"

/** 发言限制: *每个玩家每个武器1分钟限制1条弹幕* */
export async function equipCommentsCreate(
  params: EquipReq.CommentsCreate & { ip: string }
): Promise<EquipRes.CommentsCreate> {
  await checkTextByyFilter(params.text)
  await QnmEquipComment.addComment(params.ip, params.equipId, params.text)
  const data: EquipRes.CommentsCreate = {}
  return data
}

/** 兵器谱弹幕装备列表 */
export async function equipCommentsList(params: EquipReq.CommentsList): Promise<EquipRes.CommentsList> {
  const list = await QnmEquipComment.listCommentsText(params.equipId)
  const data: EquipRes.CommentsList = {
    list,
  }
  return data
}

/** 获取兵器谱排行筛选数据 */
export async function equipWeaponsFilters(): Promise<EquipRes.WeaponsFilters> {
  const servers = await getServerFilters()
  const data: EquipRes.WeaponsFilters = {
    servers,
    equipPositions: EquipPositions,
  }

  return data
}

const COLS_FOR_LIST = [
  "rank_pos as Rank",
  "ownner_id as RoleId",
  "equip_gk as EquipId",
  "equip_name as EquipName",
  "score as Score",
  "equip_type as PositionId",
]

export enum EPreciousRank {
  /** 武器榜 */
  Weapon = 0,
  /** 奇珍榜 */
  Precious = 1,
}

interface EquipRankCommon extends Pagination {
  server_id: number
  equip_type?: number
  precious_rank: EPreciousRank
}

async function getLatestRankDs(): Promise<string> {
  const query = QnmEquipRank.scope().orderBy("ds", "desc").limit(1).select(["ds"])
  const rows: { ds: string }[] = await QnmEquipRank.executeByQuery(query)
  if (rows && rows.length > 0) {
    return rows[0].ds
  } else {
    return ""
  }
}

export async function getEquipRankListFromDB(params: EquipRankCommon): Promise<EquipRes.WeaponsRank> {
  const serverId = params.server_id
  const equip_type = params.equip_type
  const ds = await getLatestRankDs()
  const condition = { server: serverId, precious_rank: params.precious_rank, ds }
  if (equip_type > 0) {
    condition["equip_type"] = equip_type
  }
  const COLS_FOR_LIST = [
    "rank_pos as Rank",
    "ownner_id as RoleId",
    "equip_gk as EquipId",
    "equip_name as EquipName",
    "server as Server",
    "score as Score",
    "equip_type as PositionId",
    "tag_name as TagName",
  ]
  const query = QnmEquipRank.scope().where(condition).select(COLS_FOR_LIST).orderBy("rank_pos", "asc")
  const totalCount = await QnmEquipRank.count(condition)
  const totalPage = Math.ceil(totalCount / params.pageSize)
  const ranks = await QnmEquipRank.queryWithPagination(query, params)
  const list: EquipRankItem[] = await fillEquipRankInfo(ranks)
  const updateStamp = Date.now()
  if (params.precious_rank === EPreciousRank.Precious) {
    for (const e of list) {
      e.RankName = findTreasureRankName(e.PositionId)
      e.RoleName = e.RoleName || ""
    }
  }
  const updateTime = await getUpdateTime(updateStamp)
  return {
    list: list,
    updateTime: updateTime,
    pagination: {
      cur: params.page,
      total: totalPage,
    },
  }
}

export const getEquipRankList = smartMemorize(getEquipRankListFromDB, {
  keyGen: function (params) {
    return cacheKeyGen("qnm:equip_rank_list", {
      server_id: params.server_id,
      equip_type: params.equip_type,
      page: params.page,
      pageSize: params.pageSize,
      precious_rank: params.precious_rank,
    })
  },
  expireSeconds: 60,
})

/** 获取兵器谱排行 */
export async function equipWeaponsRank(params: EquipReq.WeaponsRank): Promise<EquipRes.WeaponsRank> {
  return getEquipRankList({
    server_id: params.server_id,
    precious_rank: EPreciousRank.Weapon,
    page: params.page,
    pageSize: params.page_size,
    equip_type: params.equip_position,
  })
}

/** 获取奇珍榜排行 */
export async function equipTreasuresRank(params: EquipReq.TreasuresRank): Promise<EquipRes.TreasuresRank> {
  return getEquipRankList({
    server_id: params.server_id,
    precious_rank: EPreciousRank.Precious,
    page: params.page,
    pageSize: params.page_size,
  })
}