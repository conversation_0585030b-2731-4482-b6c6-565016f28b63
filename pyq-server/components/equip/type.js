"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = void 0;
exports.ReqSchemas = {
    EquipCommentsCreate: {
        equipId: { type: String },
        text: { type: String },
    },
    EquipCommentsList: {
        equipId: { type: String },
    },
    EquipWeaponsFilters: {},
    EquipWeaponsRank: {
        server_id: { type: Number },
        equip_position: { type: Number },
        page: { type: Number, default: 1 },
        page_size: { type: "integer", min: 1, max: 20 },
    },
    EquipTreasuresRank: {
        server_id: { type: Number },
        page: { type: Number, default: 1 },
        page_size: { type: "integer", min: 1, max: 20 },
    },
};
//# sourceMappingURL=type.js.map