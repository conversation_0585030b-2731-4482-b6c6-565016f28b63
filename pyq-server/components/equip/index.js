"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EquipComponent = exports.paths = void 0;
const operation_1 = require("./operation");
const type_1 = require("./type");
const betterJsonp_1 = require("../../../common/betterJsonp");
const ipAuth_1 = require("../../middlewares/ipAuth");
exports.paths = [
    {
        method: "post",
        url: "/equip/comments/create",
        before: [(0, ipAuth_1.injectIpHelper)()],
        paramsSchema: type_1.ReqSchemas.EquipCommentsCreate,
        operation: operation_1.equipCommentsCreate,
        option: { skipAuth: true },
    },
    {
        method: "post",
        url: "/equip/comments/list",
        paramsSchema: type_1.ReqSchemas.EquipCommentsList,
        operation: operation_1.equipCommentsList,
        option: { skipAuth: true },
    },
    {
        method: "get",
        url: "/equip/weapons/filters",
        before: [(0, betterJsonp_1.jsonp)()],
        paramsSchema: type_1.ReqSchemas.EquipWeaponsFilters,
        operation: operation_1.equipWeaponsFilters,
    },
    {
        method: "get",
        url: "/equip/weapons/rank",
        before: [(0, betterJsonp_1.jsonp)()],
        paramsSchema: type_1.ReqSchemas.EquipWeaponsRank,
        operation: operation_1.equipWeaponsRank,
    },
    {
        method: "get",
        url: "/equip/treasures/rank",
        paramsSchema: type_1.ReqSchemas.EquipTreasuresRank,
        before: [(0, betterJsonp_1.jsonp)()],
        operation: operation_1.equipTreasuresRank,
    },
];
exports.EquipComponent = {
    paths: exports.paths,
    prefix: "/equip/",
};
//# sourceMappingURL=index.js.map