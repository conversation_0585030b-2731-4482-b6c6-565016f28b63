import { getJsonInfo } from "../../../common/util";
import { getProfileRecord } from "../../../service/qnm/pyq/profile";
import { Features } from "../../common/config";
import { ExpressionBaseCols, ExpressionBaseDefault } from "../../constants";
import { errorCodes } from "../../errorCodes";
import { clazzLogger } from "../../logger";
import { ProfileModel } from "../../models";
import { PlayerCleanService } from "../../services/player";
import { ExpressionBase } from "../../types/type";
import { getRelation } from "../slientLove/service";
import { PlayerReq, PlayerRes } from "./type";
const logger = clazzLogger("player/operation");

/** 获取玩家信息详情 */
export async function getprofile(params: PlayerReq.Getprofile): Promise<PlayerRes.Getprofile> {
  const queryRoleId = params.targetid || params.roleid;
  const data: PlayerRes.Getprofile = await getProfileRecord(queryRoleId, params);

  if (Features.slientLove) {
    const slientLove = await getRelation(params.roleid, queryRoleId);
    data.slientLove = slientLove;
  }
  return data;
}

/**
 * 清空玩家所有梦岛信息
 * 需要删除的内容为：我的梦岛，心愿，留言板，曾用名
 */
export async function cleanAccount(params: PlayerReq.CleanAccount): Promise<PlayerRes.CleanAccount> {
  const { roleid } = params;
  const ret = await Promise.all([
    PlayerCleanService.cleanMoment(roleid),
    PlayerCleanService.cleanWishList(roleid),
    PlayerCleanService.cleanMsgList(roleid),
    PlayerCleanService.cleanUsedName(roleid),
  ]);
  logger.info({ ret, params }, "cleanAccountRet");
  return { isOk: true };
}

/** 更新玩家的头像框 */
export async function updateExpressionBase(
  params: PlayerReq.UpdateExpressionBase
): Promise<PlayerRes.UpdateExpressionBase> {
  const { roleid } = params;
  const curProfile = await ProfileModel.findOne({ RoleId: roleid }, ["RoleId", "ExpressionBase"]);
  if (!curProfile) {
    throw errorCodes.ProfileNotFound;
  }
  const curExpressionBase: ExpressionBase = curProfile.ExpressionBase
    ? getJsonInfo(curProfile.ExpressionBase, {})
    : ExpressionBaseDefault;
  const updateProps: ExpressionBase = {};
  for (const key of ExpressionBaseCols) {
    if (params[key] !== undefined) {
      updateProps[key] = params[key];
    } else {
      updateProps[key] = curExpressionBase[key];
    }
  }
  const expressionBaseStr = JSON.stringify(updateProps);
  const ret = await ProfileModel.updateByCondition({ RoleId: roleid }, { ExpressionBase: expressionBaseStr });
  logger.info({ ret, roleid, expressionBaseStr }, "UpdateExpressionBaseOK");
  return { isOk: true };
}
