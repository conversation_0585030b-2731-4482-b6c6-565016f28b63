import { validateS<PERSON>ure<PERSON><PERSON> } from "../../controllers/auth";
import { Path } from "../../types/type";
import { cleanAccount, getprofile, updateExpressionBase } from "./operation";
import { ReqSchemas } from "./type";

export const paths: Path[] = [
  {
    method: "post",
    url: "/getprofile",
    paramsSchema: ReqSchemas.Getprofile,
    operation: getprofile,
  },
  {
    method: "post",
    url: "/player/expression_base/update",
    paramsSchema: ReqSchemas.UpdateExpressionBase,
    operation: updateExpressionBase,
  },
  {
    method: "post",
    url: "/clean_account",
    before: validateSecure<PERSON>pi,
    paramsSchema: ReqSchemas.CleanAccount,
    operation: cleanAccount,
    option: { skipAuth: true }
  },
];

export const PlayerComponent = {
  paths: paths,
  prefix: "/",
};