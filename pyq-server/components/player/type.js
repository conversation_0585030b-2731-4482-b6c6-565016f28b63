"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = void 0;
exports.ReqSchemas = {
    Getprofile: {
        roleid: { type: Number },
        targetid: { type: Number },
    },
    UpdateExpressionBase: {
        roleid: { type: Number },
        expression: { type: Number, required: false },
        expression_text: { type: Number, required: false },
        sticker: { type: Number, required: false },
        frame: { type: Number, required: false }
    },
    CleanAccount: {
        time: { type: Number },
        urs: { type: String },
        account: { type: String },
        roleid: { type: Number },
        token: { type: String }
    },
};
//# sourceMappingURL=type.js.map