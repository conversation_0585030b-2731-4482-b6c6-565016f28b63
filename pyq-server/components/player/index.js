"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlayerComponent = exports.paths = void 0;
const auth_1 = require("../../controllers/auth");
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "post",
        url: "/getprofile",
        paramsSchema: type_1.ReqSchemas.Getprofile,
        operation: operation_1.getprofile,
    },
    {
        method: "post",
        url: "/player/expression_base/update",
        paramsSchema: type_1.ReqSchemas.UpdateExpressionBase,
        operation: operation_1.updateExpressionBase,
    },
    {
        method: "post",
        url: "/clean_account",
        before: auth_1.validateSecureApi,
        paramsSchema: type_1.ReqSchemas.CleanAccount,
        operation: operation_1.cleanAccount,
        option: { skipAuth: true }
    },
];
exports.PlayerComponent = {
    paths: exports.paths,
    prefix: "/",
};
//# sourceMappingURL=index.js.map