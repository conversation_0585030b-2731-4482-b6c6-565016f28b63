"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateExpressionBase = exports.cleanAccount = exports.getprofile = void 0;
const util_1 = require("../../../common/util");
const profile_1 = require("../../../service/qnm/pyq/profile");
const config_1 = require("../../common/config");
const constants_1 = require("../../constants");
const errorCodes_1 = require("../../errorCodes");
const logger_1 = require("../../logger");
const models_1 = require("../../models");
const player_1 = require("../../services/player");
const service_1 = require("../slientLove/service");
const logger = (0, logger_1.clazzLogger)("player/operation");
/** 获取玩家信息详情 */
function getprofile(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const queryRoleId = params.targetid || params.roleid;
        const data = yield (0, profile_1.getProfileRecord)(queryRoleId, params);
        if (config_1.Features.slientLove) {
            const slientLove = yield (0, service_1.getRelation)(params.roleid, queryRoleId);
            data.slientLove = slientLove;
        }
        return data;
    });
}
exports.getprofile = getprofile;
/**
 * 清空玩家所有梦岛信息
 * 需要删除的内容为：我的梦岛，心愿，留言板，曾用名
 */
function cleanAccount(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const { roleid } = params;
        const ret = yield Promise.all([
            player_1.PlayerCleanService.cleanMoment(roleid),
            player_1.PlayerCleanService.cleanWishList(roleid),
            player_1.PlayerCleanService.cleanMsgList(roleid),
            player_1.PlayerCleanService.cleanUsedName(roleid),
        ]);
        logger.info({ ret, params }, "cleanAccountRet");
        return { isOk: true };
    });
}
exports.cleanAccount = cleanAccount;
/** 更新玩家的头像框 */
function updateExpressionBase(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const { roleid } = params;
        const curProfile = yield models_1.ProfileModel.findOne({ RoleId: roleid }, ["RoleId", "ExpressionBase"]);
        if (!curProfile) {
            throw errorCodes_1.errorCodes.ProfileNotFound;
        }
        const curExpressionBase = curProfile.ExpressionBase
            ? (0, util_1.getJsonInfo)(curProfile.ExpressionBase, {})
            : constants_1.ExpressionBaseDefault;
        const updateProps = {};
        for (const key of constants_1.ExpressionBaseCols) {
            if (params[key] !== undefined) {
                updateProps[key] = params[key];
            }
            else {
                updateProps[key] = curExpressionBase[key];
            }
        }
        const expressionBaseStr = JSON.stringify(updateProps);
        const ret = yield models_1.ProfileModel.updateByCondition({ RoleId: roleid }, { ExpressionBase: expressionBaseStr });
        logger.info({ ret, roleid, expressionBaseStr }, "UpdateExpressionBaseOK");
        return { isOk: true };
    });
}
exports.updateExpressionBase = updateExpressionBase;
//# sourceMappingURL=operation.js.map