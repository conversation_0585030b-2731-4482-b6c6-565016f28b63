import { operations } from "../../types/swagger";

export namespace PlayerReq {
  export type Getprofile = operations["getprofile"]["parameters"]["query"];
  export type CleanAccount = operations["cleanAccount"]["parameters"]["query"];
  export type UpdateExpressionBase = operations["playerExpressionBaseUpdate"]["parameters"]["query"] & operations["playerExpressionBaseUpdate"]["requestBody"];
}

export namespace PlayerRes {
  export type Getprofile = operations["getprofile"]["responses"]["200"]["content"]["application/json"]["data"];
  export type CleanAccount = operations["cleanAccount"]["responses"]["200"]["content"]["application/json"]["data"];

  export type UpdateExpressionBase = operations["playerExpressionBaseUpdate"]["responses"]["200"]["content"]["application/json"]["data"]
}

export const ReqSchemas = {
  Getprofile: {
    roleid: { type: Number },
    targetid: { type: Number },
  },

  UpdateExpressionBase: {
    roleid: { type: Number },
    expression: { type: Number, required: false },
    expression_text: { type: Number, required: false },
    sticker: { type: Number, required: false },
    frame: { type: Number, required: false }
  },

  CleanAccount: {
    time: { type: Number },
    urs: { type: String },
    account: { type: String },
    roleid: { type: Number },
    token: { type: String }
  },
};
