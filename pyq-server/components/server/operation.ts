import { downMomentByRoleId } from "../../../service/qnm/pyq/HotMomentsCache";
import { RoleMinorModel } from "../../models";
import { ServerReq, ServerRes } from "./type";
import { clazzLogger } from "../../logger";
import { checkParamsByAjv } from "../../common/ajvCheck";
const logger = clazzLogger("component.server");
import { fpReviewStatusToAuditStatues } from "../../services/fp";
import { TransferModel } from "../../models/transfer";
import { BussError, errorCodes } from "../../errorCodes";
import { addMigrateJob } from "../../services/transfer";
import { FPSendPicModel, FpSendPicRecord } from "../../models/fpSendPic";
import { tryToReturnPicWhenAuditFinish } from "../../services/fpReview";
import { fpCfg } from "../../common/config";

/** 梦岛设置好友可见 */
export async function serverPlayerSetProtectMode(params: ServerReq.PlayerSetProtectMode): Promise<unknown> {
  const { roleid, protectMode } = params;

  const originRecord = await RoleMinorModel.findOne({ roleId: roleid });
  const now = Date.now();
  const isOk = true;

  if (originRecord) {
    if (protectMode == 0 && originRecord.protectMode == 1) {
      await RoleMinorModel.updateByCondition({ roleId: roleid }, { protectMode: 0, updateTime: now });
    }

    if (protectMode == 1 && originRecord.protectMode == 0) {
      await RoleMinorModel.updateByCondition({ roleId: roleid }, { protectMode: 1, updateTime: now });
    }
  } else {
    await RoleMinorModel.insert({ roleId: roleid, protectMode: protectMode, createTime: now, updateTime: now });
  }

  if (protectMode == 1) {
    await downMomentByRoleId(roleid);
  }

  return {
    isOk: isOk,
  };
}

export async function serverTransferAdd(params: ServerReq.ServerTransferAdd): Promise<ServerRes.ServerTransferAdd> {
  const schema = {
    type: "object",
    properties: {
      oldId: { type: "integer" },
      newId: { type: "integer" },
    },
  };
  checkParamsByAjv(params, schema);
  logger.info({ params }, "serverTransferAdd");
  const record = await TransferModel.findOne({
    oldId: params.oldId,
    newId: params.newId,
  });
  if (record) {
    return BussError(errorCodes.AlreadyTransfer);
  }
  const originRecord = await TransferModel.findOne({
    newId: params.oldId,
  });
  const originId = originRecord?.originId || params.oldId;
  const props = {
    oldId: params.oldId,
    newId: params.newId,
    originId,
    createTime: Date.now(),
  };
  const id = await TransferModel.insert(props);
  await addMigrateJob(params.oldId, params.newId);
  return { taskId: id.insertId };
}

export function checkFpFileReviewCallbackAuth(req, res, next) {
  // get token from header
  const token = req.headers["x-fp-token"];
  if (!token) {
    res.send(401, { error: "No token" });
    return;
  }
  if (token != fpCfg.xFpToken) {
    res.send(401, { error: "Invalid token" });
    return;
  }
  return next();
}

export async function fpFileReviewCallback(
  params: ServerReq.FpFileReviewCallback
): Promise<ServerRes.FpFileReviewCallback> {
  const project = params.project_name;
  for (const f of params.files) {
    const fileId = f.file_id;
    const auditStatus = fpReviewStatusToAuditStatues(f.to_status);
    const r = await FPSendPicModel.findOne({ Project: project, FileId: fileId });
    const now = Date.now();
    if (!r) {
      const props: Omit<FpSendPicRecord, "ID"> = {
        Project: project,
        FileId: fileId,
        Url: "",
        RoleId: 0,
        PicId: "",
        Media: 0,
        CreateTime: now,
        AuditStatus: auditStatus,
        AuditFinishTime: now,
      };
      const id = await FPSendPicModel.insert(props);
      logger.info({ id, props }, "fpFileReviewCallbackSave");
    } else {
      const upRet = await FPSendPicModel.updateByCondition(
        { Project: project, FileId: fileId },
        { AuditStatus: auditStatus, AuditFinishTime: now }
      );
      logger.info({ upRet, r }, "fpFileReviewCallbackUpdateStatus");
      await tryToReturnPicWhenAuditFinish([{ project: project, fileId: fileId }]);
    }
  }

  return { isOk: true };
}
