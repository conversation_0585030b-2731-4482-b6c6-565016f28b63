"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authCheckByToken = exports.checkProtectToken = void 0;
const config_1 = require("../../../common/config");
const constants_1 = require("../../../common/constants");
const util_1 = require("../../../common/util");
const ajvCheck_1 = require("../../common/ajvCheck");
const config_2 = require("../../common/config");
const helper_1 = require("../../helper");
const logger_1 = require("../../logger");
const _ = require("lodash");
function checkProtectToken(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const schema = {
                type: "object",
                properties: {
                    roleid: { type: "number" },
                    time: { type: "number" },
                    protectMode: { type: "number" },
                    token: { type: "string" },
                },
                required: ["roleid", "time", "protectMode", "token"],
            };
            (0, ajvCheck_1.checkParamsByAjv)(req.params, schema);
            const params = req.params;
            const signStr = `${params.roleid}${params.protectMode}${params.time}${config_1.AUTH_TOKEN_SALT}`;
            const token = (0, util_1.hexMd5)(signStr);
            if (token !== params.token && !config_2.testCfg.skip_token_check) {
                logger_1.logger.info({ expectToken: token, actualToken: token, signStr }, "checkProtectToken err");
                throw { code: -1, msg: "token不正确", errorType: "token err" };
            }
            next();
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.checkProtectToken = checkProtectToken;
function authCheckByToken(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        const schema = {
            type: "object",
            properties: {
                time: { type: "integer" },
                nonce: { type: "string", minLength: 6, maxLength: 6 },
                token: { type: "string", minLength: 1 },
            },
        };
        try {
            const params = req.params;
            yield (0, ajvCheck_1.checkParamsByAjv)(req.params, schema);
            const keys = Object.keys(_.omit(params, ["token"])).sort();
            const values = keys.map((k) => params[k]);
            const signStr = [...values, config_1.AUTH_TOKEN_SALT].join("");
            const expectToken = (0, util_1.hexMd5)(signStr);
            if (expectToken === params.token) {
                next();
            }
            else {
                logger_1.logger.warn("CheckTokenFailed", {
                    url: req.url,
                    params: params,
                    sortKeys: keys,
                    actualToken: params.token,
                    expectToken: expectToken,
                    signStr: signStr,
                    skipToken: config_2.testCfg.skip_token,
                });
                if (config_2.testCfg.skip_token) {
                    logger_1.logger.warn("SkipTokenCheck");
                    next();
                }
                else {
                    res.send({ code: constants_1.HTTP_STATUS_CODES.UNAUTHORIZED, message: "Token invalid" });
                }
            }
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    });
}
exports.authCheckByToken = authCheckByToken;
//# sourceMappingURL=middleware.js.map