import { operations } from "../../types/swagger";

export namespace ServerReq {
  export type PlayerSetProtectMode = operations["serverPlayerSetProtectMode"]["parameters"]["query"];
  export type ServerTransferAdd = operations["serverTransferAdd"]["parameters"]["query"];
  export type FpFileReviewCallback =
    operations["serverFpFileReviewCallback"]["requestBody"]["content"]["application/json"];
}

export namespace ServerRes {
  export type ServerTransferAdd =
    operations["serverTransferAdd"]["responses"]["200"]["content"]["application/json"]["data"];
  export type FpFileReviewCallback =
    operations["serverFpFileReviewCallback"]["responses"]["200"]["content"]["application/json"]["data"];
}

export const ReqSchemas = {
  ServerPlayerSetProtectMode: {
    roleid: { type: Number },
    time: { type: Number },
    protectMode: { type: Number, values: [0, 1] },
    token: { type: String },
  },

  ServerTransferAdd: {
    type: "object",
    properties: {
      oldId: { type: "integer" },
      newId: { type: "integer" },
      time: { type: "integer" },
      nonce: { type: "string", minLength: 6, maxLength: 6 },
      token: { type: "string", minLength: 1 },
    },
    required: ["oldId", "newId", "time", "nonce", "token"],
  },

  ServerFpFileReviewCallback: {
    project_name: { type: "string" },
    operator: { type: "string" },
    files: {
      type: "array",
      items: {
        type: "object",
        properties: {
          file_id: { type: "string" },
          from_status: { type: "integer" },
          to_status: { type: "integer" },
          extra_info: {
            type: "object",
            properties: {
              review_callback_url: { type: "string" },
              pic_id: { type: "string" },
              media: { type: "string" },
              role_id: { type: "string" },
            },
          },
        },
        required: ["file_id", "from_status", "to_status", "extra_info"],
      },
    },
  },
};
