"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServerComponent = exports.paths = void 0;
const moment_1 = require("../../services/moment");
const middleware_1 = require("./middleware");
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "post",
        url: "/server/player/setProtectMode",
        paramsSchema: type_1.ReqSchemas.ServerPlayerSetProtectMode,
        before: middleware_1.checkProtectToken,
        operation: operation_1.serverPlayerSetProtectMode,
        option: { skipAuth: true },
    },
    {
        method: "post",
        url: "/server/transfer/add",
        before: middleware_1.authCheckByToken,
        operation: operation_1.serverTransferAdd,
        option: { skipAuth: true },
    },
    {
        method: "post",
        url: "/server/fpFileReviewCallback",
        paramsSchema: type_1.ReqSchemas.ServerFpFileReviewCallback,
        before: operation_1.checkFpFileReviewCallbackAuth,
        operation: operation_1.fpFileReviewCallback,
        option: { skipAuth: true },
    },
    {
        method: "post",
        url: "/server/moment_lottery/add",
        option: { skipAuth: true },
        handler: moment_1.serverMomentLotteryAddHandler,
    },
];
exports.ServerComponent = {
    paths: exports.paths,
    prefix: "/server/",
};
//# sourceMappingURL=index.js.map