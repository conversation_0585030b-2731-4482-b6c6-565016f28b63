import { serverMomentLotteryAddHandler } from "../../services/moment";
import { Path } from "../../types/type";
import { authCheckByToken, checkProtectToken } from "./middleware";
import {
  checkFpFileReviewCallbackAuth,
  fpFileReviewCallback,
  serverPlayerSetProtectMode,
  serverTransferAdd,
} from "./operation";
import { ReqSchemas } from "./type";

export const paths: Path[] = [
  {
    method: "post",
    url: "/server/player/setProtectMode",
    paramsSchema: ReqSchemas.ServerPlayerSetProtectMode,
    before: checkProtectToken,
    operation: serverPlayerSetProtectMode,
    option: { skipAuth: true },
  },
  {
    method: "post",
    url: "/server/transfer/add",
    before: authCheckByToken,
    operation: serverTransferAdd,
    option: { skipAuth: true },
  },
  {
    method: "post",
    url: "/server/fpFileReviewCallback",
    paramsSchema: ReqSchemas.ServerFpFileReviewCallback,
    before: checkFpFileReviewCallbackAuth,
    operation: fpFileReviewCallback,
    option: { skipAuth: true },
  },
  {
    method: "post",
    url: "/server/moment_lottery/add",
    option: { skipAuth: true },
    handler: serverMomentLotteryAddHandler,
  },
];

export const ServerComponent = {
  paths: paths,
  prefix: "/server/",
};
