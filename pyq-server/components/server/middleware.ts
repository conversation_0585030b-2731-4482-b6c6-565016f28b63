import { AUTH_TOKEN_SALT } from "../../../common/config";
import { HTTP_STATUS_CODES } from "../../../common/constants";
import { hexMd5 } from "../../../common/util";
import { checkParamsByAjv } from "../../common/ajvCheck";
import { testCfg } from "../../common/config";
import { errorHandler } from "../../helper";
import { logger } from "../../logger";
import * as _ from "lodash";

export async function checkProtectToken(req, res, next) {
  try {
    const schema = {
      type: "object",
      properties: {
        roleid: { type: "number" },
        time: { type: "number" },
        protectMode: { type: "number" },
        token: { type: "string" },
      },
      required: ["roleid", "time", "protectMode", "token"],
    };
    checkParamsByAjv(req.params, schema);
    const params = req.params;
    const signStr = `${params.roleid}${params.protectMode}${params.time}${AUTH_TOKEN_SALT}`;
    const token = hexMd5(signStr);
    if (token !== params.token && !testCfg.skip_token_check) {
      logger.info({ expectToken: token, actualToken: token, signStr }, "checkProtectToken err");
      throw { code: -1, msg: "token不正确", errorType: "token err" };
    }
    next();
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}

export async function authCheckByToken(req, res, next) {
  const schema = {
    type: "object",
    properties: {
      time: { type: "integer" },
      nonce: { type: "string", minLength: 6, maxLength: 6 },
      token: { type: "string", minLength: 1 },
    },
  };
  try {
    const params = req.params;
    await checkParamsByAjv(req.params, schema);
    const keys = Object.keys(_.omit(params, ["token"])).sort();
    const values = keys.map((k) => params[k]);
    const signStr = [...values, AUTH_TOKEN_SALT].join("");
    const expectToken = hexMd5(signStr);
    if (expectToken === params.token) {
      next();
    } else {
      logger.warn("CheckTokenFailed", {
        url: req.url,
        params: params,
        sortKeys: keys,
        actualToken: params.token,
        expectToken: expectToken,
        signStr: signStr,
        skipToken: testCfg.skip_token,
      });
      if (testCfg.skip_token) {
        logger.warn("SkipTokenCheck");
        next();
      } else {
        res.send({ code: HTTP_STATUS_CODES.UNAUTHORIZED, message: "Token invalid" });
      }
    }
  } catch (err) {
    errorHandler(err, req, res, next);
  }
}
