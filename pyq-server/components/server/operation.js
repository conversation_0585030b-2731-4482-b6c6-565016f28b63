"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.fpFileReviewCallback = exports.checkFpFileReviewCallbackAuth = exports.serverTransferAdd = exports.serverPlayerSetProtectMode = void 0;
const HotMomentsCache_1 = require("../../../service/qnm/pyq/HotMomentsCache");
const models_1 = require("../../models");
const logger_1 = require("../../logger");
const ajvCheck_1 = require("../../common/ajvCheck");
const logger = (0, logger_1.clazzLogger)("component.server");
const fp_1 = require("../../services/fp");
const transfer_1 = require("../../models/transfer");
const errorCodes_1 = require("../../errorCodes");
const transfer_2 = require("../../services/transfer");
const fpSendPic_1 = require("../../models/fpSendPic");
const fpReview_1 = require("../../services/fpReview");
const config_1 = require("../../common/config");
/** 梦岛设置好友可见 */
function serverPlayerSetProtectMode(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const { roleid, protectMode } = params;
        const originRecord = yield models_1.RoleMinorModel.findOne({ roleId: roleid });
        const now = Date.now();
        const isOk = true;
        if (originRecord) {
            if (protectMode == 0 && originRecord.protectMode == 1) {
                yield models_1.RoleMinorModel.updateByCondition({ roleId: roleid }, { protectMode: 0, updateTime: now });
            }
            if (protectMode == 1 && originRecord.protectMode == 0) {
                yield models_1.RoleMinorModel.updateByCondition({ roleId: roleid }, { protectMode: 1, updateTime: now });
            }
        }
        else {
            yield models_1.RoleMinorModel.insert({ roleId: roleid, protectMode: protectMode, createTime: now, updateTime: now });
        }
        if (protectMode == 1) {
            yield (0, HotMomentsCache_1.downMomentByRoleId)(roleid);
        }
        return {
            isOk: isOk,
        };
    });
}
exports.serverPlayerSetProtectMode = serverPlayerSetProtectMode;
function serverTransferAdd(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const schema = {
            type: "object",
            properties: {
                oldId: { type: "integer" },
                newId: { type: "integer" },
            },
        };
        (0, ajvCheck_1.checkParamsByAjv)(params, schema);
        logger.info({ params }, "serverTransferAdd");
        const record = yield transfer_1.TransferModel.findOne({
            oldId: params.oldId,
            newId: params.newId,
        });
        if (record) {
            return (0, errorCodes_1.BussError)(errorCodes_1.errorCodes.AlreadyTransfer);
        }
        const originRecord = yield transfer_1.TransferModel.findOne({
            newId: params.oldId,
        });
        const originId = (originRecord === null || originRecord === void 0 ? void 0 : originRecord.originId) || params.oldId;
        const props = {
            oldId: params.oldId,
            newId: params.newId,
            originId,
            createTime: Date.now(),
        };
        const id = yield transfer_1.TransferModel.insert(props);
        yield (0, transfer_2.addMigrateJob)(params.oldId, params.newId);
        return { taskId: id.insertId };
    });
}
exports.serverTransferAdd = serverTransferAdd;
function checkFpFileReviewCallbackAuth(req, res, next) {
    // get token from header
    const token = req.headers["x-fp-token"];
    if (!token) {
        res.send(401, { error: "No token" });
        return;
    }
    if (token != config_1.fpCfg.xFpToken) {
        res.send(401, { error: "Invalid token" });
        return;
    }
    return next();
}
exports.checkFpFileReviewCallbackAuth = checkFpFileReviewCallbackAuth;
function fpFileReviewCallback(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const project = params.project_name;
        for (const f of params.files) {
            const fileId = f.file_id;
            const auditStatus = (0, fp_1.fpReviewStatusToAuditStatues)(f.to_status);
            const r = yield fpSendPic_1.FPSendPicModel.findOne({ Project: project, FileId: fileId });
            const now = Date.now();
            if (!r) {
                const props = {
                    Project: project,
                    FileId: fileId,
                    Url: "",
                    RoleId: 0,
                    PicId: "",
                    Media: 0,
                    CreateTime: now,
                    AuditStatus: auditStatus,
                    AuditFinishTime: now,
                };
                const id = yield fpSendPic_1.FPSendPicModel.insert(props);
                logger.info({ id, props }, "fpFileReviewCallbackSave");
            }
            else {
                const upRet = yield fpSendPic_1.FPSendPicModel.updateByCondition({ Project: project, FileId: fileId }, { AuditStatus: auditStatus, AuditFinishTime: now });
                logger.info({ upRet, r }, "fpFileReviewCallbackUpdateStatus");
                yield (0, fpReview_1.tryToReturnPicWhenAuditFinish)([{ project: project, fileId: fileId }]);
            }
        }
        return { isOk: true };
    });
}
exports.fpFileReviewCallback = fpFileReviewCallback;
//# sourceMappingURL=operation.js.map