import { uuidV1 } from "../../../common/util2";
import { addComplainLog } from "../../../service/yunyinglog";
import { ComplainReq, ComplainRes } from "./type";

/** 添加举报日志 */
export async function complainAdd(params: ComplainReq.Add): Promise<ComplainRes.Add> {
  const uuid = uuidV1()

  addComplainLog(params.roleid, {
    uuid,
    resource_type: params.resouce_type,
    resource_id: params.resouce_id,
    content: params.content,
    reason: params.reason
  })

  return { id: uuid };
}