import { operations } from "../../types/swagger";

export namespace ComplainReq {
  export type Add = operations["complainAdd"]["parameters"]["query"];
}

export namespace ComplainRes {
  export type Add = operations["complainAdd"]["responses"]["200"]["content"]["application/json"]["data"];
}

export const ReqSchemas = {
  ComplainAdd: {
    roleid: { type: Number },
    skey: { type: String },
    targetid: { type: Number },
    resouce_type: { type: String },
    resouce_id: { type: Number },
    content: { type: String },
    reason: { type: String },
  },
};
