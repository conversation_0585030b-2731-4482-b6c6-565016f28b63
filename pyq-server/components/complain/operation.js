"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.complainAdd = void 0;
const util2_1 = require("../../../common/util2");
const yunyinglog_1 = require("../../../service/yunyinglog");
/** 添加举报日志 */
function complainAdd(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const uuid = (0, util2_1.uuidV1)();
        (0, yunyinglog_1.addComplainLog)(params.roleid, {
            uuid,
            resource_type: params.resouce_type,
            resource_id: params.resouce_id,
            content: params.content,
            reason: params.reason
        });
        return { id: uuid };
    });
}
exports.complainAdd = complainAdd;
//# sourceMappingURL=operation.js.map