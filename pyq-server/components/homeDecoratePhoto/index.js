"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HomeDecoratePhotoComponent = exports.paths = void 0;
const ipAuth_1 = require("../../middlewares/ipAuth");
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "post",
        url: "/home_decorate_photo/update",
        paramsSchema: type_1.ReqSchemas.HomeDecoratePhotoUpdate,
        before: (0, ipAuth_1.injectIpHelper)(),
        operation: operation_1.homeDecoratePhotoUpdate,
    },
    {
        method: "post",
        url: "/home_decorate_photo/remove",
        paramsSchema: type_1.ReqSchemas.HomeDecoratePhotoRemove,
        operation: operation_1.homeDecoratePhotoRemove,
    },
    {
        method: "get",
        url: "/home_decorate_photo/list",
        paramsSchema: type_1.ReqSchemas.HomeDecoratePhotoList,
        operation: operation_1.homeDecoratePhotoList,
    },
    {
        method: "get",
        url: "/home_decorate_photo/get_by_ids",
        paramsSchema: type_1.ReqSchemas.HomeDecoratePhotoGetByIds,
        operation: operation_1.homeDecoratePhotoGetByIds,
    },
];
exports.HomeDecoratePhotoComponent = {
    paths: exports.paths,
    prefix: "/home_decorate_photo/",
};
//# sourceMappingURL=index.js.map