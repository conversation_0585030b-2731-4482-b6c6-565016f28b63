import { operations } from "../../types/swagger";

export namespace HomeDecoratePhotoReq {
  export type Update = operations["homeDecoratePhotoUpdate"]["parameters"]["query"];
  export type Remove = operations["homeDecoratePhotoRemove"]["parameters"]["query"];
  export type List = operations["homeDecoratePhotoList"]["parameters"]["query"];
  export type GetByIds = operations["homeDecoratePhotoGetByIds"]["parameters"]["query"];
}

export namespace HomeDecoratePhotoRes {
  export type Update = operations["homeDecoratePhotoUpdate"]["responses"]["200"]["content"]["application/json"]["data"];
  export type Remove = operations["homeDecoratePhotoRemove"]["responses"]["200"]["content"]["application/json"]["data"];
  export type List = operations["homeDecoratePhotoList"]["responses"]["200"]["content"]["application/json"]["data"];
  export type GetByIds =
    operations["homeDecoratePhotoGetByIds"]["responses"]["200"]["content"]["application/json"]["data"];
}

export const ReqSchemas = {
  HomeDecoratePhotoUpdate: {
    type: "object",
    properties: {
      roleid: { type: "number" },
      url: { type: "string" },
      index: { type: "integer", minimum: 0, maximum: 9 },
    },
    required: ["roleid", "url", "index"],
  },
  HomeDecoratePhotoRemove: {
    type: "object",
    properties: { roleid: { type: "number" }, index: { type: "integer", minimum: 0, maximum: 9 } },
    required: ["roleid", "index"],
  },
  HomeDecoratePhotoList: {
    type: "object",
    properties: { roleid: { type: "number" }, targetid: { type: "number" } },
    required: ["roleid", "targetid"],
  },
  HomeDecoratePhotoGetByIds: {
    type: "object",
    properties: { roleid: { type: "number" }, ids: { type: "string" } },
    required: ["roleid", "ids"],
  },
};
