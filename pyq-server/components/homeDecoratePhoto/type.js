"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = void 0;
exports.ReqSchemas = {
    HomeDecoratePhotoUpdate: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            url: { type: "string" },
            index: { type: "integer", minimum: 0, maximum: 9 },
        },
        required: ["roleid", "url", "index"],
    },
    HomeDecoratePhotoRemove: {
        type: "object",
        properties: { roleid: { type: "number" }, index: { type: "integer", minimum: 0, maximum: 9 } },
        required: ["roleid", "index"],
    },
    HomeDecoratePhotoList: {
        type: "object",
        properties: { roleid: { type: "number" }, targetid: { type: "number" } },
        required: ["roleid", "targetid"],
    },
    HomeDecoratePhotoGetByIds: {
        type: "object",
        properties: { roleid: { type: "number" }, ids: { type: "string" } },
        required: ["roleid", "ids"],
    },
};
//# sourceMappingURL=type.js.map