import { AuditStatues } from "../../../../common/constants"
import { BaseModelClass } from "../../../../models/baseModel2"

export interface HomeDecoratePhotoRecord {
  id: number
  roleId: number
  index: number
  url: string
  auditStatus: AuditStatues
  createTime: number
}

class HomeDecoratePhotoModelClass extends BaseModelClass<HomeDecoratePhotoRecord> {
  constructor() {
    super("pyq_home_decorate_photo")
  }
}

export const HomeDecoratePhotoModel = new HomeDecoratePhotoModelClass()