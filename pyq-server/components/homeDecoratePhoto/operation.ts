import { genPicIdFromInfo } from "../../../common/audit";
import { PicMediaType } from "../../../common/auditType";
import { AuditStatues } from "../../../common/constants";
import { csvStrToIntArray } from "../../../common/util";
import { homeDecoratePhotoCfg } from "../../common/config";
import { ErrorTypes } from "../../constants";
import { BusinessError } from "../../helper";
import { clazzLogger } from "../../logger";
import { sendL10Pic } from "../../services/audit";
import { HomeDecoratePhotoModel, HomeDecoratePhotoRecord } from "./models/photo";
import { HomeDecoratePhotoReq, HomeDecoratePhotoRes } from "./type";
const logger = clazzLogger("homeDecoratePhoto/operation");

/** 更新指定位置的家园自定义图片 */
export async function homeDecoratePhotoUpdate(
  params: HomeDecoratePhotoReq.Update & { ip: string }
): Promise<HomeDecoratePhotoRes.Update> {
  let photoId = 0;
  const condition = { roleId: params.roleid, index: params.index };
  const record = await HomeDecoratePhotoModel.findOne(condition, ["id"]);
  if (record) {
    await HomeDecoratePhotoModel.updateByCondition(
      { id: record.id },
      { url: params.url, index: params.index, auditStatus: AuditStatues.Auditing, createTime: Date.now() }
    );
    photoId = record.id;
  } else {
    const props: Omit<HomeDecoratePhotoRecord, "id"> = {
      roleId: params.roleid,
      url: params.url,
      index: params.index,
      auditStatus: AuditStatues.Auditing,
      createTime: Date.now(),
    };

    photoId = await HomeDecoratePhotoModel.insert(props);
  }
  auditHomeDecoratePhoto(params.roleid, [params.url], photoId, params.ip);
  return { id: photoId };
}

/** 删除指定位置的图片 */
export async function homeDecoratePhotoRemove(
  params: HomeDecoratePhotoReq.Remove
): Promise<HomeDecoratePhotoRes.Remove> {
  const record = await HomeDecoratePhotoModel.findOne({ roleId: params.roleid, index: params.index }, ["id"]);
  if (record && record.id) {
    await HomeDecoratePhotoModel.deleteByCondition({ id: record.id });
    return { id: record.id };
  } else {
    return BusinessError(ErrorTypes.DataNotFound, "家园装饰图片未找到");
  }
}

/** 玩家家园自定义图片列表(10张) */
export async function homeDecoratePhotoList(params: HomeDecoratePhotoReq.List): Promise<HomeDecoratePhotoRes.List> {
  const queryRoleId = params.targetid || params.roleid;
  const data = await HomeDecoratePhotoModel.powerListQuery({
    where: { roleId: queryRoleId },
    orderBy: [["index"], ["asc"]],
    pagination: { page: 1, pageSize: homeDecoratePhotoCfg.maxSize },
  });
  const list = formatToPhotoList(data.list);
  return { list: list, count: data.meta.totalCount };
}

/** 通过图片id列表获取图片 */
export async function homeDecoratePhotoGetByIds(
  params: HomeDecoratePhotoReq.GetByIds
): Promise<HomeDecoratePhotoRes.GetByIds> {
  const idList = csvStrToIntArray(params.ids);
  const rows = await HomeDecoratePhotoModel.find({ id: idList });
  const list = formatToPhotoList(rows);
  const count = list.length;
  return { list, count };
}

function formatToPhotoList(rows: HomeDecoratePhotoRecord[]) {
  return rows.map((r) => {
    const item: HomeDecoratePhotoRes.List["list"][0] = {
      id: r.id,
      roleId: r.roleId,
      index: r.index,
      url: r.url,
      auditStatus: r.auditStatus,
    };
    return item;
  });
}

export function auditHomeDecoratePhoto(roleId: number, imgList: string[], id: number, ip: string) {
  logger.info({ roleId, imgList, id, ip }, "PrepareAuditHomeDecoratePhoto");
  return sendL10Pic(imgList, {
    // 提交图片审核
    roleId: roleId,
    picId: genPicIdFromInfo({ type: "home_decorate_photo", id: "" + id }),
    media: PicMediaType.Image,
    manual: 1,
    ip: ip,
  });
}
