import { injectIpHelper } from "../../middlewares/ipAuth";
import { Path } from "../../types/type";
import {
  homeDecoratePhotoGetByIds,
  homeDecoratePhotoList,
  homeDecoratePhotoRemove,
  homeDecoratePhotoUpdate,
} from "./operation";
import { ReqSchemas } from "./type";

export const paths: Path[] = [
  {
    method: "post",
    url: "/home_decorate_photo/update",
    paramsSchema: ReqSchemas.HomeDecoratePhotoUpdate,
    before: injectIpHelper(),
    operation: homeDecoratePhotoUpdate,
  },
  {
    method: "post",
    url: "/home_decorate_photo/remove",
    paramsSchema: ReqSchemas.HomeDecoratePhotoRemove,
    operation: homeDecoratePhotoRemove,
  },
  {
    method: "get",
    url: "/home_decorate_photo/list",
    paramsSchema: ReqSchemas.HomeDecoratePhotoList,
    operation: homeDecoratePhotoList,
  },
  {
    method: "get",
    url: "/home_decorate_photo/get_by_ids",
    paramsSchema: ReqSchemas.HomeDecoratePhotoGetByIds,
    operation: homeDecoratePhotoGetByIds,
  },
];

export const HomeDecoratePhotoComponent = {
  paths: paths,
  prefix: "/home_decorate_photo/",
};
