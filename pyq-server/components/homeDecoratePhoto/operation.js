"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.auditHomeDecoratePhoto = exports.homeDecoratePhotoGetByIds = exports.homeDecoratePhotoList = exports.homeDecoratePhotoRemove = exports.homeDecoratePhotoUpdate = void 0;
const audit_1 = require("../../../common/audit");
const auditType_1 = require("../../../common/auditType");
const constants_1 = require("../../../common/constants");
const util_1 = require("../../../common/util");
const config_1 = require("../../common/config");
const constants_2 = require("../../constants");
const helper_1 = require("../../helper");
const logger_1 = require("../../logger");
const audit_2 = require("../../services/audit");
const photo_1 = require("./models/photo");
const logger = (0, logger_1.clazzLogger)("homeDecoratePhoto/operation");
/** 更新指定位置的家园自定义图片 */
function homeDecoratePhotoUpdate(params) {
    return __awaiter(this, void 0, void 0, function* () {
        let photoId = 0;
        const condition = { roleId: params.roleid, index: params.index };
        const record = yield photo_1.HomeDecoratePhotoModel.findOne(condition, ["id"]);
        if (record) {
            yield photo_1.HomeDecoratePhotoModel.updateByCondition({ id: record.id }, { url: params.url, index: params.index, auditStatus: constants_1.AuditStatues.Auditing, createTime: Date.now() });
            photoId = record.id;
        }
        else {
            const props = {
                roleId: params.roleid,
                url: params.url,
                index: params.index,
                auditStatus: constants_1.AuditStatues.Auditing,
                createTime: Date.now(),
            };
            photoId = yield photo_1.HomeDecoratePhotoModel.insert(props);
        }
        auditHomeDecoratePhoto(params.roleid, [params.url], photoId, params.ip);
        return { id: photoId };
    });
}
exports.homeDecoratePhotoUpdate = homeDecoratePhotoUpdate;
/** 删除指定位置的图片 */
function homeDecoratePhotoRemove(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const record = yield photo_1.HomeDecoratePhotoModel.findOne({ roleId: params.roleid, index: params.index }, ["id"]);
        if (record && record.id) {
            yield photo_1.HomeDecoratePhotoModel.deleteByCondition({ id: record.id });
            return { id: record.id };
        }
        else {
            return (0, helper_1.BusinessError)(constants_2.ErrorTypes.DataNotFound, "家园装饰图片未找到");
        }
    });
}
exports.homeDecoratePhotoRemove = homeDecoratePhotoRemove;
/** 玩家家园自定义图片列表(10张) */
function homeDecoratePhotoList(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const queryRoleId = params.targetid || params.roleid;
        const data = yield photo_1.HomeDecoratePhotoModel.powerListQuery({
            where: { roleId: queryRoleId },
            orderBy: [["index"], ["asc"]],
            pagination: { page: 1, pageSize: config_1.homeDecoratePhotoCfg.maxSize },
        });
        const list = formatToPhotoList(data.list);
        return { list: list, count: data.meta.totalCount };
    });
}
exports.homeDecoratePhotoList = homeDecoratePhotoList;
/** 通过图片id列表获取图片 */
function homeDecoratePhotoGetByIds(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const idList = (0, util_1.csvStrToIntArray)(params.ids);
        const rows = yield photo_1.HomeDecoratePhotoModel.find({ id: idList });
        const list = formatToPhotoList(rows);
        const count = list.length;
        return { list, count };
    });
}
exports.homeDecoratePhotoGetByIds = homeDecoratePhotoGetByIds;
function formatToPhotoList(rows) {
    return rows.map((r) => {
        const item = {
            id: r.id,
            roleId: r.roleId,
            index: r.index,
            url: r.url,
            auditStatus: r.auditStatus,
        };
        return item;
    });
}
function auditHomeDecoratePhoto(roleId, imgList, id, ip) {
    logger.info({ roleId, imgList, id, ip }, "PrepareAuditHomeDecoratePhoto");
    return (0, audit_2.sendL10Pic)(imgList, {
        // 提交图片审核
        roleId: roleId,
        picId: (0, audit_1.genPicIdFromInfo)({ type: "home_decorate_photo", id: "" + id }),
        media: auditType_1.PicMediaType.Image,
        manual: 1,
        ip: ip,
    });
}
exports.auditHomeDecoratePhoto = auditHomeDecoratePhoto;
//# sourceMappingURL=operation.js.map