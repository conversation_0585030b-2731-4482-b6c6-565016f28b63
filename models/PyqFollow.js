"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
const bluebird = require("bluebird");
const _ = require("lodash");
const constants_1 = require("../common/constants");
const constants_2 = require("../pyq-server/constants");
const BaseModelClass = require("./BaseModelClass");
class FollowClass extends BaseModelClass {
    constructor() {
        super(constants_1.TABLE_NAMES.PyqFollow);
    }
    cancelFollow(roleId, otherRoleId) {
        return __awaiter(this, void 0, void 0, function* () {
            const isFollow = yield this.isFollowing(roleId, otherRoleId);
            if (!isFollow) {
                return bluebird.reject({ errorType: "cancelFollowInvalid", msg: "关注后才能取消关注" });
            }
            else {
                const ret = yield this.updateByCondition({ RoleId: roleId, TargetId: otherRoleId }, { Status: constants_2.FollowStatus.CancelFollowed, UpdateTime: Date.now() });
                return ret;
            }
        });
    }
    isFollowing(roleId, otherRoleId) {
        if (roleId && otherRoleId) {
            return this.exists({ RoleId: roleId, TargetId: otherRoleId, Status: constants_2.FollowStatus.Followed });
        }
        else {
            return bluebird.resolve(false);
        }
    }
    filterMeFollow(roleIds, meRoleId) {
        return __awaiter(this, void 0, void 0, function* () {
            const query = this.normalScope().whereIn("TargetId", roleIds).where("RoleId", meRoleId).select("TargetId");
            const rows = yield this.executeByQuery(query);
            return _.map(rows, "TargetId");
        });
    }
    filterFollowingMe(roleIds, meRoleId) {
        return __awaiter(this, void 0, void 0, function* () {
            const query = this.normalScope().whereIn("RoleId", roleIds).where("TargetId", meRoleId).select("RoleId");
            const rows = yield this.executeByQuery(query);
            return _.map(rows, "RoleId");
        });
    }
    getFollowingIds(roleId) {
        return __awaiter(this, void 0, void 0, function* () {
            const query = this.normalScope().select(["TargetId"]).where("RoleId", roleId).orderBy("ID", "desc");
            const rows = (yield this.executeByQuery(query));
            return rows.map((r) => r.TargetId);
        });
    }
    getFollowerIds(roleId) {
        const query = this.normalScope().select(["RoleId"]).where("TargetId", roleId);
        return this.executeByQuery(query).then(function (rows) {
            return _.map(rows, "RoleId");
        });
    }
    getFollowingIdsByRoleIds(roleIds) {
        return __awaiter(this, void 0, void 0, function* () {
            const hash = {};
            if (roleIds.length === 0) {
                return hash;
            }
            const query = this.normalScope().select(["RoleId", "TargetId"]).whereIn("RoleId", roleIds);
            const rows = (yield this.executeByQuery(query));
            for (const r of rows) {
                const followIds = hash[r.RoleId] || [];
                followIds.push(r.TargetId);
                hash[r.RoleId] = followIds;
            }
            return hash;
        });
    }
    getFansCount(roleId) {
        const query = this.normalScope().where("TargetId", roleId);
        return this.countByQuery(query);
    }
    getFollowCount(roleId) {
        const query = this.normalScope().where("RoleId", roleId);
        return this.countByQuery(query);
    }
    isFollowEach(roleId, targetId) {
        return __awaiter(this, void 0, void 0, function* () {
            const query = this.normalScope()
                .where("RoleId", roleId)
                .where("TargetId", targetId)
                .union(this.normalScope().where("RoleId", targetId).where("TargetId", roleId));
            const rows = yield this.executeByQuery(query);
            return rows.length === 2;
        });
    }
}
const FollowModel = new FollowClass();
module.exports = FollowModel;
//# sourceMappingURL=PyqFollow.js.map