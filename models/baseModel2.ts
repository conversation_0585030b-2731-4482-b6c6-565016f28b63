import * as _ from "lodash";
import * as bluebird from "bluebird";
import * as Constants from "../common/constants";
import * as DB from "../common/db";
import * as knex from "knex";
const SqlBuilder = knex({ client: "mysql" });
import { getPageMeta } from "../common/util2";
import { Pagination, UpdateResult } from "../common/type";
import { QueryBuilder } from "knex";
import { pageCfg } from "../pyq-server/common/config";
import Knex = require("knex");
export type DB_NODE_TYPE = "MASTER" | "SLAVE";

interface Offset {
  lastId?: number;
  count: number;
}

const Statuses = {
  NORMAL: Constants.Statues.Normal,
  DELETED: Constants.Statues.Deleted,
};

export interface DBExecuteOption {
  dbNode: DB_NODE_TYPE;
  maxScaleForceMaster?: boolean;
}

export type Condition<T> = Partial<{ [P in keyof T]: T[P] | Array<T[P]> }>;

type ElementOf<T> = T extends Array<infer E> ? E : never;

type IOrderBy<T> = [(keyof T)[], ("asc" | "desc")[]];

interface PowerQueryOption<T> {
  where?: Condition<T>;
  initQuery?: QueryBuilder;
  orderBy?: IOrderBy<T>;
  pagination: Pagination;
  select?: (keyof T)[];
  dbNode?: DB_NODE_TYPE;
}

type OrderDirection = "asc" | "desc";

export type IOrderByV2<T> = Array<[keyof T, OrderDirection]>;


interface FindManyOption<T> {
  where?: Condition<T>;
  initQuery?: knex.QueryBuilder;
  orderBy?: IOrderByV2<T>;
  pagination: Pagination;
  select?: (keyof T)[];
}



type C<T, Option extends PowerQueryOption<T>> = ElementOf<Option["select"]>;

type PowerQueryResult<T, O extends PowerQueryOption<T>> = [C<T, O>] extends [never] ? T[] : Pick<T, C<T, O>>[];

export class BaseModelClass<T> {
  private ID_COLUMN_NAME: string;
  private db: any;
  private dbNode: DB_NODE_TYPE;
  public tableName: string;
  Statuses: any;

  constructor(tableName: string, primaryKey?: keyof (T & { ID: number }), dbNode?, option?) {
    option = option || { db: DB };
    this.tableName = tableName;
    this.ID_COLUMN_NAME = (primaryKey as string) || "ID";
    this.dbNode = dbNode || "MASTER"; //默认指向MASTER节点
    this.db = option.db;
  }

  protected getOffsetCondition(query: knex.QueryBuilder, offset: Offset): knex.QueryBuilder {
    query.limit(offset.count).orderBy("ID", "DESC");

    if (offset.lastId) query.andWhere("ID", "<=", offset.lastId);

    return query;
  }

  protected getConditionQuery(conditions: Condition<T>, initQuery?: knex.QueryBuilder): knex.QueryBuilder {
    let query = initQuery || SqlBuilder.table(this.tableName);
    _.forEach(conditions, function (value, key) {
      if (_.isArray(value)) {
        query = query.whereIn(key, value);
      } else {
        query = query.where(key, value as any);
      }
    });
    return query;
  }

  private where(conditions, option?, dbOpt?: DBExecuteOption) {
    option = option || {};
    let query = this.getConditionQuery(conditions);
    if (option.limit) {
      query = query.limit(option.limit);
    }
    if (option.cols) {
      query = query.select(option.cols);
    }
    return this.executeByQuery(query, dbOpt);
  }

  scope(): knex.QueryBuilder {
    return SqlBuilder.table(this.tableName);
  }

  normalScope(): knex.QueryBuilder {
    return SqlBuilder.table(this.tableName).where("Status", Statuses.NORMAL);
  }

  byIdScope(id: number) {
    return this.scope().where({ [this.ID_COLUMN_NAME]: id });
  }

  updateByCondition(condition: Condition<T>, props: Condition<T>): Promise<UpdateResult> {
    const query = this.getConditionQuery(condition).update(props);
    return this.executeByQuery(query);
  }

  updateById(id: number, props: Partial<T>): Promise<UpdateResult> {
    const query = this.scope()
      .where({ [this.ID_COLUMN_NAME]: id })
      .update(props);
    return this.executeByQuery(query);
  }

  updateByIds(ids: number[], props: Partial<T>) {
    return this.executeByQuery(this.scope().whereIn(this.ID_COLUMN_NAME, ids).update(props)).then(() => {
      return this.findByIds(ids);
    });
  }

  async insert(props: Partial<T>): Promise<number> {
    const query = this.scope().insert(props);
    const result = await this.executeByQuery(query);
    return result.insertId;
  }

  async insertBatch(props: Partial<T>[]): Promise<number> {
    if (_.isEmpty(props)) return;
    const query = this.scope().insert(props);
    const result = await this.executeByQuery(query);
    return result.insertId;
  }

  delete(condition: Condition<T>) {
    const query = this.getConditionQuery(condition);
    return this.executeByQuery(query.del());
  }

  deleteByIds(ids: number[]) {
    const qb = this.scope().whereIn(this.ID_COLUMN_NAME, ids).del();
    return this.executeByQuery(qb);
  }

  find<Key extends keyof T>(
    conditions: Condition<T>,
    option?: { cols?: Key[]; limit?: number },
    dbOpt?: DBExecuteOption
  ): Promise<{ [P in Key]: T[P] }[]> {
    return this.where(conditions, option, dbOpt);
  }

  findOne<Key extends keyof T>(
    conditions: Condition<T>,
    cols?: Key[],
    option?: DBExecuteOption
  ): Promise<{ [P in Key]: T[P] }> {
    return this.find(conditions, { limit: 1, cols: cols }, option).then(function (records) {
      return _.chain(records).first().value() as T;
    });
  }

  findNormal<Key extends keyof T>(condition: Condition<T>, cols?: Key[]): Promise<{ [P in Key]: T[P] }[]> {
    const query = this.normalScope()
      .where(condition)
      .select(cols as string[]);
    return this.executeByQuery(query);
  }

  async findById<Key extends keyof T>(id: number, cols?: Key[]): Promise<{ [P in Key]: T[P] }> {
    const records = await this.where({ [this.ID_COLUMN_NAME]: id }, { limit: 1, cols: cols });
    const record = _.chain(records).first().value() as T;
    return record;
  }

  async findNormalById<Key extends keyof T>(id: number, cols?: Key[]): Promise<{ [P in Key]: T[P] }> {
    const records = await this.where({ [this.ID_COLUMN_NAME]: id, Status: Statuses.NORMAL }, { limit: 1, cols: cols });
    const record = _.chain(records).first().value() as T;
    return record;
  }

  async findByIds<Key extends keyof T>(
    ids: number[],
    cols?: Key[],
    conditions?: Condition<T>
  ): Promise<{ [P in Key]: T[P] }[]> {
    if (_.isEmpty(ids)) return [];
    ids = _.uniq(_.compact(_.map(ids, _.toNumber)));
    let query = this.scope()
      .select(cols as string[])
      .whereIn(this.ID_COLUMN_NAME, ids);

    if (conditions) query = query.andWhere(conditions);

    const records = await this.executeByQuery(query);
    const recordGroupById = _.groupBy(records, this.ID_COLUMN_NAME);

    return _.chain(ids)
      .map(function (id) {
        return _.head(recordGroupById[id]);
      })
      .compact()
      .value();
  }

  findByOffset<Key extends keyof T>(
    conditions: Condition<T>,
    offset: Offset,
    cols?: Key[]
  ): Promise<{ [P in Key]: T[P] }[]> {
    let query = this.scope();

    if (!_.isEmpty(conditions)) query.andWhere(conditions);
    if (cols) query = query.select(cols as string[]);

    query = this.getOffsetCondition(query, offset);

    return this.executeByQuery(query);
  }

  findByPagination<Key extends keyof T>(
    conditions: Condition<T>,
    pagination: { page: number; pageSize: number },
    cols?: Key[]
  ): Promise<{ [P in Key]: T[P] }[]> {
    let query = this.scope().select(cols as string[]);

    if (!_.isEmpty(conditions)) {
      query.andWhere(conditions);
    }

    const page = pagination.page;
    const pageSize = pagination.pageSize;
    const offset = pageSize * (page - 1);
    query = query.offset(offset).limit(pageSize || pageCfg.defaultPageSize);

    return this.executeByQuery(query);
  }

  async queryByPagination(query: knex.QueryBuilder, pagination: Pagination) {
    const offset = pagination.pageSize * (pagination.page - 1);
    query = query.offset(offset).limit(pagination.pageSize);
    const rows = await this.executeByQuery(query);
    return rows;
  }

  async exists(conditions: Condition<T>): Promise<boolean> {
    const exist = await this.isExist(conditions);
    return exist;
  }

  async notExist(conditions: Condition<T>): Promise<boolean> {
    const exist = this.exists(conditions);
    return !exist;
  }

  async existsById(id: number): Promise<boolean> {
    const exist = this.isExist({ [this.ID_COLUMN_NAME]: id });
    return exist;
  }

  private isExist(conditions) {
    return this.findOne(conditions, [this.ID_COLUMN_NAME as keyof T]).then(function (rows) {
      return !_.isEmpty(rows);
    });
  }

  private execute(sql: string, option?: DBExecuteOption) {
    const opt = option || { dbNode: this.dbNode };
    return this.db.execute(sql, opt);
  }

  executeByQuery<T=any>(query: QueryBuilder | knex.Raw, option?: DBExecuteOption): Promise<T> {
    const opt: DBExecuteOption = option || { dbNode: this.dbNode, maxScaleForceMaster: false };
    let sql = query.toString();
    if (opt.maxScaleForceMaster) {
      sql += " -- maxscale route to master";
    }
    return this.execute(sql, opt);
  }

  smartQuery<Key extends keyof T>(query: QueryBuilder, cols?: Key[]): Promise<{ [P in Key]: T[P] }[]> {
    query = query.select(cols as unknown as string[]);
    return this.executeByQuery(query);
  }

  public queryWithPagination(query, pagination: { page: number; pageSize: number }) {
    const page = pagination.page;
    const pageSize = pagination.pageSize;
    const offset = pageSize * (page - 1);
    query = query.offset(offset).limit(pageSize);
    return this.executeByQuery(query);
  }

  count(condition: Condition<T>): Promise<number> {
    const query = this.scope().where(condition);
    return this.countByQuery(query);
  }

  async countByQuery(query): Promise<number> {
    const rows = (await this.executeByQuery(query.count())) as { ["count(*)"]: number }[];
    if (rows.length > 0) {
      return rows[0]["count(*)"] || 0;
    } else {
      return 0;
    }
  }

  async softDeleteById(id: number): Promise<UpdateResult> {
    const query = this.scope()
      .where({ [this.ID_COLUMN_NAME]: id })
      .update({ status: Statuses.DELETED });
    return this.executeByQuery(query);
  }

  softDeleteByCondition(condition: Condition<T>): Promise<UpdateResult> {
    let query = this.getConditionQuery(condition);
    query = query.update({ status: Statuses.DELETED });
    return this.executeByQuery(query);
  }

  deleteByCondition(condition: Condition<T>): Promise<UpdateResult> {
    const query = this.getConditionQuery(condition).del();
    return this.executeByQuery(query);
  }

  refresh(record) {
    return this.findById(record[this.ID_COLUMN_NAME]);
  }

  escapeLikeStr(likeStr) {
    return likeStr.replace(/[_%]|\\/g, function (escapeChar) {
      return "\\" + escapeChar;
    });
  }

  promiseMap(iterable, mapper, concurrency) {
    return bluebird.map(iterable, mapper, { concurrency: concurrency || 5 });
  }

  createOrUpdate(props: Partial<T>, updateProps?: Partial<T>): Promise<UpdateResult> {
    if (!updateProps) {
      updateProps = props;
    }
    const insert = this.scope().insert(props).toString();
    const update = this.scope()
      .update(updateProps)
      .toString()
      .replace(/^update .* set /i, "");
    const query = this.raw(insert + " on duplicate key update " + update);
    return this.executeByQuery<UpdateResult>(query);
  }

  createOrUpdateQuery(props: Partial<T>, updateProps?: Partial<T>) {
    if (!updateProps) {
      updateProps = props;
    }
    const insert = this.scope().insert(props).toString();
    const update = this.scope()
      .update(updateProps)
      .toString()
      .replace(/^update .* set /i, "");
    return this.raw(insert + " on duplicate key update " + update) as unknown as Knex.QueryBuilder<any, number>;
  }

  raw(sql: string, valueBinding?: any[]) {
    return SqlBuilder.raw(sql, valueBinding);
  }

  async increment(condition: Condition<T>, incrCol: keyof T, incrVal = 1): Promise<UpdateResult> {
    const query = this.getConditionQuery(condition).increment(incrCol as string, incrVal);
    return this.executeByQuery(query);
  }

  async incrManyFields(condition: Condition<T>, incrCols: (keyof T)[]): Promise<UpdateResult> {
    let query = this.getConditionQuery(condition);
    for (const r of incrCols) {
      query = query.increment(r as string, 1);
    }
    return this.executeByQuery(query);
  }



  async powerQuery<Option extends PowerQueryOption<T>>(option: Option): Promise<PowerQueryResult<T, Option>> {
    let query: QueryBuilder = this.scope();
    if (option.initQuery) {
      query = option.initQuery.clone();
    }
    if (option.where) {
      query = this.getConditionQuery(option.where, query);
    }
    if (option.orderBy) {
      const [cols, orders] = option.orderBy;
      const orderBys = _.zip(cols, orders);
      for (const r of orderBys) {
        const [col, order] = r;
        query = query.orderBy(col as any, order);
      }
    }
    if (option.select) {
      query = query.select(option.select as any);
    }
    const pagination = option.pagination;
    const offset = (pagination.page - 1) * pagination.pageSize;
    query = query.offset(offset).limit(pagination.pageSize || pageCfg.defaultPageSize);
    const rows = await this.executeByQuery(query, { dbNode: option.dbNode || "MASTER" });
    return rows;
  }

  async powerListQuery<Option extends PowerQueryOption<T>>(option: Option) {
    const params = option.pagination;
    const list = await this.powerQuery(option);

    const getCountQuery = () => {
      let query: QueryBuilder = null;
      if (option.initQuery) {
        query = option.initQuery;
      } else {
        query = this.scope();
      }
      if (option.where) {
        query = this.getConditionQuery(option.where, query);
      }
      return query;
    };

    const countQuery = getCountQuery();
    const totalCount = await this.countByQuery(countQuery);
    const meta = getPageMeta(params, totalCount);
    return { list: list, meta: meta };
  }

  protected getConditionQueryWithQuery(query: knex.QueryBuilder, conditions: Condition<T>) {
    _.forEach(conditions, function (value, key) {
      if (_.isArray(value)) {
        query = query.whereIn(key, value);
      } else {
        query = query.where({ [key]: value });
      }
    });
    return query;
  }


  /**
   * 高级查询函数，支持条件查询、排序、分页和字段选择
   *
   * @param ctx - 上下文对象
   * @param option - 查询选项，包含where条件、初始查询、排序规则、分页参数和选择字段
   * @returns 返回查询结果数组，如果指定了select字段，则返回包含这些字段的对象数组
   */
  async findMany<Option extends FindManyOption<T>>(
    option: Option
  ): Promise<Option["select"] extends (keyof T)[] ? Pick<T, ElementOf<Option["select"]>>[] : T[]> {
    let query: knex.QueryBuilder = this.scope();
    if (option.initQuery) {
      query = option.initQuery.clone()
    }
    if (option.where) {
      query = this.getConditionQueryWithQuery(query, option.where);
    }
    if (option.orderBy) {
      for (let [col, order] of option.orderBy) {
        // @ts-ignore
        query = query.orderBy(col, order);
      }
    }
    if (option.select) {
      //@ts-ignore
      query = query.select(option.select);
    }
    let offset = (option.pagination.page - 1) * option.pagination.pageSize;
    query = query.offset(offset).limit(option.pagination.pageSize);
    let rows = await this.executeByQuery<any>(query);
    return rows;
  }

}
