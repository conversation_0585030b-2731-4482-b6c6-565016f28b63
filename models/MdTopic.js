const BaseModelClass = require('./BaseModelClass')
const MdTopic = new BaseModelClass('md_topic')
const MdTopicMoment = require('./MdTopicMoment')
const util = require('../common/util')
const co = util.co
const _ = require('lodash')

function prependItemToCsvStr (csvStr, newItem, limit) {
  return _.chain(util.csvStrToArray(csvStr)).unshift(newItem).take(limit).join(',').value()
}

MdTopic.addMomentToTopic = function (subject, momentId, userId) {
  return co(function * () {
    const now = Date.now()
    const topic = yield MdTopic.findOne({Subject: subject}, ['ID', 'Hot', 'MomentList'])
    if (topic) {
      yield MdTopicMoment.insert({ TopicId: topic.ID, UserId: userId, MomentId: momentId, CreateTime: now })
      const updateValues = {UpdateTime: now, Hot: topic.Hot + 1}
      updateValues.MomentList = prependItemToCsvStr(topic.MomentList, momentId, 20) // 保留最新的20个
      yield MdTopic.updateByCondition({ID: topic.ID}, updateValues)
      return topic.ID
    } else {
      const insertInfo = yield MdTopic.insert({Subject: subject, MomentList: momentId, Hot: 1, CreateTime: now, UpdateTime: now})
      yield MdTopicMoment.insert({ TopicId: insertInfo.insertId, UserId: userId, MomentId: momentId, CreateTime: now })
      return insertInfo.insertId
    }
  })
}

module.exports = MdTopic
