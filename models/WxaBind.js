/**
 * Created by zhen<PERSON> on 2017/3/9.
 */

const BaseModelClass = require('./BaseModelClass');
const WxaBind = new BaseModelClass("md_wxa_bind");
const Users = require('./Users');
const _ = require('lodash');

WxaBind.getUserIdByOpenId = function (openId) {
  return WxaBind.findOne({OpenId:openId}, ['UserId']).then(userInfo => {
    return _.get(userInfo, 'UserId');
  });
};

WxaBind.getUserInfoByOpenId = function (openId) {
  return WxaBind.getUserIdByOpenId(openId).then(userId => {
    if (userId) {
      return Users.findById(userId, ['ID', 'Avatar', 'Province', 'City', 'NickName', 'AvaAuthStatus', 'Gender', 'Signature'])
    } else {
      return null;
    }
  });
};

module.exports = WxaBind;
