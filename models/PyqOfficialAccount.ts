import { TABLE_NAMES } from "../common/constants";
import BaseModelClass = require("./BaseModelClass");
import { contains } from "../common/util2";
import { OfficialRoleIds } from "../common/constants";

export interface OfficialAccountSetting {
  PublicStatus: number;
  ForceFollow: number;
  AutoBeHot: number;
}

export interface OfficialAccountRecord extends OfficialAccountSetting {
  RoleId: number;
  CreateTime: number;
}

export enum PublicStatus {
  Public = 1,
  NoPublic = 0,
}

export let DefaultOASetting = {
  PublicStatus: 0,
  ForceFollow: 0,
  AutoBeHot: 0,
};

class OfficialAccountClass extends BaseModelClass<OfficialAccountRecord> {
  constructor() {
    super(TABLE_NAMES.PyqOfficialAccount);
  }

  async getSetting(roleId: number) {
    let record = await this.findOne({ RoleId: roleId }, ["PublicStatus", "ForceFollow", "AutoBeHot"]);
    if (record) {
      return record;
    } else {
      return DefaultOASetting;
    }
  }

  async getAllRecords(): Promise<OfficialAccountRecord[]> {
    return this.find({});
  }

  async getRecordMap(): Promise<Map<number, OfficialAccountRecord>> {
    let records = await this.getAllRecords();
    let map: Map<number, OfficialAccountRecord> = new Map();
    for (let r of records) {
      map.set(r.RoleId, r);
    }
    return map;
  }

  async filterAllowCancel(ids: number[]) {
    let recordMap = await this.getRecordMap();
    return ids.filter((id) => {
      let record = recordMap.get(id) || { ForceFollow: false };
      return record.ForceFollow === 0;
    });
  }

  async getPublicIds(excludeIds?: number[]) {
    let query = this.scope().where("PublicStatus", 1).select(["RoleId"]);
    if (excludeIds && excludeIds.length > 0) {
      query = query.whereNotIn("RoleId", excludeIds);
    }
    let rows = (await this.executeByQuery(query)) as { RoleId: number }[];
    return rows.map((x) => x.RoleId);
  }

  async getAllOfficialIds() {
    let query = this.scope().select(["RoleId"]);
    let rows = (await this.executeByQuery(query)) as { RoleId: number }[];
    return rows.map((x) => x.RoleId);
  }

  isInOfficialAccountIdRange(roleId: number) {
    let startId = ********;
    return roleId >= startId && roleId <= startId + 500;
  }

  isOfficialAccount(roleId: number) {
    return contains(OfficialRoleIds, roleId) || this.isInOfficialAccountIdRange(roleId);
  }

  async getAutoBeHotAccountId() {
    let record = await this.findOne({ AutoBeHot: 1 }, ["RoleId"]);
    if (record) {
      return record.RoleId;
    } else {
      return 0;
    }
  }

  async isPublic(roleId: number) {
    let record = await this.getSetting(roleId);
    if (record && record.PublicStatus) {
      return true;
    } else {
      return false;
    }
  }
}

export let OfficialAccount = new OfficialAccountClass();
