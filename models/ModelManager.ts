import * as _ from "lodash";
import BaseModelClass = require("./BaseModelClass");
let tableNameHash = require("./meta.json");

type DB_NODE = "MASTER" | "SLAVE";

const ModelCacheGroup = {
  MASTER: {},
  SLAVE: {},
};

export function getModelByTableName<T>(tableName: string, dbNode?: DB_NODE): BaseModelClass<T> {
  dbNode = dbNode || "MASTER";
  let filename = tableNameHash[tableName];
  let modelCache = ModelCacheGroup[dbNode];
  if(!filename) throw new Error(`require model ${tableName} failed`)
  if (filename) {
    if (!modelCache[tableName]) {
      let model = require("./" + filename) as { dbNode: DB_NODE };
      if(!model) throw new Error("require model ${tableName} is empty")
      let newModel = Object.create(model);
      newModel.dbNode = dbNode;
      modelCache[tableName] = newModel;
    }
    return modelCache[tableName];
  }
}

export function getSlaveModel(model: { tableName: string }) {
  return getModelByTableName(model.tableName, "SLAVE");
}

export function getSlaveModelByName<T>(tableName: string) {
  return getModelByTableName<T>(tableName, "SLAVE");
}
