﻿var BaseModelClass = require('./BaseModelClass');
var LikeAble = require('./mixins/LikeAble');
var CommentAble = require('./mixins/CommentAble');
var ImagesAuditable = require('./mixins/ImagesAuditable');
var _ = require('lodash');
var ShowTopics = module.exports = _.extend(new BaseModelClass("md_show_topic"), LikeAble, CommentAble, ImagesAuditable);
var Tags = require('./Tags');
var Promise = require('bluebird');
var util = require('../common/util');
var logger = require('../common/logger');
var Photos = require('./Photos');
var MdEventBus = require('../md-server/eventBus');
var logger = require('../common/logger');
const Constants = require('../common/data').Constants;

const REJECT_PHOTO_URL = "https://hi-163-common.nosdn.127.net/upload/201612/02/c9704c20b83b11e68d3fc7b913867c9a";

ShowTopics.AttrsList = [
  'ID',
  'SubjectId',
  'UserId',
  'Status',
  'CreateTime',
  'UpdateTime',
  'ImgList',
  'ImgAuditList',
  'Title',
  'Content',
  'ZanList'
];

ShowTopics.create = function(props) {
  var now = Date.now();
  props = _.assign({CreateTime: now, UpdateTime: now}, props);
  return BaseModelClass.prototype.create.call(this, props);
};

ShowTopics.updateById = function(id, props) {
  var updateById = BaseModelClass.prototype.updateById.bind(this);
  if(!props.Tags) {
    return updateById( id, props);
  } else {
    var updateTags = _.uniq(props.Tags);
    var updateProps = _.omit(props, 'Tags');
    return Promise.join(
      updateById(id, updateProps),
      ShowTopics.updateTagsById(id, updateTags),
      function(showTopic, newTags) {
        showTopic.Tags = updateTags;
        return showTopic;
      }
    );
  }
};


ShowTopics.allAuditPassedQuery = function (query) {
  query = query.where('ImgAudit', 'REGEXP', "^(1\,?)+$");
  return query;
};

ShowTopics.listByUser = function(userId, pagination, sortBy, isViewBySelf) {
  var query = this.scope()
    .where('Status', ShowTopics.Statuses.NORMAL)
    .where('UserId', userId);
  if(!isViewBySelf) {
    query = ShowTopics.allAuditPassedQuery(query);
  }
  if(sortBy === "new") {
    query = query.orderBy('CreateTime', 'desc');
  } else if(sortBy == "hot") {
    query = query.orderBy('Hot', 'desc');
  }
  return this.queryWithPagination(query, pagination);
};

ShowTopics.findByTags = function(tags, pagination) {
  return Tags.findTargetIdsByTags(ShowTopics.tableName, tags)
    .then(function(topicIds) {
      var query = ShowTopics.scope()
        .where('Status', ShowTopics.Statuses.NORMAL)
        .whereIn('ID', topicIds);
      return ShowTopics.queryWithPagination(query, pagination);
    });
};

ShowTopics.findByTagLike = function (keyword, pagination) {
  return Tags.findTargetIdsByTagLike(ShowTopics.tableName, keyword)
    .then(function(topicIds) {
      var query = ShowTopics.scope()
        .where('Status', ShowTopics.Statuses.NORMAL)
        .whereIn('ID', topicIds);
      query = ShowTopics.allAuditPassedQuery(query);
      return ShowTopics.queryWithPagination(query, pagination);
    });
};

ShowTopics.findByTagLikeInSubject = function (subjectId, keyword, pagination) {
  var getIdsFromSubject = subjectId => ShowTopics.executeByQuery(
    ShowTopics.normalScope()
      .select('ID')
      .where('SubjectId', subjectId)
  ).map(r => r.ID);
  return Promise.join(
    getIdsFromSubject(subjectId),
    Tags.findTargetIdsByTagLike(ShowTopics.tableName, keyword),
    function (ids1, ids2) {
      return _.intersection(ids1, ids2);
    }
  ).then(topicIds => {
    var query = ShowTopics.scope()
      .where('Status', ShowTopics.Statuses.NORMAL)
      .whereIn('ID', topicIds);
    query = ShowTopics.allAuditPassedQuery(query);
    return ShowTopics.queryWithPagination(query, pagination);
  });
};

ShowTopics.addTags = function(showTopicId, tags) {
  return Tags.createForTarget(ShowTopics.tableName, showTopicId, tags);
};

ShowTopics.deleteTagsById = function(showTopicId) {
  return Tags.deleteByTarget(ShowTopics.tableName, showTopicId);
};

ShowTopics.updateTagsById = function(showTopicId, tags) {
  return Tags.updateByTarget(ShowTopics.tableName, showTopicId, tags);
};

ShowTopics.getShowTopicsWithTags = function(showTopics) {
  var showTopicIds = _.map(showTopics, 'ID');
  var query = Tags.scope()
    .where('TargetType', ShowTopics.tableName)
    .whereIn('TargetId', showTopicIds);
  return Tags.executeByQuery(query).then(function(tags) {
    var tagsGroupByShowTopicId = _.groupBy(tags, 'TargetId');

    return _.map(showTopics, function(showTopic) {
      var tags = _.map(tagsGroupByShowTopicId[showTopic.ID], 'Title');
      showTopic.Tags = tags;
      return showTopic;
    });
  });
};

ShowTopics.getLatestBySubjectId = function (subjectId, size) {
  var query = ShowTopics.normalScope()
    .where('SubjectId', subjectId)
    .orderBy('CreateTime', 'desc')
    .limit(size);
  query = ShowTopics.allAuditPassedQuery(query);
  return ShowTopics.executeByQuery(query);
};

ShowTopics.getHottestBySubjectId = function (subjectId, size) {
  var query = ShowTopics.normalScope()
    .where('SubjectId', subjectId)
    .orderBy('Hot', 'desc')
    .limit(size);
  var query = ShowTopics.allAuditPassedQuery(query);
  return ShowTopics.executeByQuery(query);
};

ShowTopics.getTagsById = function(id) {
  return Tags.findByTarget(ShowTopics.tableName, id).then(Tags.toTagTitles);
};

ShowTopics.Serializers= {
  default: function (records) {
    var serialize = function (r) {
      r.ZanList = util.csvStrToArray(r.ZanList);
      r.ImgList= util.csvStrToArray(r.ImgList);
      var coverInfo = ShowTopics.getCoverInfo(r.ImgList, util.csvStrToIntArray(r.ImgAudit));
      r.Cover = coverInfo.url;
      r.CoverAudit = coverInfo.auditStatus;
      return r;
    };
    if(_.isArray(records)) {
      return records.map(serialize);
    } else {
      return serialize(records);
    }
  }
};

ShowTopics.getCoverInfo = function (imgList, auditList) {
  if(_.isEmpty(imgList)) {
    return {url:null, auditStatus:null};
  }
  var rejectIndex = _.findIndex(auditList, r => _.eq(Constants.STATUS_AUDIT_REJECT, r));
  if(rejectIndex !== -1) {
    return {url: REJECT_PHOTO_URL, auditStatus: Constants.STATUS_AUDIT_REJECT};
  } else {
    return {url: _.first(imgList), auditStatus: _.first(auditList)};
  }
};

ShowTopics.like = function (userId, id) {
  let Notifications = require('../models/Notifications');
  var self = this;
  return LikeAble.like.bind(self)(userId, id, {
    beforeUpdate: function (props, showTopic) {
      var zanCount = util.csvStrToIntArray(props.ZanList).length;
      var hotState = ShowTopics.updateHotState(ShowTopics.getHotState(showTopic), {ZanCount: zanCount});
      props.HotState = JSON.stringify(hotState);
      props.Hot = ShowTopics.getHotFromState(hotState);
      return props;
    }
  }).then(function (data) {
    return ShowTopics.findById(id, ['ID', 'UserId']).then(function (r) {
      if(r) {
        return Notifications.addLikeShowTopicEvent(userId, r);
      }
    }).then(() => {
      return data;
    })
  });
};

ShowTopics.getHotState = function (showTopic) {
  return JSON.parse(showTopic.HotState) || {ZanCount:0, CommentCount:0};
};

ShowTopics.updateHotState = function (hotState, props) {
  return _.assign({}, hotState, props);
};

ShowTopics.getHotFromState = function (hotState) {
  return hotState.ZanCount + hotState.CommentCount;
};


ShowTopics.unLike = function (userId, id) {
  let Notifications = require('../models/Notifications');
  var self = this;
  return LikeAble.unLike.bind(self)(userId, id, {
    beforeUpdate: function (props, showTopic) {
      var zanCount = util.csvStrToIntArray(props.ZanList).length;
      var hotState = ShowTopics.updateHotState(ShowTopics.getHotState(showTopic), {ZanCount: zanCount});
      props.HotState = JSON.stringify(hotState);
      props.Hot = ShowTopics.getHotFromState(hotState);
      return props;
    }
  }).then(function (data) {
    return ShowTopics.findById(id, ['ID', 'UserId']).then(function (r) {
      if(r) {
        return Notifications.softDeleteLikeShowTopicEvent(userId, r);
      }
    }).then(() => {
      return data;
    })
  });
};

ShowTopics.getWithTags = function (showTopic) {
  return ShowTopics.getTagsById(showTopic.ID)
    .then(function (tags) {
      showTopic.Tags = _.pull(tags, showTopic.Title);
      return showTopic;
    });
};

function updateHotWhenComment(id) {
  return ShowTopics.findById(id)
    .then(r => {
      var hotState = ShowTopics.getHotState(r);
      var hotState = ShowTopics.updateHotState(hotState, {CommentCount: hotState.CommentCount + 1});
      var props = {
        Hot: ShowTopics.getHotFromState(hotState),
        HotState: JSON.stringify(hotState)
      };
      return ShowTopics.updateById(r.ID, props);
    }).catch(err => {
      logger.error(err);
    });
}

function updateHotWhenDeleteComment(id) {
  return ShowTopics.findById(id)
    .then(r => {
      var hotState = ShowTopics.getHotState(r);
      var hotState = ShowTopics.updateHotState(hotState, {CommentCount: hotState.CommentCount - 1});
      var props = {
        Hot: ShowTopics.getHotFromState(hotState),
        HotState: JSON.stringify(hotState)
      };
      return ShowTopics.updateById(r.ID, props);
    }).catch(err => {
      logger.error(err);
    });
}


ShowTopics.comment = function (userId, targetId, text) {
  let Notifications = require('./Notifications');
  return CommentAble.comment.bind(this)(userId, targetId, text)
    .then(function (res) {
      updateHotWhenComment(targetId);
      return res;
    }).then(function (data) {
      return ShowTopics.findById(targetId, ['ID', 'UserId']).then(function (r) {
        if(r) {
          return Notifications.addEvent(userId, r.UserId, Notifications.EVENT_TYPES.COMMENT_SHOW_TOPIC, data.ID);
        }
      }).then(() => {
        return data;
      })
    })
};

ShowTopics.replyComment = function (userId, targetId, replyId, text) {
  let Notifications = require('./Notifications');
  return CommentAble.replyComment.bind(this)(userId,targetId, replyId, text)
    .then(function (res) {
      updateHotWhenComment(targetId);
      return res;
    }).then(function (data) {
      return ShowTopics.findById(targetId, ['ID', 'UserId']).then(function (r) {
        if(r) {
          return Notifications.addEvent(userId, replyId, Notifications.EVENT_TYPES.REPLY_SHOW_TOPIC, data.ID);
        }
      }).then(() => {
        return data;
      })
    });
};

ShowTopics.updateHotWhenDeleteComment = updateHotWhenDeleteComment;

MdEventBus.on("showTopicPhotoAuditFinishEvent", function(data) {
  ShowTopics.photoAuditFinish(data.id, data.url, data.auditStatus)
});

ShowTopics.REJECT_PHOTO_URL = REJECT_PHOTO_URL;
