"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoleIdPartitionModel = void 0;
const db2_1 = require("../common/db2");
const redis_1 = require("../common/redis");
const knex = require("knex");
const config_1 = require("../common/config");
class RoleIdPartitionModel {
    constructor() {
        this.dbClient = db2_1.DBClient.getInstance();
        this.rowRecordExpire = 30 * 60; //30分钟
    }
    allTables() {
        let name = this.tableName();
        let list = [name];
        for (let i = 0; i < 10; i++) {
            let pname = name + '_' + i;
            list.push(pname);
        }
        return list;
    }
    getPartitionTableName(roleId) {
        let partitionId = roleId % 10;
        return this.tableName() + '_' + partitionId;
    }
    getOriginTableName() {
        return this.tableName();
    }
    scope(roleId) {
        if (roleId) {
            return RoleIdPartitionModel.sqlBuilder.from(this.getPartitionTableName(roleId));
        }
        else {
            return RoleIdPartitionModel.sqlBuilder.from(this.getOriginTableName());
        }
    }
    migrateRowToPartition(record) {
        return this.insertOrUpdate(record);
    }
    findOneByQuery(query) {
        return __awaiter(this, void 0, void 0, function* () {
            query = query.limit(1);
            let list = yield this.executeByQuery(query);
            let row = list[0];
            return row || null;
        });
    }
    setRowRecordToCache(roleId, record) {
        return __awaiter(this, void 0, void 0, function* () {
            let key = this.getRowRecordCacheKey(roleId);
            if (record) {
                let recordStr = JSON.stringify(record);
                yield (0, redis_1.getRedis)().setAsync(key, recordStr);
                yield (0, redis_1.getRedis)().expireAsync(key, this.rowRecordExpire);
            }
        });
    }
    markRowRecordCacheInvalid(roleId) {
        return __awaiter(this, void 0, void 0, function* () {
            let key = this.getRowRecordCacheKey(roleId);
            let info = yield (0, redis_1.getRedis)().delAsync(key);
            return info;
        });
    }
    getRowRecordCacheKey(roleId) {
        let key = 'md_models:' + this.tableName() + ':record:' + roleId;
        return key;
    }
    findByRoleIdByCache(roleId, cols) {
        return __awaiter(this, void 0, void 0, function* () {
            let key = this.getRowRecordCacheKey(roleId);
            let existResult = yield (0, redis_1.getRedis)().existsAsync(key);
            let fullRecord = null;
            if (existResult === redis_1.IExistResult.NotExist) {
                fullRecord = yield this.findByRoleIdByDB(roleId);
                yield this.setRowRecordToCache(roleId, fullRecord);
            }
            else {
                let recordStr = yield (0, redis_1.getRedis)().getAsync(key);
                fullRecord = JSON.parse(recordStr);
            }
            if (fullRecord) {
                let result = { RoleId: roleId };
                if (cols) {
                    for (let c of cols) {
                        result[c] = fullRecord[c];
                    }
                }
                else {
                    result = fullRecord;
                }
                return result;
            }
            else {
                return null;
            }
        });
    }
    findByRoleIdByDB(roleId) {
        return __awaiter(this, void 0, void 0, function* () {
            let query1 = this.scope(roleId).where('RoleId', roleId);
            let row = yield this.findOneByQuery(query1);
            if (row) {
                return row;
            }
            else {
                let query2 = this.scope().where('RoleId', roleId);
                let row = yield this.findOneByQuery(query2);
                if (row) {
                    yield this.migrateRowToPartition(row);
                }
                return row;
            }
        });
    }
    findByRoleId(roleId, cols) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.findByRoleIdByCache(roleId, cols);
        });
    }
    findByRoleIdFromOrigin(roleId, cols) {
        return __awaiter(this, void 0, void 0, function* () {
            let query = this.scope().where('RoleId', roleId).select(cols);
            let rows = yield this.executeByQuery(query);
            return rows[0];
        });
    }
    executeByQuery(query) {
        return __awaiter(this, void 0, void 0, function* () {
            let sql = query.toString();
            if (config_1.testCfg.db_debug) {
                console.log(sql);
            }
            return this.dbClient.execute(sql);
        });
    }
    findByRoleIds(roleIds, cols) {
        return __awaiter(this, void 0, void 0, function* () {
            let list = [];
            for (let r of roleIds) {
                let item = yield this.findByRoleId(r, cols);
                list.push(item);
            }
            return list;
        });
    }
    getFullRecordFromOriginTable(roleId) {
        return __awaiter(this, void 0, void 0, function* () {
            let query = this.scope().select('*').where('RoleId', roleId);
            let rows = yield this.executeByQuery(query);
            return rows[0] || null;
        });
    }
    insertOrUpdate(props, updateProps) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!updateProps) {
                updateProps = props;
            }
            let query = this.scope(props.RoleId).where('RoleId', props.RoleId);
            let rows = yield this.executeByQuery(query);
            let recordInNewTable = rows[0];
            if (recordInNewTable) {
                let query = this.scope(props.RoleId).where('RoleId', props.RoleId).update(updateProps);
                let result = yield this.executeByQuery(query);
                yield this.markRowRecordCacheInvalid(props.RoleId);
                return result;
            }
            else {
                let recordInOldTable = yield this.getFullRecordFromOriginTable(props.RoleId);
                let insertProps = Object.assign({}, recordInOldTable, props);
                yield this.markRowRecordCacheInvalid(props.RoleId);
                return this.insert(insertProps);
            }
        });
    }
    raw(sql) {
        return RoleIdPartitionModel.sqlBuilder.raw(sql);
    }
    insert(props) {
        return __awaiter(this, void 0, void 0, function* () {
            let query = this.scope(props.RoleId).insert(props);
            let result = yield this.executeByQuery(query);
            return result;
        });
    }
}
exports.RoleIdPartitionModel = RoleIdPartitionModel;
RoleIdPartitionModel.sqlBuilder = knex({ client: 'mysql' });
//# sourceMappingURL=RoleIdPartitionModel.js.map