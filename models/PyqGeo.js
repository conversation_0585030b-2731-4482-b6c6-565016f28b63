﻿/**
 * Created by <PERSON><PERSON><PERSON> on 16-11-21.
 */

var _ = require('lodash');
var BaseModelClass = require('./BaseModelClass');
let QnmRoleAble = require('./mixins/QnmRoleAble');

const ONLINE_STATUSES = {
  ONLINE: 1,
  OFFLINE: 0,
};

var PyqGeo = _.extend(new BaseModelClass("pyq_geo"), QnmRoleAble, {
  PrimaryKey: 'RoleId',

  ONLINE_STATUSES: ONLINE_STATUSES,

  switchToOffline: function (roleId) {
    return PyqGeo.updateByCondition({RoleId:roleId}, {Online: ONLINE_STATUSES.OFFLINE});
  },

  isOnline: function (record) {
    return record.Online == ONLINE_STATUSES.ONLINE;
  },

  isOffline: function (record) {
    return record.Online == ONLINE_STATUSES.OFFLINE;
  },

  rejectHideLbs: function (roleIds) {
    return PyqGeo.where({RoleId:roleIds, HideLbs:0}, {cols:['RoleId']})
      .then(rows => {
        return _.map(rows, 'RoleId');
      })
  },

  isHideLbs: function (record) {
    return record.HideLbs == 1;
  },

  hideLbs: function (roleId) {
    return PyqGeo.updateByCondition({RoleId:roleId}, {
      HideLbs: 1
    });
  },

  showLbs: function (roleId) {
    return PyqGeo.updateByCondition({RoleId:roleId}, {
      HideLbs: 0
    });
  }

});

module.exports = PyqGeo;
