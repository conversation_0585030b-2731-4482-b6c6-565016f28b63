"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const BaseModelClass = require("./BaseModelClass");
const mysql = require("mysql");
const config = require("../common/config");
const config_1 = require("../pyq-server/common/config");
const logger2_1 = require("../common/logger2");
const dbLogger = (0, logger2_1.getLogger)("db");
function getDataTableName() {
    return config_1.equipRankCfg.tableName;
}
let QnmEquipRank = new BaseModelClass(getDataTableName());
if (config.BingQiPuDbConfig) {
    const connection = mysql.createPool(config.BingQiPuDbConfig);
    const dbClient = {
        execute: function (sql, dbNode) {
            if (config.testCfg.db_debug) {
                dbLogger.info({ sql }, "ExecuteSql");
            }
            return new Promise((resolve, reject) => {
                connection.query(sql, function (err, results, fields) {
                    if (err) {
                        if (err) {
                            dbLogger.error({ err: err, sql: sql }, "ExecuteSqlError");
                        }
                        reject(err);
                    }
                    else {
                        resolve(results);
                    }
                });
            });
        }
    };
    QnmEquipRank = new BaseModelClass(getDataTableName(), null, { db: dbClient });
}
exports.default = QnmEquipRank;
//# sourceMappingURL=QnmEquipRank.js.map