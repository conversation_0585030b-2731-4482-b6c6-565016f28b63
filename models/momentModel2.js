"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MomentModel = exports.MomentModelClass = void 0;
const baseModel2_1 = require("./baseModel2");
const PyqMomentModel = require("./PyqMoments");
class MomentModelClass extends baseModel2_1.BaseModelClass {
    constructor() {
        super("pyq_moment");
    }
}
exports.MomentModelClass = MomentModelClass;
MomentModelClass.ShowMomentCols = PyqMomentModel.BASIC_MOMENT_COLS;
exports.MomentModel = new MomentModelClass();
//# sourceMappingURL=momentModel2.js.map