/**
 * Created by <PERSON><PERSON><PERSON> on 16-12-21.
 */

let _ = require("lodash");
const { clazzLogger } = require("../pyq-server/logger");
let BaseModelClass = require("./BaseModelClass");
let PyqMomentTopic = new BaseModelClass("pyq_topic_moment");
const logger = clazzLogger("models/PyqTopicMoment");


PyqMomentTopic.checkTopicBySubject = function (subject) {
  let PyqTopic = require('../models/PyqTopic');

  return PyqTopic.getTopicIdBySubject(subject)
    .then(function (topicId) {
      if(topicId) {
        return topicId;
      } else {
        return Promise.reject({errorType: "TopicSubjectNotFound", topicSubject: subject});
      }
    })
};

PyqMomentTopic.addToTopic = function (subject, momentId) {
  let PyqMoment = require("../models/PyqMoments");
  return PyqMomentTopic.checkTopicBySubject(subject)
    .then(function (topicId) {
      return PyqMoment.findById(momentId, ["RoleId"]).then(function (m) {
        if (m) {
          return PyqMomentTopic.create({ TopicId: topicId, MomentId: momentId, RoleId: m.RoleId });
        }
      });
    })
    .catch((err) => {
      logger.warn({ err, subject, momentId }, "AddToTopicFailed");
      return null;
    });
};

PyqMomentTopic.removeFromTopic = function (subject, momentId) {
  return PyqMomentTopic.checkTopicBySubject(subject).then(function (topicId) {
    if(topicId && momentId) {
      return PyqMomentTopic.deleteByCondition({TopicId:topicId, MomentId:momentId});
    }
  })
};

PyqMomentTopic.create = function (props) {
  let now = Date.now();
  props = _.defaults({CreateTime:now}, props);
  return BaseModelClass.prototype.create.bind(PyqMomentTopic)(props);
};

module.exports = PyqMomentTopic;