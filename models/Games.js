var Games = module.exports = {};
var QNRoleInfos = require('./QNRoleInfos');
var QNMRoleInfos = require('./QNMRoleInfos');
var QNRoleFriends = require('./QNRoleFriends');
var _ = require('lodash');
var Promise = require('bluebird');
var QNRoleJobs = require('./QNRoleJobs');
var QNServers = require('./QNServers');
var PyqProfile = require('./PyqProfile');

Games.GameIds = {
  QN: 8001,
  QNM: 8100,
  TONGREN: 1
};

Games.GameNames= {
  QN: "qn",
  QNM: "qnm",
  TONGREN: "tongren"
};

GamesTable = [
  {ID: Games.GameIds.QN, Name: "qn", ChineseName:"倩女幽魂端游"},
  {ID: Games.GameIds.QNM, Name: "qnm", ChineseName:"倩女幽魂手游"},
  {ID: Games.GameIds.TONGREN, Name: "tongren", ChineseName: "同人"}
];

Games.getRoleInfo = function(gameId, roleId) {
  if(gameId === Games.GameIds.QN) {
    return QNRoleInfos.findByRoleId(roleId)
    .then(QNRoleInfos.fillRoleInfo);
  } else if (gameId === Games.GameIds.QNM){
  }
};

Games.getGameIdByGameName = function(gameName) {
  var game = _.find(GamesTable, {Name:gameName});
  if(game) {
    return game.ID;
  } else {
    return null;
  }
};

Games.getGameIdByNameForce = function(gameName) {
  var game = _.find(GamesTable, {Name:gameName});
  if(game) {
    return Promise.resolve(game.ID);
  } else {
    return Promise.reject({errorType:"GameNotExist", msg:"游戏不存在！"});
  }
};

Games.findByGameName = function(gameName) {
  return _.find(GamesTable, {Name:gameName});
};


Games.getRoleInfos = function(gameName, userName, userId) {
  var QNDataCenter = require('./QNDataCenter');
  var BindRoles = require('./BindRoles');
  if(gameName === Games.GameNames.QN) {
    return QNDataCenter.QNRoleInfos.findByUserName(userName)
      .then(function (roleInfos) {
        var roleIds = _.map(roleInfos, 'RoleId');
        return BindRoles.correctBindRoles(userId, roleIds, Games.GameIds.QN)
          .then(function () {
            return roleInfos;
          });
      })
      .then(function(roleInfos) {
        return Games.getRoleInfosWithBindType(gameName, roleInfos);
      });
  } else if (gameName === Games.GameNames.QNM){
  }
};

Games.getRoleInfosWithBindType = function(gameName, roleInfos) {
  var BindRoles = require('./BindRoles');
  return Promise.map(roleInfos, function(roleInfo) {
    return Games.getBindInfoByRoleId(gameName, roleInfo.RoleId).then(function(bindInfo) {
      var bindType = BindRoles.BindTypes.NO_BIND;
      if(bindInfo) {
        bindType = BindRoles.BindTypes.NORMAL_BIND;
        if(bindInfo.Type == BindRoles.RoleTypes.MAIN) {
          bindType = BindRoles.BindTypes.MAIN_ROLE_BIND;
        }
      }
      roleInfo.BindType = bindType;
      return roleInfo;
    });
  });
};

Games.getUserBindRoles = function(gameName, userId) {
  var BindRoles = require('./BindRoles');
  var gameId = Games.getGameIdByGameName(gameName);
  if(!gameId) {
    return Promise.reject({errorType:"GameNotExist", msg:"游戏不存在!"});
  } else {
    return BindRoles.findByUserIdAndGameId(userId, gameId);
  }
};

Games.getRoleFriendsByRoleIds = function(gameName, roleIds) {
  if(gameName === Games.GameNames.QN) {
    return QNRoleInfos.findByUserName(userName);
  } else if (gameName === Games.GameNames.QNM){
  }
};

Games.getFriendRoleInfosByRoleIds = function(gameName, roleIds) {
  if(gameName === Games.GameNames.QN) {
    return QNRoleFriends.getFriendsRoleInfoIds(roleIds).then(QNRoleInfos.findByRoleIds);
  } else if (gameName === Games.GameNames.QNM){
    return PyqProfile.getFriendsRoleInfoIds(roleIds).then(function (roleIds) {
      return QNMRoleInfos.findByRoleIds(roleIds);
    })
  }
};

Games.getMainRole = function(gameName, userId) {
  var BindRoles = require('./BindRoles');
  if(gameName === Games.GameNames.QN) {
    return BindRoles.getUserMainRoleInGame(userId, Games.GameIds.QN);
  } else if (gameName === Games.GameNames.QNM){
    return BindRoles.getUserMainRoleInGame(userId, Games.GameIds.QNM);
  }
};

Games.getUsersWithMainRole = function(gameName, users, embedKey) {
  return Promise.map(users, function(user) {
    return Games.getUserWithMainRole(gameName, user, embedKey);
  });
};

Games.getUserWithMainRole = function(gameName, user, embedKey) {
  return Games.getMainRole(gameName, user.ID).then(function(mainRoleInfo) {
    if(embedKey) {
      if(mainRoleInfo) {
        mainRoleInfo.isMainRole = true;
      }
      user[embedKey] = mainRoleInfo;
    } else {
      user.mainRoleInfo = mainRoleInfo;
    }
    return user;
  });
};


Games.getGroupedServers = function(gameName) {
  var toGroupList = function (groupServers) {
    var groupList = [];
    for(var key in groupServers) {
      groupList.push({group:key, servers: groupServers[key]});
    }
    return groupList;
  };
  if(gameName === Games.GameNames.QN) {
    return require('../service/qn/server/list').getGroup().then(toGroupList);
  } else if (gameName === Games.GameNames.QNM){
    return require('../service/qnm/server/list').getGroup().then(toGroupList);
  }
};


Games.getRoleJobs = function(gameName) {
  if(gameName === Games.GameNames.QN) {
    return QNRoleJobs.findAll();
  } else if (gameName === Games.GameNames.QNM){
    var jobHash =require('../common/data').qnm.JobHash
    var roleJobs = [];
    Object.keys(jobHash).forEach(function (jobId) {
      roleJobs.push({id: jobId, name: jobHash[jobId]});
    });
    return roleJobs;
  }
};

Games.searchRoleInfoByRoleName = function(gameName, roleName) {
  if(gameName === Games.GameNames.QN) {
    return QNRoleInfos.findByRoleNameLike(roleName);
  } else if (gameId === GameIds.QNM){
  }
};

Games.searchBindRoleInfoByRoleName = function(gameName, roleName, pagination) {
  if(gameName === Games.GameNames.QN) {
    return QNRoleInfos.findByBindRoleNameLike(roleName, pagination);
  } else if (gameName === Games.GameNames.QNM){
    return QNMRoleInfos.findByBindRoleNameLike(roleName, pagination);
  }
};

Games.getBindInfoByRoleId = function(gameName, roleId) {
  var BindRoles = require('./BindRoles');
  return Games.getGameIdByNameForce(gameName)
    .then(function(gameId) {
      return BindRoles.findByGameIdAndRoleId(gameId, roleId);
    });
};

Games.getBindInfosByRoleIds = function(gameName, roleIds) {
  return Promise.map(roleIds, _.partial(Games.getBindInfoByRoleId, gameName));
};

Games.findBindRoleIds = function(gameName) {
  var BindRoles = require('./BindRoles');
  return Games.getGameIdByNameForce(gameName)
    .then(function(gameId) {
      var query = BindRoles.scope().select('RoleId').where('GameId', gameId);
      return BindRoles.executeByQuery(query);
    }).then(function(bindInfos) {
      return _.map(bindInfos, 'RoleId');
    });
};

Games.fuzzySearchRoleInfos = function(gameName, searchOptions) {
  if(gameName === Games.GameNames.QN) {
    return QNRoleInfos.fuzzySearch(searchOptions);
  } else if (gameName === Games.GameNames.QNM){
    return QNMRoleInfos.fuzzySearch(searchOptions);
  }
};

Games.getRoleInfoByGameNameAndRoleId = function(gameName, roleId) {
  return Games.getGameIdByNameForce(gameName)
    .then(function(gameId) {
      return Games.getRoleInfo(gameId, roleId);
    });
};
