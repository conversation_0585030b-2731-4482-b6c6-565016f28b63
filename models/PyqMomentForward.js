"use strict";
/**
 * Created by <PERSON><PERSON><PERSON> on 2017/4/13.
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
const errorCodes_1 = require("../pyq-server/errorCodes");
const logger_1 = require("../pyq-server/logger");
const models_1 = require("../pyq-server/models");
const momentLotteryAttendService_1 = require("../pyq-server/services/momentLotteryAttendService");
const momentModel_1 = require("./momentModel");
const InformModel = require("./PyqInform");
const BaseModelClass = require("./BaseModelClass");
const _ = require("lodash");
const util_1 = require("../common/util");
const momentTagCfgModel_1 = require("../pyq-server/models/momentTagCfgModel");
const momentTagModel_1 = require("../pyq-server/models/momentTagModel");
const util2_1 = require("../common/util2");
const logger = (0, logger_1.clazzLogger)("models.momentForward");
class MomentForwardModelClass extends BaseModelClass {
    constructor(tableName) {
        super(tableName);
    }
    /**
     * 创建转发动态并写入数据库
     * @param roleId 角色ID
     * @param text 动态文本
     * @param originTags 原始标签
     * @param forwardMomentId 被转发的动态ID
     * @param originMomentId 原始动态ID
     * @param originRoleId 原始动态作者ID
     * @returns 创建的动态ID
     */
    createForwardMoment(roleId, text, originTags, forwardMomentId, originMomentId, originRoleId) {
        return __awaiter(this, void 0, void 0, function* () {
            // 1. 过滤抽奖标签
            const forwardTags = momentTagCfgModel_1.default.filterLotteryTagIds(originTags);
            // 2. 创建动态基本信息
            let insertProps = {
                RoleId: roleId,
                Text: text,
                CreateTime: Date.now(),
            };
            if (forwardTags.length > 0) {
                const forwardTagsStr = forwardTags.join(",");
                insertProps.TagIds = forwardTagsStr;
            }
            // 3. 插入动态记录
            const newMomentId = yield momentModel_1.MomentModel2.insert(insertProps);
            // 4. 并行处理转发记录和通知
            yield Promise.all([
                this.insert({
                    RoleId: roleId,
                    MomentId: newMomentId,
                    ForwardId: forwardMomentId,
                    OriginId: originMomentId,
                }),
                this.addInform(roleId, { RoleId: originRoleId }, newMomentId),
            ]);
            // 5. 插入标签
            if (forwardTags.length > 0) {
                const serverId = (0, util2_1.getServerIdFromRoleId)(roleId);
                yield momentTagModel_1.MomentTagModel.add({ ID: newMomentId, RoleId: roleId }, serverId, forwardTags);
            }
            // 6. 处理抽奖参与逻辑
            yield (0, momentLotteryAttendService_1.handlePlayerAttendMomentLottery)(roleId, originMomentId, originRoleId, 4 /* Forward */, JSON.stringify({ forwardId: newMomentId }), new Date());
            return newMomentId;
        });
    }
    addInform(roleId, forwardMoment, newMomentId) {
        return __awaiter(this, void 0, void 0, function* () {
            const res = yield InformModel.add({
                Type: InformModel.InformTypes.FORWARD_MOMENT,
                RoleId: roleId,
                TargetId: forwardMoment.RoleId,
                ObjectId: newMomentId,
            });
            logger.info({ roleId, forwardMoment, newMomentId, res }, "AddForwardMomentInformOk");
            return res;
        });
    }
    /**
     * 检查角色是否处于保护模式
     * @param roleIds 需要检查的角色ID或ID数组
     * @param context 日志上下文信息
     * @throws 如果任一角色处于保护模式，抛出错误
     */
    checkRoleProtectMode(roleIds, context) {
        return __awaiter(this, void 0, void 0, function* () {
            const roleIdArray = Array.isArray(roleIds) ? roleIds : [roleIds];
            const roleMinors = yield models_1.RoleMinorModel.find({ roleId: roleIdArray });
            if (roleMinors === null || roleMinors === void 0 ? void 0 : roleMinors.some((minor) => minor.protectMode === 1)) {
                logger.warn(Object.assign(Object.assign({}, context), { roleMinors }), "RoleMinorProtectMode");
                yield (0, errorCodes_1.BussError)(errorCodes_1.errorCodes.MomentPlayerInProtectMode);
            }
        });
    }
    reForwardMoment(roleId, text, forwardMomentId, forwardOriginId) {
        return __awaiter(this, void 0, void 0, function* () {
            // 1. 查询原始动态和转发动态
            const rows = yield momentModel_1.MomentModel2.find({ ID: [forwardMomentId, forwardOriginId], Status: 0 }, { cols: ["ID", "RoleId", "TagIds"] });
            // 2. 验证必须存在两个有效动态
            if (rows.length !== 2) {
                logger.info({ forwardMomentId, forwardOriginId, rows, roleId, text }, "ReForwardMomentEmpty");
                yield (0, errorCodes_1.BussError)(errorCodes_1.errorCodes.MomentNotFound);
            }
            // 3. 检查角色保护模式
            const roleIds = _.map(rows, "RoleId");
            yield this.checkRoleProtectMode(roleIds, { forwardMomentId, forwardOriginId, roleId, text });
            // 4. 创建新动态
            const originMoment = _.find(rows, { ID: forwardOriginId });
            const originTags = (0, util_1.csvStrToIntArray)(originMoment.TagIds);
            const newMomentId = yield this.createForwardMoment(roleId, text, originTags, forwardMomentId, forwardOriginId, originMoment.RoleId);
            // 5. 返回结果
            const resp = {
                momentId: newMomentId,
                forwardId: forwardMomentId,
                originId: forwardOriginId,
            };
            return resp;
        });
    }
    forwardOriginMoment(roleId, text, forwardMomentId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // 1. 查询原始动态
                const momentRecord = yield momentModel_1.MomentModel2.findOne({ ID: forwardMomentId, Status: 0 }, ["ID", "RoleId", "TagIds"]);
                if (!momentRecord) {
                    logger.warn({ forwardMomentId, roleId, text }, "ForwardOriginMomentNotFound");
                    yield (0, errorCodes_1.BussError)(errorCodes_1.errorCodes.MomentNotFound);
                }
                // 2. 检查保护模式
                yield this.checkRoleProtectMode(momentRecord.RoleId, { forwardMomentId, roleId, text });
                // 3. 创建新动态
                const originTags = (0, util_1.csvStrToIntArray)(momentRecord.TagIds);
                const newMomentId = yield this.createForwardMoment(roleId, text, originTags, forwardMomentId, forwardMomentId, momentRecord.RoleId);
                // 4. 返回结果
                const resp = {
                    momentId: newMomentId,
                    forwardId: forwardMomentId,
                    originId: forwardMomentId,
                };
                return resp;
            }
            catch (err) {
                if (err instanceof Error) {
                    logger.error({ err, roleId, forwardMomentId, text }, "ForwardOriginMomentError");
                }
                else {
                    logger.warn({ err, roleId, forwardMomentId, text }, "ForwardOriginMomentBizError");
                }
                throw err;
            }
        });
    }
    forwardMoment(roleId, forwardMomentId, text) {
        return __awaiter(this, void 0, void 0, function* () {
            const forwardOriginId = yield this.findOriginId(forwardMomentId);
            // 首次转发的情况
            if (!forwardOriginId) {
                return this.forwardOriginMoment(roleId, text, forwardMomentId);
            }
            // 再次转发的情况
            return this.reForwardMoment(roleId, text, forwardMomentId, forwardOriginId);
        });
    }
    findOriginId(momentId) {
        return __awaiter(this, void 0, void 0, function* () {
            const r = yield this.findOne({ MomentId: momentId }, ["OriginId"]);
            if (!r) {
                return 0;
            }
            else {
                return r.OriginId;
            }
        });
    }
    getForwardInfo(momentId) {
        return __awaiter(this, void 0, void 0, function* () {
            const originMomentId = yield this.findOriginId(momentId);
            if (originMomentId > 0) {
                const originMoment = yield momentModel_1.MomentModel2.findNormalById(originMomentId);
                return originMoment;
            }
            return null;
        });
    }
    /**
     * 获取转发动态的原始动态信息
     * @param momentIds 动态ID数组
     * @returns 返回一个对象，键为动态ID，值为对应的原始动态信息
     */
    getForwardOriginMomentsHash(momentIds) {
        return __awaiter(this, void 0, void 0, function* () {
            // 如果传入的动态ID数组为空，直接返回空对象
            if (momentIds.length === 0) {
                return {};
            }
            // 查询转发关系，获取每个动态ID对应的原始动态ID
            const forwardRelations = yield this.find({ MomentId: momentIds }, { cols: ["MomentId", "OriginId"] });
            // 将转发关系转换为动态ID到原始动态ID的映射
            const momentIdToOriginId = forwardRelations.reduce((acc, relation) => {
                acc[relation.MomentId] = relation.OriginId;
                return acc;
            }, {});
            // 提取所有原始动态ID
            const originMomentIds = forwardRelations.map((relation) => relation.OriginId);
            // 查询原始动态信息
            const originMoments = yield momentModel_1.MomentModel2.find({ ID: originMomentIds }, { cols: momentModel_1.BASIC_MOMENT_COLS });
            // 将原始动态信息转换为ID到动态记录的映射
            const idToMoments = originMoments.reduce((acc, moment) => {
                acc[moment.ID] = moment;
                return acc;
            }, {});
            // 遍历传入的动态ID数组，填充结果对象
            return momentIds.reduce((acc, momentId) => {
                const originMomentId = momentIdToOriginId[momentId];
                if (originMomentId && idToMoments[originMomentId]) {
                    acc[momentId] = Object.assign({}, idToMoments[originMomentId]);
                }
                return acc;
            }, {});
        });
    }
    getForwardCount(momentIds) {
        return __awaiter(this, void 0, void 0, function* () {
            const resp = {};
            if (_.isEmpty(momentIds)) {
                return resp;
            }
            for (const mId of momentIds) {
                const count = yield this.getMomentForwardCount(mId);
                resp[mId] = count;
            }
            return resp;
        });
    }
    getMomentForwardCount(momentId) {
        return __awaiter(this, void 0, void 0, function* () {
            const query = this.scope()
                .count("MomentId as ForwardCount")
                .from(this.tableName + " as f")
                .innerJoin(momentModel_1.MomentModel2.tableName + " as m", "f.MomentId", "m.ID")
                .where(function () {
                this.orWhere("f.ForwardId", momentId).orWhere("f.OriginId", momentId);
            })
                .where("m.Status", 0);
            const rows = (yield this.executeByQuery(query));
            if (rows && rows.length > 0) {
                return rows[0].ForwardCount;
            }
            return 0;
        });
    }
    getPreviousForwards(momentId, queryLimit, textLimit) {
        return __awaiter(this, void 0, void 0, function* () {
            textLimit = textLimit || 200;
            queryLimit = queryLimit || 50;
            let queryCount = 0;
            const result = [];
            let totalText = "";
            let curMomentId = momentId;
            while (curMomentId && queryCount < queryLimit) {
                const forward = yield this.findOne({ MomentId: curMomentId }, ["ID", "ForwardId", "OriginId"]);
                if (!forward) {
                    break;
                }
                //查询到了根节点
                if (forward.ForwardId === forward.OriginId) {
                    break;
                }
                queryCount += 1;
                curMomentId = forward.ForwardId;
                const parentMoment = yield momentModel_1.MomentModel2.findNormalById(curMomentId, ["ID", "Text", "RoleId"]);
                if (parentMoment && parentMoment.Text) {
                    if (totalText.length + parentMoment.Text.length > textLimit) {
                        break;
                    }
                    else {
                        totalText += parentMoment.Text;
                    }
                }
                if (parentMoment) {
                    result.push(parentMoment);
                }
            }
            return result;
        });
    }
}
const MomentForwardModel = new MomentForwardModelClass("pyq_moment_forward");
module.exports = MomentForwardModel;
//# sourceMappingURL=PyqMomentForward.js.map