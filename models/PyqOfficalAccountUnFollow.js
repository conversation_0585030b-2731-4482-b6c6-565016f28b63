"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OfficialAccountUnFollow = void 0;
const constants_1 = require("../common/constants");
const BaseModelClass = require("./BaseModelClass");
class OfficialAccountUnFollowClass extends BaseModelClass {
    constructor() {
        super(constants_1.TABLE_NAMES.PyqOfficialAccountUnFollow);
    }
    isUnFollow(roleId, targetId) {
        return __awaiter(this, void 0, void 0, function* () {
            let record = yield this.findOne({ RoleId: roleId, TargetId: targetId }, ["ID"]);
            return record && record.ID;
        });
    }
    getUnFollowIRelationID(roleId, targetId) {
        return __awaiter(this, void 0, void 0, function* () {
            let record = yield this.findOne({ RoleId: roleId, TargetId: targetId }, ["ID"]);
            return record === null || record === void 0 ? void 0 : record.ID;
        });
    }
    getUnFollowIds(roleId) {
        return __awaiter(this, void 0, void 0, function* () {
            let rows = yield this.find({ RoleId: roleId }, { cols: ["TargetId"] });
            return rows.map((r) => r.TargetId);
        });
    }
    filterFollowed(roleId, targetIds) {
        return __awaiter(this, void 0, void 0, function* () {
            let rows = yield this.find({ RoleId: roleId, targetId: targetIds }, { cols: ["TargetId"] });
            let unFollowed = new Set(rows.map((r) => r.TargetId));
            let followed = targetIds.filter((r) => !unFollowed.has(r));
            return followed;
        });
    }
}
exports.OfficialAccountUnFollow = new OfficialAccountUnFollowClass();
//# sourceMappingURL=PyqOfficalAccountUnFollow.js.map