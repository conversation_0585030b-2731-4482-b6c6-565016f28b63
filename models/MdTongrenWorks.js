let _ = require("lodash");
let Users = require('./Users');

let BaseModelClass = require('./BaseModelClass');
let MdTongrenWorks = _.extend(new BaseModelClass("md_tongren_works"));
let { getRedis } = require('../common/redis');

/* 作品类型 */
const WORK_TYPE = {
    cos: 1, // COS
    video: 2, // 视频
    audio: 3, // 音乐
    novel: 4, // 小说
    illustration: 5, // 插画
    comic: 6 // 漫画
};

/* 作品类型映射表 */
const WORK_TYPE_MAP = [
    {ID: WORK_TYPE.cos, NAME: 'COS'},
    {ID: WORK_TYPE.video, NAME: '视频'},
    {ID: WORK_TYPE.audio, NAME: '音乐'},
    {ID: WORK_TYPE.novel, NAME: '小说'},
    {ID: WORK_TYPE.illustration, NAME: '插画'},
    {ID: WORK_TYPE.comic, NAME: '漫画'}
];

/* 作品状态 */
const WORK_STATUS = {
    Auditing: 0, // 审核中
    NORMAL: 1, // 审核成功
    DELETED: -1, // 已删除
    REJECT: -2, // 审核失败
};

/* 作品是否置顶 */
const WORK_TOP_STATUS = {
    NORMAL: 0, // 正常
    TOP: 1 // 置顶
};

/* 作品展示设置 */
const WORK_CONFIG = {
    workMaxPageSize: 20 // 作品的最大分页
};

/* 热度计算参数 */
const HOT_PARAMS = {
    likeScore: 10, // 点赞的分数
    commentScore: 20, // 评论的分数
    topScore: 1000000, // 置顶分数
    scoreRatio: 6, // 实际分数所占比重
    interactRatio: 0.4 // 点赞/评论的分数所占比重
};

/* Redis缓存参数 */
const CACHE_PARAMS = {
    EXPIRE_TIME: 5 * 60,
    DEFAULT_CACHE_PAGE: 10
};

MdTongrenWorks.WORK_TYPE = WORK_TYPE;
MdTongrenWorks.WORK_STATUS = WORK_STATUS;
MdTongrenWorks.WORK_TOP_STATUS = WORK_TOP_STATUS;
MdTongrenWorks.WORK_TYPE_MAP = WORK_TYPE_MAP;
MdTongrenWorks.WORK_CONFIG = WORK_CONFIG;

module.exports = MdTongrenWorks;

// 新增投稿
MdTongrenWorks.createWork = function(workInfo) {
    return MdTongrenWorks.create({
        'Type': workInfo.type,
        'Title': workInfo.title,
        'Description': workInfo.description,
        'CoverImageUrl': workInfo.coverImageUrl,
        'AttachmentList': workInfo.attachmentList,
        'AuthorId': workInfo.userId,
        'CreateTime': workInfo.createTime,
        'Status': workInfo.status,
        'TagList': workInfo.tagList
    });
};

/**
 * 获取作品列表
 * @param {Object} filterOpt 过滤条件
 * @param {Object} selectOpt 字段
 * @param {Boolean} isCurUser 是否是当前登录用户
 */
MdTongrenWorks.getWorksList = function(filterOpt, selectOpt, isCurUser) {
    let workType = filterOpt.workType;
    let authorId = filterOpt.authorId;
    let workStatus = filterOpt.workStatus;
    let pagination = {
        page: filterOpt.page,
        pageSize: filterOpt.pageSize
    };
    let sortKeyword = ""; // 排序关键词

    switch (filterOpt.sortType) {
        case "hot":
            sortKeyword = "Hot";
            break;
        default:
            sortKeyword = "CreateTime";
            break;
    }

    let query = this.scope()
        .orderBy(sortKeyword, 'desc')
        .select(selectOpt);

    if(workType) {
        query = query.where('Type', workType);
    }

    if(authorId) {
        query = query.where('AuthorId', authorId);
    }

    if(workStatus && workStatus !== WORK_STATUS.NORMAL) {
        query = query.where('Status', workStatus);
    }

    if(isCurUser) {
        query = query.where('Status', 'not in', [WORK_STATUS.DELETED]);
    } else {
        query.where('Status', WORK_STATUS.NORMAL);
    }

    return MdTongrenWorks.queryWithPagination(query, pagination);
};

// 获取作品的字段
MdTongrenWorks.getWorkSelectOpt = function(isCurUser) {
    let selectOpt = ['ID', 'Type', 'Title', 'Description', 'CoverImageUrl', 'AttachmentList', 'LikeCount', 'CommentCount', 'AuthorId', 'TagList', 'CreateTime', 'Status', 'Hot'];
    return selectOpt;
};

// 获取全部的作品列表
MdTongrenWorks.getWorksListOfAll = function(filterOpt, isCurUser) {
    let selectOpt = this.getWorkSelectOpt(false);
    filterOpt = _.defaults({workStatus: WORK_STATUS.NORMAL}, filterOpt);
    return this.getWorksList(filterOpt, selectOpt, isCurUser);
};

// 获取用户的作品列表
MdTongrenWorks.getWorksListByUserId = function(filterOpt, isCurUser) {
    let selectOpt = this.getWorkSelectOpt(isCurUser);
    return this.getWorksList(filterOpt, selectOpt, isCurUser);
};

// 获取作品详情信息
MdTongrenWorks.getWorkDetail = function(workId, isCurUser) {
    let selectOpt = this.getWorkSelectOpt(isCurUser);
    let query = this.scope()
        .select(selectOpt)
        .where('ID', workId);
    if(!isCurUser) {
        query = query.where('Status', WORK_STATUS.NORMAL);
    } else {
        query = query.where('Status', 'not in', [WORK_STATUS.DELETED]);
    }
    return this.executeByQuery(query)
        .then(data => {
            return data[0];
        });
};

// 获取作品的作者ID
MdTongrenWorks.getAuthorIdByWorkId = function(workId) {
    let selectOpt = ['AuthorId'];
    return this.findById(workId, selectOpt).then(data => {
        if(!_.isEmpty(data) && _.isObject(data)) {
            return data.AuthorId;
        } else {
            return null;
        }
    });
};

// 获取作品的点赞数
MdTongrenWorks.getLikeCountByWorkId = function(workId) {
    let selectOpt = ['LikeCount'];
    return this.findById(workId, selectOpt).then(data => {
        return data.LikeCount;
    });
};

// 设置作品的点赞数
MdTongrenWorks.setLikeCountByWorkId = function(workId, likeCount) {
    let condition = {
        'ID': workId
    };
    let props = {
        'LikeCount': likeCount
    };
    return this.updateByCondition(condition, props);
};

// 获取作品的评论数
MdTongrenWorks.getCommentCountByWorkId = function(workId) {
    let selectOpt = ['CommentCount'];
    return this.findById(workId, selectOpt).then(data => {
        return data.CommentCount;
    });
};

// 设置作品的评论数
MdTongrenWorks.setCommentCountByWorkId = function(workId, commentCount) {
    let condition = {
        'ID': workId
    };
    let props = {
        'CommentCount': commentCount
    };
    return this.updateByCondition(condition, props);
};

// 根据搜索标题和说明，获取作品ID集合
MdTongrenWorks.getWorkIdsBySearch = function(keyword) {
    let query = this.scope()
        .where('Title', 'like', `%${keyword}%`)
        .orWhere('Description', 'like', `%${keyword}%`)
        .select('ID');
    return this.executeByQuery(query)
        .then(data => {
            return _.map(data, 'ID');
        });
};

// 根据作品ID获取作品信息列表
MdTongrenWorks.getWorksListByIds = function(workIds, filterOpt) {
    let selectOpt = this.getWorkSelectOpt(false);
    let pagination = {
        page: filterOpt.page,
        pageSize: filterOpt.pageSize
    };
    let query = this.scope()
        .where('ID', 'in', workIds)
        .andWhere('Status', WORK_STATUS.NORMAL)
        .select(selectOpt)
        .orderBy('CreateTime', 'desc');

    if(filterOpt.work_type) {
        query = query.andWhere('Type', filterOpt.work_type);
    }

    return this.queryWithPagination(query, pagination);
};

// 根据用户ID集合获取作品ID集合
MdTongrenWorks.getWorkIdsByUserIds = function(userIds) {
    let query = this.scope()
        .whereIn('AuthorId', userIds)
        .select('ID');

    return this.executeByQuery(query)
        .then(data => {
            return _.map(data, 'ID');
        });
};

// 更新作品信息
MdTongrenWorks.updateWorkInfoWhenAudit = function(workInfo) {
    let condition = {
        'ID': workInfo.ID
    };
    let updatePropKeys = ['CoverImageUrl', 'AttachmentList', 'Score', 'ScoreComment', 'IsTop', 'Status', 'AuditAdminName'];
    let props = {};
    _.forEach(updatePropKeys, (key) => {
        let val = workInfo[key];
        if(val || _.isNumber(val)) {
            props[key] = val;
        }
    });
    return this.updateByCondition(condition, props);
};

// 删除作品
MdTongrenWorks.deleteWork = function(workId) {
    let condition = {
        ID: workId
    };
    let props = {
        Status: WORK_STATUS.DELETED
    };
    return this.updateByCondition(condition, props);
};

/**
 * 热度计算: Hot=(点赞数 * 10 + 评论数 * 20) * 0.4 + 实际分数 * 6 + 置顶 * 1000000
 * @param {Int} workId 作品ID
 * @param {Object} options 热度计算参数对象，属性的可选值有：like 点赞  comment 评论  top 置顶  score 实际分数
 */
MdTongrenWorks.updateHotScore = function(workId, options) {
    let deltaScore = 0;
    _.forEach(options, (value, key) => {
        let scoreUnit = 0;
        switch(key) {
            case 'like':
                scoreUnit = HOT_PARAMS.likeScore * HOT_PARAMS.interactRatio;
                break;
            case 'comment':
                scoreUnit = HOT_PARAMS.commentScore * HOT_PARAMS.interactRatio;
                break;
            case 'top':
                scoreUnit = HOT_PARAMS.topScore;
                break;
            case 'score':
                scoreUnit = HOT_PARAMS.scoreRatio;
                break;
            default:
                break;
        }
        value = value || 0;
        deltaScore += scoreUnit * value;
    });
    // 获取当前的热度值
    return MdTongrenWorks.findById(workId, 'Hot')
        .then(data => {
            // 计算当前热度值
            let hotScore = data.Hot || 0;
            hotScore += deltaScore;
            hotScore = _.ceil(_.max([0, hotScore]));
            return hotScore;
        })
        .then(hotScore => {
            // 设置热度值
            let condition = {
                ID: workId
            };
            let props = {
                Hot: hotScore
            };
            return this.updateByCondition(condition, props)
                .then(() => {
                    return hotScore;
                });
        });
};

/**
 * 获取用户在某个时间范围内，评分低于某个分值的投稿数量
 */
MdTongrenWorks.getContributeTimesMonthlyByUserId = function(userId, maxScore, startTime, endTime) {
    let query = this.scope()
        .whereBetween('CreateTime', [startTime, endTime])
        // .whereNotIn('Status', [WORK_STATUS.DELETED, WORK_STATUS.REJECT])
        .andWhere('AuthorId', userId)
        .andWhere('Score', '<', maxScore)
        .count('* as ContributeTimes');
    return this.executeByQuery(query);
};

/**
 * 获取作品列表缓存的Key
 */
MdTongrenWorks.getWorksListCacheKeyName = function(workType, sortType) {
    let workTypeName = _.find(WORK_TYPE_MAP, {ID: workType}).NAME;
    let CUR_WORKS_KEY = `md_tongren_works:${workTypeName}:` + sortType;
    return CUR_WORKS_KEY;
};

/**
 * 获取全部作品列表
 */
MdTongrenWorks.getListOfAllFromCache = function(filterOpt) {
    let page = filterOpt.page,
        pageSize = filterOpt.pageSize,
        sortType = filterOpt.sortType,
        workType = filterOpt.workType,
        CUR_WORKS_KEY = MdTongrenWorks.getWorksListCacheKeyName(workType, sortType);

    if(page > CACHE_PARAMS.DEFAULT_CACHE_PAGE) {
        // 超出默认缓存页数的，直接读数据库
        return MdTongrenWorks.getWorksListOfAll(filterOpt);
    } else {
        return getRedis().ttlAsync(CUR_WORKS_KEY)
            .then(ttl => {
                if(ttl <= 0) {
                    // 写缓存
                    return MdTongrenWorks.cacheWorkList(sortType, workType);
                } else {
                    return CUR_WORKS_KEY;
                }
            })
            .then(() => {
                // 读缓存
                let start = (page - 1) * pageSize;
                let stop = start + pageSize - 1;
                return getRedis().zcardAsync(CUR_WORKS_KEY)
                    .then(function (total) {
                        stop = stop < total ? stop: total - 1;
                        return getRedis().zrevrangeAsync(CUR_WORKS_KEY, start, stop);
                    })
                    .then(worksList => {
                        return _.map(worksList, work => {
                            return JSON.parse(work);
                        });
                    });
            });
    }
};

/**
 * 将作品列表写入缓存
 */
MdTongrenWorks.cacheWorkList = function(sortType, workType) {
    let pageSize = WORK_CONFIG.workMaxPageSize,
        expireTime = CACHE_PARAMS.EXPIRE_TIME,
        cachePage = CACHE_PARAMS.DEFAULT_CACHE_PAGE,
        CUR_WORKS_KEY = MdTongrenWorks.getWorksListCacheKeyName(workType, sortType),
        CUR_WORKS_IDS_KEY = CUR_WORKS_KEY + ':ids',
        CUR_SCORE_FIELD;

    switch (sortType) {
        case 'hot':
            CUR_SCORE_FIELD = 'Hot';
            break;
        default:
            CUR_SCORE_FIELD = 'CreateTime';
            break;
    }

    let storeListOpt = {
        page: 1,
        pageSize: pageSize * cachePage,
        sortType: sortType,
        workType: workType
    };
    return MdTongrenWorks.getWorksListOfAll(storeListOpt, false)
        .then(worksList => {
            // 缓存作品IDs
            let workIds = _.map(worksList, 'ID');
            return getRedis().delAsync(CUR_WORKS_IDS_KEY)
                .then(() => {
                    if(!_.isEmpty(workIds)) {
                        let addArray = _.concat(CUR_WORKS_IDS_KEY, workIds);
                        return getRedis().saddAsync.apply(getRedis(), addArray);
                    }
                })
                .then(() => {
                    return getRedis().expireAsync(CUR_WORKS_IDS_KEY, expireTime);
                })
                .then(() => {
                    return worksList;
                });
        })
        .then(worksList => {
            // 缓存作品列表
            return getRedis().delAsync(CUR_WORKS_KEY)
                .then(() => {
                    if(!_.isEmpty(worksList)) {
                        let addArray = [];
                        _.forEach(worksList, work => {
                            let score = work[CUR_SCORE_FIELD] || 0;
                            addArray.push(score);
                            addArray.push(JSON.stringify(work));
                        });
                        addArray = _.concat(CUR_WORKS_KEY, addArray);
                        return getRedis().zaddAsync.apply(getRedis(), addArray);
                    }
                })
                .then(() => {
                    return getRedis().expireAsync(CUR_WORKS_KEY, expireTime);
                })
                .then(() => {
                    return CUR_WORKS_KEY;
                });
        });
};

/**
 * 删除作品列表缓存
 */
MdTongrenWorks.clearListCache = function(workType, workId) {
    let HOT_WORKS_KEY = MdTongrenWorks.getWorksListCacheKeyName(workType, 'hot'),
        NEW_WORKS_KEY = MdTongrenWorks.getWorksListCacheKeyName(workType, 'new'),
        HOT_WORKS_IDS_KEY = HOT_WORKS_KEY + ':ids',
        NEW_WORKS_IDS_KEY = NEW_WORKS_KEY + ':ids';
    return getRedis().sismemberAsync(HOT_WORKS_IDS_KEY, workId)
        .then(isMember => {
            if(isMember) {
                // 更新热门列表缓存
                return MdTongrenWorks.cacheWorkList('hot', workType);
            }
        })
        .then(() => {
            return getRedis().sismemberAsync(NEW_WORKS_IDS_KEY, workId)
                .then(isMember => {
                    if(isMember) {
                        // 更新最新列表缓存
                        return MdTongrenWorks.cacheWorkList('new', workType);
                    }
                });
        });
};