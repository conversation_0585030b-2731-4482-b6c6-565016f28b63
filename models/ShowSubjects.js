var BaseModelClass = require('./BaseModelClass');
var ShowSubjects = module.exports = new BaseModelClass("md_show_subject");
var ShowTopics =  require('./ShowTopics');
var _ = require('lodash');

ShowSubjects.create = function(props) {
  props = _.assign({CreateTime: Date.now()}, props);
  return BaseModelClass.prototype.create.call(this, props);
};

ShowSubjects.listShowTopics = function(subjectId, paganation, orderBy) {
  var query = ShowTopics.scope()
    .where('SubjectId', subjectId)
    .where('Status', ShowSubjects.Statuses.NORMAL)
    .orderBy(orderBy, 'desc');
  query = ShowTopics.allAuditPassedQuery(query);
  return ShowTopics.queryWithPagination(query, paganation);
};

ShowSubjects.listHot = function (pagination) {
  return ShowSubjects.listNew(pagination);
};

ShowSubjects.listNew = function (pagination) {
  var query = ShowSubjects.normalScope()
    .orderBy('CreateTime', 'desc');
  return ShowSubjects.queryWithPagination(query, pagination);
};

ShowSubjects.searchByTitleLike = function (str, pagination) {
  var query = ShowSubjects.normalScope()
    .where('Title', 'like', `%${ShowSubjects.escapeLikeStr(str)}%`)
    .orderBy('CreateTime', 'desc');
  return ShowSubjects.queryWithPagination(query, pagination)
};

ShowSubjects.getSubjectWithHot = function (subject) {
  var query = ShowTopics.normalScope().where('SubjectId', subject.ID).sum('Hot as Hot');
  return ShowTopics.executeByQuery(query).then(function (r) {
    return _.assign(subject, {Hot: _.first(r).Hot || 0});
  });
};

ShowSubjects.getHottestTopics = function (subject, size) {
  var query = ShowTopics.normalScope()
    .where('SubjectId', subject.ID)
    .orderBy('Hot', 'desc')
    .limit(size);
  return ShowTopics.executeByQuery(query);
};
