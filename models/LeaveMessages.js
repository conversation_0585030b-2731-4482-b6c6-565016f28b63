var BaseModelClass = require('./BaseModelClass');
var LeaveMessages = module.exports = new BaseModelClass("md_message");
var _ = require('lodash');

LeaveMessages.Statuses = {
  NORAML: 0,
  DELETED: -1
};

LeaveMessages.create = function(props) {
  props = _.assign({CreateTime: Date.now()}, props);
  return BaseModelClass.prototype.create.call(this, props);
};

LeaveMessages.getCountByUserId = function(userId) {
  return this.count({
    TargetId: userId,
    Status: LeaveMessages.Statuses.NORAML
  });
};
