/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/7/10
 */

const BaseModelClass = require('./BaseModelClass');
const QnFDRecord = module.exports = new BaseModelClass('qn_fdrecord');
const { getRedis } = require('../common/redis');
const _ = require('lodash') ;
const Promise = require('bluebird');
const util = require('../common/util');
const dataUtil = require('../common/data');
const logger = require('../common/logger');
const SeverList = require('../service/qn/server/list.js');
const co = util.co;

const FIRST_DOWN_BOSS_MAP = {
  // 寇岛首杀 1-9
  1: '菜理子',
  2: '海河童龙之介',
  3: '阴阳师安倍',
  4: '幕府将军足利',
  5: '徐海',

  // 京师榜 10-19, 20-29
  10: '多尔衮',
  11: '代善和多铎',
  12: '攻城战',
  13: '骑战',
  14: '后金大炮',
  15: '后金战车',
  16: '皇太极'
};
const RANK_BOSS_TYPES = [ 5, 16, 26 ];

QnFDRecord.getBossName = function (bossType) {
  var suffix = '';
  if (bossType >= 20) {
    suffix = '（疯狂）';
    bossType -= 10;
  }
  else if (bossType >= 10) {
    suffix = '（英雄）';
  }
  return FIRST_DOWN_BOSS_MAP[bossType] + suffix;
};

function getBossType(bossName) {
  for (var key in FIRST_DOWN_BOSS_MAP) {
    var boss = FIRST_DOWN_BOSS_MAP[key];
    if (bossName.indexOf(boss) != -1) {
      var type = parseInt(key, 10);
      if (bossName.indexOf('疯') != -1) { // 疯狂模式
        type += 10;
      }
      return type;
    }
  }
}
function getBossTypeList(rankType, mode) {
  if (rankType !== 'jingshi') {
    return [1, 2, 3, 4, 5];
  }

  var bossTypes =  [10, 11, 12, 13, 14, 15, 16];  // 英雄模式
  if (mode > 0) {   // 疯狂模式
    _.map(bossTypes, (t) => {
      return t + mode * 10;
    });
  }
  return bossTypes;
}
function getBossTypeByRankId(rankId) {
  let type = (rankId + '').substr(2, 2);
  return parseInt(type, 10);
}

function getWeekRankKey(serverId, bossType, weekNum) {
  serverId = serverId || 'all';
  weekNum = weekNum || util.getMondayZero();
  return `qn:fdweekrank:${bossType}:${serverId}:${weekNum}`;
}

QnFDRecord.addFirst = function (data, option) {
  let bossType = getBossType(data.BossName);
  if (!bossType) {
    return Promise.resolve('Invalid_BossName');
  }

  let serverId = data.ServerId;
  if (serverId < 10) {
    return Promise.resolve('Invalid_ServerId');
  }

  let guildId = data.GuildId;
  let fdTime = dataUtil.qn.fmtTime(data.FDTime);
  let weekNum = util.getMondayZero(fdTime);

  // 周排行
  if (_.includes(RANK_BOSS_TYPES, bossType)) {
    let line = JSON.stringify([bossType, guildId, serverId, fdTime]);

    // 本周排行全服榜
    let key2 = getWeekRankKey('all', bossType, weekNum);
    getRedis().existsAsync(key2).then(function (exist) {
      exist || getRedis().expireAsync(key2, 86400 * 14);    // 设置过期时间
      return getRedis().rpushAsync(key2, line);
    }).catch(err => logger.error(err));
  }

  // 历史排行
  return QnFDRecord.findOne({
    BossType: bossType,
    GuildId: guildId
  }).then((exist) => {
    if (exist) {
      if (option && option.force) {
        return QnFDRecord.updateByCondition({
          BossType: bossType,
          GuildId: guildId
        }, {
          WeekNum: weekNum,
          PublishTime: fdTime
        });
      }
    } else {
      return QnFDRecord.insert({
        BossType: bossType,
        GuildId: guildId,
        ServerId: serverId,
        WeekNum: weekNum,
        PublishTime: fdTime
      });
    }
  });
};

QnFDRecord.getAllFDInfo = function (serverId, rankType, mode, limit) {
  let bossTypes = getBossTypeList(rankType, mode);
  let queries = [];
  let bossArr = [];
  _.each(bossTypes, (type) => {
    bossArr.push(FIRST_DOWN_BOSS_MAP[type]);

    let query = QnFDRecord.scope()
        .select(['ServerId', 'GuildId', 'BossType', 'PublishTime'])
        .where('BossType', type)
        .orderBy('PublishTime')
        .limit(limit);
    if (serverId) {
      query.where('ServerId', serverId);
    }
    queries.push(QnFDRecord.executeByQuery(query));
  });

  Promise.all(queries).then((rankList) => {
    var rows = [],
        colNum = bossTypes.length,
        rowNum = 0;
    for (var i = 0, l = colNum; i < l; i++) {
      rowNum = Math.max(rowNum, rankList[i].length);
    }
    for (i = 0, l = rowNum; i < l; i++) {
      var col = [];
      for (var j = 0, k = colNum; j < k; j++) {
        col.push(rankList[j]);
      }
      rows.push(col);
    }

    return { title: bossArr, rows: rows };
  });
};

QnFDRecord.getAllFDRecords = function (serverId, rankId, limit) {
  let bossType = getBossTypeByRankId(rankId);
  let ModelManager = require('./ModelManager');
  let model = ModelManager.getSlaveModel(QnFDRecord);
  let query = model.scope()
      .select(['ServerId', 'GuildId', 'BossType', 'PublishTime'])
      .where('BossType', bossType)
      .orderBy('PublishTime')
      .limit(limit);
  return Promise.method(function () {
    if (serverId) {
      return SeverList.getFullServerIds(serverId).then( serverIds => {
        query.whereIn('ServerId', serverIds);
      });
    }
  })().then(() => {
    return model.executeByQuery(query);
  });
};

// 本周排行
QnFDRecord.getWeekFDRecords = function (serverId, rankId) {
  let bossType = getBossTypeByRankId(rankId);
  let key = getWeekRankKey('all', bossType);
  return getRedis().lrangeAsync(key, 0, -1).then(list => {
    return _.map(list, row => {
      return JSON.parse(row);
    });
  }).then(ranking => {
    if (!serverId) {
      return ranking;
    }

    return SeverList.getFullServerIds(serverId).then(serverIds => {
      return _.filter(ranking, row => {
        return _.includes(serverIds, parseInt(row[2], 10));
      });
    });
  });
};