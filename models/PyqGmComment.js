const BaseModelClass = require('./BaseModelClass')
const PyqGmComment = new BaseModelClass('pyq_gm_comment')
const _ = require('lodash')

PyqGmComment.insert = function insertComment (props) {
  const insertProps = _.defaults(props, {CreateTime: Date.now()})
  return BaseModelClass.prototype.insert.call(this, insertProps)
  .then(insertInfo => {
    return _.get(insertInfo, 'insertId')
  })
}

module.exports = PyqGmComment
