var QNDataCenter = {};
var request = require('request');
var Promise = require('bluebird');
var _ = require('lodash');
var Config = require('../common/config');
var QNRoleInfos = require('./QNRoleInfos');


QNDataCenter.QNRoleInfos = {
  findByUserName: function(userName) {
    return QNDataCenter.request({
      url: `/listcharacter/account/${userName}`,
      timeout: 15000
    })
      .then(function(roleInfos) {
        return _.map(roleInfos, function(roleInfo) {
          roleInfo = {
            RoleId: roleInfo.Id,
            RoleName: roleInfo.Name,
            Level:roleInfo.Grade,
            ServerId: roleInfo.ServerId,
            JobId: roleInfo.Profession,
            CreateTime: roleInfo.CreateTime,
            Gender: roleInfo.Gender,
            UserName: userName
            // VipLevel: roleInfo.VipLevel
          };
          return roleInfo;
        });
      })
      .then(function(roleInfos) {
        QNRoleInfos.syncToDB(roleInfos); // Async sync data to DB
        return QNRoleInfos.getRoleInfosWithServerAndJob(roleInfos);
      });
  },
};

QNDataCenter.queryRank = function (serverId, rankId) {
  return QNDataCenter.request({
    url: `/queryrank/serverid/${serverId}/rankid/${rankId}`,
  }).then(ranking => {
    ranking = _.filter(ranking, r => {
      return r.Id > 0;
    });
    return ranking;
  });
};


QNDataCenter.request = function(options) {
  options.url = Config.QN_API.dataCenter + "/api" + options.url;
  options = _.assign({json:true, timeout: 5000}, options);
  return new Promise(function(resolve, reject) {
    return request(options, function(err, response, body) {
      if(err) {
        reject(err);
      } else {
        if(body.code || body.ErrorCode) {
          body.extra = {msg: "Request Data Center failed!"};
          reject(body);
        } else {
          resolve(body.Result);
        }
      }
    });
  });
};

module.exports = QNDataCenter;
