﻿/**
 * Created by <PERSON><PERSON><PERSON> on 16-9-8.
 */
var _ = require('lodash');
var BaseModelClass = require('./BaseModelClass');
var QnmRoleAble = require('./mixins/QnmRoleAble');
var QNMRoleInfoService = require('../service/qnm/role/info');

const UsedNameStatuses = {
  PUBLIC: 0,
  HIDE: -1
};

var QNMRoleInfos = _.extend(new BaseModelClass("qnm_roleinfo"), QnmRoleAble, {
  PrimaryKey: 'RoleId',

  getByRoleId: function (roleId) {
    return QNMRoleInfoService.getInfo({roleid: roleId})
      .then(function (roleInfo) {
        roleInfo.Server = {
          name: roleInfo.Server
        };
        return roleInfo;
      });
  },

  findByRoleIds: function (roleIds) {
    var self = this;
    return self.where({RoleId: roleIds})
      .then(self.getRoleInfosWithServerAndJob)
      .then(self.Serializers.default);
  },

  findByBindRoleNameLike: function (roleName, pagination) {
    var self = this;
    var query = self.scope()
      .table(self.tableName + " as r")
      .rightJoin('md_bindrole' + " as b", "b.RoleId", "r.RoleId")
      .where('r.RoleName', 'like', `%${self.escapeLikeStr(roleName)}%`);
    return self.queryWithPagination(query, pagination)
        .then(self.getRoleInfosWithServerAndJob)
        .then(self.Serializers.default)
  },

  getRoleInfosWithServerAndJob: function (roleInfos) {
    var qnm = require('../common/data').qnm;
    var JobHash = qnm.JobHash;
    return qnm.getServerHash()
      .then(function (serverHash) {
      roleInfos.forEach(function (roleInfo) {
        if(roleInfo) {
          roleInfo.Server = serverHash[roleInfo.ServerId] || null;
          roleInfo.Job = JobHash[roleInfo.JobId] || null;
        }
      });
      return roleInfos;
    })
  },

  fuzzySearch: function (option) {
    var QNMRoleInfos = this;
    var serverId = option.serverId;
    var jobIds = option.jobIds;
    var levelRangeIds = option.levelRangeIds;
    var paganation = option.paganation;
    var query = this.scope().where('ServerId', serverId)
      .join("md_bindrole", "md_bindrole.RoleId", "qnm_roleinfo.RoleId")
      .where("md_bindrole.GameId", 8100)
      .select('md_bindrole.RoleId', 'RoleName', 'ServerId', 'Level', 'JobId', 'GangId');
    if(!_.isEmpty(jobIds)) {
      query = query.whereIn('JobId', jobIds);
    }
    query.where(function(){
      var self = this;
      _.forEach(levelRangeIds, function(id, index) {
        var levelRange = QNMRoleInfos.LevelRanges.findById(id);
        if(levelRange.start && levelRange.end) {
          self= self.orWhereBetween('Level', [levelRange.start, levelRange.end]);
        } else if(levelRange.start && (!levelRange.end)) {
          self= self.orWhere('Level', '>=', levelRange.start);
        }
      });
    });

    return QNMRoleInfos.queryWithPagination(query, paganation)
      .then(QNMRoleInfos.getRoleInfosWithServerAndJob)
      .then(QNMRoleInfos.Serializers.default);
  },

  getByRoleIds: function (roleIds, cols) {
    var query = QNMRoleInfos.scope().whereIn('RoleId', roleIds).select(cols);
    return QNMRoleInfos.executeByQuery(query);
  },


  Serializers: {
    default: function (record) {
      var attrList = ['CreateTime', 'Gender', 'JobId', 'RoleId', 'RoleName', 'ServerId', 'Level', 'Server', 'Job', 'GangId'];
      var pickAttr = function (obj) {
        return _.pick(obj, attrList);
      };
      if (_.isArray(record)) {
        return _.map(record, pickAttr);
      } else {
        return pickAttr(record);
      }
    }
  },

  LevelRanges: {
    dataTable: [
      {ID: 0, start: 1, end: 69},
      {ID: 1, start: 70, end: 89},
      {ID: 2, start: 90, end: 109},
      {ID: 3, start: 110, end: 129},
      {ID: 4, start: 130, end: null},
    ],

    findById: function (id) {
      id = parseInt(id);
      return _.find(this.dataTable, {ID: id});
    },
  },
  
  isMale: function (r) {
    return r.Gender == 0;
  },

  filterBySameServer: function (roleIds, filterRoleId) {
    return QNMRoleInfos.findOne({RoleId: filterRoleId}, ['ServerId'])
      .then(function (role) {
        if(role && role.ServerId) {
          var query = QNMRoleInfos.scope().where('ServerId', role.ServerId).whereIn('RoleId', roleIds).select('RoleId');
          return QNMRoleInfos.executeByQuery(query);
        }
      }).then(function (rows) {
        return _.map(rows, 'RoleId');
      });
  },

  getJobAvatar: function (roleInfo) {
    var genderStr = "female";
    if(QNMRoleInfos.isMale(roleInfo)) {
      genderStr = "male";
    }
    if(roleInfo.JobId && genderStr) {
      return `https://hi-163-qnm.nosdn.127.net/avatars/job_${roleInfo.JobId}_${genderStr}.jpg`;
    } else {
      return null;
    }
  },

  create: function (props) {
    props = _.defaults(props, {CreateTime: Date.now(), UpdateTime: Date.now()});
    return BaseModelClass.prototype.create.call(QNMRoleInfos, props);
  },

  UsedNameStatuses: UsedNameStatuses,

  getJobById: function (jobId) {
    return require('../common/data').qnm.JobHash[jobId] || null;
  },

  getGuildRoleIds: function (guildId) {
    const maxGuildPlayerLimit = 150
    return QNMRoleInfos.find({GangId: _.toString(guildId)}, {cols: ['RoleId'], limit: maxGuildPlayerLimit})
    .then(rows => {
      return _.map(rows, _.property('RoleId'))
    })
  }

});

module.exports = QNMRoleInfos;
