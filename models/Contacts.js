var BaseModelClass = require('./BaseModelClass');
var Contacts = new BaseModelClass("md_contacts");
var Users= require('./Users');
var Notifications = require('./Notifications');
var _ = require('lodash');
var Errors = require('../common/errors');
var logger = require('../common/logger');
var Promise = require('bluebird');

Contacts.Fields = {
  ID: "ID",
  UserId: "UserId",
  TargetId: "TargetId",
  Status: "Status",
};

Contacts.Statuses = {
  Followed: 0,
  CancelFollowed: -1
};

Contacts.FollowType = {
  NO_FOLLOW: -1,
  FOLLOW_ME: 0,
  ME_FOLLOW: 1,
  FOLLOW_EACH: 2,
  IS_SELF: 3
};

Contacts.isFollowing = function(userId, otherUserId) {
  if(userId && otherUserId) {
    return this.exists({UserId: userId, TargetId: otherUserId, Status: Contacts.Statuses.Followed});
  } else {
    return Promise.resolve(false);
  }
};

Contacts.follow = function(userId, otherUserId) {
  if(userId === otherUserId) {
    var error = new Errors.UserFollowSelf(userId);
    return Promise.reject(error);
  } else {
    return Contacts.findOne({UserId: userId, TargetId: otherUserId})
      .then(function(userRelation) {
        if(_.isEmpty(userRelation)) {
          return Contacts.create({
            UserId: userId,
            TargetId: otherUserId,
            Status: Contacts.Statuses.Followed
          });
        } else if(userRelation.Status === Contacts.Statuses.Followed) {
          throw new Errors.UserAlreadyFollowed(otherUserId);
        } else if(userRelation.Status === Contacts.Statuses.CancelFollowed) {
          return Contacts.updateById(userRelation.ID, {
            Status: Contacts.Statuses.Followed
          });
        } else {
          logger.error(`Contacts id:${userRelation.ID} is invalid!`);
        }
      });
  }
};

Contacts.cancelFollow = function(userId, otherUserId) {
  return Contacts.isFollowing(userId, otherUserId)
    .then(function(isFollowing) {
      if(!isFollowing) {
        return Promise.reject({errorType:"cancelFollowInvalid", msg: "关注后才能取消关注"});
      } else {
        var query = Contacts.scope()
          .where('UserId', userId)
          .where('TargetId', otherUserId)
          .update('Status', Contacts.Statuses.CancelFollowed);
        return Contacts.executeByQuery(query).then(function(record) {
          Notifications.softDeleteFollowEvent(userId, otherUserId);
          return record;
        });
      }
    });
};

Contacts.isFriend = function(userId, otherUserId) {
  return Promise.join(
    Contacts.isFollowing(userId, otherUserId),
    Contacts.isFollowing(otherUserId, userId),
    function(isUserFollowOther, isOtherFollowUser) {
      return isUserFollowOther && isOtherFollowUser;
    }
  );
};

Contacts.getUserFollowCount = function(userId) {
  return this.count({UserId: userId, Status: Contacts.Statuses.Followed});
};

Contacts.getFollowUserCount = function(userId) {
  return this.count({TargetId: userId, Status: Contacts.Statuses.Followed});
};

Contacts.getUserFollowInfo = function(userId) {
  return Promise.props({
    userFollowCount: Contacts.getUserFollowCount(userId),
    followUserCount: Contacts.getFollowUserCount(userId)
  });
};

Contacts.getUsersWithFollowInfo = function(users) {
  return Promise.map(users, function(user) {
    return Contacts.getUserFollowInfo(user.ID).then(function(followInfo) {
      _.assign(user, followInfo);
      return user;
    });
  });
};


Contacts.followScope = function() {
  return this.scope().where({Status: Contacts.Statuses.Followed});
};

Contacts.getUserFollowedRelation = function(userId) {
  var query = this.followScope().where({UserId: userId});
  return this.executeByQuery(query);
};

Contacts.getFollowUserRelation = function(userId) {
  var query = this.followScope().where({TargetId: userId});
  return this.executeByQuery(query);
};

Contacts.getUserIdToFollowType = function(userFollowRelation, followUserRelation) {
  var meFollowUserIds = _.map(userFollowRelation, 'TargetId');
  var followMeUserIds = _.map(followUserRelation, 'UserId');
  var userIds = _.chain(_.concat(meFollowUserIds, followMeUserIds)).uniq().value();
  var userIdToFollowType = _.reduce(userIds, function(hash, userId) {
    var isMeFollow = _.includes(meFollowUserIds, userId);
    var isFollowMe = _.includes(followMeUserIds, userId);
    if(isMeFollow && isFollowMe) {
      hash[userId] = Contacts.FollowType.FOLLOW_EACH;
    } else if(isMeFollow){
      hash[userId] = Contacts.FollowType.ME_FOLLOW;
    } else {
      hash[userId] = Contacts.FollowType.FOLLOW_ME;
    }
    return hash;
  }, {});
  return userIdToFollowType;
};

Contacts.getUserContact = function(userId) {
  return Promise.props({
    userFollowRelation: Contacts.getUserFollowedRelation(userId),
    followUserRelation: Contacts.getFollowUserRelation(userId)
  }).then(function(relations) {
    var userFollowRelation = relations.userFollowRelation;
    var followUserRelation = relations.followUserRelation;
    var userIdToFollowType = Contacts.getUserIdToFollowType(userFollowRelation, followUserRelation);
    var userIds = _.keys(userIdToFollowType);
    return Users.getUsersByIds(userIds)
      .then(Users.Serializers.normalUsers)
      .map(function(user) {
        user.type = userIdToFollowType[user.ID];
        return user;
      });
  });
};

Contacts.getUsersWithFollowType = function(myUserId, users, embededKey) {
  const userIds = _.map(users, u => parseInt(u.ID, 10));
  const curUserId = parseInt(myUserId, 10);
  if(_.isFinite(curUserId)) {
    const filterFollowMeUserIds = Contacts.executeByQuery(
      Contacts.normalScope().select('UserId').where('TargetId', curUserId).whereIn('UserId', userIds)
    ).then(rows => _.map(rows, 'UserId'));
    const filterMeFollowerUserIds  = Contacts.executeByQuery(
      Contacts.normalScope().select('TargetId').where('UserId', curUserId).whereIn('TargetId', userIds)
    ).then(rows => _.map(rows, 'TargetId'));

    return Promise.all([
      filterMeFollowerUserIds,
      filterFollowMeUserIds
    ]).spread((meFollowIds, followMeIds) => {
      _.forEach(users, user => {
        const isMeFollow = _.includes(meFollowIds, user.ID);
        const isFollowMe = _.includes(followMeIds, user.ID);
        user[embededKey || 'Type'] =  Contacts.getFollowTypeByRelation(isMeFollow, isFollowMe);
      });
      return users;
    })
  } else {
    return users;
  }
};

Contacts.getFollowTypeByRelation = function (isAFollowB, isBFollowA) {
  if(isAFollowB && isBFollowA) {
    return Contacts.FollowType.FOLLOW_EACH;
  } else if (isAFollowB) {
    return Contacts.FollowType.ME_FOLLOW;
  } else if(isBFollowA) {
    return Contacts.FollowType.FOLLOW_ME;
  } else {
    return Contacts.FollowType.NO_FOLLOW;
  }
};

Contacts.getUserWithFollowType = function(myUserId, user, embededKey) {
  return Contacts.getFollowType(myUserId, user.ID).then(function(followType) {
    embededKey = embededKey || "Type";
    user[embededKey] = followType;
    return user;
  });
};

Contacts.getFollowType = function(userAId, userBId) {
  if(userAId == userBId) {
    return Promise.resolve(Contacts.FollowType.IS_SELF);
  } else {
    return Promise.join(
      Contacts.isFollowing(userAId, userBId),
      Contacts.isFollowing(userBId, userAId),
      function(isAFollowB, isBFollowA) {
        return Contacts.getFollowTypeByRelation(isAFollowB, isBFollowA)
      }
    );
  }
};

// 获取curUser与otherUsers的关注状态
Contacts.getFollowTypeOfUserIds = function(curUserId, otherUserIds) {
    // 获取curUser与otherUsers的关注状态记录列表
    var userIdSet = _.map(_.concat(otherUserIds, curUserId), x => parseInt(x, 10));
    var query = this.scope()
        .whereIn('TargetId', userIdSet)
        .andWhere('UserId', 'in', userIdSet)
        .andWhere('Status', Contacts.Statuses.Followed)
        .select(['UserId', 'TargetId']);
    return this.executeByQuery(query)
        .then(followRelations => {
            // 判断关注状态
            var userAId = curUserId;
            return _.map(otherUserIds, function (userBId) {
                var isAFollowB = _.some(followRelations, {UserId: userAId, TargetId: userBId});
                var isBFlollowA = _.some(followRelations, {UserId: userBId, TargetId: userAId});

                var followType;
                if(userAId == userBId) {
                    followType = Contacts.FollowType.IS_SELF;
                } else if (isAFollowB && isBFlollowA) {
                    followType = Contacts.FollowType.FOLLOW_EACH;
                } else if (isAFollowB) {
                    followType = Contacts.FollowType.ME_FOLLOW;
                } else if (isBFlollowA) {
                    followType = Contacts.FollowType.FOLLOW_ME;
                } else {
                    followType = Contacts.FollowType.NO_FOLLOW;
                }

                return {
                    TargetId: userBId,
                    FollowType: followType
                };
            });
        });
};

/**
 * 获取用户关注列表
 * @param {Number} userId
 * @return {Array} userIds
 */
Contacts.getUserFriendIds = function (userId) {
  const query = Contacts.normalScope()
  .where('UserId', userId)
  .select('TargetId')
  return Contacts.executeByQuery(query)
  .map(row => _.get(row, 'TargetId'))
}

/**
 * 获取用户粉丝列表
 * @param {Number} userId
 * @return {Array} userIds
 */
Contacts.getUserFollowerIds = function (userId) {
  const query = Contacts.normalScope()
  .where('TargetId', userId)
  .select('UserId')
  return Contacts.executeByQuery(query)
  .map(row => _.get(row, 'UserId'))
}

module.exports = Contacts;
