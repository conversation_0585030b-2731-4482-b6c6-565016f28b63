"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.equipRecordToHtml = exports.getEquipDetails = exports.QnmRoleProp = void 0;
const RoleIdPartitionModel_1 = require("./RoleIdPartitionModel");
const data_1 = require("../common/data");
const util = require("../common/util");
class QnmRolePropClass extends RoleIdPartitionModel_1.RoleIdPartitionModel {
    tableName() {
        return 'qnm_roleprop';
    }
}
exports.QnmRoleProp = new QnmRolePropClass();
function getEquipDetails(roleIds) {
    return __awaiter(this, void 0, void 0, function* () {
        const equipInfos = yield exports.QnmRoleProp.findByRoleIds(roleIds, ['RoleId', 'EquipInfo']);
        return equipInfos.map(row => {
            row.EquipInfo = util.getJsonInfo(row.EquipInfo);
            return row;
        });
    });
}
exports.getEquipDetails = getEquipDetails;
function equipRecordToHtml(equip) {
    const item = data_1.qnm.getEquip(equip.templateid);
    const small = item.small;
    let str = (0, data_1.parseEquipItem)(equip);
    str = (small ? '<img src="' + small + '" /><br/>' : '') + str;
    return { desc: str, small: small, color: item.color };
}
exports.equipRecordToHtml = equipRecordToHtml;
//# sourceMappingURL=QnmRoleProp.js.map