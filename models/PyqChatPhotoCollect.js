/**
 * Created by <PERSON>hen<PERSON> on 2017/5/17.
 */

const BaseModel = require('./BaseModelClass');
const PyqChatPhotoCollect = new BaseModel("pyq_chat_photo_collect");

const MAX_COLLECT_LIMIT = 10; //每个人最多收藏的限制

PyqChatPhotoCollect.add = function (roleId, photoId) {
  return PyqChatPhotoCollect.insert({
    RoleId: roleId,
    PhotoId: photoId,
    CreateTime: Date.now()
  }).then(insertRes => {
    return insertRes.insertId;
  })
};

PyqChatPhotoCollect.list = function (roleId) {
  const query = PyqChatPhotoCollect.scope()
    .select(['ID', 'PhotoId'])
    .where('RoleId', roleId)
    .orderBy('ID')
    .limit(MAX_COLLECT_LIMIT);

  return PyqChatPhotoCollect.executeByQuery(query);
};

PyqChatPhotoCollect.remove = function (roleId, photoId) {
  return PyqChatPhotoCollect.deleteByCondition({RoleId: roleId, PhotoId: photoId})
    .then(info => {
      if(info.affectedRows === 0) {
        return Promise.reject({msg: "没有发现这个图片收藏, photoId: " + photoId})
      } else {
        return info;
      }
    })
};

PyqChatPhotoCollect.removeBatch = function (roleId, photoIds) {
  return PyqChatPhotoCollect.deleteByCondition({RoleId: roleId, PhotoId: photoIds})
};

PyqChatPhotoCollect.getCountByRoleId = function (roleId) {
  return PyqChatPhotoCollect.count({RoleId: roleId});
};

PyqChatPhotoCollect.MAX_COLLECT_LIMIT = MAX_COLLECT_LIMIT;

module.exports = PyqChatPhotoCollect;
