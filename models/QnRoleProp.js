"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QnRoleProp = void 0;
const RoleIdPartitionModel_1 = require("./RoleIdPartitionModel");
class RoleAttr {
    static get AttrList() {
        return ['hp', 'mhit', 'pmiss', 'expfull', 'cor', 'block', 'mfatal',
            'patt', 'mdef', 'mf', 'pdef', 'mp', 'ignoreblock', 'sta', 'int',
            'mmiss', 'matt', 'exp', 'pfatal', 'str', 'anger', 'agi', 'phit'];
    }
    constructor(props) {
        var self = this;
        RoleAttr.AttrList.forEach(function (attr) {
            self[attr] = props[attr];
        });
    }
    static fromJsonStr(str) {
        return new RoleAttr(JSON.parse(str));
    }
}
class RoleAntiAttr {
    static get AttrList() {
        return ['AntiLight', 'AntiTie', 'AntiWind', 'AntiIce', 'AntiMass', 'AntiDizzy', 'AntiPoison', 'AntipFatal',
            'AntiWater', 'AntiFire', 'AntiSilence', 'AntiThunder', 'AntiPetrify', 'AntiBianHu', 'AntiBind', 'AntimFatal',
            'AntiSleep', 'AntiIllusion', 'AntiFreeze'];
    }
    constructor(props) {
        var self = this;
        RoleAntiAttr.AttrList.forEach(function (attr) {
            self[attr] = props[attr];
        });
    }
    static fromJsonStr(str) {
        return new RoleAntiAttr(JSON.parse(str));
    }
}
class QnRolePropClass extends RoleIdPartitionModel_1.RoleIdPartitionModel {
    tableName() {
        return 'qn_roleprop';
    }
    getRoleProp(roleId) {
        return __awaiter(this, void 0, void 0, function* () {
            let cols = ['RoleId', 'EquipInfo', 'PropertyInfo', 'OtherInfo'];
            let record = yield this.findByRoleId(roleId, cols);
            if (record) {
                return {
                    roleId: roleId,
                    attrs: RoleAttr.fromJsonStr(record.PropertyInfo),
                    antiAttrs: RoleAntiAttr.fromJsonStr(record.OtherInfo),
                    equipInfo: JSON.parse(record.EquipInfo)
                };
            }
            else {
                return null;
            }
        });
    }
}
exports.QnRoleProp = new QnRolePropClass();
//# sourceMappingURL=QnRoleProp.js.map