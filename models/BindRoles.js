var BaseModelClass = require('./BaseModelClass');
var Games = require('./Games');
var Users = require('./Users');
var BindRoles = module.exports = new BaseModelClass("md_bindrole");
var _ = require('lodash');
var logger = require('../common/logger');

BindRoles.RoleTypes = {
  NORMAL:0,
  MAIN: 2
};

BindRoles.PrimaryKey = ['RoleId', 'GameId'];

BindRoles.getUserMainRoleInGame = function(userId, gameId) {
  return BindRoles.findOne({UserId: userId, GameId: gameId, Type: BindRoles.RoleTypes.MAIN})
  .then(function(bindRole) {
    if(bindRole) {
      return Games.getRoleInfo(bindRole.GameId, bindRole.RoleId);
    } else {
      return null;
    }
  });
};

BindRoles.create = function(props) {
  var now = Date.now();
  props = _.assign({BindTime: now, Auths:""}, props);
  return BaseModelClass.prototype.create.call(this, props);
};

BindRoles.bindMainRole = function(userId, gameId, roleId) {
  return BindRoles.bindRole(userId, gameId, roleId, BindRoles.RoleTypes.MAIN);
};

BindRoles.bindRole = function(userId, gameId, roleId, bindType) {
  return this.create({
    UserId: userId,
    GameId: gameId,
    RoleId: roleId,
    Type: bindType
  });
};

BindRoles.findByUserIdAndGameId = function(userId, gameId) {
  return this.where({
    UserId: userId,
    GameId: gameId
  });
};


BindRoles.getBindUsersByRoleIds = function(gameId, roleIds) {
  return BindRoles.where({GameId: Games.GameIds.QN, RoleId: roleIds}).then(function(bindRoles) {
    var userIds = _.chain(bindRoles).map('UserId').compact().uniq().value();
    return Users.getUsersByIds(userIds).then(function(users) {
      return _.map(users, function(user) {
        return _.pick(user, ['Avatar', 'ID', 'Gender', 'City', 'Province', 'Signature', 'UserName']);
      });
    });
  });
};

BindRoles.findByGameIdAndRoleId = function(gameId, roleId) {
  return BindRoles.findOne({GameId: gameId, RoleId: roleId});
};

BindRoles.BindTypes = {
  NO_BIND:  null,
  NORMAL_BIND: 1,
  MAIN_ROLE_BIND: 2
};


BindRoles.getUserIdByMainRoleId = function(gameId, roleId) {
  return BindRoles.findOne({
    GameId: gameId,
    RoleId: roleId,
    Type: BindRoles.RoleTypes.MAIN
  }).then(function(bindRole) {
    return _.get(bindRole, 'UserId');
  });
};

BindRoles.getUserIdByGameIdAndRoleId = function(gameId, roleId) {
  return BindRoles.findOne({
    GameId: gameId,
    RoleId: roleId,
  }).then(function(bindRole) {
    return _.get(bindRole, 'UserId');
  });
};

BindRoles.getUserIdByQnRoleId = function (roleId) {
  return BindRoles.findOne({ GameId: Games.GameIds.QN, RoleId: roleId }, ['UserId'])
    .then(function (bindRole) {
      return _.get(bindRole, 'UserId')
    })
}

BindRoles.unBindUserRoleIdsInGame = function (userId, roleIds, GameId) {
  var query = BindRoles.scope().where('UserId', userId).whereIn('RoleId', roleIds).where('GameId', GameId).del();
  return BindRoles.executeByQuery(query);
};

//解绑所有不是参数中userId和roleIds对应的绑定关系
BindRoles.correctBindRoles = function (userId, roleIds, gameId) {
  const Promise = require('bluebird');

  const findUserIdBindWrongRoleIds = () => {
    const query = BindRoles.scope()
    .where('UserId', userId)
    .whereNotIn('RoleId', roleIds)
    .where('GameId', gameId);
    return BindRoles.executeByQuery(query);
  };

  const findRoleIdBindWrongUserId = () => {
    const query = BindRoles.scope()
    .whereIn('RoleId', roleIds)
    .whereNot('UserId', userId)
    .where('GameId', gameId);
    return BindRoles.executeByQuery(query);
  };

  const deleteBindRole = bindRole => {
    const query = BindRoles.scope()
    .where('UserId', bindRole.UserId)
    .where('RoleId', bindRole.RoleId)
    .where('GameId', bindRole.GameId)
    .del();
    return BindRoles.executeByQuery(query);
  };

  return Promise.all([
    findUserIdBindWrongRoleIds(),
    findRoleIdBindWrongUserId()
  ]).reduce((rows, curRows) => {
    return _.concat(rows, curRows)
  }, []).then(unBindRoles => {
    if(!_.isEmpty(unBindRoles)) {
      logger.add("correct_bind_role", JSON.stringify(unBindRoles));
      return Promise.mapSeries(unBindRoles, row => deleteBindRole(row))
    }
  });
};

BindRoles.replaceQnBinding = function (oldCurRoleId, newRoleId) {
  let oldBindInfo;
  return BindRoles.findByGameIdAndRoleId(Games.GameIds.QN, oldCurRoleId).then(bindInfo => {
    if(!_.isEmpty(bindInfo)) {
      oldBindInfo = bindInfo;
      const newBindInfo = _.assign({}, bindInfo, {RoleId:newRoleId, BindTime: Date.now()});
      return BindRoles.insert(newBindInfo);
    } else {
      return Promise.reject({msg: "oldRoleId is not binding to qn"});
    }
  }).then(info => {
    if(info.affectedRows === 1) {
      return BindRoles.deleteByCondition(oldBindInfo)
    }
  });
};

BindRoles.getUserQnMainRoleBatch = function (userIds) {
  const query = BindRoles.scope()
  .whereIn('UserId', userIds)
  .whereIn('GameId', Games.GameIds.QN)
  .where('Type', BindRoles.RoleTypes.MAIN)
  .select('UserId', 'RoleId');
  return BindRoles.executeByQuery(query);
};

BindRoles.getUserQnMainRoleId = function (userId) {
  const query = BindRoles.scope()
  .where('UserId', userId)
  .whereIn('GameId', Games.GameIds.QN)
  .where('Type', BindRoles.RoleTypes.MAIN)
  .select('RoleId');
  return BindRoles.executeByQuery(query)
  .then(row => _.get(row[0], 'RoleId'));
};

BindRoles.getUserIdByQnMainRoleIds = function (roleIds) {
  const query = BindRoles.scope()
  .whereIn('RoleId', roleIds)
  .where('Type', BindRoles.RoleTypes.MAIN)
  .where('GameId', Games.GameIds.QN)
  .select('UserId');
  return BindRoles.executeByQuery(query)
  .map(row => _.get(row, 'UserId'))
};
