"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.isHealth = void 0;
const DB = require("../common/db");
const SqlBuilder = require("../common/sqlBuilder");
function isHealth() {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const ret = yield DB.execSql(SqlBuilder.raw('select 0').toString(), {});
            if (ret && ret[0]['0'] == 0) {
                return { isHealth: true };
            }
            else {
                return { isHealth: false, msg: `db check res ${JSON.stringify(ret)}` };
            }
        }
        catch (err) {
            return { isHealth: false, msg: err.stack };
        }
    });
}
exports.isHealth = isHealth;
//# sourceMappingURL=DBHealth.js.map