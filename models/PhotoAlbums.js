var BaseModelClass = require('./BaseModelClass');
var Promise = require('bluebird');
var Errors = require('../common/errors');
var { getRedis } = require('../common/redis');
var Contacts = require('./Contacts');
var _ = require('lodash');
var PhotoAlbums = new BaseModelClass("md_photo_album");
module.exports = PhotoAlbums;
var MdEventBus = require('../md-server/eventBus');
var logger = require('../common/logger');
const util = require('../common/util');
const Constants = require('../common/data').Constants;



PhotoAlbums.VisibilityType = {
  TO_ALL: 0,
  TO_FRIEND: 5,
  TO_SELF_ONLY: 10
};

PhotoAlbums.Types = {
  QNM_NORMAL: 0,
  DEFAULT: 1,
  MOMENT: 2,
  AVATAR: 3,
  QN_SCREENSHOT: 4,
  QN_NORMAL: 5,
  SHOW_TOPIC: 6,
};

PhotoAlbums.TypeToName = {
  0: "普通相册",
  1: "默认相册",
  2: "新鲜事相册",
  3: "头像相册",
  4: "新倩女幽魂",
  6: "炫耀相册"
};

PhotoAlbums.Channels = {
  RealityShow:  0, //"真人秀"
  Wow:          1, //"牛图"
  Wallpaper:    2, //"壁纸"
  Doujin :      3, //"精彩同人"
};

PhotoAlbums.Statuses = {
  NORMAL: 0,
  DELETED: -1
};

PhotoAlbums.AUDIT_STATUSES = {
  REJECT: -1,
  AUDITING: 0,
  PASSED: 1,
  EDITOR_PICK: 2,
};


PhotoAlbums.ChannelIds = _.values(PhotoAlbums.Channels);


PhotoAlbums.create = function(props) {
  var now = Date.now();
  props = _.merge({CreateTime:now, UpdateTime: now}, props);
  return BaseModelClass.prototype.create.call(PhotoAlbums, props);
};


PhotoAlbums.getUserAlbums = function(userId, visitorUserId) {
  var isBrowseSelfAlbums = (userId == visitorUserId);
  return PhotoAlbums.createUserDefaultAlbumIfNotExist(userId).then(function() {
    return Promise.props({
      avatarAlbum: PhotoAlbums.getUserAvatarAlbum(userId, isBrowseSelfAlbums),
      momentAlbum: PhotoAlbums.getUserMomentAlbum(userId, isBrowseSelfAlbums),
      qnScreenshotAlbum: PhotoAlbums.getUserQNScreenshotAlbum(userId, isBrowseSelfAlbums),
      showTopicAlbum: PhotoAlbums.getUserShowTopicAlbum(userId, isBrowseSelfAlbums),
      normalAlbums: PhotoAlbums.getUserNormalAlbums(userId, visitorUserId)
    });
  });
};

PhotoAlbums.getUserNormalAlbums = function(userId, visitorUserId) {
  var isBrowseSelfAlbums = (userId == visitorUserId);
  var Contacts = require('../models/Contacts');
  var query = PhotoAlbums.scope()
    .where('UserId', userId)
    .whereIn('Type', [PhotoAlbums.Types.QNM_NORMAL, PhotoAlbums.Types.DEFAULT, PhotoAlbums.Types.QN_NORMAL])
    .where('Status', PhotoAlbums.Statuses.NORMAL);

  return Promise.resolve(isBrowseSelfAlbums)
    .then(function (isBrowseSelfAlbums) {
      if(!isBrowseSelfAlbums) {
        return Contacts.isFriend(visitorUserId, userId)
          .then(function (isFriend) {
            if(isFriend) {
              query = query.whereIn('Visibility', [PhotoAlbums.VisibilityType.TO_ALL, PhotoAlbums.VisibilityType.TO_FRIEND]);
            } else {
              query = query.where('Visibility', PhotoAlbums.VisibilityType.TO_ALL);
            }
          })
      }
    })
    .then(function () {
      return PhotoAlbums.executeByQuery(query);
    })
    .then(function(albums) {
      return Promise.map(albums, function(album) {
        return PhotoAlbums.getAlbumWithCount(album, isBrowseSelfAlbums);
      });
    }).then(function(albums) {
      return Promise.map(albums, PhotoAlbums.getAlbumWithCoverUrlIfNeed);
    }).then(function(albums) {
      return Promise.map(albums, PhotoAlbums.fillLastAddPhotoTime);
    }).then(function(albums) {
      albums = _.orderBy(albums, 'LastAddPhotoTime', 'desc');
      var defaultAlbum = _.find(albums, {Type: PhotoAlbums.Types.DEFAULT});
      _.remove(albums, {Type: PhotoAlbums.Types.DEFAULT});
      return _.concat(defaultAlbum, albums);
    });
};

PhotoAlbums.getAlbumWithCoverUrlIfNeed = function(album) {
  var Photos = require('./Photos');
  if(album.CoverUrl) {
    return Promise.resolve(album);
  } else {
    const query = Photos.scope()
    .where({
      PhotoAlbumID: album.ID,
      Status: Photos.Statuses.NORMAL,
    })
    .whereIn('AuditStatus', [Constants.STATUS_AUDIT_PASS, Constants.STATUS_AUDIT_PICK])
    .select('Url')
    .orderBy("ID", "desc")
    .limit(1);
    return Photos.executeByQuery(query)
    .then(function(rows) {
      var photo = rows[0];
      if(photo) {
        album.CoverUrl = photo.Url;
        return album;
      }
      return album;
    });
  }
};

PhotoAlbums.fillLastAddPhotoTime = function(album) {
  var Photos = require('./Photos');
  return Photos.executeByQuery(Photos.scope().where({
    PhotoAlbumID: album.ID,
    Status: Photos.Statuses.NORMAL,
  }).whereNot('AuditStatus', Photos.AUDIT_STATUSES.REJECT)
  .orderBy("ID", "desc").limit(1))
  .then(function(rows) {
    var photo = rows[0];
    if(photo) {
      album.LastAddPhotoTime = photo.CreateTime;
    } else {
      album.LastAddPhotoTime = album.CreateTime;
    }
    return album;
  });
};

PhotoAlbums.getAlbumWithCount = function(album, isBrowseSelfAlbum) {
  var Photos = require('./Photos');
  var albumId = album.ID;
  return Photos.getCountByAlbumId(albumId, isBrowseSelfAlbum).then(function(count) {
    album.Count = count;
    return album;
  });
};

PhotoAlbums.createUserDefaultAlbumIfNotExist = function(userId) {
  var type = PhotoAlbums.Types.DEFAULT;
  return PhotoAlbums.createUserAlbumByTypeIfNotExsit(userId, type);
};

PhotoAlbums.createUserMomentAlbumIfNotExist = function(userId) {
  var type = PhotoAlbums.Types.MOMENT;
  return PhotoAlbums.createUserAlbumByTypeIfNotExsit(userId, type);
};

PhotoAlbums.createUserAvatarAlbumIfNotExist = function(userId) {
  var type = PhotoAlbums.Types.AVATAR;
  return PhotoAlbums.createUserAlbumByTypeIfNotExsit(userId, type);
};

PhotoAlbums.createQNScreenshotAlbumIfNotExsit = function(userId) {
  var type = PhotoAlbums.Types.QN_SCREENSHOT;
  return PhotoAlbums.createUserAlbumByTypeIfNotExsit(userId, type);
};

PhotoAlbums.createShowTopicAlbumIfNotExist = function (userId) {
  var type = PhotoAlbums.Types.SHOW_TOPIC;
  return PhotoAlbums.createUserAlbumByTypeIfNotExsit(userId, type);
};

/**
 * @private createUserAlbumByTypeIfNotExsit
 *
 * @param {Number} userId
 * @param {Number} type
 * @returns {Object} album
 */
PhotoAlbums.createUserAlbumByTypeIfNotExsit = function(userId, type) {
  return PhotoAlbums.findOne({UserId: userId, Type: type}).then(function(album) {
    if(album) {
      return album;
    } else {
      return PhotoAlbums.create({
        UserId: userId,
        Name: PhotoAlbums.TypeToName[type],
        Type: type
      });
    }
  });
};

PhotoAlbums.getUserDefaultAlbum = function(userId) {
  return this.createUserAlbumByTypeIfNotExsit(userId, PhotoAlbums.Types.DEFAULT);
};

PhotoAlbums.getUserMomentAlbum = function(userId, isUserOwnAlbum) {
  return PhotoAlbums.createUserMomentAlbumIfNotExist(userId).then(function(album) {
    return PhotoAlbums.getAlbumWithCount(album, isUserOwnAlbum);
  }).then(function(album) {
    return PhotoAlbums.getAlbumWithCoverUrlIfNeed(album);
  });
};

PhotoAlbums.getUserAvatarAlbum = function(userId, isUserOwnAlbum) {
  return PhotoAlbums.createUserAvatarAlbumIfNotExist(userId).then(function(album) {
    return PhotoAlbums.getAlbumWithCount(album, isUserOwnAlbum);
  }).then(function(album) {
    return PhotoAlbums.getAlbumWithCoverUrlIfNeed(album);
  });
};

PhotoAlbums.getUserQNScreenshotAlbum = function(userId, isUserOwnAlbum) {
  return PhotoAlbums.createQNScreenshotAlbumIfNotExsit(userId).then(function(album) {
    return PhotoAlbums.getAlbumWithCount(album, isUserOwnAlbum);
  }).then(function(album) {
    return PhotoAlbums.getAlbumWithCoverUrlIfNeed(album);
  });
};

PhotoAlbums.getUserShowTopicAlbum = function (userId, isUserOwnAlbum) {
  return PhotoAlbums.createShowTopicAlbumIfNotExist(userId).then(function(album) {
    return PhotoAlbums.getAlbumWithCount(album, isUserOwnAlbum);
  }).then(function(album) {
    return PhotoAlbums.getAlbumWithCoverUrlIfNeed(album);
  });
};

PhotoAlbums.listPhotos = function(albumId, options) {
  var Photos = require('./Photos');
  var limit = options.perPage;
  var lastCreateTime = options.lastCreateTime;
  var isBrowseSelfAlbum = options.isBrowseSelfAlbum || false;
  var query = Photos.scope().where({Status: Photos.Statuses.NORMAL, PhotoAlbumID: albumId});
  if(isBrowseSelfAlbum) {
    query = query.whereNot('AuditStatus', Photos.AUDIT_STATUSES.REJECT);
  } else {
    query = query.whereIn('AuditStatus', [Constants.STATUS_AUDIT_PASS, Constants.STATUS_AUDIT_PICK]);
  }
  if(lastCreateTime) {
    query = query.where('CreateTime', '<', lastCreateTime);
  }
  query = query.orderBy('CreateTime', 'desc').limit(limit);
  return Photos.executeByQuery(query);
};

PhotoAlbums.isViewByAll = function(album) {
  return album.Visibility === PhotoAlbums.VisibilityType.TO_ALL;
};

PhotoAlbums.isViewByFriend = function(album) {
  return album.Visibility === PhotoAlbums.VisibilityType.TO_FRIEND;
};

PhotoAlbums.isViewBySelfOnly = function(album) {
  return album.Visibility === PhotoAlbums.VisibilityType.TO_SELF_ONLY;
};

PhotoAlbums.hasViewPermission = function(album, userId) {
  var isSelfAlbum = (album.UserId == userId);
  if(isSelfAlbum) {
    return Promise.resolve(true);
  } else {
    if(PhotoAlbums.isViewByAll(album)) {
      return Promise.resolve(true);
    } else if(PhotoAlbums.isViewByFriend(album)) {
      return Contacts.isFriend(userId, album.UserId);
    }
  }
  return Promise.resolve(false);
};

PhotoAlbums.checkViewPermission = function(album, userId) {
  return PhotoAlbums.hasViewPermission(album, userId).then(function(hasPermission) {
    if(!hasPermission) {
      throw new Errors.NoPermissionToViewAlbum(userId, album.ID);
    } else {
      return album;
    }
  });
};

PhotoAlbums.totalPhotoCount = function(albums) {
  return _.sumBy(albums, 'Count');
};

PhotoAlbums.isMomentAlbum = function(album) {
  return album.Type === PhotoAlbums.Types.MOMENT;
};

PhotoAlbums.isAvatarAlbum = function(album) {
  return album.Type === PhotoAlbums.Types.AVATAR;
};

PhotoAlbums.isNormalAlbum = function(album) {
  return album.Type === PhotoAlbums.Types.QNM_NORMAL || album.Type === PhotoAlbums.Types.QN_NORMAL;
};

PhotoAlbums.updateById = function(albumId, props) {
  return PhotoAlbums.findByIdForce(albumId).then(function(album) {
    var canBeRename = PhotoAlbums.canBeRename(album);
    if(props.Name && !canBeRename) {
      throw new Errors.ForbidModify("PhotoAlbums", albumId);
    }
    return album;
  }).then(function(album) {
    return BaseModelClass.prototype.updateById.bind(PhotoAlbums)(albumId, props);
  });
};


PhotoAlbums.canBeRename = function(album) {
  return PhotoAlbums.isNormalAlbum(album);
};

PhotoAlbums.canBeMovedIn = function(album) {
  return PhotoAlbums.isNormalAlbum(album) || album.Type === PhotoAlbums.Types.DEFAULT;
};

PhotoAlbums.isUserOwnAlbum = function(album, userId) {
  userId = parseInt(userId);
  return album.UserId === userId;
};

PhotoAlbums.isUserOwnAlbums = function(albums, userId) {
  return _.every(albums, function(album) {
    return PhotoAlbums.isUserOwnAlbum(album, userId);
  });
};

PhotoAlbums.softDeleteById = function(albumId) {
  var Photos = require('./Photos');
  var album;
  return this.updateById(albumId, {Status:PhotoAlbums.Statuses.DELETED}).then(function(res) {
    album = res;
    return Photos.executeByQuery(
      Photos.scope().where({PhotoAlbumID: albumId}).update({Status: Photos.Statuses.DELETED})
    );
  }).then(function() {
    return album;
  });
};

PhotoAlbums.isRenameConflict = function(album, updateName) {
  return this.exists({UserId:album.UserId, Name: updateName});
};


PhotoAlbums.checkUserOwnAlbum = function(userId, albumId) {
  return PhotoAlbums.findByIdForce(albumId).then(function(album) {
    if(!PhotoAlbums.isUserOwnAlbum(album, userId)) {
      return Promise.reject({errorType: Errors.ErrorTypes.AlbumIdInvalid, msg: "Album owned by other!"});
    } else {
      return album;
    }
  });
};

PhotoAlbums.addPhotoUrlsToMomentAlbum = function(userId, photoUrls) {
  var self = this;
  var Photos = require('./Photos');
  return self.getUserMomentAlbum(userId).then(function(album) {
    var propsList = photoUrls.map(function(url) {
      return {Url: url, PhotoAlbumID: album.ID};
    });
    return Photos.createBatch(propsList);
  });
};

PhotoAlbums.addPhotoUrlsToShowTopicAlbum = function(userId, photoUrls) {
  var self = this;
  var Photos = require('./Photos');
  return self.getUserShowTopicAlbum(userId).then(function(album) {
    var propsList = photoUrls.map(function(url) {
      return {Url: url, PhotoAlbumID: album.ID};
    });
    return Photos.createBatch(propsList);
  });
};

PhotoAlbums.addPhotoUrlToAvatarAlbum = function(userId, url) {
  var self = this;
  var Photos = require('./Photos');
  return self.getUserAvatarAlbum(userId).then(function(album) {
    return Photos.create({Url:url, PhotoAlbumID:album.ID});
  });
};

PhotoAlbums.addPhotoToQNScreenshot = function(userId, url) {
  var Photos = require('./Photos');
  return PhotoAlbums.getUserQNScreenshotAlbum(userId).then(function(album) {
    return Photos.create({Url:url, PhotoAlbumID:album.ID, AuditStatus: PhotoAlbums.AUDIT_STATUSES.PASSED});
  });
};

MdEventBus.on('onAddMoment', function(data) {
  var urls = data.urls;
  var userId = data.userId;
  var Photos = require('./Photos');
  PhotoAlbums.addPhotoUrlsToMomentAlbum(userId,urls).then(function(photo) {
    return Photos.auditPhotos(photo);
  }).catch(function(err) {
    logger.error(err);
  });
});

MdEventBus.on('onChangeAvatar', function(data) {
  var avatar = data.avatar;
  var userId = data.userId;
  var Photos = require('./Photos');
  PhotoAlbums.addPhotoUrlToAvatarAlbum(userId, avatar).then(function(photo) {
    return Photos.auditPhoto(photo);
  }).catch(function(err) {
    logger.error(err);
  });
});


PhotoAlbums.getByLast7Days = function() {
  var self = this;
  var Photos = require('./Photos');
  return Photos.getByLast7Days().then(function(photos) {
    var albumIds = _.uniq(_.map(photos, 'PhotoAlbumID'));
    var query =  self.scope()
      .whereIn('ID', albumIds)
      .whereNot('Status', PhotoAlbums.Statuses.DELETED);
    return self.executeByQuery(query);
  });
};

PhotoAlbums.fillInfoForAlbumWithPhotos = function(album, photos) {
  var Photos = require('./Photos');
  if(_.isEmpty(photos)) return album;
  photos = _.orderBy(photos, 'CreateTime', 'desc');
  var firstAuditPhoto = _.find(photos, photo => {
    return [Constants.STATUS_AUDIT_PASS, Constants.STATUS_AUDIT_PICK].includes(photo.AuditStatus);
  });
  if(firstAuditPhoto) {
    album.LastAddPhotoTime = firstAuditPhoto.CreateTime;
    if(!album.CoverUrl) {
      album.CoverUrl = firstAuditPhoto.Url;
    }
  }
  return album;
};



PhotoAlbums.sortByLastAddPhotoTimeInRedis = function(albums) {
  var sortedAlbumsByLastAddPhotoKey = "sort:albums:by_last_add_photo";
  return getRedis().delAsync(sortedAlbumsByLastAddPhotoKey).then(function() {
    return Promise.map(albums, function(album) {
      return getRedis().zaddAsync(sortedAlbumsByLastAddPhotoKey, album.LastAddPhotoTime, album.ID);
    });
  }).then(function() {
    return sortedAlbumsByLastAddPhotoKey;
  });
};

PhotoAlbums.sortByHotInRedis = function(albums) {
  var sortedAlbumsByHotKey = "sort:albums:by_hot";
  var now = Date.now();
  var servenDaysDuration = 7 * 24 * 3600 * 1000; //ms
  return getRedis().delAsync(sortedAlbumsByHotKey).then(function() {
    return Promise.map(albums, function(album) {
      // 刚上传图片的相册折算为10个新鲜度
      var freshness = (Math.max(0, (servenDaysDuration - Math.max(0, (now - album.LastAddPhotoTime)))) / servenDaysDuration) * 10;
      var score = album.LikesCount + freshness;
      return getRedis().zaddAsync(sortedAlbumsByHotKey, score, album.ID);
    });
  }).then(function() {
    return sortedAlbumsByHotKey;
  });
};

PhotoAlbums.listByEditorPick = function(channelId, option) {
  const MAX_TOP_SIZE = 200;
  var editorPickChannelKey = `editorpick:channels:${channelId}:albums`;
  var curPage = option.curPage || 1;
  var pageSize = option.pageSize || 8;
  var totalPage;

  return getRedis().zrevrangeAsync(editorPickChannelKey, 0, MAX_TOP_SIZE)
  .then(albumIds => {
    //这里做个保底， 原则上可以信任审核后台写的redis key中的相册ID都是合法的
    return PhotoAlbums.filterAlbumIdsByShowInPublic(albumIds);
  }).then(albumIds => {
    const albumIdsList = _.chunk(albumIds, pageSize);
    totalPage = albumIdsList.length;
    return albumIdsList[curPage - 1];
  }).then(albumIds => {
    return PhotoAlbums.findByIds(albumIds, ['ID', 'Name', 'Desc', 'CoverUrl', 'ChannelId', 'CreateTime', 'UserId']);
  }).then(albums => {
    return Promise.mapSeries(albums, album =>  {
      return PhotoAlbums.getAlbumWithCoverUrlIfNeed(album);
    });
  }).then(albums => {
    _.forEach(albums, album => {
      album.CoverUrl = util.toHttps(album.CoverUrl);
    });
    return {
      albums: albums,
      meta: {
        totalPage: totalPage,
        curPage: curPage
      }
    };
  });

};

PhotoAlbums.editorPick = function(channelId, albumId) {
  var editorPickChannelKey = `editorpick:channels:${channelId}:albums`;
  return getRedis().zaddAsync(editorPickChannelKey, Date.now(), albumId);
};

PhotoAlbums.cancelEditorPick= function(channelId, albumId) {
  var editorPickChannelKey = `editorpick:channels:${channelId}:albums`;
  return getRedis().zremAsync(editorPickChannelKey, albumId);
};

PhotoAlbums.tryToTransAuditing = function(ids) {
  ids = _.uniq(ids);
  var query = PhotoAlbums.scope()
    .whereIn('ID', ids)
    .where({AuditStatus: PhotoAlbums.AUDIT_STATUSES.REJECT})
    .update({AuditStatus: PhotoAlbums.AUDIT_STATUSES.AUDITING});

  return PhotoAlbums.executeByQuery(query);
};

PhotoAlbums.tryToTransAuditingWhenMove = function(photos, targetAlbumId) {
  var albumIds = _.map(photos, 'PhotoAlbumID');
  return PhotoAlbums.tryToTransAuditing(albumIds).then(function(updateAlbums) {
    if(!_.isEmpty(updateAlbums)) {
      return PhotoAlbums.updateById(targetAlbumId, {
        AuditStatus: PhotoAlbums.AUDIT_STATUSES.AUDITING
      });
    }
  });
};

PhotoAlbums.filterAlbumIdsByShowInPublic = function(albumIds) {
  const Photos = require('./Photos');
  const AuditStatus = PhotoAlbums.AUDIT_STATUSES;
  const query = PhotoAlbums.scope()
  .from(PhotoAlbums.tableName + ' as a')
  .innerJoin(Photos.tableName + ' as p', 'a.ID', 'p.PhotoAlbumId')
  .distinct('a.Id')
  .whereIn('a.AuditStatus', [AuditStatus.PASSED, AuditStatus.EDITOR_PICK])
  .whereIn('a.ID', albumIds)
  .where('a.Status', 0)
  .where('p.Status', 0)
  .whereIn('p.AuditStatus', [AuditStatus.PASSED, AuditStatus.EDITOR_PICK]);
  return PhotoAlbums.executeByQuery(query).then(rows => {
    const idSet = new Set(_.map(rows, 'Id'));
    // 保持原来albumId的顺序
    return _.filter(albumIds, id => idSet.has(parseInt(id, 10)));
  });
};

PhotoAlbums.filterAlbumIdByShowInPublic = function(albumId) {
  var Photos = require('./Photos');
  var isNormalAlbum = PhotoAlbums.exists({ID:albumId, Status: PhotoAlbums.Statuses.NORMAL, AuditStatus: [PhotoAlbums.AUDIT_STATUSES.PASSED, PhotoAlbums.AUDIT_STATUSES.EDITOR_PICK]});
  var isAlbumContainsAnyValidPhotos = Photos.exists({PhotoAlbumID: albumId, Status: PhotoAlbums.Statuses.NORMAL, AuditStatus: [Constants.STATUS_AUDIT_PASS, Constants.STATUS_AUDIT_PICK]});
  return Promise.join(isNormalAlbum, isAlbumContainsAnyValidPhotos, function(isNormalAlbum, isAlbumContainsAnyValidPhotos) {
    return isNormalAlbum && isAlbumContainsAnyValidPhotos;
  });
};
