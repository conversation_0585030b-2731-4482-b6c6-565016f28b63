/**
 * Created by <PERSON><PERSON><PERSON> on 16-12-13.
 */

const BaseModelClass = require('./BaseModelClass');
const _ = require('lodash');
let PyqEvent = new BaseModelClass("pyq_event");

const Events = {
  SEND_FLOWER: "send_flower"
};

const Types = {
  VISIT_SPACE: 1,
  SEND_GIFT: 2,
  SEND_FLOWER: 3,
};

PyqEvent.create = function (props) {
  let now = Date.now();
  props = _.defaults(props, {PublishTime:now});
  return BaseModelClass.prototype.create.bind(this)(props);
};


PyqEvent.Events = Events;
PyqEvent.Types = Types;

module.exports = PyqEvent;
