/**
 * Created by zhenhua on 2017/5/12.
 */

const BaseModel = require('../models/BaseModelClass');
const PyqSummerLbsGeo = new BaseModel("pyq_summerlbs_geo");
const _ = require('lodash');

module.exports = PyqSummerLbsGeo;

//获取最新的一条插入记录
PyqSummerLbsGeo.getLastRecordByRoleId = function (roleId) {
  const query = PyqSummerLbsGeo.scope()
    .where('RoleId', roleId)
    .orderBy('ID', 'desc')
    .limit(1);
  return PyqSummerLbsGeo.executeByQuery(query).then(reocrds => {
    return _.first(reocrds) || null;
  });
};


FoodTypes = {
  "common": 0,
  "special": 1
}


function getFoodTypeStr (type) {
  return _.invert(FoodTypes)[type];
}

const ActionTypes = {
  "discover": 0,
  "place": 1
};

PyqSummerLbsGeo.discoverActionScope = function discoverActionScope() {
  return PyqSummerLbsGeo.scope().where('ActionType', ActionTypes.discover);
};

PyqSummerLbsGeo.FoodTypes = FoodTypes;
PyqSummerLbsGeo.ActionTypes = ActionTypes;
PyqSummerLbsGeo.getFoodTypeStr = getFoodTypeStr;
