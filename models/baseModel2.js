"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseModelClass = void 0;
const _ = require("lodash");
const bluebird = require("bluebird");
const Constants = require("../common/constants");
const DB = require("../common/db");
const knex = require("knex");
const SqlBuilder = knex({ client: "mysql" });
const util2_1 = require("../common/util2");
const config_1 = require("../pyq-server/common/config");
const Statuses = {
    NORMAL: Constants.Statues.Normal,
    DELETED: Constants.Statues.Deleted,
};
class BaseModelClass {
    constructor(tableName, primaryKey, dbNode, option) {
        option = option || { db: DB };
        this.tableName = tableName;
        this.ID_COLUMN_NAME = primaryKey || "ID";
        this.dbNode = dbNode || "MASTER"; //默认指向MASTER节点
        this.db = option.db;
    }
    getOffsetCondition(query, offset) {
        query.limit(offset.count).orderBy("ID", "DESC");
        if (offset.lastId)
            query.andWhere("ID", "<=", offset.lastId);
        return query;
    }
    getConditionQuery(conditions, initQuery) {
        let query = initQuery || SqlBuilder.table(this.tableName);
        _.forEach(conditions, function (value, key) {
            if (_.isArray(value)) {
                query = query.whereIn(key, value);
            }
            else {
                query = query.where(key, value);
            }
        });
        return query;
    }
    where(conditions, option, dbOpt) {
        option = option || {};
        let query = this.getConditionQuery(conditions);
        if (option.limit) {
            query = query.limit(option.limit);
        }
        if (option.cols) {
            query = query.select(option.cols);
        }
        return this.executeByQuery(query, dbOpt);
    }
    scope() {
        return SqlBuilder.table(this.tableName);
    }
    normalScope() {
        return SqlBuilder.table(this.tableName).where("Status", Statuses.NORMAL);
    }
    byIdScope(id) {
        return this.scope().where({ [this.ID_COLUMN_NAME]: id });
    }
    updateByCondition(condition, props) {
        const query = this.getConditionQuery(condition).update(props);
        return this.executeByQuery(query);
    }
    updateById(id, props) {
        const query = this.scope()
            .where({ [this.ID_COLUMN_NAME]: id })
            .update(props);
        return this.executeByQuery(query);
    }
    updateByIds(ids, props) {
        return this.executeByQuery(this.scope().whereIn(this.ID_COLUMN_NAME, ids).update(props)).then(() => {
            return this.findByIds(ids);
        });
    }
    insert(props) {
        return __awaiter(this, void 0, void 0, function* () {
            const query = this.scope().insert(props);
            const result = yield this.executeByQuery(query);
            return result.insertId;
        });
    }
    insertBatch(props) {
        return __awaiter(this, void 0, void 0, function* () {
            if (_.isEmpty(props))
                return;
            const query = this.scope().insert(props);
            const result = yield this.executeByQuery(query);
            return result.insertId;
        });
    }
    delete(condition) {
        const query = this.getConditionQuery(condition);
        return this.executeByQuery(query.del());
    }
    deleteByIds(ids) {
        const qb = this.scope().whereIn(this.ID_COLUMN_NAME, ids).del();
        return this.executeByQuery(qb);
    }
    find(conditions, option, dbOpt) {
        return this.where(conditions, option, dbOpt);
    }
    findOne(conditions, cols, option) {
        return this.find(conditions, { limit: 1, cols: cols }, option).then(function (records) {
            return _.chain(records).first().value();
        });
    }
    findNormal(condition, cols) {
        const query = this.normalScope()
            .where(condition)
            .select(cols);
        return this.executeByQuery(query);
    }
    findById(id, cols) {
        return __awaiter(this, void 0, void 0, function* () {
            const records = yield this.where({ [this.ID_COLUMN_NAME]: id }, { limit: 1, cols: cols });
            const record = _.chain(records).first().value();
            return record;
        });
    }
    findNormalById(id, cols) {
        return __awaiter(this, void 0, void 0, function* () {
            const records = yield this.where({ [this.ID_COLUMN_NAME]: id, Status: Statuses.NORMAL }, { limit: 1, cols: cols });
            const record = _.chain(records).first().value();
            return record;
        });
    }
    findByIds(ids, cols, conditions) {
        return __awaiter(this, void 0, void 0, function* () {
            if (_.isEmpty(ids))
                return [];
            ids = _.uniq(_.compact(_.map(ids, _.toNumber)));
            let query = this.scope()
                .select(cols)
                .whereIn(this.ID_COLUMN_NAME, ids);
            if (conditions)
                query = query.andWhere(conditions);
            const records = yield this.executeByQuery(query);
            const recordGroupById = _.groupBy(records, this.ID_COLUMN_NAME);
            return _.chain(ids)
                .map(function (id) {
                return _.head(recordGroupById[id]);
            })
                .compact()
                .value();
        });
    }
    findByOffset(conditions, offset, cols) {
        let query = this.scope();
        if (!_.isEmpty(conditions))
            query.andWhere(conditions);
        if (cols)
            query = query.select(cols);
        query = this.getOffsetCondition(query, offset);
        return this.executeByQuery(query);
    }
    findByPagination(conditions, pagination, cols) {
        let query = this.scope().select(cols);
        if (!_.isEmpty(conditions)) {
            query.andWhere(conditions);
        }
        const page = pagination.page;
        const pageSize = pagination.pageSize;
        const offset = pageSize * (page - 1);
        query = query.offset(offset).limit(pageSize || config_1.pageCfg.defaultPageSize);
        return this.executeByQuery(query);
    }
    queryByPagination(query, pagination) {
        return __awaiter(this, void 0, void 0, function* () {
            const offset = pagination.pageSize * (pagination.page - 1);
            query = query.offset(offset).limit(pagination.pageSize);
            const rows = yield this.executeByQuery(query);
            return rows;
        });
    }
    exists(conditions) {
        return __awaiter(this, void 0, void 0, function* () {
            const exist = yield this.isExist(conditions);
            return exist;
        });
    }
    notExist(conditions) {
        return __awaiter(this, void 0, void 0, function* () {
            const exist = this.exists(conditions);
            return !exist;
        });
    }
    existsById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            const exist = this.isExist({ [this.ID_COLUMN_NAME]: id });
            return exist;
        });
    }
    isExist(conditions) {
        return this.findOne(conditions, [this.ID_COLUMN_NAME]).then(function (rows) {
            return !_.isEmpty(rows);
        });
    }
    execute(sql, option) {
        const opt = option || { dbNode: this.dbNode };
        return this.db.execute(sql, opt);
    }
    executeByQuery(query, option) {
        const opt = option || { dbNode: this.dbNode, maxScaleForceMaster: false };
        let sql = query.toString();
        if (opt.maxScaleForceMaster) {
            sql += " -- maxscale route to master";
        }
        return this.execute(sql, opt);
    }
    smartQuery(query, cols) {
        query = query.select(cols);
        return this.executeByQuery(query);
    }
    queryWithPagination(query, pagination) {
        const page = pagination.page;
        const pageSize = pagination.pageSize;
        const offset = pageSize * (page - 1);
        query = query.offset(offset).limit(pageSize);
        return this.executeByQuery(query);
    }
    count(condition) {
        const query = this.scope().where(condition);
        return this.countByQuery(query);
    }
    countByQuery(query) {
        return __awaiter(this, void 0, void 0, function* () {
            const rows = (yield this.executeByQuery(query.count()));
            if (rows.length > 0) {
                return rows[0]["count(*)"] || 0;
            }
            else {
                return 0;
            }
        });
    }
    softDeleteById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            const query = this.scope()
                .where({ [this.ID_COLUMN_NAME]: id })
                .update({ status: Statuses.DELETED });
            return this.executeByQuery(query);
        });
    }
    softDeleteByCondition(condition) {
        let query = this.getConditionQuery(condition);
        query = query.update({ status: Statuses.DELETED });
        return this.executeByQuery(query);
    }
    deleteByCondition(condition) {
        const query = this.getConditionQuery(condition).del();
        return this.executeByQuery(query);
    }
    refresh(record) {
        return this.findById(record[this.ID_COLUMN_NAME]);
    }
    escapeLikeStr(likeStr) {
        return likeStr.replace(/[_%]|\\/g, function (escapeChar) {
            return "\\" + escapeChar;
        });
    }
    promiseMap(iterable, mapper, concurrency) {
        return bluebird.map(iterable, mapper, { concurrency: concurrency || 5 });
    }
    createOrUpdate(props, updateProps) {
        if (!updateProps) {
            updateProps = props;
        }
        const insert = this.scope().insert(props).toString();
        const update = this.scope()
            .update(updateProps)
            .toString()
            .replace(/^update .* set /i, "");
        const query = this.raw(insert + " on duplicate key update " + update);
        return this.executeByQuery(query);
    }
    createOrUpdateQuery(props, updateProps) {
        if (!updateProps) {
            updateProps = props;
        }
        const insert = this.scope().insert(props).toString();
        const update = this.scope()
            .update(updateProps)
            .toString()
            .replace(/^update .* set /i, "");
        return this.raw(insert + " on duplicate key update " + update);
    }
    raw(sql, valueBinding) {
        return SqlBuilder.raw(sql, valueBinding);
    }
    increment(condition, incrCol, incrVal = 1) {
        return __awaiter(this, void 0, void 0, function* () {
            const query = this.getConditionQuery(condition).increment(incrCol, incrVal);
            return this.executeByQuery(query);
        });
    }
    incrManyFields(condition, incrCols) {
        return __awaiter(this, void 0, void 0, function* () {
            let query = this.getConditionQuery(condition);
            for (const r of incrCols) {
                query = query.increment(r, 1);
            }
            return this.executeByQuery(query);
        });
    }
    powerQuery(option) {
        return __awaiter(this, void 0, void 0, function* () {
            let query = this.scope();
            if (option.initQuery) {
                query = option.initQuery.clone();
            }
            if (option.where) {
                query = this.getConditionQuery(option.where, query);
            }
            if (option.orderBy) {
                const [cols, orders] = option.orderBy;
                const orderBys = _.zip(cols, orders);
                for (const r of orderBys) {
                    const [col, order] = r;
                    query = query.orderBy(col, order);
                }
            }
            if (option.select) {
                query = query.select(option.select);
            }
            const pagination = option.pagination;
            const offset = (pagination.page - 1) * pagination.pageSize;
            query = query.offset(offset).limit(pagination.pageSize || config_1.pageCfg.defaultPageSize);
            const rows = yield this.executeByQuery(query, { dbNode: option.dbNode || "MASTER" });
            return rows;
        });
    }
    powerListQuery(option) {
        return __awaiter(this, void 0, void 0, function* () {
            const params = option.pagination;
            const list = yield this.powerQuery(option);
            const getCountQuery = () => {
                let query = null;
                if (option.initQuery) {
                    query = option.initQuery;
                }
                else {
                    query = this.scope();
                }
                if (option.where) {
                    query = this.getConditionQuery(option.where, query);
                }
                return query;
            };
            const countQuery = getCountQuery();
            const totalCount = yield this.countByQuery(countQuery);
            const meta = (0, util2_1.getPageMeta)(params, totalCount);
            return { list: list, meta: meta };
        });
    }
    getConditionQueryWithQuery(query, conditions) {
        _.forEach(conditions, function (value, key) {
            if (_.isArray(value)) {
                query = query.whereIn(key, value);
            }
            else {
                query = query.where({ [key]: value });
            }
        });
        return query;
    }
    /**
     * 高级查询函数，支持条件查询、排序、分页和字段选择
     *
     * @param ctx - 上下文对象
     * @param option - 查询选项，包含where条件、初始查询、排序规则、分页参数和选择字段
     * @returns 返回查询结果数组，如果指定了select字段，则返回包含这些字段的对象数组
     */
    findMany(option) {
        return __awaiter(this, void 0, void 0, function* () {
            let query = this.scope();
            if (option.initQuery) {
                query = option.initQuery.clone();
            }
            if (option.where) {
                query = this.getConditionQueryWithQuery(query, option.where);
            }
            if (option.orderBy) {
                for (let [col, order] of option.orderBy) {
                    // @ts-ignore
                    query = query.orderBy(col, order);
                }
            }
            if (option.select) {
                //@ts-ignore
                query = query.select(option.select);
            }
            let offset = (option.pagination.page - 1) * option.pagination.pageSize;
            query = query.offset(offset).limit(option.pagination.pageSize);
            let rows = yield this.executeByQuery(query);
            return rows;
        });
    }
}
exports.BaseModelClass = BaseModelClass;
//# sourceMappingURL=baseModel2.js.map