import * as PyqProfile from './PyqProfile'
import * as bluebird from 'bluebird'

export enum Permission {
  Img = 'Img',
  Photo = 'Photo',
  Tape = 'Tape',
  Signature = 'Signature',
  Moment = 'Moment',
  Message = 'Message',
  Location = 'Location'
}

export async function hasPermission(roleId: number, permission: Permission): Promise<boolean> {
  let record = await PyqProfile.findOne({ RoleId: roleId }, ['BanState']) as { BanState: string }
  if (record && record.BanState) {
    let state: Object = JSON.parse(record.BanState)
    if (state.hasOwnProperty(permission)) {
      let banTime: number = state[permission]
      return banTime < Date.now()
    } else {
      return true
    }
  } else {
    return true
  }
}

export async function checkPermission(roleId: number, permission: Permission, message: string) {
  let isAllow = await hasPermission(roleId, permission)
  if (isAllow) {
    return isAllow
  } else {
    return bluebird.reject({ errorType: 'PermissionDeny', msg: message })
  }
}