import { PYQ_MOMENT_COLS } from "../common/constants";
import { clazz<PERSON>ogger } from "../pyq-server/logger";
import { MomentRecord } from "../pyq-server/models";
import { BaseModelClass } from "./baseModel2";
const logger = clazzLogger("models/momentModel");

export class MomentModel2Class extends BaseModelClass<MomentRecord> {
  constructor() {
    super("pyq_moment");
  }
  static ShowMomentCols: string[] = PYQ_MOMENT_COLS;

  async getMinIdGteCreateTime(createTime: number): Promise<number> {
    const query = this.scope().min("ID as minId").where("CreateTime", ">=", createTime).limit(1);
    let rows: { minId: number }[] = [];
    rows = await this.executeByQuery(query);
    let maxId = 0;
    if (rows && rows.length > 0) {
      maxId = rows[0].minId;
      logger.info({ maxId, createTime }, "GetMaxIdGteCreateTimeOk");
    } else {
      logger.info({ createTime }, "GetMaxIdGteCreateTimeEmpty");
    }
    return maxId;
  }

  /**
   * 过滤出存在的动态ID
   * @param ids 需要检查的动态ID数组
   * @returns 实际存在的动态ID数组
   */
  async filterExists(ids: number[]): Promise<number[]> {
    if (!ids || ids.length === 0) {
      return [];
    }
    const query = this.normalScope().whereIn("ID", ids).select("ID");
    let rows: { ID: number }[] = [];
    rows = await this.executeByQuery(query);
    return rows.map((r) => r.ID);
  }
}

export const BASIC_MOMENT_COLS = PYQ_MOMENT_COLS as (keyof MomentRecord)[];

export const MomentModel2: MomentModel2Class = new MomentModel2Class();
