var BaseModelClass = require('./BaseModelClass');
var Users = require('./Users');
var _ = require('lodash');
var ImagesAuditAble = require('./mixins/ImagesAuditable');
var Moments = _.extend(new BaseModelClass("md_moment"), ImagesAuditAble);
var MdEventBus = require('../md-server/eventBus');
let Games = require('./Games');
const bbCodeUtil = require('../common/bbcodeUtil')

var PHOTO_THUMB_SUFFIX = "?imageView&thumbnail=150y150%7Cwatermark&type=1&gravity=center&image=Y29tbW9uL3dhdGVybWFyay1hdWRpdC5wbmc=";

const Category = {
  data : [
    {name: "qn", GameId: 8001},
    {name: "qnm", GameId: 8100},
    {name: "tong_ren", GameId: 1}
  ],

  getGameIdByName: (name) => {
    const row = _.find(Category.data, {name:name});
    return _.get(row, 'GameId') || null;
  }
};

Moments.Category = Category;

Moments.normalizeRecord = function(record) {
  if(record) {
    var imgListStr = record.ImgList;
    var imgList = _.chain(_.split(imgListStr, ',')).compact().value();
    record.ImgList = _.map(imgList, function(img) {
      return {pic:img, thumb: img+PHOTO_THUMB_SUFFIX};
    });
    record.ZanList = _.chain(_.split(record.ZanList, ',')).compact().value();
    record.Duration = Date.now() - record.CreateTime;
    record.Text = Moments.getFormattedTextInMoment(record);
    return record;
  } else {
    return record;
  }
};

Moments.embedUserInfo = function(moment, curUserId) {
  return Users.findById(moment.UserId).then(function(user) {
    user = _.pick(user, ['ID', 'AvaAuthStatus', 'Avatar', 'City', 'NickName', 'Gender']);
    require('../common/data').md.setAvatarView(user, curUserId);
    moment.userInfo = user; return moment;
  });
};

Moments.getMoment = function(momentId) {
  return Moments.findById(momentId).then(Moments.normalizeRecord);
};

Moments.getLatestMoments = function (option) {
  const cols = option.cols;
  const size = option.limit;
  const category = option.category;
  const getQueryWithCategory = (query, category) => {
    if(category) {
      query = query.where(function () {
        if(option.category === "qn") {
          //端游和同人帖的GameId为8100 和 1
          this.whereNull('GameId').orWhereIn('GameId', [8001,1]);
        } else if (option.category === "qnm") {
          this.whereNull('GameId').orWhere('GameId', 8100);
        }
      })
    }
    return query;
  };

  const getTopMoments = () => {
    let query = Moments.normalScope().select(cols).where('isTop', '>', 0);
    query = getQueryWithCategory(query, category);
    return Moments.executeByQuery(query)
  };

  const getMoments = (size) => {
    let query = Moments.normalScope().select(cols).orderBy('CreateTime', 'desc').limit(size);
    query = getQueryWithCategory(query, category);
    return Moments.executeByQuery(query)
  };

  return getTopMoments()
    .then(function (topMoments) {
      const normalSize =  Math.max(0, size - topMoments.length);
      return getMoments(normalSize)
        .then(function (normalMoments) {
          return _.concat(topMoments, normalMoments);
        });
    });
};

Moments.getUserLatestMoment = function (userId) {
  return Moments.executeByQuery(
    Moments.normalScope()
      .where('UserId', userId)
      .orderBy('CreateTime', 'desc').limit(1)
  ).then(function (moments) {
    return _.first(moments);
  }).then(function (moment) {
    return Moments.normalizeRecord(moment);
  });
};

Moments.getFormattedTextInMoment = function (moment) {
  let str = moment.Text || "";
  const bbCode = str.replace(/<link\s+item=([^>]+)>/g, function (match, p1) {
    let tokens = _.split(p1, ',');
    const txt = tokens[tokens.length - 2];
    return `[color=#c29d5b]${txt}[/color]`
  });
  return bbCodeUtil.bbTextToHtml(bbCode)
};

Moments.create = function (props) {
  var now = Date.now();
  props = _.merge({CreateTime:now, isTop: 0}, props);
  return BaseModelClass.prototype.create.call(Moments, props);
};

MdEventBus.on("momentPhotoAuditFinishEvent", function(data) {
  Moments.photoAuditFinish(data.id, data.url, data.auditStatus)
});
MdEventBus.on("momentVideoAuditFinishEvent", function(data) {
  Moments.videoAuditFinish(data.id, data.url, data.auditStatus)
});

Moments.filterValidMoments = function (userid, moments) {
  return _.filter(moments, m => {
    if(m.UserId == userid || m.Text) {
      return true;
    }
    if(m.ImgList && m.ImgAudit && m.ImgAudit.match(/^(1,?)+$/)) {
      return true;
    }
    return false;
  })
};

Moments.extractTopic = function (text) {
  let topic = ''
  if (text) {
    const match = text.match(/##([^#\s]+)##/)
    if (match) {
      topic = match[1]
    }
  }
  return topic
}

module.exports = Moments;
