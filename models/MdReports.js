const BaseModelClass = require('./BaseModelClass')
const MdReports = new BaseModelClass('md_report')
const _ = require('lodash')

MdReports.Reasons = [
  {id: 1, name: '广告垃圾'},
  {id: 2, name: '违规内容'},
  {id: 3, name: '恶意灌水'},
  {id: 4, name: '其他原因'}
]

MdReports.ReasonTypes = _.map(MdReports.Reasons, 'id')

MdReports.OtherReason = 4

const ReportSources = {
  PC: 1,
  QnPocket: 2
}

MdReports.Sources = ReportSources

module.exports = MdReports
