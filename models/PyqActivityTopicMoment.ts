
import BaseModelClass = require('./BaseModelClass')
import { TABLE_NAMES } from '../common/constants';

export interface ActivityTopicMomentRecord {
    ID?: number
    TopicId: number
    RoleId: number
    ImgCount: number
    MomentId: number
    TopTime: number
    Status: number
}

class ActivityTopicMomentClass extends BaseModelClass<ActivityTopicMomentRecord> {
    constructor() {
        super(TABLE_NAMES.ActivityTopicMoment)
    }

    async addRecord(roleId: number, topicId: number, momentId: number, imgCount: number) {
        let record: ActivityTopicMomentRecord = {
            TopicId: topicId,
            RoleId: roleId,
            ImgCount: imgCount,
            MomentId: momentId,
            TopTime: 0,
            Status: 0
        }
        let ret = await this.insert(record)
        return ret
    }
}

export let ActivityTopicMoment = new ActivityTopicMomentClass()