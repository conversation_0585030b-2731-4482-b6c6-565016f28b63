/**
 * Created by <PERSON><PERSON><PERSON> on 16-12-13.
 */

const BaseModelClass = require('./BaseModelClass');
const _ = require('lodash');
let PyqTopic = new BaseModelClass("pyq_topic");

const Events = {
  ADD_MOMENT_TO_TOPIC: "add_topic",
};

PyqTopic.create = function (props) {
  let now = Date.now();
  props = _.defaults(props, {CreateTime:now, UpdateTime: now});
  return BaseModelClass.prototype.create.bind(this)(props);
};


PyqTopic.Events = Events;

PyqTopic.getTopicIdBySubject = function (topicSubject) {
  let query = PyqTopic
    .normalScope()
    .where('Subject', topicSubject)
    .select('ID')
    .limit(1);
  return PyqTopic.executeByQuery(query).then(function (r) {
    return _.get(_.first(r), 'ID') || null;
  })
};

module.exports = PyqTopic;
