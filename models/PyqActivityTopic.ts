import BaseModelClass = require("./BaseModelClass");
import { TABLE_NAMES } from "../common/constants";

export interface TopicRecord {
  ID: number;
  Name: string;
  Banner: string;
  Desc: string;
  Url: string;
  CreateTime: number;
  TopTime: number;
  Status: number;
}

export enum TopicStatus {
  DOWN = -1,
  TOP = 1,
  Normal = 0,
}

class ActivityTopicClass extends BaseModelClass<TopicRecord> {
  constructor() {
    super(TABLE_NAMES.ActivityTopic);
  }

  async getTopics(size: number, cols: string[]): Promise<Partial<TopicRecord>[]> {
    let query = this.scope()
      .select(cols)
      .whereNot("Status", TopicStatus.DOWN)
      .orderBy("TopTime", "desc")
      .orderBy("ID", "desc")
      .limit(size);
    let rows = await this.executeByQuery(query);
    return rows;
  }

  async getNameById(topicId: number) {
    let record: Partial<TopicRecord> = await this.findOne({ ID: topicId }, ["Name"]);
    if (record) {
      return record.Name;
    } else {
      return null;
    }
  }

  async getIdByName(name: string) {
    let record: Partial<TopicRecord> = await this.findOne({ Name: name }, ["ID"]);
    if (record) {
      return record.ID;
    } else {
      return 0;
    }
  }

  async topTopic(topicId: number, topTime: number) {
    let ret = await this.updateByCondition({ ID: topicId }, { TopTime: topTime, Status: TopicStatus.TOP });
    return ret;
  }
}

export let ActivityTopic = new ActivityTopicClass();
export let ActivityTopicModel = ActivityTopic
