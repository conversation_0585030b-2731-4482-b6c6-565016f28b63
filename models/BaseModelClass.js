﻿/// <reference path="./BaseModelClass.d.ts" />
const SqlBuilder = require("../common/sqlBuilder")
const DB = require("../common/db")
const _ = require("lodash")
const Errors = require("../common/errors")
const Promise = require("bluebird")
const EventEmitter = require("events").EventEmitter
const util = require("util")
const Config = require("../common/config")
const Constants = require("../common/data").Constants

function BaseModelClass(tableName, dbNode, option) {
  EventEmitter.call(this)
  option = option || {}
  this.tableName = tableName
  this.dbNode = dbNode || "MASTER" //默认指向MASTER节点
  this.db = option.db || DB
}

util.inherits(BaseModelClass, EventEmitter)

const alias = function (obj, aliasName, name) {
  obj[aliasName] = obj[name]
}

BaseModelClass.prototype.scope = function () {
  return SqlBuilder.table(this.tableName)
}

BaseModelClass.prototype.normalScope = function () {
  return SqlBuilder.table(this.tableName).where("Status", BaseModelClass.prototype.Statuses.NORMAL)
}

BaseModelClass.prototype.byIdScope = function (id) {
  return this.scope().where({ ID: id })
}

BaseModelClass.prototype.findById = function (id, cols) {
  return this.findOne({ ID: id }, cols)
}

BaseModelClass.prototype.findNormalById = function (id, cols) {
  return this.findOne({ ID: id, Status: Constants.STATUS_NORMAL }, cols)
}

BaseModelClass.prototype.findByIds = function (ids, selectProps) {
  const self = this
  selectProps = selectProps || "*"
  ids = _.uniq(_.compact(_.map(ids, _.toNumber)))
  return self.executeByQuery(self.scope().select(selectProps).whereIn("ID", ids)).then(function (records) {
    const recordGroupById = _.groupBy(records, "ID")
    return _.chain(ids)
      .map(function (id) {
        return _.head(recordGroupById[id])
      })
      .compact()
      .value()
  })
}

/**
 * findByIdForce
 *
 * Force mean it will throw EntityNotFound error when id not exist
 * No Force version just return null as a result
 * @param {String} id
 * @param {Array} selectProps
 */
BaseModelClass.prototype.findByIdForce = function (id, selectProps) {
  const self = this
  return self.findOne({ ID: id }, selectProps).then(function (r) {
    if (_.isEmpty(r)) {
      throw new Errors.EntityNotFound(self.tableName, id)
    } else {
      return r
    }
  })
}

BaseModelClass.prototype.deleteById = function (id) {
  const self = this
  return this.checkEntityExsits(id).then(function () {
    const sql = self.byIdScope(id).del().toString()
    return self.execute(sql)
  })
}

BaseModelClass.prototype.deleteByIds = function (ids) {
  const self = this
  const sql = self.scope().whereIn("ID", ids).del().toString()
  return self.execute(sql)
}

BaseModelClass.prototype.checkEntityExsits = function (id) {
  const self = this
  return self.existsById(id).then(function (isExsits) {
    if (!isExsits) {
      throw new Errors.EntityNotFound(self.tableName, id)
    } else {
      return true
    }
  })
}

BaseModelClass.prototype.updateById = function (id, props) {
  const self = this
  return this.existsById(id)
    .then(function (isExsits) {
      if (!isExsits) {
        throw new Errors.EntityNotFound(self.tableName, id)
      }
      if (!_.isEmpty(props)) {
        const sql = self.byIdScope(id).update(props).toString()
        return self.execute(sql)
      }
    })
    .then(function () {
      return self.findById(id)
    })
}

BaseModelClass.prototype.updateByCondition = function (condition, props) {
  const query = this.getConditionQuery(condition).update(props)
  return this.executeByQuery(query)
}

BaseModelClass.prototype.updateByIds = function (ids, props) {
  const self = this
  return self.executeByQuery(self.scope().whereIn("ID", ids).update(props)).then(function () {
    return self.findByIds(ids)
  })
}

BaseModelClass.prototype.create = function (props) {
  const self = this
  let primaryKey = self.PrimaryKey || "ID"
  return self.insert(props).then(function (okPacket) {
    if (okPacket.insertId) {
      return self.findById(okPacket.insertId)
    } else {
      if (!_.isArray(primaryKey)) {
        primaryKey = [primaryKey]
      }
      const conditions = _.reduce(
        primaryKey,
        function (result, key) {
          result[key] = props[key]
          return result
        },
        {}
      )
      return self.findOne(conditions)
    }
  })
}

BaseModelClass.prototype.insert = function (props) {
  return this.executeByQuery(this.scope().insert(props))
}

BaseModelClass.prototype.insertBatch = function (props) {
  return this.executeByQuery(this.scope().insert(props))
}

BaseModelClass.prototype.createBatch = function (propsArray) {
  const self = this
  return Promise.map(
    propsArray,
    function (props) {
      return self.create(props)
    },
    { concurrency: 3 }
  )
}

BaseModelClass.prototype.getConditionQuery = function (conditions) {
  let query = SqlBuilder.table(this.tableName)
  _.forEach(conditions, function (value, key) {
    if (_.isArray(value)) {
      query = query.whereIn(key, value)
    } else {
      query = query.where(key, value)
    }
  })
  return query
}

BaseModelClass.prototype.deleteByCondition = function (condition) {
  const query = this.getConditionQuery(condition)
  return this.executeByQuery(query.del())
}

BaseModelClass.prototype.where = function (conditions, option) {
  option = option || {}
  let query = this.getConditionQuery(conditions)
  if (option.limit) {
    query = query.limit(option.limit)
  }
  if (option.cols) {
    query = query.select(option.cols)
  }
  return this.executeByQuery(query)
}

alias(BaseModelClass.prototype, "find", "where")

BaseModelClass.prototype.findOne = function (conditions, cols) {
  return this.find(conditions, { limit: 1, cols: cols }).then(function (records) {
    return _.chain(records).first().value()
  })
}

/**
 * Check records match conditions exists?
 *
 * @param {Object} conditons like {ID: 1}
 * @returns {Boolean} exsitFlag
 */
BaseModelClass.prototype.exists = function (conditions) {
  return this.findOne(conditions).then(function (rows) {
    return !_.isEmpty(rows)
  })
}

BaseModelClass.prototype.notExist = function (conditions) {
  return this.exists(conditions).then(function (isExist) {
    return !isExist
  })
}

BaseModelClass.prototype.existsById = function (id) {
  return this.exists({ ID: id })
}

BaseModelClass.prototype.executeByQuery = function (query) {
  return this.execute(query.toString())
}

BaseModelClass.prototype.execute = function (sql) {
  return this.db.execute(sql, { dbNode: this.dbNode })
}

BaseModelClass.prototype.queryWithPagination = function (query, pagination) {
  const page = pagination.page
  const pageSize = pagination.pageSize
  const offset = pageSize * (page - 1)
  query = query.offset(offset).limit(pageSize)
  return this.executeByQuery(query)
}

BaseModelClass.prototype.count = function (condition) {
  const query = this.scope().where(condition)
  return this.countByQuery(query)
}

BaseModelClass.prototype.countByQuery = function (query) {
  return this.executeByQuery(query.count()).then(function (rows) {
    return _.chain(rows).first().value()["count(*)"]
  })
}

BaseModelClass.prototype.Statuses = {
  NORMAL: 0,
  DELETED: -1,
}

BaseModelClass.prototype.isSoftDeleted = function (record) {
  return record.Status === BaseModelClass.prototype.Statuses.DELETED
}

BaseModelClass.prototype.softDeleteById = function (id) {
  return this.updateById(id, { Status: BaseModelClass.prototype.Statuses.DELETED })
}

BaseModelClass.prototype.softDeleteByCondition = function (condition) {
  return this.updateByCondition(condition, { Status: BaseModelClass.prototype.Statuses.DELETED })
}

BaseModelClass.prototype.refresh = function (record) {
  return this.findById(record.ID)
}

BaseModelClass.prototype.escapeLikeStr = function (likeStr) {
  return likeStr.replace(/[_%]|\\/g, function (escapeChar) {
    return "\\" + escapeChar
  })
}

BaseModelClass.prototype.promiseMap = function (iterable, mapper, concurrency) {
  return Promise.map(iterable, mapper, { concurrency: concurrency || 5 })
}

BaseModelClass.prototype.checkRecordById = function (id, msg) {
  return this.findOne({ ID: id, Status: BaseModelClass.prototype.Statuses.NORMAL }).then(function (r) {
    if (r) {
      return r
    } else {
      return Promise.reject({ errorType: "EntityNotFound", msg: msg || "该实体未找到" })
    }
  })
}

BaseModelClass.prototype.createOrUpdate = function (props, updateProps) {
  const self = this
  if (!updateProps) {
    updateProps = props
  }
  const insert = self.scope().insert(props).toString()
  const update = self
    .scope()
    .update(updateProps)
    .toString()
    .replace(/^update .* set /i, "")
  const query = self.raw(insert + " on duplicate key update " + update)
  return self.executeByQuery(query)
}

BaseModelClass.prototype.push = function (condition, props, newProps, hookVal) {
  return this.findOne(condition).then((exist) => {
    hookVal && hookVal(props, exist)

    if (exist) {
      return this.updateByCondition(condition, props)
    }
    return this.insert(Object.assign(props, newProps, condition))
  })
}

/**
 * Returns a raw SQL query.
 * @param {Array<any>} args - The arguments for the raw SQL query.
 * @returns { Knex.Raw<any> } - The raw SQL query.
 */
BaseModelClass.prototype.raw = function (...args) {
  return SqlBuilder.raw(...args)
}

module.exports = BaseModelClass
