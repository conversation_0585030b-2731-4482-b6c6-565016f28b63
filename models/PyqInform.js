"use strict";
/**
 * Created by <PERSON><PERSON><PERSON> on 2017/4/24.
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
const BaseModelClass = require("./BaseModelClass");
const _ = require("lodash");
const InformTypes = {
    LIKE_MOMENT: 0,
    COMMENT_MOMENT: 1,
    REPLY_MOMENT: 2,
    FORWARD_MOMENT: 3,
};
class InformModelClass extends BaseModelClass {
    constructor() {
        super(...arguments);
        this.InformTypes = InformTypes;
    }
    add(props) {
        return __awaiter(this, void 0, void 0, function* () {
            props = _.defaults(props, { CreateTime: Date.now() });
            if (props.RoleId !== props.TargetId) {
                return this.insert(props);
            }
            else {
                return null;
            }
        });
    }
}
const InformModel = new InformModelClass("pyq_inform");
module.exports = InformModel;
//# sourceMappingURL=PyqInform.js.map