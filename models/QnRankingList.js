/**
 * Created by zhen<PERSON> on 16-10-21.
 */

var QnRankingList = module.exports;
var QNDataCenter = require('./QNDataCenter');
var QnFDRecord = require('../models/QnFDRecord');
var { getRedis } = require('../common/redis');
var logger = require('../common/logger');
const Promise = require('bluebird');
const util = require('../common/util');
var _ = require('lodash');

var RankingListMaxSize = 3000;

function setInRedis(key, data) {
  return getRedis().setAsync(key, JSON.stringify(data)).catch(err => logger.error(err));
}

QnRankingList.getByServerIdAndRankId = function (serverId, rankId, option) {
  var key = `qn:servers:${serverId}:ranks:${rankId}:ranking_list`;
  var queryRemoteAndSave = function() {
    return QNDataCenter.queryRank(serverId, rankId).then(ranking => {
      setInRedis(key, ranking);
      return ranking;
    });
  };
  var queryInCache = function () {
    return getRedis().getAsync(key).then(function (data) {
      if(data) {
        return JSON.parse(data);
      } else {
        return [];
      }
    });
  };
  option = option || {isSkipCache: false};
  if(option.isSkipCache) {
    return queryRemoteAndSave();
  }else {
    return queryInCache().then(ranking => {
      if(_.isEmpty(ranking)) {
        return queryRemoteAndSave();
      } else {
        return ranking;
      }
    })
  }
};

QnRankingList.getByRankId = function (rankId) {
  var key = QnRankingList.getAllServerRankKey(rankId);
  return getRedis().getAsync(key).then(data => {
    return JSON.parse(data) || [];
  });
};

QnRankingList.setByRankId = function (rankId, data) {
  let key = QnRankingList.getAllServerRankKey(rankId);
  return getRedis().setAsync(key, JSON.stringify(data));
};


QnRankingList.getAllServerRankKey = function (rankId) {
  return `qn:servers:ranks:${rankId}:ranking_list`;
};

QnRankingList.updateAllServerRankingList = function (rankId, data, unValidIds) {
  var key = QnRankingList.getAllServerRankKey(rankId);
  return QnRankingList.getByRankId(rankId).then(ranking => {
    ranking = _.reject(ranking, r => _.includes(unValidIds, r.Id));
    ranking = _.chain(data).concat(ranking).uniqBy(_.property('Id')).orderBy('Val', 'desc').take(RankingListMaxSize).value();
    return setInRedis(key, ranking);
  }).catch(err => {
    logger.error(err);
  });
};

/**
 * save house ranking to redis cache
 * @param serverId
 * @param pushData
 */
QnRankingList.updateHouseRankingList = function (serverId, pushData) {
  return QnRankingList.updatePushRankingList(serverId, pushData);
};

QnRankingList.updatePushRankingList = function (serverId, pushData) {
  const ranks = pushData.ranks;
  const infos = pushData.infos;
  const mergeInfoRanks = _.map(ranks, (value, key) => {
    const tbl = _.map(value.tbl, row => {
      const houseId = row[0];
      return _.concat(houseId, infos[houseId], _.tail(row));
    });
    return {rankId: key, tbl: tbl};
  });

  return Promise.mapSeries(mergeInfoRanks, row => {
    const key = `qn:servers:${serverId}:ranks:${row.rankId}:ranking_list`;
    QnRankingList.setUpdateTime(row.rankId, serverId, Date.now());
    return getRedis().setAsync(key, JSON.stringify(row.tbl));
  });
};

QnRankingList.updateGuildRankingList = function (serverId, pushData) {
  return QnRankingList.updatePushRankingList(serverId, pushData);
};

QnRankingList.updatePlayerRankingList = function (serverId, pushData) {
  return QnRankingList.updatePushRankingList(serverId, pushData);
};

QnRankingList.getQnPushRankingList = function (rankId, serverId) {
  let key;
  if(serverId) {
    key = `qn:servers:${serverId}:ranks:${rankId}:ranking_list`;
  } else {
    key = `qn:servers:ranks:${rankId}:ranking_list`;
  }
  return getRedis().getAsync(key).then(data => {
    return JSON.parse(data);
  });
};

QnRankingList.getEventRankingList = function (rankId, serverId, option) {
  // ������
  if (rankId % 100 === 0) {
      return QnFDRecord.getWeekFDRecords(serverId, rankId);
  }
  // ������
  return QnFDRecord.getAllFDRecords(serverId, rankId, RankingListMaxSize).then( ranking => {
    return _.map(ranking, row => {
      return [row.BossType, row.GuildId, row.ServerId, row.PublishTime];
    });
  });
};

QnRankingList.setUpdateTime = function (rankId, serverId, time) {
  const key = "md:qn:ranking_list:update_times";
  serverId = serverId || "all";
  const timeHashKey = `${rankId}:${serverId}`;
  return getRedis().hsetAsync(key, timeHashKey, time || Date.now());
};

QnRankingList.getUpdateTime = function (rankId, serverId) {
  const key = "md:qn:ranking_list:update_times";
  serverId = serverId || "all";
  const timeHashKey = `${rankId}:${serverId}`;
  return getRedis().hgetAsync(key, timeHashKey).then(value => {
    const timestamp = parseInt(value, 10);
    if(timestamp) {
      return util.formatDate(timestamp, 'yyyy-MM-dd HH:mm');
    } else {
      return "";
    }
  });
};

QnRankingList.RankingListMaxSize = RankingListMaxSize;
