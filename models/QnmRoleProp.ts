import { RoleIdPartitionModel } from './RoleIdPartitionModel'
import { parseEquipItem, qnm as qnmDataUtil } from '../common/data'
import * as util from '../common/util'
import * as _ from 'lodash'

export interface QnmRolePropRecord {
  RoleId: number
  EquipInfo: string
  PropertyInfo: string
  OtherInfo: string
  PointInfo?: string
  UpdateTime: number
  bucketno?: number
}

class QnmRolePropClass extends RoleIdPartitionModel<QnmRolePropRecord> {
  tableName(): string {
    return 'qnm_roleprop'
  }
}

export let QnmRoleProp = new QnmRolePropClass()

export async function getEquipDetails(roleIds: number[]) {
  const equipInfos = await QnmRoleProp.findByRoleIds(roleIds, ['RoleId', 'EquipInfo'])
  return equipInfos.map(row => {
    row.EquipInfo = util.getJsonInfo(row.EquipInfo)
    return row
  })
}

export interface EquipHtmlShow {
  small: string
  desc: string
  color: string
}

export function equipRecordToHtml(equip: { templateid: number }): EquipHtmlShow {
  const item = qnmDataUtil.getEquip(equip.templateid)
  const small = item.small
  let str = parseEquipItem(equip);
  str = (small ? '<img src="' + small + '" /><br/>' : '') + str;
  return { desc: str, small: small, color: item.color };
}