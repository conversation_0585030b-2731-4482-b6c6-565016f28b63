import * as knex from "knex";
import { Pagination, UpdateResult } from "../common/type";

type DBNode = "MASTER" | "SLAVE";

type Condition<T> = Partial<{ [P in keyof T]: T[P] | Array<T[P]> }>

declare class BaseModelClass<T> {
  tableName: string;

  constructor(name: string, dbNode?: DBNode, option?: any);

  scope(): knex.QueryBuilder;

  normalScope(): knex.QueryBuilder;

  new(): any;

  executeByQuery(query: knex.QueryBuilder): Promise<any>;

  insert(props: Partial<T>): Promise<{ insertId: number }>;

  insertBatch(props: Partial<T>[]): Promise<{ insertId: number }>;

  create(props: { [key: string]: any }): Promise<T>;

  queryWithPagination(query: knex.QueryBuilder, pagination: Pagination): Promise<Partial<T>[]>;

  updateByCondition(condition: object, props: Partial<T>): Promise<UpdateResult>;

  deleteByCondition(condition: Condition<T>): Promise<UpdateResult>;

  softDeleteById(id: number): Promise<UpdateResult>;

  updateById(id: number, props: Partial<T>): Promise<T>;

  countByQuery(query: knex.QueryBuilder): Promise<number>;

  findOne<K extends keyof T>(conditions: Condition<T>, cols?: K[]): Promise<{ [P in K]: T[P] }>;

  find<K extends keyof T>(
    conditions: { [key: string]: any },
    option?: { limit?: number; cols?: K[] }
  ): Promise<{ [P in K]: T[P] }[]>;

  findById<K extends keyof T>(id: number, cols?: K[]): Promise<{ [P in K]: T[P] }>;

  count(condition: Condition<T>): Promise<number>

  exists(conditions: { [key: string]: any }): Promise<boolean>;

  createOrUpdate(props: Partial<T>, updateProps?: Partial<T>): Promise<UpdateResult>;

  raw(str: string): string
}

export = BaseModelClass;
