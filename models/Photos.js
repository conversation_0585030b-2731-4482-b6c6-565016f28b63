﻿var BaseModelClass = require('./BaseModelClass');
var Promise = require('bluebird');
var ErrorTypes = require('../common/errors').ErrorTypes;
var _ = require('lodash');
var SqlBuilder = require('../common/sqlBuilder');
var CommentAble = require('./mixins/CommentAble');
var Photos = module.exports = _.extend(new BaseModelClass("md_photo"), CommentAble);
var Likes = require('./Likes');
var Users = require('./Users');
var { getRedis } = require('../common/redis');
var AuditPhotoService = require('../common/audit');
var MdEventBus = require('../md-server/eventBus');
var logger = require('../common/logger');
const FRESHNESS_PHOTO_IDS_SET = "freshness:photos_ids";
const Constants = require('../common/data').Constants;

Photos.auditPhoto = function(photo) {
  return Photos.getPhotoOwner(photo).then(function(user) {
    var photoOwnerUserId = user.ID;
    var photoUrls = [photo.Url];
    AuditPhotoService.sendPic('md', photoUrls, {
      roleId: photoOwnerUserId,
      picId: `md_album_photo:${photo.ID}`
    });
    return photo;
  });
};

Photos.auditPhotos = function(photos) {
  return Promise.map(photos, Photos.auditPhoto);
};


Photos.Statuses = {
  NORMAL: 0,
  DELETED: -1
};

Photos.AUDIT_STATUSES = {
  AUDITING: 0,
  PASSED: 1,
  REJECT: -1
};

Photos.create = function(props) {
  var now = Date.now();
  props = _.merge({CreateTime:now, UpdateTime: now}, props);
  return BaseModelClass.prototype.create.call(Photos, props);
};

Photos.createBatch = function(propsList) {
  var now = Date.now();
  propsList = _.map(propsList, function (props, index) {
    props = _.merge({CreateTime:now + index, UpdateTime: now + index}, props);
    return props;
  });
  return BaseModelClass.prototype.createBatch.call(Photos, propsList);
};

Photos.createThenAuditPhoto = function(props) {
  return Photos.create(props).then(Photos.aduitPhoto);
};

Photos.addFreshnessToRedis = function(photo) {
  var now = Date.now();
  var sevenDays = 7 * 24 * 3600 * 1000; //ms
  var expireTime = sevenDays - (now - photo.CreateTime);
  return getRedis().setAsync(`freshness:photos:${photo.ID}`, "", "PX", expireTime)
  .then(function () {
    return getRedis().saddAsync(FRESHNESS_PHOTO_IDS_SET, photo.ID);
  });
};


Photos.addFreshnessToRedisBatch = function(photos) {
  return Promise.map(photos, Photos.addFreshnessToRedis);
};

Photos.findByAlbumId = function(albumId) {
  var query = this.scope().where({PhotoAlbumID: albumId})
  .where({Status: Photos.Statuses.NORMAL})
  .whereNot({AuditStatus: Photos.AUDIT_STATUSES.REJECT});
  return this.executeByQuery(query);
};

// Photos.findPassedByAlbumId = function(albumId) {
//   return this.where({PhotoAlbumID:albumId,
//     Status: Photos.Statuses.NORMAL,
//     AuditStatus: Photos.Statuses.PASSED
//   });
// }

Photos.findByAlbumIdWithLikesCount = function(albumId, condition) {
  return Photos.findByAlbumId(albumId).then(function(photos) {
    return Promise.map(photos, function(photo) {
      return Likes.getPhotoLikesCount(photo.ID).then(function(likesCount) {
        photo.LikesCount = likesCount;
        return photo;
      })
    });
  });
}


Photos.getCountByAlbumId = function(albumId, isBrowseSelfAlbum) {
  var query = this.scope().count('*').where({
    PhotoAlbumID:albumId,
    Status: Photos.Statuses.NORMAL,
  })
  if(isBrowseSelfAlbum) {
    query = query.whereNot({AuditStatus: Photos.AUDIT_STATUSES.REJECT});
  } else {
    query = query.whereIn('AuditStatus', [Constants.STATUS_AUDIT_PASS, Constants.STATUS_AUDIT_PICK]);
  }
  return this.executeByQuery(query).then(function(rows) {
    return _.chain(rows).first().value()['count(*)'];
  });
};


Photos.move = function(photoId, targetAlbumId) {
  var PhotoAlbums = require('./PhotoAlbums');
  return Promise.join(
    Photos.findByIdForce(photoId),
    PhotoAlbums.findByIdForce(targetAlbumId),
    function(photo, album) {
      PhotoAlbums.tryToTransAuditingWhenMove([photo], targetAlbumId);
      if(!PhotoAlbums.canBeMovedIn(album)) {
        return Promise.reject({msg: "Target Album can't be movedIn"});
      } else {
        return Photos.updateById(photoId, {
          PhotoAlbumID: targetAlbumId
        });
      }
    }
  );
};

Photos.moveBatch = function(ids, targetAlbumId) {
  return this.findByIds(ids).then(photos => {
    return Photos.deleteAlbumCoverUrlByPhotos(photos);
  }).then(photos => {
    return this.updateByIds(ids, {
      PhotoAlbumID: targetAlbumId
    })
  });
};

Photos.deleteAlbumCoverUrlByPhotos = function(photos) {
  var PhotoAlbums = require('./PhotoAlbums');
  var albumIds = _.uniq(_.map(photos, 'PhotoAlbumID'));
  var deleteUrls = _.map(photos, 'Url');

  return PhotoAlbums.executeByQuery(
    PhotoAlbums.scope().whereIn('ID', albumIds).whereIn('CoverUrl', deleteUrls).update({CoverUrl: null})
  );
};


Photos.checkIdsForUserDelete = function(userId, ids) {
  var PhotoAlbums = require('./PhotoAlbums');
  return this.where({ID: ids}).then(function(photos) {
    if(_.isEmpty(photos)) {
      return Promise.reject({msg: "Photos don't exist!"});
    }
    return Promise.map(photos, function(photo) {
      return PhotoAlbums.findByIdForce(photo.PhotoAlbumID);
    });
  }).then(function(albums) {
    var isAllPhotoOwnedByUser = _.every(albums, function(album) {
      return album.UserId == userId;
    });
    if(isAllPhotoOwnedByUser) {
      return true;
    } else {
      return Promise.reject({errorType: ErrorTypes.PhotoIdInvalid, msg: "Photo owned by others!"});
    }
  });
};

Photos.getAlbumsByPhotoIds = function(ids) {
  var PhotoAlbums = require('./PhotoAlbums');
  return Photos.findByIds(ids).then(function(photos) {
    if(_.isEmpty(photos)) {
      return Promise.reject({msg: "Photos don't exsit!"});
    } else {
      var albumIds = _.uniq(_.map(photos, 'PhotoAlbumID'));
      return PhotoAlbums.findByIds(albumIds);
    }
  });
};

Photos.softDeleteById = function(id) {
  return this.updateById(id, {Status: Photos.Statuses.DELETED});
};

Photos.softDeleteByIds = function(ids) {
  var photos;
  return this.updateByIds(ids, {Status: Photos.Statuses.DELETED})
  .then(function(res) {
    photos = res;
    return Photos.deleteAlbumCoverUrlByPhotos(photos);
  }).then(function() {
    return photos;
  });
};


Photos.generateChannelPhotoLikesSortedSet = function(channelId) {
  var channelHotLikesKey = `hot:channels:${channelId}:by_likes`
  return getRedis().delAsync(channelHotLikesKey).then(function() {
    return Photos.getChannelPhotoIds(channelId);
  }).then(function(photoIds) {
    return Promise.map(photoIds, function(photoId) {
      return Likes.getPhotoLikesCount(photoId).then(function(likesCount) {
        return getRedis().zaddAsync(channelHotLikesKey, likesCount, photoId);
      })
    })
  }).then(function() {
    return channelHotLikesKey;
  });
};


Photos.generateChannelPhotoHotSortedSet = function(channelId) {
  var channelHotKey = `hot:channels:${channelId}`
  return getRedis().delAsync(channelHotKey).then(function() {
    return Promise.join(
      Photos.generateChannelPhotoLikesSortedSet(channelId),
      Photos.generateAllPhotoFreshnessSortedSet(),
      function(likeSortSet, freshnessSortSet) {
        return getRedis().zinterstoreAsync(channelHotKey, 2, likeSortSet, freshnessSortSet, "WEIGHTS", 1, 0.00001653);
      }
    )
  }).then(function() {
    return channelHotKey;
  });
};



Photos.generateAllPhotoFreshnessSortedSet = function() {
  var photoFreshnessKey = 'photos_freshness';
  return getRedis().smembersAsync(FRESHNESS_PHOTO_IDS_SET).then(function(photoIds) {
    return Promise.map(photoIds, function(photoId) {
      let key = `freshness:photos:${photoId}`;
      return getRedis().ttlAsync(key).then(function(freshness) {
        if(freshness < 0) {
          getRedis().sremAsync(FRESHNESS_PHOTO_IDS_SET, photoId)
        } else {
          return getRedis().zaddAsync(photoFreshnessKey, freshness, photoId);
        }
      });
    });
  }).then(function() {
    return photoFreshnessKey;
  });
};


Photos.listNewPhotosByChannelId = function(channelId, option) {
  var PhotoAlbums = require('./PhotoAlbums');
  var photoTable = Photos.tableName;
  var albumTable = PhotoAlbums.tableName;
  var curPage = option.curPage;
  var pageSize = option.pageSize;
  var servenDaysAgo = (Date.now() - 7 * 24 * 3600 * 1000); //ms
  var offset = (curPage - 1) * pageSize;
  var totalPage;
  var self = this;

  return Photos.getChannelNewPhotosCountThisWeek(channelId)
  .then(function(photoCount) {
    totalPage = Math.ceil(photoCount / pageSize);
    curPage = Math.min(curPage, totalPage);
  }).then(function() {
    var query = SqlBuilder.column([
      `${photoTable}.ID`,
      `${photoTable}.Url`,
      `${photoTable}.Name`,
      `${photoTable}.PhotoAlbumID`,
    ]).select().from(photoTable)
    .leftJoin(albumTable, `${photoTable}.PhotoAlbumID`, `${albumTable}.ID`)
    .where(`${photoTable}.Status`, Photos.Statuses.NORMAL)
    .where(`${photoTable}.AuditStatus`, Photos.AUDIT_STATUSES.PASSED)
    .where(`${albumTable}.ChannelId`, channelId)
    .where(`${photoTable}.CreateTime`, '>', servenDaysAgo)
    .orderBy(`${photoTable}.CreateTime`, 'desc')
    .offset(offset)
    .limit(pageSize)
    return self.executeByQuery(query)
  }).then(function(photos) {
    return Promise.map(photos, function(photo, index) {
      return Likes.getPhotoLikesCount(photo.ID).then(function(likesCount) {
        photo.LikesCount = likesCount;
        return photo;
      });
    });
  }).then(function(photos) {
    return {
      curPage: curPage,
      totalPage: totalPage,
      photos: photos
    }
  });
}

Photos.listHotPhotosByChannelId = function(channelId, option) {
  var self = this;
  var curPage = option.curPage;
  var pageSize = option.pageSize;
  var totalPage;
  var start;
  var stop;
  var channelHotKey;
  var sortedPhotoIds;

  return Photos.generateChannelPhotoHotSortedSet(channelId).then(function(key) {
    channelHotKey = key;
    return getRedis().zcardAsync(channelHotKey)
  }).then(function(photoCount) {
    totalPage = Math.ceil(photoCount/pageSize);
    curPage = Math.min(curPage, totalPage);
    start = (curPage - 1) * pageSize;
    stop = start + pageSize - 1;
  }).then(function() {
    return getRedis().zrevrangeAsync(channelHotKey, start, stop)
  }).then(function(photoIds) {
    sortedPhotoIds = photoIds;
    return self.findByIds(photoIds)
  }).then(function(photos) {
    return Promise.map(photos, function(photo, index) {
      return Likes.getPhotoLikesCount(photo.ID).then(function(likesCount) {
        photo = _.pick(photo, 'ID', 'Url', 'Name', 'PhotoAlbumID');
        photo.LikesCount = likesCount;
        return photo;
      });
    });
  }).then(function(photos) {
    photos = sortedPhotoIds.map(function(id) {
      id = parseInt(id);
      return _.find(photos, {ID: id});
    });
    return {
      curPage: curPage,
      totalPage: totalPage,
      photos: photos
    }
  });
};

Photos.getChannelPhotoIds = function(channelId) {
  var PhotoAlbums = require('./PhotoAlbums');
  var servenDaysAgo = (Date.now() - 7 * 24 * 3600 * 1000); //ms
  var photoTable = Photos.tableName;
  var albumTable = PhotoAlbums.tableName;
  var query = SqlBuilder.column([`${photoTable}.ID`]).select().from(photoTable)
  .leftJoin(albumTable, `${photoTable}.PhotoAlbumID`, `${albumTable}.ID`)
  .where(`${photoTable}.Status`, Photos.Statuses.NORMAL)
  .where(`${photoTable}.AuditStatus`, Photos.AUDIT_STATUSES.PASSED)
  .where(`${albumTable}.ChannelId`, channelId)
  .where(`${photoTable}.CreateTime`, '>', servenDaysAgo);

  return this.executeByQuery(query).then(function(rows) {
    return _.map(rows, 'ID');
  });
};


Photos.getChannelPhotosCount = function(channelId) {
  var PhotoAlbums = require('./PhotoAlbums');
  var servenDaysAgo = (Date.now() - 7 * 24 * 3600 * 1000); //ms
  var photoTable = Photos.tableName;
  var albumTable = PhotoAlbums.tableName;
  var query = SqlBuilder.from(photoTable).countDistinct(`${photoTable}.ID`)
  .leftJoin(albumTable, `${photoTable}.PhotoAlbumID`, `${albumTable}.ID`)
  .where(`${photoTable}.Status`, Photos.Statuses.NORMAL)
  .where(`${photoTable}.AuditStatus`, Photos.AUDIT_STATUSES.PASSED)
  .where(`${albumTable}.ChannelId`, channelId)
  .where(`${photoTable}.CreateTime`, '>', servenDaysAgo);

  return this.executeByQuery(query).then(function(res) {
    return res[0]['count(distinct `md_photo`.`ID`)'];
  });
};

Photos.getChannelNewPhotosCountThisWeek = function(channelId) {
  var PhotoAlbums = require('./PhotoAlbums');
  var photoTable = Photos.tableName;
  var albumTable = PhotoAlbums.tableName;
  var servenDaysAgo = (Date.now() - 7 * 24 * 3600 * 1000); //ms

  var query = SqlBuilder.from(photoTable).countDistinct(`${photoTable}.ID`)
  .leftJoin(albumTable, `${photoTable}.PhotoAlbumID`, `${albumTable}.ID`)
  .where(`${photoTable}.Status`, Photos.Statuses.NORMAL)
  .where(`${photoTable}.AuditStatus`, Photos.AUDIT_STATUSES.PASSED)
  .where(`${albumTable}.ChannelId`, channelId)
  .where(`${photoTable}.CreateTime`, '>', servenDaysAgo)

  return this.executeByQuery(query).then(function(res) {
    return res[0]['count(distinct `md_photo`.`ID`)'];
  });
};

Photos.getAlbum = function(photo) {
  var PhotoAlbums = require('./PhotoAlbums');
  return PhotoAlbums.findByIdForce(photo.PhotoAlbumID)
};

Photos.getAdjacentIds = function(photo) {
  return Photos.findByAlbumId(photo.PhotoAlbumID).then(function(photos) {
    return Photos.getAdjacentIdsInPhotos(photos, photo);
  });
}

Photos.getAdjacentIdsInPhotos = function(photos, photo) {
  var albumSize = photos.length;
  photos = _.orderBy(photos, ['CreateTime'], ['desc']);
  var photoIndex = _.findIndex(photos, {ID: photo.ID});
  var prePhoto = photos[photoIndex - 1] || {};
  var nextPhoto = photos[photoIndex + 1] || {};
  return {
    prePhotoId: prePhoto.ID || null,
    nextPhotoId: nextPhoto.ID || null
  }
};


Photos.isUserOwnPhoto = function(photo, userId) {
  var PhotoAlbums = require('./PhotoAlbums');
  return this.getAlbum(photo).then(function(album) {
    return PhotoAlbums.isUserOwnAlbum(album, userId);
  });
};

Photos.getPhotoWithLikesCount = function(photo) {
  return Likes.getPhotoLikesCount(photo.ID).then(function(likesCount) {
    photo.LikesCount = likesCount;
    return photo;
  });
};

Photos.getPhotoOwner = function(photo) {
  var PhotoAlbums = require('./PhotoAlbums');
  return PhotoAlbums.findByIdForce(photo.PhotoAlbumID).then(function(album) {
    return Users.findByIdForce(album.UserId)
  });
};

Photos.getPhotoOwnerAvatar = function(photo) {
  return Photos.getPhotoOwner(photo).then(function(user) {
    return {Avatar: user.Avatar};
  });
};

// var AUDITING_PHOTO_WATERMARK_SUFFIX = "?watermark&type=1&gravity=center&image=Y29tbW9uL3dhdGVybWFyay1hdWRpdC5wbmc=";
var AUDITING_PHOTO_WATERMARK_SUFFIX = ""; //client add the watermark

Photos.addAuditingWaterMarkToPhotos = function(photos) {
  return _.map(photos, Photos.addAuditingWaterMarkToPhotoIfNeed);
};

Photos.addAuditingWaterMarkToPhotoIfNeed = function(photo) {
  if(photo.AuditStatus === Photos.AUDIT_STATUSES.AUDITING) {
    photo.Url += AUDITING_PHOTO_WATERMARK_SUFFIX;
  }
  return photo;
};

Photos.filterAuditPassedPhotos = function(photos) {
  return _.filter(photos, photo => {
    return [Constants.STATUS_AUDIT_PICK, Constants.STATUS_AUDIT_PASS].includes(photo.AuditStatus);
  });
};

Photos.getByLast7Days = function() {
  var servenDaysDuration = 7 * 24 * 3600 * 1000; //ms
  var query = this.scope()
  .where('Status', Photos.Statuses.NORMAL)
  .where('AuditStatus', Photos.AUDIT_STATUSES.PASSED)
  .where('CreateTime', '>',  Date.now() - servenDaysDuration);

  return this.executeByQuery(query);
};

Photos.getTotalLikesCount = function(photos) {
  return Promise.map(photos, function(photo) {
    return Likes.getPhotoLikesCount(photo.ID);
  }).then(function(countArray) {
    return _.sum(countArray) || 0;
  });
};

/**
 * updateByUrl url is unique in photos table
 *
 * @param {Object} updateProps
 * @returns {Object} photo
 */
Photos.updateByUrl = function(url, updateProps) {
  return this.executeByQuery(this.scope().where('Url', url).update(updateProps))
  .then(function() {
    return Photos.findOne({Url: url});
  });
};


function photoAuditFinish(photoUrl, auditStatus) {
  return Photos.updateByUrl(photoUrl, {AuditStatus: auditStatus})
  .then(Photos.updatePhotoAlbumTimestamp)
  .then(function(err) {
    logger.error(err);
  });
}

MdEventBus.on("momentPhotoAuditFinishEvent", function(data) {
  photoAuditFinish(data.url, data.auditStatus)

});

MdEventBus.on("showTopicPhotoAuditFinishEvent", function(data) {
  photoAuditFinish(data.url, data.auditStatus)
});

MdEventBus.on("avatarAuditFinishEvent", function(data) {
  var avatar = data.avatar;
  var auditStatus = data.auditStatus;
  return Photos.updateByUrl(avatar, {AuditStatus: auditStatus})
  .then(Photos.updatePhotoAlbumTimestamp)
  .then(function(err) {
    logger.error(err);
  });
});

MdEventBus.on("normalPhotoAuditFinishEvent", function(data) {
  var photoId = data.photoId;
  var auditStatus = data.auditStatus;
  return Photos.updateById(photoId, {AuditStatus: auditStatus})
  .then(Photos.updatePhotoAlbumTimestamp)
  .then(function(err) {
    logger.error(err);
  });
});

Photos.getUserRecentPhotos = function(userId, size) {
  var PhotoAlbums = require('./PhotoAlbums');
  var albumQuery = PhotoAlbums.scope()
  .select('ID')
  .where('UserId', userId)
  .where('Visibility', PhotoAlbums.VisibilityType.TO_ALL)
  .where('Status', PhotoAlbums.Statuses.NORMAL);
  return PhotoAlbums.executeByQuery(albumQuery).then(function(userAlbumIds) {
    albumIds = _.map(userAlbumIds, 'ID');
    var query = Photos.scope()
    .where('Status', Photos.Statuses.NORMAL)
    .where('AuditStatus', Photos.AUDIT_STATUSES.PASSED)
    .whereIn('PhotoAlbumID', albumIds)
    .orderBy('CreateTime', 'desc')
    .limit(size);
    return Photos.executeByQuery(query);
  });
};

Photos.updatePhotoAlbumTimestamp = function(photo) {
  var PhotoAlbums = require('./PhotoAlbums');
  PhotoAlbums.updateById(photo.PhotoAlbumID, {UpdateTime: Date.now()});
};

Photos.getPhotoUserId = function(photo) {
  var PhotoAlbums = require('./PhotoAlbums');
  return PhotoAlbums.findById(photo.PhotoAlbumID, ['UserId'])
  .then(function(album) {
    return album.UserId;
  });
};

Photos.getPhotoOwnerId = function (photoId) {
  const PhotoAlbums = require('./PhotoAlbums');
  const query = Photos.scope()
  .select('UserId')
  .from(Photos.tableName + " as p")
  .innerJoin(PhotoAlbums.tableName + " as a", "p.PhotoAlbumId", "a.ID")
  .where('p.ID', photoId);

  return Photos.executeByQuery(query).then(rows => {
    return _.get(rows, '[0].UserId') || null;
  });
};

Photos.comment = function (userId, photoId, text) {
  let Notifications = require('./Notifications');
  return CommentAble.comment.bind(this)(userId, photoId, text)
  .then(function (data) {
    return Photos.getPhotoOwnerId(photoId).then(photoOwnerId => {
      Notifications.addEvent(userId, photoOwnerId, Notifications.EVENT_TYPES.COMMENT_PHOTO, data.ID);
    }).then(() => {
      return data;
    });
  })
};

Photos.replyComment = function (userId, targetId, replyId, text) {
  let Notifications = require('./Notifications');
  return CommentAble.replyComment.bind(this)(userId,targetId, replyId, text)
  .then(function (data) {
    Notifications.addEvent(userId, replyId, Notifications.EVENT_TYPES.REPLY_PHOTO, data.ID);
    return data;
  });
};

//不把图片的上图库的状态2暴露给前端， 还是显示通过的状态
Photos.formatAuditStatus =  function (photos) {
  const format = photo => {
    if(photo.AuditStatus === Constants.STATUS_AUDIT_PICK) {
      photo.AuditStatus = Constants.STATUS_AUDIT_PASS;
    }
    return photo;
  };
  if(_.isArray(photos)) {
    return _.map(photos, format)
  } else {
    return format(photos);
  }
};
