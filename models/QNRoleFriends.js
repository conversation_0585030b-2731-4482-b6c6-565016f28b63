var BaseModelClass = require('./BaseModelClass');
var QNRoleInfos = require('./QNRoleInfos');
var _ = require('lodash');
const util = require('../common/util');

var QNRoleFriends = module.exports = new BaseModelClass("qn_rolefriend");


QNRoleFriends.PrimaryKey = 'RoleId';

QNRoleInfos.SEP = ',';

QNRoleFriends.getFriendsRoleInfoIds = function(roleIds) {
  return this.findByRoleIds(roleIds, ['RoleId', 'FriendList']).then(function(roleFriends) {
    return _.chain(roleFriends).map(function(roleInfo) {
      return roleInfo.FriendList;
    }).flatten().value();
  });
};

QNRoleFriends.findByRoleIds = function(roleIds, cols) {
  return this.where({RoleId: roleIds}, {cols: cols}).then(QNRoleFriends.normalizeRecords);
};


QNRoleFriends.normalizeRecords = function(records) {
  return records.map(function(qnRoleFriend) {
    qnRoleFriend.FriendList =  util.csvStrToIntArray(qnRoleFriend.FriendList);
    return qnRoleFriend;
  });
};
