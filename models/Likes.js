var Promise = require('bluebird');
var { getRedis } = require('../common/redis');

var Likes = module.exports = {};


Likes.likePhoto = function(userId, photoId) {
  var likePhotoUsersKey = `likes:photos:${photoId}`;
  return getRedis().sismemberAsync(likePhotoUsersKey, userId).then(function(isLiked) {
    if(isLiked) {
      return Promise.reject({errorType:"PhotoAlreadyLiked", msg: "您已经赞过该图片!"})
    }
  }).then(function() {
    return getRedis().saddAsync(likePhotoUsersKey, userId);
  }).then(function() {
    return getRedis().scardAsync(likePhotoUsersKey);
  });
};

Likes.cancelLikePhoto = function(userId, photoId) {
  var likePhotoUsersKey = `likes:photos:${photoId}`;
  return getRedis().sismemberAsync(likePhotoUsersKey, userId).then(function(isLiked) {
    if(!isLiked) {
      return Promise.reject({errorType:"CancelUnlikedPhoto", msg: "您还没有点赞过该图片!"})
    }
  }).then(function() {
    return getRedis().sremAsync(likePhotoUsersKey, userId);
  }).then(function() {
    return getRedis().scardAsync(likePhotoUsersKey);
  });
}

Likes.getPhotoLikesCount = function(photoId) {
  var likePhotoUsersKey = `likes:photos:${photoId}`;
  return getRedis().scardAsync(likePhotoUsersKey);
};

Likes.isUserLikePhoto = function(userId, photo) {
  var photoId = photo.ID;
  var likePhotoUsersKey = `likes:photos:${photoId}`;
  return getRedis().sismemberAsync(likePhotoUsersKey, userId).then(function(value) {
    return Boolean(value);
  })
};
