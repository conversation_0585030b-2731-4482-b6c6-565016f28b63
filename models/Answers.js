const BaseModelClass = require('./BaseModelClass')
const Answers = module.exports = new BaseModelClass('md_answer')
const Users = require('./Users')
const _ = require('lodash')

Answers.fullFillAnswers = function (answers, curUserId) {
  const userIds = _.uniq(
    _.concat(
      _.map(answers, 'UserId'),
      _.map(answers, 'ReplyId')
    ))
  const query = Users.scope()
  .where('Status', Answers.Statuses.NORMAL)
  .whereIn('Id', userIds)

  return Users.executeByQuery(query)
  .then(Users.Serializers.default)
  .then(function (users) {
    return Users.setUsersAvatar(users, curUserId)
  })
  .then(function (users) {
    const usersGroupById = _.groupBy(users, 'ID')
    return _.map(answers, function (answer) {
      const userId = answer.UserId
      const replyUserId = answer.ReplyId
      if (usersGroupById[userId]) {
        answer.userInfo = usersGroupById[userId][0]
      }
      if (usersGroupById[replyUserId]) {
        answer.replyUserInfo = usersGroupById[replyUserId][0]
      }
      return answer
    })
  })
}

Answers.getMessageAnswers = function (messageIds, cols) {
  const query = Answers.normalScope().whereIn('TargetId', messageIds).select(cols)
  return Answers.executeByQuery(query)
}

Answers.getRecentAnswers = function (messageId, options) {
  const query = Answers.normalScope()
  .select(options.cols)
  .where('TargetId', messageId)
  .orderBy('ID', 'desc')
  .select(options.size)
  .limit(options.limit || 10)
  return Answers.executeByQuery(query)
}

Answers.getCommentsCountBatch = function (momentIds) {
  const query = Answers.scope()
  .where({Status: 0})
  .whereIn('TargetId', momentIds)
  .select(['TargetId'])
  .count('ID as Count')
  .groupBy('TargetId')
  return Answers.executeByQuery(query)
}
