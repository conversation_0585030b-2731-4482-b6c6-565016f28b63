"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkPermission = exports.hasPermission = exports.Permission = void 0;
const PyqProfile = require("./PyqProfile");
const bluebird = require("bluebird");
var Permission;
(function (Permission) {
    Permission["Img"] = "Img";
    Permission["Photo"] = "Photo";
    Permission["Tape"] = "Tape";
    Permission["Signature"] = "Signature";
    Permission["Moment"] = "Moment";
    Permission["Message"] = "Message";
    Permission["Location"] = "Location";
})(Permission = exports.Permission || (exports.Permission = {}));
function hasPermission(roleId, permission) {
    return __awaiter(this, void 0, void 0, function* () {
        let record = yield PyqProfile.findOne({ RoleId: roleId }, ['BanState']);
        if (record && record.BanState) {
            let state = JSON.parse(record.BanState);
            if (state.hasOwnProperty(permission)) {
                let banTime = state[permission];
                return banTime < Date.now();
            }
            else {
                return true;
            }
        }
        else {
            return true;
        }
    });
}
exports.hasPermission = hasPermission;
function checkPermission(roleId, permission, message) {
    return __awaiter(this, void 0, void 0, function* () {
        let isAllow = yield hasPermission(roleId, permission);
        if (isAllow) {
            return isAllow;
        }
        else {
            return bluebird.reject({ errorType: 'PermissionDeny', msg: message });
        }
    });
}
exports.checkPermission = checkPermission;
//# sourceMappingURL=UserPermission.js.map