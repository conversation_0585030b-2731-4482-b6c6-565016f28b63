let _ = require("lodash");

let BaseModelClass = require('./BaseModelClass');
let MdTongrenLike = _.extend(new BaseModelClass("md_tongren_like"));
let { getRedis } = require('../common/redis');

const LIKE_STATUS = {
    NORMAL: 0, // 正常
    CANCEL: -1 // 取消点赞
};

/* Redis缓存参数 */
const CACHE_PARAMS = {
    EXPIRE_TIME: 7 * 24 * 60 * 60 // 一个星期
};

module.exports = MdTongrenLike;

// 点赞
MdTongrenLike.like = function(workId, authorId, userId, createTime) {
    return this.create({
        'UserId': userId,
        'AuthorId': authorId,
        'WorkId': workId,
        'CreateTime': createTime,
        'Status': LIKE_STATUS.NORMAL
    });
};

// 点赞 + 更新缓存
MdTongrenLike.likeAndUpdateCache = function(workId, authorId, userId, createTime) {
    return MdTongrenLike.like(workId, authorId, userId, createTime)
        .then(() => {
            return MdTongrenLike.cacheLikeRecordByWorkId(workId);
        });
};

// 取消点赞
MdTongrenLike.unLike = function(workId, userId) {
    let condition = {
        'WorkId': workId,
        'UserId': userId
    };
    let props = {
        'Status': LIKE_STATUS.CANCEL
    };
    return this.updateByCondition(condition, props);
};

// 取消点赞 + 更新缓存
MdTongrenLike.unLikeAndUpdateCache = function(workId, userId) {
    return MdTongrenLike.unLike(workId, userId)
        .then(() => {
            return MdTongrenLike.cacheLikeRecordByWorkId(workId);
        });
};

// 获取用户对某作品的点赞数
MdTongrenLike.getLikeCountByWorkId = function(userId, workId) {
    let query = this.scope()
        .count('* as likeCount')
        .where('UserId', userId)
        .andWhere('Status', LIKE_STATUS.NORMAL)
        .andWhere('WorkId', workId);
    return this.executeByQuery(query)
        .then(data => {
            return data[0].likeCount;
        });
};

// 获取用户对某作品列表的点赞记录
MdTongrenLike.getUserLikeRecordByWorkIds = function(userId, workIds) {
    let query = this.scope()
        .where('WorkId', 'in', workIds)
        .andWhere('UserId', userId)
        .andWhere('Status', LIKE_STATUS.NORMAL)
        .select('WorkId', 'UserId');
    return this.executeByQuery(query);
};

// 获取某作品列表的点赞记录
MdTongrenLike.getLikeRecordByWorkIds = function(workIds) {
    let query = this.scope()
        .where('WorkId', 'in', workIds)
        .andWhere('Status', LIKE_STATUS.NORMAL)
        .select('WorkId', 'UserId');
    return this.executeByQuery(query);
};

// 获取某作品的点赞记录
MdTongrenLike.getLikeRecordByWorkId = function(workId) {
    let query = this.scope()
        .where('WorkId', workId)
        .andWhere('Status', LIKE_STATUS.NORMAL)
        .select('UserId');
    return this.executeByQuery(query);
};

// 从缓存中获取某作品的点赞记录
MdTongrenLike.getLikeRecordByWorkIdFromCache = function(workId) {
    let LIKE_KEY = `md_tongren_like:${workId}`;

    return getRedis().ttlAsync(LIKE_KEY)
        .then(ttl => {
            if(ttl <= 0) {
                // 写缓存
                return MdTongrenLike.cacheLikeRecordByWorkId(workId);
            }
        })
        .then(() => {
            return getRedis().smembersAsync(LIKE_KEY);
        });
};

// 缓存作品的点赞用户列表
MdTongrenLike.cacheLikeRecordByWorkId = function(workId) {
    let expireTime = CACHE_PARAMS.EXPIRE_TIME,
        LIKE_KEY = `md_tongren_like:${workId}`;

    return MdTongrenLike.getLikeRecordByWorkId(workId)
        .then(userIdsHash => {
            let userIds = _.map(userIdsHash, 'UserId');
            if(!_.isEmpty(userIds)) {
                return getRedis().delAsync(LIKE_KEY)
                    .then(() => {
                        return getRedis().saddAsync(LIKE_KEY, userIds);
                    })
                    .then(() => {
                        return getRedis().expireAsync(LIKE_KEY, expireTime);
                    })
                    .then(() => {
                        return userIds;
                    });
            }
        });
};

// 取消作品的所有点赞
MdTongrenLike.unlikeAllByWorkId = function(workId) {
    let condition = {
        'WorkId': workId
    };
    let props = {
        'Status': LIKE_STATUS.CANCEL
    };
    return this.updateByCondition(condition, props);
};