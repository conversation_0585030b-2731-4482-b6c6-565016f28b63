import { TABLE_NAMES } from "../common/constants";
import BaseModelClass = require("./BaseModelClass");

export interface OAUnFollowRecord {
  RoleId: number;
  TargetId: number;
  ID: number;
  CreateTime: number
}

class OfficialAccountUnFollowClass extends BaseModelClass<OAUnFollowRecord> {
  constructor() {
    super(TABLE_NAMES.PyqOfficialAccountUnFollow);
  }

  async isUnFollow(roleId: number, targetId: number) {
    let record = await this.findOne({ RoleId: roleId, TargetId: targetId }, ["ID"]);
    return record && record.ID;
  }

  async getUnFollowIRelationID(roleId: number, targetId: number): Promise<number> {
    let record = await this.findOne({ RoleId: roleId, TargetId: targetId }, ["ID"]);
    return record?.ID;
  }

  async getUnFollowIds(roleId: number) {
    let rows = await this.find({ RoleId: roleId }, { cols: ["TargetId"] });
    return rows.map((r) => r.TargetId);
  }

  async filterFollowed(roleId: number, targetIds: number[]) {
    let rows = await this.find({ RoleId: roleId, targetId: targetIds }, { cols: ["TargetId"] });
    let unFollowed = new Set(rows.map((r) => r.TargetId));
    let followed = targetIds.filter((r) => !unFollowed.has(r));
    return followed;
  }
}

export let OfficialAccountUnFollow = new OfficialAccountUnFollowClass();
