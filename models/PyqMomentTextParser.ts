/**
 * Created by zhenhua on 2017/7/14.
 */

import * as _ from "lodash";

const SuperColor = {
  P: "#ff40ff",
  N: "#b59852",
  M: "#ff1200",
  A: "#7ab65a",
  R: "#ff0000",
  G: "#01bf02",
  B: "#0000ff",
  Y: "#ca7027",
  W: "#837b7b",
  K: "#000000",
  V: "#8000ff",
};

export function parse(str) {
  const LOOK_AHEAD_CHARS = 30;
  const specialPatterns = [
    /^<link button=/,
    /^<link item=/,
    /^#u/,
    /^#n/,
    /^\[[sbi]]/,
    /^\[sub]/,
    /^#r/,
    /^#[PNMARGBYWKV]/,
    /^#c[0-9a-f]{3,6}/i,
  ];
  let tagBeginCount = 0;
  let pos = 0;

  function readUtil(predict) {
    let value = "";
    while (pos < str.length) {
      const ch = str[pos];
      if (predict(ch)) {
        pos--;
        break;
      }
      value += ch;
      pos++;
    }
    return value;
  }

  function readButtonLinkValues() {
    const csvStr = readUtil((ch) => ch === ">");
    pos += 2; // skip '>'
    const values = _.split(csvStr, ",");
    const ast: any = { type: "ButtonLink" };
    ast.buttonName = values[0];
    ast.buttonType = values[1];
    ast.args = _.slice(values, 2);
    return ast;
  }

  function readItemLinkValues() {
    const csvStr = readUtil((ch) => ch === ">");
    pos += 2; // skip '>'
    const values = _.split(csvStr, ",");
    const ast: any = { type: "ItemLink" };
    ast.itemId = values[0];
    ast.itemCode = values[1];
    ast.itemName = values[2];
    ast.itemColor = values[3];
    return ast;
  }

  function isSpecialToken() {
    return !!tryToReadSpecialToken();
  }

  function tryToReadSpecialToken() {
    const checkStr = str.substr(pos, LOOK_AHEAD_CHARS);
    for (let i = 0; i < specialPatterns.length; i++) {
      const pattern = specialPatterns[i];
      const match = checkStr.match(pattern);
      if (match) {
        return match[0];
      }
    }
    return null;
  }

  function readNormalText() {
    const text = readUtil(isSpecialToken);
    return { type: "Text", text: text };
  }

  function readTag(token) {
    const tagObj = { type: "Tag", tag: "span", color: "", asts: [] };
    if (token === "#u") {
      // underline 下划线使用特殊的#u标记，其他且为[\w+]形式
      tagObj.tag = "u";
    } else if (/^#[PNMARGBYWKV]/.test(token)) {
      tagObj.color = SuperColor[token[1]];
    } else if (/^#[0-9a-f]{3,6}/i.test(token)) {
      tagObj.color = "#" + token.substr(2);
    } else {
      const match = token.match(/\[(\w+)]/);
      tagObj.tag = match[1];
    }
    tagBeginCount++;

    function closeTag(token) {
      if (token === "#n") {
        // 结束tag标记
        if (tagBeginCount > 1) {
          pos -= 2; // 用一个#n标记闭合在这之前所有tag
        }
        tagBeginCount--;
        return true;
      } else {
        return false;
      }
    }
    tagObj.asts = parserToASTUtil(closeTag);
    return tagObj;
  }

  function parseSpecialToken(token) {
    if (token === "<link button=") {
      return readButtonLinkValues();
    } else if (token === "<link item=") {
      return readItemLinkValues();
    } else if (token === "#r") {
      return { type: "NewLine" };
    } else if (token === "#n") {
      return { type: "recoverNormal" };
    } else {
      return readTag(token);
    }
  }

  function parserToASTUtil(predict) {
    const asts = [];
    while (pos < str.length) {
      let ast;
      const token = tryToReadSpecialToken();
      if (token) {
        pos += token.length;
        if (predict(token)) {
          break;
        }
        ast = parseSpecialToken(token);
      } else {
        ast = readNormalText();
        pos++;
      }
      asts.push(ast);
    }
    return asts;
  }

  function parserToAst() {
    return parserToASTUtil((token) => false);
  }

  return parserToAst();
}

function textNodeToHtml(astNode) {
  return astNode.text;
}

function webLinkClickButtonToHtml(astNode) {
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  const WebLinkValidHost = require("../models/PyqMoments").WebLinkValidHost;
  const hostIndex = astNode.args[1];
  const relativeUrl = astNode.args[0];
  const url = WebLinkValidHost[hostIndex] + relativeUrl;
  return `<a href="${url}" target="_blank">${astNode.buttonName}</a>`;
}

function buttonLinkToHtml(astNode) {
  if (astNode.buttonType === "WebLinkClick") {
    return webLinkClickButtonToHtml(astNode);
  }
  return `<span style="color:#00C13A">[${astNode.buttonName}]</span>`;
}

function itemLinkToHtml(astNode) {
  return `<span style="color:#${astNode.itemColor}">${astNode.itemName}</span>`;
}

function tagNodeToHtml(astNode) {
  const tag = astNode.tag;
  const innerHtml = astsToHtml(astNode.asts);
  if (astNode.color) {
    return `<${tag} style="color:${astNode.color}">${innerHtml}</${tag}>`;
  } else {
    return `<${tag}>${innerHtml}</${tag}>`;
  }
}

function astToHtml(astNode) {
  switch (astNode.type) {
    case "Text":
      return textNodeToHtml(astNode);
    case "ButtonLink":
      return buttonLinkToHtml(astNode);
    case "ItemLink":
      return itemLinkToHtml(astNode);
    case "Tag":
      return tagNodeToHtml(astNode);
    case "NewLine":
      return "<br>";
    default:
      return "";
  }
}

function astsToHtml(asts) {
  return _.map(asts, astToHtml).join("");
}

export function parseToHtml(str) {
  const asts = parse(str);
  return astsToHtml(asts);
}
