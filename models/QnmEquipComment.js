"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteEquipComment = exports.listCommentsText = exports.removeComment = exports.addComment = void 0;
const redisCollection_1 = require("../common/redisCollection");
const QnmRoleInfo = require("./QNMRoleInfos");
const util_1 = require("../common/util");
const logger = require("../common/logger");
const config_1 = require("../pyq-server/common/config");
const cacheUtil_1 = require("../common/cacheUtil");
const errorCodes_1 = require("../pyq-server/errorCodes");
const fs = require("fs");
// 装备评论只保留最新200条
function getEquipCommentList(equipId) {
    const key = `qnm:pyq:equip_comments:${config_1.equipRankCfg.version}:${equipId}`;
    return new redisCollection_1.FixedStringList({ size: 200, key: key });
}
function addComment(ip, equipId, text) {
    return __awaiter(this, void 0, void 0, function* () {
        const lockKey = `qnm:pyq:equip_comments:add_lock:${config_1.equipRankCfg.version}:${equipId}:${ip}`;
        const isLocked = yield cacheUtil_1.OperationInterval.locked(lockKey, config_1.equipRankCfg.commentInterval * 1000);
        if (isLocked) {
            return (0, errorCodes_1.BussError)(errorCodes_1.errorCodes.EquipCommentTooOften);
        }
        const list = getEquipCommentList(equipId);
        return list.push(text);
    });
}
exports.addComment = addComment;
function removeComment(equipId, text) {
    return __awaiter(this, void 0, void 0, function* () {
        const list = getEquipCommentList(equipId);
        const removeRet = yield list.remove(text);
        return removeRet;
    });
}
exports.removeComment = removeComment;
function listCommentsText(equipId) {
    const list = getEquipCommentList(equipId);
    return list.getAll();
}
exports.listCommentsText = listCommentsText;
function genBingQiPuChatLogItem(roleid, equipGk, text) {
    return __awaiter(this, void 0, void 0, function* () {
        const info = (yield QnmRoleInfo.findOne({ RoleId: roleid }, ["ServerId", "RoleName", "UserName", "Vip", "Level"])) || {};
        const targetId = (0, util_1.hexMd5)(text);
        const logItem = {
            server: info.ServerId,
            account_id: info.UserName,
            role_id: roleid,
            role_name: info.RoleName,
            role_level: info.Level,
            u_vip: info.Vip,
            gk: equipGk,
            chat: text,
            TargetId: targetId,
            CreateTime: Date.now(),
        };
        return logItem;
    });
}
const logTypeName = "bingqipuchat";
function makeSureCreateLogDir() {
    const logPath = logger.getLogPath();
    if (!fs.existsSync(logPath)) {
        (0, util_1.mkDirsSync)(logger.getLogPath(), logTypeName);
    }
    return logPath;
}
makeSureCreateLogDir();
function logBingQiPuChat(roleid, equipGk, text) {
    return __awaiter(this, void 0, void 0, function* () {
        const logItem = yield genBingQiPuChatLogItem(roleid, equipGk, text);
        const today = (0, util_1.formatDate)(new Date(), "yyyy-MM-dd");
        const loggerFile = logTypeName + "_" + today;
        logger.add(loggerFile, `[${logTypeName}],` + JSON.stringify(logItem), { subPath: `/${logTypeName}` });
    });
}
function deleteEquipComment(equipGk, textMd5) {
    return __awaiter(this, void 0, void 0, function* () {
        const texts = yield listCommentsText(equipGk);
        let delText = null;
        for (const t of texts) {
            if ((0, util_1.hexMd5)(t) === textMd5) {
                delText = t;
                break;
            }
        }
        if (delText) {
            const list = getEquipCommentList(equipGk);
            const delCount = yield list.remove(delText);
            return delCount;
        }
        else {
            return 0;
        }
    });
}
exports.deleteEquipComment = deleteEquipComment;
//# sourceMappingURL=QnmEquipComment.js.map