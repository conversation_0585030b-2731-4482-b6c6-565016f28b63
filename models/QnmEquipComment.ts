import { FixedStringList } from "../common/redisCollection";
import * as QnmRoleInfo from "./QNMRoleInfos";
import { hexMd5, formatDate, mkDirsSync } from "../common/util";
import * as logger from "../common/logger";
import { equipRankCfg } from "../pyq-server/common/config";
import { OperationInterval } from "../common/cacheUtil";
import { BussError, errorCodes } from "../pyq-server/errorCodes";
import * as fs from "fs";

// 装备评论只保留最新200条
function getEquipCommentList(equipId) {
  const key = `qnm:pyq:equip_comments:${equipRankCfg.version}:${equipId}`;
  return new FixedStringList({ size: 200, key: key });
}

export async function addComment(ip: string, equipId: string, text: string) {
  const lockKey = `qnm:pyq:equip_comments:add_lock:${equipRankCfg.version}:${equipId}:${ip}`;
  const isLocked = await OperationInterval.locked(lockKey, equipRankCfg.commentInterval * 1000);
  if (isLocked) {
    return BussError(errorCodes.EquipCommentTooOften);
  }
  const list = getEquipCommentList(equipId);
  return list.push(text);
}

export async function removeComment(equipId: string, text: string) {
  const list = getEquipCommentList(equipId);
  const removeRet = await list.remove(text);
  return removeRet;
}

export function listCommentsText(equipId): Promise<string[]> {
  const list = getEquipCommentList(equipId);
  return list.getAll();
}

// 兵器谱日志监控字段
interface BingQiPuChatItem {
  server: number;
  account_id: string;
  role_id: number;
  role_name: string;
  role_level: number;
  u_vip: number; //角色vip等级
  gk: string; // 装备gk
  chat: string;
  TargetId; // 评论唯一编号
  CreateTime; //时间戳
}

async function genBingQiPuChatLogItem(roleid: number, equipGk: string, text: string): Promise<BingQiPuChatItem> {
  const info =
    (await QnmRoleInfo.findOne({ RoleId: roleid }, ["ServerId", "RoleName", "UserName", "Vip", "Level"])) || {};
  const targetId = hexMd5(text);
  const logItem: BingQiPuChatItem = {
    server: info.ServerId,
    account_id: info.UserName,
    role_id: roleid,
    role_name: info.RoleName,
    role_level: info.Level,
    u_vip: info.Vip,
    gk: equipGk,
    chat: text,
    TargetId: targetId,
    CreateTime: Date.now(),
  };
  return logItem;
}

const logTypeName = "bingqipuchat";

function makeSureCreateLogDir() {
  const logPath = logger.getLogPath();
  if (!fs.existsSync(logPath)) {
    mkDirsSync(logger.getLogPath(), logTypeName);
  }
  return logPath;
}

makeSureCreateLogDir();

async function logBingQiPuChat(roleid: number, equipGk: string, text: string): Promise<void> {
  const logItem = await genBingQiPuChatLogItem(roleid, equipGk, text);

  const today = formatDate(new Date(), "yyyy-MM-dd");
  const loggerFile = logTypeName + "_" + today;
  logger.add(loggerFile, `[${logTypeName}],` + JSON.stringify(logItem), { subPath: `/${logTypeName}` });
}

export async function deleteEquipComment(equipGk: string, textMd5: string) {
  const texts = await listCommentsText(equipGk);
  let delText = null;
  for (const t of texts) {
    if (hexMd5(t) === textMd5) {
      delText = t;
      break;
    }
  }
  if (delText) {
    const list = getEquipCommentList(equipGk);
    const delCount = await list.remove(delText);
    return delCount;
  } else {
    return 0;
  }
}
