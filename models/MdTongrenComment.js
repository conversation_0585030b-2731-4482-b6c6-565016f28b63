let _ = require("lodash");

let BaseModelClass = require('./BaseModelClass');
let TongrenUserModel = require('./MdTongrenUser');
let MdTongrenComment = _.extend(new BaseModelClass("md_tongren_comment"));
let { getRedis } = require('../common/redis');

const COMMENT_STATUS = {
    NORMAL: 0, // 正常
    DELETE: -1 // 删除
};

const COMMENT_CONFIG = {
    pageSize: 1000
};

/* Redis缓存参数 */
const CACHE_PARAMS = {
    EXPIRE_TIME: 7 * 24 * 60 * 60, // 一个星期
    DEFAULT_CACHE_PAGE: 1
};

MdTongrenComment.COMMENT_STATUS = COMMENT_STATUS;
MdTongrenComment.COMMENT_CONFIG = COMMENT_CONFIG;

module.exports = MdTongrenComment;

// 添加评论
MdTongrenComment.add = function(commentInfo) {
    return this.create({
        'UserId': commentInfo.userId,
        'AuthorId': commentInfo.authorId,
        'WorkId': commentInfo.workId,
        'ReplyId': commentInfo.replyId,
        'Text': commentInfo.replyContent,
        'CreateTime': commentInfo.createTime,
        'Status': COMMENT_STATUS.NORMAL
    });
};

// 添加评论 + 更新缓存
MdTongrenComment.addAndUpdateCache = function(commentInfo) {
    let workId = commentInfo.workId;
    let COMMENT_KEY = `md_tongren_comment:${workId}`;
    return MdTongrenComment.add(commentInfo)
        .then(commentDetail => {
            return getRedis().existsAsync(COMMENT_KEY)
                .then(isExist => {
                    if(!isExist) {
                        return MdTongrenComment.cacheListByWorkId(workId);
                    } else {
                        let score = commentDetail.CreateTime || 0;
                        return getRedis().zaddAsync(COMMENT_KEY, score, JSON.stringify(commentDetail));
                    }
                })
                .then(() => {
                    return commentDetail;
                });
        });
};

// 删除评论
MdTongrenComment.delete = function(commentId) {
    let condition = {
        'ID': commentId
    };
    let props = {
        'Status': COMMENT_STATUS.DELETE
    };
    return this.updateByCondition(condition, props);
};

// 删除评论 + 更新缓存
MdTongrenComment.deleteAndUpdateCache = function(commentId, workId) {
    return MdTongrenComment.delete(commentId)
        .then(() => {
            return MdTongrenComment.cacheListByWorkId(workId);
        });
};

// 删除作品的所有评论
MdTongrenComment.deleteAllByWorkId = function(workId) {
    let condition = {
        'WorkId': workId
    };
    let props = {
        'Status': COMMENT_STATUS.DELETE
    };
    return this.updateByCondition(condition, props);
};

// 根据作品ID获取评论列表
MdTongrenComment.getListByWorkId = function(workId, page, pageSize) {
    let selectOpt = ['ID', 'UserId', 'ReplyId as TargetId', 'AuthorId', 'Text', 'CreateTime'];
    let query = this.scope()
        .where('WorkId', workId)
        .andWhere('Status', COMMENT_STATUS.NORMAL)
        .select(selectOpt)
        .orderBy('CreateTime', 'desc');
    let pagination = {
        page: page,
        pageSize: pageSize
    };

    return this.queryWithPagination(query, pagination);
};

// 从缓存中读取作品的评论列表
MdTongrenComment.getListByWorkIdFromChache = function(workId, page, pageSize) {
    let cachePage = CACHE_PARAMS.DEFAULT_CACHE_PAGE,
        COMMENT_KEY = `md_tongren_comment:${workId}`;
    if(page > cachePage) {
        // 超出默认缓存页数的，直接读数据库
        let page = 1,
            pageSize = COMMENT_CONFIG.pageSize;
        return MdTongrenComment.getListByWorkId(workId, page, pageSize);
    } else {
        return getRedis().ttlAsync(COMMENT_KEY)
            .then(ttl => {
                if(ttl <= 0) {
                    // 写缓存
                    return MdTongrenComment.cacheListByWorkId(workId);
                }
            })
            .then(() => {
                // 读缓存
                let start = (page - 1) * pageSize;
                let stop = start + pageSize - 1;
                return getRedis().zcardAsync(COMMENT_KEY)
                    .then(function (total) {
                        stop = stop < total ? stop: total - 1;
                        return getRedis().zrevrangeAsync(COMMENT_KEY, start, stop);
                    })
                    .then(commentList => {
                        return _.map(commentList, comment => {
                            return JSON.parse(comment);
                        });
                    });
            });
    }
};

// 缓存作品的评论列表
MdTongrenComment.cacheListByWorkId = function(workId) {
    let expireTime = CACHE_PARAMS.EXPIRE_TIME,
        COMMENT_KEY = `md_tongren_comment:${workId}`,
        CUR_SCORE_FIELD = 'CreateTime',
        page = 1,
        pageSize = COMMENT_CONFIG.pageSize;
    return MdTongrenComment.getListByWorkId(workId, page, pageSize)
        .then(commentList => {
            return getRedis().delAsync(COMMENT_KEY)
                .then(() => {
                    if(!_.isEmpty(commentList)) {
                        // 写缓存
                        let addArray = [];
                        _.forEach(commentList, comment => {
                            let score = comment[CUR_SCORE_FIELD] || 0;
                            addArray.push(score);
                            addArray.push(JSON.stringify(comment));
                        });
                        addArray = _.concat(COMMENT_KEY, addArray);
                        return getRedis().zaddAsync.apply(getRedis(), addArray);
                    }
                })
                .then(() => {
                    return getRedis().expireAsync(COMMENT_KEY, expireTime);
                });
        });
};

// 根据评论ID集合获取对应的作品ID
MdTongrenComment.getWorkListByCommentIds = function (commentIds) {
    let query = this.scope()
        .whereIn('ID', commentIds)
        .select('ID as CommentId', 'WorkId');
    return this.executeByQuery(query)
        .then(data => {
            return data;
        });
};

// 判断作品是否允许被删除
MdTongrenComment.checkDeletePermissionById = function(userId, commentId) {
    let selectOpt = ['UserId', 'AuthorId', 'Status'];
    return this.findById(commentId, selectOpt).then(data => {
        if(data.Status != COMMENT_STATUS.NORMAL) {
            return Promise.reject({msg: '评论不存在'});
        } else if(userId != data.AuthorId && userId != data.UserId) {
            return Promise.reject({msg: '没有删除权限'});
        } else {
            return true;
        }
    });

};

// 获取评论列表
MdTongrenComment.getFullCommentList = function(workId, userId, page, pageSize) {
    let curTime = (new Date()).getTime();
    return MdTongrenComment.getListByWorkIdFromChache(workId, page, pageSize)
        .then(data => {
            // 获取回复的用户ID集合
            let replyUserIdSet = [];
            _.forEach(data, (comment, key) => {
                let targetId = comment.TargetId;
                if(targetId) {
                    let replyItem = _.find(data, {'ID': targetId});
                    if(_.isObject(replyItem)) {
                        comment.ReplyId = replyItem.UserId;
                        replyUserIdSet.push(comment.ReplyId);
                    }
                }
            });

            // 获取全部评论用户的ID集合
            let userIdSet = _.concat(_.map(data, 'UserId'), replyUserIdSet);
            userIdSet = _.uniq(userIdSet);

            return {
                data: data,
                userIdSet: userIdSet
            };
        })
        .then(res => {
            // 获取评论者和回复者的用户信息
            let data = res.data;
            let userIdSet = res.userIdSet;
            return TongrenUserModel.getUsersByIds(userIdSet, userId).then(userData => {
                _.forEach(data, (value) => {
                    if(value.ReplyId) {
                        value.ReplyUserInfo = _.find(userData, {'ID': value.ReplyId});
                    }
                    value.UserInfo = _.find(userData, {'ID': value.UserId});
                });
                return data;
            });
        })
        .then(data => {
            _.forEach(data, (item) => {
                // 计算评论时间间隔
                item.Duration = curTime - item.CreateTime;
                // 判断当前登录用户是否允许删除评论
                if(userId && (userId == item.AuthorId || userId == item.UserId)) {
                    item.AllowDelete = true;
                } else {
                    item.AllowDelete = false;
                }
            });
            return data;
        });
};

// 获取评论详情
MdTongrenComment.getCommentInfoById = function(commentId) {
    let selectOpt = ['ID', 'UserId', 'ReplyId as TargetId', 'AuthorId', 'Text', 'CreateTime', 'WorkId'];
    let query = this.scope()
        .where('ID', commentId)
        .andWhere('Status', COMMENT_STATUS.NORMAL)
        .select(selectOpt);
    return this.executeByQuery(query)
        .then(commentInfo => {
            return commentInfo[0];
        });
};