"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSlaveModelByName = exports.getSlaveModel = exports.getModelByTableName = void 0;
let tableNameHash = require("./meta.json");
const ModelCacheGroup = {
    MASTER: {},
    SLAVE: {},
};
function getModelByTableName(tableName, dbNode) {
    dbNode = dbNode || "MASTER";
    let filename = tableNameHash[tableName];
    let modelCache = ModelCacheGroup[dbNode];
    if (!filename)
        throw new Error(`require model ${tableName} failed`);
    if (filename) {
        if (!modelCache[tableName]) {
            let model = require("./" + filename);
            if (!model)
                throw new Error("require model ${tableName} is empty");
            let newModel = Object.create(model);
            newModel.dbNode = dbNode;
            modelCache[tableName] = newModel;
        }
        return modelCache[tableName];
    }
}
exports.getModelByTableName = getModelByTableName;
function getSlaveModel(model) {
    return getModelByTableName(model.tableName, "SLAVE");
}
exports.getSlaveModel = getSlaveModel;
function getSlaveModelByName(tableName) {
    return getModelByTableName(tableName, "SLAVE");
}
exports.getSlaveModelByName = getSlaveModelByName;
//# sourceMappingURL=ModelManager.js.map