/**
 * Created by zhenhua on 2017/4/24.
 */

import BaseModelClass = require("./BaseModelClass");
import * as _ from "lodash";

const InformTypes = {
  LIKE_MOMENT: 0,
  COMMENT_MOMENT: 1,
  REPLY_MOMENT: 2,
  FORWARD_MOMENT: 3,
};

interface InformRecord {
  ID: number;
  RoleId: number;
  TargetId: number;
  ObjectId: number;
  RelateId: string;
  Text?: string;
  Type: number;
  Status: number;
  CreateTime: number;
}

class InformModelClass extends BaseModelClass<InformRecord> {
  public InformTypes = InformTypes;
  async add(props: Partial<InformRecord>): Promise<{ insertId: number }> {
    props = _.defaults(props, { CreateTime: Date.now() });
    if (props.RoleId !== props.TargetId) {
      return this.insert(props);
    } else {
      return null;
    }
  }
}

const InformModel = new InformModelClass("pyq_inform");

export = InformModel;
