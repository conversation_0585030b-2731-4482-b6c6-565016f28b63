var BaseModelClass = require('./BaseModelClass');
var AuditModel = require('./mixins/AuditModel');
var LikeAble = require('./mixins/LikeAble');
var _ = require('lodash');
var Users = require('./Users');
var Promise = require('bluebird');
var MdEventBus = require('../md-server/eventBus');
var util = require('../common/util');



var RealityShow = {
  checkApplyCondition: function (user) {
    if(!user.Avatar) {
      return Promise.reject({errorType:"ApplyShowWithoutAvatar", msg:"您必须先上传头像才能申请真人秀验证!"});
    }
    if(user.Gender === null) {
      return Promise.reject({errorType:"ApplyShowWithoutGender", msg:"您必须先设置性别才能申请真人秀验证!"});
    }
    return Promise.resolve(user);
  },

  applyAudit: function(user) {
    return RealityShow.checkApplyCondition(user)
      .then(function () {
        return RealityShow.isUserApplyWithNewAvatar(user.ID, user.Avatar)
          .then(function(isUserApplyNewAvatar) {
            if(isUserApplyNewAvatar) {
              return RealityShow.softDeleteByCondition({UserId:user.ID})
                .then(function() {
                  return RealityShow.create({
                    UserId: user.ID,
                    Avatar: user.Avatar,
                    applyTime: Date.now(),
                  });
                });
            } else {
              return RealityShow.findOne({UserId: user.ID, Avatar: user.Avatar})
                .then(function (realityShow) {
                  if(RealityShow.isSoftDeleted(realityShow)) {
                    return RealityShow.updateById(realityShow.ID, {
                        Status: RealityShow.Statuses.NORMAL,
                        AuditStatus: RealityShow.AUDIT_STATUSES.AUDITING,
                        ApplyTime: Date.now()
                      }
                    );
                  } else {
                    if(RealityShow.isAuditReject(realityShow)) {
                      return Promise.reject({errorType:"ApplyWithRejectAvatar", msg:"该头像申请失败，请更换头像后再申请认证！"});
                    } else {
                      return realityShow;
                    }
                  }
                });
            }
          });
      })
  },

  isUserApplyWithNewAvatar: function(userId, avatar) {
    return RealityShow.notExist({UserId: userId, Avatar: avatar});
  },

  findByUserIds: function(userIds, selectProps) {
    return RealityShow.where({UserId: userIds, Status: RealityShow.Statuses.NORMAL}, {cols: selectProps});
  },

  fillUsersWithRealityShow: function(users) {
    var userIds = _.uniq(_.map(users, 'ID'));
    return RealityShow.findByUserIds(userIds, ['ID', 'UserId', 'AuditStatus'])
      .then(function(realityShows) {
        return RealityShow.embededRealityShowForUsers(users, realityShows);
      });
  },

  fillUserWithRealityShow: function(user) {
    return RealityShow.fillUsersWithRealityShow([user]).then(function(users) {
      return users[0];
    });
  },

  embededRealityShowForUsers: function(users, realityShows) {
    var userIdToRealityShow = _.reduce(realityShows, function(res, realityShow) {
      res[realityShow.UserId] = _.pick(realityShow, 'ID', 'AuditStatus');
      return res;
    }, {});
    users = _.map(users, function(user) {
      user.RealityShow = userIdToRealityShow[user.ID] || null;
      return user;
    });
    return users;
  },


  listByNew: function (pagination) {
    var query = RealityShow.normalScope()
      .select(['ID', 'UserId', 'Avatar', 'AuditTime', 'ZanList'])
      .where('AuditStatus',RealityShow.AUDIT_STATUSES.PASSED)
      .orderBy('AuditTime', 'desc');
    return RealityShow.queryWithPagination(query, pagination);
  },

  listByHot: function (pagination) {
    var query = RealityShow.normalScope()
      .select(['ID', 'UserId', 'Avatar', 'AuditTime', 'ZanList'])
      .where('AuditStatus',RealityShow.AUDIT_STATUSES.PASSED)
      .orderBy('Hot', 'desc');
    return RealityShow.queryWithPagination(query, pagination);
  },
  
  listEvents: function (size) {
    return RealityShow.executeByQuery(
      RealityShow.scope()
        .from(RealityShow.tableName + " as r")
        .leftJoin(Users.tableName + " as u", "r.UserId", 'u.ID')
        .select('r.ID', 'r.UserId', 'r.Avatar', 'r.AuditTime', 'r.ZanList', 'u.NickName')
        .where('r.AuditStatus', RealityShow.AUDIT_STATUSES.PASSED)
        .where('r.Status', RealityShow.Statuses.NORMAL)
        .orderBy('r.AuditTime', 'desc')
        .limit(size)
    )
  },
  
  searchByNickNameLike: function (str, pagination) {
    var query = RealityShow.scope()
        .from(RealityShow.tableName + " as r")
        .leftJoin(Users.tableName + " as u", "r.UserId", 'u.ID')
        .select('r.ID', 'r.UserId', 'r.Avatar', 'r.AuditTime', 'r.ZanList', 'u.NickName')
        .where('u.NickName', 'like', `%${RealityShow.escapeLikeStr(str)}%`)
        .where('r.AuditStatus', RealityShow.AUDIT_STATUSES.PASSED)
        .where('r.Status', RealityShow.Statuses.NORMAL)
        .orderBy('r.AuditTime', 'desc');
    return RealityShow.queryWithPagination(query, pagination)
  },

  like: function (userId, id) {
    let Notifications = require('./Notifications');
    let self = this;
    return LikeAble.like.bind(self)(userId, id, {
      beforeUpdate: function (props) {
        props.Hot = util.csvStrToIntArray(props.ZanList).length;
        return props;
      }
    }).then(function (data) {
      return RealityShow.findById(id, ['ID', 'UserId']).then(function (r) {
        if(r) {
          return Notifications.addLikeRealityShowEvent(userId, r);
        }
      }).then(() => {
        return data;
      });
    });
  },

  unLike: function (userId, id) {
    let Notifications = require('./Notifications');
    let self = this;
    return LikeAble.unLike.bind(self)(userId, id, {
      beforeUpdate: function (props) {
        props.Hot = util.csvStrToIntArray(props.ZanList).length;
        return props;
      }
    }).then(function (data) {
      return RealityShow.findById(id, ['ID', 'UserId']).then(function (r) {
        if(r) {
          return Notifications.softDeleteLikeRealityShowEvent(userId, r);
        }
      }).then(() => {
        return data;
      })
    });
  }
};

MdEventBus.on('onChangeAvatar', function(data) {
  // var avatar = data.avatar;
  var userId = data.userId;

  return RealityShow.softDeleteByCondition({UserId: userId})
    .catch(function(err){
      logger.error(err);
    });
});


RealityShow = _.extend({}, new BaseModelClass("md_reality_show"), AuditModel, LikeAble, RealityShow);
module.exports = RealityShow;
