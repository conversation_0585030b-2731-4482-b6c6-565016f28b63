import { DBClient } from "../common/db2";
import { getRedis, IExistResult } from "../common/redis";
import * as knex from 'knex'
import { testCfg } from '../common/config'
import * as _ from 'lodash'

type IQueryAble = knex.QueryBuilder | knex.Raw

interface Record {
  RoleId: number
}

export abstract class RoleIdPartitionModel<T extends Record> {
  protected dbClient: DBClient = DBClient.getInstance()
  private static sqlBuilder: knex = knex({ client: 'mysql' })
  protected rowRecordExpire = 30 * 60 //30分钟

  abstract tableName(): string

  allTables(): string[] {
    let name = this.tableName()
    let list: string[] = [name]
    for (let i = 0; i < 10; i++) {
      let pname = name + '_' + i
      list.push(pname)
    }
    return list
  }

  getPartitionTableName(roleId: number): string {
    let partitionId = roleId % 10
    return this.tableName() + '_' + partitionId
  }

  getOriginTableName() {
    return this.tableName()
  }

  scope(roleId?: number) {
    if (roleId) {
      return RoleIdPartitionModel.sqlBuilder.from(this.getPartitionTableName(roleId))
    } else {
      return RoleIdPartitionModel.sqlBuilder.from(this.getOriginTableName())
    }
  }

  migrateRowToPartition(record: T) {
    return this.insertOrUpdate(record)
  }

  async findOneByQuery(query: knex.QueryBuilder): Promise<Partial<T>> {
    query = query.limit(1)
    let list: Partial<T>[] = await this.executeByQuery(query)
    let row = list[0]
    return row || null
  }

  private async setRowRecordToCache(roleId: number, record: T) {
    let key = this.getRowRecordCacheKey(roleId)
    if (record) {
      let recordStr = JSON.stringify(record)
      await getRedis().setAsync(key, recordStr)
      await getRedis().expireAsync(key, this.rowRecordExpire)
    }
  }

  private async markRowRecordCacheInvalid(roleId: number) {
    let key = this.getRowRecordCacheKey(roleId)
    let info = await getRedis().delAsync(key)
    return info
  }

  private getRowRecordCacheKey(roleId: number) {
    let key = 'md_models:' + this.tableName() + ':record:' + roleId
    return key
  }

  private async findByRoleIdByCache(roleId: number, cols?: string[]): Promise<Partial<T>> {
    let key = this.getRowRecordCacheKey(roleId)
    let existResult = await getRedis().existsAsync(key)
    let fullRecord = null
    if (existResult === IExistResult.NotExist) {
      fullRecord = await this.findByRoleIdByDB(roleId)
      await this.setRowRecordToCache(roleId, fullRecord)
    } else {
      let recordStr = await getRedis().getAsync(key)
      fullRecord = JSON.parse(recordStr)
    }
    if (fullRecord) {
      let result = { RoleId: roleId } as Partial<T>
      if (cols) {
        for (let c of cols) {
          result[c] = fullRecord[c]
        }
      } else {
        result = fullRecord
      }
      return result
    } else {
      return null
    }
  }

  private async findByRoleIdByDB(roleId: number): Promise<Partial<T>> {
    let query1 = this.scope(roleId).where('RoleId', roleId)
    let row = await this.findOneByQuery(query1)
    if (row) {
      return row
    } else {
      let query2 = this.scope().where('RoleId', roleId)
      let row = await this.findOneByQuery(query2) as unknown as T
      if (row) {
        await this.migrateRowToPartition(row)
      }
      return row
    }
  }

  async findByRoleId(roleId: number, cols?: string[]): Promise<Partial<T>> {
    return this.findByRoleIdByCache(roleId, cols)
  }

  async findByRoleIdFromOrigin(roleId: number, cols?: string[]): Promise<Partial<T>> {
    let query = this.scope().where('RoleId', roleId).select(cols)
    let rows = await this.executeByQuery(query)
    return rows[0]
  }

  async executeByQuery(query: IQueryAble): Promise<Partial<T>[]> {
    let sql = query.toString()
    if (testCfg.db_debug) {
      console.log(sql);
    }
    return this.dbClient.execute(sql)
  }

  async findByRoleIds(roleIds: number[], cols: string[]): Promise<Partial<T>[]> {
    let list: Partial<T>[] = []
    for (let r of roleIds) {
      let item = await this.findByRoleId(r, cols)
      list.push(item)
    }
    return list
  }

  async getFullRecordFromOriginTable(roleId: number) {
    let query = this.scope().select('*').where('RoleId', roleId)
    let rows = await this.executeByQuery(query) as T[]
    return rows[0] || null
  }

  async insertOrUpdate(props: Partial<T>, updateProps?: Partial<T>) {
    if (!updateProps) {
      updateProps = props
    }
    let query = this.scope(props.RoleId).where('RoleId', props.RoleId)
    let rows = await this.executeByQuery(query)
    let recordInNewTable = rows[0]
    if (recordInNewTable) {
      let query = this.scope(props.RoleId).where('RoleId', props.RoleId).update(updateProps)
      let result = await this.executeByQuery(query)
      await this.markRowRecordCacheInvalid(props.RoleId)
      return result
    } else {
      let recordInOldTable = await this.getFullRecordFromOriginTable(props.RoleId)
      let insertProps = Object.assign({}, recordInOldTable, props)
      await this.markRowRecordCacheInvalid(props.RoleId)
      return this.insert(insertProps)
    }
  }

  raw(sql: string): knex.Raw {
    return RoleIdPartitionModel.sqlBuilder.raw(sql)
  }

  async insert(props: Partial<T>) {
    let query = this.scope(props.RoleId).insert(props)
    let result = await this.executeByQuery(query)
    return result
  }
}