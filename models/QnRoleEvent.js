﻿/**
 * Created by <PERSON><PERSON><PERSON> on 16-11-25.
 */

var _ = require('lodash');
var BaseModelClass = require('./BaseModelClass');
let EventList = require('../service/qn/data/EventList');
const QnServerTransfer = require('../models/QnServerTransfer');

var QnRoleEvent = _.extend(new BaseModelClass("qn_roleevent"), {
  insert: function (props) {
    props = _.defaults(props, {Priority: EventList.getPriority(props.Type), PublishTime:Date.now()});
    return BaseModelClass.prototype.insert.bind(this)(props);
  },

  /**
   * 将推送的事件同步到DB
   * @param props
   * @returns {*}
   */
  syncToDB: function (props) {
    let deDupEventTypes = EventList.getDeDupEventIds();
    let roleId = props.RoleId;
    let type = parseInt(props.Type);
    let allEventTypes = EventList.getEventIds();
    let parameter = props.Parameter;
    if(!_.includes(allEventTypes, type)) {
      return Promise.resolve(null);
    }
    return QnServerTransfer.getTransferPathTo(roleId).then(roleIds => {
      if(deDupEventTypes.includes(type)) {
        let existQuery;
        if(EventList.isOnlyTimeArgs(type)) { //只有时间参数时，检查事件存在不考虑parameter参数
          existQuery = {RoleId: roleIds, Type: type}
        } else {
          existQuery = {RoleId: roleIds, Type: type, Parameter:parameter}
        }
        return QnRoleEvent.exists(existQuery)
          .then(function (isExist) {
            if(!isExist) {
              return QnRoleEvent.insert(props);
            }
          })
      } else {
        return QnRoleEvent.insert(props);
      }
    });

  },

});

module.exports = QnRoleEvent;
