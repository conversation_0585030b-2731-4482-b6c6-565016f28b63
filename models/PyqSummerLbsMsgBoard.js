/**
 * Created by zhenhua on 2017/5/19.
 */

const BaseModel = require('../models/BaseModelClass');
const PyqSummerLbsMsgBoard = new BaseModel("pyq_summerlbs_msg_board");
const _ = require('lodash');

module.exports = PyqSummerLbsMsgBoard;


const BoardTypes = {
  Food: "food",
  Scene: "scene",
  Province: "province",
  Continent: "continent"
}


PyqSummerLbsMsgBoard.listBoardByName = function (name) {
  const query = PyqSummerLbsMsgBoard.scope()
    .select('ID', 'RoleId', 'Text', 'CreateTime')
    .where('Name', name)
    .orderBy('ID', 'desc')
    .limit(500);
  return PyqSummerLbsMsgBoard.executeByQuery(query);
}


PyqSummerLbsMsgBoard.hasMsgOnScenery = function (roleId, sceneId) {
  return PyqSummerLbsMsgBoard.findOne({Name: PyqSummerLbsMsgBoard.getBoardName(BoardTypes.Scene, sceneId)}, ['ID']).then(record => {
    return !_.isEmpty(record);
  })
}

PyqSummerLbsMsgBoard.getBoardName = function (boardType, boardId) {
  return `${boardType}:${boardId}`;
}

PyqSummerLbsMsgBoard.BoardTypes = BoardTypes;
