const BaseModelClass = require('./BaseModelClass')
const Messages = new BaseModelClass('md_message')

Messages.getUserMessages = function (userId, options) {
  let query = Messages.normalScope()
  .select(options.cols)
  .whereIn('TargetId', userId)
  .orderBy('ID', 'desc')
  .limit(options.limit || 10)
  if (options.sinceId) {
    query = query.where('ID', '>', options.sinceId)
  }
  if (options.maxId) {
    query = query.where('ID', '<', options.maxId)
  }
  return Messages.executeByQuery(query)
}

module.exports = Messages
