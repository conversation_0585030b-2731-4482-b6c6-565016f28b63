import BaseModelClass = require('./BaseModelClass')

interface SyncEquip {
  pos: number,
  templateid: number,
  equipname: string
  type: string
}

interface SyncEquipRecord {
  equipscore: string,
  equips: SyncEquip[]
}

interface BingQiPuEquipRecord {
  RoleId: number
  Detail: string
  EquipId: string
}

class BingQiPuEquipClass extends BaseModelClass<BingQiPuEquipRecord> {
  [x: string]: any

  constructor() {
    super('qnm_bingqipu_equip');
  }

  async getEquipDetailsHash(equipIds) {
    let rows = await this.find({ EquipId: equipIds }, { cols: ['Detail', 'EquipId'] })
    let hash = {}
    rows.forEach(row => {
      let record = JSON.parse(row.Detail) as SyncEquipRecord
      hash[row.EquipId] = record.equips[0]
    })
    return hash
  }
}

const BingQiPuEquip = new BingQiPuEquipClass()

export = BingQiPuEquip