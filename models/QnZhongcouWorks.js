/**
 * Created by <PERSON><PERSON><PERSON> on 16-9-30.
 */

var BaseModelClass = require('./BaseModelClass');
var QnRoleInfos = require('./QNRoleInfos');
var _ = require('lodash');
var logger = require('../common/logger');
var rq = require('../common/request');
const QnDataCenter = require('../models/QNDataCenter');
const Promise = require('bluebird');

var ZhongcouAPI =  "http://10.240.161.131:82";

if(process.env.NODE_ENV === "production") {
  ZhongcouAPI = "http://file.qn.163.com"
}

var QnZhongcouWorks = {
  create: function (props) {
    var now = Date.now() / 1000;
    props = _.merge({ctime:now}, props);
    return BaseModelClass.prototype.create.call(QnZhongcouWorks, props);
  },

  attend: function (roleId, subjectId, photo) {
    return Promise.all([
        QnRoleInfos.findOne({RoleId: roleId}, ['UserName', 'RoleName']),
        getRoleName(roleId)
      ]
    ).spread((r, roleName) => {
      if (r) {
        return rq.get(ZhongcouAPI + "/2016/zhongchou/api.php", {
          action: "import",
          account: r.UserName,
          roleid: roleId,
          rolename: roleName || r.RoleName,
          theme: subjectId,
          url: photo.Url,
          des: photo.Desc
        }).then(function (res) {
          return res;
        }).catch(function (err) {
          logger.error(err);
        })
      }
    })
  }
};


function getRoleName(roleId) {
  return QnDataCenter.request({
    url:`/querycharacterbasicinfo3/playerid/${roleId}`
  }).then(roleInfos => {
    const roleInfo = _.find(roleInfos, {Id: roleId});
    return _.get(roleInfo, 'Name');
  });
}

QnZhongcouWorks = _.extend(new BaseModelClass("qn_zhongcou_works"), QnZhongcouWorks);
module.exports = QnZhongcouWorks;
