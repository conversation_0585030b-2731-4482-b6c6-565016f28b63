﻿/**
 * Created by <PERSON><PERSON><PERSON> on 16-11-26.
 */

let _ = require('lodash');
let BaseModelClass = require('./BaseModelClass');
let Promise = require('bluebird');
let cacheService = require('../common/cacheService');
let { getRedis } = require('../common/redis');

const COMMENTS_OF_MOMENT_EXPIRE_TIME = 12 * 60 * 3600; // 12小时

let PyqComment = _.extend(new BaseModelClass("pyq_comment"), {
  getTopComments: function (momentId, size, opts) {
    opts = _.defaults(opts, {cols:
      ['ID', 'TargetId', 'RoleId', 'ReplyId', 'Text', 'CreateTime']
    });
    let query = PyqComment.normalScope()
      .where('TargetId', momentId)
      .orderBy('ID', 'desc')
      .limit(size);
    if(opts.cols) {
      query.select(opts.cols);
    }
    return PyqComment.executeByQuery(query);
  },

  getTopCommentsForMoments: function (momentIds, size, opts) {
    return PyqComment.promiseMap(momentIds, (momentId) => {
      return PyqComment.getTopComments(momentId, size, opts);
    }, 1).then(function (commentsList) {
      return _.flatten(commentsList);
    });
  },

  getMomentCommentsCacheKey: function (momentId) {
    return `pyq:moments:${momentId}:top_comments`
  },

  refreshMomentCommentsCache: function (momentId) {
    const  key = PyqComment.getMomentCommentsCacheKey(momentId);
    return getRedis().existsAsync(key).then(isExist => {
      if(isExist) {
        return PyqComment.getTopComments(momentId, 10).then(comments => {
          return getRedis().setAsync(key, JSON.stringify(comments), "EX", COMMENTS_OF_MOMENT_EXPIRE_TIME);
        });
      }
    });
  },

  clearMomentCommentsCache: function (momentId) {
    return getRedis().delAsync(PyqComment.getMomentCommentsCacheKey(momentId));
  },

  getTopCommentsForMomentsCached: function (momentIds, size, opts) {
    let getTopComments = cacheService.cacheInRedis(PyqComment.getTopComments, {
      expire: COMMENTS_OF_MOMENT_EXPIRE_TIME,
      context: PyqComment,
      cacheKey: PyqComment.getMomentCommentsCacheKey,
    });
    return Promise.mapSeries(momentIds, (momentId) => {
      return getTopComments(momentId, size, opts);
    }).then(function (commentsList) {
      return _.flatten(commentsList);
    });
  },

  getMomentIdByCommentId: function (commentId) {
    return PyqComment.findById(commentId, ['TargetId'])
      .then(c => {
        return _.get(c, 'TargetId');
      })
  },

  getRoleIdsAndReplyIds: function (comments) {
    let roleIds = _.map(comments, 'RoleId');
    let replyIds = _.compact(_.map(comments, 'ReplyId'));
    return {
      roleIds: roleIds,
      replyIds: replyIds
    }
  },

  getRelatedRoleIds: function (comments) {
    let roleIdsAndReplyIds = PyqComment.getRoleIdsAndReplyIds(comments);
    return _.uniq(_.concat([], roleIdsAndReplyIds.roleIds, roleIdsAndReplyIds.replyIds));
  },

  getMoreComments: function (momentId, commentId, size, opts) {
    opts = _.defaults(opts, {cols:['ID', 'TargetId', 'RoleId', 'ReplyId', 'Text', 'CreateTime']});
    let query = PyqComment.normalScope()
      .where('ID', '<', commentId)
      .where('TargetId', momentId)
      .orderBy('CreateTime', 'desc')
      .limit(size);
    if(opts.cols) {
      query.select(opts.cols);
    }
    return PyqComment.executeByQuery(query);
  }

});

module.exports = PyqComment;
