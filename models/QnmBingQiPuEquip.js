"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
const BaseModelClass = require("./BaseModelClass");
class BingQiPuEquipClass extends BaseModelClass {
    constructor() {
        super('qnm_bingqipu_equip');
    }
    getEquipDetailsHash(equipIds) {
        return __awaiter(this, void 0, void 0, function* () {
            let rows = yield this.find({ EquipId: equipIds }, { cols: ['Detail', 'EquipId'] });
            let hash = {};
            rows.forEach(row => {
                let record = JSON.parse(row.Detail);
                hash[row.EquipId] = record.equips[0];
            });
            return hash;
        });
    }
}
const BingQiPuEquip = new BingQiPuEquipClass();
module.exports = BingQiPuEquip;
//# sourceMappingURL=QnmBingQiPuEquip.js.map