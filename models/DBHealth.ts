import * as <PERSON> from '../common/db';
import * as  SqlBuilder from '../common/sqlBuilder';

interface isHealthRes {
  isHealth: boolean;
  msg?: string;
}

export async function isHealth(): Promise<isHealthRes> {
  try {
    const ret = await DB.execSql(SqlBuilder.raw('select 0').toString(), {});
    if (ret && ret[0]['0'] == 0) {
      return { isHealth: true };
    } else {
      return { isHealth: false, msg: `db check res ${JSON.stringify(ret)}` };
    }
  } catch (err) {
    return { isHealth: false, msg: err.stack };
  }
}
