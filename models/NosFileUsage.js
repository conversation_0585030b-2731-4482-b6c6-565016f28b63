var BaseModelClass = require('./BaseModelClass');

var NosFileUsage = new BaseModelClass("nos_file_usage");
var _ = require('lodash');

NosFileUsage.UseTypes = {
  Moment: "md_moment:ID",
  Avatar: "md_user:ID",
};

NosFileUsage.PrimaryKey = 'Url';

NosFileUsage.Statuses = {
  "newAdded": 0,
  "useing": 1,
  "expired": -1
};

NosFileUsage.create = function(props) {
  props.CreateTime = Date.now();
  return BaseModelClass.prototype.create.call(NosFileUsage, props);
};

NosFileUsage.findUserAvatars = function(userId) {
  return this.where({UseType: NosFileUsage.UseTypes.Avatar, TargetId: userId});
};

NosFileUsage.findUserMomentPhotos = function(userId) {
  return this.where({UseType: NosFileUsage.UseTypes.Moment, TargetId: userId});
};


NosFileUsage.getUserMomentPhotoCount = function(userId) {
  return this.count({TargetId: userId, UseType: NosFileUsage.UseTypes.Moment});
};

NosFileUsage.getUserAvatarPhotoCount = function(userId) {
  return this.count({TargetId: userId, UseType: NosFileUsage.UseTypes.Avatar});
};

NosFileUsage.createAvatarRecord = function(userId, url) {
  return this.create({
    TargetId: userId,
    Url: url,
    UseType: NosFileUsage.UseTypes.Avatar
  });
};

module.exports = NosFileUsage;
