import BaseModelClass = require('./BaseModelClass')
import * as mysql from 'mysql'
import * as config from '../common/config'
import * as _ from 'lodash'
import { equipRankCfg } from '../pyq-server/common/config'
import { getLogger } from '../common/logger2'
const dbLogger = getLogger("db");

interface DbClient {
  execute: (sql: string, dbNode: string) => Promise<any>
}


function getDataTableName() {
  return equipRankCfg.tableName
}


let QnmEquipRank: any = new BaseModelClass(getDataTableName())

if (config.BingQiPuDbConfig) {
  const connection = mysql.createPool(config.BingQiPuDbConfig)
  const dbClient: DbClient = {
    execute: function (sql, dbNode) {
      if (config.testCfg.db_debug) {
        dbLogger.info({ sql }, "ExecuteSql");
      }

      return new Promise((resolve, reject) => {
        connection.query(sql, function (err, results, fields) {
          if (err) {
            if(err) {
              dbLogger.error({ err: err, sql: sql }, "ExecuteSqlError");
            }
            reject(err)
          } else {
            resolve(results)
          }
        })
      })
    }
  }
  QnmEquipRank = new BaseModelClass(getDataTableName(), null, { db: dbClient })
}

export default QnmEquipRank