const BaseModelClass = require('./BaseModelClass')
const PyqGmProfile = new BaseModelClass('pyq_gm_profile')
const _ = require('lodash')

PyqGmProfile.getProfile = function getProfile (gmId) {
  const cols = ['GmId', 'Avatar', 'Name', 'WeChatId', 'PhoneNumber', 'JobDesc', 'PublicComments', 'OnlineTime', 'Signature']
  return this.findOne({GmId: gmId}, cols).then(record => {
    if (record && record.PublicComments) {
      record.PublicComments = _.split(record.PublicComments, '\n')
    }
    return record
  })
}

module.exports = PyqGmProfile
