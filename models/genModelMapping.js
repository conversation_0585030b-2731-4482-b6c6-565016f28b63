const fs = require("fs");
const files = fs.readdirSync(__dirname);
const _ = require("lodash");

let map = _.reduce(
  files,
  (hash, fileName) => {
    if (fileName.endsWith("js")) {
      const module = require("./" + fileName);
      if (module.tableName) {
        hash[module.tableName] = fileName
      }
    }
    return hash;
  },
  {}
);

fs.writeFileSync("meta.json", JSON.stringify(map, null, 2))
