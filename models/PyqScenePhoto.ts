import BaseModelClass = require('./BaseModelClass')
import { TABLE_NAMES, Statues } from '../common/constants';

export const MAX_SIZE_FOR_PLAYER = 30 // 每个人最多存30个

export interface PyqScenePhotoRecord {
    ID: number
    RoleId: number
    Index: number
    Url: string
    CreateTime: number
    Status: number
}

class PyqScenePhotoClass extends BaseModelClass<PyqScenePhotoRecord> {
    constructor() {
        super(TABLE_NAMES.PyqScenePhoto)
    }

    async findByRoleIdAndIndex(roleId: number, index: number, cols: (keyof PyqScenePhotoRecord)[]) {
        let record = await this.findOne({ RoleId: roleId, Index: index, Status: Statues.Normal }, cols)
        return record
    }

    async add(roleId: number, index: number, url: string): Promise<number> {
        let record = await this.findByRoleIdAndIndex(roleId, index, ['ID'])
        if (record && record.ID) {
            await this.updateByCondition({ ID: record.ID }, { CreateTime: Date.now(), Url: url })
            return record.ID
        } else {
            let ret = await this.insert({ RoleId: roleId, Index: index, Url: url, CreateTime: Date.now(), Status: Statues.Normal })
            return ret.insertId
        }
    }

    async listAllByRoleId(roleId: number, cols: (keyof PyqScenePhotoRecord)[]) {
        let records = await this.find({ RoleId: roleId, Status: Statues.Normal }, { limit: MAX_SIZE_FOR_PLAYER, cols: cols })
        return records
    }
}

export let PyqScenePhoto = new PyqScenePhotoClass()