"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityTopicModel = exports.ActivityTopic = exports.TopicStatus = void 0;
const BaseModelClass = require("./BaseModelClass");
const constants_1 = require("../common/constants");
var TopicStatus;
(function (TopicStatus) {
    TopicStatus[TopicStatus["DOWN"] = -1] = "DOWN";
    TopicStatus[TopicStatus["TOP"] = 1] = "TOP";
    TopicStatus[TopicStatus["Normal"] = 0] = "Normal";
})(TopicStatus = exports.TopicStatus || (exports.TopicStatus = {}));
class ActivityTopicClass extends BaseModelClass {
    constructor() {
        super(constants_1.TABLE_NAMES.ActivityTopic);
    }
    getTopics(size, cols) {
        return __awaiter(this, void 0, void 0, function* () {
            let query = this.scope()
                .select(cols)
                .whereNot("Status", TopicStatus.DOWN)
                .orderBy("TopTime", "desc")
                .orderBy("ID", "desc")
                .limit(size);
            let rows = yield this.executeByQuery(query);
            return rows;
        });
    }
    getNameById(topicId) {
        return __awaiter(this, void 0, void 0, function* () {
            let record = yield this.findOne({ ID: topicId }, ["Name"]);
            if (record) {
                return record.Name;
            }
            else {
                return null;
            }
        });
    }
    getIdByName(name) {
        return __awaiter(this, void 0, void 0, function* () {
            let record = yield this.findOne({ Name: name }, ["ID"]);
            if (record) {
                return record.ID;
            }
            else {
                return 0;
            }
        });
    }
    topTopic(topicId, topTime) {
        return __awaiter(this, void 0, void 0, function* () {
            let ret = yield this.updateByCondition({ ID: topicId }, { TopTime: topTime, Status: TopicStatus.TOP });
            return ret;
        });
    }
}
exports.ActivityTopic = new ActivityTopicClass();
exports.ActivityTopicModel = exports.ActivityTopic;
//# sourceMappingURL=PyqActivityTopic.js.map