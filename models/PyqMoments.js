/**
 * Created by zhen<PERSON> on 16-9-29.
 */

const config = require("../common/config");

const BaseModelClass = require("./BaseModelClass");
const _ = require("lodash");
const QnmRoleAble = require("./mixins/QnmRoleAble");
const ImagesAuditAble = require("./mixins/ImagesAuditable");
const Promise = require("bluebird");
const util = require("../common/util");
const { PYQ_BASIC_MOMENT_COLS } = require("../common/constants");
const { MomentHotStateService } = require("../service/momentHotStateService");


const Events = {
  LIKE_MOMENT: "like_moment",
  Cancel_LIKE_MOMENT: "cancel_like_moment",
};

const WebLinkValidHost = [
  "https://qnm.netease.com",
  "https://qnm.16163.com",
  "https://www.16163.com/zt/qnm",
  "https://cc.163.com/v/qnm/v/qnyh",
  "https://qnm-web.163163.com",
  "https://qnm.163.com",
  "https://test.nie.163.com/",
];

const BASIC_MOMENT_COLS = PYQ_BASIC_MOMENT_COLS

var PyqMoments = {
  WebLinkValidHost: WebLinkValidHost,
  Events: Events,

  BASIC_MOMENT_COLS: BASIC_MOMENT_COLS,

  create: function (props) {
    const now = Date.now();
    props = _.merge({ CreateTime: now }, props);
    return BaseModelClass.prototype.create.call(PyqMoments, props);
  },

  getHotMomentsInAllServers: function (limit) {
    return this.executeByQuery(this.getHotQuery(limit));
  },

  getHotQuery: function (limit) {
    return this.normalScope()
      .select("ID", "RoleId", "Text", "ImgList", "VideoList", "CreateTime", "Hot")
      .orderBy("Hot", "desc")
      .orderBy("ID", "desc")
      .limit(limit);
  },

  getHotMomentsByServer: function (serverId, limit) {
    const self = this;
    const query = self.getHotQuery(limit);
    return self.queryByServerId(query, serverId).then(function (result) {
      return self.executeByQuery(result.query);
    });
  },

  updateHotStateWhenDeleteComments: function (id, commentIds) {
    const PyqComments = require("./PyqComment");
    let momentRoleId;
    let hotState;
    //@ts-ignore
    return PyqMoments.findById(id, ["RoleId", "HotState"])
      .then(function (moment) {
        momentRoleId = moment.RoleId;
        hotState = JSON.parse(moment.HotState || "{}");
        hotState = _.defaults(hotState, { comment: 0, reply: 0 });
      })
      .then(function () {
        const query = PyqComments.normalScope().select("RoleId").where("TargetId", id).whereIn("ID", commentIds);
        return PyqComments.executeByQuery(query);
      })
      .then(function (comments) {
        const deleteReplyCount = _.filter(comments, (c) => c.RoleId == momentRoleId).length;
        const deleteCommentCount = comments.length - deleteReplyCount;

        hotState.comment = Math.max(0, hotState.comment - deleteCommentCount);
        hotState.reply = Math.max(0, hotState.reply - deleteReplyCount);

        const hotStateStr = JSON.stringify(hotState);
        //@ts-ignore
        return PyqMoments.updateById(id, { HotState: hotStateStr });
      });
  },

  getCommentsCountFromHotStateStr: function (hotStateStr) {
    if (_.isEmpty(hotStateStr)) {
      return 0;
    } else {
      const hotState = JSON.parse(hotStateStr);
      const commentNum = hotState.comment || 0;
      const replyNum = hotState.reply || 0;
      return commentNum + replyNum;
    }
  },

  formatHotState: function (hotStateStr) {
    const initHotState = { comment: 0, reply: 0, like: 0 };
    try {
      return _.defaults(JSON.parse(hotStateStr), initHotState);
    } catch (err) {
      return initHotState;
    }
  },

  formatVideoList: function (videoListStr) {
    return _.map(util.csvStrToIntArray(videoListStr), (url) => {
      return { url: url, snapshot: url };
    });
  },

  /**
   * 上全服热门的心情ids
   * @param {Array} ids
   * @return {Promise.<TResult>|*}
   */
  upHotMoments: function (ids, hotLvel) {
    return Promise.mapSeries(ids, (id) => {
      return PyqMoments.updateHotStateTop(id, hotLvel || 1);
    }).then(() => {
      const HotMomentCache = require("../service/qnm/pyq/HotMomentsCache");
      return HotMomentCache.refreshMoments(new Date(), "all", null);
    });
  },

  updateHotStateTop: function (id, topValue) {
    const JsonEditor = require("../common/jsonEditor");
    return PyqMoments.findByIdForce(id, ["HotState"])
      .then((m) => {
        return JsonEditor.fromJsonStr(m.HotState).edit("top", topValue);
      })
      .then((editor) => {
        const hotStateStr = editor.toStr();
        const state = editor.toObj();
        const hot = PyqMoments.getHotFromState(state);
        return PyqMoments.updateByCondition({ ID: id }, { HotState: hotStateStr, Hot: hot });
      });
  },

  /**
   * 下全服热门的心情ids
   * @param {Array} ids
   * @return {Promise.<TResult>|*}
   */
  downHotMoments: function (ids) {
    return Promise.mapSeries(ids, (id) => {
      return PyqMoments.updateHotStateTop(id, -1);
    }).then(() => {
      const HotMomentCache = require("../service/qnm/pyq/HotMomentsCache");
      return HotMomentCache.refreshMoments(new Date(), "all");
    });
  },

  getHotFromState: function (state) {
    return MomentHotStateService.calHotFromState(state);
  },

  genLinkButton: function (url, name) {
    const hostIndex = _.findIndex(WebLinkValidHost, (host) => url.startsWith(host));
    if (hostIndex !== -1) {
      name = name || url;
      const urlSuffix = url.substr(WebLinkValidHost[hostIndex].length).replace("#", "##");
      return `<link button=${name},WebLinkClick,${urlSuffix},${hostIndex}>`;
    } else {
      throw new Error("不是合法的域名");
    }
  },

  richTextToHtml: function (str) {
    return require("./PyqMomentTextParser").parseToHtml(str);
  },

  getInitialImgAudit: function (imgList) {
    const Constants = require("../common/data").Constants;
    const PhotoAuditWhiteList = require("../pyq-server/data/photoAuditWhiteList");
    const skipAudit = config.testCfg.skip_audit;
    return _.join(
      _.map(imgList, (url) => {
        if (skipAudit) {
          return Constants.STATUS_AUDIT_PASS;
        }
        if (PhotoAuditWhiteList.isInWhiteList(url)) {
          return Constants.STATUS_AUDIT_PASS;
        } else {
          return Constants.STATUS_AUDIT_INIT;
        }
      }),
      ","
    );
  },
};

PyqMoments = _.extend(new BaseModelClass("pyq_moment"), ImagesAuditAble, QnmRoleAble, PyqMoments);
module.exports = PyqMoments;
