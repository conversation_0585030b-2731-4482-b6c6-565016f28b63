/**
 * Created by <PERSON><PERSON><PERSON> on 16-9-18.
 */

var BaseModelClass = require('./BaseModelClass');
var PhotoAlbum = require('./PhotoAlbums');
var _ = require('lodash');

//主题更新到2017-11
var subjectIdToRecord = {
  1: {name:"胭脂露·家具"},
  2: {name:"胭脂露·家具"},
  3: {name:"明月清辉·时装"},
  4: {name:"别良人·坐骑"},
  5: {name:"和春住·家具"},
  6: {name:"水云烟·时装"},
  7: {name:"斑竹枝·坐骑"},
  8: {name:"半盏红·家具"},
  9: {name:"火炎焱·时装"},
  10: {name:"彼岸灵槎·坐骑"},
  11: {name:"金桂香·家具"},
  12: {name:"秋中星夜·时装"},
  13: {name:"初雪·坐骑"},
};

var PhotoAlbumSubject = _.extend({
  PrimaryKey: 'Id',

  getUserSubjectAlbumId: function (userId, subjectId) {
    var self = this;
    return self.findOne({UserId: userId, SubjectId: subjectId})
      .then(function (albumSubject) {
        if(_.isEmpty(albumSubject)) {
          var subject = subjectIdToRecord[subjectId];
          if(subject) {
            return PhotoAlbum.create({UserId: userId, Name: subject.name})
              .then(function (photoAlbum) {
                return PhotoAlbumSubject.create({
                  UserId: userId,
                  SubjectId: subjectId,
                  PhotoAlbumID: photoAlbum.ID
                }).then(function () {
                  return photoAlbum.ID;
                })
              })
          } else {
            return Promise.reject({errorType: "InvalidSubjectId", msg:"非法的主题Id", validSubjectIds: PhotoAlbumSubject.SUBJECT_IDS});
          }
        } else {
          return albumSubject.PhotoAlbumId;
        }
      });
  },

  SUBJECT_IDS: Object.keys(subjectIdToRecord).map(function (id) {
    return parseInt(id, 10);
  })
}, new BaseModelClass("md_photo_album_subject"));

module.exports = PhotoAlbumSubject;
