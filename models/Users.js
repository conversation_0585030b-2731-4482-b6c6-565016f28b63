﻿var BaseModelClass = require('./BaseModelClass');
var Users = new BaseModelClass("md_user");
var _ = require('lodash');

const VISITOR_KEEP_MAX_SIZE = 16

module.exports = Users;

Users.DEFAULT_AVATAR = "https://hi.res.netease.com/m/gw/20160607200900/img/default_7c2232e.jpg";

Users.AvaAuthStatuses = {
  AUDITING: 0,
  PASSED: 1,
  REJECT: -1
};

Users.Statuses = {
  NORMAL: 0,
  BANNED: -1
};

/* 用户类型 */
Users.UserType = {
    ACG: 1 // 二次元
};

Users.findById = function(id, cols) {
  return BaseModelClass.prototype.findById.call(Users, id, cols)
    .then(function(user) {
      if(user && user.Status === Users.Statuses.BANNED) {
        _.assign(user, {NickName:"", UserName:"", Province: "", City: "", Signature: "", Avatar: null});
        return user;
      } else {
        return user;
      }
    });
};

/**
 * visit 标记vistorId的用户访问userId的主页
 *
 * @param {String} visitorId
 * @param {String} userId
 * @returns {Void}
 */
Users.visit = function(visitorId, userId) {
  visitorId = parseInt(visitorId);
  userId = parseInt(userId);
  if(visitorId === userId) {
    return Promise.resolve({msg: "User visit self!"});
  } else {
    return Users.getRecentVisitorIds(userId).then(function(visitorIds) {
      if(_.first(visitorIds) === visitorId) {
        return;
      } else {
        var otherVistorIds  = _.without(visitorIds, visitorId);
        var newVisitorIds = _.concat(visitorId, otherVistorIds);
        return Users.updateVistorIds(userId, newVisitorIds);
      }
    });
  }
};

Users.updateVistorIds = function (id, visitorIds) {
  visitorIds = _.take(visitorIds, VISITOR_KEEP_MAX_SIZE)
  var visitListCsv = _.join(visitorIds, ',');
  return Users.updateById(id, { VisitList: visitListCsv });
};

Users.getRecentVisitorIds = function(userId) {
  return Users.findById(userId).then(Users.getRecentVisitorIdsByUser);
};

Users.getRecentVisitorIdsByUser = function(user, size) {
  return _.chain(_.split(user.VisitList, ','))
    .compact()
    .uniq()
    .map(function(intStr) {
      return parseInt(intStr, 10);
    })
    .slice(0, size)
    .value();
};

Users.fillDefaultAvatarForUser = function(user) {
  if(!user.Avatar) {
    user.Avatar = Users.DEFAULT_AVATAR;
  }
  return user;
};

Users.fillDefaultAvatarForUsers = function(users) {
  return _.map(users, Users.fillDefaultAvatarForUser);
};

Users.getRecentVisitors = function(userId, size) {
  return Users.findById(userId)
    .then(Users.getRecentVisitorsByUser, size)
    .then(Users.normalizeRecords);
};

Users.getRecentVisitorsByUser = function(user, size) {
  return Promise.resolve(Users.getRecentVisitorIdsByUser(user, size))
    .then(function(userIds) {
      return Users.findByIds(userIds);
    })
    .then(Users.Serializers.normalUsers);
};

Users.getUser = function(userId, selectProps) {
  return Users.findById(userId, selectProps).then(Users.Serializers.normalUsers);
};

Users.getUsersByIds = function(userIds, selectProps) {
  return Users.findByIds(userIds, selectProps).then(Users.normalizeRecords);
};

Users.isAvatarAuditPassed = function(user) {
  return user.AvaAuthStatus === Users.AvaAuthStatuses.PASSED;
};

Users.isAvatarAuditReject = function(user) {
  return user.AvaAuthStatus === Users.AvaAuthStatuses.REJECT;
};

Users.isAvatarAuditing = function(user) {
  return user.AvaAuthStatus === Users.AvaAuthStatuses.AUDITING;
};

Users.normalizeRecords = function(users) {
  return _.map(users, function(user) {
    if(Users.isAvatarAuditReject(user)) {
      user.Avatar = null;
    }
    return user;
  });
};

Users.Serializers = {};

Users.Serializers.normalUsers = function(users) {
  var pickAttr = function(user) {
    return _.pick(user, ['ID', 'Avatar', 'Province', 'City', 'NickName', 'AvaAuthStatus', 'Gender']);
  };
  if(_.isArray(users)) {
    return _.map(users, pickAttr);
  } else {
    return pickAttr(users);
  }
};

Users.Serializers.default = Users.Serializers.normalUsers;

Users.findUserNickNameLike = function(nickName, paganation) {
  var query = Users.scope().where('NickName', 'like', `%${Users.escapeLikeStr(nickName)}%`).orderBy('ID');
  return Users.queryWithPagination(query, paganation)
    .then(Users.normalizeRecords)
    .then(Users.Serializers.normalUsers);
};

Users.getUsersWithRoleInfos = function(users, roleInfos, bindInfos) {
  var bindInfosGroupByUserId = _.groupBy(bindInfos, 'UserId');
  var roleInfosGroupByRoleId = _.groupBy(roleInfos, 'RoleId');
  return _.map(users, function(user) {
    var bindrole = _.first(bindInfosGroupByUserId[user.ID]);
    var roleInfo = _.first(roleInfosGroupByRoleId[bindrole.RoleId]);
    user.RoleInfo = roleInfo;
    return user;
  });
};

Users.getBasicUserInfo = function(userId) {
  return this.findById(userId).then(Users.Serializers.default);
};

Users.setUserAvatar = function(user, curUserId) {
  require('../common/data').md.setAvatarView(user, curUserId);
  if(_.isEmpty(user.Avatar) && !Users.isAvatarAuditReject(user)) {
    user.Avatar = "https://hi.res.netease.com/pc/fab/20160803171644/img/avatarDefault_e466644.jpg";
  }
  return user;
};

Users.setUsersAvatar = function(users, curUserId) {
  return _.map(users, function(user) {
    // var isViewBySelf = (user.ID == curUserId);
    return Users.setUserAvatar(user, curUserId);
  });
};

Users.isBannedUser = function (userId) {
  if(userId) {
    return this.exists({ID: userId, Status: Users.Statuses.BANNED})
  } else {
    return Promise.resolve(false);
  }
};

Users.checkUserValid = function (userId) {
  return this.checkRecordById(userId, "该用户不存在");
};

/**
 * 设置用户类型
 * @param {Int} userId 用户ID
 * @param {Int} userType 用户类型
 */
Users.setUserType = function(userId, userType) {
    let condition = {
        'ID': userId
    };
    let props = {
      'UserType': userType
    };
    return this.updateByCondition(condition, props);
};