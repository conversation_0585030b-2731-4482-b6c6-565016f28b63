"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityTopicMoment = void 0;
const BaseModelClass = require("./BaseModelClass");
const constants_1 = require("../common/constants");
class ActivityTopicMomentClass extends BaseModelClass {
    constructor() {
        super(constants_1.TABLE_NAMES.ActivityTopicMoment);
    }
    addRecord(roleId, topicId, momentId, imgCount) {
        return __awaiter(this, void 0, void 0, function* () {
            let record = {
                TopicId: topicId,
                RoleId: roleId,
                ImgCount: imgCount,
                MomentId: momentId,
                TopTime: 0,
                Status: 0
            };
            let ret = yield this.insert(record);
            return ret;
        });
    }
}
exports.ActivityTopicMoment = new ActivityTopicMomentClass();
//# sourceMappingURL=PyqActivityTopicMoment.js.map