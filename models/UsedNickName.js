/**
 * Created by <PERSON><PERSON><PERSON> on 16-11-10.
 */


var _ = require('lodash');
var BaseModelClass = require('./BaseModelClass');


var UsedNickName = _.extend(new BaseModelClass("md_nickname_histories"), {
  getLastUpdateTime: function (userId) {
    var query = UsedNickName.scope()
      .where('UserId', userId)
      .orderBy('UpdateTime', 'desc')
      .limit(1);
    return UsedNickName.executeByQuery(query).then(function (r) {
      if(r && r[0]) {
        return r[0].UpdateTime;
      } else {
        return null;
      }
    });
  },

  canRename: function (userId) {
    const oneMonth = 30 * 24 * 2600 * 1000;
    return UsedNickName.getLastUpdateTime(userId).then(function (t) {
      if(t) {
        return Date.now() - t >= oneMonth;
      } else {
        return true;
      }
    })
  },

  addRenameRecord: function (userId, oldName, newName) {
    return UsedNickName.create({
      UserId: userId,
      OldNickName: oldName,
      NewNickName: newName,
      UpdateTime: Date.now()
    })
  }
});

module.exports = UsedNickName;
