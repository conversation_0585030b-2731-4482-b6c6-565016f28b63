/**
 * Created by <PERSON><PERSON><PERSON> on 2017/4/13.
 */

import { BussError, errorCodes } from "../pyq-server/errorCodes";
import { clazzLogger } from "../pyq-server/logger";
import { MomentRecord, RoleMinorModel } from "../pyq-server/models";
import { handlePlayerAttendMomentLottery } from "../pyq-server/services/momentLotteryAttendService";
import { AttendMomentLotteryAction } from "../pyq-server/types/momentLotteryType";
import { BASIC_MOMENT_COLS, MomentModel2 } from "./momentModel";
import * as InformModel from "./PyqInform";
import BaseModelClass = require("./BaseModelClass");
import * as _ from "lodash";
import { csvStrToIntArray } from "../common/util";
import momentTagCfgModel from "../pyq-server/models/momentTagCfgModel";
import { MomentTagModel } from "../pyq-server/models/momentTagModel";
import { getServerIdFromRoleId } from "../common/util2";
const logger = clazzLogger("models.momentForward");

type ShowMomentForward = Pick<MomentRecord, "ID" | "Text" | "RoleId">;

interface ForwardMomentResult {
  momentId: number;
  forwardId: number;
  originId: number;
}

interface MomentForwardRecord {
  ID: number;
  RoleId: number;
  MomentId: number;
  ForwardId: number;
  OriginId: number;
}

class MomentForwardModelClass extends BaseModelClass<MomentForwardRecord> {
  constructor(tableName: string) {
    super(tableName);
  }

  /**
   * 创建转发动态并写入数据库
   * @param roleId 角色ID
   * @param text 动态文本
   * @param originTags 原始标签
   * @param forwardMomentId 被转发的动态ID
   * @param originMomentId 原始动态ID
   * @param originRoleId 原始动态作者ID
   * @returns 创建的动态ID
   */
  private async createForwardMoment(
    roleId: number,
    text: string,
    originTags: number[],
    forwardMomentId: number,
    originMomentId: number,
    originRoleId: number
  ): Promise<number> {
    // 1. 过滤抽奖标签
    const forwardTags = momentTagCfgModel.filterLotteryTagIds(originTags);

    // 2. 创建动态基本信息
    let insertProps: Partial<MomentRecord> = {
      RoleId: roleId,
      Text: text,
      CreateTime: Date.now(),
    }

    if (forwardTags.length > 0) {
      const forwardTagsStr = forwardTags.join(",");
      insertProps.TagIds = forwardTagsStr;
    }

    // 3. 插入动态记录
    const newMomentId = await MomentModel2.insert(insertProps);

    // 4. 并行处理转发记录和通知
    await Promise.all([
      this.insert({
        RoleId: roleId,
        MomentId: newMomentId,
        ForwardId: forwardMomentId,
        OriginId: originMomentId,
      }),
      this.addInform(roleId, { RoleId: originRoleId }, newMomentId),
    ]);

    // 5. 插入标签
    if (forwardTags.length > 0) {
      const serverId = getServerIdFromRoleId(roleId);
      await MomentTagModel.add({ ID: newMomentId, RoleId: roleId }, serverId, forwardTags);
    }

    // 6. 处理抽奖参与逻辑
    await handlePlayerAttendMomentLottery(
      roleId,
      originMomentId,
      originRoleId,
      AttendMomentLotteryAction.Forward,
      JSON.stringify({ forwardId: newMomentId }),
      new Date()
    );

    return newMomentId;
  }

  async addInform(roleId: number, forwardMoment: Pick<MomentRecord, "RoleId">, newMomentId: number) {
    const res = await InformModel.add({
      Type: InformModel.InformTypes.FORWARD_MOMENT,
      RoleId: roleId,
      TargetId: forwardMoment.RoleId,
      ObjectId: newMomentId,
    });
    logger.info({ roleId, forwardMoment, newMomentId, res }, "AddForwardMomentInformOk");
    return res;
  }

  /**
   * 检查角色是否处于保护模式
   * @param roleIds 需要检查的角色ID或ID数组
   * @param context 日志上下文信息
   * @throws 如果任一角色处于保护模式，抛出错误
   */
  private async checkRoleProtectMode(roleIds: number | number[], context: Record<string, any>): Promise<void> {
    const roleIdArray = Array.isArray(roleIds) ? roleIds : [roleIds];
    const roleMinors = await RoleMinorModel.find({ roleId: roleIdArray });

    if (roleMinors?.some((minor) => minor.protectMode === 1)) {
      logger.warn({ ...context, roleMinors }, "RoleMinorProtectMode");
      await BussError(errorCodes.MomentPlayerInProtectMode);
    }
  }

  async reForwardMoment(
    roleId: number,
    text: string,
    forwardMomentId: number,
    forwardOriginId: number
  ): Promise<ForwardMomentResult> {
    // 1. 查询原始动态和转发动态
    const rows = await MomentModel2.find(
      { ID: [forwardMomentId, forwardOriginId], Status: 0 },
      { cols: ["ID", "RoleId", "TagIds"] }
    );

    // 2. 验证必须存在两个有效动态
    if (rows.length !== 2) {
      logger.info({ forwardMomentId, forwardOriginId, rows, roleId, text }, "ReForwardMomentEmpty");
      await BussError(errorCodes.MomentNotFound);
    }

    // 3. 检查角色保护模式
    const roleIds = _.map(rows, "RoleId");
    await this.checkRoleProtectMode(roleIds, { forwardMomentId, forwardOriginId, roleId, text });

    // 4. 创建新动态
    const originMoment = _.find(rows, { ID: forwardOriginId });
    const originTags = csvStrToIntArray(originMoment.TagIds);

    const newMomentId = await this.createForwardMoment(
      roleId,
      text,
      originTags,
      forwardMomentId,
      forwardOriginId,
      originMoment.RoleId
    );

    // 5. 返回结果
    const resp: ForwardMomentResult = {
      momentId: newMomentId,
      forwardId: forwardMomentId,
      originId: forwardOriginId,
    };
    return resp;
  }

  async forwardOriginMoment(roleId: number, text: string, forwardMomentId: number): Promise<ForwardMomentResult> {
    try {
      // 1. 查询原始动态
      const momentRecord = await MomentModel2.findOne({ ID: forwardMomentId, Status: 0 }, ["ID", "RoleId", "TagIds"]);

      if (!momentRecord) {
        logger.warn({ forwardMomentId, roleId, text }, "ForwardOriginMomentNotFound");
        await BussError(errorCodes.MomentNotFound);
      }

      // 2. 检查保护模式
      await this.checkRoleProtectMode(momentRecord.RoleId, { forwardMomentId, roleId, text });

      // 3. 创建新动态
      const originTags = csvStrToIntArray(momentRecord.TagIds);
      const newMomentId = await this.createForwardMoment(
        roleId,
        text,
        originTags,
        forwardMomentId,
        forwardMomentId,
        momentRecord.RoleId
      );

      // 4. 返回结果
      const resp: ForwardMomentResult = {
        momentId: newMomentId,
        forwardId: forwardMomentId,
        originId: forwardMomentId,
      };
      return resp;
    } catch (err) {
      if (err instanceof Error) {
        logger.error({ err, roleId, forwardMomentId, text }, "ForwardOriginMomentError");
      } else {
        logger.warn({ err, roleId, forwardMomentId, text }, "ForwardOriginMomentBizError");
      }
      throw err;
    }
  }

  async forwardMoment(roleId: number, forwardMomentId: number, text: string) {
    const forwardOriginId = await this.findOriginId(forwardMomentId);

    // 首次转发的情况
    if (!forwardOriginId) {
      return this.forwardOriginMoment(roleId, text, forwardMomentId);
    }

    // 再次转发的情况
    return this.reForwardMoment(roleId, text, forwardMomentId, forwardOriginId);
  }

  async findOriginId(momentId: number): Promise<number> {
    const r = await this.findOne({ MomentId: momentId }, ["OriginId"]);
    if (!r) {
      return 0;
    } else {
      return r.OriginId;
    }
  }

  async getForwardInfo(momentId: number): Promise<MomentRecord> {
    const originMomentId = await this.findOriginId(momentId);
    if (originMomentId > 0) {
      const originMoment = await MomentModel2.findNormalById(originMomentId);
      return originMoment;
    }
    return null;
  }

  /**
   * 获取转发动态的原始动态信息
   * @param momentIds 动态ID数组
   * @returns 返回一个对象，键为动态ID，值为对应的原始动态信息
   */
  async getForwardOriginMomentsHash(momentIds: number[]): Promise<{ [key: number]: MomentRecord }> {
    // 如果传入的动态ID数组为空，直接返回空对象
    if (momentIds.length === 0) {
      return {};
    }

    // 查询转发关系，获取每个动态ID对应的原始动态ID
    const forwardRelations = await this.find({ MomentId: momentIds }, { cols: ["MomentId", "OriginId"] });

    // 将转发关系转换为动态ID到原始动态ID的映射
    const momentIdToOriginId = forwardRelations.reduce((acc, relation) => {
      acc[relation.MomentId] = relation.OriginId;
      return acc;
    }, {} as { [key: number]: number });

    // 提取所有原始动态ID
    const originMomentIds = forwardRelations.map((relation) => relation.OriginId);

    // 查询原始动态信息
    const originMoments = await MomentModel2.find({ ID: originMomentIds }, { cols: BASIC_MOMENT_COLS });

    // 将原始动态信息转换为ID到动态记录的映射
    const idToMoments = originMoments.reduce((acc, moment) => {
      acc[moment.ID] = moment;
      return acc;
    }, {} as { [key: number]: MomentRecord });

    // 遍历传入的动态ID数组，填充结果对象
    return momentIds.reduce((acc, momentId) => {
      const originMomentId = momentIdToOriginId[momentId];
      if (originMomentId && idToMoments[originMomentId]) {
        acc[momentId] = { ...idToMoments[originMomentId] };
      }
      return acc;
    }, {} as { [key: number]: MomentRecord });
  }

  async getForwardCount(momentIds: number[]): Promise<{ [key: number]: number }> {
    const resp: { [key: number]: number } = {};

    if (_.isEmpty(momentIds)) {
      return resp;
    }

    for (const mId of momentIds) {
      const count = await this.getMomentForwardCount(mId);
      resp[mId] = count;
    }

    return resp;
  }

  async getMomentForwardCount(momentId: number): Promise<number> {
    const query = this.scope()
      .count("MomentId as ForwardCount")
      .from(this.tableName + " as f")
      .innerJoin(MomentModel2.tableName + " as m", "f.MomentId", "m.ID")
      .where(function () {
        this.orWhere("f.ForwardId", momentId).orWhere("f.OriginId", momentId);
      })
      .where("m.Status", 0);

    const rows = (await this.executeByQuery(query)) as { ForwardCount: number }[];
    if (rows && rows.length > 0) {
      return rows[0].ForwardCount;
    }
    return 0;
  }

  async getPreviousForwards(momentId: number, queryLimit?: number, textLimit?: number): Promise<ShowMomentForward[]> {
    textLimit = textLimit || 200;
    queryLimit = queryLimit || 50;
    let queryCount = 0;
    const result: ShowMomentForward[] = [];
    let totalText = "";
    let curMomentId = momentId;
    while (curMomentId && queryCount < queryLimit) {
      const forward = await this.findOne({ MomentId: curMomentId }, ["ID", "ForwardId", "OriginId"]);
      if (!forward) {
        break;
      }
      //查询到了根节点
      if (forward.ForwardId === forward.OriginId) {
        break;
      }
      queryCount += 1;
      curMomentId = forward.ForwardId;
      const parentMoment = await MomentModel2.findNormalById(curMomentId, ["ID", "Text", "RoleId"]);
      if (parentMoment && parentMoment.Text) {
        if (totalText.length + parentMoment.Text.length > textLimit) {
          break;
        } else {
          totalText += parentMoment.Text;
        }
      }
      if (parentMoment) {
        result.push(parentMoment);
      }
    }
    return result;
  }
}

const MomentForwardModel = new MomentForwardModelClass("pyq_moment_forward");

export = MomentForwardModel;