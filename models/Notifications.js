var BaseModelClass = require('./BaseModelClass');
var Notifications = module.exports = new BaseModelClass("md_inform");
var Users = require('./Users');
var Photos = require('./Photos');
var _ = require('lodash');
var Promise = require('bluebird');

Notifications.Statuses = {
  DELETED: -1,
  UNREAD: 0,
  READ: 1
};

const EVENT_TYPES_TABLE = [
  { ID: 0,  Action: "FOLLOW",               RelatedTable: "md_contacts",      Group: "FRI"    },

  { ID: 5,  Action: "LEAVE_MESSAGE",        RelatedTable: "md_message",       Group: "MSG"    },
  { ID: 3,  Action: "REPLY_LEAVE_MESSAGE",  RelatedTable: "md_answer",        Group: "MSG"    },

  { ID: 1,  Action: "COMMENT_MOMENT",       RelatedTable: "md_comment",       Group: "INFORM" },
  { ID: 2,  Action: "REPLY_MOMENT",         RelatedTable: "md_comment",       Group: "INFORM" },
  { ID: 4,  Action: "LIKE_MOMENT",          RelatedTable: "md_moment",        Group: "INFORM" },
  { ID: 6,  Action: "LIKE_PHOTO",           RelatedTable: "md_photo",         Group: "INFORM" },
  { ID: 7,  Action: "SYSTEM_MESSAGE",       RelatedTable: null,               Group: "INFORM" },
  { ID: 8,  Action: "LIKE_SHOW_TOPIC",      RelatedTable: "md_show_topic",    Group: "INFORM" },
  { ID: 9,  Action: "LIKE_REALITY_SHOW",    RelatedTable: "md_reality_show",  Group: "INFORM" },
  { ID: 10, Action: "COMMENT_SHOW_TOPIC",   RelatedTable: "md_new_comment",   Group: "INFORM" },
  { ID: 11, Action: "REPLY_SHOW_TOPIC",     RelatedTable: "md_new_comment",   Group: "INFORM" },
  { ID: 12, Action: "AUDIT_TONGREN_WORK",   RelatedTable: "md_tongren_works", Group: "INFORM" },
  { ID: 13, Action: "COMMENT_TONGREN_WORK", RelatedTable: "md_tongren_works", Group: "INFORM" },
  { ID: 14, Action: "LIKE_TONGREN_WORK",    RelatedTable: "md_tongren_works", Group: "INFORM" },
  { ID: 15, Action: "COMMENT_PHOTO",        RelatedTable: "md_new_comment",   Group: "INFORM" },
  { ID: 16, Action: "REPLY_PHOTO",          RelatedTable: "md_new_comment",   Group: "INFORM" },
];


function getAllEventTypes() {
  return _.reduce(EVENT_TYPES_TABLE, (hash, cur) => {
    hash[cur.Action] = cur.ID;
    return hash;
  }, {});
}

const EVENT_TYPES = getAllEventTypes();

Notifications.EVENT_TYPES = EVENT_TYPES;

function getUserActionName(userAction) {
  return _.findKey(Notifications.EVENT_TYPES, function(value) {
    return value === userAction;
  });
}

Notifications.getInformTypes = function () {
  return _.map(_.filter(EVENT_TYPES_TABLE, {Group: "INFORM"}), 'ID');
};

Notifications.getFriInformTypes = function () {
  return _.map(_.filter(EVENT_TYPES_TABLE, {Group: "FRI"}), 'ID');
};

Notifications.getMsgInformTypes = function () {
  return _.map(_.filter(EVENT_TYPES_TABLE, {Group: "MSG"}), 'ID');
};

Notifications.isLikeMomentEvent = function (notification) {
  return notification.Type === EVENT_TYPES.LIKE_MOMENT;
};

Notifications.isRelatedCommentMoment = function(notification) {
  var userAction = notification.Type;
  return userAction === EVENT_TYPES.COMMENT_MOMENT ||
    userAction === EVENT_TYPES.REPLY_MOMENT;
};

Notifications.create = function(props) {
  props = _.assign({CreateTime: Date.now()}, props);
  return BaseModelClass.prototype.create.call(this, props);
};

Notifications.findUserUnRead = function(userId) {
  return this.where({TargetId:userId}, {Status: Notifications.Statuses.UNREAD}).then(Notifications.normalizeRecords);
};

Notifications.normalizeRecord = function(notification) {
  return Promise.resolve(notification)
  .then(function(notification) {
    notification.actionName = getUserActionName(notification.Type);
    return notification;
  }).then(function(notification) {
    var userId = notification.UserId;
    return Users.getBasicUserInfo(userId);
  }).then(function(user) {
    notification.user = user;
    return notification;
  });
};
Notifications.normalizeRecords = function(notifications) {
  return Promise.map(notifications, Notifications.normalizeRecord);
};

Notifications.addLikePhotoEvent = function(userId, photo) {
  return Photos.getPhotoUserId(photo).then(function(photoUserId) {
    return Notifications.addEvent(userId, photoUserId, EVENT_TYPES.LIKE_PHOTO, photo.ID);
  });
};

Notifications.addLikeRealityShowEvent = function (userId, realityShow) {
  return Notifications.addEvent(userId, realityShow.UserId, EVENT_TYPES.LIKE_REALITY_SHOW, realityShow.ID);
};

Notifications.softDeleteLikeRealityShowEvent = function (userId, realityShow) {
  var query = this.scope()
  .where('UserId', userId)
  .where('RelateId', realityShow.ID)
  .where('Type', EVENT_TYPES.LIKE_REALITY_SHOW)
  .update({Status: Notifications.Statuses.DELETED});
  return Notifications.executeByQuery(query);
};

Notifications.addLikeShowTopicEvent = function (userId, showTopic) {
  return Notifications.addEvent(userId, showTopic.UserId, EVENT_TYPES.LIKE_SHOW_TOPIC, showTopic.ID);
};

Notifications.softDeleteLikeShowTopicEvent = function (userId, showTopic) {
  var query = this.scope()
  .where('UserId', userId)
  .where('RelateId', showTopic.ID)
  .where('Type', EVENT_TYPES.LIKE_SHOW_TOPIC)
  .update({Status: Notifications.Statuses.DELETED});
  return Notifications.executeByQuery(query);
};

Notifications.softDeleteLikePhotoEvent = function(userId, photo) {
  var query = this.scope()
  .where('UserId', userId)
  .where('RelateId', photo.ID)
  .where('Type', EVENT_TYPES.LIKE_PHOTO)
  .update({Status: Notifications.Statuses.DELETED});
  return Notifications.executeByQuery(query);
};

Notifications.addEvent = function(userId, targetUserId, eventType, resourceId) {
  if(userId != targetUserId) {
    return this.create({
      UserId: userId,
      TargetId: targetUserId,
      Type: eventType,
      RelateId: resourceId
    });
  }else {
    return Promise.resolve(false);
  }
};

Notifications.softDeleteFollowEvent = function(userId, followUserId) {
  var query = this.scope()
  .where('UserId', userId)
  .where('TargetId', followUserId)
  .where('Type', EVENT_TYPES.FOLLOW)
  .update({Status: Notifications.Statuses.DELETED});
  return Notifications.executeByQuery(query);
};

Notifications.addLikeShowTopicEvent = function (userId, showTopic) {
  return Notifications.addEvent(userId, showTopic.UserId, EVENT_TYPES.LIKE_SHOW_TOPIC, showTopic.ID);
};

Notifications.softDeleteLikeShowTopicEvent = function (userId, showTopic) {
  var query = this.scope()
  .where('UserId', userId)
  .where('RelateId', showTopic.ID)
  .where('Type', EVENT_TYPES.LIKE_SHOW_TOPIC)
  .update({Status: Notifications.Statuses.DELETED});
  return Notifications.executeByQuery(query);
};

Notifications.removeOnDeleteShowTopic = function (topicId) {
  let Promise = require('bluebird');
  let getLikeShowTopicEventIds = function () {
    return Notifications.find({Status: [Notifications.Statuses.UNREAD, Notifications.Statuses.READ], Type: EVENT_TYPES.LIKE_SHOW_TOPIC, RelateId:topicId}, {cols: ['ID']})
    .map(row => row.ID);
  };
  let getCommentIds = function () {
    let Comments = require('./NewComments');
    return Comments.find({Status:0, TargetType: "md_show_topic", TargetId:topicId}, {cols: ['ID']})
    .map(row => row.ID)
  };
  let getCommentShowTopicEventIds = function () {
    return getCommentIds().then(function (commentIds) {
      return Notifications.find({Status:[Notifications.Statuses.UNREAD, Notifications.Statuses.READ], Type: [EVENT_TYPES.COMMENT_SHOW_TOPIC, EVENT_TYPES.REPLY_SHOW_TOPIC], RelateId: commentIds}, {cols: ['ID']})
    }).map(row => row.ID);
  };
  return Promise.all([
    getLikeShowTopicEventIds(),
    getCommentShowTopicEventIds(),
  ]).then(function (idsList) {
    let ids = _.flatten(idsList);
    return Notifications.updateByIds(ids, {Status: Notifications.Statuses.DELETED});
  })
};

// 删除同人作品消息
Notifications.removeDeletedTongrenWork = function(workId) {
  var query = this.scope()
  .where('RelateId', workId)
  .andWhere('Type', Notifications.EVENT_TYPES.AUDIT_TONGREN_WORK)
  .update({Status: Notifications.Statuses.DELETED});
  return Notifications.executeByQuery(query);
};

// 删除同人作品点赞消息
Notifications.removeDeletedTongrenLike = function(workId, userId) {
  var query = this.scope()
  .where('RelateId', workId)
  .andWhere('UserId', userId)
  .andWhere('Type', Notifications.EVENT_TYPES.LIKE_TONGREN_WORK)
  .update({Status: Notifications.Statuses.DELETED});
  return Notifications.executeByQuery(query);
};

// 删除同人作品评论消息
Notifications.removeDeletedTongrenComment = function(commentId) {
  var query = this.scope()
  .where('RelateId', commentId)
  .andWhere('Type', EVENT_TYPES.COMMENT_TONGREN_WORK)
  .update({Status: Notifications.Statuses.DELETED});
  return Notifications.executeByQuery(query);
};

Notifications.getUserInforms = function (userId, types, lastId, pageSize) {
  let query = Notifications.scope()
  .select(['ID','UserId','TargetId','Type','Status','CreateTime','RelateId','Message'])
  .where('TargetId', userId)
  .whereIn('Type', types)
  .whereNot('Status', Notifications.Statuses.DELETED)
  .orderBy('CreateTime', 'desc')
  .limit(pageSize);
  if(lastId) {
    query = query.where('ID', '<', lastId);
  }
  return Notifications.executeByQuery(query)
  .then(informs => {
    return _.map(informs, inform => {
      const relatedInfo = Notifications.getRelatedInfoByType(inform.Type);
      inform.RelatedTable = relatedInfo.RelatedTable;
      inform.ActionType = relatedInfo.Action;
      return inform;
    })
  })
};

Notifications.readUserInforms = function (userId, types, lastId, pageSize) {
  return Notifications.getUserInforms(userId, types, lastId, pageSize)
  .then(rows => {
    const ids = _.map(_.filter(rows, {Status: Notifications.Statuses.UNREAD}), 'ID');
    if(!_.isEmpty(ids)) {
      Notifications.updateByCondition({ID: ids}, {Status: Notifications.Statuses.READ});
    }
    return rows;
  });
};

Notifications.getRelatedInfoByType = function (type) {
  return _.find(EVENT_TYPES_TABLE, {ID: type});
};

Notifications.getRefers = function (informs) {
  const co = require('../common/util').co;
  const ModelManager = require('../models/ModelManager');


  function addIdToQueryHash(queryHash, tableName, ids) {
    if (queryHash[tableName]) {
      queryHash[tableName].ids = _.concat(queryHash[tableName].ids, ids);
    } else {
      queryHash[tableName] = {ids: ids, tableName:tableName}
    }
  }

  function queryRefers(tableName, ids) {
    const queryCols = Notifications.getReferQueryCols(tableName);
    const model = ModelManager.getModelByTableName(tableName);
    if(_.isEmpty(ids)) {
      return Promise.resolve(ids);
    } else {
      const queryIds = _.uniq(_.map(ids, id => parseInt(id, 10)));
      return model.find({ID: queryIds}, {cols: queryCols});
    }
  }

  return co(function* () {
    const result = {};

    const referTables = [
      "md_message",
      "md_answer",
      "md_comment",
      "md_moment",
      "md_new_comment",
      "md_show_topic",
      "md_photo",
      "md_reality_show",
      "md_tongren_works"
    ];

    const queryHash = _.reduce(informs, (hash, inform) => {
      const relatedInfo = Notifications.getRelatedInfoByType(inform.Type);
      if (relatedInfo.RelatedTable) {
        const tableName = relatedInfo.RelatedTable;
        if (hash[tableName]) {
          hash[tableName].ids.push(inform.RelateId);
        } else {
          hash[tableName] = {ids: [inform.RelateId], tableName: tableName}
        }
      }
      return hash;
    }, {});

    for(let i = 0; i < referTables.length; i++) {
      const tableName = referTables[i];
      const queryInfo = queryHash[tableName];
      if(!queryInfo)  continue;
      const data = yield queryRefers(tableName, queryInfo.ids);
      if(tableName === "md_comment") {
        const commentReferMomentIds = _.map(data, "TargetId");
        addIdToQueryHash(queryHash, "md_moment", commentReferMomentIds);
      }
      if(tableName === "md_new_comment") {
        const hash = _.groupBy(data, "TargetType");
        _.map(hash, (rows, tableName) => {
          const ids = _.map(rows, 'TargetId');
          addIdToQueryHash(queryHash, tableName, ids);
        });
      }
      result[tableName] = data;
    }

    //给md_photo加上userid字段
    const photos = result['md_photo'];
    if(photos) {
      const albumIds = _.map(photos, 'PhotoAlbumID');
      const albums = yield ModelManager.getModelByTableName("md_photo_album").findByIds(albumIds, ['ID', 'UserId']);
      _.forEach(photos, photo => {
        photo.UserId = _.get(_.find(albums, {ID: photo.PhotoAlbumID}), 'UserId');
      })
    }

    return  result;
  });
};

Notifications.getReferQueryCols = function (tableName) {
  const tableNameToCols = {
    md_show_topic: ['ID', 'ImgList', 'ImgAudit'],
    md_tongren_works: ['ID', 'Title', 'Description', 'CoverImageUrl', 'AttachmentList', 'TagList', 'Type', 'CreateTime'],
    md_comment: ['ID', 'UserId', 'TargetId', 'ReplyId', 'Text', 'CreateTime'],
    md_moment: ['ID', 'UserId', 'Text', 'ImgList', 'ImgAudit', 'CreateTime'],
    md_photo: ['ID', 'Url', 'AuditStatus','PhotoAlbumID', 'CreateTime'],
  };
  return tableNameToCols[tableName] || ['*'];
};

Notifications.getRelatedUsers = function (informs) {
  const userIds = _.map(informs, 'UserId');
  const Users = require('../models/Users');
  return Users.findByIds(userIds, ['ID', 'NickName', 'AvaAuthStatus', 'Avatar']);
};
