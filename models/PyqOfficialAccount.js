"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OfficialAccount = exports.DefaultOASetting = exports.PublicStatus = void 0;
const constants_1 = require("../common/constants");
const BaseModelClass = require("./BaseModelClass");
const util2_1 = require("../common/util2");
const constants_2 = require("../common/constants");
var PublicStatus;
(function (PublicStatus) {
    PublicStatus[PublicStatus["Public"] = 1] = "Public";
    PublicStatus[PublicStatus["NoPublic"] = 0] = "NoPublic";
})(PublicStatus = exports.PublicStatus || (exports.PublicStatus = {}));
exports.DefaultOASetting = {
    PublicStatus: 0,
    ForceFollow: 0,
    AutoBeHot: 0,
};
class OfficialAccountClass extends BaseModelClass {
    constructor() {
        super(constants_1.TABLE_NAMES.PyqOfficialAccount);
    }
    getSetting(roleId) {
        return __awaiter(this, void 0, void 0, function* () {
            let record = yield this.findOne({ RoleId: roleId }, ["PublicStatus", "ForceFollow", "AutoBeHot"]);
            if (record) {
                return record;
            }
            else {
                return exports.DefaultOASetting;
            }
        });
    }
    getAllRecords() {
        return __awaiter(this, void 0, void 0, function* () {
            return this.find({});
        });
    }
    getRecordMap() {
        return __awaiter(this, void 0, void 0, function* () {
            let records = yield this.getAllRecords();
            let map = new Map();
            for (let r of records) {
                map.set(r.RoleId, r);
            }
            return map;
        });
    }
    filterAllowCancel(ids) {
        return __awaiter(this, void 0, void 0, function* () {
            let recordMap = yield this.getRecordMap();
            return ids.filter((id) => {
                let record = recordMap.get(id) || { ForceFollow: false };
                return record.ForceFollow === 0;
            });
        });
    }
    getPublicIds(excludeIds) {
        return __awaiter(this, void 0, void 0, function* () {
            let query = this.scope().where("PublicStatus", 1).select(["RoleId"]);
            if (excludeIds && excludeIds.length > 0) {
                query = query.whereNotIn("RoleId", excludeIds);
            }
            let rows = (yield this.executeByQuery(query));
            return rows.map((x) => x.RoleId);
        });
    }
    getAllOfficialIds() {
        return __awaiter(this, void 0, void 0, function* () {
            let query = this.scope().select(["RoleId"]);
            let rows = (yield this.executeByQuery(query));
            return rows.map((x) => x.RoleId);
        });
    }
    isInOfficialAccountIdRange(roleId) {
        let startId = ********;
        return roleId >= startId && roleId <= startId + 500;
    }
    isOfficialAccount(roleId) {
        return (0, util2_1.contains)(constants_2.OfficialRoleIds, roleId) || this.isInOfficialAccountIdRange(roleId);
    }
    getAutoBeHotAccountId() {
        return __awaiter(this, void 0, void 0, function* () {
            let record = yield this.findOne({ AutoBeHot: 1 }, ["RoleId"]);
            if (record) {
                return record.RoleId;
            }
            else {
                return 0;
            }
        });
    }
    isPublic(roleId) {
        return __awaiter(this, void 0, void 0, function* () {
            let record = yield this.getSetting(roleId);
            if (record && record.PublicStatus) {
                return true;
            }
            else {
                return false;
            }
        });
    }
}
exports.OfficialAccount = new OfficialAccountClass();
//# sourceMappingURL=PyqOfficialAccount.js.map