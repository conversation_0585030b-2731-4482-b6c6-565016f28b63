import * as bluebird from "bluebird";
import * as _ from "lodash";
import { TABLE_NAMES } from "../common/constants";
import { FollowStatus as Statuses } from "../pyq-server/constants";
import BaseModelClass = require("./BaseModelClass");
import { PyqFollowRecord } from "../common/type";

class FollowClass extends BaseModelClass<PyqFollowRecord> {
  constructor() {
    super(TABLE_NAMES.PyqFollow);
  }

  async cancelFollow(roleId: number, otherRoleId: number) {
    const isFollow = await this.isFollowing(roleId, otherRoleId);
    if (!isFollow) {
      return bluebird.reject({ errorType: "cancelFollowInvalid", msg: "关注后才能取消关注" });
    } else {
      const ret = await this.updateByCondition(
        { RoleId: roleId, TargetId: otherRoleId },
        { Status: Statuses.CancelFollowed, UpdateTime: Date.now() }
      );
      return ret;
    }
  }

  isFollowing(roleId: number, otherRoleId: number) {
    if (roleId && otherRoleId) {
      return this.exists({ RoleId: roleId, TargetId: otherRoleId, Status: Statuses.Followed });
    } else {
      return bluebird.resolve(false);
    }
  }

  async filterMeFollow(roleIds: number[], meRoleId: number) {
    const query = this.normalScope().whereIn("TargetId", roleIds).where("RoleId", meRoleId).select("TargetId");
    const rows = await this.executeByQuery(query);
    return _.map(rows, "TargetId");
  }

  async filterFollowingMe(roleIds: number[], meRoleId: number) {
    const query = this.normalScope().whereIn("RoleId", roleIds).where("TargetId", meRoleId).select("RoleId");
    const rows = await this.executeByQuery(query);
    return _.map(rows, "RoleId");
  }

  async getFollowingIds(roleId: number): Promise<number[]> {
    const query = this.normalScope().select(["TargetId"]).where("RoleId", roleId).orderBy("ID", "desc");
    const rows = (await this.executeByQuery(query)) as Pick<PyqFollowRecord, "TargetId">[];
    return rows.map((r) => r.TargetId);
  }

  getFollowerIds(roleId: number[]) {
    const query = this.normalScope().select(["RoleId"]).where("TargetId", roleId);
    return this.executeByQuery(query).then(function (rows) {
      return _.map(rows, "RoleId");
    });
  }

  async getFollowingIdsByRoleIds(roleIds: number[]): Promise<Record<number, number[]>> {
    const hash = {};
    if (roleIds.length === 0) {
      return hash;
    }
    const query = this.normalScope().select(["RoleId", "TargetId"]).whereIn("RoleId", roleIds);
    const rows = (await this.executeByQuery(query)) as { RoleId: number; TargetId: number }[];
    for (const r of rows) {
      const followIds = hash[r.RoleId] || [];
      followIds.push(r.TargetId);
      hash[r.RoleId] = followIds;
    }
    return hash;
  }

  getFansCount(roleId: number) {
    const query = this.normalScope().where("TargetId", roleId);
    return this.countByQuery(query);
  }

  getFollowCount(roleId: number) {
    const query = this.normalScope().where("RoleId", roleId);
    return this.countByQuery(query);
  }

  async isFollowEach(roleId: number, targetId: number) {
    const query = this.normalScope()
      .where("RoleId", roleId)
      .where("TargetId", targetId)
      .union(this.normalScope().where("RoleId", targetId).where("TargetId", roleId));
    const rows = await this.executeByQuery(query);
    return rows.length === 2;
  }
}

const FollowModel = new FollowClass();

export = FollowModel;
