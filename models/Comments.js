const BaseModelClass = require('./BaseModelClass')
const Users = require('./Users')
const Comments = module.exports = new BaseModelClass('md_comment')
const _ = require('lodash')
const Promise = require('bluebird')

Comments.embededUserInfoAndReplayInfo = function (comment) {
  return Promise.join(
    Users.findById(comment.UserId),
    Users.findById(comment.ReplyId),
    function (userInfo, replyUserInfo) {
      userInfo = Users.Serializers.default(userInfo)
      replyUserInfo = Users.Serializers.default(replyUserInfo)
      comment.userInfo = userInfo
      if (replyUserInfo) {
        comment.replyUserInfo = replyUserInfo
      }
      return comment
    }
  )
}

Comments.countForMoment = function (moment) {
  return Comments.count({Status: Comments.Statuses.NORMAL, TargetId: moment.ID})
}

Comments.getCommentAndReplyCount = function (momentId) {
  return Comments.find({Status: 0, TargetId: momentId}, {cols: ['ReplyId']}).then(rows => {
    const info = {commentCount: 0, replyCount: 0}
    if (rows) {
      info.replyCount = rows.filter(r => r.ReplyId).length
      info.commentCount = rows.length - info.replyCount
    }
    return info
  })
}

Comments.getLastedComments = function (momentId, options) {
  const query = Comments.scope()
  .where({TargetId: momentId, Status: 0})
  .select(options.cols || '*')
  .orderBy('CreateTime', 'desc')
  .limit(options.limit || 10)
  return Comments.executeByQuery(query)
}

Comments.getLastedCommentsBatch = function (mIds, options) {
  return Promise.map(mIds, mId => {
    return Comments.getLastedComments(mId, options)
  }).then(_.flatten)
}

Comments.getCommentsCountBatch = function (momentIds) {
  const query = Comments.scope()
  .where({Status: 0})
  .whereIn('TargetId', momentIds)
  .select(['TargetId'])
  .count('ID as Count')
  .groupBy('TargetId')
  return Comments.executeByQuery(query)
}
