﻿/**
 * Created by z<PERSON><PERSON> on 16-9-12.
 */

var _ = require('lodash');
var BaseModelClass = require('./BaseModelClass');
const QnmRoleAble = require('./mixins/QnmRoleAble')
var ipUtil = require('../common/ipUtil');
const util = require('../common/util');
const Promise = require('bluebird');

class Privacy {
  static  get privacyOptions() {
    return ["location", "space", "name", "msg_for_following", "hide_lbs"];
  }

  constructor(info) {
    Privacy.privacyOptions.forEach(prop => {
      if(_.has(info, prop)) {
        this[prop] = info[prop];
      } else {
        this[prop] = false;
      }
    })
  }

  toPrivacyStr() {
    return _.values(this).map(isChecked => {
      return isChecked ? 1: 0;
    }).join(',');
  }

  static fromPrivacyStr(privacyStr) {
    return _.mapValues(_.zipObject(Privacy.privacyOptions, _.split(privacyStr, ',')), value => {
      return value == 1;
    });
  }
}

var PyqProfile = _.extend({
  PrimaryKey: 'RoleId',

  Privacy: Privacy,

  getFriendsRoleInfoIds: function (roleIds) {
    var self = this;
    return self.executeByQuery(
      self.scope().select('FriendList').whereIn('RoleId', roleIds)
    ).then(function (records) {
      return _.chain(records).map(function (record) {
        return _.split(record.FriendList, ',');
      }).flatten().compact().uniq().value();
    });
  },

  getLastLoginIps: function (roleIds) {
    return this.find({RoleId: roleIds}, {cols:['RoleId', 'LastLoginIp']})
      .then(function (records) {
        _.forEach(records, function (r) {
          if( r.LastLoginIp === '***********' || r.LastLoginIp === '************') {
            r.LastLoginIp = "";
          }
        });
        return records;
      }) ;
  },

  getLastLoginIpLocations: function (roleIds) {
    return this.getLastLoginIps(roleIds).then(function (rows) {
      return _.map(rows, function (r) {
        r.IpLocation = ipUtil.getLocationFromIp(r.LastLoginIp);
        return r;
      });
    })
  },

  getPrivacy: function (roleId) {
    return this.findOne({RoleId: roleId}, ['Privacy']).then(function (res) {
      let privacyStr = _.get(res, 'Privacy') || "";
      return Privacy.fromPrivacyStr(privacyStr);
    });
  },

  isHideLbsByRoleId: function (roleId) {
    var self = this;
    return self.getPrivacy(roleId).then(function (privacy) {
      return privacy.hide_lbs
    });
  },

  updatePrivacy: function (roleId, privacyStr) {
    return PyqProfile.updateByCondition({RoleId: roleId}, {Privacy: privacyStr});
  },

  getAvatars: function (roleIds) {
    const PyqData = require('../common/data').pyq;
    const QnmRoleInfo = require('./QNMRoleInfos');
    return Promise.all([
      PyqProfile.find({RoleId: roleIds}, {cols: ['RoleId', 'Photo', 'ShowPhoto', 'PhotoAudit']}),
      QnmRoleInfo.find({RoleId:roleIds}, {cols: ['RoleId', 'JobId', 'Gender']})
    ]).spread((rolePhotos, qnmRoleInfos) => {
      const roleIdToInfo = util.keyToRecordHash(qnmRoleInfos, 'RoleId');
      return rolePhotos.map(rolePhoto => {
        PyqData.setPhotoView(rolePhoto);
        const roleInfo = roleIdToInfo[rolePhoto.RoleId] || {};
        const avatar = rolePhoto.Photo ? rolePhoto.Photo : QnmRoleInfo.getJobAvatar(roleInfo);
        return {RoleId: rolePhoto.RoleId, Avatar: avatar}
      });
    })
  }

}, new BaseModelClass("pyq_profile"), QnmRoleAble);

module.exports = PyqProfile;
