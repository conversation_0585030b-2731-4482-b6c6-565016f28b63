/**
 * Created by z<PERSON><PERSON> on 16-10-28.
 */

var logger = require('../../common/logger');
var DB = require('../../common/db');
var util = require('../../common/util');
var _ = require('lodash');
let Constants = require('../../common/data').Constants;


//确保mixin进入的model具有ImgList和ImgAudit字段
var ImagesAuditable = {
  _auditLog: function(data) {
    logger.add(`debug_${this.tableName}_audit`, JSON.stringify(data));
  },

  photoAuditFinish: function (id, url, auditStatus) {
    var imgAudit;
    var self = this;
    self._auditLog({
      msg: "进入审核处理",
      id: id,
      url: url,
      auditStatus: auditStatus,
    });
    return DB.transact([
      function (conn) {
        return DB.query({
          table: self.tableName,
          cols: ['ImgList', 'ImgAudit'],
          filter: { ID: id},
          conn: conn
        }).then(function (rows) {
          var record = rows[0];
          self._auditLog({
            msg: "获取对应的record",
            id: id,
            url: url,
            auditStatus: auditStatus,
            topic: record,
          });
          let imgList = util.csvStrToArray(record.ImgList);
          let auditList = _.fill(new Array(imgList.length), Constants.STATUS_NORMAL);
          if(record.ImgAudit) {
            let curAuditList = record.ImgAudit.split(',');
            _.forEach(curAuditList, (val, index) => {
              auditList[index] = val;
            });
          }
          let auditIndex = _.indexOf(imgList, url);
          if(auditIndex != -1) {
            auditList[auditIndex] = auditStatus;
          }
          imgAudit = _.join(auditList, ',');
        })
      },

      function (conn) {
        self._auditLog({
          msg: "写入新的审核状态",
          id: id,
          url: url,
          auditStatus: auditStatus,
          newImgAudit: imgAudit
        });
        return DB.update({
          table: self.tableName,
          values: {ImgAudit: imgAudit},
          filter: { ID: id},
          conn: conn
        })
      }
    ])
  },
  videoAuditFinish: function (id, url, auditStatus) {
    var videoAudit;
    var self = this;
    self._auditLog({
      msg: "进入审核处理",
      id: id,
      url: url,
      auditStatus: auditStatus,
    });
    return DB.transact([
      function (conn) {
        return DB.query({
          table: self.tableName,
          cols: ['VideoList', 'VideoAudit'],
          filter: { ID: id },
          conn: conn
        }).then(function (rows) {
          var record = rows[0];
          self._auditLog({
            msg: "获取对应的record",
            id: id,
            url: url,
            auditStatus: auditStatus,
            topic: record,
          });
          let videoList = util.csvStrToArray(record.VideoList);
          let auditList = _.fill(new Array(videoList.length), Constants.STATUS_NORMAL);
          if (record.VideoAudit) {
            let curAuditList = record.VideoAudit.split(',');
            _.forEach(curAuditList, (val, index) => {
              auditList[index] = val;
            });
          }
          let auditIndex = _.indexOf(videoList, url);
          if (auditIndex != -1) {
            auditList[auditIndex] = auditStatus;
          }
          videoAudit = _.join(auditList, ',');
        })
      },

      function (conn) {
        self._auditLog({
          msg: "写入新的审核状态",
          id: id,
          url: url,
          auditStatus: auditStatus,
          newVideoAudit: videoAudit
        });
        return DB.update({
          table: self.tableName,
          values: { VideoAudit: videoAudit },
          filter: { ID: id },
          conn: conn
        })
      }
    ])
  }
};

module.exports = ImagesAuditable;
