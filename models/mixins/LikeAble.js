/**
 * Created by zhen<PERSON> on 16-9-26.
 */
var Promise = require('bluebird');
var _ = require('lodash');
var util = require('../../common/util');
var Users = require('../Users');

var LikeAble = {
  like: function (userId, id, option) {
    option = option || {};
    var self = this;
    return self.findByIdForce(id)
      .then(function (resource) {
        userId = parseInt(userId, 10);
        var likedUserList = _.map(_.uniq(_.compact(_.split(resource.ZanList, ','))), function (x) {
          return parseInt(x ,10);
        });
        if(_.includes(likedUserList, userId)) {
          return Promise.reject({errorType: "AlreadyLiked", msg: "您已经点赞过该资源"})
        } else {
          var zanList = _.join(_.concat(userId, likedUserList), ',');
          var updateProps = {ZanList: zanList};
          if(_.isFunction(option.beforeUpdate)) {
            option.beforeUpdate(updateProps, resource);
          }
          return self.updateById(id, updateProps)
            .then(function (resource) {
              return Promise.props({
                userInfo: Users.getUser(userId, ['ID', 'NickName', 'Avatar', 'AvaAuthStatus']),
                resource: resource,
              });
            });
        }
      })
  },

  unLike: function (userId, id, option) {
    var self = this;
    option = option || {};
    return self.findByIdForce(id)
      .then(function (resource) {
        userId = parseInt(userId, 10);
        var likedUserList = _.map(_.compact(_.split(resource.ZanList, ',')), function (x) {
          return parseInt(x ,10);
        });
        if(!_.includes(likedUserList, userId)) {
          return Promise.reject({errorType: "cancelUnLikedResource", msg: "您尚未赞过该资源"})
        } else {
          var zanList = _.join(_.pull(likedUserList, userId), ',');
          var updateProps = {ZanList: zanList};
          if(_.isFunction(option.beforeUpdate)) {
            option.beforeUpdate(updateProps, resource);
          }
          return self.updateById(id, updateProps);
        }
      })
      .then(function (resource) {
        return Promise.props({
          userInfo: Users.getUser(userId, ['ID', 'NickName', 'Avatar', 'AvaAuthStatus']),
          resource: resource,
        });
      });
  },

  getLikedUsers: function (record, curUserId) {
    var likedUserIds= util.csvStrToArray(record.ZanList);
    return Users.getUsersByIds(likedUserIds, ['ID', 'Avatar', 'AvaAuthStatus', 'NickName'])
      .then(function (users) {
        return _.map(users, function (user) {
          require('../../common/data').md.setAvatarView(user, curUserId);
          return user;
        });
      });
  }
};

module.exports = LikeAble;
