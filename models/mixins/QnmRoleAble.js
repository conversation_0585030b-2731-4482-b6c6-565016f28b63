﻿/**
 * Created by z<PERSON><PERSON> on 16-11-18.
 */

let QnmRoleAble = module.exports;
let QnmServerList = require('../../service/qnm/server/list');
let _ = require('lodash');

QnmRoleAble.queryByServerId = function (query, serverId) {
  return QnmServerList.getMergedServerIds(serverId)
    .then(function (serverIds) {
      query = query.where('RoleId', '>', 10000)
        .andWhere(function () {
          var self = this;
          serverIds.forEach(function (serverId) {
            serverId = _.parseInt(serverId, 10);
            self.orWhereRaw('RoleId % 10000 = ?', serverId);
          })
        });
      return {query: query}; // 这里不能直接返回query， knex的query对象自己覆盖了then方法
    });
};

QnmRoleAble.findByRoleId = function (roleId) {
  return this.findOne({RoleId: roleId});
};

QnmRoleAble.getServerIdByRoleId = function (roleId) {
  return _.trimStart(_.toString(roleId).slice(-4), '0');
};

QnmRoleAble.getServerIdByGuildId = function (guildId) {
  return _.trimStart(_.toString(guildId).slice(-4), '0');
};

/**
 * filter roleIds by serverid in data center server list
 * @param {Array} roleIds
 */
QnmRoleAble.filterRoleIdsByValidServerId = function (roleIds) {
  return QnmRoleAble.getValidServerIds().then(function (serverIds) {
    let serverIdSet = new Set(serverIds);
    return _.filter(roleIds, roleId => {
      let serverId = +QnmRoleAble.getServerIdByRoleId(roleId);
      return serverIdSet.has(serverId);
    });
  });
};

QnmRoleAble.getValidServerIds = function () {
  return QnmServerList.getList().then(function (list) {
    return _.uniq(_.map(list, 'id'));
  });
};
