let _ = require('lodash');

var AuditModel = {
  isAuditing: function (record) {
    return record.AuditStatus === AuditModel.AUDIT_STATUSES.AUDITING;
  },

  isAuditPassed: function (record) {
    return record.AuditStatus === AuditModel.AUDIT_STATUSES.PASSED ||
      record.AuditStatus === AuditModel.AUDIT_STATUSES.RECOMMEND;
  },

  isAuditRecommend: function (record) {
    return record.AuditStatus === AuditModel.AUDIT_STATUSES.RECOMMEND;
  },

  isAuditReject: function (record) {
    return record.AuditStatus === AuditModel.AUDIT_STATUSES.REJECT;
  },


  AUDIT_STATUSES: {
    REJECT: -1,
    AUDITING: 0,
    PASSED: 1,
    RECOMMEND: 2
  },

  sendToAudit: function (record, option) {
    option = _.defaults(option, {product: 'qnm'});
    let picId = this.tableName + ":" + record.ID;
    require('../../common/audit').sendPic(option.product, [record.Url], {
      picId: picId,
      roleId: record.RoleId
    })
  }
}


module.exports = AuditModel;
