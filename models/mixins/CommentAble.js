/**
 * Created by <PERSON><PERSON><PERSON> on 16-9-26.
 */

var NComments = require('../NewComments');

var CommentAble = {
  comment: function (userId, targetId, text) {
    return NComments.comment(userId, this.tableName, targetId, text);
  },

  replyComment: function (userId, targetId, replyId, text) {
    return NComments.replyComment(userId, this.tableName, targetId, replyId, text);
  },

  getCommentsCount: function (id) {
    return NComments.countForResource(this.tableName, id);
  },

  getComments: function (id) {
    return NComments.getCommentsByResource(this.tableName, id);
  },

};

module.exports = CommentAble;
