import { RoleIdPartitionModel } from './RoleIdPartitionModel'

export interface QnRolePropRecord {
  RoleId: number
  EquipInfo: string
  PropertyInfo: string
  OtherInfo: string
  PointInfo: string
  UpdateTime: number
  bucketno?: number
}

class RoleAttr {
  static get AttrList() {
    return ['hp', 'mhit', 'pmiss', 'expfull', 'cor', 'block', 'mfatal',
      'patt', 'mdef', 'mf', 'pdef', 'mp', 'ignoreblock', 'sta', 'int',
      'mmiss', 'matt', 'exp', 'pfatal', 'str', 'anger', 'agi', 'phit']
  }
  constructor(props) {
    var self = this;
    RoleAttr.AttrList.forEach(function (attr) {
      self[attr] = props[attr]
    })
  }

  static fromJsonStr(str) {
    return new RoleAttr(JSON.parse(str));
  }
}

class RoleAntiAttr {
  static get AttrList() {
    return ['AntiLight', 'AntiTie', 'AntiWind', 'AntiIce', 'AntiMass', 'AntiDizzy', 'AntiPoison', 'AntipFatal',
      'AntiWater', 'AntiFire', 'AntiSilence', 'AntiThunder', 'AntiPetrify', 'AntiBianHu', 'AntiBind', 'AntimFatal',
      'AntiSleep', 'AntiIllusion', 'AntiFreeze'];
  }
  constructor(props) {
    var self = this;
    RoleAntiAttr.AttrList.forEach(function (attr) {
      self[attr] = props[attr]
    })
  }

  static fromJsonStr(str) {
    return new RoleAntiAttr(JSON.parse(str));
  }
}

class QnRolePropClass extends RoleIdPartitionModel<QnRolePropRecord> {
  tableName(): string {
    return 'qn_roleprop'
  }

  async getRoleProp(roleId: number) {

    let cols = ['RoleId', 'EquipInfo', 'PropertyInfo', 'OtherInfo']
    let record = await this.findByRoleId(roleId, cols)
    if (record) {
      return {
        roleId: roleId,
        attrs: RoleAttr.fromJsonStr(record.PropertyInfo),
        antiAttrs: RoleAntiAttr.fromJsonStr(record.OtherInfo),
        equipInfo: JSON.parse(record.EquipInfo)
      }
    } else {
      return null
    }
  }
}

export let QnRoleProp = new QnRolePropClass()