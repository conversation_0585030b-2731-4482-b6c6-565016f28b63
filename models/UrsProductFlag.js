const BaseModelClass = require('./BaseModelClass')
const UrsProductFlag = new BaseModelClass('urs_product_flag')
const _ = require('lodash')

const FlagType = {
  ON: 1,
  OFF: 0
}

/**
 * 迁移的数据163账号一部分不带@163.com后缀， 一部分含有后缀
 * @param {string} userName
 */
function getQueryUserNames(userName) {
  let suffix = '@163.com'
  if (userName.endsWith(suffix)) {
    let baseName = userName.substr(0, userName.length - suffix.length)
    return [baseName, userName]
  } else if (!userName.includes('@')) {
    let fullName = userName + suffix
    return [userName, fullName]
  } else {
    return [userName]
  }
}

UrsProductFlag.queryAllFlags = function (userName) {
  let queryUserNames = getQueryUserNames(userName)
  let query = this.scope()
    .whereIn('UserName', queryUserNames)
    .select(['Product', 'Flag'])
    .orderBy('UpdateTime', 'asc') // 新的覆盖旧的
  return this.executeByQuery(query)
    .then(rows => {
      return _.reduce(rows, (hash, row) => {
        hash[row.Product] = row.Flag
        return hash
      }, {})
    })
}

UrsProductFlag.queryFlagByProduct = function (userName, product) {
  let queryUserNames = getQueryUserNames(userName)
  let query = this.scope()
    .whereIn('UserName', queryUserNames)
    .select(['Flag'])
    .where('Product', product)
    .orderBy('UpdateTime', 'desc')
    .limit(1) //最新设置的一条
  return this.executeByQuery(query)
    .then(rows => {
      if (rows && rows[0]) {
        return rows[0].Flag
      } else {
        return FlagType.OFF
      }
    })
}

module.exports = UrsProductFlag
