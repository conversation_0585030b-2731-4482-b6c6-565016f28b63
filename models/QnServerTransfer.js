/**
 * Created by <PERSON>hen<PERSON> on 2017/2/22.
 */

const BaseModelClass = require('./BaseModelClass');
const _ = require('lodash') ;
const QnServerTransfer = new BaseModelClass("qn_server_transfer");
const Promise = require('bluebird');
const co = require('../common/util').co;


/**
 * 以该roleId为终点， 得到原始roleId到该Id转服经过的路径
 * @param roleId
 * @return {Array} roleIds列表
 */
QnServerTransfer.getTransferPathTo = function (roleId) {
  return co(function *() {
    const path = [];
    while(roleId) {
      path.unshift(roleId);
      roleId = yield QnServerTransfer.getOriginRoleId(roleId);
    }
    return _.map(path, x => parseInt(x));
  });
};

/**
 * 以该roleId为起点， 得到该Id到当前roleId转服经过的路径
 * @param  roleId
 * @return {Array} roleIds列表
 */
QnServerTransfer.getTransferPathFrom = function (roleId) {
  return co(function *() {
    const path = [];
    while(roleId) {
      path.push(roleId);
      roleId = yield QnServerTransfer.getNewRoleId(roleId);
    }
    return _.map(path, x => parseInt(x));
  });
};

QnServerTransfer.getOriginRoleId = function (roleId) {
  return QnServerTransfer.findOne({NewRoleId: roleId}, ['RoleId']).then(row => {
    return _.get(row, 'RoleId');
  });
};

QnServerTransfer.getNewRoleId = function (roleId) {
  return QnServerTransfer.findOne({roleId: roleId}, ['NewRoleId']).then(row => {
    return _.get(row, 'NewRoleId');
  });
};

module.exports = QnServerTransfer;
