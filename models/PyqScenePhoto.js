"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PyqScenePhoto = exports.MAX_SIZE_FOR_PLAYER = void 0;
const BaseModelClass = require("./BaseModelClass");
const constants_1 = require("../common/constants");
exports.MAX_SIZE_FOR_PLAYER = 30; // 每个人最多存30个
class PyqScenePhotoClass extends BaseModelClass {
    constructor() {
        super(constants_1.TABLE_NAMES.PyqScenePhoto);
    }
    findByRoleIdAndIndex(roleId, index, cols) {
        return __awaiter(this, void 0, void 0, function* () {
            let record = yield this.findOne({ RoleId: roleId, Index: index, Status: constants_1.Statues.Normal }, cols);
            return record;
        });
    }
    add(roleId, index, url) {
        return __awaiter(this, void 0, void 0, function* () {
            let record = yield this.findByRoleIdAndIndex(roleId, index, ['ID']);
            if (record && record.ID) {
                yield this.updateByCondition({ ID: record.ID }, { CreateTime: Date.now(), Url: url });
                return record.ID;
            }
            else {
                let ret = yield this.insert({ RoleId: roleId, Index: index, Url: url, CreateTime: Date.now(), Status: constants_1.Statues.Normal });
                return ret.insertId;
            }
        });
    }
    listAllByRoleId(roleId, cols) {
        return __awaiter(this, void 0, void 0, function* () {
            let records = yield this.find({ RoleId: roleId, Status: constants_1.Statues.Normal }, { limit: exports.MAX_SIZE_FOR_PLAYER, cols: cols });
            return records;
        });
    }
}
exports.PyqScenePhoto = new PyqScenePhotoClass();
//# sourceMappingURL=PyqScenePhoto.js.map