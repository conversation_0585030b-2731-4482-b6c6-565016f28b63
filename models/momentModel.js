"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MomentModel2 = exports.BASIC_MOMENT_COLS = exports.MomentModel2Class = void 0;
const constants_1 = require("../common/constants");
const logger_1 = require("../pyq-server/logger");
const baseModel2_1 = require("./baseModel2");
const logger = (0, logger_1.clazzLogger)("models/momentModel");
class MomentModel2Class extends baseModel2_1.BaseModelClass {
    constructor() {
        super("pyq_moment");
    }
    getMinIdGteCreateTime(createTime) {
        return __awaiter(this, void 0, void 0, function* () {
            const query = this.scope().min("ID as minId").where("CreateTime", ">=", createTime).limit(1);
            let rows = [];
            rows = yield this.executeByQuery(query);
            let maxId = 0;
            if (rows && rows.length > 0) {
                maxId = rows[0].minId;
                logger.info({ maxId, createTime }, "GetMaxIdGteCreateTimeOk");
            }
            else {
                logger.info({ createTime }, "GetMaxIdGteCreateTimeEmpty");
            }
            return maxId;
        });
    }
    /**
     * 过滤出存在的动态ID
     * @param ids 需要检查的动态ID数组
     * @returns 实际存在的动态ID数组
     */
    filterExists(ids) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!ids || ids.length === 0) {
                return [];
            }
            const query = this.normalScope().whereIn("ID", ids).select("ID");
            let rows = [];
            rows = yield this.executeByQuery(query);
            return rows.map((r) => r.ID);
        });
    }
}
exports.MomentModel2Class = MomentModel2Class;
MomentModel2Class.ShowMomentCols = constants_1.PYQ_MOMENT_COLS;
exports.BASIC_MOMENT_COLS = constants_1.PYQ_MOMENT_COLS;
exports.MomentModel2 = new MomentModel2Class();
//# sourceMappingURL=momentModel.js.map