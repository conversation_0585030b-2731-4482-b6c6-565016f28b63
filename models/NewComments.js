/**
 * Created by <PERSON><PERSON><PERSON> on 16-9-26.
 */

var BaseModelClass = require('./BaseModelClass');
var _ = require('lodash');
var Users = require('./Users');
var util = require('../common/util');


var NComments = _.extend(new BaseModelClass("md_new_comment"), {
  create: function (props) {
    var props = _.merge({CreateTime: Date.now()}, props);
    return BaseModelClass.prototype.create.call(NComments, props);
  },

  comment: function (userId, targetType, targetId, text) {
    return NComments.create({
      UserId: userId,
      TargetType: targetType,
      TargetId: targetId,
      Text: text,
    }).then(NComments.fillUser)
  },

  fillUser: function (comment) {
    return Users.getUsersByIds([comment.UserId, comment.ReplyId], ['ID', 'Avatar', 'AvaAuthStatus', 'NickName'])
      .then(function (users) {
        util.embeddedOn(comment, users, 'UserId', 'ID', 'UserInfo');
        util.embeddedOn(comment, users, 'ReplyId', 'ID', 'ReplyUserInfo');
        return comment;
      });
  },

  replyComment: function (userId, targetType, targetId, replyId, text) {
    if(replyId == userId) {
      return Promise.reject({errorType: "ReplySelf", msg: "您不能回复自己的评论!"})
    } else {
      return NComments.create({
        UserId: userId,
        TargetType: targetType,
        TargetId: targetId,
        ReplyId: replyId,
        Text: text
      }).then(NComments.fillUser);
    }
  },


  countForResource: function (targetType, targetId) {
    return NComments.count({TargetType: targetType, TargetId: targetId, Status: NComments.Statuses.NORMAL});
  },

  getCommentsByResource: function (targetType, targetId) {
    return NComments.executeByQuery(
      NComments.normalScope()
        .select('ID', 'UserId', 'ReplyId', 'Text', 'CreateTime')
        .where('TargetType', targetType)
        .where('TargetId', targetId)
        .orderBy('CreateTime', 'desc')
        .orderBy('ID', 'desc')
    ).then(function (cs) {
      _.forEach(cs, function (c) {
        c.Duration = Date.now() - c.CreateTime;
      });
      return cs;
    });
  },

  getRelateUserIds: function (comments) {
    return _.compact(_.flatMap(comments, c=> [c.UserId, c.ReplyId]));
  }

});

module.exports = NComments;
