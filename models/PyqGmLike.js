const BaseModelClass = require('./BaseModelClass')
const PyqGmLike = new BaseModelClass('pyq_gm_like')
const $24Hours = 24 * 3600 * 1000

PyqGmLike.addLike = function getProfile (gmId, roleId) {
  function addLikeRecord () {
    return PyqGmLike.insert({GmId: gmId, RoleId: roleId, CreateTime: Date.now()})
    .then(insertInfo => {
      return {id: insertInfo.insertId}
    })
  }
  return this.findOne({GmId: gmId, RoleId: roleId}, ['CreateTime'])
  .then(record => {
    if (!record) {
      return addLikeRecord()
    } else {
      const lastLikeTime = record.CreateTime
      if (Date.now() - lastLikeTime > $24Hours) {
        return addLikeRecord()
      } else {
        return Promise.reject({errorType: 'LikeFrequent', msg: '每个角色24小时只能点赞一次'})
      }
    }
  })
}

PyqGmLike.countGmLike = function (gmId) {
  return this.count({GmId: gmId})
}

PyqGmLike.isRecentLiked = function (gmId, roleId) {
  return this.findOne({GmId: gmId, RoleId: roleId}, ['CreateTime'])
  .then(record => {
    if (record) {
      const lastLikeTime = record.CreateTime
      return Date.now() - lastLikeTime < $24Hours
    } else {
      return false
    }
  })
}

module.exports = PyqGmLike
