let _ = require("lodash");
let Users = require('./Users');
let UserType = Users.UserType;

let BaseModelClass = require('./BaseModelClass');
let MdTongrenUser = _.extend(new BaseModelClass("md_tongren_user"));

module.exports = MdTongrenUser;

// 获取梦岛用户列表的基本信息
MdTongrenUser.getUsersByIds = function(userIds, curUserId) {
    let selectProps = ['ID', 'NickName', 'Avatar', 'AvaAuthStatus'];
    return Users.getUsersByIds(userIds, selectProps)
        .then(users => {
            return Users.setUsersAvatar(users, curUserId);
        });
};

// 获取梦岛用户的基本信息
MdTongrenUser.getUserById = function(userId, curUserId) {
    let selectProps = ['ID', 'NickName', 'Avatar', 'AvaAuthStatus'];
    return Users.findById(userId, selectProps)
        .then(user => {
            return Users.setUserAvatar(user, curUserId);
        });
};

// 获取梦岛用户完整信息
MdTongrenUser.getUserFullInfoById = function(userId, curUserId) {
    let selectProps = ['ID', 'NickName', 'Avatar', 'AvaAuthStatus', 'Gender', 'GenderStatus', 'Status', 'UserType'];
    return Users.findById(userId, selectProps)
        .then(user => {
            return Users.setUserAvatar(user, curUserId);
        });
};

// 获取同人用户基本信息
MdTongrenUser.getTonrenUserById = function(userId) {
    let query = this.scope()
        .where('UserId', userId)
        .select('CurrentScore', 'HistoryScore');
    return MdTongrenUser.executeByQuery(query);
};

// 根据搜索用户名获取UserIds
MdTongrenUser.getUserIdBySearchName = function(nameKeyWord) {
    let query = Users.scope()
        .where('NickName', 'like', '%' + nameKeyWord + '%')
        .select('ID');
    return Users.executeByQuery(query)
        .then(data => {
            return _.map(data, 'ID');
        });
};

// 创建同人用户
MdTongrenUser.addTongrenUser = function(userInfo) {
    // 创建用户记录
    let props = {
        UserId: userInfo.UserId,
        CurrentScore: userInfo.CurrentScore,
        HistoryScore: userInfo.HistoryScore,
        CreateTime: userInfo.CreateTime
    };
    let query = MdTongrenUser.scope()
        .insert(props);
    return MdTongrenUser.executeByQuery(query);
};

// 更新用户积分
MdTongrenUser.updateScore = function(userInfo) {
    let props = {
        CurrentScore: userInfo.CurrentScore,
        HistoryScore: userInfo.HistoryScore
    };
    let query = MdTongrenUser.scope()
        .where('UserId', userInfo.UserId)
        .update(props);
    return MdTongrenUser.executeByQuery(query);
};

// 更新用户积分或创建同人用户
MdTongrenUser.updateOrCreateScore = function(userInfo) {
    return MdTongrenUser.getTonrenUserById(userInfo.UserId)
        .then(tongrenUserData => {
            if(!_.isEmpty(tongrenUserData)) {
                // 更新用户积分
                userInfo = _.defaults(userInfo, tongrenUserData[0]);
                return MdTongrenUser.updateScore(userInfo);
            } else {
                // 创建同人用户
                return MdTongrenUser.addTongrenUser(userInfo);
            }
        });
};

// 设置用户为二次元
MdTongrenUser.setUserToACG = function(userId) {
    return Users.findById(userId, 'UserType')
        .then(type => {
            if(type != UserType.ACG) {
                return Users.setUserType(userId, UserType.ACG);
            }
        });
};