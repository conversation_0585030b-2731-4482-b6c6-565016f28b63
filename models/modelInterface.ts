export interface PyqMomentRecord {
  ID: number;
  RoleId: number;
  Text: string;
  ImgList: string;
  ImgAudit: string;
  VideoList: string;
  VideoAudit: string;
  VideoCoverList: string;
  VideoCoverAudit: string;
  ZanList: string;
  Hot: number;
  HotState: string;
  CreateTime: number;
  Status: number;
}

export interface PyqSpaceEventRecord {
  ID: number;
  RoleId: number;
  TargetId: number;
  PublishTime: number;
}

export interface PyqCommentRecord {
  ID: number;
  RoleId: number;
  TargetId: number;
  ReplyId: number;
  Text: string;
  TopTime: number;
  LikeCount: number;
  CreateTime: number;
  Status: number;
}

export interface PyqMomentLikeRecord {
  ID: number;
  RoleId: number;
  MomentId: number;
  TargetId: number;
  Status: number;
  CreateTime: number;
}

export interface PyqMomentForwardRecord {
  ID: number;
  RoleId: number;
  MomentId: number;
  ForwardId: number;
  OriginId: number;
}

export interface PyqPhotoRecord {
  ID: number;
  RoleId: number;
  Url: string;
  AuditStatus: number;
  CreateTime: number;
  Status: number;
  Type: number;
}

export interface QnmRoleInfoRecord {
  RoleId: number;
  UserName: string;
  ServerId: number;
  RoleName: string;
  Gender: number;
  Level: number;
  JobId: number;
  CreateTime: number;
  VIP: number;
  Title: string;
  TitleId: number;
  GangId: number;
  Gang: string;
  ImageUrl: string;
  State: number;
  UpdateTime: number;
  Xiuwei: number;
  Outfit: number;
  Money: number;
  bucketno: number;
  FTRoleName: string;
  SnsGender: number;
  OtherInfo: string;
  UsedName: string;
  UsedNameStatus: number;
  FightingCapacity: string;
  XianFanStatus: number;
  Status: number;
}

export interface PyqGeoRecord {
  RoleId: number;
  Longitude: number;
  Latitude: number;
  Online: number;
  HideLbs: number;
}

export interface PyqProfileRecord {
  RoleId: number;
  Location: string;
  Signature: string;
  Flower: number;
  RenQi: number;
  Gift: number;
  Photo: string;
  PhotoAudit: number;
  ShowPhoto: number;
  FriendList: string;
  HideList: string;
  BlackList: string;
  ExpressionBase: string;
  Wedding: string;
  Privacy: string;
  UpdateTime: number;
  LastLoginIp: string;
  background: background;
}

type background = {
  backgroundid?: number;
  backgroundvalidity?: number;
};

export interface PyqFollowRecord {
  ID: number;
  RoleId: number;
  TargetId: number;
  Status: number;
  CreateTime: number;
  UpdateTime: number;
}

export interface PyqTopicRecord {
  ID: number;
  Subject: string;
  MomentList: string;
  Hot: number;
  CreateTime: number;
  UpdateTime: number;
  Status: number;
  Type: number;
}

export interface PyqTopicMomentRecord {
  ID: number;
  TopicId: number;
  RoleId: number;
  MomentId: number;
  CreateTime: number;
}

export interface PyqMessageRecord {
  ID: number;
  RoleId: number;
  TargetId: number;
  ReplyId: number;
  OwnerId?: number;
  Text: string;
  CreateTime: number;
  Status: number;
}

export interface PyqChatPhotoCollect {
  ID: number;
  RoleId: number;
  PhotoId: number;
  CreateTime: number;
}

export interface PyqInformRecord {
  ID: number;
  RoleId: number;
  TargetId: number;
  ObjectId: number;
  RelateId: string;
  Text?: any;
  Type: number;
  Status: number;
  CreateTime: number;
}

export interface MdMomentRecord {
  ID: number;
  UserId: number;
  Text: string;
  ImgList: string;
  ImgAudit: string;
  Status: number;
  CreateTime: number;
}

export interface MdShowTopicRecord {
  ID: number;
  SubjectId: number;
  UserId: number;
  Status: number;
  CreateTime: number;
  UpdateTime: number;
  ImgList: string;
  ImgAudit: string;
  Title: string;
  Content: string;
  Desc: string;
  ZanList: string;
  Hot: number;
  HotState: string;
}
