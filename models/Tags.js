var BaseModelClass = require('./BaseModelClass');
var Tags = new BaseModelClass("md_tag");
var _ = require('lodash');

Tags.create = function(props) {
  var now = Date.now();
  props = _.assign({CreateTime: now}, props);
  return BaseModelClass.prototype.create.call(this, props);
};

Tags.createForTarget = function(targetType, targetId, tags) {
  var insertProps = _.map(tags, function(tagTitle) {
    return {
      TargetType: targetType,
      TargetId: targetId,
      Title: tagTitle,
      CreateTime: Date.now()
    };
  });
  var query = Tags.scope().insert(insertProps);
  return Tags.executeByQuery(query);
};

Tags.findByTarget = function(targetType, targetId) {
  return this.where({TargetType: targetType, TargetId: targetId});
};

Tags.updateByTarget  = function(targetType, targetId, tags) {
  return Tags.deleteByTarget(targetType, targetId)
    .then(function() {
      return Tags.createForTarget(targetType, targetId, tags);
    });
};

Tags.deleteByTarget = function(targetType, targetId) {
  var query = this.scope().where({TargetType: targetType, TargetId: targetId}).del();
  return Tags.executeByQuery(query);
};

Tags.toTagTitles = function(tags) {
  return _.map(tags, 'Title');
};

Tags.fromTagsStr = function(tagsStr) {
  return _.chain( _.split(tagsStr, ','))
    .map(function(str) {
      return _.trim(str);
    }).reject(function(str) {
      return _.isEmpty(str);
    }).uniq().value();
};

Tags.findTargetIdsByTags = function(targetType, titles) {
  var query = Tags.scope().distinct('TargetId')
    .where('TargetType', targetType)
    .whereIn('Title', titles);
  return Tags.executeByQuery(query)
    .then(function(records) {
      return _.map(records, 'TargetId');
    });
};

Tags.findTargetIdsByTagLike = function(targetType, keyword) {
  var query = Tags.scope().distinct('TargetId')
    .where('TargetType', targetType)
    .where('Title', 'like', `%${Tags.escapeLikeStr(keyword)}%`);
  return Tags.executeByQuery(query)
    .then(function(records) {
      return _.map(records, 'TargetId');
    });
};

module.exports = Tags;
