var BaseModelClass = require('./BaseModelClass');
var QNRoleInfos = module.exports = new BaseModelClass("qn_roleinfo");
var _ = require('lodash');
var QNData = require('../common/data').qn;
var Promise = require('bluebird');
var QNServers = require('./QNServers');
var PhotoAlbums = require('./PhotoAlbums');
var QNEventBus = require('../qn-server/eventBus');
var QNRoleInfoService = require('../service/qn/role/info');
var util = require('../common/util');

QNRoleInfos.PrimaryKey = 'RoleId';

QNRoleInfos.JobIdToName = QNData.JobHash;

QNRoleInfos.Servers = {};

QNRoleInfos.Servers.findAll = function () {
  return QNServers.findAll().then(function (servers) {
    return _.groupBy(servers, 'id');
  });
};

QNRoleInfos.Servers.findById = function (serverId) {
  return require('../service/qn/server/list.js')
    .getFullServerHash()
    .then(function (serverIdToInfo) {
      return serverIdToInfo[serverId];
    });
};


QNRoleInfos.create = function (props) {
  var now = Date.now();
  props = _.assign({ CreateTime: now, UpdateTime: now }, props);
  return BaseModelClass.prototype.create.call(this, props);
};

QNRoleInfos.findByUserName = function (userName) {
  var selectList = ['CreateTime', 'Gender', 'JobId', 'RoleId', 'RoleName', 'ServerId', 'Level'];
  return QNRoleInfos.where({ UserName: userName })
    .then(function (roleInfos) {
      return Promise.map(roleInfos, function (roleInfo) {
        return QNRoleInfos.Servers.findById(roleInfo.ServerId).then(function (server) {
          roleInfo = _.pick(roleInfo, selectList);
          roleInfo.Job = QNRoleInfos.JobIdToName[roleInfo.JobId];
          roleInfo.Server = server;
          return roleInfo;
        });
      });
    });
};


QNRoleInfos.getRoleInfoWithServerAndJob = function (roleInfo) {
  if (roleInfo && roleInfo.ServerId) {
    return QNRoleInfos.Servers.findById(roleInfo.ServerId).then(function (server) {
      roleInfo.Server = server || null;
      if (server) {
        roleInfo.ServerId = server.id;
      }
      roleInfo.Job = QNRoleInfos.JobIdToName[roleInfo.JobId] || null;
      return roleInfo;
    });
  } else {
    return Promise.resolve(roleInfo);
  }
};


QNRoleInfos.fillRoleInfo = function (roleInfo) {
  var serverId = _.get(roleInfo, 'ServerId');
  var guildId = _.get(roleInfo, 'GangId');
  return Promise.join(
    QNRoleInfos.getRoleInfoWithServerAndJob(roleInfo),
    QNRoleInfoService.getGuild(serverId, guildId),
    function (roleInfo, guild) {
      var gangName = guild.Name || null;
      roleInfo.Gang = gangName;
      return roleInfo;
    }
  );
};

QNRoleInfos.getRoleInfosWithServerAndJob = function (roleInfos) {
  return Promise.map(roleInfos, QNRoleInfos.getRoleInfoWithServerAndJob);
};

QNRoleInfos.findByRoleId = function (roleId) {
  return QNRoleInfos.findOne({ RoleId: roleId })
    .then(QNRoleInfos.getRoleInfoWithServerAndJob)
    .then(QNRoleInfos.Serializers.default);
};

QNRoleInfos.findByRoleIds = function (roleIds) {
  return QNRoleInfos.where({ RoleId: roleIds })
    .then(QNRoleInfos.getRoleInfosWithServerAndJob)
    .then(QNRoleInfos.Serializers.default);
};

QNRoleInfos.Serializers = {
  default: function (record) {
    var attrList = ['CreateTime', 'Gender', 'JobId', 'RoleId', 'RoleName', 'ServerId', 'Level', 'Server', 'Job', 'GangId'];
    var pickAttr = function (obj) {
      return _.pick(obj, attrList);
    };
    if (_.isArray(record)) {
      return _.map(record, pickAttr);
    } else {
      return pickAttr(record);
    }
  }
};

QNRoleInfos.findByRoleNameLike = function (roleName) {
  var query = QNRoleInfos.scope().where('RoleName', 'like', `%${roleName}%`);
  return QNRoleInfos.executeByQuery(query);
};

QNRoleInfos.findByBindRoleNameLike = function (roleName, paganation) {
  var query = QNRoleInfos.scope()
    .rightJoin("md_bindrole", "md_bindrole.RoleId", "qn_roleinfo.RoleId")
    .where('qn_roleinfo.RoleName', 'like', `%${QNRoleInfos.escapeLikeStr(roleName)}%`);
  return QNRoleInfos.queryWithPagination(query, paganation)
    .then(QNRoleInfos.getRoleInfosWithServerAndJob)
    .then(QNRoleInfos.Serializers.default);
};

QNRoleInfos.fuzzySearch = function (option) {
  var serverId = option.serverId;
  var jobIds = option.jobIds;
  var levelRangeIds = option.levelRangeIds;
  var paganation = option.paganation;
  var query = this.scope()
    .select(['qn_roleinfo.RoleId', 'RoleName', 'ServerId', 'Level', 'JobId', 'GangId'])
    .join('md_bindrole', 'md_bindrole.RoleId', 'qn_roleinfo.RoleId')
    .where('ServerId', serverId)
    .where('GameId', 8001);
  if (!_.isEmpty(jobIds)) {
    query = query.whereIn('JobId', jobIds);
  }
  query.where(function () {
    var self = this;
    _.forEach(levelRangeIds, function (id, index) {
      var levelRange = QNRoleInfos.LevelRanges.findById(id);
      if (levelRange.start && levelRange.end) {
        self = self.orWhereBetween('Level', [levelRange.start, levelRange.end]);
      } else if (levelRange.start && (!levelRange.end)) {
        self = self.orWhere('Level', '>=', levelRange.start);
      }
    });
  });

  return QNRoleInfos.queryWithPagination(query, paganation)
    .then(QNRoleInfos.getRoleInfosWithServerAndJob)
    .then(QNRoleInfos.Serializers.default);
};

QNRoleInfos.LevelRanges = {
  dataTable: [
    { ID: 0, start: 1, end: 69 },
    { ID: 1, start: 70, end: 89 },
    { ID: 2, start: 90, end: 109 },
    { ID: 3, start: 110, end: 129 },
    { ID: 4, start: 130, end: null },
  ],

  findById: function (id) {
    id = parseInt(id);
    return _.find(this.dataTable, { ID: id });
  },
};

QNEventBus.on("QNRoleScreenshotEvent", function (data) {
  var Games = require('./Games');
  var roleId = data.roleId;
  var url = data.url;
  var BindRoles = require('./BindRoles');
  return BindRoles.getUserIdByGameIdAndRoleId(Games.GameIds.QN, roleId)
    .then(function (userId) {
      if (userId) {
        return PhotoAlbums.addPhotoToQNScreenshot(userId, url);
      }
    });
});

QNEventBus.on("QNRolePhotoSubjectEvent", function (data) {
  var Games = require('./Games');
  var roleId = data.roleId;
  var url = data.url;
  var desc = data.desc;
  var subjectId = data.subjectId;
  var PhotoAlbumSubject = require('../models/PhotoAlbumSubject');
  var Photos = require('../models/Photos');
  var BindRoles = require('./BindRoles');

  return BindRoles.getUserIdByGameIdAndRoleId(Games.GameIds.QN, roleId)
    .then(function (userId) {
      if (userId) {
        PhotoAlbumSubject.getUserSubjectAlbumId(userId, subjectId)
          .then(function (albumId) {
            return Photos.create({ Url: url, PhotoAlbumID: albumId, Desc: desc });
          })
      }
    })
});

QNRoleInfos.getRoleInfo = function (roleId) {
  return QNRoleInfoService.getInfo({ roleid: roleId });
};


QNRoleInfos.syncToDB = function (roleInfos) {
  return Promise.map(roleInfos, function (roleInfo) {
    return QNRoleInfos.exists({ RoleId: roleInfo.RoleId }).then(function (hasRoleInfo) {
      var attrs = ['RoleId', 'ServerId', 'JobId', 'Level', 'RoleName', 'Gender', 'UserName'];
      var props = _.pick(roleInfo, attrs);
      if (hasRoleInfo) {
        var query = QNRoleInfos.scope().where({ RoleId: roleInfo.RoleId }).update(props);
        return QNRoleInfos.executeByQuery(query);
      } else {
        return QNRoleInfos.create(props);
      }
    });
  });
};

let GENDERS = {
  MALE: 0,
  FEMALE: 1
}

QNRoleInfos.isMale = function (roleId) {
  return QNRoleInfos.findOne({ RoleId: roleId }, ['Gender'])
    .then(record => {
      return record && record.Gender === GENDERS.MALE
    })
}