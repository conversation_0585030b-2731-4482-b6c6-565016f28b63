var restify = require('restify'),
    config = require('../common/config'),
    logger = require('../common/logger'),
    util = require('../common/util');

var app = restify.createServer({
    name: 'gdc-bw',
    version: '1.0.0'
});
app.use(restify.acceptParser(app.acceptable));
app.use(restify.queryParser());
app.use(function logger(req, res, next) {
    logger.req(app.name, req);
    next();
});
app.on('uncaughtException', function (req, res, route, err) {
    logger.server(app.name, err);
    try { // 部分请求未等待异步操作完成，可能多次产生多个 uncaughtException
        res.send(500, util.response(err));
    } catch (ex) {
//        logger.error(ex);
    }
});

// http://localhost:1594/public/
app.get(/\/public\/?.*/, restify.serveStatic({
    directory: __dirname + '/../',
    default: 'index.html'
}));

app.listen(1595, function () {
    logger.server(app.name, 'Start listening at %s', app.url);
});
