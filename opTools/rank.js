"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.refreshFlowerRenQiRank = void 0;
const logger_1 = require("../pyq-server/logger");
const rank_1 = require("../service/qnm/pyq/rank");
const logger = logger_1.opLogger.child({ clazz: "opTools/rank" });
function refreshFlowerRenQiRank(serverId) {
    return __awaiter(this, void 0, void 0, function* () {
        const ret = yield (0, rank_1.refreshTopPlayerRanks)({ serverIds: [serverId], orderColumns: ['FlowerRenQi'] });
        logger.info({ ret }, "RefreshFlowerRenqi");
        const newRank = yield (0, rank_1.getRank)({ serverid: serverId, threshold: 1 }, 'FlowerRenQi');
        logger.info({ newRank, serverId }, "FlowerRenqiNewRank");
    });
}
exports.refreshFlowerRenQiRank = refreshFlowerRenQiRank;
//# sourceMappingURL=rank.js.map