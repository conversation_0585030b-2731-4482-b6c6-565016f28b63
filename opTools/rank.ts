import { opLogger } from '../pyq-server/logger'
import { refreshTopPlayerRanks, getRank } from '../service/qnm/pyq/rank'
const logger = opLogger.child({ clazz: "opTools/rank" })

export async function refreshFlowerRenQiRank(serverId: number) {
    const ret = await refreshTopPlayerRanks({ serverIds: [serverId], orderColumns: ['FlowerRenQi'] })
    logger.info({ ret }, "RefreshFlowerRenqi")
    const newRank = await getRank({ serverid: serverId, threshold: 1 }, 'FlowerRenQi')
    logger.info({ newRank, serverId }, "FlowerRenqiNewRank")
}