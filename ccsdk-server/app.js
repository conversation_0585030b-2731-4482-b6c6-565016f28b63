// CC SDK接口服务器
var restify = require('restify'),
    BufferHelper = require('bufferhelper'),
    config = require('../common/config'),
    logger = require('../common/logger'),
    util = require('../common/util');

var app = restify.createServer({
    name: 'ccsdk-server',
    version: '1.0.0'
});
app.use(restify.acceptParser(app.acceptable));
app.use(restify.queryParser());
app.use(restify.bodyParser());
app.use(function (req, res, next) {
    res.charSet('utf-8');
    logger.req(app.name, req);
    // IP校验
    if (config.ccSDKServerList.join(';').indexOf(util.getIp(req)) < 0) {
        res.send(util.response({ code: -2, msg: '非法请求' }));
        return;
    }
    next();
});
app.on('uncaughtException', function (req, res, route, err) {
    logger.server(app.name, err);
    try { // 部分请求未等待异步操作完成，可能多次产生多个 uncaughtException
        res.send(500, util.response(err));
    } catch (ex) {
//        logger.error(ex);
    }
});

// 同步CC视频到游戏空间
app.post('/ccsdk/qnm/addvideo', function(req, res, next) {
    var cc = require('../services/qnm/pyq/cc');
    cc.addVideo(req.params, function () {
        res.send(util.response.apply(null, arguments));
    });
});

var portArg = process.argv.splice(2)[0] || '';
var port = portArg.indexOf('-p') >=0 ? parseInt(portArg.replace('-p', ''), 10) : 3591;
app.listen(port, function () {
    logger.server(app.name, 'Start listening at %s', app.url);
});
