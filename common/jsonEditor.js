/**
 * Created by <PERSON>hen<PERSON> on 2017/2/8.
 */

const _ = require('lodash');

class JsonEditor {
  constructor(obj) {
    this.editObj = obj || {};
  }

  /**
   * 从字符串中构建
   * @param str
   * @return {JsonEditor}
   */
  static fromJsonStr(str) {
    const obj = _.isEmpty(str) ? {} : JSON.parse(str);
    return new JsonEditor(obj)
  }

  /**
   * 设置属性
   * @param path
   * @param value
   * @return {JsonEditor}
   */
  edit(path, value) {
    _.set(this.editObj, path, value);
    return this;
  }

  toStr() {
    return JSON.stringify(this.editObj);
  }

  toObj() {
    return this.editObj;
  }
}

module.exports = JsonEditor;
