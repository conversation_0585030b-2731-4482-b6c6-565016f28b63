import { SendPaoPao2Class } from '@leihuo/paopao-alarm';
import { paopaoCfg } from "../pyq-server/common/config";
import { getRedis } from './redis';
const paopao = SendPaoPao2Class.init(paopaoCfg.url);

export async function sendPaoPao(content: string, receivers: string[]) {
  for (const account of receivers) {
    await paopao.sendPaoPao(content, account);
  }
}


const PAO_PAO_INTERVAL = 5 * 60 * 1000;

function getLastSendTimeKey(type: string) {
  return "popo_alarm_last_send_time:" + type;
}

async function getLastSendTime(type: string) {
  let str = await getRedis().getAsync(getLastSendTimeKey(type));
  return parseInt(str, 10);
}

async function setLastSendTime(type: string, time) {
  let key = getLastSendTimeKey(type);
  let ret = await getRedis().setAsync(key, time);
  await getRedis().expireAsync(key, 24 * 3600);
  return ret;
}

export async function sendPoPo(type: string, msg: string, to?: string[]) {
  let lastSendTime = await getLastSendTime(type);
  let now = Date.now();
  if (now - lastSendTime < PAO_PAO_INTERVAL) {
    // popo 报警CD中
  } else {
    let result = await sendPaoPao(msg, paopaoCfg.alarmReceivers);
    await setLastSendTime(type, Date.now());
    return result;
  }
}