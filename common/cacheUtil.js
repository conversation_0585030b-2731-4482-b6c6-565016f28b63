"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisLock = exports.OperationInterval = exports.RankList = exports.RedisSet = exports.AllowOnceCache = exports.FixOrderSetCache = exports.BasicCache = exports.GenericLazyCache = exports.GenericCache = void 0;
const bluebird = require("bluebird");
const util_1 = require("../common/util");
const redis_1 = require("./redis");
const util2_1 = require("./util2");
const _ = require("lodash");
class GenericCache {
    get(params) {
        return __awaiter(this, void 0, void 0, function* () {
            const str = yield (0, redis_1.getRedis)().getAsync(this.getKey(params));
            if (this.isNeedRefresh(str)) {
                const result = yield this.refresh(params);
                return result;
            }
            else {
                const result = (0, util_1.getJsonInfo)(str);
                return result;
            }
        });
    }
    isNeedRefresh(str) {
        return (0, util2_1.isNullOrUndefined)(str);
    }
    getExpireTime(params) {
        return null;
    }
    refresh(params) {
        return __awaiter(this, void 0, void 0, function* () {
            const content = yield this.fetchDataSource(params);
            yield this.writeToCache(params, content);
            return content;
        });
    }
    clear(params) {
        return __awaiter(this, void 0, void 0, function* () {
            yield (0, redis_1.getRedis)().delAsync(this.getKey(params));
        });
    }
    writeToCache(params, content) {
        return __awaiter(this, void 0, void 0, function* () {
            const str = JSON.stringify(content);
            yield (0, redis_1.getRedis)().setAsync(this.getKey(params), str);
            const expire = this.getExpireTime(params);
            if (expire !== null) {
                yield (0, redis_1.getRedis)().expireAsync(this.getKey(params), expire);
            }
        });
    }
}
exports.GenericCache = GenericCache;
class GenericLazyCache extends GenericCache {
    get(params) {
        return __awaiter(this, void 0, void 0, function* () {
            const str = yield (0, redis_1.getRedis)().getAsync(this.getKey(params));
            if (this.isNeedRefresh(str)) {
                const result = this.getTempResultForCacheMiss();
                yield this.writeToCache(params, result);
                this.refresh(params);
                return result;
            }
            else {
                const result = JSON.parse(str);
                return result;
            }
        });
    }
}
exports.GenericLazyCache = GenericLazyCache;
class BasicCache {
    constructor(key) {
        this.key = key;
    }
    serialize(ele) {
        return JSON.stringify(ele);
    }
    deSerialize(str) {
        return JSON.parse(str);
    }
}
exports.BasicCache = BasicCache;
class FixOrderSetCache extends BasicCache {
    constructor(key, size) {
        super(key);
        this.size = size;
    }
    add(ele) {
        return __awaiter(this, void 0, void 0, function* () {
            const str = this.serialize(ele);
            yield (0, redis_1.getRedis)().zaddAsync(this.key, Date.now(), str);
            yield (0, redis_1.getRedis)().zremrangebyrankAsync(this.key, 0, -(this.size + 1));
        });
    }
    getAll() {
        return __awaiter(this, void 0, void 0, function* () {
            const list = yield (0, redis_1.getRedis)().zrevrangeAsync(this.key, 0, -1);
            return list.map((x) => this.deSerialize(x));
        });
    }
}
exports.FixOrderSetCache = FixOrderSetCache;
class AllowOnceCache {
    constructor(key, expire) {
        this.key = key;
        this.expire = expire;
    }
    isAllow() {
        return __awaiter(this, void 0, void 0, function* () {
            const ret = yield (0, redis_1.getRedis)().setnxAsync(this.key, "" + Date.now() + this.expire * 1000);
            if (ret) {
                yield (0, redis_1.getRedis)().expireAsync(this.key, this.expire);
            }
            return !!ret;
        });
    }
}
exports.AllowOnceCache = AllowOnceCache;
class RedisSet {
    constructor(key, expire) {
        this.key = key;
        this.expire = expire;
    }
    add(member) {
        return __awaiter(this, void 0, void 0, function* () {
            const addRet = yield (0, redis_1.getRedis)().saddAsync(this.key, member);
            const expireRet = yield (0, redis_1.getRedis)().expireAsync(this.key, this.expire);
            return { addRet, expireRet };
        });
    }
    getAll() {
        return __awaiter(this, void 0, void 0, function* () {
            return (0, redis_1.getRedis)().smembersAsync(this.key);
        });
    }
    remove(member) {
        return __awaiter(this, void 0, void 0, function* () {
            return (0, redis_1.getRedis)().sremAsync(this.key, member);
        });
    }
    isMember(member) {
        return __awaiter(this, void 0, void 0, function* () {
            return (0, redis_1.getRedis)().sismemberAsync(this.key, member);
        });
    }
    static create(key, expire) {
        return new this(key, expire);
    }
}
exports.RedisSet = RedisSet;
class RankList {
    constructor(key, expire) {
        this.key = key;
        this.expire = expire;
    }
    add(member, score) {
        return __awaiter(this, void 0, void 0, function* () {
            if (_.isNumber(member)) {
                member = "" + member;
            }
            const addRet = yield (0, redis_1.getRedis)().zaddAsync(this.key, score, member);
            const expireRet = yield (0, redis_1.getRedis)().expireAsync(this.key, this.expire);
            return { addRet, expireRet };
        });
    }
    list(size) {
        return __awaiter(this, void 0, void 0, function* () {
            const items = yield (0, redis_1.getRedis)().zrevrangeAsync(this.key, 0, size - 1, "WITHSCORES");
            const list = [];
            for (let i = 0; i < items.length; i = i + 2) {
                const ele = { member: items[i], score: parseInt(items[i + 1], 10) };
                list.push(ele);
            }
            return list;
        });
    }
    static create(key, expire) {
        return new this(key, expire);
    }
}
exports.RankList = RankList;
class OperationInterval {
    static locked(key, interval = 500) {
        return __awaiter(this, void 0, void 0, function* () {
            const ret = yield (0, redis_1.getRedis)().setAsync(key, "1", redis_1.ExpireType.PX, interval, redis_1.SET_OPTION.NX);
            return !ret;
        });
    }
}
exports.OperationInterval = OperationInterval;
class RedisLock {
    static fullKey(key) {
        return key + ":lock";
    }
    static lock(key, ttl, ownerId) {
        return __awaiter(this, void 0, void 0, function* () {
            const k = this.fullKey(key);
            const ret = yield (0, redis_1.getRedis)().setAsync(k, ownerId, redis_1.ExpireType.PX, ttl, redis_1.SET_OPTION.NX);
            return !!ret;
        });
    }
    static unLock(key, ownerId) {
        return __awaiter(this, void 0, void 0, function* () {
            const k = this.fullKey(key);
            const ret = yield (0, redis_1.getRedis)().getAsync(k);
            if (ret && ret === ownerId) {
                yield (0, redis_1.getRedis)().delAsync(k);
                return true;
            }
            return false;
        });
    }
    static optimistic(key, ownerId, ttl, maxAttempts, wait) {
        return __awaiter(this, void 0, void 0, function* () {
            let attempts = 0;
            // eslint-disable-next-line @typescript-eslint/no-this-alias
            const self = this;
            function tryLock() {
                return __awaiter(this, void 0, void 0, function* () {
                    attempts += 1;
                    const ret = yield self.lock(key, ttl, ownerId);
                    if (ret) {
                        return ret;
                    }
                    else {
                        if (attempts >= maxAttempts) {
                            return false;
                        }
                        else {
                            yield bluebird.delay(wait);
                            const ret = yield tryLock();
                            return ret;
                        }
                    }
                });
            }
            const ret = yield tryLock();
            return ret;
        });
    }
}
exports.RedisLock = RedisLock;
//# sourceMappingURL=cacheUtil.js.map