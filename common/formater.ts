import * as _ from 'lodash'

interface Handler {
    (data: any, option?): any
}

enum TaskType {
    FILL_NORMAL = 1,
    FILL_ONCE = 2,
    PARSE = 3
}

type Result<T, F> = F & { [P in Exclude<keyof T, keyof T & keyof F>]: T[P] }

export class Formater<T, F> {
    private AsyncTaskList: Array<AsyncTask> = []
    private SyncTaskList: Array<any> = []
    private raw: T[]

    constructor(raw: T[]) {
        this.raw = raw
    }

    static create<T, F>(raw: T[]) {
        return new Formater<T, F>(raw)
    }

    fill<Key extends keyof F, U>(key: Key, fetcher: (data: T, option?: { [P in keyof U]: U[P] }) => Promise<F[Key]>, option?: U) {
        this.addTask(key, TaskType.FILL_NORMAL, fetcher, option)
        return this
    }

    fillOnce<Key extends keyof F, U>(key: Key[], fetcher: (data: T[], option?: { [P in keyof U]: U[P] }) => Promise<{ [P in Key]: F[P] }[]>, option?: U) {
        this.addTask(key, TaskType.FILL_ONCE, fetcher, option)
        return this
    }

    format<ArgKey extends keyof T, RtnKey extends keyof F>(key: ArgKey, handler: (data: T[ArgKey]) => F[RtnKey]) {
        this.SyncTaskList.push({ key, handler })
        return this
    }

    private addTask<Key extends keyof F>(key: Key | Key[], type: TaskType, dataFetcher: Handler, option) {
        let task
        if (type === TaskType.FILL_NORMAL)
            task = new FillTask(key as string, dataFetcher, option)

        if (type === TaskType.FILL_ONCE)
            task = new FillOnceTask(key as string[], dataFetcher, option)

        this.AsyncTaskList.push(task)
        return this
    }

    async execute(): Promise<Result<T, F>[]> {
        let raw = this.raw
        let promiseList = this.AsyncTaskList.map(async task => {
            return task.execute(raw)
        })
        await Promise.all(promiseList)
        let data: Result<T, F>[] = _.map(raw, item => {
            let obj: F = {} as F
            let result: Result<T, F> = _.assign(obj, item)
            this.SyncTaskList.forEach(task => {
                result[task.key] = task.handler(item[task.key])
            })
            return result
        })
        return data
    }
}

abstract class AsyncTask {
    protected abstract key
    protected abstract dataFetcher: Handler

    abstract execute(raw: any[]): Promise<any>
}

class FillTask extends AsyncTask {
    protected key: string
    protected dataFetcher: Handler
    protected option

    constructor(key: string, dataFecher: Handler, option) {
        super()
        this.key = key
        this.dataFetcher = dataFecher
        this.option = option
    }

    async execute(raw: any[]): Promise<any> {
        let key = this.key
        let promiseList = raw.map(async item => {
            return this.dataFetcher(item, this.option)
        })

        let data = await Promise.all(promiseList)
        for (let i = 0; i < data.length; i++) {
            raw[i][key] = data[i]
        }
    }
}

class FillOnceTask extends AsyncTask {
    protected key: string[]
    protected dataFetcher: Handler
    protected option

    constructor(key: string[], dataFecher: Handler, option) {
        super()
        this.key = key
        this.dataFetcher = dataFecher
        this.option = option;
    }

    async execute(raw: any[]): Promise<any> {
        let key = this.key
        let data = await this.dataFetcher(raw, this.option)

        for (let i = 0; i < data.length; i++) {
            let dataItem = data[i]
            let resultItem = raw[i]

            for (let j in key) {
                let keyName: string = key[j]
                if (dataItem[keyName] !== undefined)
                    resultItem[keyName] = dataItem[keyName]
                else
                    resultItem[keyName] = null
            }
        }
    }
}
