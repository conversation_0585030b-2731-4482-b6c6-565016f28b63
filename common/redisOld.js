"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.zrevrangebyscore = exports.zrevrange = exports.zincrby = exports.zscore = exports.setHMKV = exports.getHAKV = exports.delHKV = exports.setHKV = exports.getHKV = exports.remSKV = exports.addSKV = exports.getSKV = exports.setKV = exports.getKV = exports.delKey = exports.getKeys = exports.expireK = exports.existKey = void 0;
const _ = require("lodash");
const redisNew_1 = require("./redisNew");
function existKey(key, option) {
    return exec('exists', [key], option);
}
exports.existKey = existKey;
function expireK(key, seconds, option) {
    return exec('expire', [key, seconds], option);
}
exports.expireK = expireK;
function getKeys(pattern, option) {
    return exec('keys', [pattern + '*'], option);
}
exports.getKeys = getKeys;
function delKey(key, option) {
    return exec('del', [key], option);
}
exports.delKey = delKey;
// string
function getKV(key, option) {
    return exec('get', [key], option);
}
exports.getKV = getKV;
function setKV(key, val, option) {
    return exec('set', [key, val], option);
}
exports.setKV = setKV;
// set
function getSKV(key, option) {
    return exec('smembers', [key], option);
}
exports.getSKV = getSKV;
function addSKV(key, list, option) {
    list.unshift(key);
    return exec('sadd', list, option);
}
exports.addSKV = addSKV;
function remSKV(key, list, option) {
    list.unshift(key);
    return exec('srem', list, option);
}
exports.remSKV = remSKV;
// hash
function getHKV(key, field, option) {
    return exec('hget', [key, field], option);
}
exports.getHKV = getHKV;
function setHKV(key, field, val, option) {
    return exec('hset', [key, field, val], option);
}
exports.setHKV = setHKV;
function delHKV(key, field, option) {
    return exec('hdel', [key, field], option);
}
exports.delHKV = delHKV;
function getHAKV(key, option) {
    return exec('hgetall', [key], option);
}
exports.getHAKV = getHAKV;
function setHMKV(key, item, option) {
    return exec('hmset', [key, item], option);
}
exports.setHMKV = setHMKV;
// sorted set
function zscore(key, member, option) {
    return exec('zscore', [key, member], option);
}
exports.zscore = zscore;
function zincrby(key, increment, member, option) {
    return exec('zincrby', [key, increment, member], option);
}
exports.zincrby = zincrby;
function zrevrange(key, start, stop, option) {
    return exec('zrevrange', [key, start, stop, 'withscores'], option);
}
exports.zrevrange = zrevrange;
function zrevrangebyscore(key, max, min, count, option) {
    return exec('zrevrangebyscore', [key, max || '+inf', min, 'withscores', 'LIMIT', 0, count], option);
}
exports.zrevrangebyscore = zrevrangebyscore;
function exec(op, args, option) {
    const redisClient = (0, redisNew_1.getRedis)();
    option = _.defaults({ client: redisClient }, option);
    const client = option.client;
    return new Promise((resolve, reject) => {
        args.push(function (err, result) {
            if (err) {
                reject(err);
            }
            else {
                resolve(result);
            }
        });
        client[op].apply(client, args);
    });
}
//# sourceMappingURL=redisOld.js.map