import { RedisClient } from 'redis';

export enum GeoUnit {
  ml = 'ml',
  km = 'km',
  ft = 'ft',
  mi = 'mi'
}

export enum ExpireType {
  EX = 'EX',
  PX = 'PX'
}

export enum IExistResult {
  Exist = 1,
  NotExist = 0
}

type RedisBound = '+inf' | '-inf'


export enum SET_OPTION {
  NX = "NX", // Only set the key if it does not already exist.
  XX = "XX",  // Only set the key if it already exist.
}



export interface ExtendRedisClient extends RedisClient {
  lremAsync: (key: string, member: string) => Promise<number>
  keysAsync: (pattern: string) => Promise<string[]>
  expireAsync: (key: string, seconds: number) => Promise<number>
  expireatAsync: (key: string, ts: number) => Promise<number>
  geoaddAsync: (key: string, lng: number, lat: number, member: string | number) => Promise<string>
  georadiusAsync: (key: string, lng: number, lat: number, radius: number, unit: GeoUnit, ...rest) => Promise<any>

  hmsetAsync: (key: string, kvs: { k: string, v: string }[]) => Promise<string>
  hmgetAsync: (key: string, ...fields: (string | number)[]) => Promise<string[]>
  hsetAsync: (key: string, k: string, v: string | number) => Promise<string>
  hgetAsync: (key: string, k: string) => Promise<string>
  hdelAsync: (key: string, k: string) => Promise<string>
  hgetallAsync: (key: string) => Promise<{ [key: string]: string }>
  hincrbyAsync: (key: string, m: string, incr: number) => Promise<number>
  hexistsAsync: (key: string, l: string | number) => Promise<IExistResult>
  hsetnxAsync: (key: string, k: string, v: string | number) => Promise<number>

  lpopAsync: (key: string) => Promise<string>
  rpushAsync: (key: string, ele: string) => Promise<string>
  lrangeAsync: (key: string, start: number, end: number) => Promise<string[]>
  llenAsync: (key: string) => Promise<number>

  getAsync: (key: string) => Promise<string>
  setAsync: (key: string, content: string | number, expireType?: ExpireType, expire?: number, set_option?: SET_OPTION) => Promise<string>
  setnxAsync: (key: string, value: string) => Promise<boolean>
  delAsync: (key: string) => Promise<string>
  quitAsync: () => Promise<string>

  incrAsync: (key: string) => Promise<number>
  decrAsync: (key: string) => Promise<number>
  existsAsync: (key: string) => Promise<IExistResult>

  scardAsync: (ley: string) => Promise<number>
  saddAsync: (ley: string, ele: string | number) => Promise<number>
  sremAsync: (ley: string, ele: string | number) => Promise<number>
  sismemberAsync: (key: string, ele: string | number) => Promise<number>
  smembersAsync: (key: string) => Promise<string[]>
  zrevrangebyscoreAsync: (key: string, start: number | RedisBound, end: number | RedisBound, withScore?: string, limit?: string, offset?: number, count?: number) => Promise<string[]>
  ttlAsync: (key: string) => Promise<number>
  lpushAsync: (key: string, ele: string) => Promise<string>
  ltrimAsync: (key: string, start: number, end: number) => Promise<number>
  setexAsync: (key: string, seconds: number, value: string) => Promise<string>
  incrbyAsync: (key: string, increment: number) => Promise<number>

  zaddAsync: (key: string, score: number, member: string | number) => Promise<string>
  zremAsync: (key: string, member: string | number) => Promise<string>
  zrevrangeAsync: (key: string, start: number, end: number, withScore?: string) => Promise<string[]>
  zincrbyAsync: (key: string, increment: number, member: string | number) => Promise<string>
  zscoreAsync: (key: string, member: string) => Promise<string>
  zremrangebyrankAsync: (key: string, start: number, stop: number) => Promise<number>
  zcardAsync: (key: string) => Promise<number>
  flushallAsync: () => Promise<number>
}
