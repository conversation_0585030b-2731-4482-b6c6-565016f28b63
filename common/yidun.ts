const _ = require('lodash')
const Request = require('./request.js')
const Config = require('./config.js')
import { hexMd5 } from '../common/util'
import { apiLogger } from '../audit-server/logger';

function genSignature(secretKey, params) {
    let str = ''
    let keys = _.keys(params).sort()
    _.each(keys, (k) => {
        str += k + (params[k] || '')
    })
    str += secretKey

    return hexMd5(str).toLowerCase()
}

export enum Action {
    Normal = 0,
    Suspicion = 10,
    Deadly = 20
}

export async function loginCheck(option: { token: string, username: string, ip: string, password: string }): Promise<boolean> {
    let cfg = Config.YI_DUN_ANTI_SPAM
    if (!cfg.enable) {
        return true
    }
    let pwdHash = hexMd5(option.password + cfg.PWD_SALT)
    let extData = JSON.stringify({ password: pwdHash })
    let data = {
        version: '200',
        secretId: cfg.SECRET_ID,
        businessId: cfg.BUSINESS_ID,
        timestamp: (Math.round(Date.now() / 1000) + ''),
        nonce: Math.random() + '',
        token: option.token || '',
        account: option.username,
        email: option.username,
        ip: option.ip,
        extData: extData
    };
    //@ts-ignore
    data.signature = genSignature(cfg.SECRET_KEY, data)

    return Request.request({
        url: cfg.CHECK_URL,
        method: 'POST',
        form: data,
        timeout: 30000
    }).then(function (res) {
        let code = res.code
        let msg = res.msg
        if (code === 200) {
            let result = res.result || {}
            let action = result.action
            if (action === Action.Normal) {
                return true
            } else if (action === Action.Suspicion) {
                return true
            } else if (action === Action.Deadly) {
                apiLogger.error('YD_CHECK_Deadly', { res: res, option: option })
                return false
            }
        } else {
            apiLogger.error('YD_CHECK_API_FAIL', { code: code, msg: msg, option: option })
            return false
        }
    }).catch(function (err) {
        apiLogger.error('YD_CHECK_TIMEOUT', { err: err, option: option })
        return true
    })
}