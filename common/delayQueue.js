"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DelayQueue = void 0;
const redis_1 = require("./redis");
const util2_1 = require("./util2");
class JobPool {
    constructor(keyName) {
        this.keyName = keyName;
    }
    addJob(job) {
        return __awaiter(this, void 0, void 0, function* () {
            let redis = (0, redis_1.getRedis)();
            let jobDetail = JSON.stringify(job);
            yield redis.hsetAsync(this.keyName, '' + job.id, jobDetail);
            return job.id;
        });
    }
    getJobById(jobId) {
        return __awaiter(this, void 0, void 0, function* () {
            let redis = (0, redis_1.getRedis)();
            let jobDetail = yield redis.hgetAsync(this.keyName, jobId);
            let job = JSON.parse(jobDetail);
            return job;
        });
    }
    removeJob(jobId) {
        return __awaiter(this, void 0, void 0, function* () {
            let redis = (0, redis_1.getRedis)();
            return redis.hdelAsync(this.keyName, jobId);
        });
    }
}
class DelayBucket {
    constructor(keyName) {
        this.keyName = keyName;
    }
    addJob(job) {
        return __awaiter(this, void 0, void 0, function* () {
            let redis = (0, redis_1.getRedis)();
            let result = yield redis.zaddAsync(this.keyName, job.runAt, job.id);
            return result;
        });
    }
    getTriggerJobIds(now) {
        return __awaiter(this, void 0, void 0, function* () {
            let redis = (0, redis_1.getRedis)();
            //@ts-ignore
            let jobIds = yield redis.zrangebyscoreAsync(this.keyName, 0, now, 'LIMIT', 0, 1);
            return jobIds;
        });
    }
    removeJob(jobId) {
        return __awaiter(this, void 0, void 0, function* () {
            let redis = (0, redis_1.getRedis)();
            let result = redis.zremAsync(this.keyName, jobId);
            return result;
        });
    }
}
class ReadyQueue {
    constructor(keyName) {
        this.keyName = keyName;
    }
    add(jobId) {
        return __awaiter(this, void 0, void 0, function* () {
            let redis = (0, redis_1.getRedis)();
            let result = yield redis.lpushAsync(this.keyName, jobId);
            return result;
        });
    }
    pop() {
        return __awaiter(this, void 0, void 0, function* () {
            let redis = (0, redis_1.getRedis)();
            let result = yield redis.lpopAsync(this.keyName);
            return result;
        });
    }
}
class DelayQueue {
    constructor(keyName) {
        this.jobPool = new JobPool(keyName + '_job_pool');
        this.delayBucket = new DelayBucket(keyName + '_delay_bucket');
        this.readyQueue = new ReadyQueue(keyName + '_ready_queue');
    }
    addJob(job) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!job.id) {
                job.id = (0, util2_1.uuidV1)();
            }
            yield this.jobPool.addJob(job);
            yield this.delayBucket.addJob(job);
            return job.id;
        });
    }
    onTick() {
        return __awaiter(this, void 0, void 0, function* () {
            let now = Date.now();
            let jobIds = yield this.delayBucket.getTriggerJobIds(now);
            for (let id of jobIds) {
                yield this.readyQueue.add(id);
                yield this.delayBucket.removeJob(id);
            }
        });
    }
    popReady() {
        return __awaiter(this, void 0, void 0, function* () {
            let jobId = yield this.readyQueue.pop();
            if (jobId) {
                let job = this.jobPool.getJobById(jobId);
                this.jobPool.removeJob(jobId);
                return job;
            }
        });
    }
}
exports.DelayQueue = DelayQueue;
//# sourceMappingURL=delayQueue.js.map