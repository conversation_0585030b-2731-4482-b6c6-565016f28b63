import * as _ from 'lodash'
import { getRedis } from './redisNew';

export function existKey(key, option?) {
    return exec('exists', [key], option);
}

export function expireK(key, seconds, option?) {
    return exec('expire', [key, seconds], option);
}

export function getKeys(pattern, option?) {
    return exec('keys', [pattern + '*'], option);
}

export function delKey(key, option?) {
    return exec('del', [key], option);
}

// string
export function getKV(key, option?) {
    return exec('get', [key], option);
}
export function setKV(key, val, option?) {
    return exec('set', [key, val], option);
}

// set
export function getSKV(key, option?) {
    return exec('smembers', [key], option);
}
export function addSKV(key, list, option?) {
    list.unshift(key);
    return exec('sadd', list, option);
}
export function remSKV(key, list, option?) {
    list.unshift(key);
    return exec('srem', list, option);
}

// hash
export function getHKV(key, field, option?) {
    return exec('hget', [key, field], option) as Promise<number>;
}
export function setHKV(key, field, val, option?) {
    return exec('hset', [key, field, val], option);
}
export function delHKV(key, field, option?) {
    return exec('hdel', [key, field], option);
}

export function getHAKV(key, option?) {
    return exec('hgetall', [key], option);
}

export function setHMKV(key, item, option?) {
    return exec('hmset', [key, item], option);
}

// sorted set
export function zscore(key, member, option?) {
    return exec('zscore', [key, member], option);
}
export function zincrby(key, increment, member, option?) {
    return exec('zincrby', [key, increment, member], option);
}
export function zrevrange(key, start, stop, option?) {
    return exec('zrevrange', [key, start, stop, 'withscores'], option);
}
export function zrevrangebyscore(key, max, min, count, option?) {
    return exec('zrevrangebyscore', [key, max || '+inf', min, 'withscores', 'LIMIT', 0, count], option);
}


function exec(op: string, args, option?) {
    const redisClient = getRedis()
    option = _.defaults({ client: redisClient }, option);
    const client = option.client;
    return new Promise((resolve, reject) => {
        args.push(function (err, result) {
            if (err) {
                reject(err);
            } else {
                resolve(result)
            }
        });
        client[op].apply(client, args)
    });
}