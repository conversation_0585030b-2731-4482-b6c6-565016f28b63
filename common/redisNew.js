"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getGeoRedis = exports.redisClient = exports.getRedis = void 0;
/* eslint-disable prefer-rest-params */
/* eslint-disable @typescript-eslint/no-this-alias */
const bluebird = require("bluebird");
const _ = require("lodash");
const redis = require("redis");
const config = require("./config");
const logger2_1 = require("./logger2");
const logger = (0, logger2_1.getLogger)("redis_logger");
const redisCfg = config.REDIS_CFG;
const geoRedisCfg = config.GEO_REDIS_CFG || redisCfg;
bluebird.promisifyAll(redis.RedisClient.prototype, {
    promisifier: promisifier,
});
bluebird.promisifyAll(redis.Multi.prototype, {
    promisifier: promisifier,
});
function promisifier(originalMethod) {
    return function promisified() {
        const args = [].slice.call(arguments);
        const self = this;
        return new Promise(function (resolve, reject) {
            args.push(function (err, data) {
                if (err) {
                    reject(err);
                }
                else {
                    resolve(data);
                }
            });
            originalMethod.apply(self, args);
        }).catch((err) => {
            const debugArgs = args.filter((ele) => !_.isFunction(ele));
            logger.error("RedisCommendError", { method: originalMethod.name, args: debugArgs });
            throw err;
        });
    };
}
function getRedisClient(config) {
    const client = redis.createClient(config);
    return client;
}
class RedisClass {
    constructor(config) {
        this.instance = null;
        this.redisConfig = config;
    }
    retry_strategy() {
        this.instance = null;
    }
    getInstance() {
        const redisConfig = _.assign({}, this.redisConfig, { retry_strategy: this.retry_strategy.bind(this) });
        if (this.instance === null) {
            this.instance = getRedisClient(redisConfig);
        }
        return this.instance;
    }
}
class RedisPool {
    constructor(config) {
        this.pool = [];
        const hosts = config.hosts || [config.host];
        for (const h of hosts) {
            const cfg = Object.assign({}, config, { host: h });
            const rc = new RedisClass(cfg);
            this.pool.push(rc);
        }
    }
    getInstance() {
        const idx = _.random(0, this.pool.length - 1);
        return this.pool[idx].getInstance();
    }
}
const redisPool = new RedisPool(redisCfg);
function getRedis() {
    return redisPool.getInstance();
}
exports.getRedis = getRedis;
exports.redisClient = getRedis();
const GeoRedisPool = new RedisPool(geoRedisCfg);
function getGeoRedis() {
    return GeoRedisPool.getInstance();
}
exports.getGeoRedis = getGeoRedis;
exports.redisClient.on("error", function (err) {
    logger.error({ err }, "RedisClientError");
});
if (config.testCfg.redis_debug) {
    const monitorClient = exports.redisClient.duplicate();
    monitorClient.monitor(function (err) {
        if (err) {
            logger.error(err);
        }
        else {
            logger.info("RedisClient: Entering monitoring mode.");
        }
    });
    monitorClient.on("monitor", function (time, args, rawReply) {
        const commend = _.upperCase(args[0]);
        const commendArgs = _.tail(args)
            .filter((r) => {
            return !_.isFunction(r);
        })
            .join(" ");
        if (commend !== "PING") {
            logger.info("RedisClient" + ": " + commend + " " + commendArgs);
        }
    });
}
//# sourceMappingURL=redisNew.js.map