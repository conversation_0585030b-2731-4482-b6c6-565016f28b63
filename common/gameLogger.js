"use strict";
/***
雷火游戏JSON日志规范
https://confluence.leihuo.netease.com/pages/viewpage.action?pageId=15624022
***/
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameLogger = void 0;
const fs = require("fs");
const path = require("path");
const DateUtil = require("./dateUtil");
const util_1 = require("./util");
const LOG_TIME_FORMAT = 'yyyy-MM-dd HH:mm:ss';
//  lineProtocol= [logtime][ operation],{key1=value1,key2=value2,key3=value3}
// [ 2013-04-11 00:00:09 ][OnlineRoleNum],{ “ time ” :12344343, ”username””:” yemingjiang ” }
function formatLineProtocol(params) {
    let logTime = DateUtil.format(params.logTime, LOG_TIME_FORMAT);
    let recordJson = JSON.stringify(params.data);
    return `[${logTime}][${params.operation}],${recordJson}\n`;
}
function writeLog(filePath, line) {
    fs.appendFile(filePath, line, function (err) {
        if (err) {
            console.error(err);
        }
    });
}
class GameLogger {
    constructor(option) {
        this.logName = option.logName;
        this.printInConsole = option.printInConsole || false;
        if (option.createLogDir) {
            (0, util_1.mkDirsSync)(this.getLogDir(), '');
        }
    }
    info(operation, data) {
        let lineStr = formatLineProtocol({ logTime: new Date(), operation: operation, data: data });
        let fullLogName = this.formatLogName(this.logName);
        let logPath = path.join(this.getLogDir(), fullLogName);
        if (this.printInConsole) {
            console.info(lineStr);
        }
        return writeLog(logPath, lineStr);
    }
    formatLogName(name) {
        let dateStr = DateUtil.format(new Date(), 'yyyyMMdd');
        return `${name}_${dateStr}_utf8.log`;
    }
}
exports.GameLogger = GameLogger;
//# sourceMappingURL=gameLogger.js.map