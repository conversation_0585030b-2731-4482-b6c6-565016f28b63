﻿/* eslint-disable no-var */
/* eslint-disable @typescript-eslint/no-var-requires */
// NOS直传服务
const crypto = require("crypto"),
  BufferHelper = require("bufferhelper"),
  fs = require("fs"),
  path = require("path"),
  db = require("./db"),
  util = require("./util"),
  config = require("./config"),
  cfg = config.NOS_CFG;

import { commonLogger } from "./commonLogger";
import { isValidMediaUrl } from "./mediaUtil";

const logger = commonLogger.child({ clazz: "/common/nos" });

const _ = require("lodash");
const uuid = require("node-uuid");

export function getObjUrl(bucketName: string, objectName?: string) {
  const bucket = bucketName;
  const nosHost = "$(Bucket).nosdn.127.net"; // 启用CDN加速路径
  const url = "http://" + nosHost + "/$(Object)";
  return objectName ? url.replace("$(Bucket)", bucket).replace("$(Object)", objectName) : url;
}

export interface INosToken {
  token: string;
  bucketname: string;
  objectname: string;
  prefix: string;
  expires: number;
}

interface IParams {
  type?: string;
  extname?: string;
  picid?: string;
  roleid?: string;
  objectName?: string;
}

export function getToken2(bucket: string, objectName: string) {
  return getTokenInner({
    bucketName: bucket,
    objectName,
    callbackUrl: "",
    product: "",
  });
}

interface IGetTokenInner {
  bucketName: string;
  objectName: string;
  callbackUrl: string;
  product: string;
}

function getTokenInner(params: IGetTokenInner) {
  const { product, bucketName, objectName, callbackUrl } = params;
  const objUrl = getObjUrl(bucketName);
  const expires = Math.floor(Date.now() / 1000) + 600; // 十分钟过期
  const putPolicy = {
    Bucket: bucketName,
    Object: objectName,
    Expires: expires,
    ReturnBody: '{"code":0,"data":{"bucketname":"$(Bucket)","url":"' + objUrl + '"}}',
    CallbackUrl: "",
    CallbackBody: "",
  };
  if (config.testCfg.nos_callback_on) {
    // 默认不开启NOS回调
    putPolicy.CallbackUrl = callbackUrl
      ? cfg.callback.replace("/#{product}/nos/callback", callbackUrl)
      : cfg.callback.replace("#{product}", product);
    putPolicy.CallbackBody = JSON.stringify(
      util.extend({ bucketname: "$(Bucket)", objectname: "$(Object)", url: objUrl }, params)
    ); //'$(Bucket)&$(Object)&$(FileName)&$(ImageInfo.Width)&$(ImageInfo.Height)';
  }

  const encodedPutPolicy = Buffer.from(JSON.stringify(putPolicy)).toString("base64");
  const encodedSign = crypto.createHmac("sha256", cfg.secretKey).update(encodedPutPolicy).digest("base64");
  const token = "UPLOAD " + cfg.accessKey + ":" + encodedSign + ":" + encodedPutPolicy;

  return {
    token: token,
    bucketname: bucketName,
    objectname: objectName,
    expires: expires,
    prefix: objUrl.replace("$(Object)", "").replace("$(Bucket)", bucketName),
  };
}

export function getToken(product: string, params: IParams, callbackUrl?): INosToken {
  const info = genUploadInfo(params),
    objectName = info.path,
    bucketName = cfg.bucketName[product];

  return getTokenInner({ product: product, bucketName, objectName, callbackUrl });
}

// 录音需要裁剪路径
function genShortUploadInfoPath() {
  const name = uuid.v1().replace(/-/g, "");
  return { path: name, dir: "", name: name };
}

const ShortPathNosTypeSet = new Set(["tape", "jlaudio"]);
const AllTypeSet = new Set([
  "photo",
  "avatar",
  "moment",
  "video",
  "tape",
  "screenshot",
  "cjbpic",
  "cjbsharepic",
  "pyqchat",
  "jlaudio",
  "fireworks",
  "report_log",
]);

function genUploadPath(type) {
  if (ShortPathNosTypeSet.has(type)) {
    return genShortUploadInfoPath();
  } else {
    const info = util.genUploadPath();
    return info;
  }
}

export function genUploadInfo(params: IParams) {
  let dir = "";
  let name = "";
  let type = params.type;
  const extName = params.extname;

  if (params.objectName) {
    name = params.objectName;
  } else if (type === "role" || type === "child" || type === "fengshui" || type === "land") {
    // 角色形象等图片直接覆盖
    name = type === "child" ? params.picid : params.roleid;
    const md5 = util.hexMd5(name + config.DIGEST_KEY);
    dir = type + "/" + md5.substr(0, 2) + "/" + md5.substr(30, 2);
  } else {
    const info = genUploadPath(type);
    name = info.name;
    type = AllTypeSet.has(type) ? type : "upload";
    if (info.dir) {
      dir = type + "/" + info.dir;
    } else {
      dir = type;
    }
  }
  if (extName) {
    name = name + "." + _.trim(extName);
  }

  return { path: dir + "/" + name, dir: dir, name: name };
}

export function callback(product, req, callback) {
  const bufferHelper = new BufferHelper();
  req.on("data", function (chunk) {
    bufferHelper.concat(chunk);
  });
  req.on("end", function () {
    const buffer = bufferHelper.toBuffer(),
      body = buffer.toString(),
      url = cfg.callback.replace("/#{product}/nos/callback", req.route.path),
      sign = "NOS:" + new Buffer(url + "\n" + body, "utf-8").toString("base64"),
      encodedSign = crypto.createHmac("sha256", cfg.secretKey).update(sign).digest("base64"),
      authStr = "NOS " + cfg.accessKey + ":" + encodedSign;

    const isDEBUG = process.env.NODE_ENV === "DEBUG";
    if (authStr !== req.headers.authorization && !isDEBUG) {
      logger.debug(`nos_callback:body:${body}`);
      logger.debug(`headers.authorization:${req.headers.authorization}, authStr:${authStr}`);
      callback({ code: -1, msg: "Authorize_Fail" });
      return;
    }
    try {
      var info = JSON.parse(body);
    } catch (ex) {
      callback({ code: -2, msg: "Bad_Response" });
      return;
    }

    const fileUrl = info.url,
      args = callback(info);
    !(args && args.ignore) && addFile(fileUrl, args); // 添加文件生成记录
  });
}
function addFile(file, args) {
  const values = {
    Url: file,
    CreateTime: Date.now(),
    Status: 0,
    UseType: 0,
    TargetId: 0,
  };
  if (args && args.usage) {
    values.Status = 1;
    values.UseType = args.usage;
    values.TargetId = args.target;
  }

  return db
    .add({
      table: config.TABLE_CFG.nosFile,
      values: values,
      ignore: true,
    })
    .then(function (result) {
      return result;
    });
}

export function markFileUsage(files, targetId, usage) {
  files = typeof files === "string" ? [files] : files;
  return db
    .update({
      table: config.TABLE_CFG.nosFile,
      values: { Status: 1, UseType: usage, TargetId: targetId, BindTime: Date.now() },
      filter: { Url: files },
    })
    .then(function (result) {
      return result.affectedRows;
    })
    .catch((err) => {
      logger.error({ err, files, targetId, usage }, "makeFileUsageFunction");
    });
}

export function clearFileUsage(files) {
  files = typeof files === "string" ? [files] : files;
  return db
    .update({
      table: config.TABLE_CFG.nosFile,
      values: { Status: -1 },
      filter: { Url: files },
    })
    .then(function (result) {
      return result.affectedRows;
    });
}

export function getObjPath(product, objectName) {
  const pathRoot = cfg.nosImgPath + "/" + cfg.bucketName[product];
  return pathRoot + "/" + objectName;
}

export function validateUrlForNos(url) {
  const isPass = /^https?:\/\/.*(nosdn\.127\.net|nos\.netease\.com)/.test(url);
  return isPass;
}

// 保持旧代码的兼容
export function validateUrl(url) {
  return isValidMediaUrl(url);
}

export function moveToTrash(url) {
  const info = getBucket(url),
    bucketName = info.bucketName,
    objectName = info.objectName,
    srcRoot = cfg.nosImgPath + "/" + bucketName,
    srcPath = srcRoot + "/" + objectName,
    recycleRoot = cfg.nosImgPath + "/" + cfg.bucketName["recycle"],
    recyclePath = recycleRoot + "/" + bucketName + "/" + objectName;

  const exist = fs.existsSync(srcPath);
  if (!exist) {
    config.testCfg.debug && console.log("File not exist!");
    return;
  }

  util.mkDirsSync(recycleRoot, path.dirname(recyclePath.replace(recycleRoot, "")));
  util.mvFile(srcPath, recyclePath); // 移动文件
}

export function restoreFromTrash(url) {
  const info = getBucket(url),
    bucketName = info.bucketName,
    objectName = info.objectName,
    srcRoot = cfg.nosImgPath + "/" + bucketName,
    srcPath = srcRoot + "/" + objectName,
    recycleRoot = cfg.nosImgPath + "/" + cfg.bucketName["recycle"],
    recyclePath = recycleRoot + "/" + bucketName + "/" + objectName;

  const exist = fs.existsSync(recyclePath);
  if (!exist) {
    config.testCfg.debug && console.log("File not exist!");
    return;
  }

  util.mkDirsSync(srcRoot, path.dirname(srcPath.replace(srcRoot, "")));
  util.mvFile(recyclePath, srcPath); // 移动文件
}

function getBucket(url) {
  let bucketName, objectName;
  url = url.replace(
    /^http:\/\/(.*)(\.nosdn\.127\.net|nos\.netease\.com)\/(.+)$/,
    function (match, bucket, domain, obj) {
      if (bucket) {
        bucketName = bucket;
        objectName = obj;
      }
      return bucket ? "" : obj;
    }
  );
  bucketName ||
    url.replace(/(.+?)\/(.*)$/, function (match, bucket, obj) {
      bucketName = bucket;
      objectName = obj;
      return "";
    });

  return {
    bucketName: bucketName,
    objectName: objectName,
  };
}

export function genUniqObjectPath() {
  return uuid().replace(/-/g, "");
}

export function getNosPrefix(bucketName: string, enableCDN = true) {
  if (enableCDN) {
    return `https://${bucketName}.nosdn.127.net/`;
  } else {
    return "https://nos.netease.com/" + bucketName + "/";
  }
}

export interface IPutPolicy {
  Bucket: string;
  Region: string;
  Object: string;
  Expires: number;
  CallbackUrl?: string;
  CallbackBody?: string;
  ReturnUrl?: string;
  ReturnBody?: string;
}

type Region = "HZ" | "JD";

export interface INosConfig {
  bucketName: string;
  enableCDN?: boolean;
  region: string;
  accessKey: string;
  secretKey: string;
  tokenExpire: number;
}

export function genUploadToken(nosConfig: INosConfig, putPolicy: IPutPolicy) {
  putPolicy.Region = nosConfig.region;
  const putPolicyStr = JSON.stringify(putPolicy);
  const encodedPutPolicy = Buffer.from(putPolicyStr).toString("base64");
  const encodedSign = crypto.createHmac("sha256", nosConfig.secretKey).update(encodedPutPolicy).digest("base64");
  const token = "UPLOAD " + nosConfig.accessKey + ":" + encodedSign + ":" + encodedPutPolicy;
  return token;
}
