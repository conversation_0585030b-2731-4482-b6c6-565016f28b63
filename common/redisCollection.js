"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FixedStringList = exports.Queue = void 0;
const bluebird = require("bluebird");
const _ = require("lodash");
const config_1 = require("./config");
const redis_1 = require("./redis");
const redisTypes_1 = require("./redisTypes");
const util_1 = require("./util");
class Queue {
    constructor(options) {
        this.key = options.key;
        this.md5Prefix = options.md5Prefix || 'md5_key:';
    }
    push(obj) {
        const self = this;
        const str = JSON.stringify(obj);
        const contentMd5 = (0, util_1.hexMd5)(str);
        const reHashKey = this.md5Prefix + contentMd5;
        return (0, redis_1.getRedis)().rpushAsync(self.key, reHashKey).then(len => {
            return (0, redis_1.getRedis)().setAsync(self.getReHashFullKey(reHashKey), str, redisTypes_1.ExpireType.EX, 7 * config_1.ONE_DAY_SECONDS);
        });
    }
    isReHashKey(str) {
        return _.isString(str) && str.startsWith(this.md5Prefix);
    }
    getReHashFullKey(reHashKey) {
        return this.key + ':' + reHashKey;
    }
    len() {
        return (0, redis_1.getRedis)().llenAsync(this.key);
    }
    getThenDelete(key) {
        let content = null;
        return (0, redis_1.getRedis)().getAsync(key)
            .then(s => {
            content = s;
            return (0, redis_1.getRedis)().delAsync(key);
        }).then(() => {
            return content;
        });
    }
    pop() {
        let self = this;
        return (0, redis_1.getRedis)().lpopAsync(this.key)
            .then(ele => {
            if (self.isReHashKey(ele)) {
                return self.getThenDelete(self.getReHashFullKey(ele));
            }
            else {
                return ele;
            }
        }).then(raw => {
            if (raw) {
                return JSON.parse(raw);
            }
            else {
                return raw;
            }
        });
    }
    popx(n) {
        let self = this;
        return self.len().then(length => {
            let len = Math.min(n, length);
            return bluebird.map(new Array(len), () => {
                return self.pop();
            }, { concurrency: 5 });
        }).then(list => {
            return _.filter(list, ele => !!ele); // pop 过程中可能被其他进程消费导致 pop 出空元素
        });
    }
}
exports.Queue = Queue;
// 固定列表长度字符串列表
class FixedStringList {
    constructor(options) {
        this.key = options.key;
        this.size = options.size;
    }
    push(str) {
        const self = this;
        return (0, redis_1.getRedis)().lpushAsync(this.key, str)
            .then(() => {
            (0, redis_1.getRedis)().ltrimAsync(self.key, 0, self.size - 1);
        });
    }
    getAll() {
        return (0, redis_1.getRedis)().lrangeAsync(this.key, 0, this.size - 1);
    }
    remove(str) {
        // @ts-ignore
        return (0, redis_1.getRedis)().lremAsync(this.key, 0, str);
    }
}
exports.FixedStringList = FixedStringList;
//# sourceMappingURL=redisCollection.js.map