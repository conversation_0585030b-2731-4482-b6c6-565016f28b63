/**
 * Created by <PERSON>hen<PERSON> on 2017/1/19.
 */

const Config = require('../common/config');
const FeatureToggleHash = Config.FeatureToggle;
const _ = require('lodash');
const DateUtil = require('../common/dateUtil');

class FeatureToggle {
  static isActive(feature) {
    const features = FeatureToggle.list();
    if(_.includes(features, feature)) {
      const status = FeatureToggleHash[feature];
      if(status.hasOwnProperty('open')) {
        return status.open;
      } else if(status.start && status.end){
        return DateUtil.isWithInRange(new Date(), new Date(status.start), new Date(status.end));
      } else {
        return false;
      }
    } else {
      throw `UnKnow feature: ${feature}`;
    }
  }

  static list() {
    return Object.keys(FeatureToggleHash);
  }

  static whenActive(feature, func) {
    if(FeatureToggle.isActive(feature)) {
      return func()
    }
  }
}

module.exports = FeatureToggle;
