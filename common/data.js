"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.appMap = exports.parseEquipItem = exports.getPhotoView = exports.md = exports.pyq = exports.qnm = exports.qn = exports.Constants = exports.appIdMap = void 0;
/* eslint-disable prefer-rest-params */
/* eslint-disable @typescript-eslint/no-var-requires */
// 数据处理通用类
const nos = require("./nos");
const config = require("./config");
const util = require("./util");
const _ = require("lodash");
const logger_1 = require("../pyq-server/logger");
const { textFilterFor64 } = require("../service/qnm/data/TextRegListFor64");
const { isIn64SpecialPeriod } = require("../common/helper");
const { replaceHttps, removeQueryString } = require("../common/util2");
const logger = (0, logger_1.clazzLogger)("common/data");
exports.appIdMap = {
    qn: 8001,
    qnm: 8100 /*,
      tx3: 8002,
      dh2: 8004,
      dh3: 8005*/,
};
exports.Constants = {
    STATUS_NORMAL: 0,
    STATUS_READ: 1,
    STATUS_DELETE: -1,
    STATUS_REJECT: -2,
    STATUS_AUDIT_INIT: 0,
    STATUS_AUDIT_PASS: 1,
    STATUS_AUDIT_REJECT: -1,
    STATUS_AUDIT_PICK: 2,
    GM_ROLE_ID: 1002,
    JL_ROLE_ID: 1005, // 金陵情报官
};
exports.qn = {
    api: require("./config").QN_API,
    StuffImgHash: require("../service/qn/data/StuffImg"),
    EquipImgHash: require("../service/qn/data/EquipImg"),
    SkillImgHash: require("../service/qn/data/SkillImg"),
    ChildSkillHash: require("../service/qn/data/ChildSkill"),
    JobHash: require("../service/qn/data/RoleJob").getHash(),
    getServerHash: function () {
        const defer = require("q").defer();
        require("../service/qn/server/list").getHash(function (hash) {
            defer.resolve(hash);
        });
        return defer.promise;
    },
    fmtTime: function (time) {
        return time && (time + "").length === 10 ? time * 1000 : time;
    },
    getRoleId: function (roleId) {
        return roleId;
    },
    getStuff: function (id) {
        const item = exports.qn.StuffImgHash[id];
        if (!item) {
            require("./logger").add("qn_missStuff", id); // 未找到时写日志
            return {};
        }
        const small = exports.qn.api.iconHost + item[1], img = exports.qn.api.iconHost + item[2], name = item[0];
        return { name: name, small: small, img: img };
    },
    getEquip: function (id) {
        const item = exports.qn.EquipImgHash[id];
        if (!item) {
            require("./logger").add("qn_missEquip", id); // 未找到时写日志
            return {};
        }
        const small = exports.qn.api.iconHost + item[1], img = exports.qn.api.iconHost + item[2], name = item[0];
        return { name: name, small: small, img: img };
    },
    getSkill: function (id) {
        const item = exports.qn.SkillImgHash[id];
        if (!item) {
            require("./logger").add("qn_missSkill", id); // 未找到时写日志
            return {};
        }
        const img = exports.qn.api.iconHost + item[1], desc = parseSkill(item[2]), name = item[0];
        return { name: name, img: img, desc: desc };
    },
    getChildSkill: function (id) {
        const item = exports.qn.ChildSkillHash[id];
        if (!item) {
            require("./logger").add("qn_missChildSkill", id); // 未找到时写日志
            return {};
        }
        const img = exports.qn.api.iconHost + item[1], desc = parseSkill(item[2]), name = item[0];
        return { name: name, img: img, desc: desc };
    },
    parseStuff: function (str) {
        const info = parseStr(str), imgSrc = exports.qn.getStuff(info.cjbId).img;
        return { desc: info.str, img: imgSrc || "" };
    },
    parseEquip: function (str) {
        const info = parseStr(str), equipId = info.id.substr(0, 7), // TemplateId*10 + nWordColor
        item = equipId ? exports.qn.getEquip(equipId) : {}, imgSrc = item.img, small = imgSrc && imgSrc.indexOf("_l") >= 0 ? imgSrc.replace("_l", "_m") : item.small;
        str = (imgSrc ? '<img src="' + imgSrc + '" /><br/>' : "") + info.str;
        return { desc: str, img: imgSrc, small: small };
    },
    parseSkill: parseSkill,
    getHomeInfo: function (level) {
        const list = require("../service/qn/data/HomeInfo").list;
        return list[parseInt(level, 10)];
    },
};
exports.qnm = {
    api: require("./config").QNM_API,
    JobHash: require("../service/qnm/data/RoleJob").getHash(),
    EquipImgHash: require("../service/qnm/data/EquipImg"),
    TextRegList: require("../service/qnm/data/TextRegList"),
    getRoleInfoTable: function (serverId) {
        if (serverId < 500 || serverId >= 1000) {
            // 小于500是网易服。大于等于1000是苹果服，有urs信息
            return config.TABLE_CFG.qnm.roleInfo;
        }
        return config.TABLE_CFG.qnm.roleInfo_other;
    },
    getRolePropTable: function (serverId) {
        if (serverId < 500 || serverId >= 1000) {
            return config.TABLE_CFG.qnm.roleProp;
        }
        return config.TABLE_CFG.qnm.roleProp_other;
    },
    getServerHash: function () {
        const defer = require("q").defer();
        require("../service/qnm/server/list").getHash(function (hash) {
            defer.resolve(hash);
        });
        return defer.promise;
    },
    fmtTime: function (time) {
        return time && (time + "").length === 10 ? time * 1000 : time;
    },
    getRoleId: function (roleId) {
        if (typeof roleId === "string") {
            return parseInt(roleId, 10);
        }
        else {
            return roleId;
        }
    },
    getEquip: function (id) {
        const item = exports.qnm.EquipImgHash[id];
        if (!item) {
            require("./logger").add("qnm_missEquip", id); // 未找到时写日志
            return {};
        }
        const small = exports.qnm.api.iconHost + item[1], img = exports.qnm.api.iconHost + item[2], name = item[0];
        return {
            name: name,
            small: small,
            img: img,
            color: exports.qnm.getEquipColor(item[3]),
            bothHand: !!item[4],
        };
    },
    getEquipColor: function (color) {
        const hash = {
            1: "",
            2: "",
            3: "",
            4: "blue",
            5: "red",
            6: "purple",
            7: "purple", // 鬼装
        };
        return hash[color];
    },
    parseEquip: function (data) {
        let item = exports.qnm.getEquip(data.templateid), imgSrc = item.img, small = item.small, str = parseEquipItem(data);
        str = (small ? '<img src="' + small + '" /><br/>' : "") + str;
        return { pos: data.pos, desc: str, img: imgSrc, small: small, color: item.color, bothHand: item.bothHand };
    },
    /**
     * 限制恶意构造重复文本通过卡死正则
     * example "aaabbbccc" => "aabbcc"
     */
    fixTooLongRepeatStr(str) {
        let repeat = 1;
        let newStr = "";
        let preChar = "";
        for (const c of str) {
            if (preChar === c) {
                repeat += 1;
            }
            else {
                repeat = 1;
            }
            if (repeat <= 2) {
                newStr += c;
            }
            preChar = c;
        }
        return newStr;
    },
    textFilter: function (str) {
        let RegList = exports.qnm.TextRegList;
        if (isIn64SpecialPeriod(new Date())) {
            RegList = [].concat(RegList, textFilterFor64);
        }
        str = str.replace(/<link\s(.+?)=(.+?)>/gi, ""); // 链接不参与不过滤
        str = exports.qnm.fixTooLongRepeatStr(str);
        if (!str) {
            return false;
        }
        for (let i = 0, l = RegList.length; i < l; i++) {
            const item = RegList[i], expr = item[0], flag = item[1], reg = new RegExp(expr, flag);
            if (reg.test(str)) {
                const match = str.match(reg);
                logger.warn({ expr: expr, flag, text: str, match }, "HitTextFilter");
                return "" + i;
            }
        }
        return false;
    },
};
exports.pyq = {
    getAllowHash: function (role) {
        let friendList = role.FriendList;
        const followingList = role.FollowingList || [];
        let privacy = role && role.Privacy;
        privacy = privacy ? privacy.split(",") : [];
        const onlyAllowFriend = privacy[1] == 1; // 为1时只允许好友留言
        const allowFollowing = privacy[3] == 1; // 为1时我关注的人可留言
        if (onlyAllowFriend) {
            const allowHash = {};
            friendList = friendList ? friendList.split(",") : []; // 无论好友留言还是关注的人留言， 好友总是有评论权限
            for (let j = 0, k = friendList.length; j < k; j++) {
                allowHash[friendList[j]] = 1;
            }
            if (allowFollowing) {
                for (let i = 0; i < followingList.length; i++) {
                    allowHash[followingList[i]] = 1;
                }
            }
            return allowHash;
        }
        else {
            return undefined;
        }
    },
    canComment: function (role, roleId, allowHash) {
        allowHash = allowHash || exports.pyq.getAllowHash(role);
        return !!(roleId == role.RoleId || !allowHash || allowHash[roleId]);
    },
    setPhotoView: function (item, roleId) {
        let rid = item.RoleId, photo = item.Photo, showPhoto = item.ShowPhoto, photoAudit = item.PhotoAudit;
        if (!rid) {
            return;
        }
        photo = getPhotoView(photo, showPhoto, photoAudit, rid, roleId);
        item.Photo = photo;
        return photo;
    },
    setImgListView: function (item, roleId) {
        const rid = item.RoleId, imgList = item.ImgList, imgAudit = item.ImgAudit;
        const list = getImgListView(imgList, imgAudit, rid, roleId, "250y160");
        item.ImgList = list;
        return list;
    },
    getImgList: function (imgList) {
        return getImgList(imgList, "250x250&crop=0_45_250_160");
    },
};
exports.md = {
    NameRegList: require("../service/app/data/NameRegList"),
    nameFilter: function (str) {
        const RegList = exports.md.NameRegList;
        if (!str) {
            return false;
        }
        for (let i = 0, l = RegList.length; i < l; i++) {
            const item = RegList[i], reg = new RegExp(item);
            if (reg.test(str)) {
                return "" + i;
            }
        }
        return false;
    },
    getAvatarView: function (photo) {
        return photo + "?watermark&type=1&gravity=center&image=Y29tbW9uL3dhdGVybWFyay1hdWRpdC5wbmc="; // 审核中水印
    },
    setAvatarView: function (item, roleId) {
        let rid = item.UserId || item.ID, photo = item.Avatar, photoAudit = item.AvaAuthStatus;
        if (!rid) {
            return;
        }
        photo = getPhotoView(photo, 1, photoAudit, rid, roleId);
        item.Avatar = photo;
        return photo;
    },
    setImgListView: function (item, roleId) {
        const rid = item.UserId, imgList = item.ImgList, imgAudit = item.ImgAudit;
        let list;
        if (_.isString(imgList)) {
            list = getImgListView(imgList, imgAudit, rid, roleId, "150y150");
            item.ImgList = list;
        }
        return list;
    },
    getTopic: function (text) {
        if (text && /^#[cC][0-9a-fA-F]{6}/.test(text)) {
            return null;
        }
        if (text && /#([^0-9RGBKYWPQrnuU#][^#\s]{1,19})#/.test(text)) {
            return RegExp.$1;
        }
        return null;
    },
};
function getPhotoView(photo, showPhoto, photoAudit, rid, roleId) {
    if (config.testCfg.skip_audit) {
        // 内网测试直接跳过审核
        return showPhoto == 1 ? photo : null;
    }
    photo = replaceHttps(photo);
    return photo && showPhoto == 1
        ? photoAudit == exports.Constants.STATUS_AUDIT_PASS // 审核通过
            ? photo
            : photoAudit == exports.Constants.STATUS_AUDIT_REJECT // 审核未通过
                ? rid == roleId
                    ? nos.getObjUrl("common", "common/deny.jpg")
                    : null // 审核不通过图片
                : rid == roleId // 审核中
                    ? photo + "?watermark&type=1&gravity=center&image=Y29tbW9uL3dhdGVybWFyay1hdWRpdC5wbmc=" // 审核中水印
                    : null
        : null;
}
exports.getPhotoView = getPhotoView;
const parseUrl = require("url").parse;
function pipeNosUrl(url, ...rest) {
    const urlObj = parseUrl(url);
    const nosArgs = _.slice(arguments, 1);
    let pipeArgs = nosArgs;
    let concatUrl = url;
    if (!urlObj.query) {
        const firstArg = _.first(nosArgs);
        pipeArgs = _.tail(nosArgs);
        if (firstArg) {
            concatUrl += "?" + firstArg;
        }
    }
    if (!_.isEmpty(pipeArgs)) {
        _.forEach(pipeArgs, (arg) => {
            concatUrl += "%7c" + arg; // 管道”|”在URL中编码为”%7c”
        });
    }
    return concatUrl;
}
function getImgListView(imgList, imgAudit, rid, roleId, size) {
    const list = [];
    const crop = size.indexOf("crop") >= 0; // 手游图片需要裁剪
    const denyPic = nos.getObjUrl("common", "common/deny.jpg");
    const skipAudit = config.testCfg.skip_audit;
    imgList = imgList ? imgList.split(",") : [];
    imgAudit = imgAudit ? imgAudit.split(",") : [];
    const thumbArgs = "imageView&thumbnail=" + size;
    const waterMarkSuffix = "watermark&type=1&gravity=center&image=Y29tbW9uL3dhdGVybWFyay1hdWRpdC5wbmc=";
    const auditDefaultImage = "https://hi-163-common.nosdn.127.net/upload/201705/05/e1030d90315e11e7956f95595545646a";
    _.forEach(imgList, (url, i) => {
        url = replaceHttps(url);
        const thumbOrigin = removeQueryString(url);
        const audit = parseInt(imgAudit[i]) || exports.Constants.STATUS_AUDIT_INIT;
        const isSelf = rid == roleId;
        if (skipAudit) {
            // 内网测试直接跳过审核
            url && list.push({ pic: url, thumb: pipeNosUrl(thumbOrigin, thumbArgs) });
        }
        else {
            if (audit === exports.Constants.STATUS_AUDIT_PASS) {
                list.push({ pic: url, thumb: pipeNosUrl(thumbOrigin, thumbArgs) });
            }
            else if (audit === exports.Constants.STATUS_AUDIT_REJECT) {
                if (isSelf) {
                    list.push({ pic: denyPic, thumb: pipeNosUrl(denyPic, crop ? "imageView&crop=0_45_150_96" : "") });
                }
            }
            else {
                //审核中
                if (isSelf) {
                    list.push({ pic: url, thumb: pipeNosUrl(url, thumbArgs, waterMarkSuffix) });
                }
                else {
                    list.push({ pic: auditDefaultImage, thumb: pipeNosUrl(auditDefaultImage, thumbArgs) });
                }
            }
        }
    });
    return list;
}
function getImgList(imgList, size) {
    const list = [];
    const skipAudit = config.testCfg.skip_audit;
    for (let i = 0, k = imgList.length; i < k; i++) {
        let url = imgList[i], thumbArgs = "?imageView&thumbnail=" + size;
        url = replaceHttps(url);
        if (skipAudit) {
            // 内网测试直接跳过审核
            url &&
                list.push({
                    pic: url,
                    thumb: url + thumbArgs,
                });
            continue;
        }
        url &&
            list.push({
                pic: url,
                thumb: url + thumbArgs + "%7Cwatermark&type=1&gravity=center&image=Y29tbW9uL3dhdGVybWFyay1hdWRpdC5wbmc=", // 审核中水印
            });
    }
    return list;
}
function parseStr(equipDetail) {
    // 放在前端执行？
    const SuperColor = {
        P: "ff40ff",
        N: "fffe91",
        M: "ff1200",
        A: "9CF46C",
        R: "ff0000",
        G: "00ff00",
        B: "0000ff",
        Y: "ffff00",
        W: "ffffff",
        K: "000000",
        V: "8000ff",
    };
    const SpecialStyle = {
        u: "text-decoration:underline;",
    };
    const strList = _.compact(equipDetail.split("#n"));
    let itemId = "";
    let cjbId = "";
    const parseStrList = [];
    strList.forEach(function (str) {
        const parseStr = (str || "")
            .trim()
            .replace(/#f[\da-fA-F]{1,6}/g, "") // Event中的鬼器样式，直接无视
            .replace(/#eid([\d-]+)$/g, "")
            .replace(/^#o(\d{7,8})(#o\d{7,8})?(#r.*)$/g, function (match, id) {
            itemId = id;
            return arguments[3] || "";
        })
            .replace(/#o(\d{8})/g, function (match, id) {
            // 图标样式
            return '<b class="bs b_' + id + '"></b>';
        })
            .replace(/#o(\d{7})/g, function (match, id) {
            // 传教宝item
            cjbId = id;
            return "";
        })
            .replace(/#([uPNMARGBYWKV])(.*?)((?=#[rce])|$)/g, function (match, char, content) {
            // 特殊字体样式
            const style = SpecialStyle[char] || "color:#" + SuperColor[char];
            return '<span style="' + style + '">' + content + "</span>";
        })
            .replace(/#e([\da-fA-F]{1,6})(.*?)((?=#[c])|$)/g, function (match, color, content) {
            // 神兵装备样式
            const len = color.length;
            color = "#" + (len === 3 || len === 6 ? color : util.pad(color, 6));
            const style = "text-shadow:1px 0 1px " + color + ", -1px 0 1px " + color + ", 0 1px 1px " + color + ", 0 -1px 1px " + color;
            return '<span style="' + style + '">' + content + "</span>";
        })
            .replace(/#c([\da-fA-F]{1,6})(.*?)((?=#[c])|$)/g, function (match, color, content) {
            // 字体颜色
            const len = color.length;
            color = "#" + (len === 3 || len === 6 ? color : util.pad(color, 6));
            return '<span style="color:' + color + '">' + content + "</span>";
        })
            .replace(/#r/g, "<br/>");
        parseStrList.push(parseStr);
    });
    equipDetail = parseStrList.join("");
    return { str: equipDetail, id: itemId, cjbId: cjbId };
}
function parseSkill(str) {
    const item = parseStr(str);
    return item.str;
}
function parseEquipItem(data) {
    let str = parseSkill(data.equipname) + "<br/>";
    str += "<span>" + data.type + '</span><span class="fr">' + data.levellimit + "</span><br/>";
    str += "装备评分 " + data.score + "<br/>";
    const jewelIds = data.jewelids || []; // 宝石装备信息
    if (jewelIds.length) {
        const stStr = '<b class="jewel_';
        const edStr = '"></b>';
        str += stStr + jewelIds.join(edStr + stStr) + edStr + "<br/>"; // holenum
    }
    str += '<b class="entry">基础属性</b><br/>';
    const props = data.props || [];
    for (let i = 0, l = props.length; i < l; i++) {
        str += " " + parseSkill(props[i]) + "<br/>";
    }
    str += '<b class="entry">升级完美度</b> ' + getStars(data.perfection || 0) + "<br/>"; // 实际星级=传递的强化完美度*0.5，perfection 0~10之间的整数   intensifylevel
    str += '<b class="entry">耐久度</b> ' + data.duration + "<br/>";
    str += '<b class="entry">词条属性</b><br/>'; // 词条洗炼积分暂无
    const words = data.words || [];
    for (let i = 0, l = words.length; i < l; i++) {
        str += " " + parseSkill(words[i]) + "<br/>";
    }
    const jewelWords = data.jewelwords || [];
    if (!_.isEmpty(jewelWords)) {
        str += '<b class="entry">宝石属性</b><br/>';
        for (let i = 0, l = jewelWords.length; i < l; i++) {
            str += " " + parseSkill(jewelWords[i]) + "<br/>";
        }
    }
    const gemWords = data.gemwords || [];
    if (!_.isEmpty(gemWords)) {
        str += '<b class="entry">' + parseSkill(data.gemgroup) + "</b><br/>"; // 石之灵
        for (let i = 0, l = gemWords.length; i < l; i++) {
            str += " " + parseSkill(gemWords[i]) + "<br/>";
        }
    }
    return str;
}
exports.parseEquipItem = parseEquipItem;
function getStars(val) {
    const arr = [];
    arr.length = Math.floor(val / 2) + 1;
    return arr.join("★") + (val % 2 === 1 ? "☆" : "");
}
exports.appMap = (function () {
    const map = {};
    for (const key in exports.appIdMap) {
        map[exports.appIdMap[key]] = key;
    }
    return map;
})();
//# sourceMappingURL=data.js.map