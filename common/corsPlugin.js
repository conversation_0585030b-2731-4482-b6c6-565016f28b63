"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CorsPlugin = void 0;
const _ = require("lodash");
class CorsPlugin {
    constructor(allowOrigin) {
        this.allowOrigin = allowOrigin;
    }
    isOriginAllowed(origin, allowedOrigin) {
        if (_.isArray(allowedOrigin)) {
            for (let i = 0; i < allowedOrigin.length; i++) {
                if (this.isOriginAllowed(origin, allowedOrigin[i])) {
                    return true;
                }
            }
            return false;
        }
        else if (_.isString(allowedOrigin)) {
            return origin === allowedOrigin;
        }
        else if (allowedOrigin instanceof RegExp) {
            return allowedOrigin.test(origin);
        }
        else {
            return !!allowedOrigin;
        }
    }
    cors() {
        let self = this;
        return function (req, res, next) {
            let originHeader = req.headers['origin'];
            if (self.isOriginAllowed(originHeader, self.allowOrigin)) {
                res.header('Access-Control-Allow-Origin', originHeader);
                res.header('Access-Control-Allow-Credentials', 'true');
                next();
            }
            else {
                next();
            }
        };
    }
    preflight() {
        let self = this;
        return function (req, res, next) {
            if (req.method !== 'OPTIONS')
                return next();
            let originHeader = req.headers['origin'];
            if (self.isOriginAllowed(originHeader, self.allowOrigin)) {
                res.once('header', function () {
                    res.header('Access-Control-Allow-Origin', originHeader);
                    res.header('Access-Control-Allow-Credentials', 'true');
                    res.header('Access-Control-Allow-Methods', 'POST, GET, OPTIONS');
                    res.header('Access-Control-Allow-Headers', 'X-PINGOTHER, Content-Type');
                });
                res.send(204);
            }
            else {
                return next();
            }
        };
    }
    static create(options) {
        return new CorsPlugin(options.origins);
    }
}
exports.CorsPlugin = CorsPlugin;
//# sourceMappingURL=corsPlugin.js.map