"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateUrsCookie = void 0;
const AppConfig = require("./config");
const httpLib = require("./request");
const logger2_1 = require("./logger2");
const _ = require('lodash');
let logger = (0, logger2_1.getLogger)('ursCookie');
const bluebird = require("bluebird");
const UrsCookieHosts = AppConfig.URS_COOKIE_PARSE.HOSTS;
const Config = {
    Hosts: UrsCookieHosts,
    productid: 'f77072fb1b1a4664856363589c4127af',
    cookieNames: ['NTES_SESS', 'NTES_YD_SESS', 'NTES_OSESS', 'NTES_PASSPORT', 'NTES_YD_PASSPORT']
};
function formatSsn(ssn) {
    if (ssn.includes('@')) {
        return ssn;
    }
    else {
        return ssn + '@163.com';
    }
}
function callValidateApi(uri, cookieName, cookieValue) {
    return __awaiter(this, void 0, void 0, function* () {
        let reqOption = {
            method: 'POST',
            uri: uri,
            qs: {
                productid: Config.productid,
                cookieName: cookieName,
                cookie: cookieValue,
                recreate: 0
            }
        };
        let body = yield httpLib.request(reqOption);
        if (body.retCode) {
            if (body.retCode === 200) {
                const data = body.data;
                data.ssn = formatSsn(data.ssn);
                return { validate: true, data: data };
            }
            else {
                return { validate: false, msg: body.msg };
            }
        }
        logger.error('ValidateCookieError', { response: body, reqOption: reqOption });
        return bluebird.reject(new Error('Call urs Api ' + uri + ' failed'));
    });
}
function getValidCookie(cookies) {
    const cookeName = _.find(Config.cookieNames, name => {
        return !!cookies[name];
    });
    return { cookieName: cookeName, cookieValue: cookies[cookeName] };
}
function validateCookies(cookies) {
    const checkCookie = getValidCookie(cookies);
    return callValidateApi(Config.Hosts[0] + '/validate', checkCookie.cookieName, checkCookie.cookieValue)
        .catch(() => {
        return callValidateApi(Config.Hosts[1] + '/validate', checkCookie.cookieName, checkCookie.cookieValue);
    });
}
// 测试机无法使用 ursCookie校验接口，从登录cookie取出来
function validateFromLocal(cookies) {
    return __awaiter(this, void 0, void 0, function* () {
        const pInfo = cookies['P_INFO'];
        const netsSessInfo = cookies['NTES_SESS'];
        if (pInfo && netsSessInfo) {
            const ssn = pInfo.split('|')[0];
            return { validate: true, data: { ssn: ssn } };
        }
        else {
            return { validate: false, msg: '请先登录网易通行证' };
        }
    });
}
function validateUrsCookie(cookies) {
    if (AppConfig.testCfg.test_env) {
        return Promise.resolve({ validate: true, data: null });
    }
    else {
        return validateCookies(cookies);
    }
}
exports.validateUrsCookie = validateUrsCookie;
//# sourceMappingURL=ursCookie.js.map