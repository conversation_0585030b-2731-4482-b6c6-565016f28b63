const restify = require('restify')
const Promise = require('bluebird')
const _ = require('lodash')
const config = require('../common/config')

const influxConfig = config.INFLUX_CFG || {
  host: 'localhost',
  port: '8086',
  database: 'nodejs_sites_island'
}

class InfluxClient {
  constructor (influxConfig) {
    this.influxConfig = influxConfig
    const url = 'http://' + influxConfig.host + ':' + influxConfig.port
    this.client = restify.createStringClient({url: url})
  }

  /**
   * @param {string} lp follow influx Line Protocol string
   */
  write (lp) {
    return new Promise((resolve, reject) => {
      return this.client.post(`/write?db=${this.influxConfig.database}`, lp, function (err, req, res, obj) {
        if (err) {
          reject(err)
        } else {
          resolve(obj)
        }
      })
    })
  }
}

const client = new InfluxClient(influxConfig)

function toKeyPairString (obj) {
  return _.map(obj, (value, key) => {
    if (_.isString(value)) {
      return `${key}="${value}"`
    } else {
      return `${key}=${value}`
    }
  }).join(',')
}

class InfluxLog {
  constructor (options) {
    this.measurement = options.measurement || ''
    this.fields = options.fields || []
    this.tags = options.tags
    this.timestamp = (options.timestamp || Date.now()) * 1000000 // influxDb使用nanosecond
  }

  _getTagPart () {
    let tagPart = ''
    if (!_.isEmpty(this.tags)) {
      tagPart = ',' + toKeyPairString(this.tags)
    }
    return tagPart
  }

  _getFieldsPart () {
    return _.map(this.fields, (value, key) => {
      if (_.isString(value)) {
        return `${key}="${value}"`
      } else if (_.isNumber(value) && _.isInteger(value)) {
        return `${key}=${value}` + 'i' // integer 需要加i标记
      } else {
        return `${key}=${value}`
      }
    }).join(',')
  }

  toLineProtocol () {
    return `${this.measurement}${this._getTagPart()} ${this._getFieldsPart()} ${this.timestamp}`
  }

  send () {
    return client.write(this.toLineProtocol())
  }

  /**
   * create influx log instance
   * @param options
   * @param {String} options.measurement
   * @param {Array} options.tags
   * @param {Array} options.fields
   * @param {Number} options.timestamp
   * @return {InfluxLog}
   */
  static create (options) {
    return new InfluxLog(options)
  }
}

module.exports = {
  client: client,
  InfluxLog: InfluxLog
}
