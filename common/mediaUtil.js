"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getImgCsvListView = exports.getImgListView = exports.getPhotoViewItem = exports.auditingPublicView = exports.transNosCdnToSourceUrl = exports.getVideoAuditCover = exports.getVideoPassCover = exports.getWaterMarkUrl = exports.toFullUrl = exports.isValidMediaUrl = exports.isFpUrl = exports.isNosUrl = void 0;
const _ = require("lodash");
const url_1 = require("url");
const config_1 = require("../pyq-server/common/config");
const constants_1 = require("./constants");
const util_1 = require("./util");
const util2_1 = require("./util2");
const logger_1 = require("../pyq-server/logger");
const util_2 = require("../pyq-server/services/util");
const constants_2 = require("../pyq-server/constants");
const logger = (0, logger_1.clazzLogger)("common/mediaUtil");
function isNosUrl(url) {
    return !!/nosdn\.127\.net/.exec(url) || !!/nos\.netease\.com/.exec(url);
}
exports.isNosUrl = isNosUrl;
function isFpUrl(url) {
    return !!/fp-dev\.webapp\.163\.com/.exec(url) || !!/fp\.ps\.easebar\.com/.exec(url);
}
exports.isFpUrl = isFpUrl;
// 判断是否是有效的媒体地址, 包括nos地址（国服) 和fp地址（海外服)
function isValidMediaUrl(url) {
    return isNosUrl(url) || isFpUrl(url);
}
exports.isValidMediaUrl = isValidMediaUrl;
var MediaOpType;
(function (MediaOpType) {
    MediaOpType[MediaOpType["Watermark"] = 0] = "Watermark";
    MediaOpType[MediaOpType["Thumb"] = 1] = "Thumb";
    MediaOpType[MediaOpType["VideoCover"] = 2] = "VideoCover";
})(MediaOpType || (MediaOpType = {}));
var FopOrder;
(function (FopOrder) {
    FopOrder[FopOrder["Thumb"] = 1] = "Thumb";
    FopOrder[FopOrder["Watermark"] = 2] = "Watermark";
    FopOrder[FopOrder["VideoCover"] = 3] = "VideoCover";
})(FopOrder || (FopOrder = {}));
function toNosFullUrl(url, ops) {
    let fullOps = "";
    if (ops.length > 0) {
        const opAction = (0, util_2.hasQueryParams)(url) ? "|" : "?";
        fullOps = opAction + ops.map((r) => r.value).join("%7c");
    }
    return `${url}${fullOps}`;
}
function toFpFullUrl(url, ops) {
    let fullOps = "";
    if (ops.length > 0) {
        fullOps = "?fop=" + ops.map((r) => r.value).join("%7c");
    }
    return `${url}${fullOps}`;
}
function toFullUrl(data) {
    const ops = _.orderBy(data.ops, ["order"], ["desc"]);
    if (isNosUrl(data.url)) {
        return toNosFullUrl(data.url, ops);
    }
    else if (isFpUrl(data.url)) {
        return toFpFullUrl(data.url, ops);
    }
    else {
        return data.url;
    }
}
exports.toFullUrl = toFullUrl;
function getNosVideoCover(videoUrl) {
    return toNosFullUrl(videoUrl, [{ type: MediaOpType.VideoCover, value: config_1.photoViewCfg.NOS_VIDEO_COVER_SUFFIX }]);
}
function getFpVideoCover(videoUrl) {
    return toFpFullUrl(videoUrl, [{ type: MediaOpType.VideoCover, value: config_1.photoViewCfg.FP_VIDEO_COVER_SUFFIX }]);
}
function getWaterMarkUrl(imageUrl, markStr) {
    return pipeNosUrl(imageUrl, config_1.photoViewCfg.NOS_THUMB_SUFFIX, markStr);
}
exports.getWaterMarkUrl = getWaterMarkUrl;
function getVideoPassCover(videoUrl) {
    if (isNosUrl(videoUrl)) {
        return getNosVideoCover(videoUrl);
    }
    else if (isFpUrl(videoUrl)) {
        return getFpVideoCover(videoUrl);
    }
    else {
        return videoUrl;
    }
}
exports.getVideoPassCover = getVideoPassCover;
function getVideoAuditCover(videoUrl) {
    if (isNosUrl(videoUrl)) {
        return getWaterMarkUrl(getNosVideoCover(videoUrl), config_1.photoViewCfg.NOS_AUDITING_WATERMARK_SUFFIX);
    }
    else if (isFpUrl(videoUrl)) {
        return toFpFullUrl(videoUrl, [
            { type: MediaOpType.VideoCover, value: config_1.photoViewCfg.FP_VIDEO_COVER_SUFFIX },
            { type: MediaOpType.Watermark, value: config_1.photoViewCfg.FP_AUDITING_WATERMARK_SUFFIX },
        ]);
    }
    else {
        return videoUrl;
    }
}
exports.getVideoAuditCover = getVideoAuditCover;
function pipeNosUrl(url, ...nosArgs) {
    const urlObj = (0, url_1.parse)(url);
    let pipeArgs = nosArgs;
    let concatUrl = url;
    if (!urlObj.query) {
        const firstArg = _.first(nosArgs);
        pipeArgs = _.tail(nosArgs);
        if (firstArg) {
            concatUrl += "?" + firstArg;
        }
    }
    if (!_.isEmpty(pipeArgs)) {
        _.forEach(pipeArgs, (arg) => {
            concatUrl += "%7c" + arg; // 管道”|”在URL中编码为”%7c”
        });
    }
    return concatUrl;
}
const CDN_PATH_REP_REGEX = /(https?:\/\/)([a-zA-Z0-9-]+)(.nosdn.127.net.)(.*)/;
function transNosCdnToSourceUrl(url) {
    return url.replace(CDN_PATH_REP_REGEX, "$1nos.netease.com/$2/$4");
}
exports.transNosCdnToSourceUrl = transNosCdnToSourceUrl;
function getPhotoView(url, auditStatus, isSelf, lang) {
    if (auditStatus === constants_1.AuditStatues.PASS) {
        return { url: url };
    }
    else if (auditStatus === constants_1.AuditStatues.Reject) {
        if (isSelf) {
            return auditRejctSelfView(url, lang);
        }
        else {
            // 图片被屏蔽后别人看到依旧是审核中
            return auditingPublicView(url, lang);
        }
    }
    else {
        if (isSelf) {
            return auditingPhotoMarkView(url, lang);
        }
        else {
            return auditingPublicView(url, lang);
        }
    }
}
class AbstractImageView {
    constructor(url, ops) {
        this.url = url;
        this.ops = ops || [];
    }
    isNosUrl() {
        return isNosUrl(this.url);
    }
    isFpUrl() {
        return isFpUrl(this.url);
    }
    addOp(op) {
        this.ops.push(op);
    }
    addThumbOp(thumb) {
        this.ops.push({ type: MediaOpType.Thumb, order: FopOrder.Thumb, value: thumb });
    }
    addWaterMarkOp(watermark) {
        this.ops.push({ type: MediaOpType.Watermark, order: FopOrder.Watermark, value: watermark });
    }
    get(lang) {
        if (!this.url) {
            return { url: "" };
        }
        if (this.isNosUrl()) {
            return this.getForNos(lang);
        }
        else if (this.isFpUrl()) {
            return this.getForFp(lang);
        }
        else {
            return { url: this.url };
        }
    }
    toData() {
        return { url: this.url, ops: this.ops };
    }
}
function thumbView(url, ops, lang) {
    return ThumbImageView.create(url, _.clone(ops)).get(lang);
}
class AuditRejectSelfImageView extends AbstractImageView {
    getForNos() {
        return { url: config_1.photoViewCfg.NOS_REJECT_SELF_VIEW_URL, ops: [] };
    }
    getForFp(lang) {
        var _a;
        const rejectSelfUrl = ((_a = constants_2.FpAuditConfigMap.get(lang)) === null || _a === void 0 ? void 0 : _a.RejectSelfViewUrl) || config_1.photoViewCfg.FP_REJECT_SELF_VIEW_URL;
        return { url: rejectSelfUrl, ops: [] };
    }
    static create(url) {
        return new this(url);
    }
}
class AudtingWaterMarkView extends AbstractImageView {
    getForNos() {
        this.addWaterMarkOp(config_1.photoViewCfg.NOS_AUDITING_WATERMARK_SUFFIX);
        return this.toData();
    }
    getForFp(lang) {
        var _a;
        const watermarkUrl = ((_a = constants_2.FpAuditConfigMap.get(lang)) === null || _a === void 0 ? void 0 : _a.WatermarkUrl) || config_1.photoViewCfg.FP_WATERMARK_URL;
        const watermarkSuffix = "watermark/1/image/" + (0, util2_1.base64Encode)(watermarkUrl) + "/gravity/5"; // bese64
        this.addWaterMarkOp(watermarkSuffix);
        return this.toData();
    }
    static create(url) {
        return new this(url);
    }
}
class AudtingPublicView extends AbstractImageView {
    getForNos() {
        return { url: config_1.photoViewCfg.NOS_AUDITING_PUBLIC_URL };
    }
    getForFp(lang) {
        var _a;
        const auditingPublicUrl = ((_a = constants_2.FpAuditConfigMap.get(lang)) === null || _a === void 0 ? void 0 : _a.AuditingPublicUrl) || config_1.photoViewCfg.FP_AUDITING_PUBLIC_URL;
        return { url: auditingPublicUrl };
    }
    static create(url) {
        return new this(url);
    }
}
class ThumbImageView extends AbstractImageView {
    getForNos() {
        this.addThumbOp(config_1.photoViewCfg.NOS_THUMB_SUFFIX);
        return this.toData();
    }
    getForFp() {
        this.addThumbOp(config_1.photoViewCfg.FP_THUMB_SUFFIX);
        return this.toData();
    }
    static create(url, ops) {
        return new this(url, ops);
    }
}
function auditRejctSelfView(url, lang) {
    return AuditRejectSelfImageView.create(url).get(lang);
}
function auditingPhotoMarkView(url, lang) {
    return AudtingWaterMarkView.create(url).get(lang);
}
function auditingPublicView(url, lang) {
    return AudtingPublicView.create(url).get(lang);
}
exports.auditingPublicView = auditingPublicView;
function getPhotoViewItem(url, auditStatus, isSelf, lang) {
    const photoRecord = getPhotoView(url, auditStatus, isSelf, lang);
    const thumb = thumbView((0, util2_1.removeQueryString)(photoRecord.url || ""), photoRecord.ops, lang);
    return { pic: toFullUrl(photoRecord), thumb: toFullUrl(thumb) };
}
exports.getPhotoViewItem = getPhotoViewItem;
function getImgListView(urlList, auditList, isSelf, lang) {
    urlList = urlList.filter((url) => (0, util2_1.validateUrl)(url));
    auditList = auditList.slice(0, urlList.length);
    return _.zipWith(urlList, auditList, function (url, audit) {
        return { url: url, audit: audit };
    }).map((r) => {
        return getPhotoViewItem(r.url, r.audit, isSelf, lang);
    });
}
exports.getImgListView = getImgListView;
function getImgCsvListView(urlListCsv, auditListCsv, isSelf, lang) {
    const urlList = (0, util_1.csvStrToArray)(urlListCsv);
    const auditList = (0, util_1.csvStrToIntArray)(auditListCsv);
    return getImgListView(urlList, auditList, isSelf, lang);
}
exports.getImgCsvListView = getImgCsvListView;
//# sourceMappingURL=mediaUtil.js.map