"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.rmv = exports.setExpire = exports.delVal = exports.getVal = exports.setVal = exports.get = exports.set = void 0;
const redis = require("./redis");
const redis_1 = require("./redis");
function set(id, data) {
    data.access = Date.now();
    const promise = redis.setHMKV(getId(id), data);
    return promise;
}
exports.set = set;
function get(id) {
    const sessionKey = getId(id);
    return (0, redis_1.getRedis)().hgetallAsync(sessionKey)
        .then(sessionData => {
        if (sessionData) {
            (0, redis_1.getRedis)().hsetAsync(sessionKey, 'access', Date.now());
        }
        return sessionData;
    });
}
exports.get = get;
function setVal(id, key, val) {
    return redis.setHKV(getId(id), key, val);
}
exports.setVal = setVal;
function getVal(id, key) {
    return redis.getHKV(getId(id), key);
}
exports.getVal = getVal;
function delVal(id, key) {
    return redis.delHKV(getId(id), key);
}
exports.delVal = delVal;
function setExpire(id, seconds) {
    return redis.expireK(getId(id), seconds);
}
exports.setExpire = setExpire;
function rmv(id) {
    return redis.delKey(getId(id));
}
exports.rmv = rmv;
function getId(id) {
    return 'sess:' + id;
}
//# sourceMappingURL=session.js.map