/**
 * Created by zhen<PERSON> on 2017/3/9.
 */

const rq = require('./request');
const _ = require('lodash');
const util = require('./util');
const config = require('./config');
const bluebird = require('bluebird');
let loginCheckForNgp = require('../common/yidun').loginCheck



const UrsService = {
  request: function (option) {
    return bluebird.method(function () {
      if (config.testCfg.test_env) {
        // 提供测试环境伪造数据
        if (option.url.endsWith('ulogin_server')) {
          let payload = { type: 'ngpLogin', urs: option.qs.username }
          let cookie = JSON.stringify(payload)
          return `200\nOK\ncookie=${cookie}`
        } else {
          return `200\nOK\n`
        }
      } else {
        return rq.request(option)
      }
    })().then(UrsService.formatUrsApiInfo)
      .then(info => {
        if (info.code.match(/2\d\d/)) {
          return info;
        } else {
          return bluebird.reject(info);
        }
      })
  },

  ulogin: function (req, res) {
    let params = req.params
    let product = req.params.product

    let uLoginProduct = ''
    if (product === 'nsh') {
      uLoginProduct = 'nsh_ngp'
    } else {
      uLoginProduct = 'ngp'
    }

    let userIp = util.getIp(req).replace('::ffff:', '');

    return loginCheckForNgp({ ip: userIp, token: params.dunToken, username: params.username, password: params.password })
      .then(isValidate => {
        if (isValidate) {
          return doLogin({})
        } else {
          return bluebird.reject({ code: 'YD_CHECK_FAIL', msg: '您的登录行为异常，请1小时后重试' })
        }
      })

    function doLogin(ydInfo) {
      let noLogin = params.nologin;
      let data = {
        userip: userIp,
        username: params.username,
        password: params.password,
        passtype: 0,    // 0密码为MD5密文
        type: noLogin ? 0 : 1,        // 1表示设置cookie.
        needRiskInfo: noLogin ? 0 : 1,     // 是否需要返回风控信息
        //needmainaccount: noLogin ? 0 : 1,  // 表示如果手机邮帐号/别名邮箱帐号登录是否返回主帐号和手机邮帐号/别名邮箱帐号
        savelogin: 0,
        secuInfo: 0,
        userStatus: 1,    // 当用户名不存在的时候，1：需要返回
        product: uLoginProduct
      };

      if (uLoginProduct === 'nsh_ngp') {
        // autoMigrateNieLianData(params.username)
      }

      return UrsService.request({
        url: `${config.REG_163_HOST}/services/ulogin_server`,
        method: 'POST',
        qs: data,
        headers: {
          uuid: params.username,
          product: uLoginProduct,
          username: params.username
        }
      }).then(info => {
        let ursInfo = UrsService.ursKeyValueStringToHash(info.extraInfo);

        /*// 逆水寒版本不返回MdInfo
        if (ydInfo === 'nsh') {
            let ursCookie = ursInfo.cookie;
            delete ursInfo.cookie;
            req.cookies = { NTES_SESS: ursCookie };
            let user = require('../service/app/user');
            return user.login(req, res).spread(function(data) {
                res.setCookie('NTES_SESS', ursCookie, config.cookieCfg);

                return res.succSend({ MdInfo: {}, UrsInfo: ursInfo, MBInfo: {} });
            });
        }*/

        // 仅登录校验
        if (noLogin) {
          return res.succSend(ursInfo);
        }

        return UrsService.request({
          url: `${config.REG_163_HOST}/services/getuserinfo/getpassprotectinfo`,
          method: 'GET',
          qs: {
            userip: userIp,
            username: params.username,
            product: 'ngp'
          }
        }).then(info => {
          let mbInfo = UrsService.ursKeyValueStringToHash(info.extraInfo);

          let ursCookie = ursInfo.cookie;
          delete ursInfo.cookie;
          req.cookies = { NTES_SESS: ursCookie };
          let user = require('../service/app/user');
          return user.login(req, res).spread(function (data) {
            res.setCookie('NTES_SESS', ursCookie, config.cookieCfg);
            return res.succSend({ MdInfo: data, UrsInfo: ursInfo, MBInfo: mbInfo, YdInfo: ydInfo });
          });
        });
      });
    }
  },

  getMiBao: function (ip, urs) {
    return UrsService.request({
      url: `${config.REG_163_HOST}/services/getuserinfo/getpassprotectinfo`,
      method: 'GET',
      qs: {
        userip: ip,
        username: urs,
        product: 'ngp'
      }
    }).then(info => {
      return UrsService.ursKeyValueStringToHash(info.extraInfo);
    });
  },

  getRiskInfo: function (ip, urs) {
    return rq.request({
      url: `${config.DC_REG_163_HOST}/rest/risk/useraction`,
      method: 'POST',
      headers: {
        product: 'ngp'
      },
      json: {
        userIp: ip,
        ssn: urs,
        product: 'ngp'
      }
    }).then(info => {
      return info.result || {};
    });
  },

  login: function (data) {
    data = _.defaults(data, { type: 0, product: "qnweb" });
    return UrsService.request({
      method: "POST",
      url: "https://login.163.com/services/otplogin",
      form: data,
    })
  },

  formatUrsApiInfo: function (info) {
    const lines = _.compact(_.split(info, "\n"));
    return { code: lines[0], msg: lines[1], extraInfo: lines[2], lines: lines };
  },

  ursKeyValueStringToHash: function (str) {
    return _.chain(_.split(str, '&'))
      .reduce((hash, cur) => {
        const keyValue = _.split(cur, '=');
        const key = keyValue[0];
        const value = keyValue[1];
        if (key) {
          hash[key] = value;
        }
        return hash;
      }, {})
      .value();
  },


  queryUserSecInfoStatus: function (data) {
    return UrsService.request({
      method: "GET",
      url: `${config.REG_163_HOST}/services/userinfo/queryUserSecInfoStatus`,
      qs: data,
    }).then(info => {
      if (info.extraInfo) {
        info.extraInfo = UrsService.ursKeyValueStringToHash(info.extraInfo);
        if (info.extraInfo.games) {
          info.extraInfo.games = decodeURIComponent(info.extraInfo.games);
        }
      }
      return info;
    });
  },

  verifyintegritymibao2: function (data) {
    return UrsService.request({
      method: "GET",
      url: `${config.REG_163_HOST}/services/userinfo/verifyintegritymibao2`,
      qs: data,
    }).then(info => {
      if (info.lines) {
        const kvStr = _.slice(info.lines, 2, 4).join('&');
        info.extraInfo = UrsService.ursKeyValueStringToHash(kvStr);
        _.forEach(info.extraInfo, (v, k) => {
          info.extraInfo[k] = parseInt(v, 10);
        })
      }
      return info;
    });
  },

  queryAccountAliasBind: function (urs) {
    return UrsService.request({
      method: 'GET',
      url: `${config.REG_163_HOST}/services/queryAccountAliasBind`,
      qs: { username: urs, product: 'bwweb' }
    }).then(info => {
      if (info && info.extraInfo) {
        let accounts = info.extraInfo.split('|')
        return { main: _.first(accounts), others: _.tail(accounts) }
      } else {
        return { main: urs, others: [] }
      }
    })
  }
};

function autoMigrateNieLianData(urs) {
  let nielianMigrator = require('../service/nsh/migrateNieLianNosData')
  return UrsService.queryAccountAliasBind(urs)
    .then(info => {
      return nielianMigrator.migrate(info)
    })
}

module.exports = UrsService;
