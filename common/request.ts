import * as requestLib from "request";
import * as _ from "lodash";
import { testCfg, thirdApiSlowLogCfg } from "../common/config";
import { slow3rdLogger } from "./logger2";

if (testCfg.request_debug) {
  //@ts-ignore
  requestLib.debug = true;
}

const DEFAULT_TIMEOUT = 5000;

interface IReqParams {
  method?: string;
  uri?: string;
  url?: string;
  qs?: object;
  formData?: object;
  body?: any;
  json?: boolean;
  timeout?: number;
  headers?: object;
}

export function request(option?: IReqParams): Promise<any> {
  option = _.defaults(option, { json: true, timeout: DEFAULT_TIMEOUT });
  return new Promise(function (resolve, reject) {
    const startTime = Date.now();
    //@ts-ignore
    return requestLib(option, function (err, response, body) {
      const endTime = Date.now();
      const duration = endTime - startTime;
      if (duration > thirdApiSlowLogCfg.threshold) {
        slow3rdLogger.info({ option, duration }, "DetectSlow3rdApi");
      }

      if (err) {
        reject(err);
      } else {
        resolve(body);
      }
    });
  });
}

export function requestV2(option?: IReqParams): Promise<[any, requestLib.Response]> {
  option = _.defaults(option, { json: true, timeout: DEFAULT_TIMEOUT });
  return new Promise(function (resolve, reject) {
    const startTime = Date.now();
    //@ts-ignore
    return requestLib(option, function (err, response, body) {
      const endTime = Date.now();
      const duration = endTime - startTime;
      if (duration > thirdApiSlowLogCfg.threshold) {
        slow3rdLogger.info({ option, duration }, "DetectSlow3rdApi");
      }
      if (err) {
        reject(err);
      } else {
        resolve([body, response]);
      }
    });
  });
}

export function get(url: string, data?: object, option?: IReqParams) {
  return request(
    Object.assign(
      {
        url: url,
        method: "GET",
        qs: data,
      },
      option || {}
    )
  );
}

export function post(url: string, data?: object, option?: IReqParams) {
  return request(
    Object.assign(
      {
        url: url,
        method: "POST",
        form: data,
      },
      option || {}
    )
  );
}
