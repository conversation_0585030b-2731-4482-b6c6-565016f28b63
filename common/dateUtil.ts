import * as moment from "moment";
import * as util from "../common/util";

export function isWithInRange(date, startDate, endDate) {
  var time = date.getTime();
  var startTime = startDate.getTime();
  var endTime = endDate.getTime();

  if (startTime > endTime) {
    throw new Error("The start of the range cannot be after the end of the range");
  }

  return time >= startTime && time <= endTime;
}

// 返回一个新的实例
export function cloneDate(date: Date) {
  return new Date(date.getTime());
}

export function startOfDay(date: Date) {
  const newDate = cloneDate(date);
  newDate.setHours(0, 0, 0, 0);
  return newDate;
}

export function endOfDay(date: Date) {
  const newDate = cloneDate(date);
  newDate.setHours(23, 59, 59, 999);
  return newDate;
}

export function isBefore(date1: Date, date2: Date) {
  return date1.getTime() < date2.getTime();
}

export function isAfter(date1: Date, date2: Date) {
  return date1.getTime() > date2.getTime();
}

export function startOfMinute(date: Date) {
  const newDate = cloneDate(date);
  newDate.setSeconds(0, 0);
  return newDate;
}

export function startOfHour(date: Date) {
  const newDate = cloneDate(date);
  newDate.setMinutes(0, 0, 0);
  return newDate;
}

export function format(date: Date, format: string) {
  let pad = util.pad;
  format = format || "yyyy-MM-dd HH:mm:ss";
  let year = date.getFullYear();
  let month = date.getMonth() + 1;
  let day = date.getDate();
  let hour = date.getHours();
  let minute = date.getMinutes();
  let second = date.getSeconds();
  return format
    .replace("yyyy", "" + year)
    .replace("MM", pad(month, 2))
    .replace("dd", pad(day, 2))
    .replace("HH", pad(hour, 2))
    .replace("mm", pad(minute, 2))
    .replace("ss", pad(second, 2));
}

export function addMonths(date: Date, amount: number) {
  const newDate = cloneDate(date);
  return moment(newDate).add(amount, "months").toDate();
}

export function dayOfWeek() {
  return moment().days();
}


export function getWeekDs(timestamp: number): string {
  const date = new Date(timestamp);
  const day = date.getDay();
  // 将周日(0)转换为7,其他天数减1,这样周一就是0,周日是6
  const adjustedDay = day === 0 ? 7 : day;
  const weekDs = moment(date).subtract(adjustedDay - 1, "days").format("YYYYMMDD");
  return weekDs;
}
