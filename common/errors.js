/**
 *  Customize Common Errors
 */

var inherits = require('util').inherits;
var Errors = {};

function CustomError() {}
require('util').inherits(CustomError, Error);



//make error object can call JSON.stringify
Object.defineProperty(CustomError.prototype, 'toJSON', {
  value: function () {
    var alt = {};

    Object.getOwnPropertyNames(this).forEach(function (key) {
      alt[key] = this[key];
    }, this);

    return alt;
  },
  configurable: true,
  writable: true
});



Errors.CustomError = CustomError;



/**
 * ENTITY_NOT_FOUND
 * @param {string} entityTable
 * @param {string} entityId
 * @returns {Object} entityNotFound
 */
Errors.EntityNotFound = function EntityNotFound(entityTable, entityId) {
  this.entityTable = entityTable;
  this.entityId = entityId;
  Error.captureStackTrace(this, Errors.ENTITY_NOT_FOUND);
};

Errors.UserAlreadyFollowed = function UserAlreadyFollowed(followUserId) {
  this.followUserId = followUserId;
  Error.captureStackTrace(this, Errors.UserAlreadyFollowed);
};


Errors.UserFollowSelf = function UserFollowSelf(userId) {
  this.userId = userId;
  Error.captureStackTrace(this, Errors.UserFollowSelf);
};

Errors.NoPermissionToViewAlbum = function NoPermissionToViewAlbum(userId, albumId) {
  this.userId = userId;
  this.albumId = albumId;
  Error.captureStackTrace(this, Errors.NoPermissionToViewAlbum);
};

Errors.ForbidModify = function ForbidModify(type, id) {
  this.type = type;
  this.id = id;
  Error.captureStackTrace(this, Errors.ForbidModify);
};

Errors.BadArgument = function BadArgument() {
};


Object.keys(Errors).forEach(function(key) {
  errorType = Errors[key];
  if(errorType !== CustomError) {
    inherits(errorType, CustomError);
  }
});

Errors.isCustomError = function(err) {
  return err instanceof CustomError;
};

Errors.ErrorTypes = {
  InvalidOperation: "InvalidOperation",
  AlbumIdInvalid: "AlbumIdInvalid",
  PhotoIdInvalid: "PhotoIdInvalid"
};

module.exports = Errors;
