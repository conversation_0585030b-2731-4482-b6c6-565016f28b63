// 角色基本信息
var Q = require('q'),
    db = require('./db'),
    util = require('./util'),
    redis = require('./redis'),
    logger = require('./logger'),
    config = require('./config');

function CacheBase (option) {
    option = option || {};

    this.tbKey = option.tbKey;
    this.tbName = option.tbName;
    this.tbCols = option.tbCols;

    this.cacheKey = option.cacheKey;
    this.cacheTime = config.ONE_DAY_SECONDS * (option.cacheDay || 1);
}
module.exports = CacheBase;

CacheBase.prototype.query = function (cacheId, option, getItem) {
    option = option || {};

    var self = this;
    var filter = {};
    filter[self.tbKey] = cacheId;
    return db.query({
        table: self.tbName,
        cols: option.cols || getColList(self.tbCols),
        filter: filter,
        conn: option.conn
    });

    function getColList(cols) {
        var list = [];
        for (var key in cols) {
            list.push(key);
        }
        return list;
    }
};

CacheBase.prototype.queryAll = function(cacheIdList, option) {
  const _ = require('lodash');
  const ids = _.uniq(_.compact(_.map(cacheIdList, _.toNumber)));
  if(_.isEmpty(ids)) {
    return Promise.resolve([]);
  } else {
    return this.query(ids, option);
  }
};

CacheBase.prototype.update = function (filter, values, option) {
    option = option || {};
    if (option.cacheOnly) {  // 不需要执行db操作
        return;
    }

    if (typeof filter !== 'object') {
        var filterVal = filter;
        filter = {};
        filter[this.tbKey] = filterVal;
    }
    values = util.unifyKeys(values, this.tbCols);

    var self = this;
    var cacheId = filter[this.tbKey];
    return this.query(cacheId, option).then(function (item) {    // update之前先query，确保redis中的初始数据完整
        if (option.cacheOnly) {  // 不需要执行db操作
            self.updateCache(cacheId, values);
            return;
        }

        item = item[0];
        option.hookVal && option.hookVal(values, item);
        if (item) {
            return self.doUpdate(filter, values, option);
        } else if (option.push || option.addOnDup) {
            option.create && (values[option.create] = Date.now());
            return self.doAdd(util.extend(values, filter), option);
        }

        return {};
    });
};

CacheBase.prototype.updateAll = function(cacheIdList, values, option) {
    var promises = [];
    for (var i = 0, l = cacheIdList.length; i < l; i++) {
        promises.push(this.update(cacheIdList[i], values, option));
    }
    return Q.all(promises).then(function (results) {
        return util.unifyUpResults(results);
    });
};

CacheBase.prototype.doUpdate = function (filter, values, option) {
    var self = this;
    var cacheId = filter[this.tbKey];
    return db.update({
        table: this.tbName,
        values: values,
        filter: filter,
        conn: option.conn
    }).then(function (result) {
        result.affectedRows && self.updateCache(cacheId, values);
        return result;
    });
};

CacheBase.prototype.doAdd = function (values, option) {
    var self = this;
    var cacheId = values[this.tbKey];
    var method = option.addOnDup ? db.addOnDup : db.add;

    return method({
        table: this.tbName,
        values: values,
        conn: option.conn
    }).then(function (result) {
        self.updateCache(cacheId, values);
        return result;
    });
};

CacheBase.prototype.updateCache = function (cacheId, values) {
};