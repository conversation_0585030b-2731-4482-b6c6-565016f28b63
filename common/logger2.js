"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.imageAuditLogger = exports.slow3rdLogger = exports.slowReqLogger = exports.slowSqlLogger = exports.getLogger = exports.createLogger = void 0;
const Bunyan = require("bunyan");
const fs = require("fs");
const path = require("path");
const _ = require("lodash");
const config = require("./config");
const util_1 = require("./util");
const bunyanPoPoAlert_1 = require("./bunyanPoPoAlert");
const config_1 = require("../pyq-server/common/config");
const LogConfig = config.log || {
    level: "info",
    printInConsole: false,
    schema: "com.netease.leihuo.ccc.base.model.tables.v1.L10MdLog",
};
if (config.testCfg.test_env) {
    LogConfig.level = "debug";
}
const LogDir = config.LOG_PATH || path.join(__dirname, "/../log");
const logPath = (fileName) => path.join(LogDir, fileName);
function FileStream(option) {
    this.name = option.name;
}
// eslint-disable-next-line @typescript-eslint/no-explicit-any
FileStream.prototype.write = function (rawRec) {
    const fullName = this.name + ".log";
    const rec = Object.assign({}, rawRec);
    rec.schema = LogConfig.schema || "";
    rec.ts = (0, util_1.formatDate)(new Date(), "yyyy-MM-dd HH:mm:ss");
    fs.appendFile(logPath(fullName), JSON.stringify(rec) + "\n", (err) => {
        if (err) {
            console.error("FileStreamWriteError", err);
        }
    });
};
function createLogger(params) {
    params = _.defaultsDeep(params, {
        serializers: {
            req: Bunyan.stdSerializers.req,
            res: Bunyan.stdSerializers.res,
            err: Bunyan.stdSerializers.err,
        },
    });
    return new Bunyan(params);
}
exports.createLogger = createLogger;
function getLogger(name) {
    const streams = [
        {
            level: LogConfig.level || "info",
            type: "raw",
            stream: new FileStream({ name: name }),
        },
        {
            level: "error",
            type: "raw",
            stream: new FileStream({ name: name + "_error" }),
        },
    ];
    if (LogConfig.printInConsole && process.env.NODE_ENV !== "production") {
        streams.push({
            level: "debug",
            type: null,
            stream: process.stdout,
        });
    }
    if (config_1.bunyanPoPoAlertCfg.enable) {
        const cfg = config_1.bunyanPoPoAlertCfg;
        let atUids = "";
        if (cfg.atUids) {
            atUids = cfg.atUids.join(",");
        }
        streams.push({
            level: cfg.level || "error",
            type: "raw",
            stream: new bunyanPoPoAlert_1.BunyanPoPoAlertStream({
                webhookUrl: cfg.webhookUrl,
                secretKey: cfg.secretKey,
                project: cfg.project,
                biz: cfg.biz,
                env: cfg.env,
                minNotifyInterval: cfg.minNotifyInterval,
                atUids,
                onError: (err) => {
                    console.error(err);
                },
            }),
        });
    }
    return createLogger({
        name: name,
        streams: streams,
    });
}
exports.getLogger = getLogger;
exports.slowSqlLogger = getLogger("slowSql");
exports.slowReqLogger = getLogger("slowReq");
exports.slow3rdLogger = getLogger("slow3rdApi");
exports.imageAuditLogger = getLogger("image_audit");
//# sourceMappingURL=logger2.js.map