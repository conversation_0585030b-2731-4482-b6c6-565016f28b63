"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.loginCheck = exports.Action = void 0;
const _ = require('lodash');
const Request = require('./request.js');
const Config = require('./config.js');
const util_1 = require("../common/util");
const logger_1 = require("../audit-server/logger");
function genSignature(secretKey, params) {
    let str = '';
    let keys = _.keys(params).sort();
    _.each(keys, (k) => {
        str += k + (params[k] || '');
    });
    str += secretKey;
    return (0, util_1.hexMd5)(str).toLowerCase();
}
var Action;
(function (Action) {
    Action[Action["Normal"] = 0] = "Normal";
    Action[Action["Suspicion"] = 10] = "Suspicion";
    Action[Action["Deadly"] = 20] = "Deadly";
})(Action = exports.Action || (exports.Action = {}));
function loginCheck(option) {
    return __awaiter(this, void 0, void 0, function* () {
        let cfg = Config.YI_DUN_ANTI_SPAM;
        if (!cfg.enable) {
            return true;
        }
        let pwdHash = (0, util_1.hexMd5)(option.password + cfg.PWD_SALT);
        let extData = JSON.stringify({ password: pwdHash });
        let data = {
            version: '200',
            secretId: cfg.SECRET_ID,
            businessId: cfg.BUSINESS_ID,
            timestamp: (Math.round(Date.now() / 1000) + ''),
            nonce: Math.random() + '',
            token: option.token || '',
            account: option.username,
            email: option.username,
            ip: option.ip,
            extData: extData
        };
        //@ts-ignore
        data.signature = genSignature(cfg.SECRET_KEY, data);
        return Request.request({
            url: cfg.CHECK_URL,
            method: 'POST',
            form: data,
            timeout: 30000
        }).then(function (res) {
            let code = res.code;
            let msg = res.msg;
            if (code === 200) {
                let result = res.result || {};
                let action = result.action;
                if (action === Action.Normal) {
                    return true;
                }
                else if (action === Action.Suspicion) {
                    return true;
                }
                else if (action === Action.Deadly) {
                    logger_1.apiLogger.error('YD_CHECK_Deadly', { res: res, option: option });
                    return false;
                }
            }
            else {
                logger_1.apiLogger.error('YD_CHECK_API_FAIL', { code: code, msg: msg, option: option });
                return false;
            }
        }).catch(function (err) {
            logger_1.apiLogger.error('YD_CHECK_TIMEOUT', { err: err, option: option });
            return true;
        });
    });
}
exports.loginCheck = loginCheck;
//# sourceMappingURL=yidun.js.map