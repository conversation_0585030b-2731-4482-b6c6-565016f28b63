import * as util from "util";
import * as crypto from "crypto";
import { IncomingMessage, OutgoingHttpHeaders } from "http";
import * as request from "request";
import { csvStrToArray } from "./util";

export enum BunyanLevel {
  trace = 10,
  debug = 20,
  info = 30,
  warn = 40,
  error = 50,
  fatal = 60,
}

class BaseError<T> extends Error {
  constructor(name: string, private context: T) {
    super();
    this.name = name;
  }
}

class HttpRequestError extends Error {
  constructor(
    name: string,
    private url: string,
    private context: {
      reqBody?: object;
      statusCode: number;
      headers: OutgoingHttpHeaders;
      body?: string;
    }
  ) {
    super();
    this.name = name || "HttpRequestError";
  }
}

export namespace BunyanPoPoAlert {
  export type Env = "test" | "preview" | "release";
  export type Level = "error" | "warn" | "info";
  export type MsgRequest = PopoMsgRequest;

  export type BackupChannelMsg = {
    /** 发送人，如果是群，则填群号，其他的则corp邮箱，多个人逗号分隔，支持群和corp一起 */
    to: string;
    /** 消息内容，如果是群消息，并且指定了下面2个参数，需要在内容中加上@的内容 */
    msg: string;
  };

  export type formatterContext = Pick<PopoMsgRequest, "biz" | "project" | "env">;
}

interface PopoMsgRequest {
  /*: 业务名 eg: jinjiebao【长度限制128】须为字母或数字组合 */
  biz: string;
  /**  环境标识 eg: test [preview, test, release] */
  env: BunyanPoPoAlert.Env | string;
  /* 消息级别 eg: info [error,warn,info] */
  level: BunyanPoPoAlert.Level;
  /**   需要发送的消息文本【长度限制2700，预留300用于标识消息出处】 */
  msg: string;
  /**项目标识 eg: d90【长度限制128】须为字母或数字组合 */
  project: string;
  /**  消息接收者【长度限制128】error、warn不填，info必填 */
  receiver?: string;
  /**  当前时间戳【单位/毫秒】 */
  timestamp: number;
  // at用户列表
  atUids: string;
  // 是否at所有人
  atAll: boolean;
}

const noop = () => {};
function defaultFormatter<T extends { msg: string; level: BunyanLevel }>(
  record: T,
  levelName: keyof typeof BunyanLevel,
  context: BunyanPoPoAlert.formatterContext
) {
  const maxAllowMsgLength = 2048;
  const levelStr = levelName.toLocaleUpperCase();
  const lines = [`主题：【警报】 业务 ${context.project}-${context.biz}-${context.env} 异常`];
  lines.push(`级别：${levelStr}`);
  lines.push(`详情：${util.inspect(record)}`);
  let text = lines.join("\n");
  if (text.length > maxAllowMsgLength) {
    text = text.slice(0, maxAllowMsgLength) + "...";
  }
  return {
    text: text,
  };
}

export class BunyanPoPoAlertStream<T extends { msg: string; level: BunyanLevel }> {
  secretKey: string;
  webhookUrl: string;
  project: PopoMsgRequest["project"];
  biz: PopoMsgRequest["biz"];
  env: PopoMsgRequest["env"];
  atUids: PopoMsgRequest["atUids"];
  atAll?: PopoMsgRequest["atAll"];
  minNotifyInterval: number;
  timeout?: number;
  customFormatter: (
    record: T,
    levelName: keyof typeof BunyanLevel,
    context: BunyanPoPoAlert.formatterContext
  ) => { text: string };
  onError: (err: unknown) => void;
  private lastNotifyTime: number;

  constructor(
    options: Omit<BunyanPoPoAlertStream<T>, "write" | "customFormatter" | "onError"> & {
      customFormatter?: BunyanPoPoAlertStream<T>["customFormatter"];
      onError?: BunyanPoPoAlertStream<T>["onError"];
    }
  ) {
    if (!options.webhookUrl) {
      throw new BaseError("InvalidArgument", { webhookUrl: this.webhookUrl });
    }
    this.webhookUrl = options.webhookUrl;
    this.secretKey = options.secretKey;
    this.biz = options.biz;
    this.project = options.project;
    this.env = options.env;
    this.customFormatter = options.customFormatter || defaultFormatter;
    this.minNotifyInterval = options.minNotifyInterval || 60;
    this.timeout = options.timeout || 5000;
    this.lastNotifyTime = 0;
    this.onError = options.onError || noop;
    (this.atAll = options.atAll === true), (this.atUids = options.atUids);
  }

  private md5(content: string) {
    return crypto.createHash("md5").update(content).digest("hex");
  }

  private genToken(params: PopoMsgRequest): string {
    const keys = Object.keys(params).sort();
    let signStr = keys.map((k) => params[k]).join("");
    signStr = signStr + this.secretKey;
    return this.md5(signStr);
  }

  private updateLastNotifyTime() {
    this.lastNotifyTime = Date.now();
  }

  private isShouldRateLimit(): boolean {
    return Date.now() - this.lastNotifyTime < this.minNotifyInterval * 1000;
  }

  private toPoPoLevel(level: BunyanLevel): PopoMsgRequest["level"] {
    if (level === BunyanLevel.error || level === BunyanLevel.fatal) {
      return "error";
    } else if (level === BunyanLevel.warn) {
      return "warn";
    } else {
      return "info";
    }
  }

  async write(record: object) {
    if (this.isShouldRateLimit()) {
      return this.onError(new BaseError("AlarmRateLimited", record));
    }
    try {
      const parsedRecord = typeof record === "string" ? (JSON.parse(record) as T) : (record as T);
      const levelName = BunyanLevel[parsedRecord.level] as keyof typeof BunyanLevel;

      const content = this.customFormatter(parsedRecord, levelName, {
        project: this.project,
        biz: this.biz,
        env: this.env,
      });
      const atUids = this.atUids;
      if (!this.atAll && atUids) {
        const atStr = csvStrToArray(this.atUids)
          .map((at) => `@${at}`)
          .join(" ");
        content.text = content.text + "\n" + atStr;
      }
      const level = this.toPoPoLevel(parsedRecord.level);
      const reqBody: PopoMsgRequest = {
        atAll: this.atAll,
        project: this.project,
        biz: this.biz,
        env: this.env,
        atUids: this.atUids,
        msg: content.text,
        level,
        timestamp: Date.now(),
      };
      try {
        await this.sendNotification(reqBody);
      } catch (err) {
        this.onError(err);
      }
    } catch (err) {
      this.onError(err);
    } finally {
      this.updateLastNotifyTime();
    }
  }

  private async sendNotification(reqBody: PopoMsgRequest) {
    const [res, body] = await this.sendPoPoMsg(reqBody);
    if (body && body.code !== 0) {
      throw new HttpRequestError("ApiReturnCodeError", this.webhookUrl, {
        statusCode: res.statusCode,
        headers: res.headers,
        body: JSON.stringify(body),
      });
    }
  }

  private async sendPoPoMsg(reqBody: PopoMsgRequest) {
    return new Promise<[IncomingMessage, { code: number }]>((resolve, reject) => {
      const token = this.genToken(reqBody);
      request.post(
        {
          url: this.webhookUrl,
          timeout: this.timeout,
          json: true,
          body: reqBody,
          headers: {
            token: token,
          },
        },
        function (err, res, body) {
          if (err) {
            reject(err);
          }
          resolve([res, body]);
        }
      );
    });
  }
}
