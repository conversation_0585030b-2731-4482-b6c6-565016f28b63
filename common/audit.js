"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.addtransact = exports.batchCmd = exports.sendPic = exports.genPicIdFromInfo = void 0;
/* eslint-disable no-var */
/* eslint-disable @typescript-eslint/no-var-requires */
const auditPic_1 = require("../components/audit/auditPic");
const delayTasks_1 = require("../cron-jobs/delayTasks");
const util2_1 = require("./util2");
// 初始化接口
const Q = require("q");
const db = require("./db");
const logger = require("./logger");
const config = require("./config");
const dataUtil = require("./data");
const tableCfg = config.TABLE_CFG;
const profile = require("../service/qnm/pyq/profile");
const ProfileCache = require("../service/qnm/cache/Profile");
const PyqMoments = require("../models/PyqMoments");
const HotMomentsCache = require("../service/qnm/pyq/HotMomentsCache");
function genPicIdFromInfo(info) {
    return auditPic_1.AuditPicService.genPicIdFromInfo(info);
}
exports.genPicIdFromInfo = genPicIdFromInfo;
function sendPic(product, files, args) {
    return auditPic_1.AuditPicService.sendPic(product, files, args);
}
exports.sendPic = sendPic;
function batchCmd(params) {
    const { dispatch } = require("../audit-server/batchCmds/dispatch");
    const cmd = params.cmd, roleId = params.playerId, serverId = params.serverId, roleIds = params.playerIds, roleIdList = roleIds ? roleIds.split(";") : [];
    const _ = require("lodash");
    if (!cmd || (!roleIdList.length && !roleId)) {
        throw "参数错误2";
    }
    logger.add("L10_batchCmd", JSON.stringify(params)); // 审核回调日志
    const duration = params.duration;
    if (/^disable/.test(cmd) && !duration) {
        throw "参数错误3";
    }
    const page = params.page || 0;
    const pageSize = params.pageSize || 100;
    switch (cmd) {
        // get
        case "getsign":
            return ProfileCache.queryAll(roleIdList, { cols: ["RoleId", "Signature"] });
        case "getlocation":
            return ProfileCache.queryAll(roleIdList, { cols: ["RoleId", "Location"] });
        case "getmessage":
            return db.query({
                page: page,
                pageSize: pageSize,
                table: tableCfg.qnm.pyq_message,
                filter: { RoleId: roleId, Status: [dataUtil.Constants.STATUS_NORMAL, dataUtil.Constants.STATUS_READ] },
            });
        // del
        case "delphoto":
            return ProfileCache.updateAll(roleIdList, { PhotoAudit: dataUtil.Constants.STATUS_AUDIT_REJECT });
        case "delsign":
            return ProfileCache.updateAll(roleIdList, { Signature: null });
        case "dellocation":
            return ProfileCache.updateAll(roleIdList, { Location: null });
        // pyq 心情相关
        case "uphotmoment": //上热门
            return (function () {
                return __awaiter(this, void 0, void 0, function* () {
                    let date = new Date(Date.now() + 3600 * 24 * 1000);
                    if (params.downTime)
                        date = new Date(params.downTime);
                    const momentIds = _.split(params.momentIds, ";");
                    if (!momentIds) {
                        throw `参数错误: momentIds为空`;
                    }
                    else {
                        //
                    }
                    yield delayTasks_1.PyqDelayQueue.addJob({
                        id: "" + momentIds,
                        topic: delayTasks_1.DELAY_TOPICS.DownHotMoment,
                        body: { momentIds: momentIds, serverId: serverId },
                        runAt: date.getTime(),
                    });
                    return HotMomentsCache.upHotMoments(momentIds, serverId, null);
                });
            })();
        case "downhotmoment": //下热门
            return (function () {
                const momentIds = _.split(params.momentIds, ";");
                if (!momentIds) {
                    throw `参数错误: momentIds为空`;
                }
                return HotMomentsCache.downHotMoments(momentIds, serverId, null);
            })();
        case "delcomment":
            var targetId = params.targetId;
            var commentIds = params.commentIds;
            if (!targetId || !commentIds) {
                throw "参数错误5";
            }
            return db
                .query({
                table: tableCfg.qnm.pyq_moment,
                cols: ["ID"],
                filter: { RoleId: targetId },
                pageSize: 1000,
            })
                .then(function (results) {
                const momentIdList = [];
                for (let i = 0, l = results.length; i < l; i++) {
                    momentIdList.push(results[i].ID);
                }
                if (!momentIdList.length) {
                    throw "未找到" + targetId + "的动态记录";
                }
                const filter = { RoleId: roleId, TargetId: momentIdList };
                if (commentIds !== "all") {
                    filter["ID"] = commentIds.split(";");
                }
                db.update({
                    table: tableCfg.qnm.pyq_comment,
                    values: { Status: dataUtil.Constants.STATUS_REJECT },
                    filter: filter,
                });
                const PyqMomentService = require("../service/qnm/pyq/moment");
                PyqMomentService.refreshCommentsCache(commentIds);
                PyqMoments.updateHotStateWhenDeleteComments(targetId, commentIds);
            });
        case "delmessage":
            var messageIds = params.messageIds;
            if (!messageIds) {
                throw "参数错误6";
            }
            var filter = { RoleId: roleId };
            if (messageIds !== "all") {
                filter["ID"] = messageIds.split(";");
            }
            return db.update({
                table: tableCfg.qnm.pyq_message,
                values: { Status: dataUtil.Constants.STATUS_REJECT },
                filter: filter,
            });
        case "delimg":
            var urls = params.urls;
            var momentId = params.momentId;
            if (!urls || !momentId) {
                throw "参数错误7";
            }
            return db.push({
                table: tableCfg.qnm.pyq_moment,
                values: { ImgAudit: null },
                filter: { ID: momentId, roleId: roleId },
            }, {
                hookVal: function (values, exist) {
                    let imgList = exist && exist.ImgList;
                    if (!exist || !imgList) {
                        throw "动态" + momentId + "不存在";
                    }
                    const imgAudit = (exist.ImgAudit || "").split(",");
                    imgList = imgList ? imgList.split(",") : [];
                    urls = ";" + urls + ";";
                    for (let i = 0, l = imgList.length; i < l; i++) {
                        imgAudit[i] =
                            urls.indexOf(imgList[i]) >= 0
                                ? dataUtil.Constants.STATUS_AUDIT_REJECT
                                : imgAudit[i] || dataUtil.Constants.STATUS_AUDIT_INIT;
                    }
                    values.ImgAudit = imgAudit.join(",");
                },
            });
        case "disablesign":
            return setRoleBanState(roleIdList, "Signature", duration);
        case "enablesign":
            return setRoleBanState(roleIdList, "Signature", 0);
        case "disablephoto":
            return setRoleBanState(roleIdList, "Photo", duration);
        case "enablephoto":
            return setRoleBanState(roleIdList, "Photo", 0);
        case "disablelocation":
            return setRoleBanState(roleIdList, "Location", duration);
        case "enablelocation":
            return setRoleBanState(roleIdList, "Location", 0);
        case "disablemoment":
            return setRoleBanState(roleIdList, "Moment", duration);
        case "enablemoment":
            return setRoleBanState(roleIdList, "Moment", 0);
        case "disablemessage":
            return setRoleBanState(roleIdList, "Message", duration);
        case "enablemessage":
            return setRoleBanState(roleIdList, "Message", 0);
        case "disablehotmoment":
            return setRoleBanState(roleIdList, "HotMoment", duration);
        case "enablehotmoment":
            return setRoleBanState(roleIdList, "HotMoment", 0);
        case "disableEquipComment":
            return setRoleBanState(roleIdList, "EquipComment", duration);
        case "enableEquipComment":
            return setRoleBanState(roleIdList, "EquipComment", 0);
        case "delmdalbum":
            filter = { UserId: roleId };
            var albumIds = params.albumIds;
            if (!albumIds) {
                throw "参数错误11";
            }
            if (albumIds !== "all") {
                filter.ID = albumIds.split(";");
            }
            return db.update({
                table: "md_photo_album",
                values: { AuditStatus: dataUtil.Constants.STATUS_AUDIT_REJECT },
                filter: filter,
            });
        default:
            return dispatch(cmd, params);
    }
}
exports.batchCmd = batchCmd;
function setRoleBanState(roleIdList, state, duration) {
    const promises = [];
    for (let i = 0, l = roleIdList.length; i < l; i++) {
        promises.push(profile.setBanState(roleIdList[i], state, duration));
    }
    return Q.all(promises).then(function (results) {
        return (0, util2_1.unifyUpResults)(results);
    });
}
function addtransact(data) {
    if (!data || !data.role_id || !data.role_name || !data.dt || !data.order || !data.order.pid) {
        return;
    }
    const roleId = data.role_id, roleName = data.role_name, transactTime = data.dt, productId = data.order.pid;
    if (!roleId) {
        return;
    }
    let status = 0;
    if (data.ErrMsg && data.ErrMsg.indexOf("SKPayment_transaction_failed") >= 0) {
        //充值失败
        status = dataUtil.Constants.STATUS_DELETE;
    }
    db.add({
        table: tableCfg.qnm.transact_hisroties,
        values: {
            RoleId: roleId,
            RoleName: roleName,
            TransactTime: transactTime,
            ProductId: productId,
            Status: status,
        },
    });
}
exports.addtransact = addtransact;
//# sourceMappingURL=audit.js.map