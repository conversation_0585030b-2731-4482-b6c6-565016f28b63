"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.convertBoolVal = exports.base64Encode = exports.getLangFromReqAndSession = exports.getLangFromRequest = exports.escapeLikeStr = exports.getSubArrayByPage = exports.unifyUpResults = exports.isNullOrUndefined = exports.getMobileOperatingSystem = exports.appendQueryStringToUrl = exports.getUnixTimeStamp = exports.getIpBehindProxy = exports.cacheKeyGen = exports.getIp = exports.validateUrl = exports.hasQuery = exports.getPageMeta = exports.uuidV1 = exports.zipArrayWithMerge = exports.keyToMapArray = exports.keyToRecordMap = exports.isAllMatch = exports.removeQueryString = exports.transformObjectForHttps = exports.transformObject = exports.contains = exports.getServerIdFromRoleId = exports.replaceHttps = exports.mergeIds = exports.fillCsvStr = exports.assignForEmpty = exports.getFirstXForwardedForIp = exports.isValidCallbackName = void 0;
const _ = require("lodash");
const uuid = require("node-uuid");
const url = require("url");
const util_1 = require("./util");
const serverRegion_1 = require("../pyq-server/serverRegion");
function isValidCallbackName(name) {
    return /^[0-9a-zA-Z_-]+$/.test(name);
}
exports.isValidCallbackName = isValidCallbackName;
function getFirstXForwardedForIp(ipStr) {
    const ipList = (0, util_1.csvStrToArray)(ipStr);
    return ipList[0];
}
exports.getFirstXForwardedForIp = getFirstXForwardedForIp;
// 当值为空的时候才覆盖
function assignForEmpty(obj, supply) {
    for (const k of Object.keys(obj)) {
        if (!obj[k] && supply[k]) {
            obj[k] = supply[k];
        }
    }
    return obj;
}
exports.assignForEmpty = assignForEmpty;
function fillCsvStr(char, len) {
    const csvStr = new Array(len).fill(char).join(",");
    return csvStr;
}
exports.fillCsvStr = fillCsvStr;
function mergeIds(arr1, arr2) {
    const set = new Set();
    for (const e of arr1) {
        set.add(e);
    }
    for (const e of arr2) {
        set.add(e);
    }
    const ret = Array.from(set);
    return ret;
}
exports.mergeIds = mergeIds;
function replaceHttps(url) {
    if (url) {
        return url.replace(/^http:\/\//, "https://");
    }
    else {
        return "";
    }
}
exports.replaceHttps = replaceHttps;
function getServerIdFromRoleId(roleId) {
    const server = _.trimStart(_.toString(roleId).slice(-4), "0");
    const serverId = parseInt(server, 10);
    return serverId;
}
exports.getServerIdFromRoleId = getServerIdFromRoleId;
function contains(arr, ele) {
    const set = new Set(arr);
    return set.has(ele);
}
exports.contains = contains;
function transformObject(src, transformer) {
    if (_.isArray(src)) {
        const arr = src;
        for (let i = 0; i < arr.length; i++) {
            arr[i] = transformObject(arr[i], transformer);
        }
        return arr;
    }
    else if (_.isObject(src)) {
        const props = Object.keys(src);
        for (const p of props) {
            src[p] = transformObject(src[p], transformer);
        }
        return src;
    }
    else {
        return transformer(src);
    }
}
exports.transformObject = transformObject;
function transformObjectForHttps(src) {
    return transformObject(src, function (ele) {
        if (_.isString(ele)) {
            return replaceHttps(ele);
        }
        else {
            return ele;
        }
    });
}
exports.transformObjectForHttps = transformObjectForHttps;
function removeQueryString(str) {
    const urlObj = url.parse(str);
    return urlObj.protocol + "//" + urlObj.host + urlObj.pathname;
}
exports.removeQueryString = removeQueryString;
function isAllMatch(arr, predict) {
    return !_.some(arr, (ele) => !predict(ele));
}
exports.isAllMatch = isAllMatch;
function keyToRecordMap(records, key) {
    const map = new Map();
    for (const r of records) {
        if (r && !isNullOrUndefined(r[key])) {
            map.set(r[key], r);
        }
    }
    return map;
}
exports.keyToRecordMap = keyToRecordMap;
function keyToMapArray(records, key) {
    const map = new Map();
    if (!_.isString(key))
        throw new Error("转换map的键值必须为字符串");
    for (const item of records) {
        const temp = map.get(item[key]);
        if (!temp) {
            map.set(item[key], [item]);
            continue;
        }
        temp.push(item);
        map.set(item[key], temp);
    }
    return map;
}
exports.keyToMapArray = keyToMapArray;
function zipArrayWithMerge(arr, brr) {
    const minLen = Math.min(arr.length, brr.length);
    const list = [];
    for (let i = 0; i < minLen; i++) {
        const item = Object.assign({}, arr[i], brr[i]);
        list.push(item);
    }
    return list;
}
exports.zipArrayWithMerge = zipArrayWithMerge;
function uuidV1() {
    const id = uuid.v1().replace(/-/g, "");
    return id;
}
exports.uuidV1 = uuidV1;
function getPageMeta(pagination, totalCount) {
    const totalPage = Math.ceil(totalCount / pagination.pageSize);
    const curPage = Math.min(pagination.page, totalPage);
    return { curPage: curPage, totalPage: totalPage, totalCount: totalCount };
}
exports.getPageMeta = getPageMeta;
function hasQuery(u) {
    const urlObj = url.parse(u);
    return !!urlObj.query;
}
exports.hasQuery = hasQuery;
function validateUrl(url) {
    return /^(?:(?:(?:https?|ftp):)?\/\/)(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)(?:\.(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)*(?:\.(?:[a-z\u00a1-\uffff]{2,})))(?::\d{2,5})?(?:[/?#]\S*)?$/i.test(url);
}
exports.validateUrl = validateUrl;
function getIp(req) {
    return (getFirstXForwardedForIp(req.headers["x-forwarded-for"]) ||
        req.headers["x-real-ip"] ||
        req.connection.remoteAddress ||
        req.socket.remoteAddress ||
        req.connection.socket.remoteAddress);
}
exports.getIp = getIp;
function cacheKeyGen(kp, cacheObj) {
    const coKey = Object.keys(cacheObj)
        .filter((k) => !isNullOrUndefined(cacheObj[k]))
        .map((k) => _.snakeCase(k) + "_" + cacheObj[k])
        .join(":");
    const key = kp + ":" + coKey;
    return key;
}
exports.cacheKeyGen = cacheKeyGen;
function getIpBehindProxy(req) {
    const forwardForIpList = (0, util_1.csvStrToArray)(req.headers["x-forwarded-for"]);
    if (forwardForIpList && forwardForIpList.length > 0) {
        return forwardForIpList[0];
    }
    if (req.headers["x-real-ip"]) {
        return req.headers["x-real-ip"];
    }
    return req.connection.remoteAddress || req.socket.remoteAddress || req.connection.socket.remoteAddress;
}
exports.getIpBehindProxy = getIpBehindProxy;
/** 获取时间戳, 单位s */
function getUnixTimeStamp() {
    return Math.ceil(Date.now() / 1000);
}
exports.getUnixTimeStamp = getUnixTimeStamp;
function appendQueryStringToUrl(url, qs) {
    const urlObj = new URL(url);
    for (const name of Object.keys(qs)) {
        urlObj.searchParams.append(name, qs[name]);
    }
    return urlObj.toString();
}
exports.appendQueryStringToUrl = appendQueryStringToUrl;
/**
 * Determine the mobile operating system.
 * This function returns one of 'iOS', 'Android', 'Windows Phone', or 'unknown'.
 *
 * @returns {String}
 */
function getMobileOperatingSystem(userAgent) {
    // Windows Phone must come first because its UA also contains "Android"
    if (/windows phone/i.test(userAgent)) {
        return "Windows Phone";
    }
    if (/android/i.test(userAgent)) {
        return "Android";
    }
    // iOS detection from: http://stackoverflow.com/a/9039885/177710
    if (/iPad|iPhone|iPod/.test(userAgent)) {
        return "iOS";
    }
    return "unknown";
}
exports.getMobileOperatingSystem = getMobileOperatingSystem;
function isNullOrUndefined(val) {
    return val === null || val === undefined;
}
exports.isNullOrUndefined = isNullOrUndefined;
function unifyUpResults(results) {
    const result = {};
    for (let i = 0, l = results.length; i < l; i++) {
        const item = results[i];
        for (const k in item) {
            const v = item[k];
            typeof v === "number" && (result[k] = (result[k] || 0) + v);
        }
    }
    return result;
}
exports.unifyUpResults = unifyUpResults;
function getSubArrayByPage(array, page, pageSize) {
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    if (startIndex >= array.length) {
        return []; // 超出数组大小，返回空数组
    }
    return array.slice(startIndex, Math.min(endIndex, array.length));
}
exports.getSubArrayByPage = getSubArrayByPage;
function escapeLikeStr(likeStr) {
    return likeStr.replace(/[_%]|\\/g, function (escapeChar) {
        return "\\" + escapeChar;
    });
}
exports.escapeLikeStr = escapeLikeStr;
function getLangFromRequest(req) {
    return getLangFromReqAndSession(req, req.session);
}
exports.getLangFromRequest = getLangFromRequest;
function getLangFromReqAndSession(req, sessionArg) {
    let lang = "cn";
    const session = sessionArg || req.session;
    if (session) {
        if (session.language) {
            lang = session.language;
        }
    }
    else {
        // 只在越南区域的时候才会根据header的accept-language来判断
        if ((0, serverRegion_1.isVnRegion)()) {
            if (req.headers["accept-language"]) {
                const langFromHeader = req.headers["accept-language"].split(",")[0];
                if (langFromHeader) {
                    lang = langFromHeader;
                }
            }
        }
    }
    return lang;
}
exports.getLangFromReqAndSession = getLangFromReqAndSession;
function base64Encode(str) {
    if (!str)
        return "";
    return Buffer.from(str).toString("base64");
}
exports.base64Encode = base64Encode;
function convertBoolVal(flag) {
    if (typeof flag === "string") {
        return flag === "true";
    }
    else {
        return flag;
    }
}
exports.convertBoolVal = convertBoolVal;
//# sourceMappingURL=util2.js.map