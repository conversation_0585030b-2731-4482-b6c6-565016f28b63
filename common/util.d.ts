declare namespace util {
  function getJsonInfo<T>(str: string, defaultValue?: T): T;
  function hexMd5(content: string, option?: any): string;
  function formatDate(timestamp: number | Date, format?: string): string;
  function mkDirsSync(dirRoot: string, subPath: string, mode?: string): void;
  function extend<T, R>(dest: T, src: R): T & R;
  function response(data: any, code?: number, msg?: string): { code: number; msg?: string; data: any };
  function getIp(req): string;
  function checkToken(content: Buffer, params: any, option?: any): boolean;
  function addToList(id: string, oldStr: string, maxNum?: number): string;
  function rmvFromList(id: string, oldStr: string): string;
  function csvStrToArray(string: string): string[];
  function csvStrToIntArray(string: string, delimiter?): number[];
  function keysToCamelCase(obj: object): object;
  function keyToRecordHash<T>(record: T[], key: string): { string: T };
  function toLowerCaseKey(item: any, recursive?: boolean): any;
  function currying<T>(fn: T, ...rest): T;
  function pad(str: number | string, len: number): string;
  function trim(str: string): string;
  function removeEmoji(str: string): string;
  function curl(string, option: any): any;
}

export = util;
