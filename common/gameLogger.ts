
/***
雷火游戏JSON日志规范
https://confluence.leihuo.netease.com/pages/viewpage.action?pageId=15624022
***/


import * as fs from 'fs'
import * as path from 'path'
import * as DateUtil from './dateUtil'
import { mkDirsSync } from './util'

const LOG_TIME_FORMAT = 'yyyy-MM-dd HH:mm:ss'

interface LogItem {
    logTime: Date
    operation: string
    data: LogData
}

interface LogData {

}


//  lineProtocol= [logtime][ operation],{key1=value1,key2=value2,key3=value3}
// [ 2013-04-11 00:00:09 ][OnlineRoleNum],{ “ time ” :12344343, ”username””:” yemingjiang ” }
function formatLineProtocol(params: LogItem) {
    let logTime = DateUtil.format(params.logTime, LOG_TIME_FORMAT)
    let recordJson = JSON.stringify(params.data)
    return `[${logTime}][${params.operation}],${recordJson}\n`
}

function writeLog(filePath: string, line: string) {
    fs.appendFile(filePath, line, function (err) {
        if (err) {
            console.error(err)
        }
    })
}


export abstract class GameLogger {
    private logName: string
    private printInConsole: boolean

    constructor(option: { logName: string, printInConsole?: boolean, createLogDir?: boolean }) {
        this.logName = option.logName
        this.printInConsole = option.printInConsole || false
        if (option.createLogDir) {
            mkDirsSync(this.getLogDir(), '')
        }
    }


    info(operation: string, data: LogData) {
        let lineStr = formatLineProtocol({ logTime: new Date(), operation: operation, data: data })
        let fullLogName = this.formatLogName(this.logName)
        let logPath = path.join(this.getLogDir(), fullLogName)
        if (this.printInConsole) {
            console.info(lineStr)
        }
        return writeLog(logPath, lineStr)
    }

    protected formatLogName(name: string) {
        let dateStr = DateUtil.format(new Date(), 'yyyyMMdd')
        return `${name}_${dateStr}_utf8.log`
    }

    protected abstract getLogDir(): string
}