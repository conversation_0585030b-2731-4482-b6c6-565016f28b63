/**
 * Created by zhen<PERSON> on 2017/3/30.
 */

const request = require('./request');
const util = require('./util');
const os = require('os');
const _ = require('lodash');

const PAO_PAO_MSG_URL = "http://***********:10013/redmine/popomsg4tf.php";
const DEFAULT_RECEIVER = ["hzwangzhenhua"];
const hostname = os.hostname();

const PaoPaoAlarm = module.exports = {
  formatMsgObj: function (msgObj) {
    msgObj.time = msgObj.time || util.formatDate(Date.now());
    msgObj.hostname = hostname;
    let msg = "";
    _.forEach(msgObj, (value, key) => {
      if(!_.isString(value)) {
        value = JSON.stringify(value);
      }
      msg = msg + key + " : " + value + "\n";
    });
    return msg;
  },

  _send: function (msg, to) {
    const key = util.hexMd5("ThunderFire" + msg);
    to = to || DEFAULT_RECEIVER;
    return request.post(PAO_PAO_MSG_URL, {
      msg: msg,
      to: to.join(','),
      key: key,
    });
  },

  sendMsg: function (str, to) {
    const callerName = _.get(arguments, 'callee.caller.name') || "anonymous";
    return PaoPaoAlarm.sendMsg({callerName: callerName, msg:str}, to);
  },

  sendJson: function (obj, to) {
    obj.callerName = _.get(arguments, 'callee.caller.name') || "anonymous";
    const msg = PaoPaoAlarm.formatMsgObj(obj);
    return PaoPaoAlarm._send(msg, to);
  }
};
