"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.gameIpLimit = exports.cleanGlobalAuth = exports.skipLevelCheckMiddleware = exports.skipSkeyAuthMiddleware = exports.addGetPostRoute = exports.addRoute = exports.isIn64SpecialPeriod = exports.preUseWhenUrlStartsWith = exports.useWhenUrlStartsWith = void 0;
const moment = require("moment");
const config_1 = require("../common/config");
const ipAuth_1 = require("../pyq-server/middlewares/ipAuth");
const config_2 = require("../pyq-server/common/config");
function useWhenUrlStartsWith(app, urlPrefix, middleware) {
    app.use(function (req, res, next) {
        var _a;
        let path = ((_a = req.route) === null || _a === void 0 ? void 0 : _a.path) || req.url;
        if (path && typeof path === 'string' && path.startsWith(urlPrefix)) {
            middleware(req, res, next);
        }
        else {
            next();
        }
    });
}
exports.useWhenUrlStartsWith = useWhenUrlStartsWith;
function preUseWhenUrlStartsWith(app, urlPrefix, middleware) {
    app.pre(function (req, res, next) {
        let path = req.url;
        if (path.startsWith(urlPrefix)) {
            middleware(req, res, next);
        }
        else {
            next();
        }
    });
}
exports.preUseWhenUrlStartsWith = preUseWhenUrlStartsWith;
const preUseWhenUrlPrefix = preUseWhenUrlStartsWith;
function isIn64SpecialPeriod(date) {
    return moment(date).isBetween(config_1.SpecialPeriodFor64.startDate, config_1.SpecialPeriodFor64.endDate);
}
exports.isIn64SpecialPeriod = isIn64SpecialPeriod;
function addRoute(app, methods, route, routeCallback, ...routeCallbacks) {
    for (let m of methods) {
        app[m](route, routeCallback, ...routeCallbacks);
    }
}
exports.addRoute = addRoute;
function addGetPostRoute(app, route, routeCallback, ...routeCallbacks) {
    return addRoute(app, ["get", "post"], route, routeCallback, ...routeCallbacks);
}
exports.addGetPostRoute = addGetPostRoute;
function skipSkeyAuthMiddleware(req, res, next) {
    req.skipSkey = true;
    next();
}
exports.skipSkeyAuthMiddleware = skipSkeyAuthMiddleware;
function skipLevelCheckMiddleware(req, res, next) {
    req.skipLevel = true;
    next();
}
exports.skipLevelCheckMiddleware = skipLevelCheckMiddleware;
function cleanGlobalAuth(app, prefix) {
    return __awaiter(this, void 0, void 0, function* () {
        preUseWhenUrlPrefix(app, prefix, skipSkeyAuthMiddleware);
        preUseWhenUrlPrefix(app, prefix, skipLevelCheckMiddleware);
    });
}
exports.cleanGlobalAuth = cleanGlobalAuth;
exports.gameIpLimit = (0, ipAuth_1.ipLimitMiddleware)(config_2.gameServerIps);
//# sourceMappingURL=helper.js.map