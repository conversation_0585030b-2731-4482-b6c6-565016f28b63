"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sess = exports.generateSkey = exports.stop = exports.getSkey = exports.LoginSession = exports.start = exports.LoginPayload = exports.sessionId = void 0;
// 登录校验
const Q = require("q");
const jwt = require("jwt-simple");
const config = require("./config");
const session = require("./session");
const secret = config.JWT_TOKEN_SECRET;
const HEADER = jwt.encode("", secret).split(".")[0] + ".";
const commonLogger_1 = require("./commonLogger");
const logger = commonLogger_1.commonLogger.child({ clazz: "/common/auth" });
function sessionId(idType, id) {
    const sessKey = idType + ":" + id;
    return sessKey;
}
exports.sessionId = sessionId;
class LoginPayload {
    static toObj(info) {
        const [time, idType, idVal, account] = info;
        return {
            time,
            idType,
            idVal,
            account,
        };
    }
    static fromPayload(payload) {
        return [payload.time, payload.idType, payload.idVal, payload.account];
    }
}
exports.LoginPayload = LoginPayload;
function start(idType, params, sessData) {
    const time = sessData.time || "" + Date.now();
    const idVal = getIdVal(idType, params);
    const account = sessData.account || "";
    const payload = [time, idType, idVal, account];
    const sKey = jwt.encode(payload, secret);
    sessData.time = time;
    const sessKey = sessionId(idType, idVal);
    return session.set(sessKey, sessData).then(function () {
        session.setExpire(sessKey, config.ONE_DAY_SECONDS * (idType === "role" ? 60 : 1)); // 设置session过期时间
        return { skey: sKey.replace(HEADER, "") };
    });
}
exports.start = start;
class LoginSession {
    static get(roleId) {
        return __awaiter(this, void 0, void 0, function* () {
            const info = yield session.get(sessionId("role" /* Role */, roleId));
            return info;
        });
    }
    static set(roleId, sessData) {
        return __awaiter(this, void 0, void 0, function* () {
            const info = yield start("role" /* Role */, { roleid: roleId }, sessData);
            return info;
        });
    }
    static del(roleId) {
        return __awaiter(this, void 0, void 0, function* () {
            return session.rmv(sessionId("role" /* Role */, roleId));
        });
    }
}
exports.LoginSession = LoginSession;
function getSkey(time, idType, idVal) {
    const sKey = jwt.encode([time, idType, idVal], secret);
    return sKey.replace(HEADER, "");
}
exports.getSkey = getSkey;
function stop(idType, params) {
    return session.rmv(idType + ":" + getIdVal(idType, params));
}
exports.stop = stop;
function generateSkey(payload) {
    const sKey = jwt.encode(LoginPayload.fromPayload(payload), secret);
    return sKey.replace(HEADER, "");
}
exports.generateSkey = generateSkey;
exports.sess = {
    _check_: function (req) {
        const sessionInfo = req._SESS_INFO_;
        if (!sessionInfo) {
            return Q.fcall(function () {
                return null;
            });
        }
        return sessionInfo.id;
    },
    setVal: function (req, key, val) {
        const result = this._check_(req);
        return typeof result === "string" ? session.setVal(result, key, val) : result;
    },
    getVal: function (req, key) {
        const result = this._check_(req);
        return typeof result === "string" ? session.getVal(result, key) : result;
    },
    delVal: function (req, key) {
        const result = this._check_(req);
        return typeof result === "string" ? session.delVal(result, key) : result;
    },
};
function getIdVal(idType, params) {
    return params[idType + "id"];
}
//# sourceMappingURL=auth.js.map