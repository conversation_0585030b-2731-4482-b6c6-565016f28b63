"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SET_OPTION = exports.IExistResult = exports.ExpireType = exports.GeoUnit = void 0;
var GeoUnit;
(function (GeoUnit) {
    GeoUnit["ml"] = "ml";
    GeoUnit["km"] = "km";
    GeoUnit["ft"] = "ft";
    GeoUnit["mi"] = "mi";
})(GeoUnit = exports.GeoUnit || (exports.GeoUnit = {}));
var ExpireType;
(function (ExpireType) {
    ExpireType["EX"] = "EX";
    ExpireType["PX"] = "PX";
})(ExpireType = exports.ExpireType || (exports.ExpireType = {}));
var IExistResult;
(function (IExistResult) {
    IExistResult[IExistResult["Exist"] = 1] = "Exist";
    IExistResult[IExistResult["NotExist"] = 0] = "NotExist";
})(IExistResult = exports.IExistResult || (exports.IExistResult = {}));
var SET_OPTION;
(function (SET_OPTION) {
    SET_OPTION["NX"] = "NX";
    SET_OPTION["XX"] = "XX";
})(SET_OPTION = exports.SET_OPTION || (exports.SET_OPTION = {}));
//# sourceMappingURL=redisTypes.js.map