"use strict";
const config_all_1 = require("./config.all");
const APP_HOST = "http://***************";
const config = (0, config_all_1.initConfig)({}, APP_HOST);
// 配置文件
config.testCfg.skip_fetch_nos_image_size = true; // 打开时获取聊天图片的时候由不在返回nos查询的图片尺寸
config.NOS_CFG.accessKey = "";
config.NOS_CFG.secretKey = "";
config.JWT_TOKEN_SECRET = ""; // jwt token 生成密钥
config.AUTH_TOKEN_SALT = ""; // 游戏服务端登录等认证用token salt
config.TOKEN_SALT_DEL = ""; // 删除大事记用token salt
config.TOKEN_SALT = ""; // 客户端推送数据验证用token salt
config.DIGEST_KEY = ""; // 图片路径加密密钥
module.exports = config;
//# sourceMappingURL=config.example.js.map