/* eslint-disable prefer-rest-params */
/* eslint-disable @typescript-eslint/no-this-alias */
import * as bluebird from "bluebird";
import * as _ from "lodash";
import * as redis from "redis";
import * as config from "./config";
import { getLogger } from "./logger2";
import { ExtendRedisClient } from "./redis";
const logger = getLogger("redis_logger");

type IRedisCfg = typeof config.REDIS_CFG;

const redisCfg = config.REDIS_CFG as IRedisCfg;
const geoRedisCfg = (config.GEO_REDIS_CFG as IRedisCfg) || redisCfg;

bluebird.promisifyAll(redis.RedisClient.prototype, {
  promisifier: promisifier,
});

bluebird.promisifyAll(redis.Multi.prototype, {
  promisifier: promisifier,
});

function promisifier(originalMethod) {
  return function promisified() {
    const args = [].slice.call(arguments);
    const self = this;
    return new Promise(function (resolve, reject) {
      args.push(function (err, data) {
        if (err) {
          reject(err);
        } else {
          resolve(data);
        }
      });
      originalMethod.apply(self, args);
    }).catch((err) => {
      const debugArgs = args.filter((ele) => !_.isFunction(ele));
      logger.error("RedisCommendError", { method: originalMethod.name, args: debugArgs });
      throw err;
    });
  };
}

function getRedisClient(config: IRedisCfg) {
  const client = redis.createClient(config) as ExtendRedisClient;
  return client;
}

class RedisClass {
  private instance: ExtendRedisClient;
  private redisConfig: IRedisCfg;

  constructor(config: IRedisCfg) {
    this.instance = null;
    this.redisConfig = config;
  }

  retry_strategy() {
    this.instance = null;
  }

  getInstance() {
    const redisConfig = _.assign({}, this.redisConfig, { retry_strategy: this.retry_strategy.bind(this) });
    if (this.instance === null) {
      this.instance = getRedisClient(redisConfig);
    }
    return this.instance;
  }
}

class RedisPool {
  private pool: RedisClass[];

  constructor(config: IRedisCfg) {
    this.pool = [];
    const hosts = config.hosts || [config.host];
    for (const h of hosts) {
      const cfg: IRedisCfg = Object.assign({}, config, { host: h });
      const rc = new RedisClass(cfg);
      this.pool.push(rc);
    }
  }

  getInstance() {
    const idx = _.random(0, this.pool.length - 1);
    return this.pool[idx].getInstance();
  }
}

const redisPool = new RedisPool(redisCfg);

export function getRedis() {
  return redisPool.getInstance();
}

export const redisClient = getRedis();

const GeoRedisPool = new RedisPool(geoRedisCfg);

export function getGeoRedis() {
  return GeoRedisPool.getInstance();
}

redisClient.on("error", function (err) {
  logger.error({ err }, "RedisClientError");
});

if (config.testCfg.redis_debug) {
  const monitorClient = redisClient.duplicate();
  monitorClient.monitor(function (err) {
    if (err) {
      logger.error(err);
    } else {
      logger.info("RedisClient: Entering monitoring mode.");
    }
  });

  monitorClient.on("monitor", function (time, args, rawReply) {
    const commend = _.upperCase(args[0]);
    const commendArgs = _.tail(args)
      .filter((r) => {
        return !_.isFunction(r);
      })
      .join(" ");
    if (commend !== "PING") {
      logger.info("RedisClient" + ": " + commend + " " + commendArgs);
    }
  });
}
