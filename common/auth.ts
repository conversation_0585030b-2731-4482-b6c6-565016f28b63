// 登录校验
import * as Q from "q"
import * as _ from "lodash"
import * as jwt from "jwt-simple"
import * as config from "./config"
import * as session from "./session"

const secret = config.JWT_TOKEN_SECRET
const HEADER = jwt.encode("", secret).split(".")[0] + "."
import { commonLogger } from "./commonLogger"
import { AuthLoginIdType, LoginJwtPayload, LoginJwtPayloadTuple, LoginSessionData } from "../pyq-server/types/type"
const logger = commonLogger.child({ clazz: "/common/auth" })

export function sessionId(idType: AuthLoginIdType, id: number | string) {
  const sessKey = idType + ":" + id
  return sessKey
}

export class LoginPayload {
  static toObj(info: LoginJwtPayloadTuple): LoginJwtPayload {
    const [time, idType, idVal, account] = info
    return {
      time,
      idType,
      idVal,
      account,
    }
  }

  static fromPayload(payload: LoginJwtPayload): LoginJwtPayloadTuple {
    return [payload.time, payload.idType, payload.idVal, payload.account]
  }
}

export function start(idType: AuthLoginIdType, params, sessData: LoginSessionData) {
  const time = sessData.time || "" + Date.now()
  const idVal = getIdVal(idType, params)
  const account = sessData.account || ""
  const payload: LoginJwtPayloadTuple = [time, idType, idVal, account]
  const sKey = jwt.encode(payload, secret)
  sessData.time = time
  const sessKey = sessionId(idType, idVal)
  return session.set(sessKey, sessData).then(function () {
    session.setExpire(sessKey, config.ONE_DAY_SECONDS * (idType === "role" ? 60 : 1)) // 设置session过期时间
    return { skey: sKey.replace(HEADER, "") }
  })
}

export class LoginSession {
  static async get(roleId: number): Promise<LoginSessionData> {
    const info = await session.get(sessionId(AuthLoginIdType.Role, roleId))
    return info as unknown as LoginSessionData
  }

  static async set(roleId: number, sessData: LoginSessionData) {
    const info = await start(AuthLoginIdType.Role, { roleid: roleId }, sessData)
    return info
  }

  static async del(roleId: number) {
    return session.rmv(sessionId(AuthLoginIdType.Role, roleId))
  }
}

export function getSkey(time, idType, idVal) {
  const sKey = jwt.encode([time, idType, idVal], secret)
  return sKey.replace(HEADER, "")
}

export function stop(idType, params) {
  return session.rmv(idType + ":" + getIdVal(idType, params))
}


export function generateSkey(payload: LoginJwtPayload) {
  const sKey = jwt.encode(LoginPayload.fromPayload(payload), secret)
  return sKey.replace(HEADER, "")
}


export const sess = {
  _check_: function (req) {
    const sessionInfo = req._SESS_INFO_
    if (!sessionInfo) {
      return Q.fcall(function () {
        return null
      })
    }
    return sessionInfo.id
  },
  setVal: function (req, key, val) {
    const result = this._check_(req)
    return typeof result === "string" ? session.setVal(result, key, val) : result
  },
  getVal: function (req, key) {
    const result = this._check_(req)
    return typeof result === "string" ? session.getVal(result, key) : result
  },
  delVal: function (req, key) {
    const result = this._check_(req)
    return typeof result === "string" ? session.delVal(result, key) : result
  },
}

function getIdVal(idType: AuthLoginIdType, params: { roleid: number }) {
  return params[idType + "id"]
}