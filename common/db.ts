import { dbSlowLogCfg } from "../pyq-server/common/config";
import { getLogger, slowSqlLogger } from "./logger2";
// mysql数据库连接通用类
import * as assert from "assert";
import * as _ from "lodash";
import * as mysql from "mysql";
import * as Q from "q";
import * as config from "./config";
import * as util from "./util";

const dbLogger = getLogger("db");

function extendDBConfig(config) {
  /*兼容问题， 之前配置的key名字没和mysql模块保持一致*/
  if (config.pwd && !config.password) {
    config.password = config.pwd;
  }
  if (config.db && !config.database) {
    config.database = config.db;
  }

  const defaultOption = {
    connectionLimit: 25,
    // 1分钟后恢复连接
    restoreNodeTimeout: 60000,
  };
  return _.defaults(config, defaultOption);
}

//使用once确保只是被调用一次
const getPoolCluster = _.once(function getPoolCluster() {
  const masterConfig = extendDBConfig(config.DB_CFG);
  const poolCluster = mysql.createPoolCluster();
  poolCluster.add("MASTER", masterConfig);

  if (config.SLAVE_DB_CFG) {
    const slaveConfig = extendDBConfig(config.SLAVE_DB_CFG);
    poolCluster.add("SLAVE", slaveConfig);
  }
  return poolCluster;
});

const poolCluster = getPoolCluster();

const masterPool = poolCluster.of("MASTER");

export function query(args, opt?) {
  const table = args.table,
    cols = args.cols ? args.cols.join(",") : "*",
    condition = args.filter ? " where " + getCondition(args.filter) : "",
    order = args.order ? " order by " + getOrder(args.order) : "",
    group = args.group ? " group by " + args.group : "",
    pageSize = args.pageSize || 50,
    start = (args.page || 0) * pageSize;

  const sql =
    "select " +
    cols +
    " from " +
    table +
    condition +
    group +
    order +
    (args.pageSize ? " limit " + start + "," + pageSize : "") +
    (args && args.conn ? " for update" : "") +
    ";"; // 事务中的select语句统一加锁，防止并发请求数据冲突
  return execSql(sql, args);
}

export function add(args, onDup?) {
  const table = args.table,
    values = args.values,
    keyArr = [],
    valArr = [];
  for (const key in values) {
    const rawVal = values[key];
    keyArr.push(key);
    valArr.push(getSqlVal(rawVal));
  }

  const sql =
    "insert" +
    (args.ignore ? " ignore" : "") +
    " into `" +
    table +
    "` (" +
    keyArr.join(",") +
    ") values (" +
    valArr.join(",") +
    ")" +
    (onDup ? " on duplicate key update " + mysql.escape(onDup) : "") +
    ";";
  return execSql(sql, args);
}

export function addBatch(args, opt) {
  let table = args.table,
    values = args.values,
    itemArr = [],
    keyArr = [],
    valArr = [],
    item = values[0];
  for (const key in item) {
    keyArr.push(key);
    valArr.push(getSqlVal(item[key]));
  }
  itemArr.push("(" + valArr.join(",") + ")");

  for (let i = 1, l = values.length; i < l; i++) {
    valArr = [];
    item = values[i];
    for (let j = 0, k = keyArr.length; j < k; j++) {
      valArr.push(getSqlVal(item[keyArr[j]]));
    }
    itemArr.push("(" + valArr.join(",") + ")");
  }

  const sql = "insert into `" + table + "` (" + keyArr.join(",") + ") values " + itemArr.join(",") + ";";
  return execSql(sql, args);
}

export function del(args, opt?) {
  const table = args.table,
    condition = " where " + getCondition(args.filter);

  const sql = "delete from `" + table + "`" + condition + ";";
  return execSql(sql, args);
}

export function update(args, opt?) {
  const table = args.table,
    condition = args.filter ? " where " + getCondition(args.filter) : "",
    values = args.values,
    updArr = [];

  for (const key in values) {
    const rawVal = values[key];
    const val = getSqlVal(rawVal);
    updArr.push(key + "=" + val);
  }

  const sql = "update `" + table + "` set " + updArr.join(",") + condition + ";";
  return execSql(sql, args);
}

export function push(args, opt) {
  const option = util.extend({ holdClient: true }, args);
  return query(option).then(function (results) {
    const exists = results.length;
    const hookVal = opt && opt.hookVal;
    hookVal && hookVal(args.values, results[0]);
    return exists ? update(args) : add(mergeArgs(args));
  });
}

export function addOnDup(args, opt?) {
  return add(mergeArgs(args), args.values);
}

function mergeArgs(args) {
  args.values = args.values || {};
  for (const key in args.filter) {
    args.values[key] = args.filter[key];
  }
  args.create && (args.values[args.create] = Date.now());

  return args;
}

function getCondition(filter, isOr?) {
  const condition = [];
  for (const key in filter) {
    let str = "";
    const val = filter[key];
    const type = Object.prototype.toString.call(val);
    if (type === "[object Null]") {
      str = key;
    } else if (type === "[object Array]") {
      str = val.length === 1 ? key + "=" + getSqlVal(val[0]) : key + " in (" + getSqlVal(val) + ")";
    } else if (type === "[object Object]") {
      str = "(" + getCondition(val, true) + ")";
    } else {
      str = key + "=" + getSqlVal(val);
    }
    str && condition.push(str);
  }
  return condition.join(isOr ? " or " : " and ");
}

function getOrder(order) {
  const orders = [];
  for (const key in order) {
    const orderVal = order[key];
    orders.push(key + (orderVal ? " " + orderVal : ""));
  }
  return orders.join(",");
}

function getSqlVal(val) {
  if (val === null) {
    return "null";
  }

  const type = Object.prototype.toString.call(val);
  switch (type) {
    case "[object Undefined]":
      return "null";
    case "[object Object]":
      return getCondition(val);
  }

  return mysql.escape(val);
}

export function execSql(sql, option) {
  const defer = Q.defer();
  option = option || {};
  const conn = option.conn || masterPool;
  const timeStart = Date.now();
  conn.query(sql, function (err, results, fields) {
    const timeEnd = Date.now();
    const duration = timeEnd - timeStart;
    if (config.testCfg.db_debug) {
      dbLogger.info({ sql }, "ExecuteSql");
    }
    if (dbSlowLogCfg.enable && duration >= dbSlowLogCfg.threshold) {
      slowSqlLogger.warn({ sql, duration: `${duration}ms` }, "DetectSlowSql");
    }
    if (err) {
      dbLogger.error({ err: err, sql: sql }, "ExecuteSqlError");
      option.conn ? defer.reject(err) : assert.ifError(err);
    }
    defer.resolve(results || []);
  });

  return defer.promise;
}

export function transact(transactions, option?) {
  const defer = Q.defer();

  masterPool.getConnection(function (err, conn) {
    assert.ifError(err);

    conn.beginTransaction(function (err) {
      assert.ifError(err);
      runNext();
    });

    function runNext(result?) {
      const query = transactions.shift();
      if (query) {
        try {
          query(conn, result).then(runNext).catch(onFinal);
        } catch (ex) {
          onFinal(ex);
        }
        return;
      }

      conn.commit(function (err) {
        onFinal(err, result);
      });
    }

    function onFinal(err, result?) {
      if (err) {
        conn.rollback();
      }
      conn.release();
      err ? defer.reject(err) : defer.resolve(result);
    }
  });

  return defer.promise;
}

/**
 * execute sql
 *
 * @param {String} sql
 * @param {Object} option
 * @returns {Object} rows
 */
export function execute(sql, option?) {
  option = _.defaults(option, { dbNode: "MASTER" }); //默认使用Master节点
  if (!config.SLAVE_DB_CFG && option.dbNode === "SLAVE") {
    option.dbNode = "MASTER"; //slave配置不存在的时候重新指回master节点
  }
  const startTime = Date.now();
  const queryPool = poolCluster.of(option.dbNode);
  return new Promise<mysql.PoolConnection>((resolve, reject) => {
    queryPool.getConnection((err, conn) => {
      if (err) {
        reject(err);
      } else {
        resolve(conn);
      }
    });
  }).then((conn) => {
    return new Promise((resolve, reject) => {
      const readyRunTime = Date.now();
      conn.query(sql, function (err, rows) {
        conn.release();
        const finishTime = Date.now();
        const runDuration = finishTime - readyRunTime;
        const waitDuration = readyRunTime - startTime;
        const duration = waitDuration + runDuration;
        if (config.testCfg.db_debug) {
          dbLogger.info({ sql, duration, waitDuration, runDuration }, "ExecuteSql");
        }
        if (dbSlowLogCfg.enable && duration >= dbSlowLogCfg.threshold) {
          slowSqlLogger.warn({ sql, duration, waitDuration, runDuration }, "MayBeSlowSql");
          if (runDuration >= dbSlowLogCfg.threshold) {
            slowSqlLogger.warn({ sql, duration, waitDuration, runDuration }, "DetectSlowSql");
          }
        }

        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  });
}

export function transactByKnexQuerys(knexQuerys) {
  const querys = _.map(knexQuerys, (knexQuery) => {
    return (conn) => {
      return new Q.Promise((resolve, reject) => {
        const sql = knexQuery.toString();
        config.testCfg.db_debug && dbLogger.info({ sql }, "ExecuteSql");
        conn.query(sql, (err, rows) => {
          if (err) {
            reject(err);
          } else {
            resolve(rows);
          }
        });
      });
    };
  });
  return transact(querys);
}
