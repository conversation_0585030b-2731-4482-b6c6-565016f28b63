/* eslint-disable prefer-promise-reject-errors */
const captchaService = {}
const request = require('./request')
const _ = require('lodash')
const crypto = require('crypto')
const Promise = require('bluebird')

function noncer () {
  return _.random(0, 100000000)
}

function genSignature (secretKey, params) {
  const sortKeys = _.sortBy(Object.keys(params))
  const needSignatureStr = _.flatMap(sortKeys, key => [key, params[key]]).join('') + secretKey
  return crypto.createHash('md5').update(needSignatureStr, 'UTF-8').digest('hex')
}

captchaService.verify = function (validate) {
  if (_.isEmpty(validate)) {
    return Promise.reject({errorType: 'captchaCodeEmpty', msg: '验证码不能为空'})
  }
  const api = 'http://c.dun.163yun.com/api/v2/verify'
  const config = captchaService.getConfig()
  const postData = {
    captchaId: config.captchaId,
    validate: validate,
    user: '',
    secretId: config.secretId,
    version: 'v2',
    timestamp: Date.now(),
    nonce: noncer()
  }
  postData.signature = genSignature(config.secretKey, postData)
  return request.post(api, postData)
    .then(captchaService.verifyResHandler)
}

captchaService.verifyResHandler = function (resData) {
  if (resData.error === 0) { // 无异常
    if (resData.result) {
      return true
    } else {
      return Promise.reject({errorType: 'captchaVerifyFailed', msg: '验证码校验不通过'})
    }
  } else {
    return Promise.reject({errorType: 'NeDunServiceError', errorCode: resData.error, msg: resData.msg})
  }
}

captchaService.getConfig = function () {
  return {
    captchaId: '9f808f737ed6429ebe500c638b68db2d',
    secretId: 'a9685a920f620953a14e0c5468b6d10e',
    secretKey: 'bbfdda67822d442351b29bc8057da43d'
  }
}

const TextCheckService = {}

TextCheckService.Config = {
  businessId: 'e9cb7bbd5a6dd625dd4d827eb6155acd',
  secretId: '59373a477ece5f5753bfdbd5f42d9735',
  secretKey: 'c8cc9ce02265dd56084b03198bb14c15'
}

/**
 * check text
 * @param {Object} params params
 * @param {String} params.account account
 * @param {String} params.dataId uniq text identify
 * @param {String} params.content check text content
 * @returns {Promise} api check response
 */
TextCheckService.check = function (params) {
  const config = TextCheckService.Config
  const now = new Date().getTime()
  const api = 'https://as.dun.163yun.com/v3/text/check'
  const postData = {
    secretId: config.secretId,
    businessId: config.businessId,
    version: 'v3.1',
    timestamp: now,
    nonce: noncer(),
    dataId: params.dataId,
    content: params.content
  }
  postData.signature = genSignature(config.secretKey, postData)
  return request.post(api, postData)
}

module.exports = {
  captchaService: captchaService,
  textCheckService: TextCheckService
}
