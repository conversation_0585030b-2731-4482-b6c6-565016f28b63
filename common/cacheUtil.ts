import * as bluebird from "bluebird";
import { getJsonInfo } from "../common/util";
import { ExpireType, getRedis, SET_OPTION } from "./redis";
import { isNullOrUndefined } from "./util2";
import _ = require("lodash");

export abstract class GenericCache<T, F> {
  public async get(params: T): Promise<F> {
    const str = await getRedis().getAsync(this.getKey(params));
    if (this.isNeedRefresh(str)) {
      const result = await this.refresh(params);
      return result;
    } else {
      const result: F = getJsonInfo(str);
      return result;
    }
  }

  protected isNeedRefresh(str: string) {
    return isNullOrUndefined(str);
  }

  getExpireTime(params: T) {
    return null;
  }

  abstract getKey(params: T): string;

  abstract fetchDataSource(params: T): Promise<F>;

  async refresh(params: T): Promise<F> {
    const content = await this.fetchDataSource(params);
    await this.writeToCache(params, content);
    return content;
  }

  async clear(params: T) {
    await getRedis().delAsync(this.getKey(params));
  }

  protected async writeToCache(params: T, content: F) {
    const str = JSON.stringify(content);
    await getRedis().setAsync(this.getKey(params), str);
    const expire = this.getExpireTime(params);
    if (expire !== null) {
      await getRedis().expireAsync(this.getKey(params), expire);
    }
  }
}


export abstract class GenericLazyCache<T, F> extends GenericCache<T, F> {
  // 缓存未命中时先返回默认结果
  abstract getTempResultForCacheMiss(): F;

  public async get(params: T): Promise<F> {
    const str = await getRedis().getAsync(this.getKey(params));
    if (this.isNeedRefresh(str)) {
      const result: F = this.getTempResultForCacheMiss();
      await this.writeToCache(params, result);
      this.refresh(params);
      return result;
    } else {
      const result: F = JSON.parse(str);
      return result;
    }
  }
}

export class BasicCache<T> {
  protected key: string;

  constructor(key: string) {
    this.key = key;
  }

  protected serialize(ele: T): string {
    return JSON.stringify(ele);
  }

  protected deSerialize(str: string): T {
    return JSON.parse(str);
  }
}

export class FixOrderSetCache<T> extends BasicCache<T> {
  private size: number;

  constructor(key: string, size: number) {
    super(key);
    this.size = size;
  }

  async add(ele: T) {
    const str = this.serialize(ele);
    await getRedis().zaddAsync(this.key, Date.now(), str);
    await getRedis().zremrangebyrankAsync(this.key, 0, -(this.size + 1));
  }

  async getAll(): Promise<T[]> {
    const list = await getRedis().zrevrangeAsync(this.key, 0, -1);
    return list.map((x) => this.deSerialize(x));
  }
}

export class AllowOnceCache {
  constructor(private key: string, private expire: number) {}
  async isAllow(): Promise<boolean> {
    const ret = await getRedis().setnxAsync(this.key, "" + Date.now() + this.expire * 1000);
    if (ret) {
      await getRedis().expireAsync(this.key, this.expire);
    }
    return !!ret;
  }
}

type stringOrNumber = string | number;
export class RedisSet<T extends stringOrNumber> {
  private key: string;
  private expire: number;

  constructor(key: string, expire: number) {
    this.key = key;
    this.expire = expire;
  }

  async add(member: T) {
    const addRet = await getRedis().saddAsync(this.key, member);
    const expireRet = await getRedis().expireAsync(this.key, this.expire);
    return { addRet, expireRet };
  }

  async getAll() {
    return getRedis().smembersAsync(this.key);
  }

  async remove(member: T) {
    return getRedis().sremAsync(this.key, member);
  }

  async isMember(member: T) {
    return getRedis().sismemberAsync(this.key, member);
  }

  static create(key: string, expire: number) {
    return new this(key, expire);
  }
}

interface RankItem {
  member: string;
  score: number;
}

export class RankList {
  private key: string;
  private expire: number;

  constructor(key: string, expire: number) {
    this.key = key;
    this.expire = expire;
  }

  async add(member: stringOrNumber, score: number) {
    if (_.isNumber(member)) {
      member = "" + member;
    }
    const addRet = await getRedis().zaddAsync(this.key, score, member);
    const expireRet = await getRedis().expireAsync(this.key, this.expire);
    return { addRet, expireRet };
  }

  async list(size: number): Promise<RankItem[]> {
    const items = await getRedis().zrevrangeAsync(this.key, 0, size - 1, "WITHSCORES");
    const list: RankItem[] = [];
    for (let i = 0; i < items.length; i = i + 2) {
      const ele = { member: items[i], score: parseInt(items[i + 1], 10) };
      list.push(ele);
    }
    return list;
  }

  static create(key: string, expire: number) {
    return new this(key, expire);
  }
}

export class OperationInterval {
  static async locked(key: string, interval = 500) {
    const ret = await getRedis().setAsync(key, "1", ExpireType.PX, interval, SET_OPTION.NX);
    return !ret;
  }
}

export class RedisLock {
  static fullKey(key) {
    return key + ":lock";
  }

  static async lock(key: string, ttl: number, ownerId: string): Promise<boolean> {
    const k = this.fullKey(key);
    const ret = await getRedis().setAsync(k, ownerId, ExpireType.PX, ttl, SET_OPTION.NX);
    return !!ret;
  }

  static async unLock(key: string, ownerId: string) {
    const k = this.fullKey(key);
    const ret = await getRedis().getAsync(k);
    if (ret && ret === ownerId) {
      await getRedis().delAsync(k);
      return true;
    }
    return false;
  }

  static async optimistic(key: string, ownerId: string, ttl: number, maxAttempts: number, wait: number) {
    let attempts = 0;
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const self = this;
    async function tryLock() {
      attempts += 1;
      const ret = await self.lock(key, ttl, ownerId);
      if (ret) {
        return ret;
      } else {
        if (attempts >= maxAttempts) {
          return false;
        } else {
          await bluebird.delay(wait);
          const ret = await tryLock();
          return ret;
        }
      }
    }

    const ret = await tryLock();
    return ret;
  }
}

interface Constructor<T> {
  update: () => Promise<T>;
  ttl: number;
}
