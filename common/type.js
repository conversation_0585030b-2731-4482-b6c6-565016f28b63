"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MomentStatus = void 0;
var MomentStatus;
(function (MomentStatus) {
    MomentStatus[MomentStatus["Normal"] = 0] = "Normal";
    MomentStatus[MomentStatus["USER_DEL"] = -1] = "USER_DEL";
    MomentStatus[MomentStatus["ADMIN_DEl"] = -2] = "ADMIN_DEl";
})(MomentStatus = exports.MomentStatus || (exports.MomentStatus = {}));
__exportStar(require("../models/modelInterface"), exports);
//# sourceMappingURL=type.js.map