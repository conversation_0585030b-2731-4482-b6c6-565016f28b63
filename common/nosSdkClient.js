/**
 * Created by z<PERSON><PERSON> on 16-12-20.
 */

/**
 * Please refer sdk document
 * http://public-cloud-doc.nos-eastchina1.126.net/node-sdk.html
 */

let Promise = require('bluebird');
let NosClient = require('nos-node-sdk');
let _ = require('lodash');
const Config = require('../common/config');
let request = require('../common/request')
let nosAuth = require('./nosAuth')

let nosClient = new NosClient();
nosClient.setAccessId(Config.NOS_CFG.accessKey);
nosClient.setSecretKey(Config.NOS_CFG.secretKey);
nosClient.setEndpoint('nos.netease.com');
nosClient.setPort('80');

const API_METHODS_Promisify = [
  'abort_multipart_upload',
  'complete_multipart_upload',
  'delete_objects',
  'delete_object',
  'get_object_stream',
  'get_object_file',
  'head_object',
  'create_multipart_upload',
  'list_multipart_upload',
  'list_objects',
  'list_parts',
  'copy_object',
  'move_object',
  'put_object_stream',
  'put_file',
  'put_big_file',
  'upload_part'
];

/**
 * Generate promise version sdk method, new method name is origin name with _async suffix
 */
API_METHODS_Promisify.forEach(function (methodName) {
  let originFunc = NosClient.prototype[methodName];
  NosClient.prototype[methodName + "_async"] = function () {
    let self = this;
    let args = _.slice(arguments);
    return new Promise(function (resolve, reject) {
      args.push(function (value) {
        resolve(value);
      });
      try {
        originFunc.apply(self, args)
      } catch (err) {
        reject(err);
      }
    })
  };
});

function createNosClient(options) {
  let endPoint = options.endPoint || 'nos.netease.com'
  let port = options.port || 80
  let nosClient = new NosClient();
  nosClient.setAccessId(Config.NOS_CFG.accessKey);
  nosClient.setSecretKey(Config.NOS_CFG.secretKey);
  nosClient.setEndpoint(endPoint);
  nosClient.setPort(port);
  return nosClient
}

let jdNosClient = createNosClient({
  endPoint: 'nos-jd.163yun.com'
})

// 坑爹sdk的方法有bug, 不是在请求上放参数
NosClient.prototype.listObjects = function (params) {
  let bucket = params.bucket
  let qs = {
    'max-keys': params.limit,
    'prefix': params.prefix
  }
  let url = 'http://' + bucket + '.' + this.endpoint
  let headers = {
    'Date': (new Date()).toUTCString(),
  }
  let resource = '/' + params.bucket + '/';
  headers.Authorization = nosAuth(this.accessId, this.secretKey, 'GET', headers, resource)
  return request.get(url, qs, { headers: headers })
    .then(data => {
      return formatListObjects(data)
    })
}


function formatListObjects(xmlStr) {
  let xml2js = require('xml2js')
  let parser = new xml2js.Parser();
  let result = {}
  return new Promise((resolve, reject) => {
    parser.parseString(xmlStr, function (err, obj) {
      if (err) {
        reject(err)
      }
      result['bucketInfo'] = {};
      result['bucketInfo']['name'] = obj['ListBucketResult']['Name'][0];
      result['bucketInfo']['prefix'] = obj['ListBucketResult']['Prefix'][0];
      result['bucketInfo']['marker'] = obj['ListBucketResult']['Marker'][0];
      result['bucketInfo']['maxKeys'] = obj['ListBucketResult']['MaxKeys'][0];
      result['bucketInfo']['is_truncated'] = obj['ListBucketResult']['IsTruncated'][0];
      result['bucketInfo']['objectlist'] = [];
      var contents = obj['ListBucketResult']['Contents'] || [];
      for (var i = 0; i < contents.length; i++) {
        var temp = {};
        temp['key'] = contents[i]['Key'][0];
        temp['etag'] = contents[i]['ETag'][0].replaceAll("\"", "");
        temp['lastmodified'] = contents[i]['LastModified'][0];
        temp['size'] = contents[i]['Size'][0];
        temp['storageclass'] = contents[i]['StorageClass'][0];
        result.bucketInfo.objectlist[i] = temp;
      }
      resolve(result);
    })
  })
}

module.exports = {
  NosClient: NosClient,
  nosClient: nosClient,
  jdNosClient: jdNosClient
};
