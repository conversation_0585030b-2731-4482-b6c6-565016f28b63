import * as qs from 'querystring'
import { isValidCallbackName } from './util2'

export function jsonp() {
    function _jsonp(req, res, next) {
        let q = req.getQuery();

        // If the query plugin wasn't used, we need to hack it in now
        if (typeof (q) === 'string') {
            req.query = qs.parse(q);
        }


        let cb = req.query.callback || req.query.jsonp

        if (cb) {
            if (!isValidCallbackName(cb)) {
                res.send(400, { msg: 'Bad jsonp callback' })
            } else {
                res.setHeader('Content-Type', 'application/javascript');
                res.setHeader('X-Content-Type-Options', 'nosniff');
                next()
            }
        } else {
            next()

        }
    }

    return (_jsonp)
}