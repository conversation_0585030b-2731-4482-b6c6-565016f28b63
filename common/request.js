"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.post = exports.get = exports.requestV2 = exports.request = void 0;
const requestLib = require("request");
const _ = require("lodash");
const config_1 = require("../common/config");
const logger2_1 = require("./logger2");
if (config_1.testCfg.request_debug) {
    //@ts-ignore
    requestLib.debug = true;
}
const DEFAULT_TIMEOUT = 5000;
function request(option) {
    option = _.defaults(option, { json: true, timeout: DEFAULT_TIMEOUT });
    return new Promise(function (resolve, reject) {
        const startTime = Date.now();
        //@ts-ignore
        return requestLib(option, function (err, response, body) {
            const endTime = Date.now();
            const duration = endTime - startTime;
            if (duration > config_1.thirdApiSlowLogCfg.threshold) {
                logger2_1.slow3rdLogger.info({ option, duration }, "DetectSlow3rdApi");
            }
            if (err) {
                reject(err);
            }
            else {
                resolve(body);
            }
        });
    });
}
exports.request = request;
function requestV2(option) {
    option = _.defaults(option, { json: true, timeout: DEFAULT_TIMEOUT });
    return new Promise(function (resolve, reject) {
        const startTime = Date.now();
        //@ts-ignore
        return requestLib(option, function (err, response, body) {
            const endTime = Date.now();
            const duration = endTime - startTime;
            if (duration > config_1.thirdApiSlowLogCfg.threshold) {
                logger2_1.slow3rdLogger.info({ option, duration }, "DetectSlow3rdApi");
            }
            if (err) {
                reject(err);
            }
            else {
                resolve([body, response]);
            }
        });
    });
}
exports.requestV2 = requestV2;
function get(url, data, option) {
    return request(Object.assign({
        url: url,
        method: "GET",
        qs: data,
    }, option || {}));
}
exports.get = get;
function post(url, data, option) {
    return request(Object.assign({
        url: url,
        method: "POST",
        form: data,
    }, option || {}));
}
exports.post = post;
//# sourceMappingURL=request.js.map