"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Formater = void 0;
const _ = require("lodash");
var TaskType;
(function (TaskType) {
    TaskType[TaskType["FILL_NORMAL"] = 1] = "FILL_NORMAL";
    TaskType[TaskType["FILL_ONCE"] = 2] = "FILL_ONCE";
    TaskType[TaskType["PARSE"] = 3] = "PARSE";
})(TaskType || (TaskType = {}));
class Formater {
    constructor(raw) {
        this.AsyncTaskList = [];
        this.SyncTaskList = [];
        this.raw = raw;
    }
    static create(raw) {
        return new Formater(raw);
    }
    fill(key, fetcher, option) {
        this.addTask(key, TaskType.FILL_NORMAL, fetcher, option);
        return this;
    }
    fillOnce(key, fetcher, option) {
        this.addTask(key, TaskType.FILL_ONCE, fetcher, option);
        return this;
    }
    format(key, handler) {
        this.SyncTaskList.push({ key, handler });
        return this;
    }
    addTask(key, type, dataFetcher, option) {
        let task;
        if (type === TaskType.FILL_NORMAL)
            task = new FillTask(key, dataFetcher, option);
        if (type === TaskType.FILL_ONCE)
            task = new FillOnceTask(key, dataFetcher, option);
        this.AsyncTaskList.push(task);
        return this;
    }
    execute() {
        return __awaiter(this, void 0, void 0, function* () {
            let raw = this.raw;
            let promiseList = this.AsyncTaskList.map((task) => __awaiter(this, void 0, void 0, function* () {
                return task.execute(raw);
            }));
            yield Promise.all(promiseList);
            let data = _.map(raw, item => {
                let obj = {};
                let result = _.assign(obj, item);
                this.SyncTaskList.forEach(task => {
                    result[task.key] = task.handler(item[task.key]);
                });
                return result;
            });
            return data;
        });
    }
}
exports.Formater = Formater;
class AsyncTask {
}
class FillTask extends AsyncTask {
    constructor(key, dataFecher, option) {
        super();
        this.key = key;
        this.dataFetcher = dataFecher;
        this.option = option;
    }
    execute(raw) {
        return __awaiter(this, void 0, void 0, function* () {
            let key = this.key;
            let promiseList = raw.map((item) => __awaiter(this, void 0, void 0, function* () {
                return this.dataFetcher(item, this.option);
            }));
            let data = yield Promise.all(promiseList);
            for (let i = 0; i < data.length; i++) {
                raw[i][key] = data[i];
            }
        });
    }
}
class FillOnceTask extends AsyncTask {
    constructor(key, dataFecher, option) {
        super();
        this.key = key;
        this.dataFetcher = dataFecher;
        this.option = option;
    }
    execute(raw) {
        return __awaiter(this, void 0, void 0, function* () {
            let key = this.key;
            let data = yield this.dataFetcher(raw, this.option);
            for (let i = 0; i < data.length; i++) {
                let dataItem = data[i];
                let resultItem = raw[i];
                for (let j in key) {
                    let keyName = key[j];
                    if (dataItem[keyName] !== undefined)
                        resultItem[keyName] = dataItem[keyName];
                    else
                        resultItem[keyName] = null;
                }
            }
        });
    }
}
//# sourceMappingURL=formater.js.map