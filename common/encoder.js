var crypto = require('crypto');
	
function base64edDigest(str) {
    var digestKey = require('./config').DIGEST_KEY;
	var bytes = simpleKeyXOR(str, digestKey, digestKey.length);
	var encode = base64Md5(new Buffer(bytes), true);
	encode = replaceSpecChars(encode);
	
	return encode;
}

function simpleKeyXOR(str, key, len) {
    var arr = getBytes(key);
	var bsrc = getBytes(str);
	for(var i = 0, l = bsrc.length; i < l; i++) {
		bsrc[i] = bsrc[i] ^ (arr[i % len]);
	}
	return bsrc;
}

function base64Md5(content) {
	var md5 = crypto.createHash('md5');
	md5.update(content);
	return md5.digest('base64');
}
		
function replaceSpecChars(str) {
	return str.replace(/\//g, '_').replace(/\+/g, '-');
}

function getBytes(str) {
	var bytes = [];
	for (var i = 0, l = str.length; i < l; i++) {
		bytes.push(str.charCodeAt(i));
	}
	return bytes;       
}

exports.encode = base64edDigest;