"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.jsonp = void 0;
const qs = require("querystring");
const util2_1 = require("./util2");
function jsonp() {
    function _jsonp(req, res, next) {
        let q = req.getQuery();
        // If the query plugin wasn't used, we need to hack it in now
        if (typeof (q) === 'string') {
            req.query = qs.parse(q);
        }
        let cb = req.query.callback || req.query.jsonp;
        if (cb) {
            if (!(0, util2_1.isValidCallbackName)(cb)) {
                res.send(400, { msg: 'Bad jsonp callback' });
            }
            else {
                res.setHeader('Content-Type', 'application/javascript');
                res.setHeader('X-Content-Type-Options', 'nosniff');
                next();
            }
        }
        else {
            next();
        }
    }
    return (_jsonp);
}
exports.jsonp = jsonp;
//# sourceMappingURL=betterJsonp.js.map