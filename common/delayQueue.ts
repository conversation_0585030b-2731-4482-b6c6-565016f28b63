import { getRedis } from './redis'
import {uuidV1} from './util2'

export interface IJob {
    topic: string
    id?: string
    runAt: number
    body: object
}

class JobPool {
    private keyName: string
    constructor(keyName) {
        this.keyName = keyName
    }

  async addJob(job: IJob) {
    let redis = getRedis()
    let jobDetail = JSON.stringify(job)
    await redis.hsetAsync(this.keyName, '' + job.id, jobDetail)
    return job.id
  }

    async getJobById(jobId: string) {
        let redis = getRedis()
        let jobDetail = await redis.hgetAsync(this.keyName, jobId)
        let job: IJob = JSON.parse(jobDetail)
        return job
    }

    async removeJob(jobId: string) {
        let redis = getRedis()
        return redis.hdelAsync(this.keyName, jobId)
    }
}

class DelayBucket {
    private keyName: string

    constructor(keyName) {
        this.keyName = keyName
    }

    async addJob(job: IJob) {
        let redis = getRedis()
        let result = await redis.zaddAsync(this.keyName, job.runAt, job.id)
        return result
    }

    async getTriggerJobIds(now) {
        let redis = getRedis()
        //@ts-ignore
        let jobIds = await redis.zrangebyscoreAsync(this.keyName, 0, now, 'LIMIT', 0, 1) as string[]
        return jobIds
    }

    async removeJob(jobId: string) {
        let redis = getRedis()
        let result = redis.zremAsync(this.keyName, jobId)
        return result
    }
}

class ReadyQueue {
    private keyName: string
    constructor(keyName) {
        this.keyName = keyName
    }

    async add(jobId: string) {
        let redis = getRedis()
        let result = await redis.lpushAsync(this.keyName, jobId)
        return result
    }

    async pop() {
        let redis = getRedis()
        let result = await redis.lpopAsync(this.keyName)
        return result
    }
}

export class DelayQueue {
    private jobPool: JobPool
    private delayBucket: DelayBucket
    private readyQueue: ReadyQueue

    constructor(keyName) {
        this.jobPool = new JobPool(keyName + '_job_pool')
        this.delayBucket = new DelayBucket(keyName + '_delay_bucket')
        this.readyQueue = new ReadyQueue(keyName + '_ready_queue')
    }

    async addJob(job: IJob) {
      if(!job.id) {
        job.id = uuidV1()
      }
      await this.jobPool.addJob(job)
      await this.delayBucket.addJob(job)
      return job.id
    }

    async onTick() {
        let now = Date.now()
        let jobIds = await this.delayBucket.getTriggerJobIds(now)
        for (let id of jobIds) {
            await this.readyQueue.add(id)
            await this.delayBucket.removeJob(id)
        }
    }

    async popReady() {
        let jobId = await this.readyQueue.pop()
        if (jobId) {
            let job = this.jobPool.getJobById(jobId)
            this.jobPool.removeJob(jobId)
            return job
        }
    }
}
