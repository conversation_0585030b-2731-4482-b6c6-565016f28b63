import { execute } from './db'

export enum DBNode {
  Master = 'MASTER',
  Slave = 'SLAVE'
}

interface IExecuteOption {
  dbNode: DBNode
}

export class DBClient {
  private static instance = null

  async execute(sql: string, option?: IExecuteOption): Promise<any> {
    let result = await execute(sql, option)
    return result
  }

  static getInstance() {
    if (!this.instance) {
      this.instance = new DBClient()
    }
    return this.instance
  }
}