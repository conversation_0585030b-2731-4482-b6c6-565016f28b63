import * as redis from './redis'
import { getRedis } from "./redis";

export function set(id, data) {
    data.access = Date.now();
    const promise = redis.setHMKV(getId(id), data);

    return promise;
}

export function get(id) {
    const sessionKey = getId(id)
    return getRedis().hgetallAsync(sessionKey)
        .then(sessionData => {
            if (sessionData) {
                getRedis().hsetAsync(sessionKey, 'access', Date.now())
            }
            return sessionData
        })
}

export function setVal(id, key, val) {
    return redis.setHKV(getId(id), key, val);
}
export function getVal(id, key) {
    return redis.getHKV(getId(id), key);
}
export function delVal(id, key) {
    return redis.delHKV(getId(id), key);
}
export function setExpire(id, seconds) {
    return redis.expireK(getId(id), seconds);
}

export function rmv(id) {
    return redis.delKey(getId(id));
}

function getId(id) {
    return 'sess:' + id;
}