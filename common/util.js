﻿/// <reference path="./util.d.ts" />
// 通用工具
let Promise = require('bluebird');
function getIp(req) {
    return req.headers['x-real-ip'] ||
        req.headers['x-forwarded-for'] ||
        req.connection.remoteAddress ||
        req.socket.remoteAddress ||
        req.connection.socket.remoteAddress;
}

function trim(str) {
    return typeof str === 'string' ? str.replace(/^\s+|\s+$/g, '') : str;
}

function substr(str, len) {
    var chineseRegex = /[^\x00-\xff]/g;
    var strLength = str.replace(chineseRegex, "**").length;
    if (strLength <= len) {
        return str;
    }

    var newStr = '';
    var newLength = 0;
    for (var i = 0; i < strLength; i++) {
        var singleChar = str.charAt(i).toString();
        newLength += singleChar.match(chineseRegex) ? 2 : 1;
        if (newLength > len) {
            break;
        }
        newStr += singleChar;
    }
    return newStr;
}

function htmlEscape(str) {
    //    str = str.replace(/&/g, '&amp;');
    //    str = str.replace(/</g, '&lt;');
    //    str = str.replace(/>/g, '&gt;');
    ////    str = str.replace(/"/g, '&quot;');
    //    str = str.replace(/'/g, '&#039;');
    return str;
}

function response(data, code, msg) {
    if (data instanceof Error) {
        require('./logger').error(data);
        return {
            code: data.name,
            msg: data.message
        };
    }

    if (data && data.code && data.msg) {
        return data;
    }

    return {
        code: code === undefined ? 0 : code,
        msg: msg,
        data: data
    };
}

function curl(url, option) {
    var Q = require('q'),
        restify = require('restify'),
        BufferHelper = require('bufferhelper'),
        iconv = require('iconv-lite');

    var defer = Q.defer(),
        arr = url.split('/'),
        host = arr.slice(0, 3).join('/'),
        path = '/' + arr.slice(3).join('/');
    restify.createClient({
        connectTimeout: 1000,
        retry: false,
        url: host
    }).get(path, function (err, req) {
        if (err) return defer.reject(err); // connection error

        req.on('result', function (err, res) {
            if (err) return defer.reject(err); // HTTP status code >= 400

            var bufferHelper = new BufferHelper();
            res.on('data', function (chunk) {
                bufferHelper.concat(chunk);
            });
            res.on('end', function () {
                var buffer = bufferHelper.toBuffer();
                var encoding = option && option.encoding;
                var content = iconv.decode(buffer, encoding || 'utf-8');
                defer.resolve(content);
            });
        });
    });

    return defer.promise;
}

function format(source, params) {
    var getPrototype = Object.prototype.toString;
    params = (params !== null) && /\[object Array\]|\[object Object\]/.test(
        getPrototype.call(params))
        ? params
        : Array.prototype.slice.call(arguments, 1);

    return source.replace(/#\{(.+?)\}/g, function (match, key) {
        var replacer = params[key];
        if ('[object Function]' === getPrototype.call(replacer)) {
            replacer = replacer(key);
        }
        return ((replacer === undefined || replacer === null) ? '' : replacer);
    });
}

function formatDate(timestamp, format) {
    format = format || 'yyyy-MM-dd HH:mm:ss';
    var date = new Date(timestamp),
        year = date.getFullYear(),
        month = date.getMonth() + 1,
        day = date.getDate(),
        hour = date.getHours(),
        minute = date.getMinutes(),
        second = date.getSeconds();
    return format.replace('yyyy', year).replace('MM', pad(month, 2)).replace('dd', pad(day, 2))
        .replace('HH', pad(hour, 2)).replace('mm', pad(minute, 2)).replace('ss', pad(second, 2));
}

function currying(fn) {
    var args = [].slice.call(arguments, 1);
    return function () {
        var newArgs = args.concat([].slice.call(arguments));
        return fn.apply(this, newArgs);
    };
}

function extend(dest, src) {
    for (var key in src) {
        dest[key] = src[key];
    }
    return dest;
}

function sleep(milliSeconds) {
    var startTime = Date.now();
    while (Date.now() < startTime + milliSeconds) { }
}

function unicode(str) {
    return str.replace(/[\u00FF-\uFFFF]/g, function ($0) {
        return '\\u' + $0.charCodeAt().toString(16);
    });
}

function pad(str, len) {
    str = '' + (str || '');
    var padLen = len - str.length;
    if (padLen > 0) {
        var arr = [];
        arr.length = padLen + 1;
        str = arr.join('0') + str;
    }
    return str;
}

function toCamelCase(str) {
    str = '' + (str || '');
    return str[0].toUpperCase() + str.substr(1);
}

// 手动解析请求参数：由于游戏发送数据中的content参数使用%格式化了，不能直接使用通用的querystring包解析
function getParams(str) {
    var params = {};
    var arr = str.split('&');
    for (var i = 0, l = arr.length; i < l; i++) {
        var tmp = arr[i].split('=');
        params[tmp[0]] = tmp[1];
    }
    return params;
}

function hexMd5(content, option) {
    option = option || {};
    var crypto = require('crypto');
    var md5 = crypto.createHash('md5');
    option.encoding
        ? md5.update(content, option.encoding, 'hex')
        : md5.update(content);  // encoding: 'utf8/gbk'

    return md5.digest(option.output || 'hex');
}

//  token校验
function checkToken(content, params, option) {
    var config = require('./config');
    var tokenKey = config.TOKEN_SALT;
    var md5Key = content ? hexMd5(content, option) : '';
    var token = hexMd5('' + params.time + params.roleid + md5Key + tokenKey);
    return token === params.token;
}

// 对Date的扩展，将 Date 转化为指定格式的String
// 月(M)、日(d)、小时(h)、分(m)、秒(s)、季度(q) 可以用 1-2 个占位符，
// 年(y)可以用 1-4 个占位符，毫秒(S)只能用 1 个占位符(是 1-3 位的数字)
function dateFormat(date, fmt) { //author: meizz
    if (!date) return;

    var o = {
        'M+': date.getMonth() + 1, //月份
        'd+': date.getDate(), //日
        'h+': date.getHours(), //小时
        'm+': date.getMinutes(), //分
        's+': date.getSeconds(), //秒
        'q+': Math.floor((date.getMonth() + 3) / 3), //季度
        'S': date.getMilliseconds() //毫秒
    };
    if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
    }
    for (var k in o) {
        if (new RegExp('(' + k + ')').test(fmt)) {
            fmt = fmt.replace(
                RegExp.$1, (RegExp.$1.length == 1)
                ? (o[k]) :
                (('00' + o[k]).substr(('' + o[k]).length))
            );
        }
    }
    return fmt;
}

function genUploadPath() {
    var uuid = require('node-uuid');
    var now = new Date(),
        year = now.getFullYear(),
        month = (now.getMonth() + 1) + '',
        date = now.getDate() + '',
        name = uuid.v1().replace(/-/g, ''),
        dir = year + (month.length === 1 ? ('0' + month) : month)
            + '/' + (date.length === 1 ? ('0' + date) : date);
    return { path: dir + '/' + name, dir: dir, name: name };
}

function toLowerCaseKey(item, recursive) {
    if (!recursive) {
        var newItem = {};
        for (var key in item) {
            newItem[key.toLowerCase()] = item[key];
        }
        return newItem;
    }

    var typeStr = Object.prototype.toString.call(item);
    if (typeStr === '[object Object]') {
        newItem = {};
        for (key in item) {
            var val = item[key];
            typeStr = Object.prototype.toString.call(val);
            if (typeStr === '[object Object]' || '[object Array]') {
                val = toLowerCaseKey(val, true);
            }
            newItem[key.toLowerCase()] = val;
        }
        return newItem;
    }
    else if (typeStr === '[object Array]') {
        for (var i = 0, l = item.length; i < l; i++) {
            item[i] = toLowerCaseKey(item[i], true);
        }
    }
    return item;
}

function toSnakeCaseKey(item, recursive, matcher) {
    var _ = require('lodash');
    var defaultMatcher = x => true;
    var matcher = matcher || defaultMatcher;
    if (!recursive) {
        var newItem = {};
        for (var key in item) {
            if (matcher(key)) {
                newItem[_.snakeCase(key)] = item[key];
            } else {
                newItem[key] = item[key];
            }
        }
        return newItem;
    }

    var typeStr = Object.prototype.toString.call(item);
    if (typeStr === '[object Object]') {
        newItem = {};
        for (key in item) {
            var val = item[key];
            typeStr = Object.prototype.toString.call(val);
            if (typeStr === '[object Object]' || '[object Array]') {
                val = toSnakeCaseKey(val, true, matcher);
            }
            if (matcher(key)) {
                newItem[_.snakeCase(key)] = item[key];
            } else {
                newItem[key] = item[key];
            }
        }
        return newItem;
    }
    else if (typeStr === '[object Array]') {
        for (var i = 0, l = item.length; i < l; i++) {
            item[i] = toSnakeCaseKey(item[i], true, matcher);
        }
    }
    return item;
}

function getDayStr(timestamp) {
    var date = timestamp ? new Date(timestamp) : new Date();
    return formatDate(date);
    //    return date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate() + ' '
    //        + date.getHours() + ':' + date.getMinutes() + ':' +date.getSeconds();
}

function addToList(id, oldStr, maxNum) {
    var list = [id];
    if (oldStr) {
        var hash = {};
        hash[id] = 1;

        var num = 1;
        var arr = oldStr.split(',');
        for (var i = 0, l = arr.length; i < l; i++) {
            if (maxNum && num >= maxNum) {
                break;
            }

            var curId = arr[i];
            if (curId && !hash[curId]) {
                list.push(curId);
                hash[curId] = 1;
                num++;
            }
        }
    }
    return list.join(',');
}

function rmvFromList(id, oldStr) {
    var tmpStr = ',' + oldStr + ',',
        str = ',' + id;
    return tmpStr.replace(str, '').replace(/^,|,$/g, '');
}

function deDup(rawList) {
    var list = [];

    var hash = {};
    for (var i = 0, l = rawList.length; i < l; i++) {
        var cur = rawList[i];
        if (!hash[cur]) {
            list.push(cur);
            hash[cur] = 1;
        }
    }

    return list;
}

function mkDirsSync(dirRoot, subPath, mode) {
    var fs = require('fs');
    var path = require('path');
    var pathSep = '/';
    if (!fs.existsSync(dirRoot + pathSep + subPath)) {
        var curPath = dirRoot;
        subPath.split(pathSep).forEach(function (dirname) {
            curPath = path.join(curPath, dirname);
            if (!fs.existsSync(curPath)) {
                if (!fs.mkdirSync(curPath, mode)) {
                    return false;
                }
            }
        });
    }
    return true;
}

function mvFile(src, dest) {
    var fs = require('fs');
    var readStream = fs.createReadStream(src);
    var writeStream = fs.createWriteStream(dest);
    readStream.pipe(writeStream);
    readStream.on('end', function () {
        fs.unlinkSync(src);
    });
}

function getJsonInfo(str, defaultValue) {
    const result = defaultValue || {};
    if (!str) {
        return result;
    }
    try {
        return JSON.parse(str);
    } catch (err) {
        return result;
    }
}

function hashKeyList(hash) {
    var list = [];
    for (var key in hash) {
        list.push(key);
    }
    return list;
}

function unifyCols(item, cols, tbCols) {
    var hash = {};
    for (var col in tbCols) {
        hash[col.toLowerCase()] = col;
    }

    var result = {};
    for (var i = 0, l = cols.length; i < l; i++) {
        var arr = cols[i].split(/\s+as\s+/);
        var retKey = arr[1] || arr[0];
        var key = hash[arr[0].toLowerCase()];
        key && (result[retKey] = item[key]);
    }
    return result;
}

function unifyKeys(info, tbCols) {
    var hash = {};
    for (var col in tbCols) {
        hash[col.toLowerCase()] = col;
    }

    var result = {};
    for (var k in info) {
        var key = hash[k.toLowerCase()];
        key && (result[key] = info[k]);
    }
    return result;
}

function unifyValues(info) {
    var result = {};
    for (var key in info) {
        var value = info[key];
        if (value === undefined) {
            continue;
        }

        if (value === null) {
            value = '';
        }
        result[key] = value;
    }
    return result;
}

function unifyRetValues(info, tbCols) {
    var result = {};
    for (var key in info) {
        var value = info[key];
        if (value === '') {
            value = null;
        } else if (value && (tbCols[key] === 'number')) {
            value = parseInt(value, 10);
        }
        result[key] = value;
    }
    return result;
}

function unifyResults(results) {
    var list = [];
    for (var i = 0, l = results.length; i < l; i++) {
        var item = results[i];
        if (item) {
            list.push(item);
        }
    }
    return list;
}

function unifyUpResults(results) {
    var result = {};
    for (var i = 0, l = results.length; i < l; i++) {
        var item = results[i];
        for (var k in item) {
            var v = item[k];
            typeof v === 'number' && (result[k] = (result[k] || 0) + v);
        }
    }
    return result;
}

var _ = require('lodash');

function keyToRecordHash(records, key) {
    return _.reduce(records, function (result, record) {
        result[record[key]] = record;
        return result;
    }, {});
}

function embeddedOn(item, arrayB, aKey, BKey, embeddedKey) {
    var keyToArrayB = keyToRecordHash(arrayB, BKey);
    var embeddedFunc = function (record) {
        var embeddedRecord;
        if (_.isArray(record[aKey])) {
            embeddedRecord = record[aKey].map(function (r) {
                return keyToArrayB[r];
            });
        } else {
            embeddedRecord = keyToArrayB[record[aKey]];
        }
        record[embeddedKey] = embeddedRecord || null;
        return record;
    };
    if (_.isArray(item)) {
        return _.map(item, embeddedFunc);
    } else {
        embeddedFunc(item);
        return item;
    }
}

function capitalizeFirstLetter(string) {
    return string[0].toUpperCase() + string.slice(1);
}

function csvStrToArray(string, delimiter = ',') {
    return _.chain(_.split(string, delimiter)).compact().value();
}

function csvStrToIntArray(string, delimiter = ',') {
    return csvStrToArray(string, delimiter).map(function (r) {
        return parseInt(r, 10);
    });
}

function isJson(str) {
    try {
        JSON.parse(str);
    } catch (e) {
        return false;
    }
    return true;
}

function cartesianProduct() {
    return _.reduce(arguments, function (a, b) {
        return _.flatten(_.map(a, function (x) {
            return _.map(b, function (y) {
                return x.concat([y]);
            });
        }), true);
    }, [[]]);
}

function decodeGBKUri(str) {
    var iconv = require('iconv-lite');
    var bytes = [];
    for (var i = 0; i < str.length;) {
        if (str[i] === '%') {
            i++;
            bytes.push(parseInt(str.substring(i, i + 2), 16));
            i += 2;
        } else {
            bytes.push(str.charCodeAt(i));
            i++;
        }
    }
    var buf = new Buffer(bytes);
    return iconv.decode(buf, 'gbk');
}

function decodeGBKUriSafe(str) {
    try {
        return decodeGBKUri(str);
    } catch (err) {
        return str;
    }
}

function runAsync(promise) {
    return promise.catch(function (err) {
        require('../common/logger').error(err)
    });
}

/**
 * 获取被两个'#'包裹的文本
 * @param {String} str
 * @return {Array} texts
 */
function getHashedText(str) {
    let regex = /#[^#]+#/g;
    let matches = str.match(regex);
    matches = _.map(matches, s => s.substr(1, s.length - 2));
    return matches;
}

// 过滤对象中所有key不为UserName的属性
function filterUrs(obj) {
    return filterProperty(obj, (key, value) => key !== "UserName");
}

function filterProperty(obj, predicate) {
    if (_.isArray(obj)) {
        return _.map(obj, x => filterUrs(x));
    } else if (_.isObject(obj)) {
        const newObj = {};
        _.forEach(obj, (value, key) => {
            if (predicate(key, value)) {
                newObj[key] = filterUrs(value);
            }
        });
        return newObj;
    } else {
        return obj;
    }
}


function readUntil(str, prediction) {
    let readStr = '';
    let i = 0;
    while (str[i]) {
        if (prediction(str[i], i)) {
            break;
        }
        readStr += str[i++];
    }
    return readStr;
}

//res.hi 不支持https
const HTTPS_HOST_NAME_MAP = {
    "res.hi.netease.com": "hi.res.netease.com"
};

function toHttps(str) {
    const url = require('url');
    const urlObj = url.parse(str);
    if (urlObj.protocol === 'http:') {
        urlObj.protocol = 'https:';
        const newHostName = HTTPS_HOST_NAME_MAP[urlObj.hostname];
        if (newHostName) {
            urlObj.host = newHostName;
            if (urlObj.port) {
                urlObj.host = newHostName + ":" + urlObj.port;
            } else {
                url.host = newHostName;
            }
            urlObj.hostname = newHostName;
        }
        return url.format(urlObj);
    } else {
        return str;
    }
}

function countBy(iterate, predication) {
    return iterate && _.sumBy(iterate, x => predication(x) ? 1 : 0);
}

function isNumeric(num) {
    return !isNaN(num);
}

function keysToCamelCase(obj) {
    if (obj === null || typeof obj !== "object") {
        return obj;
    } else if (_.isArray(obj)) {
        return _.map(obj, item => keysToCamelCase(item));
    } else {
        const camelCaseObj = {};
        _.keys(obj).forEach(key => {
            camelCaseObj[_.camelCase(key)] = keysToCamelCase(obj[key]);
        });
        return camelCaseObj;
    }
}

function getMondayZero(time) {
    var today = time ? new Date(time) : new Date();
    var today_zero = (new Date(today.getFullYear(), today.getMonth(), today.getDate())).getTime(), // 今天凌晨时间戳
        today_day = today.getDay() || 7;    // 当天周几
    return today_zero - 86400 * 1000 * (today_day - 1);
}

function hashJoin(arr1, arr2, joinBy) {
    return _.map(arr1, x => {
        let where = {}
        where[joinBy] = x[joinBy]
        return _.assign({}, x, _.find(arr2, where))
    })
}

function removeEmoji(str) {
  return str.replace(/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, '');
}

exports.removeEmoji = removeEmoji

exports.getIp = getIp;
exports.trim = trim;
exports.substr = substr;
exports.htmlEscape = htmlEscape;
exports.response = response;
exports.curl = curl;
exports.format = format;
exports.formatDate = formatDate;
exports.currying = currying;
exports.extend = extend;
exports.sleep = sleep;
exports.unicode = unicode;
exports.pad = pad;
exports.toCamelCase = toCamelCase;
exports.getParams = getParams;
exports.hexMd5 = hexMd5;
exports.checkToken = checkToken;
exports.dateFormat = dateFormat;
exports.genUploadPath = genUploadPath;
exports.toLowerCaseKey = toLowerCaseKey;
exports.toSnakeCaseKey = toSnakeCaseKey;
exports.getDayStr = getDayStr;
exports.addToList = addToList;
exports.rmvFromList = rmvFromList;
exports.deDup = deDup;
exports.mkDirsSync = mkDirsSync;
exports.mvFile = mvFile;
exports.getJsonInfo = getJsonInfo;
exports.hashKeyList = hashKeyList;
exports.unifyCols = unifyCols;
exports.unifyKeys = unifyKeys;
exports.unifyValues = unifyValues;
exports.unifyRetValues = unifyRetValues;
exports.unifyResults = unifyResults;
exports.unifyUpResults = unifyUpResults;
exports.embeddedOn = embeddedOn;
exports.capitalizeFirstLetter = capitalizeFirstLetter;
exports.csvStrToArray = csvStrToArray;
exports.csvStrToIntArray = csvStrToIntArray;
exports.keyToRecordHash = keyToRecordHash;

exports.isJson = isJson;
exports.cartesianProduct = cartesianProduct;

exports.decodeGBKUri = decodeGBKUri;
exports.decodeGBKUriSafe = decodeGBKUriSafe;
exports.co = gen => Promise.coroutine(gen)();
exports.runAsync = runAsync;
exports.getHashedText = getHashedText;
exports.filterUrs = filterUrs;
exports.readUntil = readUntil;
exports.toHttps = toHttps;
exports.countBy = countBy;
exports.isNumeric = isNumeric;
exports.keysToCamelCase = keysToCamelCase;
exports.getMondayZero = getMondayZero;
exports.hashJoin = hashJoin

exports.keysToCamelCase = function keysToCamelCase(obj) {
    if (obj === null || typeof obj !== "object") {
        return obj;
    } else if (_.isArray(obj)) {
        return _.map(obj, item => keysToCamelCase(item));
    } else {
        const camelCaseObj = {};
        _.keys(obj).forEach(key => {
            camelCaseObj[_.camelCase(key)] = keysToCamelCase(obj[key]);
        });
        return camelCaseObj;
    }
}
