"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SYSTEM_ROLE_ID = exports.PYQ_MOMENT_COLS = exports.AuditProductName = exports.HTTP_STATUS_CODES = exports.SKEY_CHECK_VALID_TIME_OFFSET = exports.LOGIN_TIME_LONG_TERM_MARK = exports.LIKE_USER_MAX_SIZE = exports.PAGE_SCHEMA = exports.OfficialRoleIds = exports.RoleInfos = exports.AuditStatues = exports.Statues = exports.TABLE_NAMES = void 0;
exports.TABLE_NAMES = {
    ActivityTopic: "pyq_activity_topic",
    ActivityTopicMoment: "pyq_activity_topic_moment",
    PyqProfile: "pyq_profile",
    PyqFollow: "pyq_follow",
    PyqMoment: "pyq_moment",
    PyqComment: "pyq_comment",
    PyqMomentArticle: "pyq_moment_article",
    PyqScenePhoto: "pyq_scene_photo",
    PyqOfficialAccount: "pyq_official_account",
    PyqOfficialAccountUnFollow: "pyq_official_account_unfollow",
    QnReplay: "qn_replay",
    QnReplayCollect: "qn_replay_collect",
    Activity302Cfg: "pyq_activity_redirect_cfg",
    PyqWishlist: "pyq_wishlist",
    PyqWishlistHelp: "pyq_wishlist_help",
    FireworkPhoto: "pyq_firework_photo",
    PyqBackGround: "pyq_background",
    ReportLog: "pyq_report_log",
    Transfer: "pyq_transfer",
    PyqPhotoFreeAudit: "pyq_photo_free_audit",
    FpSendPic: "pyq_fp_send_pic",
    MomentTag: "pyq_moment_tag",
    MomentLottery: "pyq_moment_lottery",
    MomentLotteryAttendAction: "pyq_moment_lottery_attend_action",
    MomentLotteryAttend: "pyq_moment_lottery_attend",
    MomentLotteryDrawApiLog: "pyq_moment_lottery_draw_api_log",
};
var Statues;
(function (Statues) {
    Statues[Statues["Normal"] = 0] = "Normal";
    Statues[Statues["Deleted"] = -1] = "Deleted";
})(Statues = exports.Statues || (exports.Statues = {}));
var AuditStatues;
(function (AuditStatues) {
    AuditStatues[AuditStatues["Auditing"] = 0] = "Auditing";
    AuditStatues[AuditStatues["PASS"] = 1] = "PASS";
    AuditStatues[AuditStatues["Reject"] = -1] = "Reject";
})(AuditStatues = exports.AuditStatues || (exports.AuditStatues = {}));
exports.RoleInfos = [
    { id: 1005, name: "金陵情报官" },
    { id: 71000000, name: "檀无心" },
    { id: 71000001, name: "殷紫萍" },
    { id: 71000002, name: "聂秋鹰" },
    { id: 71000003, name: "萧若兰" },
    { id: 71000004, name: "霍击蒙" },
    { id: 71000005, name: "石云馨" },
    { id: 71000006, name: "卓断水" },
    { id: 71000007, name: "杨梦言" },
    { id: 71000008, name: "洛昊空" },
    { id: 71000009, name: "沈傲霜" },
    { id: 71000010, name: "莫忘尘" },
    { id: 71000011, name: "纳兰青桑" },
    { id: 71000012, name: "步临风" },
    { id: 71000013, name: "冷月心" },
    { id: 71000014, name: "风摇筝" },
    { id: 71000015, name: "叶葬花" },
    { id: 71000016, name: "花渐隐" },
    { id: 71000017, name: "阮烟罗" },
    { id: 71000018, name: "乔寻影" },
    { id: 71000019, name: "水凝墨" },
    { id: 71000020, name: "胖大海" },
    { id: 71000021, name: "阿怡" },
    { id: 71000022, name: "网易原创作者联盟" },
    { id: 71000023, name: "阿初" },
    { id: 71000024, name: "蒲松龄" },
    { id: 71000025, name: "哮天犬" },
    { id: 71000026, name: "瑶羽" },
    { id: 71000027, name: "月鸣" },
    { id: 71000027, name: "阿乌" },
];
exports.OfficialRoleIds = exports.RoleInfos.map((x) => x.id);
exports.PAGE_SCHEMA = {
    page: { type: Number, min: 1, default: 1 },
    pageSize: { type: Number, min: 1, max: 20, default: 10 },
};
exports.LIKE_USER_MAX_SIZE = 20; // zanList最多缓存的角色id数量
exports.LOGIN_TIME_LONG_TERM_MARK = "LONG_TERM";
exports.SKEY_CHECK_VALID_TIME_OFFSET = 5000; // 5s
var HTTP_STATUS_CODES;
(function (HTTP_STATUS_CODES) {
    HTTP_STATUS_CODES[HTTP_STATUS_CODES["UNAUTHORIZED"] = 401] = "UNAUTHORIZED";
})(HTTP_STATUS_CODES = exports.HTTP_STATUS_CODES || (exports.HTTP_STATUS_CODES = {}));
exports.AuditProductName = "L10";
exports.PYQ_MOMENT_COLS = [
    "ID",
    "RoleId",
    "Text",
    "ImgList",
    "ImgAudit",
    "ZanList",
    "CreateTime",
    "HotState",
    "VideoList",
    "VideoAudit",
    "VideoCoverList",
    "VideoCoverAudit",
    "Status",
    "TagIds",
];
exports.SYSTEM_ROLE_ID = 0;
//# sourceMappingURL=constants.js.map