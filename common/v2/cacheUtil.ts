import { Context } from "../../pyq-server/context";
import { getRedis } from "../redisNew";
import { getJsonInfo } from "../util";
import { isNullOrUndefined } from "../util2";

export abstract class GenericCache<T, F> {
    public async get(ctx: Context, params: T): Promise<F> {
        const str = await getRedis().getAsync(this.getKey(ctx, params));
        if (this.isNeedRefresh(str)) {
            const result = await this.refresh(ctx, params);
            return result;
        } else {
            const result: F = getJsonInfo(str);
            return result;
        }
    }

    protected isNeedRefresh(str: string) {
        return isNullOrUndefined(str);
    }

    getExpireTime(ctx: Context, params: T) {
        return null;
    }

    abstract getKey(ctx: Context, params: T): string;

    abstract fetchDataSource(ctx: Context, params: T): Promise<F>;

    async refresh(ctx: Context, params: T): Promise<F> {
        const content = await this.fetchDataSource(ctx, params);
        await this.writeToCache(ctx, params, content);
        return content;
    }

    async clear(ctx: Context, params: T) {
        await getRedis().delAsync(this.getKey(ctx, params));
    }

    protected async writeToCache(ctx: Context, params: T, content: F) {
        const str = JSON.stringify(content);
        await getRedis().setAsync(this.getKey(ctx, params), str);
        const expire = this.getExpireTime(ctx, params);
        if (expire !== null) {
            await getRedis().expireAsync(this.getKey(ctx, params), expire);
        }
    }
}