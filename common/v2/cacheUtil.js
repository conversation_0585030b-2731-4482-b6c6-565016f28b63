"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenericCache = void 0;
const redisNew_1 = require("../redisNew");
const util_1 = require("../util");
const util2_1 = require("../util2");
class GenericCache {
    get(ctx, params) {
        return __awaiter(this, void 0, void 0, function* () {
            const str = yield (0, redisNew_1.getRedis)().getAsync(this.getKey(ctx, params));
            if (this.isNeedRefresh(str)) {
                const result = yield this.refresh(ctx, params);
                return result;
            }
            else {
                const result = (0, util_1.getJsonInfo)(str);
                return result;
            }
        });
    }
    isNeedRefresh(str) {
        return (0, util2_1.isNullOrUndefined)(str);
    }
    getExpireTime(ctx, params) {
        return null;
    }
    refresh(ctx, params) {
        return __awaiter(this, void 0, void 0, function* () {
            const content = yield this.fetchDataSource(ctx, params);
            yield this.writeToCache(ctx, params, content);
            return content;
        });
    }
    clear(ctx, params) {
        return __awaiter(this, void 0, void 0, function* () {
            yield (0, redisNew_1.getRedis)().delAsync(this.getKey(ctx, params));
        });
    }
    writeToCache(ctx, params, content) {
        return __awaiter(this, void 0, void 0, function* () {
            const str = JSON.stringify(content);
            yield (0, redisNew_1.getRedis)().setAsync(this.getKey(ctx, params), str);
            const expire = this.getExpireTime(ctx, params);
            if (expire !== null) {
                yield (0, redisNew_1.getRedis)().expireAsync(this.getKey(ctx, params), expire);
            }
        });
    }
}
exports.GenericCache = GenericCache;
//# sourceMappingURL=cacheUtil.js.map