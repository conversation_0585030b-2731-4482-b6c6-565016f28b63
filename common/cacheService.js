"use strict";
/**
 * Created by zhen<PERSON> on 16-11-18.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseKVCache = exports.cacheInRedis = void 0;
/* eslint-disable @typescript-eslint/no-var-requires */
const redisTypes_1 = require("./redisTypes");
const { getRedis } = require("../common/redis");
const _ = require("lodash");
const uuid = require("node-uuid");
const util = require("./util");
const REDIS_KEY_NOT_EXIST_OR_EXPIRED = -2;
/**
 * generate a cache version function
 * @param func
 * @param {object} option
 * @param {(string|callback)} option.cacheKey
 * @param {number} option.expire  expire time in milliseconds
 * @param {object} option.context
 * @param {callback} option.onUpdateValue
 * @return {callback}
 */
function cacheInRedis(func, option) {
    let cacheKey;
    option = _.defaults(option, { cacheKey: uuid() });
    if (_.isString(option.cacheKey)) {
        cacheKey = option.cacheKey;
    }
    function cacheVersionFunc() {
        // eslint-disable-next-line prefer-rest-params
        const args = _.toArray(arguments);
        const context = option.context || null;
        if (_.isFunction(option.cacheKey)) {
            cacheKey = option.cacheKey.apply(context, args);
        }
        return getRedis()
            .ttlAsync(cacheKey)
            .then(function (ttl) {
            if (ttl === REDIS_KEY_NOT_EXIST_OR_EXPIRED) {
                return Promise.resolve(func.apply(context, args)).then(function (value) {
                    if (_.isFunction(option.onUpdateValue)) {
                        const uvArgs = _.concat([value], args);
                        option.onUpdateValue.apply(context, uvArgs);
                    }
                    if (value === null || value === undefined) {
                        value = "";
                    }
                    const setArgs = [cacheKey, JSON.stringify(value)];
                    if (option.expire && option.expire > 0) {
                        setArgs.push("PX");
                        setArgs.push(option.expire);
                    }
                    return getRedis()
                        .setAsync(...setArgs)
                        .then(function () {
                        return value;
                    });
                });
            }
            else {
                return getRedis()
                    .getAsync(cacheKey)
                    .then(function (value) {
                    return util.getJsonInfo(value);
                });
            }
        });
    }
    return cacheVersionFunc;
}
exports.cacheInRedis = cacheInRedis;
class BaseKVCache {
    constructor(option) {
        this.keyPrefix = option.keyPrefix || "";
        this.expire = option.expire;
        this.client = getRedis();
    }
    /**
     * @param {string} key
     * @param {string} value
     */
    put(key, value) {
        return this.client.setAsync(this.keyPrefix + key, value, redisTypes_1.ExpireType.EX, this.expire);
    }
    /**
     * @param {string} key
     */
    get(key) {
        return this.client.getAsync(this.keyPrefix + key);
    }
    /**
     * @param {string} key
     */
    del(key) {
        return this.client.delAsync(this.keyPrefix + key);
    }
}
exports.BaseKVCache = BaseKVCache;
//# sourceMappingURL=cacheService.js.map