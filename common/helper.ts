import * as moment from "moment";
import { SpecialPeriodFor64 } from "../common/config";
import type { Server, RequestHandler } from "restify";
import { ipLimitMiddleware } from "../pyq-server/middlewares/ipAuth";
import { gameServerIps } from "../pyq-server/common/config";

export function useWhenUrlStartsWith(app, urlPrefix: string, middleware) {
  app.use(function (req, res, next) {
    let path = req.route?.path || req.url;
    if (path && typeof path === 'string' && path.startsWith(urlPrefix)) {
      middleware(req, res, next);
    } else {
      next();
    }
  });
}



export function preUseWhenUrlStartsWith(app, urlPrefix: string, middleware) {
  app.pre(function (req, res, next) {
    let path = req.url;
    if (path.startsWith(urlPrefix)) {
      middleware(req, res, next);
    } else {
      next();
    }
  });
}

const preUseWhenUrlPrefix = preUseWhenUrlStartsWith

export function isIn64SpecialPeriod(date: Date) {
  return moment(date).isBetween(SpecialPeriodFor64.startDate, SpecialPeriodFor64.endDate);
}

type method = "get" | "post";

export function addRoute(
  app: Server,
  methods: method[],
  route: any,
  routeCallback: RequestHandler | RequestHandler[],
  ...routeCallbacks: RequestHandler[]
) {
  for (let m of methods) {
    app[m](route, routeCallback, ...routeCallbacks);
  }
}

export function addGetPostRoute(
  app: Server,
  route: any,
  routeCallback: RequestHandler | RequestHandler[],
  ...routeCallbacks: RequestHandler[]
) {
  return addRoute(app, ["get", "post"], route, routeCallback, ...routeCallbacks);
}

export function skipSkeyAuthMiddleware(req, res, next) {
  req.skipSkey = true;
  next();
}

export function skipLevelCheckMiddleware(req, res, next) {
  req.skipLevel = true;
  next();
}

export async function cleanGlobalAuth(app, prefix) {
  preUseWhenUrlPrefix(app, prefix, skipSkeyAuthMiddleware);
  preUseWhenUrlPrefix(app, prefix, skipLevelCheckMiddleware);
}


export let gameIpLimit = ipLimitMiddleware(gameServerIps)