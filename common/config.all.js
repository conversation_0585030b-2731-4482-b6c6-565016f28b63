"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.serverCfg = exports.initConfig = void 0;
const _ = require("lodash");
const FORWARD_PROXY_HOST = "";
const host = "";
const DEFAULT_CFG = {
    FORWARD_PROXY_HOST,
    testCfg: {
        test_env: false,
        debug: false,
        req_log: false,
        db_debug: false,
        redis_debug: false,
        use_test_activity_router: false,
        request_debug: false,
        skip_token_check: false,
        no_trace_req: false,
        no_text_filter: false,
        nos_callback_on: false,
        ignore_gdc_error: false,
        skip_auth: false,
        skip_audit: false,
        table_yunyinglog: false,
        skip_ip_whitelist_check: false,
        skip_qnm_sync_token_check: false,
        skip_fetch_nos_image_size: false,
        /** gdc qnm.do 同步的时候是否跳过缓冲队列 */
        skip_qnm_sync_by_queue: false,
    },
    batchSendPicLimit: 3,
    log: { level: "info", printInConsole: false, schema: "" },
    TABLE_CFG: {
        user: "md_user",
        nameupdate: "md_nickname_histories",
        bindRole: "md_bindrole",
        contacts: "md_contacts",
        moment: "md_moment",
        comment: "md_comment",
        photo: "md_photo",
        answer: "md_answer",
        message: "md_message",
        inform: "md_inform",
        report: "md_report",
        topic: "md_topic",
        nosFile: "nos_file_usage",
        qn: {
            roleInfo: "qn_roleinfo",
            roleProp: "qn_roleprop",
            roleSkill: "qn_roleskill",
            rolePet: "qn_roleanimal",
            roleEvent: "qn_roleevent",
            roleHome: "qn_rolehome",
            roleChild: "qn_rolechild",
            roleFriend: "qn_rolefriend",
        },
        qnm: {
            roleInfo: "qnm_roleinfo",
            roleProp: "qnm_roleprop",
            roleEvent: "qnm_roleevent",
            roleInfo_other: "qnm_other_roleinfo",
            roleProp_other: "qnm_other_roleprop",
            transact_hisroties: "qnm_transact_histories",
            pyq_profile: "pyq_profile",
            pyq_moment: "pyq_moment",
            pyq_message: "pyq_message",
            pyq_comment: "pyq_comment",
            pyq_event: "pyq_event",
            pyq_topic: "pyq_topic",
            pyq_inform: "pyq_inform",
        },
    },
    QNM_API: {
        iconHost: "https://qnm.res.netease.com/xt/icon/",
    },
    REDIS_CFG: {
        hosts: undefined,
        host: "127.0.0.1",
        db: 0,
        password: "",
        port: 6379,
        no_ready_check: false,
        prefix: "",
    },
    DB_CFG: {
        host: "",
        port: 3306,
        user: "",
        connectionLimit: 5,
        password: "",
        charset: "utf8mb4",
        database: "nodejs_yxb",
    },
    LOG_PATH: "",
    GEO_REDIS_CFG: {
        host: "127.0.0.1",
        password: "",
        port: 6379,
    },
    ONE_DAY_SECONDS: 86400,
    MAX_UPLOAD_SIZE: 5 * 1024 * 1024,
    SESSION_GC_POSSIBILITY: 0.001,
    MD_API_URI: "https://ssl.hi.163.com/",
    MD_HOME_URL: "https://new.hi.163.com/m/",
    MD_SESS_COOKIE: "MD_SESS",
    TRANSACT_SESS_COOKIE: "TRANSACT_SESS",
    cookieCfg: {
        path: "/",
        domain: ".163.com",
        maxAge: 0,
        expires: "",
        secure: false,
        httpOnly: true,
    },
    visitPyqSpaceMaxTimesOneDay: 200,
    mingshiFinalDate: "2018-09-15 10:00",
    BingQiPuDbConfig: {
        charset: "",
        port: 3306,
        connectionLimit: 5,
        host: "*************",
        user: "qnyh",
        password: "qnyh",
        database: "bingqipu",
    },
    TopicAdminUrs: [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ],
    dreamMingshi: {
        haixuan: {
            startDate: "2019-03-08 12:00",
            endDate: "2019-03-20 23:59",
        },
        fusai: {
            startDate: "2019-03-22 12:00",
            endDate: "2019-03-29 11:59",
        },
        fusai_sqs: {
            startDate: "2019-03-30 12:05",
            endDate: "2019-03-31 23:59",
        },
    },
    mingRenTang2019: {
        chusai: {
            startDate: "2019-04-18 10:00",
            endDate: "2019-04-27 23:59",
        },
        fusai: {
            startDate: "2019-04-28 10:00",
            endDate: "2019-05-07 23:59",
        },
        juesai: {
            startDate: "2019-05-08 10:00",
            endDate: "2019-05-09 23:59",
        },
    },
    picAuditServerList: [
        // 审核服务器白名单
        "************",
        "************",
        "**************",
        "***************",
        "**************",
        "**************",
        "***********",
        // audit/get_pyq_photo
        "************",
        "************",
        "************",
        "************",
        "************",
        "************",
        "**************",
        "**************",
        "**************",
        "*************",
        "************",
        // 梦岛管理后台服务器IP
        "**************",
        "127.0.0.1",
    ],
    // 倩女手游助手白名单登录获取skey白名单
    qnmAssistAppWhiteIpList: [
        "************",
        "************",
        "************",
        "**************",
        "**************",
        "*************",
    ],
    ccSDKServerList: [
        // CC SDK白名单
        "************",
    ],
    tongrenWorkAuditList: [
        // 同人作品审核服务器白名单
        "**************",
    ],
    tongrenScoreUpdateList: [
        // 同人积分更新的接口ip白名单
        "**************",
        "**************", // 邓智容的电脑ip(POPO:hzdengzhirong)
    ],
    qnMicroFilmServerList: [
        // 倩女微电影专题服务器白名单
        "***************",
    ],
    ursProductFlagWhiteList: [
        // 倩女端游服务端
        "**************",
        "***************",
        // 账号修复中心 https://mima.163.com/nie/
        "************",
        "***************",
        "***************",
        // 藏宝阁ip列表
        "*************",
        "*************",
        "**************",
        "***************",
        "**************",
        "**************",
        "*************",
        "************",
        "************",
        "************",
        "************",
        "************",
        "************",
        "*************",
    ],
    //倩女端游服务端用接口ip白名单
    qnGdcServerList: ["127.0.0.1", "**************", "***************"],
    TETXLIMIT: 163,
    NICKNAME_LEN: 14,
    AppLogin: {
        qian_nv_help_iOS: true,
        qian_nv_help_android: true,
    },
    NSH_API: {
        // 云信用的APP_KEY和APP_SECRET
        NIM: {
            APP_KEY: "ced787d688139a401e110a8351057edf",
            APP_SECRET: "85405638670e",
        },
        JWT_SECRET: "6D957-YK9_?2kK76muDi_igGTw7zD99yaJIlfHae9J8",
    },
    SurveyQuestionIds: [30697],
    SpecialPeriodFor64: {
        startDate: "2025-05-30 19:25",
        endDate: "2025-06-06 23:59",
    },
    FeatureToggle: {
        chuanJiaBao: { open: false },
        mingRenTang: { open: false },
        hideUsedName: { open: true },
        qingyuan_activity: { start: "2018-02-01 12:00", end: "2018-02-28 23:59" },
        qingyuan2019_activity: { start: "2019-02-14 11:00", end: "2019-03-08 23:59" },
        qnm_oneyear_fengyun: { start: "2017-05-25 10:00", end: "2017-06-21 23:59" },
        qnmCoupleHouseActivity: { start: "2017-08-25 10:00", end: "2017-09-14 23:59" },
        qnmSummerLbsMcdonaldActivity: { start: "2017-09-20 23:59", end: "2017-10-05 23:50" },
        qnmFengyun2018: { start: "2018-04-26 00:00", end: "2018-05-20 23:59" },
    },
    QNM_AUTH_LOGIN_SALT: "VBfl!z)(eNN!996AVohzF(99bM32ht",
    cronTaskSwitch: {
        SummerLbsJob: true,
        RefreshPlayersRank: true,
        FetchGmReviews: true,
        RefreshMomentHotValue: true,
        RebuildHotMomentCache: true,
        SyncPushInfoToDB: true,
        TriggerDelayTask: true,
        ProcessReadyTask: true,
        SyncGiftEventToDb: true,
        unlinkYunyingLog: true,
        makeupTransfer: true,
        FixMomentServerId: true,
        DrawMomentLottery: true,
        RetryPendingAudioAuditTasks: true,
        CheckTimeoutAudioAuditTasks: true,
    },
    corsOrigins: [/^https?:\/\/.*\.163\.com(:\d+)?/, /^https?:\/\/.*\.netease\.com(:\d+)?/],
    dbSlowLogCfg: {
        enable: true,
        threshold: 2000,
    },
    reqSlowLogCfg: {
        enable: true,
        threshold: 2000,
    },
    thirdApiSlowLogCfg: {
        enable: true,
        threshold: 5000,
    },
    auditSendPicBatchSize: 30,
    BaiduMapApiAkKey: "n8GrCUSaHM7AKfSZvAnRKbD43KSF0zwy",
    QN_API: {
        dataCenter: `http://${FORWARD_PROXY_HOST}:10001`,
        listChar: "/api/listcharacter/account/#{account}",
        getGuild: "/api/getguildname/serverid/#{serverId}/guildid/#{guildId}",
        queryCharInfo: "/api/querycharacterbasicinfo3/playerid/#{roleId}",
        queryRank: "/api/queryrank/serverid/#{serverId}/rankid/#{rankId}",
        queryAllRank: "/api/queryattrrank/server/all/rankid/#{rankId}/ranknum/#{rankNum}",
        iconHost: "https://xqn.163.com/images/icon/",
        equipStoneImg: "http://d1.bst2.126.net/********/eos/style/img/f/stone_small.png",
        petImg: "http://d2.bst2.126.net/********/style/img/m/ild/s1/r_pet.png",
        serverMergeInfoUrl: `http://${FORWARD_PROXY_HOST}:10010/export/group-name.txt`,
        // 'http://qn2admin.x.netease.com:8660/export/group-name.txt';
        //,roleJobImg: 'http://d3.bst2.126.net/style/img/qn/square/#{gender}_#{jobId}.jpg'
        //,equipStoneImg: 'http://d1.bst2.126.net/********/eos/style/img/f/stone_small.png'
    },
    NOS_CFG: {
        nosImgPath: "/home/<USER>/nosfs/mp",
        accessKey: "",
        secretKey: "",
        bucketName: {
            recycle: "hi-163-recycle",
            common: "hi-163-common",
            md: "hi-163-common",
            qn: "hi-163-qn",
            qnm: "hi-163-qnm",
            nsh: "gamebbs-nsh",
        },
        callback: host + "/#{product}/nos/callback",
    },
    JWT_TOKEN_SECRET: "",
    AUTH_TOKEN_SALT: "",
    TOKEN_SALT_DEL: "",
    TOKEN_SALT: "",
    DIGEST_KEY: "",
    picAuditHost: `http://${FORWARD_PROXY_HOST}:10011`,
    // 易盾反作弊服务
    YI_DUN_ANTI_SPAM: {
        enable: true,
        CHECK_URL: `http://${FORWARD_PROXY_HOST}:10044/v2/login/check`,
        SECRET_ID: "fe748a449fd7c9c728025d7dd9d10fd8",
        SECRET_KEY: "51869c38a901ef126d57fe9df191427a",
        BUSINESS_ID: "f28cc54b9508420e9d640b9ee63bb478",
        PRODUCT_NUMBER: "YD00000965163588",
        PWD_SALT: "b6EVVjvSV70UT#PF",
    },
    // URS cookie远程校验
    URS_COOKIE_PARSE: {
        HOSTS: [`http://${FORWARD_PROXY_HOST}:10018`, `http://${FORWARD_PROXY_HOST}:10019`],
        PRODUCT_ID: "144c50483a644d79b9eed0fc1a690a15",
    },
    REG_163_HOST: `http://${FORWARD_PROXY_HOST}:10004`,
    DC_REG_163_HOST: `http://${FORWARD_PROXY_HOST}:10017`,
    BaiduMapApiHost: `http://${FORWARD_PROXY_HOST}:10014`,
    LoginCheckCfg: {
        api: `http://${FORWARD_PROXY_HOST}:10044/pureserver/login/check`,
        enable: true,
        secretId: "48f5fe66a4df80c0f0abc6463fc72c35",
        secretKey: "1ffc9bfe25c70d60aa7230b57b65090b",
        businessId: "b9dc716116944ba89e5312e5be478a7e",
        productNumber: "YD00325221864257",
    },
    outApi: {
        gms: `http://${FORWARD_PROXY_HOST}:10002/postReceiveLimit.php`,
    },
    facePinchCfg: {
        /** 收藏和我的设计的总存储上限 */
        maxStorageCount: 300,
    },
};
function initConfig(config, host) {
    if (!config)
        config = {};
    const AWS_FORWARD_PROXY_HOST = "***********";
    const FORWARD_PROXY_HOST = config.FORWARD_PROXY_HOST || AWS_FORWARD_PROXY_HOST;
    const cfg = _.cloneDeep(DEFAULT_CFG);
    // modify config by forward proxy
    cfg.FORWARD_PROXY_HOST = FORWARD_PROXY_HOST;
    cfg.QN_API.dataCenter = `http://${FORWARD_PROXY_HOST}:10001`;
    cfg.QN_API.serverMergeInfoUrl = `http://${FORWARD_PROXY_HOST}:10010/export/group-name.txt`;
    cfg.picAuditHost = `http://${FORWARD_PROXY_HOST}:10011`;
    cfg.YI_DUN_ANTI_SPAM.CHECK_URL = `http://${FORWARD_PROXY_HOST}:10044/v2/login/check`;
    cfg.URS_COOKIE_PARSE.HOSTS = [`http://${FORWARD_PROXY_HOST}:10018`, `http://${FORWARD_PROXY_HOST}:10019`];
    cfg.REG_163_HOST = `http://${FORWARD_PROXY_HOST}:10004`;
    cfg.DC_REG_163_HOST = `http://${FORWARD_PROXY_HOST}:10017`;
    cfg.BaiduMapApiHost = `http://${FORWARD_PROXY_HOST}:10014`;
    cfg.LoginCheckCfg.api = `http://${FORWARD_PROXY_HOST}:10044/pureserver/login/check`;
    cfg.outApi.gms = `http://${FORWARD_PROXY_HOST}:10002/postReceiveLimit.php`;
    // modify config by host
    cfg.NOS_CFG.callback = host + "/#{product}/nos/callback";
    return cfg;
}
exports.initConfig = initConfig;
exports.serverCfg = {
    name: "l10_md_api",
    port: 3002,
    apiPrefix: "/qnm",
    region: "cn",
};
//# sourceMappingURL=config.all.js.map