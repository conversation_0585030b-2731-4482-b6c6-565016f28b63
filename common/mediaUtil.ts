import _ = require("lodash");
import { parse as parseUrl } from "url";
import { photoViewCfg } from "../pyq-server/common/config";
import { AuditStatues } from "./constants";
import { csvStrToArray, csvStrToIntArray, hexMd5 } from "./util";
import { base64Encode, removeQueryString, validateUrl } from "./util2";
import { clazzLogger } from "../pyq-server/logger";
import { hasQueryParams } from "../pyq-server/services/util";
import { Lang } from "../pyq-server/types/type";
import { FpAuditConfigMap } from "../pyq-server/constants";
const logger = clazzLogger("common/mediaUtil");

export function isNosUrl(url: string): boolean {
  return !!/nosdn\.127\.net/.exec(url) || !!/nos\.netease\.com/.exec(url);
}

export function isFpUrl(url: string): boolean {
  return !!/fp-dev\.webapp\.163\.com/.exec(url) || !!/fp\.ps\.easebar\.com/.exec(url);
}

// 判断是否是有效的媒体地址, 包括nos地址（国服) 和fp地址（海外服)
export function isValidMediaUrl(url: string): boolean {
  return isNosUrl(url) || isFpUrl(url);
}

enum MediaOpType {
  Watermark,
  Thumb,
  VideoCover,
}

interface Fop {
  type: MediaOpType;
  value: string;
  order?: number;
}

enum FopOrder {
  Thumb = 1,
  Watermark = 2,
  VideoCover = 3,
}

export interface ProcessPhotoData {
  url: string;
  ops?: Fop[];
}

function toNosFullUrl(url: string, ops: Fop[]) {
  let fullOps = "";
  if (ops.length > 0) {
    const opAction = hasQueryParams(url) ? "|" : "?";
    fullOps = opAction + ops.map((r) => r.value).join("%7c");
  }
  return `${url}${fullOps}`;
}

function toFpFullUrl(url: string, ops: Fop[]) {
  let fullOps = "";
  if (ops.length > 0) {
    fullOps = "?fop=" + ops.map((r) => r.value).join("%7c");
  }
  return `${url}${fullOps}`;
}

export function toFullUrl(data: ProcessPhotoData): string {
  const ops = _.orderBy(data.ops, ["order"], ["desc"]);
  if (isNosUrl(data.url)) {
    return toNosFullUrl(data.url, ops);
  } else if (isFpUrl(data.url)) {
    return toFpFullUrl(data.url, ops);
  } else {
    return data.url;
  }
}

function getNosVideoCover(videoUrl: string) {
  return toNosFullUrl(videoUrl, [{ type: MediaOpType.VideoCover, value: photoViewCfg.NOS_VIDEO_COVER_SUFFIX }]);
}

function getFpVideoCover(videoUrl: string) {
  return toFpFullUrl(videoUrl, [{ type: MediaOpType.VideoCover, value: photoViewCfg.FP_VIDEO_COVER_SUFFIX }]);
}

export function getWaterMarkUrl(imageUrl: string, markStr: string) {
  return pipeNosUrl(imageUrl, photoViewCfg.NOS_THUMB_SUFFIX, markStr);
}

export function getVideoPassCover(videoUrl: string) {
  if (isNosUrl(videoUrl)) {
    return getNosVideoCover(videoUrl);
  } else if (isFpUrl(videoUrl)) {
    return getFpVideoCover(videoUrl);
  } else {
    return videoUrl;
  }
}

export function getVideoAuditCover(videoUrl: string) {
  if (isNosUrl(videoUrl)) {
    return getWaterMarkUrl(getNosVideoCover(videoUrl), photoViewCfg.NOS_AUDITING_WATERMARK_SUFFIX);
  } else if (isFpUrl(videoUrl)) {
    return toFpFullUrl(videoUrl, [
      { type: MediaOpType.VideoCover, value: photoViewCfg.FP_VIDEO_COVER_SUFFIX },
      { type: MediaOpType.Watermark, value: photoViewCfg.FP_AUDITING_WATERMARK_SUFFIX },
    ]);
  } else {
    return videoUrl;
  }
}

function pipeNosUrl(url: string, ...nosArgs: string[]) {
  const urlObj = parseUrl(url);
  let pipeArgs = nosArgs;
  let concatUrl = url;
  if (!urlObj.query) {
    const firstArg = _.first(nosArgs);
    pipeArgs = _.tail(nosArgs);
    if (firstArg) {
      concatUrl += "?" + firstArg;
    }
  }
  if (!_.isEmpty(pipeArgs)) {
    _.forEach(pipeArgs, (arg) => {
      concatUrl += "%7c" + arg; // 管道”|”在URL中编码为”%7c”
    });
  }
  return concatUrl;
}

const CDN_PATH_REP_REGEX = /(https?:\/\/)([a-zA-Z0-9-]+)(.nosdn.127.net.)(.*)/;

export function transNosCdnToSourceUrl(url: string) {
  return url.replace(CDN_PATH_REP_REGEX, "$1nos.netease.com/$2/$4");
}

function getPhotoView(url: string, auditStatus: AuditStatues, isSelf: boolean, lang: Lang): ProcessPhotoData {
  if (auditStatus === AuditStatues.PASS) {
    return { url: url };
  } else if (auditStatus === AuditStatues.Reject) {
    if (isSelf) {
      return auditRejctSelfView(url, lang);
    } else {
      // 图片被屏蔽后别人看到依旧是审核中
      return auditingPublicView(url, lang);
    }
  } else {
    if (isSelf) {
      return auditingPhotoMarkView(url, lang);
    } else {
      return auditingPublicView(url, lang);
    }
  }
}

abstract class AbstractImageView {
  protected url: string;
  protected ops?: Fop[];

  constructor(url: string, ops?: Fop[]) {
    this.url = url;
    this.ops = ops || [];
  }

  isNosUrl() {
    return isNosUrl(this.url);
  }

  isFpUrl() {
    return isFpUrl(this.url);
  }

  addOp(op: Fop) {
    this.ops.push(op);
  }

  addThumbOp(thumb: string) {
    this.ops.push({ type: MediaOpType.Thumb, order: FopOrder.Thumb, value: thumb });
  }

  addWaterMarkOp(watermark: string) {
    this.ops.push({ type: MediaOpType.Watermark, order: FopOrder.Watermark, value: watermark });
  }

  protected abstract getForNos(lang: Lang): ProcessPhotoData;

  protected abstract getForFp(lang: Lang): ProcessPhotoData;

  get(lang: Lang): ProcessPhotoData {
    if (!this.url) {
      return { url: "" };
    }
    if (this.isNosUrl()) {
      return this.getForNos(lang);
    } else if (this.isFpUrl()) {
      return this.getForFp(lang);
    } else {
      return { url: this.url };
    }
  }

  toData() {
    return { url: this.url, ops: this.ops };
  }
}

function thumbView(url: string, ops: Fop[], lang: Lang) {
  return ThumbImageView.create(url, _.clone(ops)).get(lang);
}

class AuditRejectSelfImageView extends AbstractImageView {
  protected getForNos() {
    return { url: photoViewCfg.NOS_REJECT_SELF_VIEW_URL, ops: [] };
  }
  protected getForFp(lang: Lang): ProcessPhotoData {
    const rejectSelfUrl = FpAuditConfigMap.get(lang)?.RejectSelfViewUrl || photoViewCfg.FP_REJECT_SELF_VIEW_URL;
    return { url: rejectSelfUrl, ops: [] };
  }

  static create(url: string) {
    return new this(url);
  }
}

class AudtingWaterMarkView extends AbstractImageView {
  protected getForNos() {
    this.addWaterMarkOp(photoViewCfg.NOS_AUDITING_WATERMARK_SUFFIX);
    return this.toData();
  }

  protected getForFp(lang: Lang) {
    const watermarkUrl = FpAuditConfigMap.get(lang)?.WatermarkUrl || photoViewCfg.FP_WATERMARK_URL;
    const watermarkSuffix = "watermark/1/image/" + base64Encode(watermarkUrl) + "/gravity/5"; // bese64
    this.addWaterMarkOp(watermarkSuffix);
    return this.toData();
  }
  static create(url: string) {
    return new this(url);
  }
}

class AudtingPublicView extends AbstractImageView {
  protected getForNos() {
    return { url: photoViewCfg.NOS_AUDITING_PUBLIC_URL };
  }

  protected getForFp(lang: Lang) {
    const auditingPublicUrl = FpAuditConfigMap.get(lang)?.AuditingPublicUrl || photoViewCfg.FP_AUDITING_PUBLIC_URL;
    return { url: auditingPublicUrl };
  }

  static create(url: string) {
    return new this(url);
  }
}

class ThumbImageView extends AbstractImageView {
  getForNos() {
    this.addThumbOp(photoViewCfg.NOS_THUMB_SUFFIX);
    return this.toData();
  }

  getForFp() {
    this.addThumbOp(photoViewCfg.FP_THUMB_SUFFIX);
    return this.toData();
  }

  static create(url: string, ops: Fop[]) {
    return new this(url, ops);
  }
}

function auditRejctSelfView(url: string, lang: Lang) {
  return AuditRejectSelfImageView.create(url).get(lang);
}

function auditingPhotoMarkView(url: string, lang: Lang) {
  return AudtingWaterMarkView.create(url).get(lang);
}

export function auditingPublicView(url: string, lang: Lang) {
  return AudtingPublicView.create(url).get(lang);
}

interface ImageView {
  pic: string;
  thumb: string;
}

export function getPhotoViewItem(url: string, auditStatus: AuditStatues, isSelf: boolean, lang: Lang): ImageView {
  const photoRecord = getPhotoView(url, auditStatus, isSelf, lang);
  const thumb = thumbView(removeQueryString(photoRecord.url || ""), photoRecord.ops, lang);
  return { pic: toFullUrl(photoRecord), thumb: toFullUrl(thumb) };
}

export function getImgListView(urlList: string[], auditList: AuditStatues[], isSelf: boolean, lang: Lang): ImageView[] {
  urlList = urlList.filter((url) => validateUrl(url));
  auditList = auditList.slice(0, urlList.length);
  return _.zipWith(urlList, auditList, function (url, audit) {
    return { url: url, audit: audit };
  }).map((r) => {
    return getPhotoViewItem(r.url, r.audit, isSelf, lang);
  });
}

export function getImgCsvListView(urlListCsv: string, auditListCsv: string, isSelf: boolean, lang: Lang): ImageView[] {
  const urlList = csvStrToArray(urlListCsv);
  const auditList = csvStrToIntArray(auditListCsv);
  return getImgListView(urlList, auditList, isSelf, lang);
}
