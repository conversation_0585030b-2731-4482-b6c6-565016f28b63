import * as <PERSON><PERSON>yan from "bunyan";
import * as fs from "fs";
import * as path from "path";
import * as _ from "lodash";
import * as config from "./config";
import { formatDate } from "./util";
import { BunyanPoPoAlertStream } from "./bunyanPoPoAlert";
import { bunyanPoPoAlertCfg } from "../pyq-server/common/config";

const LogConfig = config.log || {
  level: "info",
  printInConsole: false,
  schema: "com.netease.leihuo.ccc.base.model.tables.v1.L10MdLog",
};

if (config.testCfg.test_env) {
  LogConfig.level = "debug";
}

const LogDir = config.LOG_PATH || path.join(__dirname, "/../log");
const logPath = (fileName) => path.join(LogDir, fileName);

function FileStream(option) {
  this.name = option.name;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
FileStream.prototype.write = function (rawRec: any) {
  const fullName = this.name + ".log";
  const rec = { ...rawRec };
  rec.schema = LogConfig.schema || "";
  rec.ts = formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
  fs.appendFile(logPath(fullName), JSON.stringify(rec) + "\n", (err) => {
    if (err) {
      console.error("FileStreamWriteError", err);
    }
  });
};

export function createLogger(params) {
  params = _.defaultsDeep(params, {
    serializers: {
      req: Bunyan.stdSerializers.req,
      res: Bunyan.stdSerializers.res,
      err: Bunyan.stdSerializers.err,
    },
  });
  return new Bunyan(params);
}

export function getLogger(name) {
  const streams = [
    {
      level: LogConfig.level || "info",
      type: "raw",
      stream: new FileStream({ name: name }),
    },
    {
      level: "error",
      type: "raw",
      stream: new FileStream({ name: name + "_error" }),
    },
  ];

  if (LogConfig.printInConsole && process.env.NODE_ENV !== "production") {
    streams.push({
      level: "debug",
      type: null,
      stream: process.stdout,
    });
  }

  if (bunyanPoPoAlertCfg.enable) {
    const cfg = bunyanPoPoAlertCfg;
    let atUids = "";
    if (cfg.atUids) {
      atUids = cfg.atUids.join(",");
    }
    streams.push({
      level: cfg.level || "error",
      type: "raw",
      stream: new BunyanPoPoAlertStream({
        webhookUrl: cfg.webhookUrl,
        secretKey: cfg.secretKey,
        project: cfg.project,
        biz: cfg.biz,
        env: cfg.env,
        minNotifyInterval: cfg.minNotifyInterval,
        atUids,
        onError: (err) => {
          console.error(err);
        },
      }),
    });
  }

  return createLogger({
    name: name,
    streams: streams,
  });
}

export const slowSqlLogger = getLogger("slowSql");
export const slowReqLogger = getLogger("slowReq");
export const slow3rdLogger = getLogger("slow3rdApi");

export const imageAuditLogger = getLogger("image_audit");
