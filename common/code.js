﻿var Canvas = require('canvas');

exports.genImg = function (len, params) {
    len = len || 6;

    var s = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    var code = '';
    for (var i = 0; i < len; i++) {
        code += s.substr(parseInt(Math.random() * 36, 10), 1);
    }
    var start = 5,
        font = 'bold {FONTSIZE}px arial',//"Bold Italic {FONTSIZE}px arial,sans-serif";//"13px sans-serif";
        colors = ['rgb(255,165,0)', 'rgb(16,78,139)', 'rgb(0,139,0)', 'rgb(255,0,0)'],
        trans = { c: [-0.108, 0.108], b: [-0.05, 0.05] },
        fontSizes = [20, 22, 24],
        fontSizeNum = fontSizes.length - 1;

    var canvas = new Canvas(140, 30);
    var ctx = canvas.getContext('2d');
    for (i = 0; i < len; i++){
        ctx.font = font.replace('{FONTSIZE}', fontSizes[Math.round(Math.random() * fontSizeNum)]);
        ctx.fillStyle = colors[Math.round(Math.random() * 10) % 4];//"rgba(0, 0, 200, 0.5)";
        ctx.fillText(code[i], start, 23, 80);
        ctx.fillRect();
//        ctx.translate(start, 15);
        //a:水平缩放，default：1 ,取值：0.89,1.32,-0.56等,
        //b:y轴斜切，default：0 ,取值：-0.89,1.32等,
        //c:x轴斜切，default：0 ,取值：-0.89,1.32等,
        //d:垂直缩放，default：1 ,取值：-0.89，0.88,1.32等,
        //e:平移，default：0 ,取值：-53,52等,
        //f:纵称，default：0 ,取值：-53,52等,
        var c = getRandom(trans['c'][0], trans['c'][1]);
        var b = getRandom(trans['b'][0], trans['b'][1]);
        ctx.transform(1, b, c, 1, 0, 0);
        //ctx.transform(a, b, c, d, e, f);
        start += 20;
    }

    var buf = canvas.toDataURL();
    var base64Data = buf.replace(/^data:image\/\w+;base64,/, '');
    var dataBuffer = new Buffer(base64Data, 'base64');

    return { code: code, buffer: dataBuffer, dataURL: buf};
};

function getRandom(start, end){
    return start + Math.random() * (end - start);
}
