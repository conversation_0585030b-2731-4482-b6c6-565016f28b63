/* eslint-disable prefer-promise-reject-errors */
const Promise = require("bluebird");
const _ = require("lodash");
const util = require("./util");
const SensitiveTextCheck = require("../common/data").qnm.textFilter;

module.exports = ParamsValidator;

const WholeRules = {
  RequiredAny: "RequiredAny",
};

const REGEXP_TPYE = {
  url: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/,
};

function ParamsValidator(params) {
  this.params = params;
  this.singlePropRules = {};
  this.wholeRules = {};
}

ParamsValidator.from = function (params) {
  return new ParamsValidator(params);
};

ParamsValidator.prototype.param = function (prop, rule) {
  let required = true;
  if (rule && rule.default) {
    required = false;
  }
  const curRule = { type: String, required: required };
  _.merge(curRule, rule);
  this.singlePropRules[prop] = this.singlePropRules[prop] || [];
  this.singlePropRules[prop].push(curRule);
  return this;
};

ParamsValidator.prototype.required = function (prop) {
  return this.param(prop, {});
};

ParamsValidator.prototype.requiredAny = function (propList) {
  this.wholeRules[WholeRules.RequiredAny] = propList;
  return this;
};

ParamsValidator.prototype.handleWholeRules = function () {
  const self = this;
  return new Promise(function (resolve, reject) {
    _.forEach(self.wholeRules, function (value, ruleKey) {
      if (ruleKey === WholeRules.RequiredAny) {
        const notContainsAnyProps = _.isEmpty(
          _.intersection(
            _.keys(
              _.pickBy(self.params, function (val) {
                return !_.isEmpty(formateString(val));
              })
            ),
            value
          )
        );
        if (notContainsAnyProps) {
          reject({ errorType: "ParamRequired", needOneOfParams: value });
        }
      }
    });
    resolve(true);
  });
};

function validateStringType(resolve, reject, checkProp, checkValue, rule) {
  if (rule.maxlen && checkValue.length > rule.maxlen) {
    reject({ errorType: "ParamTooLong", param: checkProp, maxlen: rule.maxlen, paramValue: checkValue });
  }
  if (rule.minlen && checkValue.length < rule.minlen) {
    reject({ errorType: "ParamTooShort", param: checkProp, minlen: rule.minlen, paramValue: checkValue });
  }
  if (rule.startsWith && !checkValue.startsWith(rule.startsWith)) {
    reject({ errorType: "ParamNotMatch", param: checkProp, mustStartsWith: rule.startsWith });
  }
  let checkSensitive = rule.sensitiveCheck;
  if (rule.sensitive) {
    checkSensitive = rule.sensitive.check;
  }
  if (checkSensitive && SensitiveTextCheck(checkValue)) {
    let errorObj = { errorType: "ContainSensitiveWord", param: checkProp, value: checkValue };
    if (rule.sensitive && rule.sensitive.error) {
      errorObj = rule.sensitive.error;
    }
    reject(errorObj);
  }
  if (rule.textType && rule.textType === "url") {
    if (!REGEXP_TPYE.url.test(checkValue)) {
      reject({ errorType: "ParamNotUrl", param: checkProp, value: checkValue });
    }
  }
  if (rule.values) {
    if (!_.includes(rule.values, checkValue)) {
      reject({ errorType: "ParamInvalid", param: checkProp, permitValues: rule.values });
    }
  }
}

function validateNumberType(resolve, reject, checkProp, checkValue, rule, context) {
  if (checkValue) {
    const parseValue = parseInt(checkValue);
    if (Number.isNaN(parseValue)) {
      reject({ errorType: "ParamIsNotNumber", param: checkProp, paramValue: checkValue });
    } else {
      context.params[checkProp] = checkValue = parseValue;
    }
  }
  if (rule.min && checkValue < rule.min) {
    reject({ errorType: "ParamTooMin", param: checkProp, minValue: rule.min });
  }
  if (rule.max && checkValue > rule.max) {
    reject({ errorType: "ParamTooMax", param: checkProp, maxValue: rule.max });
  }

  if (rule.values) {
    if (!_.includes(rule.values, checkValue)) {
      reject({ errorType: "ParamInvalid", param: checkProp, permitValues: rule.values });
    }
  }
}

function validateArrayType(resolve, reject, checkProp, checkValue, rule, context) {
  checkValue = _.filter(checkValue, (s) => !!_.trim(s));
  const isSubSet = function (arr1, arr2) {
    return arr1.length === _.intersection(arr1, arr2).length;
  };
  if (!_.isArray(checkValue)) {
    reject({ errorType: "ParamTypeArray", param: checkProp, needType: "Array" });
  }
  if (!checkValue) {
    reject({ errorType: "ParamRequired", param: checkProp });
  }
  if (rule.maxSize && checkValue.length > rule.maxSize) {
    reject({ errorType: "ParamArraySizeTooLarge", param: checkProp, maxSize: rule.maxSize });
  }
  if (rule.minSize && checkValue.length < rule.minSize) {
    reject({ errorType: "ParamArraySizeTooSmall", param: checkProp, minSize: rule.minSize });
  }
  if (rule.subSet && !isSubSet(checkValue, rule.subSet)) {
    reject({ errorType: "ParamInvalid", param: checkProp, mustSubSet: rule.subSet });
  }
  if (rule.textType && rule.textType === "url") {
    const isUrl = _.every(checkValue, function (value) {
      return REGEXP_TPYE.url.test(value);
    });
    if (!isUrl) {
      reject({ errorType: "ParamNotUrl", param: checkProp, value: checkValue });
    }
  }
  if (rule.each) {
    const ruleForEach = rule.each;
    if (ruleForEach.type === Number) {
      context.params[checkProp] = _.map(checkValue, function (item) {
        return parseInt(item, 10);
      });
    }
  }
}

function validateJsonArrayType(resolve, reject, checkProp, checkValue, rule, context) {
  let newCheckValue;
  try {
    newCheckValue = JSON.parse(checkValue);
    context.params[checkProp] = newCheckValue;
  } catch (err) {
    reject({ errorType: "ParamNotJson", param: checkProp });
  }
  if (!_.isArray(newCheckValue)) {
    reject({ errorType: "ParamNotJsonArray", param: checkProp });
  } else {
    validateArrayType(resolve, reject, checkProp, newCheckValue, rule, context);
  }
}

function formateString(str) {
  return util.removeEmoji(_.trim(str));
}

function isValueIsEmpty(checkValue) {
  return checkValue === null || checkValue === undefined || checkValue === ""
}

function isValueNotEmpty(checkValue) {
  let isEmpty = isValueIsEmpty(checkValue)
  return !isEmpty
}

ParamsValidator.prototype.handleSingleRules = function () {
  const self = this;
  let checkValue;

  return new Promise(function (resolve, reject) {
    _.forEach(self.singlePropRules, function (rules, checkProp) {
      _.forEach(rules, function (rule) {
        checkValue = self.params[checkProp];
        if (_.isString(checkValue)) {
          checkValue = formateString(checkValue);
          self.params[checkProp] = checkValue;
        }
        if (isValueIsEmpty(checkValue) && rule.hasOwnProperty("default")) {
          checkValue = rule.default;
          self.params[checkProp] = checkValue;
        }

        if (self.params.hasOwnProperty(checkProp) && isValueNotEmpty(checkValue)) {
          if (rule.type === String) {
            validateStringType(resolve, reject, checkProp, checkValue, rule);
          } else if (rule.type === Number) {
            validateNumberType(resolve, reject, checkProp, checkValue, rule, self);
          } else if (rule.type === Array) {
            if (rule.allowJsonArray && _.isString(checkValue)) {
              try {
                checkValue = JSON.parse(checkValue);
                self.params[checkProp] = checkValue;
              } catch (err) {
                reject({ errorType: "ParamNotJsonArray", param: checkProp });
              }
            }
            if (rule.allowCsvArray && _.isString(checkValue)) {
              try {
                checkValue = util.csvStrToArray(checkValue);
                self.params[checkProp] = checkValue;
              } catch (err) {
                reject({ errorType: "ParamsNotCsvArray", param: checkProp });
              }
            }
            validateArrayType(resolve, reject, checkProp, checkValue, rule, self);
          } else if (rule.type === PARAM_TYPES.JSON_ARRAY) {
            validateJsonArrayType(resolve, reject, checkProp, checkValue, rule, self);
          }
        } else {
          if (rule.required) {
            reject({ errorType: "ParamRequired", param: checkProp });
          }
        }
      });
    });
    resolve(true);
  });
};

ParamsValidator.prototype.validate = function () {
  const self = this;
  return self.handleWholeRules().then(function () {
    return self.handleSingleRules();
  });
};

const PARAM_TYPES = {
  JSON_ARRAY: "JSON_ARRAY",
};

ParamsValidator.PARAM_TYPES = PARAM_TYPES;
