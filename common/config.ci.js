"use strict";
const config_all_1 = require("./config.all");
let config = null;
const APP_HOST = "http://***************";
config = (0, config_all_1.initConfig)({}, APP_HOST);
// 配置文件
config.testCfg.debug = true;
config.testCfg.skip_auth = true;
config.testCfg.db_debug = process.env.DB_DEBUG === "true";
config.testCfg.skip_fetch_nos_image_size = true;
config.testCfg.redis_debug = process.env.REDIS_DEBUG === "true";
config.testCfg.request_debug = process.env.RQ_DEBUG === "true";
config.log.printInConsole = process.env.LOG_CONSOLE === "true";
config.REDIS_CFG = {
    hosts: ["md-test.leihuo.netease.com"],
    host: "md-test.leihuo.netease.com",
    port: 16379,
    db: 0,
    password: "unit_test",
    no_ready_check: false,
    prefix: "qnm_ci:",
};
config.DB_CFG = {
    connectionLimit: 20,
    host: "md-test.leihuo.netease.com",
    port: 3307,
    user: "unit_test",
    password: "unit_test",
    database: "l10_md_ut",
    charset: "utf8mb4",
};
config.SLAVE_DB_CFG = config.DB_CFG;
config.GEO_REDIS_CFG = config.REDIS_CFG;
config.BingQiPuDbConfig = config.DB_CFG;
config.NOS_CFG.accessKey = "80daf02d6f7042b190ef3358091cc335";
config.NOS_CFG.secretKey = "2918bfd8aa064e2b8d7cc6429a9d45eb";
config.JWT_TOKEN_SECRET = "@#!!^&*()$#@~_($%^&##*$o"; // jwt token 生成密钥
config.AUTH_TOKEN_SALT = "&7%^!*(_)$#@!~_$%^#+#"; // 游戏服务端登录等认证用token salt
config.TOKEN_SALT_DEL = "B0RYKL63NP9V12AJSFHMWQ5DX7IGUC84OZET"; // 删除大事记用token salt
config.TOKEN_SALT = "^&*()$#@!!~_($%^##&*+_"; // 客户端推送数据验证用token salt
config.DIGEST_KEY = "3go8&$8*3*3h0k(2)2"; // 图片路径加密密钥
config.dbSlowLogCfg = {
    enable: true,
    threshold: 500,
};
config.reqSlowLogCfg = {
    enable: true,
    threshold: 100,
};
config.thirdApiSlowLogCfg = {
    enable: true,
    threshold: 200,
};
config.batchSendPicLimit = 1;
module.exports = config;
//# sourceMappingURL=config.ci.js.map