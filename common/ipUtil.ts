var util = require('./util');
var ipService = require('../data/ipip');
var _ = require('lodash');


export function getLocationFromIp(ip: string) {
  var realIp = ip && ip.split(',')[1];
  ip = realIp ? util.trim(realIp) : ip;   // 代理上网后的ip格式为 *************, **************，取后者
  var location = { country: '', province: '', city: '' };
  if (_.isArray(ip) && ip.split('.')[0] == '10') {
    location.city = '局域网 A类';
  }
  else {
    try {
      const parseInfo = ipService.findSync(ip); // 查询 IP 信息，以数组形式返回
      location = _.defaults({ country: parseInfo[0], province: parseInfo[1], city: parseInfo[2] },
        { country: '', province: '', city: '' });
      if (location.country !== '中国') { //免费版国外信息只有国家是精确的
        location.province = '';
        location.city = '';
      }
    } catch (err) {
      //ignore parse ip error
    }
  }
  return location;
}