import * as _ from "lodash";
import * as uuid from "node-uuid";
import * as url from "url";
import { Pagination, PaginationMeta } from "./type";
import { csvStrToArray } from "./util";
import { Lang } from "../pyq-server/types/type";
import { isVnRegion } from "../pyq-server/serverRegion";

export function isValidCallbackName(name: string): boolean {
  return /^[0-9a-zA-Z_-]+$/.test(name);
}

export function getFirstXForwardedForIp(ipStr: string) {
  const ipList = csvStrToArray(ipStr);
  return ipList[0];
}

// 当值为空的时候才覆盖
export function assignForEmpty(obj, supply) {
  for (const k of Object.keys(obj)) {
    if (!obj[k] && supply[k]) {
      obj[k] = supply[k];
    }
  }
  return obj;
}

export function fillCsvStr(char: string, len: number) {
  const csvStr = new Array(len).fill(char).join(",");
  return csvStr;
}

export function mergeIds(arr1: number[], arr2: number[]): number[] {
  const set: Set<number> = new Set();

  for (const e of arr1) {
    set.add(e);
  }

  for (const e of arr2) {
    set.add(e);
  }

  const ret = Array.from(set);
  return ret;
}

export function replaceHttps(url: string) {
  if (url) {
    return url.replace(/^http:\/\//, "https://");
  } else {
    return "";
  }
}

export function getServerIdFromRoleId(roleId: number): number {
  const server = _.trimStart(_.toString(roleId).slice(-4), "0");
  const serverId = parseInt(server, 10);
  return serverId;
}

export function contains<T>(arr: T[], ele: T): boolean {
  const set = new Set(arr);
  return set.has(ele);
}

export function transformObject<T, R>(src: T, transformer: (ele: R) => R): T {
  if (_.isArray(src)) {
    const arr = src as unknown as any[];
    for (let i = 0; i < arr.length; i++) {
      arr[i] = transformObject(arr[i], transformer);
    }
    return arr as unknown as T;
  } else if (_.isObject(src)) {
    const props = Object.keys(src);
    for (const p of props) {
      src[p] = transformObject(src[p], transformer);
    }
    return src;
  } else {
    return transformer(src as unknown as R) as unknown as T;
  }
}

export function transformObjectForHttps<T>(src: T): T {
  return transformObject(src, function (ele: string) {
    if (_.isString(ele)) {
      return replaceHttps(ele);
    } else {
      return ele;
    }
  });
}

export function removeQueryString(str: string) {
  const urlObj = url.parse(str);
  return urlObj.protocol + "//" + urlObj.host + urlObj.pathname;
}

export function isAllMatch<T>(arr: T[], predict: (ele: T) => boolean): boolean {
  return !_.some(arr, (ele) => !predict(ele));
}

export function keyToRecordMap<T, P extends keyof T>(records: T[], key: P): Map<T[P], T> {
  const map: Map<T[P], T> = new Map();
  for (const r of records) {
    if (r && !isNullOrUndefined(r[key])) {
      map.set(r[key], r);
    }
  }
  return map;
}

export function keyToMapArray<T>(records: Array<T>, key): Map<number, Array<T>> {
  const map: Map<number, Array<T>> = new Map();

  if (!_.isString(key)) throw new Error("转换map的键值必须为字符串");

  for (const item of records) {
    const temp: Array<T> = map.get(item[key]) as Array<T>;

    if (!temp) {
      map.set(item[key], [item]);
      continue;
    }

    temp.push(item);
    map.set(item[key], temp);
  }

  return map;
}

export function zipArrayWithMerge<T, P>(arr: T[], brr: P[]): (T & P)[] {
  const minLen = Math.min(arr.length, brr.length);
  const list: (T & P)[] = [];
  for (let i = 0; i < minLen; i++) {
    const item = Object.assign({}, arr[i], brr[i]);
    list.push(item);
  }
  return list;
}

export function uuidV1(): string {
  const id = uuid.v1().replace(/-/g, "");
  return id;
}

export function getPageMeta(pagination: Pagination, totalCount: number): PaginationMeta {
  const totalPage = Math.ceil(totalCount / pagination.pageSize);
  const curPage = Math.min(pagination.page, totalPage);
  return { curPage: curPage, totalPage: totalPage, totalCount: totalCount };
}

export function hasQuery(u: string): boolean {
  const urlObj = url.parse(u);
  return !!urlObj.query;
}

export function validateUrl(url: string) {
  return /^(?:(?:(?:https?|ftp):)?\/\/)(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)(?:\.(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)*(?:\.(?:[a-z\u00a1-\uffff]{2,})))(?::\d{2,5})?(?:[/?#]\S*)?$/i.test(
    url
  );
}

export function getIp(req) {
  return (
    getFirstXForwardedForIp(req.headers["x-forwarded-for"]) ||
    req.headers["x-real-ip"] ||
    req.connection.remoteAddress ||
    req.socket.remoteAddress ||
    req.connection.socket.remoteAddress
  );
}

export function cacheKeyGen(kp: string, cacheObj: Record<string, string | number>) {
  const coKey = Object.keys(cacheObj)
    .filter((k) => !isNullOrUndefined(cacheObj[k]))
    .map((k) => _.snakeCase(k) + "_" + cacheObj[k])
    .join(":");
  const key = kp + ":" + coKey;
  return key;
}

export function getIpBehindProxy(req) {
  const forwardForIpList = csvStrToArray(req.headers["x-forwarded-for"]);
  if (forwardForIpList && forwardForIpList.length > 0) {
    return forwardForIpList[0];
  }
  if (req.headers["x-real-ip"]) {
    return req.headers["x-real-ip"];
  }
  return req.connection.remoteAddress || req.socket.remoteAddress || req.connection.socket.remoteAddress;
}

/** 获取时间戳, 单位s */
export function getUnixTimeStamp() {
  return Math.ceil(Date.now() / 1000);
}

export function appendQueryStringToUrl(url: string, qs: { [key: string]: string }): string {
  const urlObj = new URL(url);
  for (const name of Object.keys(qs)) {
    urlObj.searchParams.append(name, qs[name]);
  }
  return urlObj.toString();
}

/**
 * Determine the mobile operating system.
 * This function returns one of 'iOS', 'Android', 'Windows Phone', or 'unknown'.
 *
 * @returns {String}
 */
export function getMobileOperatingSystem(userAgent: string) {
  // Windows Phone must come first because its UA also contains "Android"
  if (/windows phone/i.test(userAgent)) {
    return "Windows Phone";
  }

  if (/android/i.test(userAgent)) {
    return "Android";
  }

  // iOS detection from: http://stackoverflow.com/a/9039885/177710
  if (/iPad|iPhone|iPod/.test(userAgent)) {
    return "iOS";
  }

  return "unknown";
}

export function isNullOrUndefined(val) {
  return val === null || val === undefined;
}

export function unifyUpResults(results: Record<string, any>): Record<string, any> {
  const result = {};
  for (let i = 0, l = results.length; i < l; i++) {
    const item = results[i];
    for (const k in item) {
      const v = item[k];
      typeof v === "number" && (result[k] = (result[k] || 0) + v);
    }
  }
  return result;
}

export function getSubArrayByPage<T>(array: T[], page: number, pageSize: number): T[] {
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;

  if (startIndex >= array.length) {
    return []; // 超出数组大小，返回空数组
  }

  return array.slice(startIndex, Math.min(endIndex, array.length));
}

export function escapeLikeStr(likeStr: string): string {
  return likeStr.replace(/[_%]|\\/g, function (escapeChar) {
    return "\\" + escapeChar;
  });
}

export function getLangFromRequest(req): Lang {
  return getLangFromReqAndSession(req, req.session);
}

export function getLangFromReqAndSession(req, sessionArg): Lang {
  let lang: Lang = "cn";
  const session = sessionArg || req.session;
  if (session) {
    if (session.language) {
      lang = session.language;
    }
  } else {
    // 只在越南区域的时候才会根据header的accept-language来判断
    if (isVnRegion()) {
      if (req.headers["accept-language"]) {
        const langFromHeader = req.headers["accept-language"].split(",")[0];
        if (langFromHeader) {
          lang = langFromHeader;
        }
      }
    }
  }
  return lang;
}

export function base64Encode(str: string) {
  if (!str) return "";
  return Buffer.from(str).toString("base64");
}

export function convertBoolVal(flag: boolean | string): boolean {
  if (typeof flag === "string") {
    return flag === "true";
  } else {
    return flag;
  }
}
