export interface ReturnCode {
  code?: number;
  msg?: string;
}

export interface ReturnMoment extends ReturnCode {
  id?: number;
  imglist?: {
    pic: string;
    thumb: string;
  }[];
}

export interface BasicParams {
  roleid: number;
  skey?: string;
}

export interface BasicTargetParams extends BasicParams {
  targetid: number;
}

export interface MomentParam extends BasicParams {
  text: string;
  imglist?: string;
}

export interface Pagination {
  page: number;
  pageSize: number;
}

export interface BasePageParams extends BasicParams {
  page: number;
  pageSize: number;
}

export enum MomentStatus {
  Normal = 0,
  USER_DEL = -1,
  ADMIN_DEl = -2,
}

export interface Pagination {
  page: number;
  pageSize: number;
}

export interface MdUserRecord {
  ID: number;
  Avatar: string;
  AvaAuthStatus: number;
}

export interface MdPhotoRecord {
  ID: number;
  Url: string;
  AuditStatus: number;
  PhotoAlbumID: number;
}
export interface MdPhotoAlbumRecord {
  ID: number;
  Name: string;
  desc: string;
  CoverUrl: string;
  UpdateTime: number;
}

export interface MdMomentRecord {
  ID: number;
  UserId: number;
  Text: string;
  ImgList: string;
  ImgAudit: string;
  Status: number;
  CreateTime: number;
}

export interface CookieCfg {
  path: string;
  domain: string;
  httpOnly: boolean;
  maxAge?: number;
  secure?: boolean;
  expires?: Date;
}

export * from "../models/modelInterface";

export interface UpdateResult {
  affectedRows: number;
  changedRows: number;
  insertId: number;
}

export type FilterFlags<Base, Condition> = {
  [Key in keyof Base]: Base[Key] extends Condition ? Key : never;
};

export type FilterKeys<Base, Condition> = {
  [Key in keyof Base]: Key extends Condition ? Key : never;
};

export type AllowKeys<Base, Condition> = FilterKeys<Base, Condition>[keyof Base];

export type SubKeysType<Base, Condition> = Pick<Base, AllowKeys<Base, Condition>>;

export type Except<Base, Condition> = Omit<Base, AllowKeys<Base, Condition>>;

export type NosFileType = "photo" | "moment" | "avatar" | "video" | "tape" | "screenshot" | "fireworks";

export interface PaginationMeta {
  curPage: number;
  totalPage: number;
  totalCount: number;
}

export interface IRedisCfg {
  hosts?: string[];
  host: string;
  port: number;
  db?: number;
  password: string;
  no_ready_check?: boolean;
  prefix?: string;
}
