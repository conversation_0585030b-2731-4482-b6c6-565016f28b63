import { MediaRecordType } from "../audit-server/services/auditHandler";
import { AuditStatues } from "./constants";

export interface PicInfo {
  type: MediaRecordType;
  id: string;
}

export enum PicMediaType {
  Image = "0",
  Video = "1",
  GIF = "2",
}

export interface PicItem {
  url: string;
  status: AuditStatues;
  role_id: string;
  pic_id: string;
  media: PicMediaType;
  note: string;
}

export interface IReturnPicParams {
  product: EProduct;
  pic_list: PicItem[];
}

export interface IAuditNote {
  ip: string;
}

export interface SendAuditOption {
  roleId: string | number;
  picId: string;
  media: PicMediaType;
  ip: string;
  manual?: 1 | 0;
  serverId? : number
  accountId?: string
}

export type EProduct = "qnm" | "md" | "L10" | "L10CHAT";

export const statusHash = {
  INIT: 0,
  PASS: 1,
  REJECT: -1,
  FORCE_MANUAL: 2,
};

type AUDIT_STATUS = "INIT" | "PASS" | "REJECT" | "FORCE_MANUAL";

export interface AuditImageResult {
  success: boolean;
  dup_info: DupInfoItem[];
}

export interface DupInfoItem {
  status: AUDIT_STATUS;
  url: string;
  role_id: string;
  pic_id: string;
  media: PicMediaType;
  note?: string;
}
