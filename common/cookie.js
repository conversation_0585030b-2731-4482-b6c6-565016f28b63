var FFI = require('ffi');
var ref = require('ref');
const bluebird = require('bluebird')
const Request = require('./request')
const Config = require('./config')
const _ = require('lodash')
const util = require('../common/util')
const config = require('../common/config')

let ursSoInstance = null
function getUrsSo () {
  if (!ursSoInstance) {
    ursSoInstance = init()
  }
  return ursSoInstance
}

exports.getUrsInfo = function(cookies) {
    var result = { result: -1 };

    var sessCookie = cookies.NTES_SESS;
    var passport = cookies.NTES_PASSPORT;
    var ydCookie = cookies.NTES_YD_SESS;
    var ydPassport = cookies.NTES_YD_PASSPORT;
    var cookieArr = [sessCookie, passport, ydCookie, ydPassport];
    var loginType = sessCookie ? 0 : (passport ? 1 : (ydCookie ? 2 : 3));   // 0 邮箱登录 1 邮箱十天免登录 2 手机登录 3 手机保持登录（一天）
    var cookie = cookieArr[loginType];
    if (!cookie) {
        return bluebird.resolve(result);
    }


    if (config.testCfg.test_env && util.isJson(sessCookie)) {
        let payload = JSON.parse(sessCookie)
        if (payload.type === 'ngpLogin') {
            let time = Math.ceil((Date.now() / 1000))
            return bluebird.resolve({
                result: 1,
                ssn: payload.urs,
                Uid: '',
                mobile: 'O15869113727',
                autoLogin: '0',
                createTime: time,
                cookieCreateTime: time,
                alias: '15869113727',
                misc: '0||10#0|3|112220|0|dream||045f57b583c0b93fa54a43da0d23fdc9'
            })
        } else {
            return bluebird.resolve({ result: -1 })
        }
    }

    return $getUrsInfoByHttps(loginType, cookie).then(result => {
        let code = result.retCode
        return Object.assign(result.data, { result: code === 200 ? 1 : -1, code })
    }, () => {
        return $getUrsInfoBySo(loginType, cookie)
    });
};
function $getUrsInfoByHttps(loginType, cookie) {
    let cookieName = ['NTES_SESS', 'NTES_PASSPORT', 'NTES_YD_SESS', 'NTES_YD_PASSPORT'][loginType]
    let cfg = Config.URS_COOKIE_PARSE
    let hosts = cfg.HOSTS || []
    let index = 0
    return tryNext()

    function tryNext() {
        let host = hosts[index++]
        if (!host) return bluebird.reject({ failed: true })

        return Request.post(`${host}/validate`, {
            productid: cfg.PRODUCT_ID,
            cookie,
            cookieName,
            recreate: 0
        }, { timeout: 3000 }).catch(ex => {
            return tryNext()
        })
    }
    // Demo:
    // {
    //     "retCode":200,
    //     "msg":"",
    //     "data":{
    //         "ssn":"",
    //         "userip":"",
    //         "mobile":"",
    //         "autologin":"",
    //         "createtime":11,
    //         "cookieCreateTime":11,
    //         "alias":"",
    //         "cookie":""
    //     }
    // }
}
function $getUrsInfoBySo(loginType, cookie) {
    if (loginType % 2 === 1) {
        return validate_persistent_cookie(cookie);
    } else {
        return validate_cookie(cookie);
    }
    // demo:
    // {
    //     result: 1,
    //     ssn: 'cjry_8854',
    //     Uid: '',
    //     mobile: 'O15869113727',
    //     autoLogin: '0',
    //     createTime: 1133669903,
    //     cookieCreateTime: 1517821309,
    //     alias: '15869113727',
    //     misc: '0||10#0|3|112220|0|dream||045f57b583c0b93fa54a43da0d23fdc9'
    // }

}

function init() {
    var charPtr = ref.refType(ref.types.char),
        longPtr = ref.refType(ref.types.long);

    return new FFI.Library(__dirname + '/../lib/libcom_netease_urs_ntescode.solib', {
        '_Z15validate_cookiePcS_iS_clS_S_PlS0_S_S_':
            [    //         cookie    ssn       p_id    p_uid    flag    timeout   mobile  autologin createtime  cookiecreatetime alias misc
                'int32', [ 'string', charPtr, 'int32', charPtr, 'string', 'int64', charPtr, charPtr, longPtr, longPtr, charPtr, charPtr ]
            ],
        '_Z26validate_persistent_cookiePcS_iS_clS_PlS0_S_S_':
            [    //         cookie    ssn       p_id    p_uid    flag    timeout  mobile createtime  cookiecreatetime alias misc
                'int32', [ 'string', charPtr, 'int32', charPtr, 'string', 'int64', charPtr, longPtr, longPtr, charPtr, charPtr ]
            ]
    });
}

function validate_cookie(cookie) {
    const ursSO = getUrsSo()
    var ssn = getCharBuff(),
        p_id = 1,
        p_uid  = getCharBuff(),
        flag = '0',
        timeout = 3600,
        mobile  = getCharBuff(),
        autoLogin  = getCharBuff(),
        createTime  = ref.alloc(ref.types.long),
        cookieCreateTime  = ref.alloc(ref.types.long),
        alias  = getCharBuff(),
        misc  = getCharBuff(),
        result = ursSO['_Z15validate_cookiePcS_iS_clS_S_PlS0_S_S_'](cookie, ssn, p_id, p_uid, flag, timeout, mobile, autoLogin, createTime, cookieCreateTime, alias, misc);
    return {
        result: result,
        ssn: getBuffStr(ssn), Uid: getBuffStr(p_uid),
        mobile: getBuffStr(mobile), autoLogin: getBuffStr(autoLogin),
        createTime: createTime.deref(), cookieCreateTime: cookieCreateTime.deref(),
        alias: getBuffStr(alias), misc: getBuffStr(misc)
    };
}

function validate_persistent_cookie(cookie) {
    const ursSO = getUrsSo()
    var ssn = getCharBuff(),
        p_id = 1,
        p_uid  = getCharBuff(),
        flag = '0',
        timeout = 3600,
        mobile  = getCharBuff(),
        createTime  = ref.alloc(ref.types.long),
        cookieCreateTime  = ref.alloc(ref.types.long),
        alias  = getCharBuff(),
        misc  = getCharBuff(),
        result = ursSO['_Z26validate_persistent_cookiePcS_iS_clS_PlS0_S_S_'](cookie, ssn, p_id, p_uid, flag, timeout, mobile, createTime, cookieCreateTime, alias, misc);

    return {
        result: result,
        ssn: getBuffStr(ssn), Uid: getBuffStr(p_uid),
        mobile: getBuffStr(mobile),
        createTime: createTime.deref(), cookieCreateTime: cookieCreateTime.deref(),
        alias: getBuffStr(alias), misc: getBuffStr(misc)
    };
}

function getBuffStr(buff) {
    var str = buff.toString();
    return str.substr(0, str.indexOf('\u0000'));
}

function getCharBuff() {
    var buff = new Buffer(100);
    buff.fill(0);
    buff.type = ref.types.char;
    return buff;
}
