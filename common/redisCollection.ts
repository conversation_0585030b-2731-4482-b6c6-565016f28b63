import * as bluebird from 'bluebird'
import * as _ from 'lodash'
import { ONE_DAY_SECONDS } from './config';
import { getRedis } from './redis';
import { ExpireType } from './redisTypes';
import { hexMd5 } from './util';

export class Queue {
  private key: string
  private md5Prefix: string

  constructor(options) {
    this.key = options.key
    this.md5Prefix = options.md5Prefix || 'md5_key:'
  }

  push(obj) {
    const self = this
    const str = JSON.stringify(obj)
    const contentMd5 = hexMd5(str)
    const reHashKey = this.md5Prefix + contentMd5
    return getRedis().rpushAsync(self.key, reHashKey).then(len => {
      return getRedis().setAsync(self.getReHashFullKey(reHashKey), str, ExpireType.EX, 7 * ONE_DAY_SECONDS)
    })
  }

  isReHashKey(str) {
    return _.isString(str) && str.startsWith(this.md5Prefix)
  }

  getReHashFullKey(reHashKey: string) {
    return this.key + ':' + reHashKey
  }

  len() {
    return getRedis().llenAsync(this.key)
  }

  getThenDelete(key: string) {
    let content = null
    return getRedis().getAsync(key)
      .then(s => {
        content = s
        return getRedis().delAsync(key)
      }).then(() => {
        return content
      })
  }

  pop() {
    let self = this
    return getRedis().lpopAsync(this.key)
      .then(ele => {
        if (self.isReHashKey(ele)) {
          return self.getThenDelete(self.getReHashFullKey(ele))
        } else {
          return ele
        }
      }).then(raw => {
        if (raw) {
          return JSON.parse(raw)
        } else {
          return raw
        }
      })
  }

  popx(n: number) {
    let self = this
    return self.len().then(length => {
      let len = Math.min(n, length)
      return bluebird.map(new Array(len), () => {
        return self.pop()
      }, { concurrency: 5 })
    }).then(list => {
      return _.filter(list, ele => !!ele) // pop 过程中可能被其他进程消费导致 pop 出空元素
    })
  }
}

// 固定列表长度字符串列表
export class FixedStringList {
  private key: string
  private size: number
  constructor(options) {
    this.key = options.key
    this.size = options.size
  }

  push(str: string) {
    const self = this
    return getRedis().lpushAsync(this.key, str)
      .then(() => {
        getRedis().ltrimAsync(self.key, 0, self.size - 1)
      })
  }

  getAll() {
    return getRedis().lrangeAsync(this.key, 0, this.size - 1)
  }

  remove(str) {
    // @ts-ignore
    return getRedis().lremAsync(this.key, 0, str)
  }
}