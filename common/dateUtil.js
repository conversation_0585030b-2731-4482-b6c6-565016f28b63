"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getWeekDs = exports.dayOfWeek = exports.addMonths = exports.format = exports.startOfHour = exports.startOfMinute = exports.isAfter = exports.isBefore = exports.endOfDay = exports.startOfDay = exports.cloneDate = exports.isWithInRange = void 0;
const moment = require("moment");
const util = require("../common/util");
function isWithInRange(date, startDate, endDate) {
    var time = date.getTime();
    var startTime = startDate.getTime();
    var endTime = endDate.getTime();
    if (startTime > endTime) {
        throw new Error("The start of the range cannot be after the end of the range");
    }
    return time >= startTime && time <= endTime;
}
exports.isWithInRange = isWithInRange;
// 返回一个新的实例
function cloneDate(date) {
    return new Date(date.getTime());
}
exports.cloneDate = cloneDate;
function startOfDay(date) {
    const newDate = cloneDate(date);
    newDate.setHours(0, 0, 0, 0);
    return newDate;
}
exports.startOfDay = startOfDay;
function endOfDay(date) {
    const newDate = cloneDate(date);
    newDate.setHours(23, 59, 59, 999);
    return newDate;
}
exports.endOfDay = endOfDay;
function isBefore(date1, date2) {
    return date1.getTime() < date2.getTime();
}
exports.isBefore = isBefore;
function isAfter(date1, date2) {
    return date1.getTime() > date2.getTime();
}
exports.isAfter = isAfter;
function startOfMinute(date) {
    const newDate = cloneDate(date);
    newDate.setSeconds(0, 0);
    return newDate;
}
exports.startOfMinute = startOfMinute;
function startOfHour(date) {
    const newDate = cloneDate(date);
    newDate.setMinutes(0, 0, 0);
    return newDate;
}
exports.startOfHour = startOfHour;
function format(date, format) {
    let pad = util.pad;
    format = format || "yyyy-MM-dd HH:mm:ss";
    let year = date.getFullYear();
    let month = date.getMonth() + 1;
    let day = date.getDate();
    let hour = date.getHours();
    let minute = date.getMinutes();
    let second = date.getSeconds();
    return format
        .replace("yyyy", "" + year)
        .replace("MM", pad(month, 2))
        .replace("dd", pad(day, 2))
        .replace("HH", pad(hour, 2))
        .replace("mm", pad(minute, 2))
        .replace("ss", pad(second, 2));
}
exports.format = format;
function addMonths(date, amount) {
    const newDate = cloneDate(date);
    return moment(newDate).add(amount, "months").toDate();
}
exports.addMonths = addMonths;
function dayOfWeek() {
    return moment().days();
}
exports.dayOfWeek = dayOfWeek;
function getWeekDs(timestamp) {
    const date = new Date(timestamp);
    const day = date.getDay();
    // 将周日(0)转换为7,其他天数减1,这样周一就是0,周日是6
    const adjustedDay = day === 0 ? 7 : day;
    const weekDs = moment(date).subtract(adjustedDay - 1, "days").format("YYYYMMDD");
    return weekDs;
}
exports.getWeekDs = getWeekDs;
//# sourceMappingURL=dateUtil.js.map