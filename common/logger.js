// 日志通用类
var bunyan = require('bunyan'),
    util = require('./util'),
    config = require('./config'),
    isDebug = config.testCfg.debug,
    noTraceReq = config.testCfg.no_trace_req,
    loggers = {};
var _ = require('lodash');

let path = require('path')

function getLogger(name, opt) {
    var logPath = config.LOG_PATH || __dirname + '/../log';
    var logger = loggers[name];
    if (!logger) {
        var streamOpt = {
            level: 'info',
            path: logPath + '/' + name + '.log'
        };
        if (opt && opt.rotation) {
            util.extend(streamOpt, {
                type: 'rotating-file',
                period: '1d',   // daily rotation
                count: 100      // keep 100 back copies
            });
        }
        logger = bunyan.createLogger({
            name: name,
            streams: [streamOpt],
            serializers: {
                req: function reqSerializer(req) {
                    return {
                        method: req.method,
                        url: req.route.path,
                        //                        headers: req.headers,
                        params: req.params
                    };
                }
            }
        });
        loggers[name] = logger;
    }
    return logger;
}

exports.req = function (name, req) {
    if (isDebug) {
        console.log('\n[' + util.getDayStr() + '][' + util.getIp(req) + '] ' + req.route.path);
        console.log(req.params);
    }
    if (noTraceReq) {
        return;
    }

    var logger = getLogger(name, { rotation: true });
    logger.info({ req: req }, '[%s][%s]', util.getDayStr(), util.getIp(req));
};

exports.server = function () {
    if (isDebug) {
        console.log.apply(console, Array.prototype.slice.call(arguments, 1));
    }
    if (noTraceReq) {
        return;
    }

    var logger = getLogger(arguments[0], { rotation: true });
    var obj = arguments[1];
    if (obj instanceof Error) {
        logger.error('[%s]%s', obj.name, obj.message);
    } else {
        logger.info.apply(logger, Array.prototype.slice.call(arguments, 1));
    }
};

exports.error = function () {
    var logger = getLogger('error');
    logger.error.apply(logger, arguments);
};

exports.add = function (fileName, content, option) {
    option = _.defaults(option, { subPath: "" });
    var logPath = config.LOG_PATH || __dirname + '/../log';
    logPath = logPath + option.subPath;
    let logLine = '[' + util.getDayStr() + ']' + content + '\n';
    if (config.testCfg.test_env) {
        process.stdout.write(logLine)
    }
    require('fs').appendFile(logPath + '/' + fileName + '.log', logLine, function (err, res) {
        if (err) {
            console.error(err)
        }
    });
};

exports.getLogPath = function () {
    return config.LOG_PATH || path.join(__dirname, '/../log')
}
