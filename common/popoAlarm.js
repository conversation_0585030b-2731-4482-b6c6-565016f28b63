"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendPoPo = exports.sendPaoPao = void 0;
const paopao_alarm_1 = require("@leihuo/paopao-alarm");
const config_1 = require("../pyq-server/common/config");
const redis_1 = require("./redis");
const paopao = paopao_alarm_1.SendPaoPao2Class.init(config_1.paopaoCfg.url);
function sendPaoPao(content, receivers) {
    return __awaiter(this, void 0, void 0, function* () {
        for (const account of receivers) {
            yield paopao.sendPaoPao(content, account);
        }
    });
}
exports.sendPaoPao = sendPaoPao;
const PAO_PAO_INTERVAL = 5 * 60 * 1000;
function getLastSendTimeKey(type) {
    return "popo_alarm_last_send_time:" + type;
}
function getLastSendTime(type) {
    return __awaiter(this, void 0, void 0, function* () {
        let str = yield (0, redis_1.getRedis)().getAsync(getLastSendTimeKey(type));
        return parseInt(str, 10);
    });
}
function setLastSendTime(type, time) {
    return __awaiter(this, void 0, void 0, function* () {
        let key = getLastSendTimeKey(type);
        let ret = yield (0, redis_1.getRedis)().setAsync(key, time);
        yield (0, redis_1.getRedis)().expireAsync(key, 24 * 3600);
        return ret;
    });
}
function sendPoPo(type, msg, to) {
    return __awaiter(this, void 0, void 0, function* () {
        let lastSendTime = yield getLastSendTime(type);
        let now = Date.now();
        if (now - lastSendTime < PAO_PAO_INTERVAL) {
            // popo 报警CD中
        }
        else {
            let result = yield sendPaoPao(msg, config_1.paopaoCfg.alarmReceivers);
            yield setLastSendTime(type, Date.now());
            return result;
        }
    });
}
exports.sendPoPo = sendPoPo;
//# sourceMappingURL=popoAlarm.js.map