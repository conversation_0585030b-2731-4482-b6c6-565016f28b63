"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DBClient = exports.DBNode = void 0;
const db_1 = require("./db");
var DBNode;
(function (DBNode) {
    DBNode["Master"] = "MASTER";
    DBNode["Slave"] = "SLAVE";
})(DBNode = exports.DBNode || (exports.DBNode = {}));
class DBClient {
    execute(sql, option) {
        return __awaiter(this, void 0, void 0, function* () {
            let result = yield (0, db_1.execute)(sql, option);
            return result;
        });
    }
    static getInstance() {
        if (!this.instance) {
            this.instance = new DBClient();
        }
        return this.instance;
    }
}
exports.DBClient = DBClient;
DBClient.instance = null;
//# sourceMappingURL=db2.js.map