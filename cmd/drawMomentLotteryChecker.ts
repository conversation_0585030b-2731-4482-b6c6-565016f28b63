import { momentLotteryCfg } from "../pyq-server/common/config";
import { clazzLogger } from "../pyq-server/logger";
import { drawMomentLotteryChecker } from "../pyq-server/services/momentLotteryService";
import { CronJob } from "cron";
const logger = clazzLogger("cmd.drawMomentLotteryChecker");

async function main() {
  const job = new CronJob({
    cronTime: momentLotteryCfg.drawCheckerCron,
    onTick: async function () {
      const drawRet = await drawMomentLotteryChecker();
      logger.info({ drawRet }, "drawMomentLotteryCheckerFinished");
    },
    start: false,
    runOnInit: true,
  });

  job.start();
}

main()
  .then(() => {
    console.log("drawMomentLotteryChecker is running");
  })
  .catch((err) => {
    console.error("drawMomentLotteryChecker error", err);
  });
