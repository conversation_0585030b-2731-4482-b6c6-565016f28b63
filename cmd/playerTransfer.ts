#!/usr/bin/env node
import { playerTransfer } from "../pyq-server/services/transfer";
import { transferLogger } from "../pyq-server/logger";

async function main() {
  const args = process.argv.slice(2); // 获取命令行参数，跳过 'node' 和脚本路径

  if (args.length < 2) {
    console.error("Usage: playerTransfer <oldId> <newId>");
    process.exit(1);
  }

  const oldId = parseInt(args[0], 10);
  const newId = parseInt(args[1], 10);

  if (isNaN(oldId) || isNaN(newId)) {
    console.error("Error: oldId and newId must be numbers.");
    process.exit(1);
  }

  transferLogger.info("Starting player transfer from " + oldId + " to " + newId);

  try {
    await playerTransfer(oldId, newId);
    transferLogger.info("Player transfer from " + oldId + " to " + newId + " completed successfully.");
    console.log("Player transfer from " + oldId + " to " + newId + " completed successfully.");
    // 在实际应用中，数据库连接等资源应当在这里正确关闭
    // 例如： await closeDbConnections();
    process.exit(0); // 确保进程在异步操作完成后退出
  } catch (error) {
    transferLogger.error("Error during player transfer from " + oldId + " to " + newId + ":", error);
    console.error("Error during player transfer from " + oldId + " to " + newId + ":", error);
    process.exit(1);
  }
}

main();