import { clazzLogger } from "../pyq-server/logger";
import { autoFixMomentServerId } from "../pyq-server/services/fixMomentIdServerIdService";
const logger = clazzLogger("cmd.fixMomentServerId");
export async function main() {
  const ret = autoFixMomentServerId();
  logger.info({ ret }, "RefreshAllOk");
}

main()
  .then(() => {
    console.log("Done");
  })
  .catch((err) => {
    console.error(err);
  });
