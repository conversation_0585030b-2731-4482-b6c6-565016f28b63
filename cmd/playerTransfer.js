#!/usr/bin/env node
"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const transfer_1 = require("../pyq-server/services/transfer");
const logger_1 = require("../pyq-server/logger");
function main() {
    return __awaiter(this, void 0, void 0, function* () {
        const args = process.argv.slice(2); // 获取命令行参数，跳过 'node' 和脚本路径
        if (args.length < 2) {
            console.error("Usage: playerTransfer <oldId> <newId>");
            process.exit(1);
        }
        const oldId = parseInt(args[0], 10);
        const newId = parseInt(args[1], 10);
        if (isNaN(oldId) || isNaN(newId)) {
            console.error("Error: oldId and newId must be numbers.");
            process.exit(1);
        }
        logger_1.transferLogger.info("Starting player transfer from " + oldId + " to " + newId);
        try {
            yield (0, transfer_1.playerTransfer)(oldId, newId);
            logger_1.transferLogger.info("Player transfer from " + oldId + " to " + newId + " completed successfully.");
            console.log("Player transfer from " + oldId + " to " + newId + " completed successfully.");
            // 在实际应用中，数据库连接等资源应当在这里正确关闭
            // 例如： await closeDbConnections();
            process.exit(0); // 确保进程在异步操作完成后退出
        }
        catch (error) {
            logger_1.transferLogger.error("Error during player transfer from " + oldId + " to " + newId + ":", error);
            console.error("Error during player transfer from " + oldId + " to " + newId + ":", error);
            process.exit(1);
        }
    });
}
main();
//# sourceMappingURL=playerTransfer.js.map