import { processReadyTask, PyqDelayQueue } from "../cron-jobs/delayTasks";
import * as momentLotteryService from "../pyq-server/services/momentLotteryService";

async function main() {
  momentLotteryService.listenMigrateTable();
  await PyqDelayQueue.onTick();
  await processReadyTask();
}

main()
  .then(() => {
    console.log("TriggerDelayTask End");
  })
  .catch((err) => {
    console.error(err);
  });
