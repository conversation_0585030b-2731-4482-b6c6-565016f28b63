"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.main = void 0;
const logger_1 = require("../pyq-server/logger");
const HotMomentsCache_1 = require("../service/qnm/pyq/HotMomentsCache");
const logger = (0, logger_1.clazzLogger)("cmd.refreshHot");
function main() {
    return __awaiter(this, void 0, void 0, function* () {
        // const ret = await refreshAll();
        const tagRet = (0, HotMomentsCache_1.refreshMoments)(new Date(), "all", 1, true);
        // const noTagRet = refreshMoments(new Date(), "all", 0, true);
    });
}
exports.main = main;
main()
    .then(() => {
    console.log("Done");
})
    .catch((err) => {
    console.error(err);
});
//# sourceMappingURL=refreshHot.js.map