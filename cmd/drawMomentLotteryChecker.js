"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const config_1 = require("../pyq-server/common/config");
const logger_1 = require("../pyq-server/logger");
const momentLotteryService_1 = require("../pyq-server/services/momentLotteryService");
const cron_1 = require("cron");
const logger = (0, logger_1.clazzLogger)("cmd.drawMomentLotteryChecker");
function main() {
    return __awaiter(this, void 0, void 0, function* () {
        const job = new cron_1.CronJob({
            cronTime: config_1.momentLotteryCfg.drawCheckerCron,
            onTick: function () {
                return __awaiter(this, void 0, void 0, function* () {
                    const drawRet = yield (0, momentLotteryService_1.drawMomentLotteryChecker)();
                    logger.info({ drawRet }, "drawMomentLotteryCheckerFinished");
                });
            },
            start: false,
            runOnInit: true,
        });
        job.start();
    });
}
main()
    .then(() => {
    console.log("drawMomentLotteryChecker is running");
})
    .catch((err) => {
    console.error("drawMomentLotteryChecker error", err);
});
//# sourceMappingURL=drawMomentLotteryChecker.js.map