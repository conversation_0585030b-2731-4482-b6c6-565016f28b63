"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const delayTasks_1 = require("../cron-jobs/delayTasks");
const momentLotteryService = require("../pyq-server/services/momentLotteryService");
function main() {
    return __awaiter(this, void 0, void 0, function* () {
        momentLotteryService.listenMigrateTable();
        yield delayTasks_1.PyqDelayQueue.onTick();
        yield (0, delayTasks_1.processReadyTask)();
    });
}
main()
    .then(() => {
    console.log("TriggerDelayTask End");
})
    .catch((err) => {
    console.error(err);
});
//# sourceMappingURL=triggerDelayTask.js.map