import { clazzLogger } from "../pyq-server/logger";
import { refreshMoments } from "../service/qnm/pyq/HotMomentsCache";
const logger = clazzLogger("cmd.refreshHot");
export async function main() {
  // const ret = await refreshAll();
  const tagRet = refreshMoments(new Date(), "all", 1, true);
  // const noTagRet = refreshMoments(new Date(), "all", 0, true);
}

main()
  .then(() => {
    console.log("Done");
  })
  .catch((err) => {
    console.error(err);
  });
